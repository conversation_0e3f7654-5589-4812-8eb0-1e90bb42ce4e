# ASS字幕操作规范

## 📋 目录
- [ASS文件结构](#ass文件结构)
- [样式定义详解](#样式定义详解)
- [标签语法规范](#标签语法规范)
- [常用操作示例](#常用操作示例)
- [错误排查指南](#错误排查指南)

## 📁 ASS文件结构

### 基本结构
```
[Script Info]        # 脚本信息段
[V4+ Styles]        # 样式定义段
[Events]            # 事件/对话段
```

### 完整示例
```ass
[Script Info]
Title: Generated Subtitle
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: TV.601
PlayResX: 1080          # 视频宽度
PlayResY: 1920          # 视频高度

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,&H00FFFFFF,&H00000000,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,100,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,, 这是一行字幕文本
```

## 🎨 样式定义详解

### Style参数说明
```
Style: 样式名,字体名,字体大小,主颜色,次颜色,描边颜色,背景颜色,粗体,斜体,下划线,删除线,X缩放,Y缩放,字符间距,旋转角度,边框样式,描边宽度,阴影距离,对齐方式,左边距,右边距,垂直边距,编码
```

### 详细参数解释

#### 1. 字体设置
- **Fontname**: 字体名称 (如: Arial, 微软雅黑, SimHei)
- **Fontsize**: 字体大小 (像素值，如: 32, 48, 64)

#### 2. 颜色设置 (BGR格式)
- **PrimaryColour**: 主要文字颜色 `&H00BBGGRR`
- **SecondaryColour**: 次要文字颜色 `&H00BBGGRR`
- **OutlineColour**: 描边颜色 `&H00BBGGRR`
- **BackColour**: 背景颜色 `&H00BBGGRR`

#### 颜色示例
```
&H00FFFFFF  # 白色
&H00000000  # 黑色
&H000000FF  # 红色
&H0000FF00  # 绿色
&H00FF0000  # 蓝色
&H0000D7FF  # 金黄色
```

#### 3. 对齐方式 (Alignment)
```
1 = 左下    2 = 中下    3 = 右下
4 = 左中    5 = 中中    6 = 右中
7 = 左上    8 = 中上    9 = 右上
```

#### 4. 边距设置
- **MarginL**: 左边距 (像素)
- **MarginR**: 右边距 (像素)
- **MarginV**: 垂直边距 (像素，从底部或顶部计算)

#### 5. 描边和阴影
- **BorderStyle**: 边框样式 (1=描边+阴影, 3=不透明背景框)
- **Outline**: 描边宽度 (像素)
- **Shadow**: 阴影距离 (像素)

## 🏷️ 标签语法规范

### 基本标签格式
```
{\标签名参数}文本内容{\r}
```

### 常用标签

#### 1. 颜色标签
```
{\c&HBBGGRR&}文本{\r}           # 改变文字颜色
{\1c&HBBGGRR&}文本{\r}          # 主要颜色
{\2c&HBBGGRR&}文本{\r}          # 次要颜色
{\3c&HBBGGRR&}文本{\r}          # 描边颜色
{\4c&HBBGGRR&}文本{\r}          # 阴影颜色
```

#### 2. 字体样式标签
```
{\b1}粗体文本{\r}               # 粗体开启
{\b0}正常文本{\r}               # 粗体关闭
{\i1}斜体文本{\r}               # 斜体开启
{\u1}下划线文本{\r}             # 下划线开启
{\s1}删除线文本{\r}             # 删除线开启
```

#### 3. 字体大小标签
```
{\fs48}大字体{\r}               # 设置字体大小为48
{\fscx120}横向放大{\r}          # 横向缩放120%
{\fscy120}纵向放大{\r}          # 纵向缩放120%
```

#### 4. 位置和对齐标签
```
{\an2}底部居中{\r}              # 对齐方式
{\pos(540,960)}绝对位置{\r}     # 绝对位置 (x,y)
{\move(100,100,200,200)}移动{\r} # 移动动画
```

#### 5. 重置标签
```
{\r}                            # 重置为默认样式
{\rStyleName}                   # 重置为指定样式
```

### 标签组合示例
```
{\c&H0000D7FF&\b1\fs48}金色粗体大字{\r}
{\an8\pos(540,200)}顶部居中位置{\r}
{\fad(500,500)}淡入淡出效果{\r}
```

## 🛠️ 常用操作示例

### 1. 调大字体
#### 方法1: 修改样式定义
```ass
# 原始
Style: Default,Arial,32,&H00FFFFFF,...

# 修改为48像素
Style: Default,Arial,48,&H00FFFFFF,...
```

#### 方法2: 使用标签
```ass
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fs48}这是大字体文本{\r}
```

### 2. 字体居中
#### 方法1: 修改样式对齐
```ass
# 对齐参数改为2 (底部居中)
Style: Default,Arial,48,&H00FFFFFF,&H00000000,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,100,1
```

#### 方法2: 使用对齐标签
```ass
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\an2}居中显示的文本{\r}
```

### 3. 字体上移
#### 方法1: 修改垂直边距
```ass
# 将MarginV从100改为200 (向上移动100像素)
Style: Default,Arial,48,&H00FFFFFF,&H00000000,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,200,1
```

#### 方法2: 使用绝对位置
```ass
# 1920高度的视频，字幕显示在距离底部300像素处
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\pos(540,1620)}上移的文本{\r}
```

#### 方法3: 使用对齐+边距组合
```ass
# 使用中上对齐 + 顶部边距
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,300,,{\an8}顶部显示{\r}
```

### 4. 高亮关键词
```ass
# 正确的高亮语法
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,, 这是 {\c&H0000D7FF&\b1}重要词汇{\r} 的示例
```

## ❌ 错误排查指南

### 常见语法错误

#### 1. 错误的标签转义
```ass
# ❌ 错误
\{\\c&H0000D7FF\\b1\}文本\{\\r\}

# ✅ 正确
{\c&H0000D7FF&\b1}文本{\r}
```

#### 2. 颜色格式错误
```ass
# ❌ 错误 (RGB格式)
{\c&HFF0000&}红色{\r}

# ✅ 正确 (BGR格式)
{\c&H0000FF&}红色{\r}
```

#### 3. 对齐参数错误
```ass
# ❌ 错误 (超出范围)
{\an10}文本{\r}

# ✅ 正确 (1-9范围)
{\an2}文本{\r}
```

### 调试技巧

#### 1. 验证ASS文件
```bash
# 使用FFmpeg验证
ffmpeg -f ass -i subtitle.ass -f null -

# 使用文本编辑器检查编码 (必须UTF-8)
```

#### 2. 测试渲染
```bash
# 快速测试字幕渲染
ffmpeg -f lavfi -i color=black:size=1080x1920:duration=10 -vf "ass=subtitle.ass" test.mp4
```

## 📊 最佳实践

### 1. 9:16短视频字幕设置
```ass
[Script Info]
PlayResX: 1080
PlayResY: 1920

[V4+ Styles]
Style: Default,Arial,48,&H00FFFFFF,&H00000000,&H00000000,&H80000000,1,0,0,0,100,100,0,0,1,3,1,2,50,50,150,1
```

### 2. 推荐参数
- **字体大小**: 48-64像素 (9:16视频)
- **对齐方式**: 2 (底部居中)
- **垂直边距**: 150-200像素
- **描边宽度**: 2-3像素
- **字体**: Arial, 微软雅黑, SimHei

### 3. 性能优化
- 避免过多的标签嵌套
- 使用样式定义而非内联标签
- 合理设置分辨率参数

## 🎬 字幕飞入效果详解

### 📋 飞入效果类型

#### 1️⃣ 淡入淡出效果 (最常用)
```ass
{\fad(淡入时间,淡出时间)}文本内容{\r}
```

**示例**:
```ass
# 500毫秒淡入，500毫秒淡出
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fad(500,500)}Good afternoon, there is another wildfire evacuation alert tonight.

# 1秒淡入，无淡出
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fad(1000,0)}Good afternoon, there is another wildfire evacuation alert tonight.
```

#### 2️⃣ 移动飞入效果
```ass
{\move(起始x,起始y,结束x,结束y,开始时间,结束时间)}文本内容{\r}
```

**示例**:
```ass
# 从左侧飞入 (1080宽度视频)
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\move(-200,1770,540,1770,0,500)}从左飞入的文本

# 从右侧飞入
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\move(1280,1770,540,1770,0,500)}从右飞入的文本

# 从上方飞入
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\move(540,-100,540,1770,0,500)}从上飞入的文本

# 从下方飞入
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\move(540,2000,540,1770,0,500)}从下飞入的文本
```

#### 3️⃣ 缩放飞入效果
```ass
{\t(开始时间,结束时间,\fscx缩放x\fscy缩放y)}文本内容{\r}
```

**示例**:
```ass
# 从小到大缩放飞入
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fscx0\fscy0\t(0,500,\fscx100\fscy100)}缩放飞入文本

# 从大到正常缩放
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fscx200\fscy200\t(0,500,\fscx100\fscy100)}缩放飞入文本
```

#### 4️⃣ 旋转飞入效果
```ass
{\t(开始时间,结束时间,\frz角度)}文本内容{\r}
```

**示例**:
```ass
# 旋转飞入
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\frz360\t(0,500,\frz0)}旋转飞入文本
```

#### 5️⃣ 组合飞入效果
```ass
# 淡入+移动+缩放组合
Dialogue: 0,0:00:00.00,0:00:03.31,Default,,0,0,0,,{\fad(500,500)\move(-200,1770,540,1770,0,500)\fscx50\fscy50\t(0,500,\fscx100\fscy100)}复合飞入效果
```

### 📊 飞入效果参数详解

#### ⏱️ 时间参数 (毫秒)
- **淡入时间**: 0-1000ms (推荐300-500ms)
- **淡出时间**: 0-1000ms (推荐200-500ms)
- **移动时间**: 300-1000ms (推荐500-800ms)
- **缩放时间**: 200-800ms (推荐400-600ms)

#### 📍 位置参数 (像素坐标)
**1080x1920视频坐标系**:
- **中心点**: (540, 960)
- **底部中心**: (540, 1770) - 字幕常用位置
- **左侧外**: (-200, 1770) - 左侧飞入起点
- **右侧外**: (1280, 1770) - 右侧飞入起点
- **上方外**: (540, -100) - 上方飞入起点
- **下方外**: (540, 2000) - 下方飞入起点

#### 🎨 缩放参数
- **fscx**: 水平缩放 (100=正常, 0=消失, 200=放大2倍)
- **fscy**: 垂直缩放 (100=正常, 0=消失, 200=放大2倍)

#### 🔄 旋转参数
- **frz**: Z轴旋转 (0=正常, 360=转一圈, -360=反向转一圈)

### 🎯 推荐飞入方案

#### 📱 9:16短视频最佳实践

##### 1. 新闻类字幕 (稳重)
```ass
{\fad(400,300)}文本内容
```

##### 2. 娱乐类字幕 (活泼)
```ass
{\move(540,2000,540,1770,0,600)\fad(200,300)}文本内容
```

##### 3. 教育类字幕 (专业)
```ass
{\fad(300,200)\t(0,400,\fscx100\fscy100)\fscx80\fscy80}文本内容
```

##### 4. 营销类字幕 (吸睛)
```ass
{\move(-300,1770,540,1770,0,800)\fscx120\fscy120\t(0,500,\fscx100\fscy100)\fad(200,300)}文本内容
```

### 🔧 批量修改方法

#### 方法1: 文本编辑器替换
```regex
# 查找
Dialogue: (.*?),Default,,0,0,0,,

# 替换为 (添加淡入效果)
Dialogue: $1,Default,,0,0,0,,{\fad(500,300)}
```

#### 方法2: 脚本批量处理
```javascript
// Node.js脚本示例
const fs = require('fs');
let content = fs.readFileSync('subtitle.ass', 'utf8');
content = content.replace(
    /Dialogue: (.*?),Default,,0,0,0,, /g,
    'Dialogue: $1,Default,,0,0,0,,{\\fad(500,300)} '
);
fs.writeFileSync('subtitle_with_effects.ass', content);
```

### ⚠️ 飞入效果注意事项

#### 1. 性能考虑
- 避免过于复杂的动画组合
- 飞入时间不宜过长 (建议<1秒)
- 大量字幕使用相同效果保持一致性

#### 2. 兼容性
- 确保播放器支持ASS高级特效
- 移动设备可能对复杂动画支持有限
- 建议提供无特效的备用版本

#### 3. 用户体验
- 飞入效果不应干扰内容阅读
- 保持动画流畅自然
- 避免过于花哨的效果

---

**📝 注意**: ASS字幕对大小写敏感，所有标签和参数必须严格按照规范书写。