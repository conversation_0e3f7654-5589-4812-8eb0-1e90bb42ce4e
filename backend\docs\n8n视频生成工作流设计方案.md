# 直播视频草稿生成工作流设计方案

## 📋 项目概述

基于现有Express流水线架构，结合n8n工作流和Python剪映草稿生成工具，实现**英语学习直播视频草稿自动生成**系统。

### 🎯 目标功能
- **输入**：选中的视频项目（包含JSON文稿）
- **输出**：剪映草稿文件，支持渐进式字幕显示
- **核心特性**：TTS多遍朗读 + 字幕渐进显示（50%→80%→100%+中文）

## 🏗️ 技术架构分工

### **Express服务器（主力）**
- ✅ 数据提取和业务逻辑
- ✅ 流水线任务编排
- ✅ 字幕时间线计算
- ✅ 剪映草稿生成（调用Python）
- ✅ 文件管理和存储

### **n8n服务器（TTS专家）**
- ✅ TTS音频生成（Azure Speech等）
- ✅ 批量API调用管理
- ✅ 音频文件处理
- ✅ 进度回调通知

### **Remotion（专业视频生成中间件）**
- ✅ React-based编程式视频生成
- ✅ 动态效果和动画支持
- ✅ 渐进式字幕样式设置
- ✅ 音频轨道和字幕轨道编排
- ✅ 支持复杂动画和转场效果

## 🔄 完整工作流程

### 整体流程图
```
[前端：选择视频]
    ↓
[Express：提取JSON文稿句子]
    ↓
[Express：调用n8n TTS工作流] → [n8n：HTTP Request节点批量生成TTS音频] → [n8n：回调Express]
    ↓
[Express：创建渐进式字幕时间线]
    ↓
[Express：调用Remotion生成视频] → [Remotion：生成动态效果视频文件]
    ↓
[Express：返回视频文件]
```

## 📋 详细执行步骤

### **步骤1：Express数据提取**

#### **1.1 提取JSON文稿句子**
- **输入**：用户选中的视频项目ID列表
- **处理**：遍历项目目录，查找`*enhanced_bilingual_subtitle*.json`文件
- **输出**：英语完整句子数组，包含中英文对照和来源信息
- **任务类**：`ExtractSentencesTask`

#### **1.2 数据预处理**
- **功能**：过滤不完整句子，去重，按来源分组
- **格式化**：为每个句子分配唯一ID和时间估算
- **验证**：确保句子完整性和中英文对应关系

### **步骤2：n8n TTS音频生成**

#### **2.1 Express调用n8n**
- **接口**：POST请求到n8n webhook
- **数据**：句子列表、回调URL、请求ID
- **任务类**：`CallN8nTTSTask`

#### **2.2 n8n工作流处理**
- **节点1**：Webhook接收Express请求
- **节点2**：Code节点处理数据，为每句话生成4个TTS请求
  - 英文第1遍：语速0.9，正常音调
  - 英文第2遍：语速1.0，正常音调
  - 英文第3遍：语速1.1，正常音调
  - 中文1遍：语速1.0，清晰发音
- **节点3**：Split In Batches批量处理（每批3个）
- **节点4**：**HTTP Request节点**调用Azure Speech API进行TTS语音生成
- **节点5**：收集音频文件，回调Express

#### **2.3 音频文件管理**
- **存储**：音频文件保存到Express服务器指定目录
- **命名规范**：`{reqId}_{语言}_{句子索引}_{遍数}.mp3`
- **回调数据**：音频文件URL列表和元数据

### **步骤3：Express字幕时间线创建**

#### **3.1 渐进式字幕逻辑**
- **第1遍显示**：50%单词显示，其余用"___"代替
- **第2遍显示**：80%单词显示，新显示词黄色高亮
- **第3遍显示**：100%英文 + 中文翻译同时显示
- **时间间隔**：每遍之间0.5秒停顿

#### **3.2 时间线计算**
- **音频时长**：根据TTS返回的实际音频时长
- **字幕同步**：精确计算每个字幕的开始和结束时间
- **任务类**：`CreateSubtitleVariationsTask`

### **步骤4：Remotion专业视频生成**

#### **4.1 Express调用Remotion**
- **方式**：通过Remotion Node.js API或服务端渲染
- **配置对象**：React组件配置，包含动画、字幕、音频配置
- **任务类**：`GenerateVideoWithRemotionTask`

#### **4.2 Remotion视频处理**
- **技术栈**：React + Remotion框架
- **视频结构**：
  - React组件：每个场景作为独立的React组件
  - 音频轨道：TTS音频文件按时间顺序同步
  - 字幕组件：渐进式字幕显示，支持动画效果
  - 背景组件：支持动态背景、渐变、图片等
  - 动画效果：支持淡入淡出、缩放、移动等动画
- **输出**：高质量MP4视频文件

#### **4.3 渐进式字幕React组件实现**
```jsx
// 第1遍：部分单词显示
<Subtitle frame={frame} text="Hello ___" style={{color: '#ffffff'}} />

// 第2遍：完整单词高亮显示
<Subtitle frame={frame} text="Hello World"
  highlightWords={['World']} highlightColor="#ffff00" />

// 第3遍：英文+中文同时显示
<BilingualSubtitle
  english="Hello World"
  chinese="你好世界"
  frame={frame}
  animation="fadeIn"
/>
```

### **步骤5：结果返回**

#### **5.1 Express响应**
- **返回数据**：视频文件路径、视频URL、处理统计信息
- **状态更新**：更新前端进度显示
- **文件管理**：视频文件保存到uploads目录

#### **5.2 用户获取结果**
- **直接下载**：通过返回的视频URL直接下载
- **在线预览**：在浏览器中预览生成的视频
- **质量检查**：检查音频和字幕同步效果
- **即时可用**：无需额外的编辑软件操作

## 🔧 技术实现要点

### **Express流水线架构**

#### **流水线服务类**
- **类名**：`LiveVideoGenerationPipelineService`
- **任务序列**：
  1. `ExtractSentencesTask` - 提取JSON文稿句子
  2. `CallN8nTTSTask` - 调用n8n TTS工作流
  3. `WaitForN8nCallbackTask` - 等待n8n回调
  4. `CreateSubtitleVariationsTask` - 创建字幕变体
  5. `GenerateVideoWithRemotionTask` - 使用Remotion生成动态效果视频

#### **关键配置参数**
- **TTS设置**：Azure Speech API，支持中英文
- **音频格式**：MP3，44.1kHz，单声道
- **视频规格**：1080x1920竖屏格式，MP4输出
- **字幕样式**：支持颜色、字体、位置自定义

### **n8n工作流配置**

#### **资源限制适配**
- **服务器配置**：1CPU + 2GB内存
- **适合任务**：TTS API调用、数据转换、文件传输
- **不适合任务**：视频合成、大文件处理
- **批处理大小**：每批3个TTS请求，避免API限流

#### **核心节点配置**
- **Webhook节点**：接收Express请求
- **Code节点**：数据处理和格式转换
- **HTTP Request节点（TTS）**：调用Azure Speech API进行TTS语音生成
- **Split In Batches节点**：批量处理管理
- **HTTP Request节点（图片生成）**：可选的背景图片生成，调用DALL-E或其他图片API

#### **HTTP Request节点详细配置**

**TTS语音生成配置**：
```javascript
// Azure Speech API配置
{
  "method": "POST",
  "url": "https://[region].tts.speech.microsoft.com/cognitiveservices/v1",
  "headers": {
    "Ocp-Apim-Subscription-Key": "{{ $credentials.azureSpeech.subscriptionKey }}",
    "Content-Type": "application/ssml+xml",
    "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3"
  },
  "body": {
    "contentType": "raw",
    "rawContent": `<speak version='1.0' xml:lang='{{ $json.language }}'>
      <voice xml:lang='{{ $json.language }}' name='{{ $json.voiceName }}'>
        <prosody rate='{{ $json.rate }}'>{{ $json.text }}</prosody>
      </voice>
    </speak>`
  }
}
```

**图片生成配置（可选扩展）**：
```javascript
// OpenAI DALL-E API配置
{
  "method": "POST",
  "url": "https://api.openai.com/v1/images/generations",
  "headers": {
    "Authorization": "Bearer {{ $credentials.openAi.apiKey }}",
    "Content-Type": "application/json"
  },
  "body": {
    "contentType": "json",
    "jsonContent": {
      "prompt": "{{ $json.imagePrompt }}",
      "size": "1024x1024",
      "n": 1,
      "response_format": "url"
    }
  }
}
```

### **Editly集成方案**

#### **环境配置**
- **依赖库**：editly (npm package)
- **FFmpeg依赖**：Editly基于FFmpeg，需要系统安装FFmpeg
- **调用方式**：直接调用Editly的Node.js API

#### **视频生成逻辑**
- **clips结构**：每个clip包含duration和layers数组
- **audio layers**：TTS音频文件按时间顺序排列
- **subtitle layers**：渐进式字幕显示，支持样式自定义
- **background layers**：背景颜色或图片
- **输出格式**：标准MP4视频文件

#### **Remotion配置示例**
```jsx
// Remotion React组件示例
import { Audio, useCurrentFrame, useVideoConfig } from 'remotion';

const LiveVideoComposition = ({ sentences, audioFiles }) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  return (
    <div style={{
      width: '100%',
      height: '100%',
      backgroundColor: '#000000',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      {/* 音频轨道 */}
      <Audio src={audioFiles.english1} startFrom={0} />
      <Audio src={audioFiles.english2} startFrom={105} />
      <Audio src={audioFiles.english3} startFrom={210} />

      {/* 渐进式字幕组件 */}
      <ProgressiveSubtitle
        frame={frame}
        fps={fps}
        sentences={sentences}
      />
    </div>
  );
};

// 渐进式字幕组件
const ProgressiveSubtitle = ({ frame, fps, sentences }) => {
  const currentTime = frame / fps;

  if (currentTime < 3.5) {
    // 第1遍：50%单词显示
    return (
      <div style={{ fontSize: 48, color: '#ffffff' }}>
        Hello ___
      </div>
    );
  } else if (currentTime < 7.0) {
    // 第2遍：100%单词高亮显示
    return (
      <div style={{ fontSize: 52, color: '#ffff00' }}>
        Hello World
      </div>
    );
  } else if (currentTime < 11.0) {
    // 第3遍：英文+中文双语显示
    return (
      <div style={{ fontSize: 48, color: '#ffffff', textAlign: 'center' }}>
        <div>Hello World</div>
        <div style={{ marginTop: 20 }}>你好世界</div>
      </div>
    );
  }

  return null;
};
```

## 🎯 Remotion技术优势

### **为什么选择Remotion作为视频生成中间件**

#### **1. 专业级动态效果支持**
- **React生态系统**：利用完整的React组件生态，支持复杂的UI组件
- **CSS动画**：支持所有CSS动画、变换和过渡效果
- **JavaScript动画库**：可集成Framer Motion、React Spring等专业动画库
- **Canvas和WebGL**：支持复杂的图形渲染和3D效果

#### **2. 编程式视频生成**
- **数据驱动**：通过props传递数据，实现完全动态的视频内容
- **条件渲染**：根据数据条件渲染不同的视频元素
- **循环和映射**：批量生成重复的视频片段
- **算法支持**：使用JavaScript算法生成复杂的视觉效果

#### **3. 与FFmpeg的完美结合**
- **底层优化**：Remotion底层使用FFmpeg进行视频编码
- **高质量输出**：支持4K、60fps等高质量视频输出
- **格式支持**：支持MP4、WebM、GIF等多种输出格式
- **性能优化**：多线程渲染，充分利用服务器资源

#### **4. 服务端渲染能力**
- **Node.js集成**：完美集成到Express服务器
- **无头渲染**：支持服务器端无头浏览器渲染
- **批量处理**：支持并行渲染多个视频
- **云端部署**：支持AWS Lambda、Docker等云端部署

#### **5. 相比传统视频工具的优势**
| 特性 | Remotion | 传统视频工具 | Editly |
|------|----------|-------------|---------|
| 动态效果 | ✅ 完整支持 | ❌ 有限 | ❌ 基础 |
| 编程控制 | ✅ 完全可编程 | ❌ 模板化 | ⚠️ 配置化 |
| React生态 | ✅ 完整支持 | ❌ 不支持 | ❌ 不支持 |
| 维护状态 | ✅ 活跃维护 | ⚠️ 商业软件 | ❌ 停止维护 |
| 学习成本 | ⚠️ 需要React知识 | ❌ 复杂 | ✅ 简单 |
| 许可成本 | ⚠️ 商业许可 | ❌ 昂贵 | ✅ 免费 |

## ⚠️ 关键限制和注意事项

### **n8n服务器限制**
- **硬件配置**：1CPU + 2GB内存
- **适用范围**：仅限轻量级API调用和数据处理
- **不支持功能**：视频合成、大文件处理、CPU密集型任务
- **建议用途**：TTS生成、数据转换、任务编排

### **Remotion依赖要求**
- **Node.js版本**：支持Node.js 16+（推荐18+）
- **FFmpeg**：系统需要安装FFmpeg 4.0+（Remotion底层依赖）
- **内存要求**：建议8GB+内存用于React渲染和视频处理
- **存储空间**：临时文件和输出视频需要足够存储空间
- **许可证**：商业使用需要Remotion Company License（$100/月起）

### **系统环境要求**
- **Express服务器**：Windows/Linux，建议8GB+内存
- **FFmpeg**：系统级安装，支持H.264编码
- **n8n服务器**：现有配置即可，无需升级
- **React环境**：支持现代JavaScript特性
- **无额外软件**：用户无需安装任何视频编辑软件

## 🚀 实施计划

### **阶段1：基础功能实现**
1. **Express任务类开发**
   - ExtractSentencesTask：提取JSON文稿
   - CallN8nTTSTask：调用n8n工作流
   - CreateSubtitleVariationsTask：字幕时间线
   - GenerateVideoWithRemotionTask：Remotion动态视频生成

2. **n8n工作流配置**
   - TTS专用工作流（使用HTTP Request节点）
   - Azure Speech API集成（通过HTTP Request节点调用）
   - 批量处理和回调机制
   - 可选图片生成工作流（HTTP Request节点调用图片API）

3. **Remotion环境搭建**
   - npm install remotion
   - 获取Remotion Company License
   - React组件开发
   - 动态效果和动画实现

### **阶段2：功能测试和优化**
1. **端到端测试**
   - 完整流程验证
   - 视频质量检查
   - 音频字幕同步测试

2. **性能优化**
   - Remotion渲染参数调优
   - React组件性能优化
   - 错误处理完善
   - 日志监控增强

3. **用户体验改进**
   - 前端进度显示
   - 视频预览功能
   - 下载链接优化

### **阶段3：扩展功能开发**
1. **高级特性**
   - 多种视频模板
   - 动画效果增强
   - 自定义背景和转场
   - 图片生成集成（HTTP Request节点调用DALL-E等API）

2. **批量处理**
   - 多项目并行生成
   - 队列管理机制
   - 资源调度优化

## 📊 预期效果

### **输出质量**
- **视频质量**：1080x1920高清MP4视频
- **音频质量**：清晰的TTS语音，支持语速调节
- **字幕效果**：渐进式显示，视觉层次分明
- **时间同步**：音频与字幕精确对应
- **用户体验**：一键生成，直接可用

### **处理效率**
- **单个项目**：预计5-12分钟生成视频（React渲染需要更多时间）
- **并发处理**：支持多个项目排队处理
- **资源消耗**：主要消耗Express服务器资源（内存和CPU）
- **成本控制**：TTS API调用 + Remotion许可证费用

### **成本分析**
- **Remotion许可证**：$100/月起（Company License）
- **Azure Speech API**：按使用量计费
- **服务器资源**：需要更高配置的Express服务器
- **总体ROI**：专业视频效果带来的价值远超成本投入

---

**文档版本**：v4.0 (Remotion版本)
**更新时间**：2025-07-31
**状态**：技术方案确认
**技术栈**：Express + n8n + Remotion + FFmpeg
**负责人**：开发团队

## 📝 更新日志

### v4.0 (2025-07-31) - Remotion版本
- ✅ 将Editly替换为Remotion作为视频生成中间件
- ✅ 增加专业级动态效果和动画支持
- ✅ 更新技术架构和实施计划
- ✅ 添加成本分析和许可证说明
- ✅ 提供React组件示例代码

### v3.0 (2025-01-30) - Editly版本
- 原始Editly技术方案（已废弃）
