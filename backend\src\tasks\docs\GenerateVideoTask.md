# GenerateVideoTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **originalVideoPath** (string): 原始视频文件路径，来自videoController.js
- **audioDuration** (number): 音频时长（秒），来自GenerateASSTask
- **videoConfig** (object): 视频配置对象，来自GenerateASSTask
- **assContent** (string): ASS字幕内容，来自GenerateASSTask
- **audioFilePath** (string): 音频文件路径，来自ConvertToAudioTask
- **savePath** (string): 文件保存路径

### 可选参数
- **reqId** (string): 请求ID，用于日志追踪

## 2. 输出上下文参数 (Output Context)

- **finalVideoPath** (string): 最终生成的视频文件路径
- **extendedAudioPath** (string): 重复拼接后的音频文件路径
- **progressBarVideoPath** (string): 进度条视频文件路径
- **backgroundVideoPath** (string): 背景视频文件路径
- **videoConfig** (object): 视频配置对象（原样返回）
- **videoGenerationStats** (object): 视频生成统计信息
  - **totalAudioDuration** (number): 总音频时长
  - **originalAudioDuration** (number): 原始音频时长
  - **repeatCount** (number): 重复次数
  - **videoResolution** (string): 视频分辨率
- **videoIdentifier** (string): 视频标识符（原样返回）
- **savePath** (string): 保存路径（原样返回）
- **taskStatus** (string): 任务状态
- **taskResult** (string): 任务结果

## 3. 重要数据格式

### 视频配置对象格式
```json
{
  "width": 1080,
  "height": 1920,
  "framerate": 30,
  "repeatCount": 3,
  "codec": "libx264",
  "preset": "medium",
  "crf": 23,
  "progressBar": {
    "height": 16,
    "backgroundColor": "#333333",
    "foregroundColor": "#00FF00"
  },
  "textArea": {
    "backgroundColor": "#80000000"
  }
}
```

### FFmpeg滤镜链格式
```
[1:v]scale=1080:-1[scaled_video];
[0:v][scaled_video]overlay=(W-w)/2:(H-h)/2[with_video];
[with_video][3:v]overlay=x:y[with_progress];
[with_progress]subtitles='subtitle.ass'[final_video]
```

## 4. 文件操作

### 保存的文件格式
- **.mp4**: 最终生成的视频文件
- **.mp3**: 重复拼接的音频文件
- **.txt**: 临时FFmpeg滤镜脚本文件
- **.ass**: 临时ASS字幕文件

### 文件命名规则
- **最终视频**: `{videoIdentifier}_extended_video_{timestamp}.mp4`
- **拼接音频**: `{videoIdentifier}_extended_audio.mp3`
- **进度条视频**: `{videoIdentifier}_progress_bar_{timestamp}.mp4`
- **背景视频**: `{videoIdentifier}_background_{timestamp}.mp4`
- **滤镜脚本**: `{videoIdentifier}_filter_script_{timestamp}.txt`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 使用时间戳确保文件名唯一性
- 临时文件在处理完成后自动清理

## 5. 执行逻辑概述

视频生成任务负责创建完整的9:16短视频，包含音频重复拼接、进度条生成、背景视频生成和ASS字幕烧录等功能。任务首先验证输入参数并计算总音频时长，然后执行音频重复拼接处理，根据配置的重复次数生成扩展音频。接下来使用Canvas生成动态进度条视频和背景视频（报纸+80%黑色遮罩）。核心合成阶段使用FFmpeg的filter_complex_script功能，将原视频、背景视频、进度条视频和ASS字幕进行复杂的层叠合成。最终生成符合9:16比例的短视频，支持专业的视频编码设置和完善的错误处理机制。
