const logger = require('./logger');

/**
 * @功能概述: SRT字幕格式校验、解析和修复工具集。
 * @模块说明: 此模块提供了对SRT字幕内容进行全面校验、结构化解析以及常见格式错误修复的功能。
 *           它严格按照标准SRT格式进行处理，支持多行文本内容，但不负责校验字幕内容的语义质量。
 *           核心功能包括：检测并移除SRT文件头部或尾部的无效文本（如LLM生成的markdown标记），
 *           校验序号、时间戳格式与逻辑、文本内容空缺，以及修复这些检测到的问题。
 */

/**
 * @功能概述: 校验SRT字幕内容的格式是否符合标准。
 * @param {string} srtContent - 需要校验的SRT字幕内容字符串。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][validateSrtFormat]'] - 日志前缀，用于追踪日志来源。
 * @returns {object} 校验结果对象，包含:
 *                   - isValid: {boolean} SRT内容是否有效。
 *                   - errors: {Array<object>} 检测到的错误列表。
 *                   - warnings: {Array<object>} 检测到的警告列表。
 *                   - statistics: {object} 字幕内容的统计信息。
 * @执行流程:
 *   1. 解析SRT内容为结构化块。
 *   2. 检查并记录解析过程中是否移除了头部非SRT文本。
 *   3. 执行基础格式检查（如内容是否为空，是否能解析到块）。
 *   4. 逐块校验每个SRT块的序号、时间戳和文本内容。
 *   5. 执行全局结构校验（如时间重叠）。
 *   6. 生成统计信息并返回校验结果。
 */
function validateSrtFormat(srtContent, logPrefix = '[文件：srtValidator.js][SRT校验工具][validateSrtFormat]') {
    logger.debug(`${logPrefix}[步骤 1] 开始校验SRT格式。`);
    
    const errors = [];
    const warnings = [];
    // 步骤 1.1: 解析SRT内容为结构化块
    const parseResult = parseSrtBlocks(srtContent, `${logPrefix}[解析子流程]`);
    const blocks = parseResult.blocks;

    // 步骤 1.2: 检查并记录头部文本移除情况
    if (parseResult.headerStripped) {
        const warningMessage = `检测到并移除了第一个序列号之前的非SRT文本（可能包含如 'plaintext' 或 '\`\`\`' 标记）。被移除的文本片段预览: "${parseResult.originalHeader.substring(0, 70)}${parseResult.originalHeader.length > 70 ? '...' : ''}"`;
        warnings.push({
            type: 'HEADER_TEXT_DETECTED',
            message: warningMessage,
            originalHeader: parseResult.originalHeader
        });
        logger.warn(`${logPrefix}[步骤 1.2.1][警告] ${warningMessage}`);
    }
    
    // 步骤 1.3: 基础格式检查 - 内容是否为空
    if (!srtContent.trim()) {
        errors.push({ type: 'EMPTY_CONTENT', message: 'SRT内容为空。' });
        logger.error(`${logPrefix}[步骤 1.3.1][错误] SRT内容为空。`);
        return { isValid: false, errors, warnings, statistics: {} };
    }
    
    // 步骤 1.4: 基础格式检查 - 是否成功解析到块
    if (blocks.length === 0) {
        errors.push({ type: 'NO_VALID_BLOCKS', message: '未找到有效的SRT字幕块。可能是因为内容完全不符合SRT格式，或仅包含无效的头部/尾部标记。' });
        logger.error(`${logPrefix}[步骤 1.4.1][错误] 未找到有效的SRT字幕块。`);
        return { isValid: false, errors, warnings, statistics: { totalBlocks: 0 } };
    }
    
    // 步骤 1.5: 逐块校验
    blocks.forEach((block, index) => {
        const blockErrors = validateSrtBlock(block, index + 1, `${logPrefix}[块校验子流程]`);
        errors.push(...blockErrors);
    });
    
    // 步骤 1.6: 全局校验
    const globalErrors = validateGlobalStructure(blocks, `${logPrefix}[全局校验子流程]`);
    errors.push(...globalErrors);
    
    // 步骤 1.7: 生成统计信息
    const statistics = {
        totalBlocks: blocks.length,
        totalErrors: errors.length,
        totalWarnings: warnings.length,
        contentLength: srtContent.length
    };
    
    logger.info(`${logPrefix}[步骤 2] SRT格式校验完成。总块数: ${statistics.totalBlocks}, 错误数: ${statistics.totalErrors}, 警告数: ${statistics.totalWarnings}。`);
    
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        statistics
    };
}

/**
 * @功能概述: 解析SRT字幕内容为结构化的块数组，并处理可能存在的头部和尾部无效文本。
 * @param {string} srtContent - 原始SRT字幕内容字符串。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][parseSrtBlocks]'] - 日志前缀。
 * @returns {object} 解析结果对象，包含:
 *                   - blocks: {Array<object>} 解析出的SRT块对象数组。
 *                   - headerStripped: {boolean} 是否移除了头部非SRT文本。
 *                   - originalHeader: {string|null} 被移除的原始头部文本内容，如果存在。
 * @执行流程:
 *   0. 预处理：移除整个内容开头和结尾可能存在的 Markdown 代码块标记 (如 ```plaintext, ```)。
 *   1. 进一步检测并移除SRT标准块之前的任何剩余非SRT头部文本。
 *   2. 分割块：使用标准SRT块分隔符（连续两个换行符）将内容分割成原始块。
 *   3. 解析块：遍历每个原始块，提取序号、时间戳和文本内容。
 *   4. 清理文本：移除每个文本块尾部可能存在的 "```" 标记。
 *   5. 构建块对象：将有效信息组装成结构化的块对象。
 *   6. 记录无效块：对未能成功解析的块进行记录。
 */
function parseSrtBlocks(srtContent, logPrefix = '[文件：srtValidator.js][SRT校验工具][parseSrtBlocks]') {
    logger.debug(`${logPrefix}[步骤 0.1] 开始预处理SRT内容。原始长度: ${srtContent.length}`);
    let preProcessedContent = srtContent;

    // 步骤 0.2: 移除整个内容开头可能存在的 ```plaintext 或 ``` 标记及其后的空行
    // 正则表达式解释:
    // ^                - 匹配字符串的开始
    // \s*              - 匹配零个或多个空白字符 (包括换行符)
    // ```(?:plaintext)? - 匹配 ``` 后面可选地跟着 plaintext
    // \s*              - 匹配零个或多个空白字符
    // (?:[\r\n]+)?     - 匹配一个或多个换行符 (可选的，非捕获组) -> 修改为 [\r\n]* 匹配零个或多个，以处理紧跟文本的情况
    const leadingFenceRegex = /^\s*```(?:plaintext)?\s*[\r\n]*/i;
    const leadingFenceMatchResult = preProcessedContent.match(leadingFenceRegex);
    if (leadingFenceMatchResult) {
        const removedLeadingFence = leadingFenceMatchResult[0];
        preProcessedContent = preProcessedContent.substring(removedLeadingFence.length);
        logger.debug(`${logPrefix}[步骤 0.2.1][信息] 从内容头部移除了标记和随后的换行: "${removedLeadingFence.trim()}"。处理后长度: ${preProcessedContent.length}`);
    }

    // 步骤 0.3: 移除整个内容末尾可能存在的 ``` 标记及其前的空行
    // 正则表达式解释:
    // (?:[\r\n]+\s*)? - 匹配可选的一个或多个换行符后跟零个或多个空白字符 -> 修改为 [\r\n]*\s*
    // ```              - 匹配 ```
    // \s*              - 匹配零个或多个空白字符
    // $                - 匹配字符串的结束
    const trailingFenceRegex = /[\r\n]*\s*```\s*$/i;
    const trailingFenceMatchResult = preProcessedContent.match(trailingFenceRegex);
    if (trailingFenceMatchResult) {
        const removedTrailingFence = trailingFenceMatchResult[0];
        preProcessedContent = preProcessedContent.substring(0, preProcessedContent.length - removedTrailingFence.length);
        logger.debug(`${logPrefix}[步骤 0.3.1][信息] 从内容尾部移除了标记和之前的换行: "${removedTrailingFence.trim()}"。处理后长度: ${preProcessedContent.length}`);
    }
    
    preProcessedContent = preProcessedContent.trim(); // 移除可能因上述操作产生的首尾真正空白行或字符
    logger.debug(`${logPrefix}[步骤 0.4] 预处理和trim后内容长度: ${preProcessedContent.length}`);


    let actualSrtContent = preProcessedContent;
    let strippedHeaderText = null;

    // 步骤 1.1: 检测并移除SRT头部可能存在的其他无效文本（在预处理的Markdown标记之后）
    const firstBlockRegex = /(?:^|\r?\n)(\d+)\r?\n(\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3})/;
    const firstBlockMatch = actualSrtContent.match(firstBlockRegex);

    if (firstBlockMatch) {
        let indexOfFirstBlockStartChar = actualSrtContent.indexOf(firstBlockMatch[0]); // 使用 match 的结果找到精确位置
        // 如果 firstBlockMatch[0] 本身就以换行符开头，那么 index 就是对的。
        // 如果 firstBlockMatch[0] 不以换行符开头 (即它是文件最开始的部分)，index 也是对的。
        // 需要处理的是 firstBlockMatch[0] 以换行符开头，但我们要的是数字序号的开始。
        if (firstBlockMatch[0].startsWith('\n') || firstBlockMatch[0].startsWith('\r\n')) {
             // firstBlockMatch[1] 是第一个捕获组 (数字序号)
             // 找到这个数字序号在 originalMatch 中的位置
            const indexOfSequenceInMatch = firstBlockMatch[0].indexOf(firstBlockMatch[1]);
            indexOfFirstBlockStartChar += indexOfSequenceInMatch;
        }
        
        if (indexOfFirstBlockStartChar > 0) {
            const potentialHeaderText = actualSrtContent.substring(0, indexOfFirstBlockStartChar).trim();
            if (potentialHeaderText) { 
                strippedHeaderText = potentialHeaderText;
                actualSrtContent = actualSrtContent.substring(indexOfFirstBlockStartChar);
                logger.debug(`${logPrefix}[步骤 1.1.1][警告] 检测到并移除了SRT头部非标准文本 (在Markdown标记清理后)。被移除内容预览: "${strippedHeaderText.substring(0, 100)}${strippedHeaderText.length > 100 ? '...' : ''}"`);
            }
        }
    } else if (actualSrtContent.length > 0) { 
        logger.warn(`${logPrefix}[步骤 1.1.2][警告] 经过预处理后，仍未能匹配到任何有效的SRT块起始模式。剩余内容可能全部为无效文本。内容预览: "${actualSrtContent.substring(0, 200)}${actualSrtContent.length > 200 ? '...' : ''}"`);
        // 如果剩余内容不是以数字开头（潜在的SRT序号），则认为全部是垃圾
        if (!/^\d/.test(actualSrtContent.trim())) {
            logger.warn(`${logPrefix}[步骤 1.1.3][警告] 判定剩余内容为无效SRT，将清空。原始剩余内容: "${actualSrtContent.substring(0,100)}..."`);
            strippedHeaderText = (strippedHeaderText ? strippedHeaderText + "\n" : "") + actualSrtContent; // 累加到已移除的头部
            actualSrtContent = "";
        }
    }


    const blocks = [];
    if (actualSrtContent.trim() === "") {
        logger.info(`${logPrefix}[步骤 1.1.4][信息] 经过预处理和头部移除后，内容为空，不进行块分割。`);
    } else {
        const rawBlocks = actualSrtContent.split(/\r?\n\s*\r?\n/);
        logger.debug(`${logPrefix}[步骤 1.3] 原始分割得到 ${rawBlocks.length} 个块，开始逐块解析。`);
        rawBlocks.forEach((rawBlock, index) => {
            const trimmedBlock = rawBlock.trim();
            if (!trimmedBlock) {
                return; 
            }
            
            const lines = trimmedBlock.split(/\r?\n/);
            
            // 块有效性检查：序号行 + 时间戳行
            if (lines.length >= 2 && lines[0]?.match(/^\d+$/) && lines[1]?.match(/^(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})$/)) {
                let blockText = lines.slice(2).join('\n').trim(); // 文本内容可能为空字符串
                
                // 再次检查并移除块文本末尾可能由LLM添加的 "```" 标记 (这一步依然有用，以防万一)
                const trailingBlockFenceRegex = /(\r?\n\s*)?```\s*$/;
                if (trailingBlockFenceRegex.test(blockText)) {
                    const originalBlockTextLength = blockText.length;
                    blockText = blockText.replace(trailingBlockFenceRegex, '').trimEnd();
                    logger.debug(`${logPrefix}[步骤 1.3.${index + 1}.A][信息] 从块文本中移除了尾随的 "\`\`\`" 标记。块序号候补: ${lines[0]}. 原文长度: ${originalBlockTextLength}, 清理后长度: ${blockText.length}`);
                }

                blocks.push({
                    sequence: lines[0].trim(),
                    timestamp: lines[1].trim(),
                    text: blockText,
                    textLines: blockText.split(/\r?\n/).map(line => line.trim()),
                    raw: trimmedBlock, 
                    blockIndexInSource: index 
                });
            } else if (trimmedBlock) { // 如果块非空但无效
                 logger.debug(`${logPrefix}[步骤 1.3.${index + 1}.C][警告] 跳过无效SRT块结构 (原始索引 ${index})。原始块内容 (前100字符): "${trimmedBlock.substring(0,100)}"`);
            }
        });
    }
    
    logger.info(`${logPrefix}[步骤 2] SRT块解析完成。成功解析 ${blocks.length} 个有效块。${strippedHeaderText ? '检测并移除了头部文本。' : '未检测到或已通过预处理移除头部文本。'}`);
    return { blocks, headerStripped: !!strippedHeaderText, originalHeader: strippedHeaderText };
}

/**
 * @功能概述: 校验单个SRT字幕块的格式和逻辑。
 * @param {object} block - 需要校验的SRT块对象。
 * @param {number} expectedSequence - 期望的字幕序号。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][validateSrtBlock]'] - 日志前缀。
 * @returns {Array<object>} 错误对象数组，每个对象描述一个具体的校验错误。
 * @执行流程:
 *   1. 校验序号是否为纯数字，以及是否与期望序号一致。
 *   2. 校验时间戳格式是否符合 "HH:MM:SS,mmm --> HH:MM:SS,mmm"。
 *   3. 校验时间逻辑，确保开始时间不晚于结束时间。
 *   4. 校验文本内容是否为空。
 */
function validateSrtBlock(block, expectedSequence, logPrefix = '[文件：srtValidator.js][SRT校验工具][validateSrtBlock]') {
    const errors = [];
    const blockIdentifier = `块 ${expectedSequence} (原始序号 ${block.sequence})`; // 用于日志和错误消息
    
    // 步骤 1: 序号校验
    const sequence = parseInt(block.sequence);
    if (isNaN(sequence) || !block.sequence.match(/^\d+$/)) {
        errors.push({
            type: 'INVALID_SEQUENCE_FORMAT', // 更具体的错误类型
            message: `${blockIdentifier} 序号格式错误: "${block.sequence}" (应为纯数字)。`,
            block: expectedSequence,
            actual: block.sequence
        });
    } else if (sequence !== expectedSequence) {
        // 序号不匹配通常在全局修复阶段处理，这里仅作为单个块的校验点记录
        errors.push({
            type: 'MISMATCHED_SEQUENCE', // 更具体的错误类型
            message: `${blockIdentifier} 序号与期望不符: 期望 ${expectedSequence}, 实际 ${sequence}。`,
            block: expectedSequence,
            expected: expectedSequence,
            actual: sequence
        });
    }
    
    // 步骤 2: 时间戳格式校验
    const timestampRegex = /^(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})$/;
    if (!timestampRegex.test(block.timestamp)) {
        errors.push({
            type: 'INVALID_TIMESTAMP_FORMAT', // 更具体的错误类型
            message: `${blockIdentifier} 时间戳格式错误: "${block.timestamp}" (标准格式: HH:MM:SS,mmm --> HH:MM:SS,mmm)。`,
            block: expectedSequence,
            actual: block.timestamp
        });
    } else {
        // 步骤 2.1: 时间逻辑校验
        const match = block.timestamp.match(timestampRegex);
        // match[0]是整个匹配，match[1]到match[4]是开始时间部分，match[5]到match[8]是结束时间部分
        const startTime = parseTimeToMs(match[1], match[2], match[3], match[4]);
        const endTime = parseTimeToMs(match[5], match[6], match[7], match[8]);
        
        if (startTime > endTime) { // 标准SRT允许开始等于结束（空时长），但不允许开始晚于结束
            errors.push({
                type: 'INVALID_TIME_RANGE',
                message: `${blockIdentifier} 时间范围逻辑错误: 开始时间 (${match[1]}:${match[2]}:${match[3]},${match[4]}) 不应晚于结束时间 (${match[5]}:${match[6]}:${match[7]},${match[8]})。`,
                block: expectedSequence,
                startTimeMs: startTime,
                endTimeMs: endTime,
                timestampString: block.timestamp
            });
        }
    }
    
    // 步骤 3: 文本内容校验（仅校验是否存在，不校验内容质量）
    if (!block.text || block.text.trim() === '') {
        errors.push({
            type: 'EMPTY_TEXT',
            message: `${blockIdentifier} 文本内容为空。`,
            block: expectedSequence
        });
    }
    
    if (errors.length > 0) {
        logger.debug(`${logPrefix}[${blockIdentifier}] 发现 ${errors.length} 个错误。`);
    }
    return errors;
}

/**
 * @功能概述: 对SRT块数组进行全局结构校验，例如检查块之间的时间是否有重叠。
 * @param {Array<object>} blocks - 已解析的SRT块对象数组。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][validateGlobalStructure]'] - 日志前缀。
 * @returns {Array<object>} 描述全局结构错误的错误对象数组。
 */
function validateGlobalStructure(blocks, logPrefix = '[文件：srtValidator.js][SRT校验工具][validateGlobalStructure]') {
    const errors = [];
    logger.debug(`${logPrefix}[步骤 1] 开始全局结构校验，共有 ${blocks.length} 个块。`);
    
    // 步骤 1.1: 检查时间重叠
    for (let i = 0; i < blocks.length - 1; i++) {
        const currentBlock = blocks[i];
        const nextBlock = blocks[i + 1];
        
        const currentEndTime = extractEndTime(currentBlock.timestamp);
        const nextStartTime = extractStartTime(nextBlock.timestamp);
        
        // 只有当时间戳都能成功解析时才进行比较
        if (currentEndTime !== null && nextStartTime !== null && currentEndTime > nextStartTime) {
            errors.push({
                type: 'TIME_OVERLAP',
                message: `块 ${currentBlock.sequence} (结束于 ${formatTimeFromMs(currentEndTime)}) 与块 ${nextBlock.sequence} (开始于 ${formatTimeFromMs(nextStartTime)}) 存在时间重叠。`,
                blockSequence: currentBlock.sequence,
                nextBlockSequence: nextBlock.sequence,
                currentBlockEndTime: formatTimeFromMs(currentEndTime),
                nextBlockStartTime: formatTimeFromMs(nextStartTime)
            });
            logger.warn(`${logPrefix}[步骤 1.1.${i + 1}][警告] 检测到时间重叠: 块 ${currentBlock.sequence} 与 ${nextBlock.sequence}。`);
        }
    }
    
    logger.info(`${logPrefix}[步骤 2] 全局结构校验完成，发现 ${errors.length} 个时间重叠问题。`);
    return errors;
}

/**
 * @功能概述: 根据错误列表尝试修复SRT字幕内容。
 * @param {string} srtContent - 原始SRT字幕内容。
 * @param {Array<object>} errors - 从 `validateSrtFormat` 函数获取的错误对象列表。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][repairSrtContent]'] - 日志前缀。
 * @returns {object} 修复结果对象，包含:
 *                   - repairedContent: {string} 修复后的SRT内容字符串。
 *                   - repairLog: {Array<object>} 修复操作的日志记录。
 *                   - success: {boolean} 是否成功执行了至少一项修复操作。
 * @执行流程:
 *   1. 重新解析SRT内容为块，同时记录头部文本移除情况。
 *   2. 按错误类型对错误进行分组。
 *   3. 针对不同类型的错误调用相应的修复函数（序号、时间戳格式、时间重叠、空文本）。
 *   4. 从修复后的块数组重新构建SRT内容。
 */
function repairSrtContent(srtContent, errors, logPrefix = '[文件：srtValidator.js][SRT校验工具][repairSrtContent]') {
    logger.info(`${logPrefix}[步骤 1] 开始尝试修复SRT内容，待处理错误数量: ${errors.length}。`);
    
    const repairLog = [];
    // 步骤 1.1: 重新解析SRT，确保使用最新的解析逻辑处理原始数据
    let { blocks, headerStripped, originalHeader } = parseSrtBlocks(srtContent, `${logPrefix}[解析子流程-修复阶段]`);

    if (headerStripped) {
        const headerMessage = `修复前置步骤：解析时已自动移除了头部非SRT文本（可能包含 'plaintext' 或 '\`\`\`'）。移除内容预览: "${originalHeader.substring(0,70)}${originalHeader.length > 70 ? '...' : ''}"`;
        logger.info(`${logPrefix}[步骤 1.1.1] ${headerMessage}`);
        repairLog.push({
            type: 'HEADER_STRIPPED_DURING_PARSE_FOR_REPAIR',
            message: headerMessage,
            removedHeader: originalHeader
        });
    }
    
    // 步骤 1.2: 按错误类型分组
    const errorsByType = groupErrorsByType(errors);
    logger.debug(`${logPrefix}[步骤 1.2.1] 错误已按类型分组: ${Object.keys(errorsByType).join(', ')}`);
    
    // 步骤 1.3: 修复序号错误 (包括格式错误和顺序错误)
    if (errorsByType.MISMATCHED_SEQUENCE || errorsByType.INVALID_SEQUENCE_FORMAT) {
        logger.info(`${logPrefix}[步骤 1.3.1] 检测到序号错误，开始修复序号。`);
        const sequenceRepair = repairSequenceNumbers(blocks, `${logPrefix}[序号修复子流程]`);
        blocks = sequenceRepair.blocks;
        repairLog.push(...sequenceRepair.log);
    }
    
    // 步骤 1.4: 修复时间戳格式错误
    if (errorsByType.INVALID_TIMESTAMP_FORMAT) {
        logger.info(`${logPrefix}[步骤 1.4.1] 检测到时间戳格式错误，开始修复。`);
        const timestampRepair = repairTimestamps(blocks, errorsByType.INVALID_TIMESTAMP_FORMAT, `${logPrefix}[时间戳修复子流程]`);
        blocks = timestampRepair.blocks;
        repairLog.push(...timestampRepair.log);
    }
    
    // 步骤 1.5: 修复时间重叠 (应在时间戳格式修复后进行)
    // 注意: 时间重叠的检测 (validateGlobalStructure) 是基于修复后的块列表重新进行的，或者依赖初次检测结果。
    // 为简化，这里假设 errorsByType.TIME_OVERLAP 来自初次检测。更稳健的做法是在修复其他项后重新检测重叠。
    // 但当前函数设计是传入一个固定的 errors 列表。
    if (errorsByType.TIME_OVERLAP) {
        logger.info(`${logPrefix}[步骤 1.5.1] 检测到时间重叠错误，开始修复。`);
        const overlapRepair = repairTimeOverlaps(blocks, errorsByType.TIME_OVERLAP, `${logPrefix}[时间重叠修复子流程]`);
        blocks = overlapRepair.blocks;
        repairLog.push(...overlapRepair.log);
    }
    
    // 步骤 1.6: 修复空文本 (仅格式修复，填充占位符)
    if (errorsByType.EMPTY_TEXT) {
        logger.info(`${logPrefix}[步骤 1.6.1] 检测到空文本错误，开始填充占位符。`);
        const textRepair = repairEmptyTexts(blocks, errorsByType.EMPTY_TEXT, `${logPrefix}[空文本修复子流程]`);
        blocks = textRepair.blocks;
        repairLog.push(...textRepair.log);
    }
    
    // 步骤 1.7: 重新生成SRT内容
    const repairedContent = reconstructSrtFromBlocks(blocks);
    
    logger.info(`${logPrefix}[步骤 2] SRT内容修复完成。共执行了 ${repairLog.length} 项修复操作。`);
    
    return {
        repairedContent,
        repairLog,
        success: repairLog.length > 0 // 表示是否进行了任何修复尝试
    };
}

/**
 * @功能概述: 执行完整的SRT校验和修复流程。首先进行初次校验，如果内容无效，则尝试进行修复，然后对修复后的内容进行最终校验。
 * @param {string} srtContent - 原始SRT字幕内容字符串。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][validateAndRepairSrt]'] - 日志前缀。
 * @returns {object} 处理结果对象，包含:
 *                   - finalContent: {string} 最终的SRT内容（可能是原始的，也可能是修复后的）。
 *                   - validationReport: {object} 包含初次校验结果、修复操作日志（如果执行了修复）和最终校验结果的报告。
 *                   - success: {boolean} 指示最终的SRT内容是否通过了校验。
 * @执行流程:
 *   1. 对原始SRT内容进行初次校验。
 *   2. 如果初次校验无效，则调用 `repairSrtContent` 尝试修复。
 *   3. 对修复后（或原始，如果未执行修复）的内容进行最终校验。
 *   4. 组装并返回包含所有处理阶段信息的报告。
 */
function validateAndRepairSrt(srtContent, logPrefix = '[文件：srtValidator.js][SRT校验工具][validateAndRepairSrt]') {
    logger.info(`${logPrefix}[步骤 1] 开始执行完整的SRT校验和修复流程。`);
    
    try {
        // 步骤 1.1: 初次校验
        logger.info(`${logPrefix}[步骤 1.1.1] 执行初次校验...`);
        const initialValidation = validateSrtFormat(srtContent, `${logPrefix}[初次校验子流程]`);
        
        let finalContent = srtContent;
        let repairResult = null;
        
        // 步骤 1.2: 如果初次校验无效，则尝试修复
        if (!initialValidation.isValid) {
            logger.warn(`${logPrefix}[步骤 1.2.1][警告] 初次校验发现 ${initialValidation.errors.length} 个错误，将尝试进行修复。`);
            repairResult = repairSrtContent(srtContent, initialValidation.errors, `${logPrefix}[修复子流程]`);
            finalContent = repairResult.repairedContent;
            logger.info(`${logPrefix}[步骤 1.2.2] 修复尝试完成。`);
        } else {
            logger.info(`${logPrefix}[步骤 1.2.3] 初次校验通过，无需修复。`);
        }
        
        // 步骤 1.3: 对最终内容进行校验（无论是原始的还是修复后的）
        logger.info(`${logPrefix}[步骤 1.3.1] 对最终内容执行校验...`);
        const finalValidation = validateSrtFormat(finalContent, `${logPrefix}[最终校验子流程]`);
        
        // 步骤 1.4: 构建结果报告
        const validationReport = {
            initialValidation,
            repairResult, // 可能为null
            finalValidation,
            processingSuccess: finalValidation.isValid, // 最终内容是否有效
            // 判断是否通过修复改进了SRT质量
            improvementMade: repairResult ? 
                             (initialValidation.errors.length > finalValidation.errors.length || (initialValidation.errors.length > 0 && finalValidation.isValid)) : 
                             false
        };
        
        logger.info(`${logPrefix}[步骤 2] SRT校验和修复流程处理完成。最终校验状态: ${finalValidation.isValid ? '通过' : '失败'}。`);
        if (validationReport.improvementMade) {
            logger.info(`${logPrefix}[步骤 2.1] 通过修复操作，错误数量减少或内容变为有效。`);
        }
        
        return {
            finalContent,
            validationReport,
            success: finalValidation.isValid
        };
        
    } catch (error) {
        logger.error(`${logPrefix}[步骤 E.1][错误] 在SRT校验和修复流程中发生未预期错误: ${error.message}`, { stack: error.stack });
        return {
            finalContent: srtContent, // 返回原始内容以防万一
            validationReport: {
                processingError: `处理过程中发生内部错误: ${error.message}`,
                initialValidation: null, // 发生错误时，这些可能未完成
                repairResult: null,
                finalValidation: null,
                processingSuccess: false
            },
            success: false
        };
    }
}

// 辅助函数区域

/**
 * @功能概述: 将SRT时间格式（时、分、秒、毫秒）字符串转换为总毫秒数。
 * @param {string|number} hours - 小时部分。
 * @param {string|number} minutes - 分钟部分。
 * @param {string|number} seconds - 秒部分。
 * @param {string|number} milliseconds - 毫秒部分。
 * @returns {number} 转换后的总毫秒数。
 */
function parseTimeToMs(hours, minutes, seconds, milliseconds) {
    return parseInt(hours, 10) * 3600000 + 
           parseInt(minutes, 10) * 60000 + 
           parseInt(seconds, 10) * 1000 + 
           parseInt(milliseconds, 10);
}

/**
 * @功能概述: 从标准SRT时间戳字符串中提取开始时间，并转换为毫秒。
 * @param {string} timestamp - SRT时间戳字符串 (格式: "HH:MM:SS,mmm --> HH:MM:SS,mmm")。
 * @returns {number|null} 开始时间的毫秒数；如果时间戳格式无效，则返回null。
 */
function extractStartTime(timestamp) {
    // 正则匹配时间戳的开始部分
    const match = timestamp.match(/^(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
    return match ? parseTimeToMs(match[1], match[2], match[3], match[4]) : null;
}

/**
 * @功能概述: 从标准SRT时间戳字符串中提取结束时间，并转换为毫秒。
 * @param {string} timestamp - SRT时间戳字符串 (格式: "HH:MM:SS,mmm --> HH:MM:SS,mmm")。
 * @returns {number|null} 结束时间的毫秒数；如果时间戳格式无效，则返回null。
 */
function extractEndTime(timestamp) {
    // 正则匹配时间戳的结束部分
    const match = timestamp.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})$/);
    return match ? parseTimeToMs(match[1], match[2], match[3], match[4]) : null;
}

/**
 * @功能概述: 将错误对象数组按照 `error.type` 属性进行分组。
 * @param {Array<object>} errors - 错误对象数组。
 * @returns {object} 一个以错误类型为键，对应错误对象数组为值的对象。
 */
function groupErrorsByType(errors) {
    return errors.reduce((groups, error) => {
        const type = error.type || 'UNKNOWN_TYPE'; // 为没有类型的错误提供默认分组
        groups[type] = groups[type] || [];
        groups[type].push(error);
        return groups;
    }, {});
}

/**
 * @功能概述: 修复SRT块数组中的序号，确保它们从1开始连续递增。
 * @param {Array<object>} blocks - SRT块对象数组 (此数组将被直接修改)。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][repairSequenceNumbers]'] - 日志前缀。
 * @returns {object} 包含修复后块数组和修复日志的对象: { blocks: Array<object>, log: Array<object> }。
 */
function repairSequenceNumbers(blocks, logPrefix = '[文件：srtValidator.js][SRT校验工具][repairSequenceNumbers]') {
    const repairLog = [];
    logger.debug(`${logPrefix}[步骤 1] 开始修复序号，共 ${blocks.length} 个块。`);
    blocks.forEach((block, index) => {
        const expectedSequence = index + 1;
        if (parseInt(block.sequence, 10) !== expectedSequence) {
            const oldSeq = block.sequence;
            block.sequence = expectedSequence.toString();
            const message = `修复了块的序号: 从 "${oldSeq}" 改为 "${block.sequence}" (基于其在数组中的位置 ${index})。`;
            repairLog.push({
                type: 'SEQUENCE_REPAIR',
                message: message,
                blockIndexInSourceArray: index, // 使用数组索引更准确
                originalSequenceAttempted: oldSeq,
                repairedSequence: block.sequence
            });
            logger.debug(`${logPrefix}[步骤 1.${index + 1}] ${message}`);
        }
    });
    logger.info(`${logPrefix}[步骤 2] 序号修复完成，执行了 ${repairLog.length} 项修复。`);
    return { blocks, log: repairLog };
}

/**
 * @功能概述: 尝试修复SRT块中常见的时间戳格式错误 (例如，分隔符错误)。
 * @param {Array<object>} blocks - SRT块对象数组 (此数组将被直接修改)。
 * @param {Array<object>} timestampErrors - 从 `validateSrtFormat` 收集的、类型为 `INVALID_TIMESTAMP_FORMAT` 的特定时间戳错误对象数组。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][repairTimestamps]'] - 日志前缀。
 * @returns {object} 包含修复后块数组和修复日志的对象: { blocks: Array<object>, log: Array<object> }。
 */
function repairTimestamps(blocks, timestampErrors, logPrefix = '[文件：srtValidator.js][SRT校验工具][repairTimestamps]') {
    const repairLog = [];
    logger.debug(`${logPrefix}[步骤 1] 开始修复时间戳格式错误，共有 ${timestampErrors.length} 个相关错误报告。`);
    timestampErrors.forEach(error => {
        // error.block 是基于1的期望序号，需要转换为数组索引
        const blockArrayIndex = blocks.findIndex(b => b.sequence === error.actual?.toString() || (b.blockIndexInSource + 1) === error.block); // 尝试通过原始序号或索引定位
        
        if (blockArrayIndex !== -1 && blocks[blockArrayIndex]) {
            const originalTimestamp = blocks[blockArrayIndex].timestamp;
            let fixedTimestamp = originalTimestamp;
            
            // 常见修复1: 将箭头 "->" 替换为 "-->" (考虑两侧空格)
            fixedTimestamp = fixedTimestamp.replace(/\s*-\s*>\s*/g, ' --> ');
            
            // 常见修复2: 将毫秒分隔符 "." 替换为 ","
            // 例如: 00:00:01.000 --> 00:00:02.000  应为 00:00:01,000 --> 00:00:02,000
            fixedTimestamp = fixedTimestamp.replace(/(\d{2}:\d{2}:\d{2})\.(\d{3})/g, '$1,$2');
            
            if (fixedTimestamp !== originalTimestamp) {
                blocks[blockArrayIndex].timestamp = fixedTimestamp;
                const message = `修复了块 ${blocks[blockArrayIndex].sequence} 的时间戳格式: 从 "${originalTimestamp}" 改为 "${fixedTimestamp}"。`;
                repairLog.push({
                    type: 'TIMESTAMP_FORMAT_REPAIR',
                    message: message,
                    blockSequence: blocks[blockArrayIndex].sequence,
                    originalTimestamp: originalTimestamp,
                    repairedTimestamp: fixedTimestamp
                });
                logger.debug(`${logPrefix}[步骤 1.${blockArrayIndex + 1}] ${message}`);
            }
        } else {
            logger.warn(`${logPrefix}[步骤 1.X][警告] 未能找到错误报告中提及的块 (期望序号 ${error.block}) 进行时间戳修复。`);
        }
    });
    logger.info(`${logPrefix}[步骤 2] 时间戳格式修复完成，执行了 ${repairLog.length} 项修复。`);
    return { blocks, log: repairLog };
}

/**
 * @功能概述: 尝试修复SRT块之间的时间重叠问题。
 * @param {Array<object>} blocks - SRT块对象数组 (此数组将被直接修改)。
 * @param {Array<object>} overlapErrors - 从 `validateSrtFormat` 收集的、类型为 `TIME_OVERLAP` 的特定时间重叠错误对象数组。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][repairTimeOverlaps]'] - 日志前缀。
 * @returns {object} 包含修复后块数组和修复日志的对象: { blocks: Array<object>, log: Array<object> }。
 */
function repairTimeOverlaps(blocks, overlapErrors, logPrefix = '[文件：srtValidator.js][SRT校验工具][repairTimeOverlaps]') {
    const repairLog = [];
    logger.debug(`${logPrefix}[步骤 1] 开始修复时间重叠问题，共有 ${overlapErrors.length} 个相关错误报告。`);
    // 注意：修复一个重叠可能会影响后续的重叠情况，理想情况下应迭代修复或使用更复杂的调整策略。
    // 当前实现为简单修复，仅调整前一个块的结束时间。
    overlapErrors.forEach(error => {
        const currentBlock = blocks.find(b => b.sequence === error.blockSequence);
        const nextBlock = blocks.find(b => b.sequence === error.nextBlockSequence);

        if (currentBlock && nextBlock) {
            const nextStartTimeMs = extractStartTime(nextBlock.timestamp);
            if (nextStartTimeMs !== null) {
                const currentStartTimeMs = extractStartTime(currentBlock.timestamp);
                let newCurrentEndTimeMs = nextStartTimeMs - 1; // 尝试将当前块结束时间设为下一块开始时间的前1毫秒

                // 确保修复后的结束时间不早于其开始时间
                if (newCurrentEndTimeMs < currentStartTimeMs) {
                    newCurrentEndTimeMs = currentStartTimeMs; // 如果重叠过大，则使当前块时长为0或极短
                    logger.warn(`${logPrefix}[步骤 1.X][警告] 块 ${currentBlock.sequence} 与 ${nextBlock.sequence} 重叠严重，修复后可能导致时长为零。`);
                }

                const newEndTimeFormatted = formatTimeFromMs(newCurrentEndTimeMs);
                const originalCurrentTimestamp = currentBlock.timestamp;
                // 替换时间戳中的结束时间部分
                const newCurrentTimestamp = originalCurrentTimestamp.replace(/(\d{2}:\d{2}:\d{2},\d{3})$/, newEndTimeFormatted);
                
                if (newCurrentTimestamp !== originalCurrentTimestamp) {
                    currentBlock.timestamp = newCurrentTimestamp;
                    const message = `修复了块 ${currentBlock.sequence} 与 ${nextBlock.sequence} 的时间重叠。将块 ${currentBlock.sequence} 的结束时间调整为 ${newEndTimeFormatted}。`;
                    repairLog.push({
                        type: 'TIME_OVERLAP_REPAIR',
                        message: message,
                        repairedBlockSequence: currentBlock.sequence,
                        originalTimestamp: originalCurrentTimestamp,
                        repairedTimestamp: newCurrentTimestamp
                    });
                    logger.debug(`${logPrefix}[步骤 1.${currentBlock.sequence}] ${message}`);
                }
            } else {
                 logger.warn(`${logPrefix}[步骤 1.X][警告] 未能解析下一块 ${nextBlock.sequence} 的开始时间，无法修复与块 ${currentBlock.sequence} 的重叠。`);
            }
        } else {
            logger.warn(`${logPrefix}[步骤 1.X][警告] 未能找到错误报告中涉及的块 (块 ${error.blockSequence} 或 ${error.nextBlockSequence}) 进行时间重叠修复。`);
        }
    });
    logger.info(`${logPrefix}[步骤 2] 时间重叠修复完成，执行了 ${repairLog.length} 项修复。`);
    return { blocks, log: repairLog };
}

/**
 * @功能概述: 修复SRT块中的空文本内容，为其填充一个占位符。
 * @param {Array<object>} blocks - SRT块对象数组 (此数组将被直接修改)。
 * @param {Array<object>} textErrors - 从 `validateSrtFormat` 收集的、类型为 `EMPTY_TEXT` 的特定空文本错误对象数组。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][repairEmptyTexts]'] - 日志前缀。
 * @returns {object} 包含修复后块数组和修复日志的对象: { blocks: Array<object>, log: Array<object> }。
 */
function repairEmptyTexts(blocks, textErrors, logPrefix = '[文件：srtValidator.js][SRT校验工具][repairEmptyTexts]') {
    const repairLog = [];
    logger.debug(`${logPrefix}[步骤 1] 开始修复空文本块，共有 ${textErrors.length} 个相关错误报告。`);
    textErrors.forEach(error => {
        const blockToRepair = blocks.find(b => b.sequence === error.block?.toString()); // error.block是期望序号
        if (blockToRepair) {
            const placeholderText = `[字幕内容空缺 #${blockToRepair.sequence}]`;
            blockToRepair.text = placeholderText;
            blockToRepair.textLines = [placeholderText];
            const message = `为块 ${blockToRepair.sequence} 的空文本内容填充了占位符: "${placeholderText}"。`;
            repairLog.push({
                type: 'EMPTY_TEXT_REPAIR',
                message: message,
                blockSequence: blockToRepair.sequence,
                filledPlaceholder: placeholderText
            });
            logger.debug(`${logPrefix}[步骤 1.${blockToRepair.sequence}] ${message}`);
        } else {
            logger.warn(`${logPrefix}[步骤 1.X][警告] 未能找到错误报告中提及的空文本块 (期望序号 ${error.block}) 进行修复。`);
        }
    });
    logger.info(`${logPrefix}[步骤 2] 空文本修复完成，执行了 ${repairLog.length} 项修复。`);
    return { blocks, log: repairLog };
}

/**
 * @功能概述: 从结构化的SRT块对象数组重新构建SRT文件内容的字符串表示。
 * @param {Array<object>} blocks - SRT块对象数组，每个块应包含 `sequence`, `timestamp`, 和 `text` 属性。
 * @returns {string} 重建的SRT字幕内容字符串。每个块后跟两个换行符，文件末尾有一个额外换行符。
 */
function reconstructSrtFromBlocks(blocks) {
    // 确保每个块之间用两个换行符分隔，并且整个文件以一个换行符结尾（这是SRT的常见做法）
    return blocks.map(block => 
        `${block.sequence}\n${block.timestamp}\n${block.text}`
    ).join('\n\n') + (blocks.length > 0 ? '\n' : ''); 
}

/**
 * @功能概述: 将总毫秒数格式化为SRT时间戳中使用的时间格式 "HH:MM:SS,mmm"。
 * @param {number} timeMs - 需要格式化的总毫秒数。
 * @returns {string} 格式化后的时间字符串。
 */
function formatTimeFromMs(timeMs) {
    if (timeMs < 0) timeMs = 0; // 确保时间不会是负数，避免格式化出问题
    const hours = Math.floor(timeMs / 3600000).toString().padStart(2, '0');
    const minutes = Math.floor((timeMs % 3600000) / 60000).toString().padStart(2, '0');
    const seconds = Math.floor((timeMs % 60000) / 1000).toString().padStart(2, '0');
    const milliseconds = (timeMs % 1000).toString().padStart(3, '0');
    return `${hours}:${minutes}:${seconds},${milliseconds}`;
}

/**
 * @功能概述: 对比两个SRT字幕内容的结构特征，包括字幕块数量、每个块的序号和时间戳。
 * @param {string} originalSrtContent - 作为基准的原始SRT字幕内容。
 * @param {string} candidateSrtContent - 需要进行结构对比的候选SRT字幕内容。
 * @param {string} [logPrefix='[文件：srtValidator.js][SRT校验工具][compareSrtBlockStructures]'] - 日志前缀。
 * @returns {object} 包含以下属性的对比结果对象:
 *                   - isValid: {boolean} 候选SRT结构是否与原始SRT结构完全匹配。
 *                   - errors: {Array<object>} 描述所有结构差异的错误对象数组。
 *                   - originalStats: {object} 原始SRT的块统计信息。
 *                   - candidateStats: {object} 候选SRT的块统计信息。
 * @执行流程:
 *   1. 使用 parseSrtBlocks 分别解析原始和候选SRT内容。
 *   2. 比较解析出的字幕块数量。如果不匹配，则记录错误并立即返回。
 *   3. 如果块数量匹配，则逐块比较序号和时间戳。
 *   4. 记录所有发现的差异。
 *   5. 返回包含校验状态和错误列表的结果。
 */
function compareSrtBlockStructures(originalSrtContent, candidateSrtContent, logPrefix = '[文件：srtValidator.js][SRT校验工具][compareSrtBlockStructures]') {
    logger.debug(`${logPrefix}[步骤 1] 开始对比两个SRT内容的结构。`);
    const errors = [];

    const originalParseResult = parseSrtBlocks(originalSrtContent, `${logPrefix}[解析原始SRT]`);
    const candidateParseResult = parseSrtBlocks(candidateSrtContent, `${logPrefix}[解析候选SRT]`);

    const originalBlocks = originalParseResult.blocks;
    const candidateBlocks = candidateParseResult.blocks;

    const originalStats = { totalBlocks: originalBlocks.length, headerStripped: originalParseResult.headerStripped };
    const candidateStats = { totalBlocks: candidateBlocks.length, headerStripped: candidateParseResult.headerStripped };

    logger.info(`${logPrefix}[步骤 1.1] 原始SRT解析出 ${originalBlocks.length} 个块。候选SRT解析出 ${candidateBlocks.length} 个块。`);

    // 步骤 2: 比较字幕块数量
    if (originalBlocks.length !== candidateBlocks.length) {
        const errorMsg = `字幕块数量不匹配。原始SRT有 ${originalBlocks.length} 个块，候选SRT有 ${candidateBlocks.length} 个块。`;
        errors.push({
            type: 'BLOCK_COUNT_MISMATCH',
            message: errorMsg,
            originalCount: originalBlocks.length,
            candidateCount: candidateBlocks.length
        });
        logger.error(`${logPrefix}[错误] ${errorMsg}`);
        return { isValid: false, errors, originalStats, candidateStats };
    }

    if (originalBlocks.length === 0) {
        logger.info(`${logPrefix}[步骤 2.1] 两个SRT均不包含有效字幕块，结构视为一致（均为空）。`);
        return { isValid: true, errors, originalStats, candidateStats };
    }

    // 步骤 3: 逐块比较序号和时间戳
    logger.debug(`${logPrefix}[步骤 3] 字幕块数量一致 (${originalBlocks.length}个)，开始逐块比较。`);
    for (let i = 0; i < originalBlocks.length; i++) {
        const originalBlock = originalBlocks[i];
        const candidateBlock = candidateBlocks[i];
        const blockDisplayIndex = i + 1; // 用于错误报告的基于1的索引

        // 比较序号
        if (originalBlock.sequence !== candidateBlock.sequence) {
            const errorMsg = `字幕块 #${blockDisplayIndex}: 序号不匹配。原始SRT序号: "${originalBlock.sequence}", 候选SRT序号: "${candidateBlock.sequence}"。`;
            errors.push({
                type: 'SEQUENCE_NUMBER_MISMATCH',
                message: errorMsg,
                blockDisplayIndex: blockDisplayIndex,
                originalSequence: originalBlock.sequence,
                candidateSequence: candidateBlock.sequence,
                originalRawBlockIndex: originalBlock.blockIndexInSource,
                candidateRawBlockIndex: candidateBlock.blockIndexInSource
            });
            logger.warn(`${logPrefix}[警告] ${errorMsg}`);
        }

        // 比较时间戳
        if (originalBlock.timestamp !== candidateBlock.timestamp) {
            const errorMsg = `字幕块 #${blockDisplayIndex} (原始序号 ${originalBlock.sequence}): 时间戳不匹配。
  原始SRT时间戳: "${originalBlock.timestamp}"
  候选SRT时间戳: "${candidateBlock.timestamp}"`;
            errors.push({
                type: 'TIMESTAMP_MISMATCH',
                message: errorMsg,
                blockDisplayIndex: blockDisplayIndex,
                blockSequence: originalBlock.sequence, // 使用原始块的序号作为参考
                originalTimestamp: originalBlock.timestamp,
                candidateTimestamp: candidateBlock.timestamp
            });
            logger.warn(`${logPrefix}[警告] ${errorMsg.replace(/\n/g, ' ')}`); // 替换换行符以便单行日志显示
        }
    }

    const isValid = errors.length === 0;
    if (isValid) {
        logger.info(`${logPrefix}[步骤 4] 结构对比完成：所有 ${originalBlocks.length} 个字幕块的序号和时间戳均匹配。`);
    } else {
        logger.error(`${logPrefix}[步骤 4] 结构对比完成：发现 ${errors.length} 处结构差异。`);
    }

    return { isValid, errors, originalStats, candidateStats };
}

module.exports = {
    validateSrtFormat,
    repairSrtContent,
    validateAndRepairSrt,
    parseSrtBlocks, // 导出 parseSrtBlocks 以便外部可能需要单独使用
    compareSrtBlockStructures // 导出新的结构对比函数
};

// 模块级日志 - 确保在所有函数定义之后，模块导出之前或之后。
logger.info('[文件：srtValidator.js][SRT校验工具][模块更新] SRT字幕校验、解析、修复和结构对比工具模块已更新并导出。');

