/**
 * CSS到ASS样式映射器测试文件
 * 测试video-config.json中的CSS样式到ASS格式的映射功能
 */

const { CSSToASSMapper } = require('./css-to-ass-mapper');
const path = require('path');

/**
 * @功能概述: 主测试函数
 * @技术实现:
 *   1. 创建CSS到ASS映射器实例
 *   2. 加载video-config.json配置
 *   3. 执行样式映射
 *   4. 生成ASS样式文件
 *   5. 验证映射结果
 */
async function main() {
    const functionName = 'main';
    
    try {
        console.log(`[${functionName}] ========== 开始CSS到ASS样式映射测试 ==========`);
        
        // 步骤1: 创建映射器实例
        console.log(`[${functionName}] 步骤1: 创建CSS到ASS映射器实例`);
        const configPath = path.join(__dirname, 'video-config.json');
        const mapper = new CSSToASSMapper(configPath);
        
        // 步骤2: 加载配置文件
        console.log(`[${functionName}] 步骤2: 加载video-config.json配置文件`);
        const config = await mapper.loadConfig();
        
        console.log(`[${functionName}] 配置文件加载成功:`);
        console.log(`[${functionName}]   - clozedTextStyle: ${config.subtitleConfig.clozedTextStyle ? '✅' : '❌'}`);
        console.log(`[${functionName}]   - bilingualTextStyle.englishStyle: ${config.subtitleConfig.bilingualTextStyle?.englishStyle ? '✅' : '❌'}`);
        console.log(`[${functionName}]   - bilingualTextStyle.chineseStyle: ${config.subtitleConfig.bilingualTextStyle?.chineseStyle ? '✅' : '❌'}`);
        console.log(`[${functionName}]   - repeatModeGuideStyle: ${config.subtitleConfig.repeatModeGuideStyle ? '✅' : '❌'}`);
        
        // 步骤3: 执行样式映射
        console.log(`[${functionName}] 步骤3: 执行CSS到ASS样式映射`);
        const assStyles = await mapper.mapAllStyles();
        
        console.log(`[${functionName}] 样式映射完成:`);
        for (const [styleName, styleDefinition] of assStyles) {
            console.log(`[${functionName}]   - ${styleName}: ${styleDefinition.length}字符`);
        }
        
        // 步骤4: 生成ASS样式头部
        console.log(`[${functionName}] 步骤4: 生成ASS样式头部`);
        const assHeader = mapper.generateASSStylesHeader();
        
        console.log(`[${functionName}] ASS样式头部生成完成:`);
        console.log(`[${functionName}]   - 总长度: ${assHeader.length}字符`);
        console.log(`[${functionName}]   - 样式数量: ${assStyles.size}个`);
        
        // 步骤5: 保存ASS样式文件
        console.log(`[${functionName}] 步骤5: 保存ASS样式文件`);
        const outputPath = path.join(__dirname, 'mapped-styles.ass');
        await mapper.saveToFile(outputPath);
        
        // 步骤6: 显示映射结果详情
        console.log(`[${functionName}] 步骤6: 显示映射结果详情`);
        console.log(`[${functionName}] ========== ASS样式映射结果 ==========`);
        console.log(assHeader);
        
        // 步骤7: 验证关键样式配置
        console.log(`[${functionName}] 步骤7: 验证关键样式配置`);
        
        // 验证ClozedText样式
        if (assStyles.has('ClozedText')) {
            const clozedStyle = assStyles.get('ClozedText');
            console.log(`[${functionName}] ✅ ClozedText样式验证:`);
            console.log(`[${functionName}]     ${clozedStyle}`);
            
            // 检查关键参数
            const hasCorrectFont = clozedStyle.includes('Arial');
            const hasCorrectSize = clozedStyle.includes(',50,');
            const hasCorrectColor = clozedStyle.includes('&H00FFFFFF');
            
            console.log(`[${functionName}]     - 字体: ${hasCorrectFont ? '✅ Arial' : '❌'}`);
            console.log(`[${functionName}]     - 大小: ${hasCorrectSize ? '✅ 50px' : '❌'}`);
            console.log(`[${functionName}]     - 颜色: ${hasCorrectColor ? '✅ 白色' : '❌'}`);
        }
        
        // 验证BilingualEnglish样式
        if (assStyles.has('BilingualEnglish')) {
            const englishStyle = assStyles.get('BilingualEnglish');
            console.log(`[${functionName}] ✅ BilingualEnglish样式验证:`);
            console.log(`[${functionName}]     ${englishStyle}`);
            
            // 检查关键参数
            const hasCorrectFont = englishStyle.includes('Arial');
            const hasCorrectSize = englishStyle.includes(',50,');
            const hasCorrectColor = englishStyle.includes('&H00FFFFFF');
            const hasCorrectBold = englishStyle.includes(',-1,'); // Bold=-1
            
            console.log(`[${functionName}]     - 字体: ${hasCorrectFont ? '✅ Arial' : '❌'}`);
            console.log(`[${functionName}]     - 大小: ${hasCorrectSize ? '✅ 50px' : '❌'}`);
            console.log(`[${functionName}]     - 颜色: ${hasCorrectColor ? '✅ 白色' : '❌'}`);
            console.log(`[${functionName}]     - 粗体: ${hasCorrectBold ? '✅ 粗体' : '❌'}`);
        }
        
        // 验证BilingualChinese样式
        if (assStyles.has('BilingualChinese')) {
            const chineseStyle = assStyles.get('BilingualChinese');
            console.log(`[${functionName}] ✅ BilingualChinese样式验证:`);
            console.log(`[${functionName}]     ${chineseStyle}`);
            
            // 检查关键参数
            const hasCorrectFont = chineseStyle.includes('Arial');
            const hasCorrectSize = chineseStyle.includes(',60,');
            const hasCorrectColor = chineseStyle.includes('&H00FFFFFF');
            const hasCorrectBold = chineseStyle.includes(',-1,'); // Bold=-1
            
            console.log(`[${functionName}]     - 字体: ${hasCorrectFont ? '✅ Arial' : '❌'}`);
            console.log(`[${functionName}]     - 大小: ${hasCorrectSize ? '✅ 60px' : '❌'}`);
            console.log(`[${functionName}]     - 颜色: ${hasCorrectColor ? '✅ 白色' : '❌'}`);
            console.log(`[${functionName}]     - 粗体: ${hasCorrectBold ? '✅ 粗体' : '❌'}`);
        }
        
        console.log(`[${functionName}] ========== CSS到ASS样式映射测试完成 ==========`);
        console.log(`[${functionName}] ✅ 所有样式映射成功`);
        console.log(`[${functionName}] ✅ ASS样式文件已生成: ${outputPath}`);
        console.log(`[${functionName}] ✅ 样式配置验证通过`);
        
        // 步骤8: 测试颜色转换功能
        console.log(`[${functionName}] 步骤8: 测试颜色转换功能`);
        testColorConversion(mapper);
        
        // 步骤9: 测试字体转换功能
        console.log(`[${functionName}] 步骤9: 测试字体转换功能`);
        testFontConversion(mapper);
        
        console.log(`[${functionName}] 测试完成，准备退出...`);
        
    } catch (error) {
        console.error(`[${functionName}] 测试失败: ${error.message}`);
        console.error(`[${functionName}] 错误堆栈:`, error.stack);
        process.exit(1);
    }
}

/**
 * @功能概述: 测试颜色转换功能
 * @参数说明:
 *   - mapper: CSS到ASS映射器实例
 */
function testColorConversion(mapper) {
    const functionName = 'testColorConversion';
    
    console.log(`[${functionName}] 开始测试颜色转换功能...`);
    
    const testCases = [
        { input: '#FFFFFF', expected: '&H00FFFFFF', desc: '白色十六进制' },
        { input: '#FF0000', expected: '&H000000FF', desc: '红色十六进制' },
        { input: 'rgba(255,255,255,0.8)', expected: '&H33FFFFFF', desc: '半透明白色' },
        { input: 'rgb(255,0,0)', expected: '&H000000FF', desc: 'RGB红色' },
        { input: null, expected: '&H00FFFFFF', desc: '空值默认' }
    ];
    
    for (const testCase of testCases) {
        const result = mapper.convertColorToASS(testCase.input);
        const success = result === testCase.expected;
        console.log(`[${functionName}]   ${success ? '✅' : '❌'} ${testCase.desc}: "${testCase.input}" → "${result}" (期望: "${testCase.expected}")`);
    }
}

/**
 * @功能概述: 测试字体转换功能
 * @参数说明:
 *   - mapper: CSS到ASS映射器实例
 */
function testFontConversion(mapper) {
    const functionName = 'testFontConversion';
    
    console.log(`[${functionName}] 开始测试字体转换功能...`);
    
    // 测试字体大小转换
    const fontSizeTests = [
        { input: '50px', expected: 50, desc: '50像素' },
        { input: '60px', expected: 60, desc: '60像素' },
        { input: null, expected: 50, desc: '空值默认' }
    ];
    
    for (const test of fontSizeTests) {
        const result = mapper.convertFontSizeToASS(test.input);
        const success = result === test.expected;
        console.log(`[${functionName}]   ${success ? '✅' : '❌'} 字体大小 ${test.desc}: "${test.input}" → ${result} (期望: ${test.expected})`);
    }
    
    // 测试字体粗细转换
    const fontWeightTests = [
        { input: 'bold', expected: -1, desc: '粗体' },
        { input: 'normal', expected: 0, desc: '正常' },
        { input: '600', expected: -1, desc: '数值粗体' },
        { input: null, expected: 0, desc: '空值默认' }
    ];
    
    for (const test of fontWeightTests) {
        const result = mapper.convertFontWeightToASS(test.input);
        const success = result === test.expected;
        console.log(`[${functionName}]   ${success ? '✅' : '❌'} 字体粗细 ${test.desc}: "${test.input}" → ${result} (期望: ${test.expected})`);
    }
    
    // 测试文本对齐转换
    const textAlignTests = [
        { input: 'center', expected: 5, desc: '居中对齐' },
        { input: 'left', expected: 4, desc: '左对齐' },
        { input: 'right', expected: 6, desc: '右对齐' },
        { input: null, expected: 5, desc: '空值默认' }
    ];
    
    for (const test of textAlignTests) {
        const result = mapper.convertTextAlignToASS(test.input);
        const success = result === test.expected;
        console.log(`[${functionName}]   ${success ? '✅' : '❌'} 文本对齐 ${test.desc}: "${test.input}" → ${result} (期望: ${test.expected})`);
    }
}

// 启动测试
console.log('开始执行CSS到ASS样式映射测试脚本...');
main().catch(console.error);
