{"version": "2.0", "configType": "universal-video-config", "videoType": "educational_subtitle", "renderEngine": "remotion", "createdAt": "2025-01-03T16:48:00Z", "description": "通用视频配置文件，支持多种视频类型和渲染引擎", "output": {"width": 1080, "height": 1920, "fps": 30, "duration": "auto", "format": "mp4", "quality": {"crf": 20, "preset": "slow", "codec": "libx264"}}, "content": {"audio": {"source": "input", "repeat": 3, "processing": {"fadeIn": 0.5, "fadeOut": 0.5, "normalize": true, "bitrate": "192k"}}, "video": {"source": "input", "layout": {"position": "center", "scale": "fit", "maxDuration": "auto"}}, "background": {"type": "image", "source": "newspaper_9_16.png", "overlay": {"enabled": true, "color": "#000000", "opacity": 0.8}, "fallback": {"type": "color", "color": "#1a1a2e"}}}, "subtitles": {"enabled": true, "types": ["cloze", "bilingual"], "timing": {"modes": [{"name": "blind<PERSON><PERSON><PERSON>", "displayText": "第一遍 盲听", "showSubtitles": false, "showVideo": true}, {"name": "clozedSubtitle", "displayText": "第二遍 单词填空", "showSubtitles": true, "subtitleType": "cloze"}, {"name": "bilingualSubtitle", "displayText": "第三遍 中英翻译", "showSubtitles": true, "subtitleType": "bilingual"}]}, "styles": {"english": {"fontSize": 50, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "color": "#FFFFFF", "backgroundColor": "transparent", "textAlign": "center", "textShadow": "none", "margin": {"left": 150, "right": 150, "bottom": 10}}, "chinese": {"fontSize": 50, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "color": "#FFFFFF", "backgroundColor": "transparent", "textAlign": "center", "letterSpacing": 2, "margin": {"left": 150, "right": 150, "bottom": 10}}, "keyword": {"color": "#FFFF00", "backgroundColor": "transparent", "fontWeight": "normal", "highlight": true}, "cloze": {"blankSymbol": "_____", "blankColor": "#FFFF00", "hintEnabled": true}}, "animations": {"fadeIn": {"duration": 0.3, "easing": "easeOut"}, "fadeOut": {"duration": 0.3, "easing": "easeIn"}, "highlight": {"type": "spring", "config": {"mass": 1, "damping": 10, "stiffness": 100}}}}, "ui": {"progressBar": {"enabled": true, "height": 16, "position": "below_video", "colors": {"background": "#333333", "foreground": "#FFFF00"}, "animation": {"type": "linear", "smooth": true}}, "guide": {"enabled": true, "texts": ["坚持30天", "听懂国外新闻"], "style": {"fontSize": 100, "fontFamily": "<PERSON><PERSON>", "color": "#FFFFFF", "fontWeight": "bold", "textShadow": "2px 2px 4px rgba(0,0,0,0.8)"}, "position": {"x": 540, "y1": 300, "y2": 420}, "animation": {"type": "fade", "duration": 1.0}}, "advertisement": {"enabled": true, "timing": {"startTime": 6, "endTime": "firstLoopEnd"}, "texts": [{"line1": "🌍关注水蜜桃英语", "line2": "摆脱字幕，听力涨得快！"}, {"line1": "🌍关注水蜜桃英语", "line2": "每天2分钟，陪你听力打卡"}, {"line1": "🌍关注水蜜桃英语", "line2": "每天2分钟，听全球要闻!"}], "style": {"fontSize": 34, "fontFamily": "Microsoft YaHei", "fontWeight": "bold", "color": "#FFFF00", "textShadow": "1px 1px 2px rgba(0,0,0,0.8)", "border": "2px solid #000000"}, "position": {"x": 880, "y1": 680, "y2": 715}, "animation": {"type": "slideIn", "duration": 0.5}}, "textArea": {"enabled": true, "backgroundColor": "#3B3B3B", "width": 1080, "height": 608, "position": "bottom"}}, "effects": {"transitions": {"enabled": true, "type": "fade", "duration": 0.5}, "particles": {"enabled": false, "type": "sparkle", "density": 10}}, "performance": {"preload": {"images": true, "fonts": true, "audio": false}, "optimization": {"enableGPU": true, "cacheFrames": true, "parallelRender": false}}, "compatibility": {"legacySupport": true, "migrationVersion": "1.0", "fallbackEngine": "ffmpeg"}, "metadata": {"author": "Video Generation System", "tags": ["educational", "subtitle", "remotion"], "notes": "通用配置文件，支持多种视频类型和渲染引擎"}}