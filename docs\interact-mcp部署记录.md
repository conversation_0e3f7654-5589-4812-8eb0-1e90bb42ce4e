# Interactive Feedback MCP 部署记录

本文档记录了在 Windows PowerShell 环境下部署 Interactive Feedback MCP 的详细过程，包括准备工作、代码获取、依赖安装、服务器运行以及 Cursor 配置。

参考文档: [https://github.com/noopstudios/interactive-feedback-mcp](https://github.com/noopstudios/interactive-feedback-mcp)

## 1. 准备工作

根据 GitHub 仓库 README 的说明，需要安装以下先决条件：

-   Python 3.11 或更新版本。
-   uv (Python 包管理器)。

### 1.1 安装 uv

在 PowerShell 中执行以下命令安装 uv：

```powershell
pip install uv
```

## 2. 获取 MCP 代码

选择一个合适的目录（例如在用户主目录下创建一个新目录），然后克隆 Interactive Feedback MCP 仓库。

### 2.1 创建存放目录

在 PowerShell 中创建目录 `cursor_interact_mcp`：

```powershell
mkdir cursor_interact_mcp
```

### 2.2 进入新创建的目录

```powershell
cd cursor_interact_mcp
```

### 2.3 克隆仓库

在 `cursor_interact_mcp` 目录中执行克隆命令：

```powershell
git clone https://github.com/noopstudios/interactive-feedback-mcp.git
```

## 3. 进入仓库目录

克隆完成后，需要进入到克隆下来的 `interactive-feedback-mcp` 目录中。

```powershell
cd interactive-feedback-mcp
```

**注意：** 您在记录中尝试在 `C:\Users\<USER>\cursor_interact_mcp>` 目录直接运行 `uv --directory . run server.py` 失败了，这是因为 `server.py` 文件位于克隆下来的 `interactive-feedback-mcp` 子目录中。需要先 `cd` 进入到该目录。

## 4. 安装项目依赖

在 `interactive-feedback-mcp` 目录中，使用 `uv sync` 安装项目所需的依赖。这会自动创建一个虚拟环境并安装依赖。

```powershell
uv sync
```

## 5. 运行 MCP 服务器

依赖安装完成后，在 `interactive-feedback-mcp` 目录中运行服务器：

```powershell
uv run server.py
```

**注意：** `uv run server.py` 命令需要保持运行状态才能让 Cursor 连接。在您的终端记录中，服务器启动后可能遇到了问题或被手动中断（表现为 Traceback 和 `KeyboardInterrupt`）。为了让 Cursor 能够连接，这个命令需要在后台持续运行。您可能需要保持 PowerShell 窗口打开并运行此命令，或者考虑寻找一种方法使其在后台运行（例如使用 `Start-Process` 或其他 Windows 服务管理方法，但这超出了当前记录的范围）。

## 6. 配置 Cursor

在 Cursor 中配置使用这个 MCP 服务器。根据 GitHub README ([https://github.com/noopstudios/interactive-feedback-mcp#installation-cursor](https://github.com/noopstudios/interactive-feedback-mcp#installation-cursor))，可以在 Cursor 的设置中添加自定义 MCP 服务器，通常通过修改 `.cursor/mcp.json` 文件来实现。

以下是 `.cursor/mcp.json` 的示例配置（请根据您实际的克隆路径进行修改）：

```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "C:\\\\Users\\\\<USER>\\\\cursor_interact_mcp\\\\interactive-feedback-mcp", // 修改为您的实际路径
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ]
    }
  }
}
```

将上述配置添加到您的 `.cursor/mcp.json` 文件中。请确保 `command` 和 `args` 中的路径是您本地仓库的实际路径。

完成上述步骤后，Interactive Feedback MCP 服务器应该已经在您的本地运行（尽管可能存在启动问题，需要进一步排查），并且 Cursor 也配置为使用它了。以后在与 AI 交互时，AI 在需要提问或完成任务时将尝试调用这个 MCP 进行用户确认。
