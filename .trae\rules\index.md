---
type: "manual"
description: "globs:"
---
# 项目规则索引

本文档是此代码库中所有活动 Cursor 规则的中心索引。

## 通用与核心 (General & Core)

基础的项目指南和结构。

| 规则文件                                                 | 描述                                     |
| :------------------------------------------------------- | :--------------------------------------- |
| [general_principles.mdc](mdc:general_principles.mdc)     | 项目通用原则与特性指南                   |
| [code-commenting-standard.mdc](mdc:code-commenting-standard.mdc) | 代码注释编写标准与习惯                    |
| [file_structure.mdc](mdc:file_structure.mdc)             | 项目文件结构与特定目录说明             |

## 代码规范 (Coding Standards)

代码编写和注释相关的规范。

| 规则文件                                                 | 描述                                     |
| :------------------------------------------------------- | :--------------------------------------- |
| [API_CREATION_GUIDELINES.mdc](mdc:API_CREATION_GUIDELINES.mdc) | API创建开发指导原则                      |
| [code-commenting-standard.mdc](mdc:code-commenting-standard.mdc) | 代码注释编写标准与习惯                    |
| [pipeline_architecture.mdc](mdc:pipeline_architecture.mdc) | 流水线架构实现规范                      |
| [task_development_standard.mdc](mdc:task_development_standard.mdc) | 任务开发标准与进度回调使用规范          |
| [sse_event_standard.mdc](mdc:sse_event_standard.mdc) | SSE 事件数据结构与实现标准             |
| [frontend_development_standard.mdc](mdc:frontend_development_standard.mdc) | 前端开发标准与WordPress兼容性要求       |
| [llm_api_standard.mdc](mdc:llm_api_standard.mdc) | LLM API调用标准化规范 - 统一接口与增强功能 |

## 项目管理与工作流程 (Project Management & Workflow)

需求分解、任务管理和开发流程规范。

| 规则文件                                                 | 描述                                     |
| :------------------------------------------------------- | :--------------------------------------- |
| [requirement_breakdown.mdc](mdc:requirement_breakdown.mdc) | 需求分解与任务管理规则              |
| [manual_debugging_workflow.mdc](mdc:manual_debugging_workflow.mdc) | Manual调试工作流程标准                   |



