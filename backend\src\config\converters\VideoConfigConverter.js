/**
 * @功能概述: 视频配置转换器，实现老配置→新配置→特定引擎配置的转换
 * @创建时间: 2025-01-03
 * @版本: 2.0
 * @依赖: 无外部依赖
 */

const path = require('path');
const logger = require('../../utils/logger');

class VideoConfigConverter {
    /**
     * @功能概述: 将老版本video-config.json转换为新版本universal-video-config.json格式
     * @param {Object} legacyConfig - 老版本配置对象
     * @returns {Object} 新版本通用配置对象
     * @执行流程:
     *   1. 验证输入配置的有效性
     *   2. 转换基础视频参数
     *   3. 转换字幕配置
     *   4. 转换UI配置
     *   5. 添加新版本特有的配置项
     */
    static convertLegacyToUniversal(legacyConfig) {
        const logPrefix = '[VideoConfigConverter][convertLegacyToUniversal]';
        
        try {
            logger.info(`${logPrefix} 开始转换老配置为新配置格式`);
            
            // 步骤 1: 验证输入配置
            if (!legacyConfig || typeof legacyConfig !== 'object') {
                throw new Error('无效的老配置对象');
            }
            
            // 步骤 2: 转换基础视频参数
            const universalConfig = {
                version: "2.0",
                configType: "universal-video-config",
                videoType: "educational_subtitle",
                renderEngine: "remotion",
                createdAt: new Date().toISOString(),
                description: "从老配置转换的通用视频配置",
                
                output: {
                    width: legacyConfig.width || 1080,
                    height: legacyConfig.height || 1920,
                    fps: legacyConfig.framerate || 30,
                    duration: "auto",
                    format: "mp4",
                    quality: {
                        crf: legacyConfig.crf || 20,
                        preset: legacyConfig.preset || "slow",
                        codec: legacyConfig.codec || "libx264"
                    }
                },
                
                content: {
                    audio: {
                        source: "input",
                        repeat: legacyConfig.repeatCount || 3,
                        processing: {
                            fadeIn: 0.5,
                            fadeOut: 0.5,
                            normalize: true,
                            bitrate: "192k"
                        }
                    },
                    video: {
                        source: "input",
                        layout: {
                            position: "center",
                            scale: "fit",
                            maxDuration: "auto"
                        }
                    },
                    background: {
                        type: "image",
                        source: this._getBackgroundSource(legacyConfig.backgroundStyle),
                        overlay: {
                            enabled: true,
                            color: "#000000",
                            opacity: legacyConfig.backgroundStyle === "newspaper" ? 0.6 : 0.5
                        },
                        fallback: {
                            type: "color",
                            color: legacyConfig.backgroundColor || "#1a1a2e"
                        }
                    }
                }
            };
            
            // 步骤 3: 转换字幕配置
            universalConfig.subtitles = this._convertSubtitleConfig(legacyConfig.subtitleConfig || {});
            
            // 步骤 4: 转换UI配置
            universalConfig.ui = this._convertUIConfig(legacyConfig);
            
            // 步骤 5: 添加新版本特有的配置项
            universalConfig.effects = {
                transitions: {
                    enabled: true,
                    type: "fade",
                    duration: 0.5
                },
                particles: {
                    enabled: false,
                    type: "sparkle",
                    density: 10
                }
            };
            
            universalConfig.performance = {
                preload: {
                    images: true,
                    fonts: true,
                    audio: false
                },
                optimization: {
                    enableGPU: true,
                    cacheFrames: true,
                    parallelRender: false
                }
            };
            
            universalConfig.compatibility = {
                legacySupport: true,
                migrationVersion: "1.0",
                fallbackEngine: "ffmpeg"
            };
            
            universalConfig.metadata = {
                author: "Video Generation System",
                tags: ["educational", "subtitle", "remotion"],
                notes: "从老配置自动转换生成"
            };
            
            logger.info(`${logPrefix} 配置转换完成`);
            return universalConfig;
            
        } catch (error) {
            logger.error(`${logPrefix} 配置转换失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 转换字幕配置部分
     * @param {Object} legacySubtitleConfig - 老版本字幕配置
     * @returns {Object} 新版本字幕配置
     */
    static _convertSubtitleConfig(legacySubtitleConfig) {
        const subtitleConfig = {
            enabled: true,
            types: ["cloze", "bilingual"],
            timing: {
                modes: []
            },
            styles: {
                english: {
                    fontSize: 50,
                    fontFamily: "Arial",
                    fontWeight: "bold",
                    color: "#FFFFFF",
                    backgroundColor: "transparent",
                    textAlign: "center",
                    textShadow: "none",
                    margin: {
                        left: 150,
                        right: 150,
                        bottom: 10
                    }
                },
                chinese: {
                    fontSize: 50,
                    fontFamily: "Arial",
                    fontWeight: "bold",
                    color: "#FFFFFF",
                    backgroundColor: "transparent",
                    textAlign: "center",
                    letterSpacing: 2,
                    margin: {
                        left: 150,
                        right: 150,
                        bottom: 10
                    }
                },
                keyword: {
                    color: "#FFFF00",
                    backgroundColor: "transparent",
                    fontWeight: "normal",
                    highlight: true
                },
                cloze: {
                    blankSymbol: "_____",
                    blankColor: "#FFFF00",
                    hintEnabled: true
                }
            },
            animations: {
                fadeIn: {
                    duration: 0.3,
                    easing: "easeOut"
                },
                fadeOut: {
                    duration: 0.3,
                    easing: "easeIn"
                },
                highlight: {
                    type: "spring",
                    config: {
                        mass: 1,
                        damping: 10,
                        stiffness: 100
                    }
                }
            }
        };
        
        // 转换repeatModes
        if (legacySubtitleConfig.repeatModes && Array.isArray(legacySubtitleConfig.repeatModes)) {
            subtitleConfig.timing.modes = legacySubtitleConfig.repeatModes.map(mode => ({
                name: mode.name,
                displayText: mode.displayText,
                showSubtitles: mode.name !== "blindListen",
                showVideo: mode.name === "blindListen",
                subtitleType: mode.name === "clozedSubtitle" ? "cloze" : 
                            mode.name === "bilingualSubtitle" ? "bilingual" : undefined
            }));
        } else {
            // 默认模式
            subtitleConfig.timing.modes = [
                {
                    name: "blindListen",
                    displayText: "第一遍 盲听",
                    showSubtitles: false,
                    showVideo: true
                },
                {
                    name: "clozedSubtitle",
                    displayText: "第二遍 单词填空",
                    showSubtitles: true,
                    subtitleType: "cloze"
                },
                {
                    name: "bilingualSubtitle",
                    displayText: "第三遍 中英翻译",
                    showSubtitles: true,
                    subtitleType: "bilingual"
                }
            ];
        }
        
        // 转换双语字幕样式
        if (legacySubtitleConfig.bilingualTextStyle) {
            const legacyStyles = legacySubtitleConfig.bilingualTextStyle;
            
            if (legacyStyles.englishStyle) {
                subtitleConfig.styles.english = {
                    fontSize: parseInt(legacyStyles.englishStyle.fontSize) || 50,
                    fontFamily: legacyStyles.englishStyle.fontFamily || "Arial",
                    fontWeight: legacyStyles.englishStyle.fontWeight || "bold",
                    color: legacyStyles.englishStyle.color || "#FFFFFF",
                    backgroundColor: legacyStyles.englishStyle.backgroundColor || "transparent",
                    textAlign: legacyStyles.englishStyle.textAlign || "center",
                    margin: {
                        left: parseInt(legacyStyles.englishStyle.marginLeft) || 150,
                        right: parseInt(legacyStyles.englishStyle.marginRight) || 150,
                        bottom: parseInt(legacyStyles.englishStyle.marginBottom) || 10
                    }
                };
            }
            
            if (legacyStyles.chineseStyle) {
                subtitleConfig.styles.chinese = {
                    fontSize: parseInt(legacyStyles.chineseStyle.fontSize) || 50,
                    fontFamily: legacyStyles.chineseStyle.fontFamily || "Arial",
                    fontWeight: legacyStyles.chineseStyle.fontWeight || "bold",
                    color: legacyStyles.chineseStyle.color || "#FFFFFF",
                    backgroundColor: legacyStyles.chineseStyle.backgroundColor || "transparent",
                    textAlign: legacyStyles.chineseStyle.textAlign || "center",
                    letterSpacing: parseInt(legacyStyles.chineseStyle.letterSpacing) || 2,
                    margin: {
                        left: parseInt(legacyStyles.chineseStyle.marginLeft) || 150,
                        right: parseInt(legacyStyles.chineseStyle.marginRight) || 150,
                        bottom: parseInt(legacyStyles.chineseStyle.marginBottom) || 10
                    }
                };
            }
            
            if (legacyStyles.keywordBlockStyle) {
                subtitleConfig.styles.keyword = {
                    color: legacyStyles.keywordBlockStyle.color || "#FFFF00",
                    backgroundColor: legacyStyles.keywordBlockStyle.backgroundColor || "transparent",
                    fontWeight: legacyStyles.keywordBlockStyle.fontWeight || "normal",
                    highlight: true
                };
            }
        }
        
        return subtitleConfig;
    }
    
    /**
     * @功能概述: 转换UI配置部分
     * @param {Object} legacyConfig - 老版本配置
     * @returns {Object} 新版本UI配置
     */
    static _convertUIConfig(legacyConfig) {
        const uiConfig = {
            progressBar: {
                enabled: true,
                height: 16,
                position: "below_video",
                colors: {
                    background: "#333333",
                    foreground: "#FFFF00"
                },
                animation: {
                    type: "linear",
                    smooth: true
                }
            },
            guide: {
                enabled: true,
                texts: ["坚持30天", "听懂国外新闻"],
                style: {
                    fontSize: 100,
                    fontFamily: "Arial",
                    color: "#FFFFFF",
                    fontWeight: "bold",
                    textShadow: "2px 2px 4px rgba(0,0,0,0.8)"
                },
                position: {
                    x: 540,
                    y1: 300,
                    y2: 420
                },
                animation: {
                    type: "fade",
                    duration: 1.0
                }
            },
            advertisement: {
                enabled: true,
                timing: {
                    startTime: 6,
                    endTime: "firstLoopEnd"
                },
                texts: [
                    {
                        line1: "🌍关注水蜜桃英语",
                        line2: "摆脱字幕，听力涨得快！"
                    }
                ],
                style: {
                    fontSize: 34,
                    fontFamily: "Microsoft YaHei",
                    fontWeight: "bold",
                    color: "#FFFF00",
                    textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                    border: "2px solid #000000"
                },
                position: {
                    x: 880,
                    y1: 680,
                    y2: 715
                },
                animation: {
                    type: "slideIn",
                    duration: 0.5
                }
            },
            textArea: {
                enabled: true,
                backgroundColor: "#3B3B3B",
                width: 1080,
                height: 608,
                position: "bottom"
            }
        };
        
        // 转换进度条配置
        if (legacyConfig.progressBar) {
            uiConfig.progressBar.height = legacyConfig.progressBar.height || 16;
            uiConfig.progressBar.colors.background = legacyConfig.progressBar.backgroundColor || "#333333";
            uiConfig.progressBar.colors.foreground = legacyConfig.progressBar.foregroundColor || "#FFFF00";
            uiConfig.progressBar.position = legacyConfig.progressBar.position || "below_video";
        }
        
        // 转换视频引导配置
        if (legacyConfig.subtitleConfig && legacyConfig.subtitleConfig.videoGuide) {
            const guide = legacyConfig.subtitleConfig.videoGuide;
            uiConfig.guide.enabled = guide.enabled !== false;
            uiConfig.guide.texts = [guide.title1 || "坚持30天", guide.title2 || "听懂国外新闻"];
            
            if (guide.style) {
                uiConfig.guide.style = {
                    fontSize: parseInt(guide.style.fontSize) || 100,
                    fontFamily: guide.style.fontFamily || "Arial",
                    color: guide.style.color || "#FFFFFF",
                    fontWeight: guide.style.fontWeight || "bold",
                    textShadow: guide.style.textShadow || "2px 2px 4px rgba(0,0,0,0.8)"
                };
                
                if (guide.style.position) {
                    uiConfig.guide.position = {
                        x: parseInt(guide.style.position.x) || 540,
                        y1: parseInt(guide.style.position.y1) || 300,
                        y2: parseInt(guide.style.position.y2) || 420
                    };
                }
            }
        }
        
        // 转换广告配置
        if (legacyConfig.subtitleConfig && legacyConfig.subtitleConfig.advertisement) {
            const ad = legacyConfig.subtitleConfig.advertisement;
            uiConfig.advertisement.enabled = ad.enabled !== false;
            uiConfig.advertisement.timing.startTime = ad.startTime || 6;
            uiConfig.advertisement.timing.endTime = ad.endTime || "firstLoopEnd";
            
            if (ad.titles && Array.isArray(ad.titles)) {
                uiConfig.advertisement.texts = ad.titles;
            }
            
            if (ad.style) {
                uiConfig.advertisement.style = {
                    fontSize: parseInt(ad.style.fontSize) || 34,
                    fontFamily: ad.style.fontFamily || "Microsoft YaHei",
                    fontWeight: ad.style.fontWeight || "bold",
                    color: ad.style.color || "#FFFF00",
                    textShadow: ad.style.textShadow || "1px 1px 2px rgba(0,0,0,0.8)",
                    border: ad.style.border || "2px solid #000000"
                };
                
                if (ad.style.position) {
                    uiConfig.advertisement.position = {
                        x: parseInt(ad.style.position.x) || 880,
                        y1: parseInt(ad.style.position.y1) || 680,
                        y2: parseInt(ad.style.position.y2) || 715
                    };
                }
            }
        }
        
        // 转换文本区域配置
        if (legacyConfig.textArea) {
            uiConfig.textArea.backgroundColor = legacyConfig.textArea.backgroundColor || "#3B3B3B";
            uiConfig.textArea.width = parseInt(legacyConfig.textArea.width) || 1080;
            uiConfig.textArea.height = parseInt(legacyConfig.textArea.height) || 608;
        }
        
        return uiConfig;
    }

    /**
     * @功能概述: 将通用配置转换为Remotion特定配置
     * @param {Object} universalConfig - 通用配置对象
     * @returns {Object} Remotion配置对象
     * @执行流程:
     *   1. 提取Remotion组件所需的基础配置
     *   2. 转换样式配置为CSS-in-JS格式
     *   3. 转换动画配置为Remotion动画参数
     *   4. 生成Composition配置
     */
    static convertUniversalToRemotion(universalConfig) {
        const logPrefix = '[VideoConfigConverter][convertUniversalToRemotion]';

        try {
            logger.info(`${logPrefix} 开始转换通用配置为Remotion配置`);

            const remotionConfig = {
                // Composition基础配置
                composition: {
                    id: "VideoComposition",
                    component: "VideoComposition",
                    width: universalConfig.output.width,
                    height: universalConfig.output.height,
                    fps: universalConfig.output.fps,
                    durationInFrames: "auto", // 将根据音频时长动态计算
                    defaultProps: {}
                },

                // 内容配置
                content: {
                    audio: {
                        repeat: universalConfig.content.audio.repeat,
                        fadeIn: universalConfig.content.audio.processing.fadeIn,
                        fadeOut: universalConfig.content.audio.processing.fadeOut
                    },
                    video: {
                        position: universalConfig.content.video.layout.position,
                        scale: universalConfig.content.video.layout.scale
                    },
                    background: {
                        type: universalConfig.content.background.type,
                        source: universalConfig.content.background.source,
                        overlay: universalConfig.content.background.overlay
                    }
                },

                // 字幕样式（转换为CSS-in-JS）
                subtitleStyles: {
                    english: this._convertStyleToCSS(universalConfig.subtitles.styles.english),
                    chinese: this._convertStyleToCSS(universalConfig.subtitles.styles.chinese),
                    keyword: this._convertStyleToCSS(universalConfig.subtitles.styles.keyword),
                    cloze: universalConfig.subtitles.styles.cloze
                },

                // UI组件配置
                ui: {
                    progressBar: {
                        enabled: universalConfig.ui.progressBar.enabled,
                        height: universalConfig.ui.progressBar.height,
                        backgroundColor: universalConfig.ui.progressBar.colors.background,
                        foregroundColor: universalConfig.ui.progressBar.colors.foreground,
                        animationType: universalConfig.ui.progressBar.animation.type
                    },
                    guide: {
                        enabled: universalConfig.ui.guide.enabled,
                        texts: universalConfig.ui.guide.texts,
                        style: this._convertStyleToCSS(universalConfig.ui.guide.style),
                        position: universalConfig.ui.guide.position,
                        animation: universalConfig.ui.guide.animation
                    },
                    advertisement: {
                        enabled: universalConfig.ui.advertisement.enabled,
                        timing: universalConfig.ui.advertisement.timing,
                        texts: universalConfig.ui.advertisement.texts,
                        style: this._convertStyleToCSS(universalConfig.ui.advertisement.style),
                        position: universalConfig.ui.advertisement.position,
                        animation: universalConfig.ui.advertisement.animation
                    }
                },

                // 动画配置
                animations: {
                    subtitles: universalConfig.subtitles.animations,
                    transitions: universalConfig.effects.transitions
                },

                // 时间轴配置
                timeline: {
                    modes: universalConfig.subtitles.timing.modes
                },

                // 性能配置
                performance: universalConfig.performance
            };

            logger.info(`${logPrefix} Remotion配置转换完成`);
            return remotionConfig;

        } catch (error) {
            logger.error(`${logPrefix} Remotion配置转换失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 将通用配置转换为FFmpeg特定配置（为其他流水线保留）
     * @param {Object} universalConfig - 通用配置对象
     * @returns {Object} FFmpeg配置对象
     */
    static convertUniversalToFFmpeg(universalConfig) {
        const logPrefix = '[VideoConfigConverter][convertUniversalToFFmpeg]';

        try {
            logger.info(`${logPrefix} 开始转换通用配置为FFmpeg配置`);

            const ffmpegConfig = {
                // 基础编码参数
                width: universalConfig.output.width,
                height: universalConfig.output.height,
                framerate: universalConfig.output.fps,
                codec: universalConfig.output.quality.codec,
                preset: universalConfig.output.quality.preset,
                crf: universalConfig.output.quality.crf,

                // 音频参数
                repeatCount: universalConfig.content.audio.repeat,
                audioBitrate: universalConfig.content.audio.processing.bitrate,

                // 背景配置
                backgroundColor: universalConfig.content.background.fallback.color,
                backgroundStyle: universalConfig.content.background.source.includes('newspaper') ? 'newspaper' : 'abstract',

                // 进度条配置
                progressBar: {
                    height: universalConfig.ui.progressBar.height,
                    backgroundColor: universalConfig.ui.progressBar.colors.background,
                    foregroundColor: universalConfig.ui.progressBar.colors.foreground,
                    position: universalConfig.ui.progressBar.position
                },

                // 文本区域配置
                textArea: {
                    backgroundColor: universalConfig.ui.textArea.backgroundColor,
                    width: `${universalConfig.ui.textArea.width}px`,
                    height: `${universalConfig.ui.textArea.height}px`
                },

                // 字幕配置（转换为ASS兼容格式）
                subtitleConfig: {
                    repeatModes: universalConfig.subtitles.timing.modes.map(mode => ({
                        name: mode.name,
                        displayText: mode.displayText
                    })),
                    videoGuide: {
                        enabled: universalConfig.ui.guide.enabled,
                        title1: universalConfig.ui.guide.texts[0] || "",
                        title2: universalConfig.ui.guide.texts[1] || "",
                        style: this._convertCSSToLegacyStyle(universalConfig.ui.guide.style, universalConfig.ui.guide.position)
                    },
                    advertisement: {
                        enabled: universalConfig.ui.advertisement.enabled,
                        titles: universalConfig.ui.advertisement.texts,
                        startTime: universalConfig.ui.advertisement.timing.startTime,
                        endTime: universalConfig.ui.advertisement.timing.endTime,
                        style: this._convertCSSToLegacyStyle(universalConfig.ui.advertisement.style, universalConfig.ui.advertisement.position)
                    },
                    bilingualTextStyle: {
                        englishStyle: this._convertCSSToLegacyStyle(universalConfig.subtitles.styles.english),
                        chineseStyle: this._convertCSSToLegacyStyle(universalConfig.subtitles.styles.chinese),
                        keywordBlockStyle: this._convertCSSToLegacyStyle(universalConfig.subtitles.styles.keyword)
                    }
                }
            };

            logger.info(`${logPrefix} FFmpeg配置转换完成`);
            return ffmpegConfig;

        } catch (error) {
            logger.error(`${logPrefix} FFmpeg配置转换失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 将样式对象转换为CSS-in-JS格式
     * @param {Object} styleObj - 样式对象
     * @returns {Object} CSS-in-JS样式对象
     */
    static _convertStyleToCSS(styleObj) {
        if (!styleObj || typeof styleObj !== 'object') {
            return {};
        }

        const cssStyle = {};

        // 字体相关
        if (styleObj.fontSize) cssStyle.fontSize = typeof styleObj.fontSize === 'number' ? `${styleObj.fontSize}px` : styleObj.fontSize;
        if (styleObj.fontFamily) cssStyle.fontFamily = styleObj.fontFamily;
        if (styleObj.fontWeight) cssStyle.fontWeight = styleObj.fontWeight;
        if (styleObj.fontStyle) cssStyle.fontStyle = styleObj.fontStyle;

        // 颜色相关
        if (styleObj.color) cssStyle.color = styleObj.color;
        if (styleObj.backgroundColor) cssStyle.backgroundColor = styleObj.backgroundColor;

        // 文本相关
        if (styleObj.textAlign) cssStyle.textAlign = styleObj.textAlign;
        if (styleObj.textShadow) cssStyle.textShadow = styleObj.textShadow;
        if (styleObj.textDecoration) cssStyle.textDecoration = styleObj.textDecoration;
        if (styleObj.letterSpacing) cssStyle.letterSpacing = `${styleObj.letterSpacing}px`;

        // 边距相关
        if (styleObj.margin) {
            if (typeof styleObj.margin === 'object') {
                if (styleObj.margin.left) cssStyle.marginLeft = `${styleObj.margin.left}px`;
                if (styleObj.margin.right) cssStyle.marginRight = `${styleObj.margin.right}px`;
                if (styleObj.margin.top) cssStyle.marginTop = `${styleObj.margin.top}px`;
                if (styleObj.margin.bottom) cssStyle.marginBottom = `${styleObj.margin.bottom}px`;
            }
        }

        // 边框相关
        if (styleObj.border) cssStyle.border = styleObj.border;

        return cssStyle;
    }

    /**
     * @功能概述: 获取背景源路径，与背景模板系统保持一致
     * @param {string} backgroundStyle - 背景样式名称
     * @returns {string} 背景源路径
     * @私有方法: 背景源路径获取逻辑
     */
    static _getBackgroundSource(backgroundStyle) {
        // 根据背景样式返回对应的背景图片路径，与背景模板保持一致
        switch (backgroundStyle) {
            case 'newspaper':
                return 'backgrounds/newspaper.png';
            case 'abstract':
                return 'backgrounds/abstract.png';
            default:
                return 'backgrounds/abstract.png'; // 默认使用abstract背景
        }
    }

    /**
     * @功能概述: 将CSS样式转换为老版本样式格式
     * @param {Object} cssStyle - CSS样式对象
     * @param {Object} position - 位置信息（可选）
     * @returns {Object} 老版本样式对象
     */
    static _convertCSSToLegacyStyle(cssStyle, position = null) {
        if (!cssStyle || typeof cssStyle !== 'object') {
            return {};
        }

        const legacyStyle = {};

        // 字体相关
        if (cssStyle.fontSize) legacyStyle.fontSize = cssStyle.fontSize;
        if (cssStyle.fontFamily) legacyStyle.fontFamily = cssStyle.fontFamily;
        if (cssStyle.fontWeight) legacyStyle.fontWeight = cssStyle.fontWeight;
        if (cssStyle.fontStyle) legacyStyle.fontStyle = cssStyle.fontStyle;

        // 颜色相关
        if (cssStyle.color) legacyStyle.color = cssStyle.color;
        if (cssStyle.backgroundColor) legacyStyle.backgroundColor = cssStyle.backgroundColor;

        // 文本相关
        if (cssStyle.textAlign) legacyStyle.textAlign = cssStyle.textAlign;
        if (cssStyle.textShadow) legacyStyle.textShadow = cssStyle.textShadow;
        if (cssStyle.textDecoration) legacyStyle.textDecoration = cssStyle.textDecoration;
        if (cssStyle.letterSpacing) legacyStyle.letterSpacing = cssStyle.letterSpacing;

        // 边距相关
        if (cssStyle.marginLeft) legacyStyle.marginLeft = cssStyle.marginLeft;
        if (cssStyle.marginRight) legacyStyle.marginRight = cssStyle.marginRight;
        if (cssStyle.marginTop) legacyStyle.marginTop = cssStyle.marginTop;
        if (cssStyle.marginBottom) legacyStyle.marginBottom = cssStyle.marginBottom;

        // 边框相关
        if (cssStyle.border) legacyStyle.border = cssStyle.border;

        // 位置信息
        if (position) {
            legacyStyle.position = position;
        }

        return legacyStyle;
    }
}

module.exports = VideoConfigConverter;
