# &lt;AnimatedImage&gt;

## 概述

`<AnimatedImage>` 组件用于渲染动画 GIF、PNG、AVIF 或 WebP 图像，并与 Remotion 的时间轴同步。

依赖于 [`ImageDecoder`](https://developer.mozilla.org/en-US/docs/Web/API/ImageDecoder) Web API，这意味着目前只在 Google Chrome 和 Firefox 中工作。

**版本要求**: v4.0.246+

## 语法

```typescript
import { AnimatedImage } from 'remotion';

<AnimatedImage src="图像路径" />
```

## 核心属性

### src (必需)
- **类型**: `string`
- **描述**: 动画图像的 URL，可以是远程 URL 或本地文件路径

### width (可选)
- **类型**: `number`
- **描述**: 显示宽度

### height (可选)
- **类型**: `number`
- **描述**: 显示高度

### fit (可选)
- **类型**: `'fill' | 'contain' | 'cover'`
- **默认值**: `'fill'`
- **描述**: 图像适应方式

### style (可选)
- **类型**: `React.CSSProperties`
- **描述**: 自定义 CSS 样式（不能包含 width 和 height）

### loopBehavior (可选)
- **类型**: `'loop' | 'pause-after-finish' | 'clear-after-finish'`
- **默认值**: `'loop'`
- **描述**: 动画循环行为

### ref (可选)
- **类型**: `React.Ref<HTMLCanvasElement>`
- **版本要求**: v3.3.88+
- **描述**: React ref 引用

## 基础用法

### 1. 远程动画图像

```typescript
import { AnimatedImage } from 'remotion';

export const RemoteAnimatedImage = () => {
  return (
    <AnimatedImage 
      src="https://mathiasbynens.be/demo/animated-webp-supported.webp" 
    />
  );
};
```

### 2. 本地动画图像

```typescript
import { AnimatedImage, staticFile } from 'remotion';

export const LocalAnimatedImage = () => {
  return (
    <AnimatedImage 
      src={staticFile('giphy.gif')} 
    />
  );
};
```

### 3. 自定义尺寸和适应方式

```typescript
import { AnimatedImage, staticFile } from 'remotion';

export const CustomSizedImage = () => {
  return (
    <AnimatedImage 
      src={staticFile('animation.webp')}
      width={400}
      height={300}
      fit="contain"
    />
  );
};
```

## 实际应用场景

### 1. 动画表情和反应

```typescript
import React from 'react';
import { AnimatedImage, staticFile, useCurrentFrame, interpolate } from 'remotion';

interface AnimatedEmojiProps {
  emotion: 'happy' | 'sad' | 'excited' | 'thinking';
  size?: number;
  delay?: number;
}

const AnimatedEmoji: React.FC<AnimatedEmojiProps> = ({ 
  emotion, 
  size = 100, 
  delay = 0 
}) => {
  const frame = useCurrentFrame();

  // 根据情感选择对应的动画图像
  const getEmojiSrc = () => {
    switch (emotion) {
      case 'happy':
        return staticFile('emojis/happy.gif');
      case 'sad':
        return staticFile('emojis/sad.gif');
      case 'excited':
        return staticFile('emojis/excited.gif');
      case 'thinking':
        return staticFile('emojis/thinking.gif');
      default:
        return staticFile('emojis/default.gif');
    }
  };

  // 延迟出现效果
  const opacity = interpolate(
    frame,
    [delay, delay + 15],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 弹跳效果
  const scale = interpolate(
    frame,
    [delay, delay + 10, delay + 20],
    [0, 1.2, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div style={{
      display: 'inline-block',
      opacity,
      transform: `scale(${scale})`,
      filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))'
    }}>
      <AnimatedImage
        src={getEmojiSrc()}
        width={size}
        height={size}
        fit="contain"
        loopBehavior="loop"
      />
    </div>
  );
};

export default AnimatedEmoji;
```

### 2. 动画背景装饰

```typescript
import React from 'react';
import { AnimatedImage, staticFile, useVideoConfig, random } from 'remotion';

interface FloatingAnimationsProps {
  count?: number;
  animationType?: 'bubbles' | 'particles' | 'sparkles';
}

const FloatingAnimations: React.FC<FloatingAnimationsProps> = ({ 
  count = 10, 
  animationType = 'bubbles' 
}) => {
  const { width, height } = useVideoConfig();

  // 根据动画类型选择图像
  const getAnimationSrc = () => {
    switch (animationType) {
      case 'bubbles':
        return staticFile('animations/bubble.gif');
      case 'particles':
        return staticFile('animations/particle.webp');
      case 'sparkles':
        return staticFile('animations/sparkle.gif');
      default:
        return staticFile('animations/default.gif');
    }
  };

  // 生成随机位置的动画元素
  const animations = Array.from({ length: count }, (_, i) => ({
    id: i,
    x: random(`x-${i}`) * width,
    y: random(`y-${i}`) * height,
    size: random(`size-${i}`) * 40 + 20,
    opacity: random(`opacity-${i}`) * 0.6 + 0.4,
    delay: random(`delay-${i}`) * 60
  }));

  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      overflow: 'hidden'
    }}>
      {animations.map((anim) => (
        <div
          key={anim.id}
          style={{
            position: 'absolute',
            left: anim.x,
            top: anim.y,
            opacity: anim.opacity,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <AnimatedImage
            src={getAnimationSrc()}
            width={anim.size}
            height={anim.size}
            fit="contain"
            loopBehavior="loop"
            style={{
              filter: 'blur(0.5px)'
            }}
          />
        </div>
      ))}
    </div>
  );
};

export default FloatingAnimations;
```

### 3. 加载动画指示器

```typescript
import React from 'react';
import { AnimatedImage, staticFile, useCurrentFrame, interpolate } from 'remotion';

interface LoadingIndicatorProps {
  type?: 'spinner' | 'dots' | 'pulse';
  size?: number;
  message?: string;
  visible?: boolean;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ 
  type = 'spinner', 
  size = 60, 
  message = '加载中...',
  visible = true 
}) => {
  const frame = useCurrentFrame();

  // 根据类型选择加载动画
  const getLoadingSrc = () => {
    switch (type) {
      case 'spinner':
        return staticFile('loading/spinner.gif');
      case 'dots':
        return staticFile('loading/dots.webp');
      case 'pulse':
        return staticFile('loading/pulse.gif');
      default:
        return staticFile('loading/default.gif');
    }
  };

  // 淡入淡出效果
  const opacity = interpolate(
    frame,
    [0, 15, 45, 60],
    visible ? [0, 1, 1, 1] : [1, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 文字闪烁效果
  const textOpacity = interpolate(
    Math.sin(frame * 0.2),
    [-1, 1],
    [0.5, 1]
  );

  if (!visible && opacity === 0) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.7)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      opacity,
      zIndex: 1000
    }}>
      <AnimatedImage
        src={getLoadingSrc()}
        width={size}
        height={size}
        fit="contain"
        loopBehavior="loop"
      />
      
      {message && (
        <div style={{
          marginTop: 20,
          color: 'white',
          fontSize: 16,
          fontWeight: 'bold',
          opacity: textOpacity
        }}>
          {message}
        </div>
      )}
    </div>
  );
};

export default LoadingIndicator;
```

### 4. 交互式动画图标

```typescript
import React from 'react';
import { AnimatedImage, staticFile, useCurrentFrame, interpolate } from 'remotion';

interface InteractiveIconProps {
  icon: 'like' | 'share' | 'comment' | 'bookmark';
  isActive?: boolean;
  size?: number;
  onClick?: () => void;
}

const InteractiveIcon: React.FC<InteractiveIconProps> = ({ 
  icon, 
  isActive = false, 
  size = 40,
  onClick 
}) => {
  const frame = useCurrentFrame();

  // 根据图标类型和状态选择动画
  const getIconSrc = () => {
    const basePath = `icons/${icon}`;
    return staticFile(isActive ? `${basePath}-active.gif` : `${basePath}-inactive.png`);
  };

  // 激活时的缩放动画
  const scale = isActive ? interpolate(
    frame % 30,
    [0, 15, 30],
    [1, 1.2, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  ) : 1;

  // 悬停效果
  const brightness = isActive ? 1.2 : 1;

  return (
    <div 
      style={{
        display: 'inline-block',
        cursor: 'pointer',
        transform: `scale(${scale})`,
        transition: 'transform 0.2s ease',
        filter: `brightness(${brightness})`
      }}
      onClick={onClick}
    >
      <AnimatedImage
        src={getIconSrc()}
        width={size}
        height={size}
        fit="contain"
        loopBehavior={isActive ? "loop" : "pause-after-finish"}
      />
    </div>
  );
};

// 使用示例
export const SocialMediaBar = () => {
  const frame = useCurrentFrame();
  
  // 模拟交互状态
  const likeActive = frame > 60 && frame < 120;
  const shareActive = frame > 120 && frame < 180;

  return (
    <div style={{
      display: 'flex',
      gap: 20,
      padding: 20,
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderRadius: 25,
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
    }}>
      <InteractiveIcon icon="like" isActive={likeActive} />
      <InteractiveIcon icon="share" isActive={shareActive} />
      <InteractiveIcon icon="comment" />
      <InteractiveIcon icon="bookmark" />
    </div>
  );
};
```

### 5. 多格式动画支持

```typescript
import React from 'react';
import { AnimatedImage, staticFile, useCurrentFrame, interpolate } from 'remotion';

interface MultiFormatAnimationProps {
  animations: Array<{
    name: string;
    src: string;
    format: 'gif' | 'webp' | 'avif' | 'apng';
    description: string;
  }>;
}

const MultiFormatAnimation: React.FC<MultiFormatAnimationProps> = ({ animations }) => {
  const frame = useCurrentFrame();

  return (
    <div style={{
      width: '100%',
      height: '100%',
      backgroundColor: '#f0f2f5',
      padding: 40
    }}>
      <h2 style={{
        textAlign: 'center',
        marginBottom: 40,
        color: '#2c3e50'
      }}>
        多格式动画图像支持
      </h2>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: 30,
        maxWidth: 1000,
        margin: '0 auto'
      }}>
        {animations.map((animation, index) => {
          // 错开显示时间
          const startFrame = index * 20;
          const opacity = interpolate(
            frame,
            [startFrame, startFrame + 15],
            [0, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          const scale = interpolate(
            frame,
            [startFrame, startFrame + 10, startFrame + 20],
            [0.8, 1.05, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          return (
            <div
              key={index}
              style={{
                backgroundColor: 'white',
                borderRadius: 15,
                padding: 20,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                opacity,
                transform: `scale(${scale})`,
                textAlign: 'center'
              }}
            >
              <div style={{
                marginBottom: 15,
                height: 150,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f8f9fa',
                borderRadius: 10,
                overflow: 'hidden'
              }}>
                <AnimatedImage
                  src={staticFile(animation.src)}
                  width={120}
                  height={120}
                  fit="contain"
                  loopBehavior="loop"
                />
              </div>

              <h3 style={{
                fontSize: 18,
                fontWeight: 'bold',
                marginBottom: 8,
                color: '#2c3e50'
              }}>
                {animation.name}
              </h3>

              <div style={{
                display: 'inline-block',
                padding: '4px 12px',
                backgroundColor: getFormatColor(animation.format),
                color: 'white',
                borderRadius: 15,
                fontSize: 12,
                fontWeight: 'bold',
                marginBottom: 10
              }}>
                {animation.format.toUpperCase()}
              </div>

              <p style={{
                fontSize: 14,
                color: '#666',
                lineHeight: 1.4,
                margin: 0
              }}>
                {animation.description}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 辅助函数：根据格式返回颜色
const getFormatColor = (format: string) => {
  switch (format) {
    case 'gif': return '#e74c3c';
    case 'webp': return '#3498db';
    case 'avif': return '#2ecc71';
    case 'apng': return '#f39c12';
    default: return '#95a5a6';
  }
};

// 使用示例
export const FormatShowcase = () => {
  const animationData = [
    {
      name: "经典 GIF",
      src: "animations/classic.gif",
      format: "gif" as const,
      description: "传统的 GIF 格式，兼容性最好"
    },
    {
      name: "现代 WebP",
      src: "animations/modern.webp",
      format: "webp" as const,
      description: "更小的文件大小，更好的质量"
    },
    {
      name: "高效 AVIF",
      src: "animations/efficient.avif",
      format: "avif" as const,
      description: "最新格式，压缩率极高"
    },
    {
      name: "透明 APNG",
      src: "animations/transparent.apng",
      format: "apng" as const,
      description: "支持透明度的 PNG 动画"
    }
  ];

  return <MultiFormatAnimation animations={animationData} />;
};
```

## 适应方式说明

### fill (默认)
图像将完全填充容器，必要时会被拉伸

### contain
图像缩放以适应容器，保持宽高比

### cover
图像完全填充容器并保持宽高比，必要时会被裁剪

## 循环行为说明

### loop (默认)
动画图像将无限循环

### pause-after-finish
动画图像播放一次后显示最后一帧

### clear-after-finish
动画图像播放一次后清空画布

## 与 &lt;Gif&gt; 组件的区别

| 特性 | AnimatedImage | Gif |
|------|---------------|-----|
| 支持格式 | GIF, WebP, AVIF, APNG | 仅 GIF |
| 浏览器支持 | Chrome, Firefox | 所有浏览器 |
| Web API | ImageDecoder | 自定义实现 |
| onLoad 支持 | 不支持 | 支持 |

## 浏览器兼容性

- ✅ Google Chrome
- ✅ Firefox
- ❌ Safari (不支持 ImageDecoder)
- ❌ Edge (部分支持)

## 最佳实践

1. **格式选择**: 根据需求选择合适的动画图像格式
2. **文件优化**: 压缩动画图像以减少加载时间
3. **CORS 配置**: 确保远程图像支持跨域访问
4. **性能考虑**: 避免同时显示过多动画图像
5. **降级方案**: 为不支持的浏览器提供静态图像替代

## 常见用例

- 动画表情和反应
- 背景装饰动画
- 加载指示器
- 交互式图标
- 多格式动画展示

## 相关 API

- [`<Gif>`](./Gif.md) - GIF 专用组件
- [`staticFile()`](./staticFile.md) - 静态文件访问
- [`<Img>`](./Img.md) - 静态图像组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/animated-image/AnimatedImage.tsx)
