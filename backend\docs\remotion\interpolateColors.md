# interpolateColors()

## 概述

`interpolateColors()` 函数允许您使用简洁的语法将一系列数值映射到颜色。这是创建颜色动画和渐变效果的强大工具。

**版本要求**: v2.0.3+

## 语法

```typescript
import { interpolateColors } from "remotion";

const color = interpolateColors(input, inputRange, outputRange);
```

## 参数

1. **input** (`number`): 输入值
2. **inputRange** (`number[]`): 期望输入值的范围
3. **outputRange** (`string[]`): 希望输入映射到的输出颜色范围

## 返回值

- **类型**: `string`
- **格式**: `rgba` 颜色字符串，例如 `rgba(255, 100, 12, 1)`

## 基础用法

### 1. 基础颜色插值

```typescript
import { interpolateColors, useCurrentFrame } from "remotion";

const frame = useCurrentFrame();

// 从红色到黄色的渐变
const color = interpolateColors(frame, [0, 20], ["red", "yellow"]);
// 在第10帧时返回: rgba(255, 128, 0, 1)
```

### 2. 十六进制颜色

```typescript
import { interpolateColors, useCurrentFrame } from "remotion";

const frame = useCurrentFrame();

const color = interpolateColors(
  frame, 
  [0, 20], 
  ["#ff0000", "#ffff00"]
); // rgba(255, 128, 0, 1)
```

### 3. RGB 和 RGBA 颜色

```typescript
import { interpolateColors, useCurrentFrame } from "remotion";

const frame = useCurrentFrame();

// RGB 颜色
const rgbColor = interpolateColors(
  frame,
  [0, 20],
  ["rgb(255, 0, 0)", "rgb(255, 255, 0)"]
); // rgba(255, 128, 0, 1)

// RGBA 颜色（包含透明度）
const rgbaColor = interpolateColors(
  frame,
  [0, 20],
  ["rgba(255, 0, 0, 1)", "rgba(255, 255, 0, 0)"]
); // rgba(255, 128, 0, 0.5)
```

### 4. HSL 和 HSLA 颜色

```typescript
import { interpolateColors, useCurrentFrame } from "remotion";

const frame = useCurrentFrame();

// HSL 颜色
const hslColor = interpolateColors(
  frame,
  [0, 20],
  ["hsl(0, 100%, 50%)", "hsl(60, 100%, 50%)"]
); // rgba(255, 128, 0, 1)

// HSLA 颜色
const hslaColor = interpolateColors(
  frame,
  [0, 20],
  ["hsla(0, 100%, 50%, 1)", "hsla(60, 100%, 50%, 1)"]
); // rgba(255, 128, 0, 1)
```

## 实际应用场景

### 1. 文字颜色动画

```typescript
import React from 'react';
import { interpolateColors, useCurrentFrame, useVideoConfig } from "remotion";

const AnimatedText: React.FC<{ text: string }> = ({ text }) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 彩虹色文字效果
  const textColor = interpolateColors(
    frame,
    [0, durationInFrames / 6, durationInFrames / 3, durationInFrames / 2, durationInFrames * 2/3, durationInFrames * 5/6, durationInFrames],
    [
      "#ff0000", // 红色
      "#ff8000", // 橙色
      "#ffff00", // 黄色
      "#00ff00", // 绿色
      "#0080ff", // 蓝色
      "#8000ff", // 紫色
      "#ff0000"  // 回到红色
    ]
  );

  return (
    <div style={{
      fontSize: 64,
      fontWeight: "bold",
      color: textColor,
      textAlign: "center",
      textShadow: `0 0 20px ${textColor}`
    }}>
      {text}
    </div>
  );
};

export default AnimatedText;
```

### 2. 背景渐变动画

```typescript
import React from 'react';
import { interpolateColors, useCurrentFrame, useVideoConfig } from "remotion";

const GradientBackground: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames, width, height } = useVideoConfig();

  // 动态背景渐变
  const color1 = interpolateColors(
    frame,
    [0, durationInFrames / 2, durationInFrames],
    ["#667eea", "#764ba2", "#667eea"]
  );

  const color2 = interpolateColors(
    frame,
    [0, durationInFrames / 2, durationInFrames],
    ["#f093fb", "#f5576c", "#f093fb"]
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      background: `linear-gradient(45deg, ${color1}, ${color2})`,
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    }}>
      <h1 style={{
        color: "white",
        fontSize: 48,
        textShadow: "0 2px 4px rgba(0,0,0,0.3)"
      }}>
        动态渐变背景
      </h1>
    </div>
  );
};

export default GradientBackground;
```

### 3. 进度条颜色变化

```typescript
import React from 'react';
import { interpolateColors, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

interface ProgressBarProps {
  progress: number; // 0-100
  showPercentage?: boolean;
}

const ColorfulProgressBar: React.FC<ProgressBarProps> = ({ 
  progress, 
  showPercentage = true 
}) => {
  const frame = useCurrentFrame();

  // 根据进度值改变颜色
  const progressColor = interpolateColors(
    progress,
    [0, 25, 50, 75, 100],
    [
      "#ff4757", // 红色 (0-25%)
      "#ffa502", // 橙色 (25-50%)
      "#fffa65", // 黄色 (50-75%)
      "#7bed9f", // 浅绿 (75-100%)
      "#2ed573"  // 深绿 (100%)
    ]
  );

  // 动态发光效果
  const glowIntensity = interpolate(
    Math.sin(frame * 0.2),
    [-1, 1],
    [0.3, 1]
  );

  return (
    <div style={{
      width: 400,
      height: 30,
      backgroundColor: "#e0e0e0",
      borderRadius: 15,
      overflow: "hidden",
      position: "relative",
      boxShadow: "inset 0 2px 4px rgba(0,0,0,0.1)"
    }}>
      {/* 进度条填充 */}
      <div style={{
        width: `${progress}%`,
        height: "100%",
        backgroundColor: progressColor,
        borderRadius: 15,
        transition: "width 0.3s ease",
        boxShadow: `0 0 ${20 * glowIntensity}px ${progressColor}`,
        position: "relative"
      }}>
        {/* 高光效果 */}
        <div style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "50%",
          background: "linear-gradient(to bottom, rgba(255,255,255,0.3), transparent)",
          borderRadius: "15px 15px 0 0"
        }} />
      </div>
      
      {/* 百分比文字 */}
      {showPercentage && (
        <div style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          color: progress > 50 ? "white" : "#333",
          fontWeight: "bold",
          fontSize: 14,
          textShadow: progress > 50 ? "0 1px 2px rgba(0,0,0,0.5)" : "none"
        }}>
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
};

export default ColorfulProgressBar;
```

### 4. 多色粒子效果

```typescript
import React from 'react';
import { interpolateColors, interpolate, useCurrentFrame, random } from "remotion";

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  colorPhase: number;
}

const ParticleSystem: React.FC<{ particleCount?: number }> = ({ 
  particleCount = 50 
}) => {
  const frame = useCurrentFrame();

  // 生成粒子数据
  const particles: Particle[] = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    x: random(`x-${i}`) * 100,
    y: random(`y-${i}`) * 100,
    size: random(`size-${i}`) * 8 + 2,
    speed: random(`speed-${i}`) * 2 + 0.5,
    colorPhase: random(`color-${i}`) * 360
  }));

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundColor: "#000"
    }}>
      {particles.map((particle) => {
        // 粒子位置动画
        const animatedY = interpolate(
          frame,
          [0, 300],
          [particle.y, particle.y - 120],
          { extrapolateRight: "extend" }
        ) % 120;

        // 粒子颜色动画
        const particleColor = interpolateColors(
          (frame + particle.colorPhase) % 360,
          [0, 60, 120, 180, 240, 300, 360],
          [
            "#ff0080", // 粉红
            "#ff8000", // 橙色
            "#ffff00", // 黄色
            "#80ff00", // 浅绿
            "#00ffff", // 青色
            "#8000ff", // 紫色
            "#ff0080"  // 回到粉红
          ]
        );

        // 粒子透明度动画
        const opacity = interpolate(
          Math.sin((frame + particle.id * 10) * 0.1),
          [-1, 1],
          [0.3, 1]
        );

        return (
          <div
            key={particle.id}
            style={{
              position: "absolute",
              left: `${particle.x}%`,
              top: `${animatedY}%`,
              width: particle.size,
              height: particle.size,
              backgroundColor: particleColor,
              borderRadius: "50%",
              opacity: opacity,
              boxShadow: `0 0 ${particle.size * 2}px ${particleColor}`,
              transform: `scale(${interpolate(
                Math.sin((frame + particle.id * 5) * 0.15),
                [-1, 1],
                [0.8, 1.2]
              )})`
            }}
          />
        );
      })}
    </div>
  );
};

export default ParticleSystem;
```

### 5. 主题色切换动画

```typescript
import React from 'react';
import { interpolateColors, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

interface ThemeTransitionProps {
  themes: {
    name: string;
    primary: string;
    secondary: string;
    background: string;
    text: string;
  }[];
  transitionDuration?: number;
}

const ThemeTransition: React.FC<ThemeTransitionProps> = ({ 
  themes, 
  transitionDuration = 60 
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 计算当前主题索引
  const themeProgress = (frame / transitionDuration) % themes.length;
  const currentThemeIndex = Math.floor(themeProgress);
  const nextThemeIndex = (currentThemeIndex + 1) % themes.length;
  const transitionProgress = themeProgress - currentThemeIndex;

  // 创建颜色范围数组
  const primaryColors = themes.map(theme => theme.primary);
  const secondaryColors = themes.map(theme => theme.secondary);
  const backgroundColors = themes.map(theme => theme.background);
  const textColors = themes.map(theme => theme.text);

  // 插值当前颜色
  const currentPrimary = interpolateColors(
    frame,
    themes.map((_, i) => i * transitionDuration),
    primaryColors
  );

  const currentSecondary = interpolateColors(
    frame,
    themes.map((_, i) => i * transitionDuration),
    secondaryColors
  );

  const currentBackground = interpolateColors(
    frame,
    themes.map((_, i) => i * transitionDuration),
    backgroundColors
  );

  const currentText = interpolateColors(
    frame,
    themes.map((_, i) => i * transitionDuration),
    textColors
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: currentBackground,
      color: currentText,
      padding: 40,
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      transition: "all 0.3s ease"
    }}>
      <h1 style={{
        fontSize: 48,
        marginBottom: 30,
        color: currentPrimary,
        textShadow: `0 2px 4px ${currentSecondary}`
      }}>
        主题切换演示
      </h1>
      
      <div style={{
        padding: "20px 40px",
        backgroundColor: currentPrimary,
        color: currentBackground,
        borderRadius: 10,
        fontSize: 24,
        fontWeight: "bold",
        boxShadow: `0 4px 8px ${currentSecondary}`,
        marginBottom: 20
      }}>
        当前主题: {themes[currentThemeIndex].name}
      </div>
      
      <div style={{
        width: 200,
        height: 10,
        backgroundColor: currentSecondary,
        borderRadius: 5,
        position: "relative"
      }}>
        <div style={{
          width: `${(transitionProgress) * 100}%`,
          height: "100%",
          backgroundColor: currentPrimary,
          borderRadius: 5,
          transition: "width 0.1s ease"
        }} />
      </div>
    </div>
  );
};

// 使用示例
export const ThemeDemo = () => {
  const themes = [
    {
      name: "海洋蓝",
      primary: "#3498db",
      secondary: "#2980b9",
      background: "#ecf0f1",
      text: "#2c3e50"
    },
    {
      name: "森林绿",
      primary: "#27ae60",
      secondary: "#229954",
      background: "#d5f4e6",
      text: "#1e8449"
    },
    {
      name: "日落橙",
      primary: "#e67e22",
      secondary: "#d35400",
      background: "#fdeaa7",
      text: "#b7950b"
    },
    {
      name: "紫罗兰",
      primary: "#9b59b6",
      secondary: "#8e44ad",
      background: "#f4ecf7",
      text: "#6c3483"
    }
  ];

  return <ThemeTransition themes={themes} transitionDuration={90} />;
};
```

## 支持的颜色格式

- **颜色名称**: `"red"`, `"blue"`, `"yellow"` 等
- **十六进制**: `"#ff0000"`, `"#00ff00"` 等
- **RGB**: `"rgb(255, 0, 0)"` 等
- **RGBA**: `"rgba(255, 0, 0, 0.5)"` 等
- **HSL**: `"hsl(0, 100%, 50%)"` 等
- **HSLA**: `"hsla(0, 100%, 50%, 0.5)"` 等

## 最佳实践

1. **性能优化**: 避免在每帧都重新计算复杂的颜色插值
2. **颜色空间**: 了解不同颜色格式的插值行为差异
3. **视觉连续性**: 确保颜色过渡自然流畅
4. **可访问性**: 考虑颜色对比度和色盲友好性
5. **缓存优化**: 对于复杂的颜色计算考虑使用缓存

## 常见用例

- 文字颜色动画
- 背景渐变效果
- 进度条颜色变化
- 粒子系统颜色
- 主题切换动画
- UI 元素状态指示

## 相关 API

- [`interpolate()`](./interpolate.md) - 数值插值
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/interpolate-colors.ts)
