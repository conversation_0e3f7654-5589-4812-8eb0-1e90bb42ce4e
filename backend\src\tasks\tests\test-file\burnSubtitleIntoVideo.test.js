/**
 * @功能概述: ASS字幕烧录到视频的独立测试脚本，验证FFmpeg字幕烧录功能的完整性
 * @核心能力:
 *   - ASS字幕文件读取和内容验证
 *   - 视频文件存在性检查和基本信息获取
 *   - FFmpeg字幕烧录命令构建和执行
 *   - 输出文件生成验证和质量检查
 * @输入依赖:
 *   - ASS字幕文件路径 (ASS_SUBTITLE_PATH)
 *   - 源视频文件路径 (VIDEO_PATH)
 *   - 输出保存目录路径 (SAVE_PATH)
 *   - 系统FFmpeg可执行文件
 * @输出结果:
 *   - 带有烧录字幕的MP4视频文件
 *   - 详细的执行日志和进度信息
 *   - 文件大小和质量验证报告
 * @测试范围: 端到端字幕烧录流程，不包含LLM调用
 * @执行方式: node backend/src/tasks/tests/test.js
 * @version 1.0.0
 * <AUTHOR>
 * @created 2025-01-07
 * @updated 2025-06-08
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

// 模块级日志前缀 - 遵循标准格式
const moduleLogPrefix = '[文件：test.js][ASS字幕烧录测试][模块初始化]';

/**
 * @功能概述: 标准化日志输出函数，提供统一的时间戳和级别标记
 * @参数说明:
 *   - message: {string} 日志消息内容
 *   - level: {string} 日志级别 (INFO, ERROR, WARN, DEBUG)
 *   - functionName: {string} [可选] 调用函数名，用于构建完整日志前缀
 * @返回值: 无返回值，直接输出到控制台
 * @执行流程:
 *   1. 生成ISO格式时间戳
 *   2. 构建标准化日志前缀
 *   3. 格式化并输出日志消息
 */
function log(message, level = 'INFO', functionName = 'general') {
    const timestamp = new Date().toISOString();
    const logPrefix = `[文件：test.js][ASS字幕烧录测试][${functionName}]`;
    console.log(`${timestamp} [${level}] ${logPrefix} ${message}`);
}

// 记录模块初始化
log('ASS字幕烧录测试模块已加载', 'INFO', '模块初始化');

/**
 * @配置说明: 测试数据文件路径配置
 * @注意事项:
 *   - 路径使用绝对路径确保测试稳定性
 *   - 所有测试文件应位于 backend/uploads/test-data/ 目录
 *   - 修改路径时需确保文件实际存在
 */

/**
 * ASS字幕文件路径 - 包含完整字幕格式和样式定义
 */
const ASS_SUBTITLE_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\test_123_subtitle.ass';

/**
 * 源视频文件路径 - 用于字幕烧录的基础视频文件
 */
const VIDEO_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z.mp4';

/**
 * 输出文件保存目录路径 - 生成的带字幕视频文件存储位置
 */
const SAVE_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data';




/**
 * @功能概述: 读取ASS字幕文件内容并进行基本验证
 * @返回值: {Promise<string>} ASS字幕文本内容，包含完整的格式和时间轴信息
 * @错误处理:
 *   - 文件不存在时抛出访问错误
 *   - 文件读取失败时抛出IO错误
 *   - 所有错误都会记录详细日志
 * @执行流程:
 *   1. 检查ASS文件是否存在和可访问
 *   2. 读取文件内容为UTF-8格式文本
 *   3. 验证内容长度和格式预览
 *   4. 返回完整ASS字幕内容
 */
async function readAssSubtitle() {
    const functionName = 'readAssSubtitle';

    try {
        log('开始读取ASS字幕文件...', 'INFO', functionName); // 记录函数开始执行
        log(`ASS文件路径: ${ASS_SUBTITLE_PATH}`, 'DEBUG', functionName); // 记录文件路径信息

        // 步骤 1: 检查文件是否存在
        await fs.access(ASS_SUBTITLE_PATH);
        log('ASS文件存在，开始读取内容', 'INFO', functionName); // 记录文件存在确认

        // 步骤 2: 读取文件内容
        const assContent = await fs.readFile(ASS_SUBTITLE_PATH, 'utf-8');
        log(`ASS文件读取成功，内容长度: ${assContent.length} 字符`, 'INFO', functionName); // 记录读取成功和内容长度

        // 步骤 3: 显示ASS文件的前200个字符作为预览
        const preview = assContent.substring(0, 200).replace(/\n/g, '\\n');
        log(`ASS内容预览: ${preview}...`, 'DEBUG', functionName); // 记录内容预览（调试级别）

        return assContent;

    } catch (error) {
        log(`读取ASS字幕文件失败: ${error.message}`, 'ERROR', functionName); // 记录读取失败错误
        throw error; // 重新抛出错误供上层处理
    }
}

/**
 * @功能概述: 检查源视频文件是否存在并获取基本文件信息
 * @返回值: {Promise<boolean>} 视频文件是否存在且可访问
 * @错误处理:
 *   - 文件不存在时返回false而非抛出异常
 *   - 记录详细的错误信息用于问题诊断
 * @执行流程:
 *   1. 尝试访问视频文件路径
 *   2. 获取文件统计信息（大小、修改时间等）
 *   3. 计算并记录文件大小（MB单位）
 *   4. 返回文件存在状态
 */
async function checkVideoFile() {
    const functionName = 'checkVideoFile';

    try {
        log('检查视频文件是否存在...', 'INFO', functionName); // 记录检查开始
        log(`视频文件路径: ${VIDEO_PATH}`, 'DEBUG', functionName); // 记录文件路径

        // 步骤 1: 检查文件访问权限
        await fs.access(VIDEO_PATH);

        // 步骤 2: 获取文件统计信息
        const stats = await fs.stat(VIDEO_PATH);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        log(`视频文件存在，大小: ${fileSizeMB}MB`, 'INFO', functionName); // 记录文件存在和大小信息
        return true;

    } catch (error) {
        log(`视频文件不存在或无法访问: ${error.message}`, 'ERROR', functionName); // 记录访问失败错误
        return false; // 返回false而非抛出异常，便于上层逻辑处理
    }
}

/**
 * @功能概述: 检查输出保存目录是否存在且具有写入权限
 * @返回值: {Promise<boolean>} 保存路径是否存在且可写入
 * @错误处理:
 *   - 目录不存在时返回false
 *   - 权限不足时记录详细错误信息
 * @执行流程:
 *   1. 验证保存目录的访问权限
 *   2. 确认目录可用于文件写入操作
 *   3. 返回目录可用状态
 */
async function checkSavePath() {
    const functionName = 'checkSavePath';

    try {
        log('检查保存路径是否存在...', 'INFO', functionName); // 记录检查开始
        log(`保存路径: ${SAVE_PATH}`, 'DEBUG', functionName); // 记录保存路径

        // 步骤 1: 检查目录访问权限
        await fs.access(SAVE_PATH);
        log('保存路径存在', 'INFO', functionName); // 记录路径存在确认
        return true;

    } catch (error) {
        log(`保存路径不存在或无法访问: ${error.message}`, 'ERROR', functionName); // 记录访问失败错误
        return false; // 返回false便于上层处理
    }
}

/**
 * @功能概述: 使用FFmpeg将ASS字幕烧录到视频文件，生成带字幕的最终视频
 * @参数说明:
 *   - assContent: {string} ASS字幕内容（当前版本直接使用文件路径，未来可优化为内容注入）
 * @返回值: {Promise<string>} 生成的输出视频文件完整路径
 * @错误处理:
 *   - FFmpeg进程启动失败时抛出进程错误
 *   - 转换超时（10分钟）时强制终止并抛出超时错误
 *   - 输出文件验证失败时抛出文件错误
 * @执行流程:
 *   1. 生成带时间戳的输出文件名
 *   2. 构建FFmpeg命令参数（字幕滤镜、编码设置）
 *   3. 启动FFmpeg子进程并监听输出
 *   4. 解析进度信息并实时报告
 *   5. 验证输出文件并返回路径
 * @技术参数:
 *   - 视频编码器: libx264
 *   - 视频质量: CRF 23 (高质量)
 *   - 编码预设: medium (平衡速度和质量)
 *   - 音频处理: 直接复制（无重编码）
 *   - 字幕滤镜: subtitles (支持ASS格式)
 */
async function burnAssSubtitleToVideo(assContent) {
    const functionName = 'burnAssSubtitleToVideo';

    return new Promise((resolve, reject) => {
        try {
            log('开始ASS字幕烧录到视频...', 'INFO', functionName); // 记录烧录开始

            // 步骤 1: 生成输出文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const outputFileName = `video_with_ass_subtitle_${timestamp}.mp4`;
            const outputPath = path.join(SAVE_PATH, outputFileName);

            log(`输出文件路径: ${outputPath}`, 'DEBUG', functionName); // 记录输出路径

            // 步骤 2: 构建FFmpeg命令参数
            // 使用subtitles滤镜，需要正确转义路径中的特殊字符
            const escapedAssPath = ASS_SUBTITLE_PATH.replace(/\\/g, '/').replace(/:/g, '\\:');
            const ffmpegArgs = [
                '-i', VIDEO_PATH,                           // 输入视频
                '-vf', `subtitles='${escapedAssPath}'`,     // ASS字幕滤镜（用单引号包围路径）
                '-c:a', 'copy',                             // 音频直接复制
                '-c:v', 'libx264',                          // 视频编码器
                '-crf', '23',                               // 视频质量
                '-preset', 'medium',                        // 编码预设
                '-y',                                       // 覆盖输出文件
                outputPath                                  // 输出路径
            ];

            log(`FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`, 'DEBUG', functionName); // 记录完整FFmpeg命令

            // 步骤 3: 启动FFmpeg进程
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

            let ffmpegOutput = '';
            let ffmpegError = '';

            // 步骤 4: 监听stdout输出（标准输出）
            ffmpegProcess.stdout.on('data', (data) => {
                ffmpegOutput += data.toString();
                // 标准输出通常较少，主要用于收集完整输出
            });

            // 步骤 5: 监听stderr输出（FFmpeg的主要输出和进度信息）
            ffmpegProcess.stderr.on('data', (data) => {
                const output = data.toString();
                ffmpegError += output;

                // 步骤 5.1: 解析时间进度信息
                if (output.includes('time=')) {
                    const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                    if (timeMatch) {
                        const timeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
                        log(`FFmpeg进度: ${timeStr}`, 'INFO', functionName); // 记录时间进度
                    }
                }

                // 步骤 5.2: 解析帧率信息
                if (output.includes('fps=')) {
                    const fpsMatch = output.match(/fps=\s*(\d+\.?\d*)/);
                    if (fpsMatch) {
                        log(`当前处理帧率: ${fpsMatch[1]} fps`, 'DEBUG', functionName); // 记录帧率信息（调试级别）
                    }
                }
            });

            // 步骤 6: 监听进程退出事件
            ffmpegProcess.on('close', async (code) => {
                if (code === 0) {
                    log('FFmpeg执行成功', 'INFO', functionName); // 记录执行成功

                    try {
                        // 步骤 6.1: 验证输出文件是否存在
                        await fs.access(outputPath);

                        // 步骤 6.2: 获取输出文件统计信息
                        const stats = await fs.stat(outputPath);
                        const outputSizeMB = (stats.size / 1024 / 1024).toFixed(2);

                        log(`输出文件生成成功: ${outputPath}`, 'INFO', functionName); // 记录文件生成成功
                        log(`输出文件大小: ${outputSizeMB}MB`, 'INFO', functionName); // 记录文件大小

                        resolve(outputPath); // 返回输出文件路径

                    } catch (error) {
                        log(`输出文件验证失败: ${error.message}`, 'ERROR', functionName); // 记录文件验证失败
                        reject(new Error(`输出文件不存在: ${outputPath}`)); // 抛出文件不存在错误
                    }
                } else {
                    const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName); // 记录执行失败
                    log(`FFmpeg错误输出: ${ffmpegError.slice(-500)}`, 'ERROR', functionName); // 记录错误输出（最后500字符）
                    reject(new Error(errorMsg)); // 抛出执行失败错误
                }
            });

            // 步骤 7: 监听进程启动错误
            ffmpegProcess.on('error', (error) => {
                const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName); // 记录进程启动失败
                reject(new Error(errorMsg)); // 抛出进程启动错误
            });

            // 步骤 8: 设置超时保护机制（10分钟）
            setTimeout(() => {
                if (!ffmpegProcess.killed) {
                    log('FFmpeg执行超时，强制终止进程', 'WARN', functionName); // 记录超时警告
                    ffmpegProcess.kill('SIGTERM'); // 优雅终止

                    // 5秒后强制杀死进程
                    setTimeout(() => {
                        if (!ffmpegProcess.killed) {
                            ffmpegProcess.kill('SIGKILL'); // 强制杀死
                        }
                    }, 5000);

                    reject(new Error('FFmpeg执行超时')); // 抛出超时错误
                }
            }, 600000); // 10分钟超时

        } catch (error) {
            log(`ASS字幕烧录失败: ${error.message}`, 'ERROR', functionName); // 记录烧录失败
            reject(error); // 重新抛出错误
        }
    });
}

/**
 * @功能概述: 主执行函数，协调整个ASS字幕烧录测试流程
 * @执行流程:
 *   1. 读取ASS字幕文件内容
 *   2. 验证源视频文件存在性
 *   3. 检查输出保存路径可用性
 *   4. 执行FFmpeg字幕烧录操作
 *   5. 验证最终输出结果
 * @错误处理:
 *   - 任何步骤失败都会终止整个流程
 *   - 记录详细错误信息并以错误码退出
 * @测试结果:
 *   - 成功时生成带字幕的MP4文件
 *   - 失败时输出详细错误诊断信息
 */
async function main() {
    const functionName = 'main';

    console.log('main函数开始执行...'); // 控制台直接输出，便于脚本监控

    try {
        log('========== 开始ASS字幕烧录测试 ==========', 'INFO', functionName); // 记录测试开始

        // 步骤 1: 读取ASS字幕内容
        log('步骤1: 读取ASS字幕文件', 'INFO', functionName); // 记录步骤1开始
        const assContent = await readAssSubtitle();

        // 步骤 2: 检查视频文件
        log('步骤2: 检查视频文件', 'INFO', functionName); // 记录步骤2开始
        const videoExists = await checkVideoFile();
        if (!videoExists) {
            throw new Error('视频文件不存在，无法继续'); // 抛出视频文件不存在错误
        }

        // 步骤 3: 检查保存路径
        log('步骤3: 检查保存路径', 'INFO', functionName); // 记录步骤3开始
        const savePathExists = await checkSavePath();
        if (!savePathExists) {
            throw new Error('保存路径不存在，无法继续'); // 抛出保存路径不存在错误
        }

        // 步骤 4: 执行ASS字幕烧录
        log('步骤4: 执行ASS字幕烧录', 'INFO', functionName); // 记录步骤4开始
        const outputPath = await burnAssSubtitleToVideo(assContent);

        log('========== ASS字幕烧录测试完成 ==========', 'INFO', functionName); // 记录测试完成
        log(`✅ 成功生成带字幕的视频: ${outputPath}`, 'INFO', functionName); // 记录成功结果

        console.log('测试完成，准备退出...'); // 控制台输出
        process.exit(0); // 成功退出

    } catch (error) {
        log(`❌ ASS字幕烧录测试失败: ${error.message}`, 'ERROR', functionName); // 记录测试失败
        console.error('错误详情:', error); // 控制台输出错误详情
        process.exit(1); // 错误退出
    }
}

/**
 * @脚本启动: 立即执行主函数并处理未捕获异常
 * @错误处理:
 *   - 捕获Promise拒绝和未处理异常
 *   - 确保脚本在任何情况下都能正确退出
 * @日志记录: 在控制台和日志系统中同时记录关键信息
 */

// 立即执行主函数
console.log('开始执行ASS字幕烧录测试脚本...'); // 控制台输出脚本启动信息
log('ASS字幕烧录测试脚本启动', 'INFO', '脚本启动'); // 日志记录脚本启动

main().catch(error => {
    console.error('脚本执行失败:', error); // 控制台输出错误
    log(`脚本执行失败: ${error.message}`, 'ERROR', '脚本启动'); // 日志记录错误
    process.exit(1); // 错误退出
});