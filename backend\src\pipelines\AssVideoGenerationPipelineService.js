/**
 * @文件名: AssVideoGenerationPipelineService.js
 * @功能概述: ASS字幕和视频生成流水线服务 - 专门测试GenerateASSTask和GenerateVideoTask
 * @作者: AI Assistant
 * @创建时间: 2025-01-20
 *
 * @功能描述:
 *   专门用于测试ASS字幕生成和视频合成的流水线，只包含两个核心任务：
 *   1. GenerateASSTask - 生成ASS字幕文件
 *   2. GenerateVideoTask - 生成最终视频
 *   主要用于测试这两个任务的功能和video-config.json配置文件的各项设置效果
 *
 * @流水线任务:
 *   Task 1: GenerateASSTask
 *   Task 2: GenerateVideoTask
 *
 * @输入上下文:
 *   - videoIdentifier: {string} 视频唯一标识符
 *   - audioFilePath: {string} 音频文件路径
 *   - clozedSubtitleJsonArray: {Array} 填空字幕数组
 *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组
 *   - savePath: {string} 文件保存路径
 *   - originalVideoPath: {string} 原始视频文件路径
 *   - videoConfig: {Object} 视频配置对象（从video-config.json加载）
 *
 * @输出结果:
 *   - assFilePath: {string} 生成的ASS字幕文件路径
 *   - finalVideoPath: {string} 最终生成的视频文件路径
 *   - videoGenerationStats: {Object} 视频生成统计信息
 */

const PipelineBase = require('../class/PipelineBase');
const logger = require('../utils/logger');

// 导入任务
const GenerateASSTask = require('../tasks/GenerateASSTask');
const GenerateVideoTask = require('../tasks/GenerateVideoTask');

/**
 * @类名: AssVideoGenerationPipelineService
 * @继承: PipelineBase
 * @功能: ASS字幕和视频生成流水线服务类
 */
class AssVideoGenerationPipelineService extends PipelineBase {
    constructor() {
        super('AssVideoGenerationPipeline');
        this.instanceLogPrefix = `[${this.pipelineName}][${this.instanceId}]`;

        // 添加所有任务到流水线
        this.addAllTasks();
    }

    /**
     * @功能概述: 添加GenerateASSTask到流水线
     * @任务功能: 根据填空字幕和双语字幕数据生成ASS字幕文件
     * 
     * @上下文输入:
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - clozedSubtitleJsonArray: {Array} 填空字幕数组
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组
     *   - savePath: {string} 文件保存路径
     *   - videoConfig: {Object} 视频配置对象
     * 
     * @执行后上下文状态:
     *   // === 来自初始输入 ===
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - audioFilePath: {string} 音频文件路径
     *   - clozedSubtitleJsonArray: {Array} 填空字幕数组
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组
     *   - savePath: {string} 文件保存路径
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - videoConfig: {Object} 视频配置对象
     *   
     *   // === 来自 GenerateASSTask (第1个任务新增) ===
     *   - assFilePath: {string} 生成的ASS字幕文件路径
     *   - assContent: {string} ASS字幕文件内容
     *   - subtitleStats: {Object} 字幕统计信息
     */
    addGenerateASSTask() {
        this.addTask(new GenerateASSTask());
    }

    /**
     * @功能概述: 添加GenerateVideoTask到流水线
     * @任务功能: 使用ASS字幕文件和原始视频生成最终视频
     * 
     * @上下文输入:
     *   - assFilePath: {string} ASS字幕文件路径（来自GenerateASSTask）
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - audioFilePath: {string} 音频文件路径
     *   - videoConfig: {Object} 视频配置对象
     *   - savePath: {string} 文件保存路径
     * 
     * @执行后上下文状态:
     *   // === 来自初始输入 ===
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - audioFilePath: {string} 音频文件路径
     *   - clozedSubtitleJsonArray: {Array} 填空字幕数组
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组
     *   - savePath: {string} 文件保存路径
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - videoConfig: {Object} 视频配置对象
     *   
     *   // === 来自 GenerateASSTask (第1个任务) ===
     *   - assFilePath: {string} 生成的ASS字幕文件路径
     *   - assContent: {string} ASS字幕文件内容
     *   - subtitleStats: {Object} 字幕统计信息
     *   
     *   // === 来自 GenerateVideoTask (第2个任务，当前任务新增) ===
     *   - finalVideoPath: {string} 最终生成的视频文件路径
     *   - videoGenerationStats: {Object} 视频生成统计信息
     *   - videoDuration: {number} 视频时长（秒）
     *   - videoResolution: {string} 视频分辨率
     *   
     *   // === 最终流水线输出摘要 ===
     *   // 此时 context 包含完整的处理结果，主要可用数据：
     *   // 1. finalVideoPath - 最终生成的视频文件路径
     *   // 2. assFilePath - ASS字幕文件路径
     *   // 3. videoGenerationStats - 视频生成统计信息
     *   // 4. subtitleStats - 字幕统计信息
     */
    addGenerateVideoTask() {
        this.addTask(new GenerateVideoTask());
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     */
    addAllTasks() {
        // 按执行顺序添加任务
        this.addGenerateASSTask();
        this.addGenerateVideoTask();
        
        logger.info(`${this.instanceLogPrefix} AssVideoGeneration Pipeline 已创建，包含完整任务序列: GenerateASSTask → GenerateVideoTask`);
    }

    /**
     * @功能概述: 执行完整的ASS字幕和视频生成流程
     * @param {object} initialContext - 初始上下文，必须包含:
     *                                 - videoIdentifier: string - 视频唯一标识符
     *                                 - audioFilePath: string - 音频文件路径
     *                                 - clozedSubtitleJsonArray: Array - 填空字幕数组
     *                                 - enhancedBilingualSubtitleJsonArray: Array - 增强双语字幕数组
     *                                 - savePath: string - 文件保存路径
     *                                 - originalVideoPath: string - 原始视频文件路径
     *                                 - videoConfig: Object - 视频配置对象
     * @param {function} serviceProgressCallback - 服务进度回调函数，用于报告服务执行状态
     * @returns {Promise<object>} Pipeline 执行结果，包含 status, context, tasks。
     *                            成功时，context 中应包含:
     *                              - assFilePath: string (来自 GenerateASSTask)
     *                              - finalVideoPath: string (来自 GenerateVideoTask)
     *                              - videoGenerationStats: Object (来自 GenerateVideoTask)
     */
    async processVideoGeneration(initialContext, serviceProgressCallback) {
        logger.info(`${this.instanceLogPrefix}[processVideoGeneration] 开始执行ASS字幕和视频生成流程。`);
        
        // 步骤 1: 构建执行上下文
        const currentContext = {
            ...initialContext
        };
        
        // 步骤 2: 参数校验
        const requiredFields = ['videoIdentifier', 'audioFilePath', 'clozedSubtitleJsonArray', 'enhancedBilingualSubtitleJsonArray', 'savePath', 'originalVideoPath', 'videoConfig'];
        const missingFields = requiredFields.filter(field => !currentContext[field]);
        
        if (missingFields.length > 0) {
            const errorMsg = `processVideoGeneration 调用失败：initialContext 必须包含 ${missingFields.join(', ')}。`;
            logger.error(`${this.instanceLogPrefix}[processVideoGeneration] ${errorMsg}`);
            
            // 步骤 2.1: 参数校验失败时的回调处理
            if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
                try {
                    logger.debug(`${this.instanceLogPrefix}[processVideoGeneration] 参数校验失败，调用服务进度回调。`);
                    serviceProgressCallback({
                        serviceName: 'AssVideoGenerationPipelineService',
                        methodName: 'processVideoGeneration',
                        status: 'failed_setup',
                        error: { message: errorMsg },
                        timestamp: new Date().toISOString()
                    });
                } catch (cbError) {
                    logger.error(`${this.instanceLogPrefix}[processVideoGeneration] 服务进度回调执行出错: ${cbError.message}`);
                }
            }
            
            return { 
                status: 'failed', 
                error: new Error(errorMsg), 
                context: currentContext, 
                tasks: [] 
            };
        }
        
        // 步骤 3: 记录任务序列
        logger.info(`${this.instanceLogPrefix}[processVideoGeneration][步骤 1] 流水线任务序列确认: GenerateASSTask → GenerateVideoTask`);
        
        // 步骤 4: 执行流水线
        logger.debug(`${this.instanceLogPrefix}[processVideoGeneration] 执行内部流水线，并传递回调函数。`);
        const result = await this.execute(currentContext, serviceProgressCallback);
        logger.info(`${this.instanceLogPrefix}[processVideoGeneration] ASS字幕和视频生成流程执行完毕。Status: ${result.status}`);
        
        // 步骤 5: 记录关键输出
        if (result.context) {
            const { assFilePath, finalVideoPath, videoGenerationStats } = result.context;
            logger.debug(`${this.instanceLogPrefix}[processVideoGeneration] 最终上下文关键信息: assFilePath=${assFilePath}, finalVideoPath=${finalVideoPath}, 统计信息=${videoGenerationStats ? JSON.stringify(videoGenerationStats) : '未知'}`);
        }
        
        return result;
    }
}

module.exports = AssVideoGenerationPipelineService;
