/**
 * @功能概述: Puppeteer网页录制任务，负责访问指定的动画网页API并录制生成视频文件。
 *           专门用于录制Web动画效果，支持16:9视口控制和30fps高质量视频输出。
 * @输入依赖: context需包含targetUrl、outputPath、duration、width、height等字段
 * @输出结果: 向context添加recordedVideoPath、videoFileName、recordingStats等字段
 * @外部依赖: Puppeteer浏览器自动化、FFmpeg视频处理工具
 * @失败策略: 任何步骤失败时立即停止并抛出详细错误信息，支持浏览器资源清理
 * @执行流程:
 *   1. 参数校验：验证目标URL、输出路径和录制参数的有效性
 *   2. 浏览器启动：启动Puppeteer浏览器并设置视口
 *   3. 页面访问：访问目标动画网页API
 *   4. 视频录制：使用screencast方法录制网页动画
 *   5. 结果处理：保存视频文件并更新context
 */

// 标准模块导入
const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');

// 模块级日志前缀
const taskModuleLogPrefix = '[文件：PuppeteerRecorderTask.js][Puppeteer录制任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`); // 日志：确认 PuppeteerRecorderTask 模块已成功加载到应用中。

class PuppeteerRecorderTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建Puppeteer录制任务实例并初始化日志前缀。
     * @param {string} [name='PuppeteerRecorderTask'] - 任务名称，用于日志标识和进度追踪
     */
    constructor(name = 'PuppeteerRecorderTask') {
        super(name); // 调用 TaskBase 构造函数
        this.instanceLogPrefix = `[文件：PuppeteerRecorderTask.js][Puppeteer录制任务][${this.name}]`;
        logger.info(`${this.instanceLogPrefix} PuppeteerRecorderTask 实例已创建。`); // 日志：记录任务实例成功创建的事件。
    }

    /**
     * @功能概述: 从上下文对象中提取文件标识符，用于日志记录和任务追踪。
     * @param {object} context - 上下文对象，期望包含renderPageUrl字段
     * @returns {string} 文件标识符，如果无法提取则返回'unknown_recording'
     */
    extractFileIdentifier(context) {
        if (context.renderPageUrl) {
            // 从URL中提取有意义的标识符
            try {
                const url = new URL(context.renderPageUrl);
                return url.pathname.replace(/[^a-zA-Z0-9]/g, '_') || 'url_recording';
            } catch (e) {
                return 'invalid_url_recording';
            }
        }
        return 'unknown_recording';
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段，确保任务执行的前置条件满足。
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出详细错误信息
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (!context[field] && typeof context[field] !== 'number') { // 允许数值为 0
                const errorMsg = `执行失败：上下文缺少必需字段 ${field} 或字段值无效。`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`); // 日志：记录因缺少必需字段或字段值无效导致的执行失败。
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。`); // 日志：确认所有必需的输入参数均已通过验证。
    }

    /**
     * @功能概述: 执行Puppeteer录制任务的核心逻辑，实现网页动画录制和视频生成。
     * @param {object} context - 上下文对象，包含任务执行所需的所有输入数据
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度
     * @returns {Promise<object>} 包含录制结果的对象，包括recordedVideoPath等关键信息
     * @throws {Error} 如果在执行过程中发生不可恢复的错误
     * @执行流程:
     *   1. 参数校验：验证目标URL、输出路径和录制参数
     *   2. 浏览器启动：启动Puppeteer浏览器并设置视口
     *   3. 页面访问：访问目标动画网页API
     *   4. 视频录制：使用screencast方法录制网页动画
     *   5. 结果处理：保存视频文件并更新context
     */
    async execute(context, progressCallback) {
        // 步骤 0: 从上下文中解构必要参数
        const {
            renderPageUrl = 'http://localhost:9999/api/bbc-render',
            chineseTitle = 'BBC英语精听',
            englishTitle = 'Breaking News',
            subtitle = '突发新闻',
            countdown = 3,
            duration = 4,
            fps = 30,
            width = 1920,
            height = 1080,
            outputPath,
            reqId
        } = context;

        // 提取文件标识符用于日志追踪
        const fileIdentifier = this.extractFileIdentifier(context);
        
        // 任务执行日志前缀，包含请求ID和文件标识符
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${fileIdentifier}]`;

        // 设置进度回调函数，用于向上层报告执行进度
        this.setProgressCallback(progressCallback);
        // 使用标准化方法标记任务开始，自动报告STARTED状态
        this.start();

        // 提前声明并初始化所有任务处理结果相关的变量
        let browser = null;
        let recordedVideoPath = null;
        let videoFileName = null;
        let recordingStats = null;

        try {
            // 步骤 1: 参数校验与准备
            const requiredFields = ['reqId']; // 基础必需字段
            this.validateRequiredFields(context, requiredFields, execLogPrefix);

            logger.info(`${execLogPrefix}[执行阶段] 任务开始执行Puppeteer录制逻辑。`); // 日志：记录任务开始执行主要业务逻辑。

            // 步骤 2: 报告初始化进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '正在启动Puppeteer浏览器',
                current: 10,
                total: 100,
                technicalDetail: `渲染页面: ${renderPageUrl}, 视口: ${width}x${height}`
            });

            // 步骤 3: 启动Puppeteer浏览器（高速模式）
            logger.info(`${execLogPrefix}[步骤 3] 启动Puppeteer浏览器，视口设置: ${width}x${height}`); // 日志：记录浏览器启动和视口配置。
            browser = await puppeteer.launch({
                headless: true, // 无头模式，提高性能
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu'
                ]
            });

            const page = await browser.newPage();

            // 设置视口为16:9比例
            await page.setViewport({
                width: width,
                height: height,
                deviceScaleFactor: 1
            });

            // 步骤 4: 报告页面访问进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '正在访问动画渲染页面',
                current: 20,
                total: 100,
                technicalDetail: `访问URL: ${renderPageUrl}`
            });

            // 步骤 5: 构建GET请求URL（支持中文参数）
            logger.info(`${execLogPrefix}[步骤 5] 构建GET请求URL，支持中文参数`); // 日志：记录URL构建。
            const queryParams = new URLSearchParams({
                chineseTitle,
                englishTitle,
                subtitle,
                countdown: countdown.toString(),
                duration: duration.toString(),
                isVideoMode: 'true'  // 关键：逐帧截图需要暂停动画
            });

            const renderUrl = `${renderPageUrl}?${queryParams}`;
            logger.info(`${execLogPrefix}[步骤 5.1] 构建的渲染URL: ${renderUrl}`); // 日志：记录完整URL。

            // 步骤 6: 设置字体加载监听（解决倒计时数字不显示问题）
            await page.evaluateOnNewDocument(() => {
                document.fonts.ready.then(() => {
                    console.log('字体加载完成');
                    document.body.setAttribute('data-fonts-ready', 'true');
                });
            });

            // 步骤 7: 访问带参数的动画渲染页面
            logger.info(`${execLogPrefix}[步骤 7] 访问带参数的动画渲染页面`); // 日志：记录页面访问操作。
            await page.goto(renderUrl, {
                waitUntil: 'networkidle0', // 等待网络完全空闲
                timeout: 30000 // 30秒超时
            });

            // 等待页面加载和参数处理完成
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 等待字体加载完成（解决倒计时数字不显示问题）
            await page.waitForFunction(() => document.body.getAttribute('data-fonts-ready') === 'true', { timeout: 10000 });

            // 等待视频模式初始化完成
            await page.waitForFunction(() => window.setVideoTime && window.getBBCStatus, { timeout: 10000 });

            // 步骤 8: 检查参数是否正确接收和视频模式状态
            logger.info(`${execLogPrefix}[步骤 8] 检查页面参数接收状态和视频模式`); // 日志：记录参数检查。
            const status = await page.evaluate(() => window.getBBCStatus());
            const hasVideoControl = await page.evaluate(() => typeof window.setVideoTime === 'function');

            logger.debug(`${execLogPrefix}[步骤 8.1] 页面状态: ${JSON.stringify(status)}`); // 日志：记录页面状态详情。
            logger.debug(`${execLogPrefix}[步骤 8.2] 视频控制函数: ${hasVideoControl ? '已就绪' : '未找到'}`); // 日志：记录视频控制状态。

            if (!hasVideoControl) {
                throw new Error('视频模式未正确初始化，setVideoTime函数不存在');
            }

            // 步骤 9: 验证倒计时元素状态（解决倒计时数字不显示问题）
            logger.info(`${execLogPrefix}[步骤 9] 验证倒计时元素状态`); // 日志：记录倒计时验证。
            const countdownVisible = await page.evaluate(() => {
                const element = document.querySelector('[class*="countdown"]');
                if (!element) return { error: '倒计时元素未找到' };

                const style = window.getComputedStyle(element);
                return {
                    opacity: style.opacity,
                    transform: style.transform,
                    color: style.color,
                    textContent: element.textContent,
                    display: style.display,
                    visibility: style.visibility
                };
            });
            logger.debug(`${execLogPrefix}[步骤 9.1] 倒计时元素状态: ${JSON.stringify(countdownVisible)}`); // 日志：记录倒计时元素详情。

            // 等待动画初始化
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 步骤 10: 准备输出路径（使用绝对路径，不创建目录）
            const outputDir = outputPath || 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output';

            // 验证输出目录存在（不创建）
            if (!fs.existsSync(outputDir)) {
                const errorMsg = `输出目录不存在: ${outputDir}`;
                logger.error(`${execLogPrefix}[步骤 10][ERROR] ${errorMsg}`); // 日志：记录输出目录不存在错误。
                throw new Error(errorMsg);
            }

            // 生成视频文件名（MP4格式）
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            videoFileName = `puppeteer_recording_${fileIdentifier}_${timestamp}.mp4`;
            recordedVideoPath = path.join(outputDir, videoFileName);

            // 创建临时帧目录
            const tempDir = path.join(outputDir, 'temp_frames');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // 步骤 11: 报告录制开始进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '开始逐帧截图录制',
                current: 30,
                total: 100,
                technicalDetail: `录制时长: ${duration}秒, 帧率: ${fps}fps, 输出: ${videoFileName}`
            });

            // 步骤 12: 逐帧截图录制
            logger.info(`${execLogPrefix}[步骤 12] 开始逐帧截图录制，时长: ${duration}秒，输出: ${recordedVideoPath}`); // 日志：记录录制开始和参数信息。

            const totalFrames = Math.ceil(duration * fps);

            logger.info(`${execLogPrefix}[步骤 12.1] 开始生成 ${totalFrames} 帧...`); // 日志：记录帧数信息。

            for (let frame = 0; frame < totalFrames; frame++) {
                const time = frame / fps;

                // 设置动画时间点 - 关键：控制暂停的动画到指定时间
                await page.evaluate((t) => {
                    if (window.setVideoTime) {
                        window.setVideoTime(t);
                    } else {
                        throw new Error('setVideoTime函数不存在');
                    }
                }, time);

                // 等待GSAP渲染稳定 - 确保动画渲染到指定时间点
                await new Promise(resolve => setTimeout(resolve, 10)); // 最小等待时间

                // 截图 - 使用文档推荐的优化设置
                const framePath = path.join(tempDir, `frame_${frame.toString().padStart(6, '0')}.png`);
                await page.screenshot({
                    path: framePath,
                    type: 'png',
                    fullPage: false,
                    clip: { x: 0, y: 0, width: width, height: height },
                    omitBackground: false
                });

                // 报告录制进度
                if (frame % 10 === 0) { // 每10帧报告一次进度
                    const progress = Math.round((frame / totalFrames) * 40) + 30; // 30-70%
                    this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                        detail: `录制进行中 (${frame}/${totalFrames} 帧)`,
                        current: progress,
                        total: 100,
                        technicalDetail: `当前帧: ${frame}, 时间点: ${time.toFixed(2)}s`
                    });
                }

                // 不等待，直接进入下一帧（最大速度）
            }

            logger.info(`${execLogPrefix}[步骤 12.2] 截图完成，共 ${totalFrames} 帧`); // 日志：记录截图完成。

            // 步骤 13: 关闭浏览器
            await browser.close();
            browser = null;

            // 步骤 14: 使用FFmpeg合成MP4视频
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '正在合成MP4视频',
                current: 70,
                total: 100,
                technicalDetail: `使用FFmpeg合成 ${totalFrames} 帧`
            });

            logger.info(`${execLogPrefix}[步骤 14] 开始FFmpeg合成MP4视频`); // 日志：记录FFmpeg合成开始。

            const ffmpeg = require('fluent-ffmpeg');
            await new Promise((resolve, reject) => {
                ffmpeg()
                    .input(path.join(tempDir, 'frame_%06d.png'))
                    .inputFPS(fps)
                    .videoCodec('libx264')
                    .outputOptions(['-pix_fmt yuv420p', '-crf 18'])
                    .output(recordedVideoPath)
                    .on('progress', (progress) => {
                        // FFmpeg进度监听
                        const percent = Math.round(progress.percent || 0);
                        logger.info(`${execLogPrefix}[步骤 14.进度] FFmpeg合成进度: ${percent}% (帧数: ${progress.frames || 0})`);
                    })
                    .on('end', () => {
                        logger.info(`${execLogPrefix}[步骤 14.1] FFmpeg合成完成: ${recordedVideoPath}`); // 日志：记录FFmpeg合成完成。
                        resolve();
                    })
                    .on('error', (err) => {
                        logger.error(`${execLogPrefix}[步骤 14.1][ERROR] FFmpeg合成失败: ${err.message}`); // 日志：记录FFmpeg合成失败。
                        reject(err);
                    })
                    .run();
            });

            // 步骤 15: 清理临时帧文件
            fs.rmSync(tempDir, { recursive: true, force: true });
            logger.info(`${execLogPrefix}[步骤 15] 临时帧文件已清理`); // 日志：记录临时文件清理。

            // 步骤 16: 获取录制生成的视频统计信息
            const stats = fs.statSync(recordedVideoPath);
            recordingStats = {
                fileSizeBytes: stats.size,
                fileSizeMB: (stats.size / 1024 / 1024).toFixed(2),
                duration: duration,
                resolution: `${width}x${height}`,
                fps: fps,
                frames: totalFrames,
                renderUrl: renderUrl
            };

            logger.info(`${execLogPrefix}[步骤 16] 录制统计: 文件大小=${recordingStats.fileSizeMB}MB, 帧数=${recordingStats.frames}`); // 日志：记录录制统计信息。

            // 步骤 17: 构建任务结果
            const taskResult = {
                recordingStatus: 'SUCCESS', // 录制最终状态
                recordedVideoPath: recordedVideoPath, // 录制生成的视频路径
                videoFileName: videoFileName, // 视频文件名
                recordingStats: recordingStats,
                renderUrl: renderUrl,
                chineseTitle: chineseTitle,
                englishTitle: englishTitle,
                subtitle: subtitle
            };

            // 步骤 18: 任务成功完成时，调用 this.complete()，并传入最终结果
            this.complete(taskResult);
            logger.info(`${execLogPrefix} Puppeteer录制任务执行成功，返回结果。`); // 日志：记录任务成功完成。

            // 步骤 19: 返回结构清晰、易于理解的结果对象
            return taskResult;

        } catch (error) {
            // 错误处理: 必须在 `execute` 方法内部使用 `try-catch` 块捕获并处理所有预期和非预期异常。
            logger.error(`${execLogPrefix}[ERROR] Puppeteer录制任务执行失败: ${error.message}`, error); // 日志：记录任务执行失败的详细错误信息。

            // 清理浏览器资源
            if (browser) {
                try {
                    await browser.close();
                    logger.info(`${execLogPrefix} 浏览器资源已清理。`); // 日志：记录浏览器资源清理操作。
                } catch (cleanupError) {
                    logger.error(`${execLogPrefix}[ERROR] 浏览器资源清理失败: ${cleanupError.message}`); // 日志：记录浏览器资源清理失败。
                }
            }

            // 使用标准化方法处理错误，自动设置失败状态、记录错误信息、调用进度回调。
            this.fail(error);
            // 重新抛出错误，确保上层调用者（如流水线）能够感知到任务失败，并进行相应处理。
            throw error;
        }
    }

    /**
     * @功能概述: 收集任务的详细上下文信息，用于前端展示和调试
     * @returns {object} 包含任务特定信息的详细上下文
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息
            const inputContext = {
                ...baseContext.inputContext,
                targetUrl: taskResult.targetUrl || 'N/A',
                recordingDuration: taskResult.recordingStats?.duration || 'N/A'
            };

            // 扩展输出上下文信息
            const outputContext = {
                ...baseContext.outputContext,
                recordingStatus: taskResult.recordingStatus || 'N/A',
                recordedVideoPath: taskResult.recordedVideoPath || 'N/A',
                videoFileName: taskResult.videoFileName || 'N/A'
            };

            // 扩展技术细节信息
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'PuppeteerRecorderTask',
                recordingMode: 'bbc_api_call',
                supportedFormats: ['mp4'],
                apiUrl: taskResult.apiUrl || 'N/A',
                recordingStats: taskResult.recordingStats || {}
            };

            // 任务特定的详细信息
            const taskSpecificDetails = {
                processingSteps: [
                    '参数验证',
                    'API参数准备',
                    'BBC渲染API调用',
                    '视频文件验证',
                    '结果处理'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '结果处理' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中'
            };

            // 合并所有上下文信息
            const extendedContext = {
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,
                inputContext,
                outputContext,
                technicalDetails,
                taskSpecificDetails,
                collectedAt: new Date().toISOString(),
                collectionMethod: 'PuppeteerRecorderTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集PuppeteerRecorderTask详细上下文信息`); // 日志：记录上下文信息收集成功。
            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`); // 日志：记录上下文信息收集失败。

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                taskProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'PuppeteerRecorderTask.collectDetailedContext (with error)'
            };
        }
    }
}

module.exports = PuppeteerRecorderTask;
logger.info(`${taskModuleLogPrefix}PuppeteerRecorderTask 类已导出。`); // 日志：确认任务类已成功导出。
