/**
 * 微模板JSON Schema验证定义
 * 用于验证微模板文件的数据结构是否符合标准格式
 */

const microTemplateSchema = {
    type: 'object',
    required: ['metadata', 'component', 'config'],
    properties: {
        metadata: {
            type: 'object',
            required: ['name', 'description'],
            properties: {
                name: {
                    type: 'string',
                    minLength: 1,
                    description: '微模板名称'
                },
                description: {
                    type: 'string',
                    minLength: 1,
                    description: '微模板描述'
                },
                version: {
                    type: 'string',
                    description: '微模板版本（可选）'
                },
                author: {
                    type: 'string',
                    description: '微模板作者（可选）'
                }
            },
            additionalProperties: false
        },
        component: {
            type: 'string',
            enum: ['Background', 'CenteredVideo'],
            description: '组件类型'
        },
        config: {
            type: 'object',
            description: '组件配置对象',
            // 根据component类型进行条件验证
            oneOf: [
                {
                    // Background组件配置
                    properties: {
                        type: {
                            type: 'string',
                            enum: ['image', 'color', 'gradient']
                        },
                        source: {
                            type: 'string',
                            description: '背景资源路径'
                        },
                        overlay: {
                            type: 'object',
                            properties: {
                                enabled: { type: 'boolean' },
                                color: { type: 'string' },
                                opacity: { type: 'number', minimum: 0, maximum: 1 }
                            },
                            additionalProperties: false
                        }
                    },
                    additionalProperties: true
                },
                {
                    // CenteredVideo组件配置
                    properties: {
                        isMuted: {
                            type: 'boolean',
                            description: '是否静音'
                        },
                        volume: {
                            type: 'number',
                            minimum: 0,
                            maximum: 1,
                            description: '音量大小'
                        }
                    },
                    additionalProperties: true
                }
            ]
        }
    },
    additionalProperties: false
};

module.exports = {
    microTemplateSchema
};