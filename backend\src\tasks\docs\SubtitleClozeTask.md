# SubtitleClozeTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **correctedSubtitleJsonArray** (Array): 校正后字幕JSON数组，来自TranscriptionCorrectionTask
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **clozeParams** (object): 挖空参数配置
  - **modelName** (string): LLM模型名称，默认'google/gemini-2.5-flash-lite-preview-06-17'
  - **temperature** (number): 创造性控制，默认0.3
  - **max_tokens** (number): 输出限制，默认4000
  - **clozeRatio** (number): 挖空比例，默认0.15（15%）
  - **minWordLength** (number): 最小词长，默认4
  - **excludeWords** (Array): 排除词列表

## 2. 输出上下文参数 (Output Context)

- **clozedSubtitleJsonArray** (Array): 挖空后的字幕JSON数组
- **clozedSubtitleJsonPath** (string): 挖空字幕JSON文件路径
- **clozedEnglishSrtContent** (string): 挖空英文SRT内容
- **clozedEnglishSrtPath** (string): 挖空英文SRT文件路径
- **clozeStats** (object): 挖空统计信息
  - **totalWords** (number): 总词数
  - **clozedWords** (number): 挖空词数
  - **clozeRatio** (number): 实际挖空比例
  - **modelUsed** (string): 使用的LLM模型
- **clozeStatus** (string): 挖空状态，成功时为'success'
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 输入格式（correctedSubtitleJsonArray）
```json
[
  {
    "id": "1",
    "start": 0,
    "end": 3.319999933242798,
    "text": " Good afternoon, there is another wildfire evacuation alert tonight.",
    "words": [
      {"text": "Good", "start": 0.1, "end": 0.5},
      {"text": "afternoon", "start": 0.5, "end": 1.0}
    ]
  }
]
```

### LLM提示词参数格式
```json
{
  "subtitle_json_to_cloze": "[{\"id\":\"1\",\"start\":0,\"end\":3.32,\"text\":\" Good afternoon, there is another wildfire evacuation alert tonight.\"}]",
  "overall_video_context": "完整视频字幕文本，用于理解整体语境..."
}
```

### LLM输出格式（挖空后）
```json
[
  {
    "id": "1",
    "start": 0,
    "end": 3.319999933242798,
    "text": " Good afternoon, there is () () () () tonight.",
    "words": ["another wildfire evacuation alert"]
  }
]
```

### 最终输出格式（重新合并words字段）
```json
[
  {
    "id": "1",
    "start": 0,
    "end": 3.319999933242798,
    "text": " Good afternoon, there is () () () () tonight.",
    "words": ["another wildfire evacuation alert"]
  }
]
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 挖空字幕JSON文件
- **.srt**: 挖空英文SRT文件

### 文件命名规则
- **挖空字幕**: `{videoIdentifier}_clozed_subtitle.json`
- **挖空SRT**: `{videoIdentifier}_clozed_english.srt`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保字符正确保存

## 5. 执行逻辑概述

字幕挖空任务负责创建填空练习版本的字幕，用于语言学习。任务首先验证输入数据，然后使用LLM智能选择适合挖空的词汇，考虑词汇难度、学习价值和上下文重要性。挖空策略包括最小词长过滤、排除常用词、控制挖空比例等。LLM会分析每个词汇的学习价值，优先选择名词、动词、形容词等实词进行挖空。挖空后的词汇使用()占位符替换，保持原有的时间戳信息。任务生成包含挖空信息的JSON文件和SRT文件，为语言学习提供互动练习材料。整个过程确保挖空的合理性和学习效果。
