# ConvertToAudioForCloudflareTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **uploadedVideoDirPath** (string): 上传视频目录路径
- **savePath** (string): 音频文件保存路径
- **originalVideoName** (string): 原始视频文件名
- **originalVideoPath** (string): 原始视频文件完整路径

### 可选参数（自动映射）
- **processedVideoPath** (string): 来自VideoClipAndCropTask的处理后视频路径（自动映射为originalVideoPath）
- **processedVideoFileName** (string): 处理后视频文件名（自动映射为originalVideoName）

## 2. 输出上下文参数 (Output Context)

- **audioFilePathInUploads** (string): 转换后音频文件的完整路径
- **audioFilePath** (string): 音频文件路径（兼容GenerateASSTask）
- **audioFileName** (string): 音频文件名
- **audioFileIdentifier** (string): 音频文件标识符（不含扩展名）
- **audioFileSize** (number): 音频文件大小（字节）
- **audioFileSizeMB** (string): 音频文件大小（MB，保留2位小数）
- **cloudflareOptimized** (boolean): 标识为Cloudflare优化版本（true）
- **finalAudioCodec** (string): 最终音频编码格式（'libmp3lame'）
- **finalAudioFormat** (string): 最终音频格式（'mp3'）
- **finalAudioBitrate** (string): 最终音频比特率（'32k'）
- **finalAudioFrequency** (string): 最终音频采样率（'16000'）
- **finalAudioChannels** (string): 最终音频声道（'1'）
- **compressionLevel** (string): 压缩级别（'extreme'）
- **optimizationTarget** (string): 优化目标（'cloudflare_workers_ai'）
- **conversionSuccess** (boolean): 转换成功标识
- **conversionMethod** (string): 转换方法（'ffmpeg_cloudflare_optimized'）
- **processingTime** (number): 处理时间（毫秒）
- **taskStatus** (string): 任务状态
- **taskResult** (string): 任务结果

## 3. 重要数据格式

### Cloudflare优化参数
```json
{
  "audioCodec": "libmp3lame",
  "audioBitrate": "32k",
  "audioFrequency": "16000",
  "audioChannels": "1",
  "compressionLevel": "extreme",
  "targetFileSize": "<2MB"
}
```

### 文件大小验证
- **目标**: 小于2MB（适合Cloudflare Workers AI）
- **压缩比**: 75-80% vs 原版
- **适用场景**: 1小时内视频的语音转录

## 4. 文件操作

### 保存的文件格式
- **.mp3**: 极度压缩的MP3音频文件

### 文件命名规则
- **模式**: `{cleanId}_{hash}_cloudflare_audio.mp3`
- **cleanId**: 清理后的videoIdentifier（替换非法字符，最多64字符）
- **hash**: SHA1哈希值前8位
- **示例**: `video123_a1b2c3d4_cloudflare_audio.mp3`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 自动创建目录（如果不存在）
- 支持VideoClipAndCropTask输出的自动映射

## 5. 执行逻辑概述

Cloudflare专用视频转音频任务专门针对Cloudflare Workers AI的文件大小限制进行优化，确保音频文件小于2MB。任务首先进行上下文映射，支持VideoClipAndCropTask输出的自动转换。然后验证输入参数并准备工作目录。使用FFmpeg进行极度压缩转换，采用32kbps比特率、16kHz采样率和单声道输出，在保持语音识别质量的同时最大化压缩比。转换过程提供详细的进度报告，包括文件大小验证和Cloudflare兼容性检查。最终生成适合Cloudflare Workers AI处理的高质量压缩音频文件。
