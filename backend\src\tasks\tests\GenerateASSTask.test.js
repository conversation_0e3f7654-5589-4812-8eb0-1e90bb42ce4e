/**
 * @文件名: GenerateASSTask.test.js
 * @功能概述: GenerateASSTask任务测试文件
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 * 
 * @测试目标:
 *   验证GenerateASSTask能够正确生成ASS字幕文件
 *   测试多种字幕模式的ASS生成功能
 *   验证CSS到ASS样式转换的正确性
 *   确保文件保存和路径处理的准确性
 * 
 * @测试数据:
 *   使用硬编码的测试数据模拟真实的任务上下文
 *   包含挖空字幕和双语字幕数组
 *   使用指定的保存路径和音频文件路径
 */

const GenerateASSTask = require('../GenerateASSTask');
const { TASK_STATUS } = require('../../constants/progress');
const logger = require('../../utils/logger');
const fs = require('fs').promises;
const path = require('path');
const assert = require('assert');

/**
 * @功能概述: GenerateASSTask硬编码数据测试
 * @测试场景: 使用预定义的测试数据验证ASS生成功能
 */
async function testGenerateASSTaskWithHardcodedData() {
    const testLogPrefix = '[GenerateASSTask.test.js][testGenerateASSTaskWithHardcodedData]';
    
    try {
        logger.info(`${testLogPrefix} ========== 开始GenerateASSTask硬编码数据测试 ==========`);
        
        // 步骤 1: 准备硬编码测试数据
        logger.info(`${testLogPrefix}[步骤 1] 准备硬编码测试数据...`);
        
        const videoIdentifier = 'test_ass_generation_0612';
        const savePath = 'C:/Users/<USER>/Desktop/codebase/express/backend/src/tasks/tests/test-output-ass';
        const audioFilePath = 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/test-data/test_123_extended_audio.mp3';
        
        // 硬编码挖空字幕数据
        const clozedSubtitleJsonArray = [
            {
                "id": 1,
                "start": 0.0,
                "end": 3.5,
                "text": "Residents say they were woken up by a () scene."
            },
            {
                "id": 2,
                "start": 3.5,
                "end": 7.0,
                "text": "A possible () driver wreaking havoc, damaging multiple vehicles."
            },
            {
                "id": 3,
                "start": 7.0,
                "end": 11.0,
                "text": "The () unit has been called in to investigate after a pedestrian was struck."
            },
            {
                "id": 4,
                "start": 11.0,
                "end": 15.0,
                "text": "Police are asking anyone with () to come forward."
            }
        ];
        
        // 硬编码增强双语字幕数据
        const enhancedBilingualSubtitleJsonArray = [
            {
                "id": 1,
                "start": 0.0,
                "end": 3.5,
                "text_english": "Residents say they were woken up by a movie-like scene.",
                "text_chinese": "居民们说，他们被一场电影般的场景惊醒。",
                "words_explanation": {
                    "residents": "居民",
                    "movie-like": "电影般的"
                }
            },
            {
                "id": 2,
                "start": 3.5,
                "end": 7.0,
                "text_english": "A possible impaired driver wreaking havoc, damaging multiple vehicles.",
                "text_chinese": "一个可能的酒驾司机造成了混乱，损坏了多辆车。",
                "words_explanation": {
                    "impaired": "受损的",
                    "wreaking": "造成",
                    "havoc": "混乱"
                }
            },
            {
                "id": 3,
                "start": 7.0,
                "end": 11.0,
                "text_english": "The homicide unit has been called in to investigate after a pedestrian was struck.",
                "text_chinese": "在一名行人被撞后，凶杀案调查组被召集进行调查。",
                "words_explanation": {
                    "homicide": "凶杀",
                    "investigate": "调查",
                    "pedestrian": "行人"
                }
            },
            {
                "id": 4,
                "start": 11.0,
                "end": 15.0,
                "text_english": "Police are asking anyone with information to come forward.",
                "text_chinese": "警方要求任何有信息的人站出来。",
                "words_explanation": {
                    "information": "信息",
                    "come forward": "站出来"
                }
            }
        ];
        
        // 构建测试上下文
        const testContext = {
            videoIdentifier: videoIdentifier,
            audioFilePath: audioFilePath,
            clozedSubtitleJsonArray: clozedSubtitleJsonArray,
            enhancedBilingualSubtitleJsonArray: enhancedBilingualSubtitleJsonArray,
            savePath: savePath
        };
        
        logger.info(`${testLogPrefix} 测试数据准备完成:`);
        logger.info(`${testLogPrefix} - videoIdentifier: ${videoIdentifier}`);
        logger.info(`${testLogPrefix} - savePath: ${savePath}`);
        logger.info(`${testLogPrefix} - 挖空字幕条目数: ${clozedSubtitleJsonArray.length}`);
        logger.info(`${testLogPrefix} - 双语字幕条目数: ${enhancedBilingualSubtitleJsonArray.length}`);
        
        // 步骤 2: 创建输出目录
        logger.info(`${testLogPrefix}[步骤 2] 创建输出目录...`);
        try {
            await fs.mkdir(savePath, { recursive: true });
            logger.info(`${testLogPrefix} 输出目录创建成功: ${savePath}`);
        } catch (error) {
            if (error.code !== 'EEXIST') {
                throw error;
            }
            logger.info(`${testLogPrefix} 输出目录已存在: ${savePath}`);
        }
        
        // 步骤 3: 创建GenerateASSTask实例
        logger.info(`${testLogPrefix}[步骤 3] 创建GenerateASSTask实例...`);
        const generateASSTask = new GenerateASSTask();
        
        // 进度回调函数
        const progressEvents = [];
        const progressCallback = (status, substatus, details) => {
            const progressEvent = {
                timestamp: new Date().toISOString(),
                taskName: generateASSTask.taskName,
                status: status,
                substatus: substatus,
                details: details
            };
            progressEvents.push(progressEvent);
            logger.info(`${testLogPrefix}[进度回调] ${status} - ${substatus}: ${details?.detail || 'N/A'}`);
        };
        
        // 步骤 4: 执行ASS生成任务
        logger.info(`${testLogPrefix}[步骤 4] 执行ASS生成任务...`);
        const startTime = Date.now();
        
        const result = await generateASSTask.execute(testContext, progressCallback);
        
        const endTime = Date.now();
        const executionTime = endTime - startTime;
        
        logger.info(`${testLogPrefix} ASS生成任务执行完成，耗时: ${executionTime}ms`);
        
        // 步骤 5: 验证任务结果
        logger.info(`${testLogPrefix}[步骤 5] 验证任务结果...`);
        
        // 验证任务状态
        assert.strictEqual(generateASSTask.status, TASK_STATUS.COMPLETED, 'GenerateASSTask状态应为COMPLETED');
        assert.strictEqual(result.taskStatus, 'completed', '任务结果状态应为completed');
        assert.strictEqual(result.taskResult, 'success', '任务结果应为success');
        
        // 验证返回的字段
        assert(result.assFilePath, '结果应包含assFilePath字段');
        assert(result.videoConfig, '结果应包含videoConfig字段');
        assert(result.audioDuration, '结果应包含audioDuration字段');
        assert(result.processedSubtitles, '结果应包含processedSubtitles字段');

        // 验证ASS文件路径
        assert(result.assFilePath, '应包含ASS文件路径');
        
        logger.info(`${testLogPrefix} 任务结果验证通过`);
        
        // 步骤 6: 验证生成的ASS文件
        logger.info(`${testLogPrefix}[步骤 6] 验证生成的ASS文件...`);

        // 验证ASS文件
        const assFilePath = result.assFilePath;
        const assFileExists = await fs.access(assFilePath).then(() => true).catch(() => false);
        assert(assFileExists, `ASS文件应存在: ${assFilePath}`);

        const assContent = await fs.readFile(assFilePath, 'utf8');
        assert(assContent.includes('[Script Info]'), 'ASS文件应包含Script Info部分');
        assert(assContent.includes('[V4+ Styles]'), 'ASS文件应包含V4+ Styles部分');
        assert(assContent.includes('[Events]'), 'ASS文件应包含Events部分');
        assert(assContent.includes('()'), 'ASS文件应包含挖空占位符');
        assert(assContent.includes('居民'), 'ASS文件应包含中文内容');
        assert(assContent.includes('坚持30天'), 'ASS文件应包含视频标题');
        assert(assContent.includes('第一遍 盲听'), 'ASS文件应包含单元引导字幕');
        
        logger.info(`${testLogPrefix} ASS文件验证通过`);
        
        // 步骤 7: 记录测试结果
        logger.info(`${testLogPrefix}[步骤 7] 记录测试结果...`);
        
        logger.info(`${testLogPrefix} ========== GenerateASSTask硬编码数据测试成功完成 ==========`);
        logger.info(`${testLogPrefix} 测试总结:`);
        logger.info(`${testLogPrefix} - 执行时间: ${executionTime}ms`);
        logger.info(`${testLogPrefix} - 进度回调次数: ${progressEvents.length}`);
        logger.info(`${testLogPrefix} - ASS文件: ${assFilePath}`);
        logger.info(`${testLogPrefix} - ASS文件大小: ${assContent.length}字符`);

        return {
            success: true,
            executionTime: executionTime,
            result: result,
            progressEvents: progressEvents,
            assFiles: {
                assFilePath: assFilePath,
                assFileSize: assContent.length
            }
        };
        
    } catch (error) {
        logger.error(`${testLogPrefix} 测试执行失败: ${error.message}`, error);
        throw error;
    }
}

// 主测试执行函数
async function runTests() {
    const mainLogPrefix = '[GenerateASSTask.test.js][runTests]';
    
    try {
        logger.info(`${mainLogPrefix} 开始执行GenerateASSTask测试套件`);
        
        const testResult = await testGenerateASSTaskWithHardcodedData();
        
        logger.info(`${mainLogPrefix} 所有测试执行完成`);
        logger.info(`${mainLogPrefix} 测试结果: ${testResult.success ? '成功' : '失败'}`);
        
        return testResult;
        
    } catch (error) {
        logger.error(`${mainLogPrefix} 测试套件执行失败: ${error.message}`, error);
        throw error;
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTests()
        .then(result => {
            console.log('✅ GenerateASSTask测试成功完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ GenerateASSTask测试失败:', error.message);
            process.exit(1);
        });
}

module.exports = {
    testGenerateASSTaskWithHardcodedData,
    runTests
};
