/**
 * @功能概述: BilingualSubtitleMergeTask 真实LLM调用测试
 * @测试目标: 验证双语字幕合并增强任务的完整功能，包括LLM词汇解释生成
 * @测试数据: 使用真实的英文、中文和挖空字幕文件
 * @测试重点: LLM调用、词汇解释生成、JSON格式校验、文件保存
 */

const BilingualSubtitleMergeTask = require('../BilingualSubtitleMergeTask');
const fs = require('fs');
const path = require('path');

// 测试配置 - 使用硬编码数据
const TEST_CONFIG = {
    // 硬编码的测试数据文件路径
    ENGLISH_SUBTITLE_FILE: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/test-data-0611/test_0611_en_corrected.json',
    CHINESE_SUBTITLE_FILE: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/test-data-0611/test_0611_cn_corrected.json',
    CLOZED_SUBTITLE_FILE: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/test-data-0611/test_0611_en_clozed_subtitle.json',
    CORRECTED_FULL_TEXT_FILE: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/correctedFullText.txt',

    // 硬编码的输出配置
    VIDEO_IDENTIFIER: 'test_611',
    SAVE_PATH: 'C:/Users/<USER>/Desktop/codebase/express/backend/src/tasks/tests/test-output-0611',

    // 测试控制
    ENABLE_HIGHEST_LOGGING: true,
    TIMEOUT_MS: 180000 // 3分钟超时
};

/**
 * @功能概述: 加载测试数据文件
 * @param {string} filePath - 文件路径
 * @param {string} description - 文件描述
 * @returns {Array} 解析后的JSON数组
 */
function loadTestData(filePath, description) {
    try {
        console.log(`📁 [数据加载] 正在加载${description}: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        const fileContent = fs.readFileSync(filePath, 'utf8');
        const jsonData = JSON.parse(fileContent);

        if (!Array.isArray(jsonData)) {
            throw new Error(`${description}必须是数组格式`);
        }

        console.log(`✅ [数据加载] ${description}加载成功，包含 ${jsonData.length} 个条目`);
        return jsonData;

    } catch (error) {
        console.error(`❌ [数据加载] 加载${description}失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 加载文本文件
 * @param {string} filePath - 文件路径
 * @param {string} description - 文件描述
 * @returns {string} 文件内容
 */
function loadTextFile(filePath, description) {
    try {
        console.log(`📁 [文本加载] 正在加载${description}: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        const fileContent = fs.readFileSync(filePath, 'utf8').trim();

        console.log(`✅ [文本加载] ${description}加载成功，内容长度: ${fileContent.length} 字符`);
        return fileContent;

    } catch (error) {
        console.error(`❌ [文本加载] 加载${description}失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 验证增强双语字幕数据结构
 * @param {Array} enhancedSubtitles - 增强双语字幕数组
 * @param {string} testPrefix - 测试前缀
 */
function validateEnhancedSubtitleStructure(enhancedSubtitles, testPrefix) {
    console.log(`🔍 [结构验证] ${testPrefix} 开始验证增强双语字幕结构`);
    
    const requiredFields = ['id', 'start', 'end', 'text_english', 'text_chinese', 'words_explanation'];
    
    for (let i = 0; i < Math.min(enhancedSubtitles.length, 5); i++) {
        const subtitle = enhancedSubtitles[i];
        
        // 验证必需字段
        for (const field of requiredFields) {
            if (!(field in subtitle)) {
                throw new Error(`第${i + 1}个条目缺少必需字段: ${field}`);
            }
        }
        
        // 验证字段类型
        if (typeof subtitle.id !== 'string') {
            throw new Error(`第${i + 1}个条目的id字段必须是字符串类型`);
        }
        
        if (typeof subtitle.start !== 'number' || typeof subtitle.end !== 'number') {
            throw new Error(`第${i + 1}个条目的start和end字段必须是数字类型`);
        }
        
        if (typeof subtitle.text_english !== 'string' || typeof subtitle.text_chinese !== 'string') {
            throw new Error(`第${i + 1}个条目的text_english和text_chinese字段必须是字符串类型`);
        }
        
        if (typeof subtitle.words_explanation !== 'object' || subtitle.words_explanation === null) {
            throw new Error(`第${i + 1}个条目的words_explanation字段必须是对象类型`);
        }
        
        console.log(`✅ [结构验证] 第${i + 1}个条目结构验证通过`);
        console.log(`   - ID: ${subtitle.id}`);
        console.log(`   - 时间: ${subtitle.start}s - ${subtitle.end}s`);
        console.log(`   - 英文: ${subtitle.text_english.substring(0, 50)}...`);
        console.log(`   - 中文: ${subtitle.text_chinese.substring(0, 30)}...`);
        console.log(`   - 词汇解释数量: ${Object.keys(subtitle.words_explanation).length}`);
        
        // 显示词汇解释示例
        const explanationEntries = Object.entries(subtitle.words_explanation);
        if (explanationEntries.length > 0) {
            console.log(`   - 词汇解释示例: ${explanationEntries.slice(0, 2).map(([word, explanation]) => `"${word}": "${explanation}"`).join(', ')}`);
        }
    }
    
    console.log(`✅ [结构验证] ${testPrefix} 增强双语字幕结构验证完成`);
}

/**
 * @功能概述: 主测试函数
 */
async function runBilingualSubtitleMergeTaskTest() {
    const testStartTime = Date.now();
    const testPrefix = '[BilingualSubtitleMergeTask真实LLM测试]';
    
    try {
        console.log(`🚀 ${testPrefix} 开始执行双语字幕合并增强任务测试`);
        console.log(`📊 [测试配置] 视频标识符: ${TEST_CONFIG.VIDEO_IDENTIFIER}`);
        console.log(`📊 [测试配置] 保存路径: ${TEST_CONFIG.SAVE_PATH}`);
        console.log(`📊 [测试配置] 超时时间: ${TEST_CONFIG.TIMEOUT_MS}ms`);
        
        // 步骤1: 加载测试数据
        console.log(`\n📁 [步骤1/6] 加载测试数据`);
        const englishSubtitles = loadTestData(TEST_CONFIG.ENGLISH_SUBTITLE_FILE, '英文字幕文件');
        const chineseSubtitles = loadTestData(TEST_CONFIG.CHINESE_SUBTITLE_FILE, '中文字幕文件');
        const clozedSubtitles = loadTestData(TEST_CONFIG.CLOZED_SUBTITLE_FILE, '挖空字幕文件');
        const correctedFullText = loadTextFile(TEST_CONFIG.CORRECTED_FULL_TEXT_FILE, '完整上下文文本文件');

        console.log(`📊 [数据统计] 英文字幕: ${englishSubtitles.length} 条`);
        console.log(`📊 [数据统计] 中文字幕: ${chineseSubtitles.length} 条`);
        console.log(`📊 [数据统计] 挖空字幕: ${clozedSubtitles.length} 条`);
        console.log(`📊 [数据统计] 完整上下文文本: ${correctedFullText.length} 字符`);
        
        // 步骤2: 创建任务实例
        console.log(`\n🏗️ [步骤2/6] 创建BilingualSubtitleMergeTask实例`);
        const task = new BilingualSubtitleMergeTask();
        console.log(`✅ [任务创建] BilingualSubtitleMergeTask实例创建成功`);
        
        // 步骤3: 准备上下文
        console.log(`\n📋 [步骤3/6] 准备任务上下文`);
        const context = {
            videoIdentifier: TEST_CONFIG.VIDEO_IDENTIFIER,
            simplifiedSubtitleJsonArray: englishSubtitles,
            translatedSubtitleJsonArray: chineseSubtitles,
            clozedSubtitleJsonArray: clozedSubtitles,
            savePath: TEST_CONFIG.SAVE_PATH,
            correctedFullText: correctedFullText // 使用加载的真实上下文文本
        };

        console.log(`✅ [上下文准备] 任务上下文准备完成`);
        
        // 步骤4: 执行任务（包含LLM调用）
        console.log(`\n🤖 [步骤4/6] 执行双语字幕合并增强任务（包含LLM调用）`);
        const taskStartTime = Date.now();
        
        const progressCallback = (status, substatus, progress) => {
            console.log(`📈 [任务进度] ${status} - ${substatus || 'N/A'} - ${progress?.detail || 'N/A'} (${progress?.current || 0}/${progress?.total || 100})`);
        };
        
        const result = await task.execute(context, progressCallback);
        const taskDuration = Date.now() - taskStartTime;
        
        console.log(`✅ [任务执行] 双语字幕合并增强任务执行完成，耗时: ${taskDuration}ms`);
        
        // 步骤5: 验证结果
        console.log(`\n🔍 [步骤5/6] 验证任务结果`);
        
        if (!result || result.bilingualSubtitleMergeTaskStatus !== 'success') {
            throw new Error(`任务执行失败: ${result ? JSON.stringify(result) : '无结果'}`);
        }
        
        if (!result.enhancedBilingualSubtitleJsonArray || !Array.isArray(result.enhancedBilingualSubtitleJsonArray)) {
            throw new Error('任务结果中缺少有效的enhancedBilingualSubtitleJsonArray');
        }
        
        if (!result.enhancedBilingualSubtitleJsonPath || typeof result.enhancedBilingualSubtitleJsonPath !== 'string') {
            throw new Error('任务结果中缺少有效的enhancedBilingualSubtitleJsonPath');
        }
        
        console.log(`✅ [结果验证] 任务结果基础验证通过`);
        console.log(`📊 [结果统计] 生成增强双语字幕条目数: ${result.enhancedBilingualSubtitleJsonArray.length}`);
        console.log(`📁 [结果文件] 保存路径: ${result.enhancedBilingualSubtitleJsonPath}`);
        
        // 步骤6: 验证数据结构和文件
        console.log(`\n📋 [步骤6/6] 验证数据结构和文件`);
        
        // 验证数据结构
        validateEnhancedSubtitleStructure(result.enhancedBilingualSubtitleJsonArray, testPrefix);
        
        // 验证文件是否存在
        if (!fs.existsSync(result.enhancedBilingualSubtitleJsonPath)) {
            throw new Error(`生成的文件不存在: ${result.enhancedBilingualSubtitleJsonPath}`);
        }
        
        // 验证文件内容
        const savedContent = fs.readFileSync(result.enhancedBilingualSubtitleJsonPath, 'utf8');
        const savedData = JSON.parse(savedContent);
        
        if (!Array.isArray(savedData) || savedData.length !== result.enhancedBilingualSubtitleJsonArray.length) {
            throw new Error('保存的文件内容与任务结果不一致');
        }
        
        console.log(`✅ [文件验证] 生成的文件验证通过`);
        
        // 测试完成
        const totalDuration = Date.now() - testStartTime;
        console.log(`\n🎉 ${testPrefix} 测试完成！`);
        console.log(`📊 [测试统计] 总耗时: ${totalDuration}ms`);
        console.log(`📊 [测试统计] 任务耗时: ${taskDuration}ms`);
        console.log(`📊 [测试统计] LLM处理条目数: ${result.enhancedBilingualSubtitleJsonArray.length}`);
        console.log(`📊 [测试统计] 生成文件: ${result.enhancedBilingualSubtitleJsonPath}`);
        
        return {
            success: true,
            result: result,
            duration: totalDuration,
            taskDuration: taskDuration
        };
        
    } catch (error) {
        const totalDuration = Date.now() - testStartTime;
        console.error(`❌ ${testPrefix} 测试失败: ${error.message}`);
        console.error(`📊 [测试统计] 失败耗时: ${totalDuration}ms`);
        throw error;
    }
}

// 执行测试
if (require.main === module) {
    runBilingualSubtitleMergeTaskTest()
        .then(result => {
            console.log(`\n✅ 测试成功完成！`);
            process.exit(0);
        })
        .catch(error => {
            console.error(`\n❌ 测试失败: ${error.message}`);
            process.exit(1);
        });
}

module.exports = { runBilingualSubtitleMergeTaskTest };
