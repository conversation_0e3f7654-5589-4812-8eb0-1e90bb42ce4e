# &lt;Folder&gt;

## 概述

`<Folder>` 组件用于在 Remotion Studio 的侧边栏中对 [`<Composition>`](./Composition.md) 进行可视化分类。当您有许多组合时，这有助于组织和管理项目结构。

**版本要求**: v3.0.1+

## 语法

```typescript
import { Folder, Composition } from "remotion";

<Folder name="文件夹名称">
  <Composition {...compositionProps} />
</Folder>
```

## 核心属性

### name (必需)
- **类型**: `string`
- **描述**: 文件夹的名称
- **限制**: 只能包含 `a-z`、`A-Z`、`0-9` 和 `-` 字符

## 基础用法

### 1. 基础文件夹组织

```typescript
import { Composition, Folder } from "remotion";
import { IntroComponent, MainComponent, OutroComponent } from "./components";

export const Video = () => {
  return (
    <>
      <Folder name="Intro-Sections">
        <Composition
          id="intro-animation"
          durationInFrames={90}
          fps={30}
          width={1920}
          height={1080}
          component={IntroComponent}
        />
        <Composition
          id="intro-logo"
          durationInFrames={60}
          fps={30}
          width={1920}
          height={1080}
          component={IntroComponent}
        />
      </Folder>
      
      <Folder name="Main-Content">
        <Composition
          id="main-video"
          durationInFrames={600}
          fps={30}
          width={1920}
          height={1080}
          component={MainComponent}
        />
      </Folder>
      
      <Folder name="Outro-Sections">
        <Composition
          id="outro-credits"
          durationInFrames={120}
          fps={30}
          width={1920}
          height={1080}
          component={OutroComponent}
        />
      </Folder>
    </>
  );
};
```

### 2. 嵌套文件夹结构

```typescript
import { Composition, Folder, Still } from "remotion";

export const ProjectStructure = () => {
  return (
    <>
      <Folder name="Social-Media">
        <Folder name="Instagram">
          <Composition
            id="instagram-story"
            durationInFrames={450}
            fps={30}
            width={1080}
            height={1920}
            component={InstagramStory}
          />
          <Still
            id="instagram-post"
            width={1080}
            height={1080}
            component={InstagramPost}
          />
        </Folder>
        
        <Folder name="YouTube">
          <Composition
            id="youtube-intro"
            durationInFrames={150}
            fps={30}
            width={1920}
            height={1080}
            component={YouTubeIntro}
          />
          <Still
            id="youtube-thumbnail"
            width={1280}
            height={720}
            component={YouTubeThumbnail}
          />
        </Folder>
        
        <Folder name="TikTok">
          <Composition
            id="tiktok-video"
            durationInFrames={900}
            fps={30}
            width={1080}
            height={1920}
            component={TikTokVideo}
          />
        </Folder>
      </Folder>
      
      <Folder name="Marketing">
        <Folder name="Advertisements">
          <Composition
            id="product-ad"
            durationInFrames={300}
            fps={30}
            width={1920}
            height={1080}
            component={ProductAd}
          />
        </Folder>
        
        <Folder name="Presentations">
          <Still
            id="presentation-slide"
            width={1920}
            height={1080}
            component={PresentationSlide}
          />
        </Folder>
      </Folder>
    </>
  );
};
```

## 实际应用场景

### 1. 按项目阶段组织

```typescript
import { Composition, Folder } from "remotion";

export const ProductionPipeline = () => {
  return (
    <>
      <Folder name="Pre-Production">
        <Composition
          id="storyboard-preview"
          durationInFrames={300}
          fps={30}
          width={1920}
          height={1080}
          component={StoryboardPreview}
        />
        <Still
          id="mood-board"
          width={1920}
          height={1080}
          component={MoodBoard}
        />
      </Folder>
      
      <Folder name="Production">
        <Composition
          id="scene-01"
          durationInFrames={450}
          fps={30}
          width={1920}
          height={1080}
          component={Scene01}
        />
        <Composition
          id="scene-02"
          durationInFrames={380}
          fps={30}
          width={1920}
          height={1080}
          component={Scene02}
        />
        <Composition
          id="scene-03"
          durationInFrames={520}
          fps={30}
          width={1920}
          height={1080}
          component={Scene03}
        />
      </Folder>
      
      <Folder name="Post-Production">
        <Composition
          id="final-edit"
          durationInFrames={1350}
          fps={30}
          width={1920}
          height={1080}
          component={FinalEdit}
        />
        <Composition
          id="color-graded"
          durationInFrames={1350}
          fps={30}
          width={1920}
          height={1080}
          component={ColorGraded}
        />
      </Folder>
      
      <Folder name="Deliverables">
        <Composition
          id="final-video-16-9"
          durationInFrames={1350}
          fps={30}
          width={1920}
          height={1080}
          component={FinalVideo}
        />
        <Composition
          id="final-video-9-16"
          durationInFrames={1350}
          fps={30}
          width={1080}
          height={1920}
          component={FinalVideoVertical}
        />
        <Composition
          id="final-video-1-1"
          durationInFrames={1350}
          fps={30}
          width={1080}
          height={1080}
          component={FinalVideoSquare}
        />
      </Folder>
    </>
  );
};
```

### 2. 按内容类型组织

```typescript
import { Composition, Folder, Still } from "remotion";

export const ContentLibrary = () => {
  return (
    <>
      <Folder name="Animations">
        <Folder name="Logo-Animations">
          <Composition
            id="logo-reveal"
            durationInFrames={120}
            fps={30}
            width={1920}
            height={1080}
            component={LogoReveal}
          />
          <Composition
            id="logo-spin"
            durationInFrames={90}
            fps={30}
            width={1920}
            height={1080}
            component={LogoSpin}
          />
        </Folder>
        
        <Folder name="Text-Animations">
          <Composition
            id="typewriter-effect"
            durationInFrames={180}
            fps={30}
            width={1920}
            height={1080}
            component={TypewriterEffect}
          />
          <Composition
            id="fade-in-text"
            durationInFrames={60}
            fps={30}
            width={1920}
            height={1080}
            component={FadeInText}
          />
        </Folder>
        
        <Folder name="Transition-Effects">
          <Composition
            id="slide-transition"
            durationInFrames={30}
            fps={30}
            width={1920}
            height={1080}
            component={SlideTransition}
          />
          <Composition
            id="fade-transition"
            durationInFrames={30}
            fps={30}
            width={1920}
            height={1080}
            component={FadeTransition}
          />
        </Folder>
      </Folder>
      
      <Folder name="Static-Graphics">
        <Folder name="Backgrounds">
          <Still
            id="gradient-background"
            width={1920}
            height={1080}
            component={GradientBackground}
          />
          <Still
            id="pattern-background"
            width={1920}
            height={1080}
            component={PatternBackground}
          />
        </Folder>
        
        <Folder name="Overlays">
          <Still
            id="frame-overlay"
            width={1920}
            height={1080}
            component={FrameOverlay}
          />
          <Still
            id="watermark"
            width={200}
            height={200}
            component={Watermark}
          />
        </Folder>
      </Folder>
      
      <Folder name="Templates">
        <Folder name="Lower-Thirds">
          <Composition
            id="lower-third-modern"
            durationInFrames={150}
            fps={30}
            width={1920}
            height={1080}
            component={LowerThirdModern}
          />
          <Composition
            id="lower-third-classic"
            durationInFrames={150}
            fps={30}
            width={1920}
            height={1080}
            component={LowerThirdClassic}
          />
        </Folder>
        
        <Folder name="Title-Cards">
          <Still
            id="title-card-bold"
            width={1920}
            height={1080}
            component={TitleCardBold}
          />
          <Still
            id="title-card-elegant"
            width={1920}
            height={1080}
            component={TitleCardElegant}
          />
        </Folder>
      </Folder>
    </>
  );
};
```

### 3. 按客户或项目组织

```typescript
import { Composition, Folder } from "remotion";

export const ClientProjects = () => {
  return (
    <>
      <Folder name="Client-A">
        <Folder name="Brand-Video">
          <Composition
            id="client-a-brand-intro"
            durationInFrames={300}
            fps={30}
            width={1920}
            height={1080}
            component={ClientABrandIntro}
          />
          <Composition
            id="client-a-brand-outro"
            durationInFrames={150}
            fps={30}
            width={1920}
            height={1080}
            component={ClientABrandOutro}
          />
        </Folder>
        
        <Folder name="Product-Showcase">
          <Composition
            id="client-a-product-demo"
            durationInFrames={600}
            fps={30}
            width={1920}
            height={1080}
            component={ClientAProductDemo}
          />
        </Folder>
      </Folder>
      
      <Folder name="Client-B">
        <Folder name="Educational-Series">
          <Composition
            id="client-b-tutorial-01"
            durationInFrames={900}
            fps={30}
            width={1920}
            height={1080}
            component={ClientBTutorial01}
          />
          <Composition
            id="client-b-tutorial-02"
            durationInFrames={750}
            fps={30}
            width={1920}
            height={1080}
            component={ClientBTutorial02}
          />
        </Folder>
      </Folder>
      
      <Folder name="Internal-Projects">
        <Folder name="Company-Updates">
          <Composition
            id="quarterly-update"
            durationInFrames={450}
            fps={30}
            width={1920}
            height={1080}
            component={QuarterlyUpdate}
          />
        </Folder>
        
        <Folder name="Training-Materials">
          <Composition
            id="onboarding-video"
            durationInFrames={1200}
            fps={30}
            width={1920}
            height={1080}
            component={OnboardingVideo}
          />
        </Folder>
      </Folder>
    </>
  );
};
```

### 4. 按格式和平台组织

```typescript
import { Composition, Folder, Still } from "remotion";

export const MultiFormatContent = () => {
  return (
    <>
      <Folder name="Horizontal-16-9">
        <Folder name="YouTube">
          <Composition
            id="youtube-main-video"
            durationInFrames={1800}
            fps={30}
            width={1920}
            height={1080}
            component={YouTubeMainVideo}
          />
          <Still
            id="youtube-thumbnail"
            width={1280}
            height={720}
            component={YouTubeThumbnail}
          />
        </Folder>
        
        <Folder name="Website-Banners">
          <Still
            id="hero-banner"
            width={1920}
            height={1080}
            component={HeroBanner}
          />
          <Still
            id="section-banner"
            width={1200}
            height={675}
            component={SectionBanner}
          />
        </Folder>
      </Folder>
      
      <Folder name="Vertical-9-16">
        <Folder name="Instagram-Stories">
          <Composition
            id="story-template-1"
            durationInFrames={450}
            fps={30}
            width={1080}
            height={1920}
            component={StoryTemplate1}
          />
          <Composition
            id="story-template-2"
            durationInFrames={450}
            fps={30}
            width={1080}
            height={1920}
            component={StoryTemplate2}
          />
        </Folder>
        
        <Folder name="TikTok-Content">
          <Composition
            id="tiktok-trend-video"
            durationInFrames={900}
            fps={30}
            width={1080}
            height={1920}
            component={TikTokTrendVideo}
          />
        </Folder>
      </Folder>
      
      <Folder name="Square-1-1">
        <Folder name="Instagram-Posts">
          <Still
            id="instagram-carousel-1"
            width={1080}
            height={1080}
            component={InstagramCarousel1}
          />
          <Still
            id="instagram-carousel-2"
            width={1080}
            height={1080}
            component={InstagramCarousel2}
          />
        </Folder>
        
        <Folder name="Profile-Graphics">
          <Still
            id="profile-avatar"
            width={400}
            height={400}
            component={ProfileAvatar}
          />
        </Folder>
      </Folder>
    </>
  );
};
```

### 5. 开发和测试组织

```typescript
import { Composition, Folder } from "remotion";

export const DevelopmentStructure = () => {
  return (
    <>
      <Folder name="Development">
        <Folder name="Component-Tests">
          <Composition
            id="button-animation-test"
            durationInFrames={120}
            fps={30}
            width={800}
            height={600}
            component={ButtonAnimationTest}
          />
          <Composition
            id="text-effect-test"
            durationInFrames={180}
            fps={30}
            width={800}
            height={600}
            component={TextEffectTest}
          />
        </Folder>
        
        <Folder name="Layout-Tests">
          <Composition
            id="responsive-layout-test"
            durationInFrames={300}
            fps={30}
            width={1920}
            height={1080}
            component={ResponsiveLayoutTest}
          />
        </Folder>
        
        <Folder name="Performance-Tests">
          <Composition
            id="heavy-animation-test"
            durationInFrames={600}
            fps={30}
            width={1920}
            height={1080}
            component={HeavyAnimationTest}
          />
        </Folder>
      </Folder>
      
      <Folder name="Staging">
        <Composition
          id="staging-final-video"
          durationInFrames={1800}
          fps={30}
          width={1920}
          height={1080}
          component={StagingFinalVideo}
        />
      </Folder>
      
      <Folder name="Production">
        <Composition
          id="production-final-video"
          durationInFrames={1800}
          fps={30}
          width={1920}
          height={1080}
          component={ProductionFinalVideo}
        />
      </Folder>
    </>
  );
};
```

## 文件夹行为特性

### 1. 仅视觉效果
- 文件夹只在 Remotion Studio 的侧边栏中改变视觉显示
- 不影响渲染行为或组合功能

### 2. 命名规则
- 文件夹名称只能包含 `a-z`、`A-Z`、`0-9` 和 `-` 字符
- 不支持空格或特殊字符

### 3. 嵌套支持
- 支持无限层级的文件夹嵌套
- 有助于创建复杂的项目结构

## 最佳实践

1. **逻辑分组**: 根据项目需求选择合适的分组策略
2. **命名规范**: 使用清晰、一致的命名约定
3. **层级控制**: 避免过深的嵌套层级，保持结构清晰
4. **功能分离**: 将不同功能的组合分到不同文件夹
5. **版本管理**: 使用文件夹区分不同版本或阶段

## 常见组织策略

- **按项目阶段**: Pre-Production, Production, Post-Production
- **按内容类型**: Animations, Static-Graphics, Templates
- **按平台格式**: YouTube, Instagram, TikTok
- **按客户项目**: Client-A, Client-B, Internal
- **按开发环境**: Development, Staging, Production

## 相关 API

- [`<Composition>`](./Composition.md) - 视频组合组件
- [`<Still>`](./Still.md) - 静态图像组件
- [`registerRoot()`](./registerRoot.md) - 根组件注册

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/Folder.tsx)
