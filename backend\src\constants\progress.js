/**
 * 进度监控统一常量和类型定义
 * 集中管理所有进度相关的枚举、状态和数据结构
 * 
 * @功能概述: 为整个项目提供标准化的进度监控基础设施
 * @数据流向: TaskBase → PipelineBase → Controller → Frontend (SSE)
 * @设计原则: 统一数据结构、层层上传、异常隔离、历史追踪
 */

// ==================== 基础状态枚举定义 ====================

/**
 * 任务状态枚举
 * 定义任务生命周期中的主要状态
 */
const TASK_STATUS = {
  PENDING: 'pending',     // 任务等待执行
  STARTED: 'started',     // 任务开始执行
  RUNNING: 'running',     // 任务正在运行
  COMPLETED: 'completed', // 任务成功完成
  FAILED: 'failed',       // 任务执行失败
  CANCELLED: 'cancelled'  // 任务被取消
};

/**
 * 任务子状态枚举（细粒度状态）
 * 为不同类型的任务提供更详细的状态追踪
 */
const TASK_SUBSTATUS = {
  // === 通用子状态 ===
  INITIALIZING: 'initializing', // 初始化阶段
  PROCESSING: 'processing',     // 处理阶段
  FINALIZING: 'finalizing',     // 完成阶段
  
  // === LLM相关子状态（高细粒度追踪）===
  LLM_PREPARING: 'llm_preparing_request',   // 准备LLM请求
  LLM_SENDING: 'llm_sending_request',       // 发送LLM请求
  LLM_WAITING: 'llm_waiting_response',      // 等待LLM响应
  LLM_RECEIVING: 'llm_receiving_response',  // 接收LLM响应
  LLM_PARSING: 'llm_parsing_response',      // 解析LLM响应
  LLM_VALIDATING: 'llm_validating_response', // 验证LLM响应
  
  // === FFmpeg相关子状态 ===
  FFMPEG_STARTING: 'ffmpeg_starting',     // FFmpeg启动
  FFMPEG_PROCESSING: 'ffmpeg_processing', // FFmpeg处理中
  FFMPEG_COMPLETED: 'ffmpeg_completed',   // FFmpeg完成
  
  // === API调用相关子状态 ===
  API_CONNECTING: 'api_connecting', // API连接中
  API_UPLOADING: 'api_uploading',   // API上传中
  API_PROCESSING: 'api_processing', // API处理中
  
  // === 失败状态子状态（错误分类）===
  FAILED_UNKNOWN: 'failed_unknown',           // 未知错误
  FAILED_VALIDATION: 'failed_validation',     // 参数验证失败
  FAILED_NETWORK: 'failed_network',           // 网络连接失败
  FAILED_API_TIMEOUT: 'failed_api_timeout',   // API调用超时
  FAILED_API_ERROR: 'failed_api_error',       // API返回错误
  FAILED_UNEXPECTED: 'failed_unexpected'     // 意外错误
};

/**
 * 流水线状态枚举
 * 定义流水线整体执行状态
 */
const PIPELINE_STATUS = {
  PENDING: 'pending',     // 流水线等待执行
  RUNNING: 'running',     // 流水线正在运行
  COMPLETED: 'completed', // 流水线成功完成
  FAILED: 'failed'        // 流水线执行失败
};




// ==================== SSE 事件标准定义 ====================

/**
 * SSE 事件类型枚举
 * 定义所有可能的 Server-Sent Events 事件类型
 */
const SSE_EVENT_TYPES = {
  PIPELINE_PROGRESS: 'pipelineProgress',     // 流水线进度事件（主要事件类型）
  PIPELINE_COMPLETE: 'pipelineComplete',     // 流水线完成事件（最终结果事件）
  TASK_PROGRESS: 'taskProgress',             // 单个任务进度事件（可选，用于详细追踪）
  SYSTEM_STATUS: 'systemStatus',             // 系统状态事件
  ERROR: 'error',                            // 错误事件
  HEARTBEAT: 'heartbeat'                     // 心跳事件（保持连接）
};

/**
 * SSE 连接状态枚举
 * 用于追踪 SSE 连接的生命周期
 */
const SSE_CONNECTION_STATUS = {
  CONNECTING: 'connecting',     // 正在建立连接
  CONNECTED: 'connected',       // 连接已建立
  SENDING: 'sending',           // 正在发送数据
  DISCONNECTED: 'disconnected', // 连接已断开
  ERROR: 'error'                // 连接错误
};

/**
 * 控制器特殊状态枚举
 * 用于控制器层面的状态报告
 */
const CONTROLLER_STATUS = {
  ACCEPTED: 'ACCEPTED',                 // 请求已接受
  FAILED_PREFLIGHT: 'FAILED_PREFLIGHT', // 预检失败
  CONTROLLER_ERROR: 'CONTROLLER_ERROR'   // 控制器错误
};

// ==================== SSE 事件工厂函数 ====================

/**
 * @功能概述: 创建标准化的SSE事件数据对象
 * @用途: 控制器层使用，为SSE事件提供统一的数据格式
 * @数据流: Controller → Frontend (EventSource)
 * 
 * @param {string} eventType - 事件类型（来自SSE_EVENT_TYPES）
 * @param {object} eventData - 事件数据对象
 * @param {object} [options={}] - 可选配置
 * @param {string} [options.eventId] - 自定义事件ID，默认使用时间戳
 * @param {number} [options.retry] - 重连间隔（毫秒）
 * 
 * @returns {object} 标准化的SSE事件对象
 * 
 * @example
 * const sseEvent = createSSEEventData(
 *   SSE_EVENT_TYPES.PIPELINE_PROGRESS,
 *   pipelineProgressData,
 *   { eventId: 'custom-123' }
 * );
 */
const createSSEEventData = (eventType, eventData, options = {}) => {
  const eventId = options.eventId || Date.now().toString();
  
  return {
    // === SSE 标准字段 ===
    id: eventId,                              // 事件ID，用于客户端追踪
    event: eventType,                         // 事件类型
    data: eventData,                          // 事件数据
    retry: options.retry || undefined,        // 重连间隔（可选）
    
    // === 扩展字段（用于内部处理）===
    timestamp: new Date().toISOString(),      // 事件创建时间戳
    dataSize: JSON.stringify(eventData).length, // 数据大小（字节）
    
    // === 格式化为SSE协议字符串的方法 ===
    toSSEString() {
      let sseString = `id: ${this.id}\n`;
      sseString += `event: ${this.event}\n`;
      sseString += `data: ${JSON.stringify(this.data)}\n`;
      if (this.retry) {
        sseString += `retry: ${this.retry}\n`;
      }
      sseString += '\n'; // SSE协议要求的空行结束符
      return sseString;
    }
  };
};

/**
 * @功能概述: 创建控制器特殊状态的SSE事件数据
 * @用途: 控制器层使用，报告请求接受、预检失败等特殊状态
 * 
 * @param {string} status - 控制器状态（来自CONTROLLER_STATUS）
 * @param {object} details - 状态详情
 * @param {string} [details.message] - 状态消息
 * @param {string} [details.videoIdentifier] - 视频标识符
 * @param {object} [details.errorDetails] - 错误详情（仅错误状态）
 * 
 * @returns {object} 控制器状态的SSE事件数据
 * 
 * @example
 * // 请求接受事件
 * const acceptedEvent = createControllerStatusSSE(
 *   CONTROLLER_STATUS.ACCEPTED,
 *   { 
 *     message: '视频上传成功，开始处理',
 *     videoIdentifier: 'video-123.mp4'
 *   }
 * );
 * 
 * // 错误事件
 * const errorEvent = createControllerStatusSSE(
 *   CONTROLLER_STATUS.CONTROLLER_ERROR,
 *   {
 *     errorDetails: {
 *       message: '文件格式不支持',
 *       stackPreview: 'Error at line 123...'
 *     }
 *   }
 * );
 */
const createControllerStatusSSE = (status, details = {}) => {
  const eventData = {
    pipelineStatus: status,                   // 使用控制器状态作为流水线状态
    timestamp: new Date().toISOString(),      // 时间戳
    ...details                                // 合并详情数据
  };
  
  return createSSEEventData(SSE_EVENT_TYPES.PIPELINE_PROGRESS, eventData);
};

/**
 * @功能概述: 创建心跳事件数据
 * @用途: 保持SSE连接活跃，防止超时断开
 * 
 * @param {object} [systemInfo={}] - 可选的系统信息
 * @returns {object} 心跳事件数据
 */
const createHeartbeatSSE = (systemInfo = {}) => {
  const eventData = {
    type: 'heartbeat',
    timestamp: new Date().toISOString(),
    serverTime: Date.now(),
    ...systemInfo
  };
  
  return createSSEEventData(SSE_EVENT_TYPES.HEARTBEAT, eventData);
};

// ==================== SSE 工具函数和类 ====================

/**
 * @功能概述: SSE响应头配置标准
 * @用途: 控制器设置SSE响应时使用的标准头部配置
 * 
 * @returns {object} 标准SSE响应头对象
 */
const getSSEHeaders = () => {
  return {
    'Content-Type': 'text/event-stream',      // SSE内容类型
    'Cache-Control': 'no-cache',              // 禁用缓存
    'Connection': 'keep-alive',               // 保持连接
    'X-Accel-Buffering': 'no',               // 禁用Nginx缓冲
    'Access-Control-Allow-Origin': '*',       // CORS支持（根据需要调整）
    'Access-Control-Allow-Headers': 'Cache-Control'
  };
};

/**
 * @功能概述: SSE连接管理器类
 * @用途: 管理SSE连接的生命周期，提供连接状态追踪和错误处理
 * 
 * @property {object} response - Express响应对象
 * @property {string} connectionId - 连接唯一标识
 * @property {string} status - 连接状态（来自SSE_CONNECTION_STATUS）
 * @property {number} startTime - 连接开始时间戳
 * @property {number} eventCount - 已发送事件数量
 * @property {number|null} lastEventTime - 最后事件时间戳
 * @property {boolean} isActive - 连接是否活跃
 */
class SSEConnectionManager {
  constructor(response, connectionId) {
    this.response = response;                 // Express响应对象
    this.connectionId = connectionId;         // 连接唯一标识
    this.status = SSE_CONNECTION_STATUS.CONNECTING; // 连接状态
    this.startTime = Date.now();             // 连接开始时间
    this.eventCount = 0;                     // 已发送事件数量
    this.lastEventTime = null;               // 最后事件时间
    this.isActive = true;                    // 连接是否活跃
  }

  /**
   * @功能概述: 初始化SSE连接
   * @说明: 设置响应头并发送连接确认事件
   */
  initialize() {
    // 设置SSE响应头
    this.response.writeHead(200, getSSEHeaders());
    this.status = SSE_CONNECTION_STATUS.CONNECTED;
    
    // 发送初始连接确认事件
    this.sendEvent(createSSEEventData(
      SSE_EVENT_TYPES.SYSTEM_STATUS,
      {
        type: 'connection_established',
        connectionId: this.connectionId,
        timestamp: new Date().toISOString()
      }
    ));
  }

  /**
   * @功能概述: 发送SSE事件
   * @param {object} sseEventData - SSE事件数据对象
   * @returns {boolean} 发送是否成功
   */
  sendEvent(sseEventData) {
    // 详细的连接状态检查和日志
    if (!this.isActive) {
      console.log(`[SSE] 连接不活跃，无法发送事件。连接ID: ${this.connectionId}`);
      return false;
    }

    if (this.response.writableEnded) {
      console.log(`[SSE] 响应已结束，无法发送事件。连接ID: ${this.connectionId}`);
      this.isActive = false;
      return false;
    }

    try {
      this.status = SSE_CONNECTION_STATUS.SENDING;
      console.log(`[SSE] 发送事件，类型: ${sseEventData.type}，连接ID: ${this.connectionId}`);

      // 发送事件数据
      this.response.write(sseEventData.toSSEString());

      // 强制刷新缓冲区
      if (typeof this.response.flush === 'function') {
        this.response.flush();
      }

      // 更新统计信息
      this.eventCount++;
      this.lastEventTime = Date.now();
      this.status = SSE_CONNECTION_STATUS.CONNECTED;

      console.log(`[SSE] 事件发送成功，连接ID: ${this.connectionId}，事件计数: ${this.eventCount}`);
      return true;
    } catch (error) {
      console.log(`[SSE] 事件发送失败，连接ID: ${this.connectionId}，错误: ${error.message}`);
      this.status = SSE_CONNECTION_STATUS.ERROR;
      this.isActive = false;
      return false;
    }
  }

  /**
   * @功能概述: 关闭SSE连接
   * @说明: 发送关闭事件并结束响应
   * @param {boolean} [sendCloseEvent=true] - 是否发送关闭事件
   * @param {number} [delay=0] - 延迟关闭时间（毫秒）
   */
  close(sendCloseEvent = true, delay = 0) {
    if (this.isActive && !this.response.writableEnded) {
      if (sendCloseEvent) {
        // 发送连接关闭事件
        this.sendEvent(createSSEEventData(
          SSE_EVENT_TYPES.SYSTEM_STATUS,
          {
            type: 'connection_closing',
            connectionId: this.connectionId,
            duration: Date.now() - this.startTime,
            eventCount: this.eventCount
          }
        ));
      }

      // 如果有延迟，则延迟关闭
      if (delay > 0) {
        setTimeout(() => {
          if (this.isActive && !this.response.writableEnded) {
            this.response.end();
          }
        }, delay);
      } else {
        this.response.end();
      }
    }

    this.status = SSE_CONNECTION_STATUS.DISCONNECTED;
    this.isActive = false;
  }

  /**
   * @功能概述: 优雅关闭SSE连接
   * @说明: 延迟发送关闭事件，给最终事件更多传输时间
   * @param {number} [delay=1000] - 延迟时间（毫秒）
   */
  gracefulClose(delay = 1000) {
    if (this.isActive && !this.response.writableEnded) {
      // 延迟发送关闭事件并关闭连接
      setTimeout(() => {
        this.close(true, 0);
      }, delay);
    } else {
      // 如果连接已经不活跃，直接标记为关闭
      this.status = SSE_CONNECTION_STATUS.DISCONNECTED;
      this.isActive = false;
    }
  }

  /**
   * @功能概述: 获取连接统计信息
   * @returns {object} 连接统计数据对象
   */
  getStats() {
    return {
      connectionId: this.connectionId,
      status: this.status,
      duration: Date.now() - this.startTime,
      eventCount: this.eventCount,
      lastEventTime: this.lastEventTime,
      isActive: this.isActive
    };
  }
}




// ==================== 进度数据工厂函数 ====================

/**
 * @功能概述: 创建标准化的任务进度数据对象
 * @用途: TaskBase 层使用，为任务级别的进度监控提供统一数据结构
 * @数据流: TaskBase.reportProgress() → taskProgressCallback → PipelineBase
 * 
 * @param {object} params - 进度数据参数
 * @param {string} params.taskName - 任务名称
 * @param {string} params.taskId - 任务唯一标识
 * @param {string} params.status - 任务状态（来自TASK_STATUS）
 * @param {string|null} [params.subStatus=null] - 任务子状态（来自TASK_SUBSTATUS）
 * @param {number} [params.current=0] - 当前进度值
 * @param {number} [params.total=100] - 总进度值
 * @param {string} [params.detail=''] - 人类可读的详细描述
 * @param {any} [params.result=null] - 任务结果（仅完成时）
 * @param {object} [params.error=null] - 错误信息（仅失败时）
 * @param {object} [params.additionalData] - 其他额外数据，包含所有未明确列出的属性
 * 
 * @returns {object} 标准化的任务进度数据对象
 * 
 * @example
 * const progressData = createProgressData({
 *   taskName: 'ConvertToAudioTask',
 *   taskId: 'convert-123-abc',
 *   status: TASK_STATUS.RUNNING,
 *   subStatus: TASK_SUBSTATUS.FFMPEG_PROCESSING,
 *   current: 50,
 *   total: 100,
 *   detail: '正在转换视频为音频格式'
 * });
 */
const createProgressData = ({
  taskName,
  taskId,
  status,
  subStatus = null,
  current = 0,
  total = 100,
  detail = '',
  result = null,
  error = null,
  ...additionalData
}) => {
  return {
    // === 基础信息 ===
    taskName,        // 任务名称，用于标识
    taskId,          // 任务唯一标识符
    timestamp: new Date().toISOString(), // ISO格式时间戳
    
    // === 状态信息 ===
    status,          // 主状态
    subStatus,       // 子状态，用于细粒度追踪
    
    // === 进度信息 ===
    progress: {
      current,       // 当前进度值
      total,         // 总进度值
      percentage: total > 0 ? Math.round((current / total) * 100) : 0, // 计算百分比
      unit: additionalData.unit || 'steps' // 进度单位
    },
    
    // === 描述信息 ===
    detail,          // 人类可读的详细描述
    technicalDetail: additionalData.technicalDetail || '', // 技术详情
    
    // === 结果和错误 ===
    result,          // 任务执行结果
    error,           // 错误信息对象
    
    // === 层级标识（用于数据流追踪）===
    dataSource: 'task', // 标识数据来源为任务层
    
    // === 其他扩展数据 ===
    ...additionalData
  };
};

/**
 * @功能概述: 创建LLM专用的进度数据对象
 * @用途: 为LLM相关任务提供细粒度的进度追踪
 * @特点: 自动映射LLM阶段到对应的子状态，简化LLM任务的进度报告
 * 
 * @param {string} taskName - 任务名称
 * @param {string} taskId - 任务唯一标识
 * @param {string} phase - LLM处理阶段：'preparing', 'sending', 'waiting', 'receiving', 'parsing', 'validating'
 * @param {string} detail - 详细描述
 * @param {object} [additionalData={}] - 额外数据
 * 
 * @returns {object} 标准化的LLM进度数据对象
 * 
 * @example
 * const llmProgress = createLLMProgressData(
 *   'TranscriptionCorrectionTask',
 *   'correction-456-def',
 *   'waiting',
 *   '等待Azure OpenAI响应',
 *   { estimatedTimeRemaining: 30 }
 * );
 */
const createLLMProgressData = (taskName, taskId, phase, detail, additionalData = {}) => {
  // LLM阶段到子状态的映射表
  const phaseStatusMap = {
    'preparing': TASK_SUBSTATUS.LLM_PREPARING,   // 准备请求参数
    'sending': TASK_SUBSTATUS.LLM_SENDING,       // 发送请求
    'waiting': TASK_SUBSTATUS.LLM_WAITING,       // 等待响应
    'receiving': TASK_SUBSTATUS.LLM_RECEIVING,   // 接收响应
    'parsing': TASK_SUBSTATUS.LLM_PARSING,       // 解析响应
    'validating': TASK_SUBSTATUS.LLM_VALIDATING  // 验证响应
  };

  return createProgressData({
    taskName,
    taskId,
    status: TASK_STATUS.RUNNING,              // LLM任务状态固定为运行中
    subStatus: phaseStatusMap[phase],         // 根据阶段自动设置子状态
    detail,
    technicalDetail: `LLM ${phase} phase`,   // 技术详情标识
    llmPhase: phase,                          // 保留原始阶段信息
    ...additionalData
  });
};

/**
 * @功能概述: 创建标准化的流水线进度数据对象
 * @用途: PipelineBase 层使用，为流水线级别的进度监控提供统一数据结构
 * @数据流: PipelineBase.safeCallPipelineProgressCallback() → Controller → SSE
 * @特点: 自动生成任务快照、计算整体进度、支持当前任务信息
 * 
 * @param {object} params - 流水线进度数据参数
 * @param {string} params.pipelineName - 流水线名称
 * @param {string} params.pipelineId - 流水线唯一标识
 * @param {string} params.pipelineStatus - 流水线状态（来自PIPELINE_STATUS）
 * @param {Array} [params.tasks=[]] - 任务数组
 * @param {string|null} [params.currentTaskName=null] - 当前执行任务名称
 * @param {string|null} [params.currentTaskStatus=null] - 当前任务状态
 * @param {string|null} [params.currentTaskDetail=null] - 当前任务详情
 * @param {object|null} [params.currentTaskProgressData=null] - 当前任务的完整进度数据
 * @param {string|null} [params.failedTaskName=null] - 失败任务名称
 * @param {object|null} [params.failedTaskErrorDetails=null] - 失败任务错误详情
 * @param {string|null} [params.finalContextPreview=null] - 最终上下文预览
 * @param {string|null} [params.finalMessage=null] - 最终消息
 * @param {object} [params.additionalData] - 其他额外数据，包含所有未明确列出的属性
 * 
 * @returns {object} 标准化的流水线进度数据对象
 * 
 * @example
 * const pipelineProgress = createPipelineProgressData({
 *   pipelineName: 'VideoProcessing',
 *   pipelineId: 'video-789-ghi',
 *   pipelineStatus: PIPELINE_STATUS.RUNNING,
 *   tasks: [task1, task2, task3],
 *   currentTaskName: 'ConvertToAudioTask',
 *   currentTaskStatus: TASK_STATUS.RUNNING
 * });
 */
const createPipelineProgressData = ({
  pipelineName,
  pipelineId,
  pipelineStatus,
  tasks = [],
  currentTaskName = null,
  currentTaskStatus = null,
  currentTaskDetail = null,
  currentTaskProgressData = null,
  failedTaskName = null,
  failedTaskErrorDetails = null,
  finalContextPreview = null,
  finalMessage = null,
  ...additionalData
}) => {
  // 生成所有任务的状态快照
  const tasksSnapshot = tasks.map(task => createTaskSnapshot(task));
  
  // 计算已完成任务数量
  const completedTasks = tasks.filter(t => t.status === TASK_STATUS.COMPLETED).length;
  
  return {
    // === 流水线基础信息 ===
    pipelineName,    // 流水线名称
    pipelineId,      // 流水线唯一标识
    pipelineStatus,  // 流水线当前状态
    timestamp: new Date().toISOString(), // ISO格式时间戳
    
    // === 任务统计信息 ===
    tasksSnapshot,   // 所有任务的状态快照数组
    totalTasks: tasks.length,     // 总任务数
    completedTasks,  // 已完成任务数
    pendingTasks: tasks.filter(t => t.status === TASK_STATUS.PENDING).length,   // 等待任务数
    runningTasks: tasks.filter(t => t.status === TASK_STATUS.RUNNING).length,   // 运行任务数
    failedTasks: tasks.filter(t => t.status === TASK_STATUS.FAILED).length,     // 失败任务数
    
    // === 当前任务信息 ===
    currentTaskName,     // 当前执行的任务名称
    currentTaskStatus,   // 当前任务的状态
    currentTaskDetail,   // 当前任务的详细描述
    currentTaskProgressData, // 当前任务的完整进度数据（用于详细追踪）
    
    // === 错误信息（仅失败时）===
    failedTaskName,      // 失败任务的名称
    failedTaskErrorDetails, // 失败任务的错误详情
    
    // === 完成信息（仅完成时）===
    finalContextPreview, // 最终上下文预览
    finalMessage,        // 最终完成消息
    
    // === 整体进度计算 ===
    progress: {
      current: completedTasks,  // 当前完成的任务数
      total: tasks.length,      // 总任务数
      percentage: tasks.length > 0 ? 
        Math.round((completedTasks / tasks.length) * 100) : 0, // 完成百分比
      unit: 'tasks'             // 进度单位为任务
    },
    
    // === 层级标识（用于数据流追踪）===
    dataSource: 'pipeline',     // 标识数据来源为流水线层
    
    // === 其他扩展数据 ===
    ...additionalData
  };
};

/**
 * @功能概述: 创建任务状态快照对象
 * @用途: 为流水线进度数据提供任务状态的简化视图
 * @特点: 避免传递大对象，提供关键信息的预览
 * 
 * @param {object} task - 任务实例对象
 * @returns {object} 任务状态快照对象
 * 
 * @example
 * const snapshot = createTaskSnapshot(myTask);
 * // 返回: { name: 'TaskName', status: 'running', duration: 1500, ... }
 */
const createTaskSnapshot = (task) => {
  return {
    // === 基础标识信息 ===
    name: task.name,                                    // 任务名称
    taskId: task.taskId || `${task.name}-unknown`,     // 任务唯一标识
    status: task.status || TASK_STATUS.PENDING,        // 任务当前状态
    subStatus: task.subStatus || null,                  // 任务子状态
    
    // === 结果和错误预览 ===
    // 结果预览，避免传递大对象，限制长度为70字符
    resultPreview: (task.status === TASK_STATUS.COMPLETED && task.result) ? 
      (JSON.stringify(task.result).substring(0, 70) + '...') : undefined,
    
    // 错误摘要，提供错误信息的简短描述
    errorSummary: (task.status === TASK_STATUS.FAILED && task.error) ? 
      (task.error.message ? task.error.message.substring(0, 70) + '...' : 'Error occurred') : undefined,
    
    // === 执行时间统计 ===
    duration: task.endTime && task.startTime ? task.endTime - task.startTime : null, // 执行时长（毫秒）
    startTime: task.startTime || null,          // 开始时间戳
    endTime: task.endTime || null,              // 结束时间戳
    
    // === 进度历史统计 ===
    progressHistoryCount: task.progressHistory ? task.progressHistory.length : 0, // 进度更新次数
    
    // 最新进度信息（如果有）
    latestProgress: task.progressHistory && task.progressHistory.length > 0 ? 
      task.progressHistory[task.progressHistory.length - 1] : null
  };
};

// ==================== 工具函数 ====================

/**
 * @功能概述: 验证进度数据对象的完整性
 * @用途: 在数据传递过程中验证数据结构的正确性
 * @param {object} progressData - 要验证的进度数据对象
 * @returns {boolean} 验证结果
 */
const validateProgressData = (progressData) => {
  if (!progressData || typeof progressData !== 'object') {
    return false;
  }
  
  // 检查必需字段
  const requiredFields = ['timestamp', 'dataSource'];
  for (const field of requiredFields) {
    if (!(field in progressData)) {
      return false;
    }
  }
  
  // 根据数据源验证特定字段
  if (progressData.dataSource === 'task') {
    return !!(progressData.taskName && progressData.taskId && progressData.status);
  } else if (progressData.dataSource === 'pipeline') {
    return !!(progressData.pipelineName && progressData.pipelineId && progressData.pipelineStatus);
  }
  
  return true;
};

// ==================== 模块导出 ====================

// 导出所有常量、枚举和工厂函数
module.exports = {
  // === 基础状态枚举 ===
  TASK_STATUS,
  TASK_SUBSTATUS,
  PIPELINE_STATUS,
  
  // === SSE 相关枚举和常量 ===
  SSE_EVENT_TYPES,
  SSE_CONNECTION_STATUS,
  CONTROLLER_STATUS,
  
  // === 进度数据工厂函数 ===
  createProgressData,
  createLLMProgressData,
  createPipelineProgressData,
  createTaskSnapshot,
  
  // === SSE 相关工厂函数 ===
  createSSEEventData,
  createControllerStatusSSE,
  createHeartbeatSSE,
  
  // === SSE 工具函数和类 ===
  getSSEHeaders,
  SSEConnectionManager,
  
  // === 工具函数 ===
  validateProgressData
}; 