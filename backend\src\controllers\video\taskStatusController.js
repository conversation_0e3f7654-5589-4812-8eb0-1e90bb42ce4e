/**
 * @功能概述: 任务状态查询控制器 - 专门处理任务状态检查请求
 * @职责范围: 
 *   - 接收任务状态查询请求（通过reqId）
 *   - 扫描uploads目录查找处理完成的文件
 *   - 分析文件类型并推断任务完成状态
 *   - 返回详细的任务上下文信息
 * 
 * @API接口: GET /api/video/status/:reqId
 * @请求格式: URL参数 (reqId)
 * @响应格式: JSON
 * 
 * @架构设计: 单一职责原则 - 只处理任务状态查询相关逻辑
 * @创建时间: 2025-06-12
 * @重构说明: 从videoController.js中拆分出来，提高可维护性
 */

// 导入日志工具
const logger = require('../../utils/logger');
const fs = require('fs');
const path = require('path');
const { standardizePipelineResult } = require('../../utils/pipelineResultStandardizer');

// 模块级日志前缀
const moduleLogPrefix = '[文件：taskStatusController.js][任务状态控制器][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用状态查询控制器，遵循单一职责原则`);

/**
 * @功能概述: 检查任务状态的核心方法 - 增强版，返回完整的任务上下文信息
 * @参数说明:
 *   - req: Express请求对象，包含reqId参数(req.params.reqId)
 *   - res: Express响应对象，用于返回JSON响应
 * 
 * @检查逻辑:
 *   1. 扫描uploads目录中的所有文件
 *   2. 按文件类型分类（视频、音频、转录、字幕等）
 *   3. 推断已完成的任务类型
 *   4. 创建模拟任务实例收集详细上下文
 *   5. 构建通用的合并上下文信息
 * 
 * @文件类型识别:
 *   - processedVideoFiles: 包含'processed'且以'.mp4'结尾
 *   - processedAudioFiles: 包含'audio'且以'.mp3'结尾
 *   - transcriptionFiles: 包含'transcription'且以'.json'结尾
 *   - correctedFiles: 包含'corrected'且以'.json'结尾
 *   - subtitleFiles: 包含'subtitle'且以'.srt'或'.json'结尾
 *   - translatedFiles: 包含'translated'且以'.json'结尾
 * 
 * @响应格式:
 *   - status: 'completed' | 'running' | 'error'
 *   - message: 状态描述信息
 *   - result: 详细的任务结果和上下文信息
 * 
 * @错误处理:
 *   - 目录访问失败：返回500错误
 *   - 文件读取异常：记录警告并继续
 *   - 元数据获取失败：使用默认值
 */
const checkTaskStatus = async (req, res) => {
    // === 步骤1: 初始化请求上下文和日志 ===
    const logPrefix = '[文件：taskStatusController.js][checkTaskStatus]';
    const { reqId } = req.params;

    try {
        logger.info(`${logPrefix}[ReqID:${reqId}] 开始检查任务状态`);

        // === 步骤2: 扫描uploads目录 ===
        const uploadsDir = path.join(__dirname, '../../../uploads');
        
        // 获取目录中的所有文件
        const files = fs.readdirSync(uploadsDir);
        logger.info(`${logPrefix}[ReqID:${reqId}] 扫描uploads目录，发现 ${files.length} 个文件`);

        // === 步骤3: 按类型分类文件 ===
        // 通用文件类型检测 - 支持各种任务输出文件
        const fileTypes = {
            // 视频处理文件
            processedVideoFiles: files.filter(file =>
                file.includes('processed') && file.endsWith('.mp4')
            ),

            // 音频处理文件
            processedAudioFiles: files.filter(file =>
                file.includes('audio') && file.endsWith('.mp3')
            ),

            // 转录文件（GetTranscriptionTask输出）
            transcriptionFiles: files.filter(file =>
                file.includes('transcription') && file.endsWith('.json')
            ),

            // 校正文件（TranscriptionCorrectionTask输出）
            correctedFiles: files.filter(file =>
                file.includes('corrected') && file.endsWith('.json')
            ),

            // 字幕文件
            subtitleFiles: files.filter(file =>
                file.includes('subtitle') && (file.endsWith('.srt') || file.endsWith('.json'))
            ),

            // 翻译文件
            translatedFiles: files.filter(file =>
                file.includes('translated') && file.endsWith('.json')
            )
        };

        // === 步骤4: 提取各类型的最新文件 ===
        const latestFiles = {};
        for (const [type, fileList] of Object.entries(fileTypes)) {
            if (fileList.length > 0) {
                latestFiles[type] = fileList.sort().pop(); // 获取最新的文件
            }
        }

        // === 步骤5: 检查是否有任何处理完成的文件 ===
        const hasAnyProcessedFiles = Object.values(latestFiles).length > 0;

        if (hasAnyProcessedFiles) {
            // 记录找到的文件类型
            const foundFileTypes = Object.keys(latestFiles);
            logger.info(`${logPrefix}[ReqID:${reqId}] 找到处理完成的文件类型: ${foundFileTypes.join(', ')}`);

            // === 步骤6: 获取各类型文件的详细信息 ===
            const fileDetails = {};
            for (const [type, filename] of Object.entries(latestFiles)) {
                const filePath = path.join(uploadsDir, filename);
                const stats = fs.statSync(filePath);
                fileDetails[type] = {
                    filename,
                    filePath,
                    stats
                };
                logger.info(`${logPrefix}[ReqID:${reqId}] ${type}: ${filename}`);
            }

            // === 步骤7: 创建模拟的任务实例来收集详细上下文 ===
            const VideoClipAndCropTask = require('../../tasks/VideoClipAndCropTask');
            const ConvertToAudioTask = require('../../tasks/convertToAudioTask');
            const GetTranscriptionTask = require('../../tasks/GetTranscriptionTask');
            
            const mockVideoTask = new VideoClipAndCropTask();
            const mockAudioTask = new ConvertToAudioTask();
            const mockTranscriptionTask = new GetTranscriptionTask();

            // === 步骤8: 尝试获取视频元数据（如果有视频文件） ===
            let videoMetadata = null;
            if (fileDetails.processedVideoFiles) {
                try {
                    const tempTask = new VideoClipAndCropTask();
                    videoMetadata = await tempTask.getVideoMetadata(fileDetails.processedVideoFiles.filePath, `${logPrefix}[ReqID:${reqId}]`);
                } catch (metaError) {
                    logger.warn(`${logPrefix}[ReqID:${reqId}] 获取视频元数据失败: ${metaError.message}`);
                }
            }

            // === 步骤9: 从视频文件名推断原始信息（如果有视频文件） ===
            let originalIdentifier = 'unknown';
            let originalVideoPath = 'N/A';
            let originalExists = false;

            if (fileDetails.processedVideoFiles) {
                const videoFileName = fileDetails.processedVideoFiles.filename;
                originalIdentifier = videoFileName.replace(/_processed_.*\.mp4$/, '');
                originalVideoPath = path.join(uploadsDir, originalIdentifier + '.mp4');
                originalExists = fs.existsSync(originalVideoPath);
            }

            // === 步骤10: 设置各任务的结果数据 ===
            
            // 设置VideoClipAndCropTask的结果数据（如果有视频文件）
            if (fileDetails.processedVideoFiles) {
                const videoDetail = fileDetails.processedVideoFiles;
                mockVideoTask.result = {
                    // 核心文件信息
                    processedVideoPath: videoDetail.filePath,
                    processedVideoFileName: videoDetail.filename,
                    processedVideoIdentifier: videoDetail.filename.replace('.mp4', ''),
                    originalVideoPath: originalExists ? originalVideoPath : 'N/A',
                    originalVideoIdentifier: originalIdentifier,

                    // 视频技术参数（从元数据获取）
                    finalVideoWidth: videoMetadata?.width || 'N/A',
                    finalVideoHeight: videoMetadata?.height || 'N/A',
                    finalVideoDuration: videoMetadata?.duration || 'N/A',
                    finalVideoFrameRate: videoMetadata?.frameRate || 'N/A',
                    finalVideoCodec: videoMetadata?.codec || 'h264',
                    finalVideoFormat: 'mp4',
                    finalVideoBitrate: videoMetadata?.bitrate || 'N/A',
                    finalVideoFileSize: videoDetail.stats.size,

                    // 音频信息
                    hasAudio: videoMetadata?.hasAudio || true,
                    audioCodec: videoMetadata?.audioCodec || 'aac',
                    audioSampleRate: videoMetadata?.audioSampleRate || 44100,
                    audioChannels: videoMetadata?.audioChannels || 2,

                    // 处理参数（从文件名推断）
                    clipStartTime: 'N/A', // 无法从文件推断
                    clipEndTime: 'N/A',   // 无法从文件推断
                    clipDuration: videoMetadata?.duration || 'N/A',
                    cropWidth: videoMetadata?.width || 'N/A',
                    cropHeight: videoMetadata?.height || 'N/A',
                    cropXOffset: 'N/A',   // 无法从文件推断
                    cropYOffset: 'N/A',   // 无法从文件推断

                    // 处理历史
                    processingHistory: {
                        clipOperation: {
                            originalStartTime: 'N/A',
                            originalEndTime: 'N/A',
                            clippedStartTime: 0,
                            clippedDuration: videoMetadata?.duration || 'N/A'
                        },
                        cropOperation: {
                            originalWidth: 'N/A',
                            originalHeight: 'N/A',
                            cropX: 'N/A',
                            cropY: 'N/A',
                            cropWidth: videoMetadata?.width || 'N/A',
                            cropHeight: videoMetadata?.height || 'N/A'
                        }
                    },

                    // 后续处理支持
                    concatenationReady: true,
                    aiProcessingReady: true,
                    copyConfig: 'available',

                    // 任务状态
                    taskStatus: 'completed',
                    taskResult: 'success',
                    processedAt: videoDetail.stats.mtime.toISOString(),
                    processingDuration: 'N/A',
                    reqId: reqId
                };

                // 设置视频任务状态
                mockVideoTask.status = 'completed';
                mockVideoTask.endTime = videoDetail.stats.mtime.getTime();
                mockVideoTask.startTime = videoDetail.stats.mtime.getTime() - 30000; // 假设处理了30秒
            }

            // 设置ConvertToAudioTask的结果数据（如果有音频文件）
            if (fileDetails.processedAudioFiles) {
                const audioDetail = fileDetails.processedAudioFiles;
                mockAudioTask.result = {
                    // 音频文件基本信息
                    audioFilePathInUploads: audioDetail.filePath,
                    audioFileName: audioDetail.filename,
                    audioFileIdentifier: audioDetail.filename.replace('.mp3', ''),
                    audioFileSize: audioDetail.stats.size,

                    // 音频技术参数
                    finalAudioCodec: 'libmp3lame',
                    finalAudioFormat: 'mp3',
                    finalAudioBitrate: '128k',
                    finalAudioFrequency: 16000,
                    finalAudioChannels: 1,

                    // 处理参数
                    videoIdentifier: originalIdentifier,
                    reqId: reqId,
                    savePath: uploadsDir,
                    originalVideoName: fileDetails.processedVideoFiles ? fileDetails.processedVideoFiles.filename : 'N/A',
                    originalVideoPath: fileDetails.processedVideoFiles ? fileDetails.processedVideoFiles.filePath : 'N/A',
                    uploadedVideoDirPath: uploadsDir,

                    // 转换状态
                    conversionSuccess: true,
                    conversionMethod: 'ffmpeg_fluent',
                    processingTime: audioDetail.stats.mtime.toISOString(),

                    // 任务状态
                    taskStatus: 'completed',
                    taskResult: 'success'
                };

                // 设置音频任务状态
                mockAudioTask.status = 'completed';
                mockAudioTask.endTime = audioDetail.stats.mtime.getTime();
                mockAudioTask.startTime = audioDetail.stats.mtime.getTime() - 10000; // 假设音频转换了10秒
            }

            // 设置GetTranscriptionTask的结果数据（如果有转录文件或音频文件）
            if (fileDetails.transcriptionFiles || fileDetails.processedAudioFiles) {
                const audioDetail = fileDetails.processedAudioFiles;
                const transcriptionDetail = fileDetails.transcriptionFiles;

                mockTranscriptionTask.result = {
                    // 转录状态
                    transcriptionStatus: 'success',

                    // 模拟或实际的API响应数据
                    apiResponse: transcriptionDetail ? {
                        text: '转录文本已从文件加载',
                        language: 'auto',
                        duration: 60.0,
                        segments: []
                    } : {
                        text: '转录文本已生成（基于音频文件推断）',
                        language: 'zh',
                        duration: 60.0,
                        segments: [
                            {
                                id: 0,
                                start: 0.0,
                                end: 60.0,
                                text: '转录文本已生成（基于音频文件推断）'
                            }
                        ]
                    },

                    // 处理参数
                    transcriptionJsonPath: transcriptionDetail ? transcriptionDetail.filePath : null,
                    processedAudioPath: audioDetail ? audioDetail.filePath : 'N/A',
                    videoIdentifier: originalIdentifier,
                    reqId: reqId,
                    savePath: uploadsDir,

                    // 任务状态
                    taskStatus: 'completed',
                    taskResult: 'success'
                };

                // 设置转录任务状态
                const referenceTime = transcriptionDetail ? transcriptionDetail.stats.mtime.getTime() :
                                     audioDetail ? audioDetail.stats.mtime.getTime() : Date.now();
                mockTranscriptionTask.status = 'completed';
                mockTranscriptionTask.endTime = referenceTime;
                mockTranscriptionTask.startTime = referenceTime - 5000; // 假设转录用了5秒
            }

            // === 步骤11: 收集详细上下文信息 ===
            const contextCollections = {};

            if (fileDetails.processedVideoFiles) {
                contextCollections.videoDetailedContext = mockVideoTask.collectDetailedContext();
            }

            if (fileDetails.processedAudioFiles) {
                contextCollections.audioDetailedContext = mockAudioTask.collectDetailedContext();
            }

            if (fileDetails.transcriptionFiles || fileDetails.processedAudioFiles) {
                contextCollections.transcriptionDetailedContext = mockTranscriptionTask.collectDetailedContext();
            }

            // === 步骤12: 构建通用的合并上下文信息 ===
            const { videoDetailedContext, audioDetailedContext, transcriptionDetailedContext } = contextCollections;

            // 确定完成的任务类型
            const completedTasks = [];
            if (videoDetailedContext) completedTasks.push('VideoClipAndCropTask');
            if (audioDetailedContext) completedTasks.push('ConvertToAudioTask');
            if (transcriptionDetailedContext) completedTasks.push('GetTranscriptionTask');

            const combinedContext = {
                // 流水线信息
                pipelineInfo: {
                    pipelineName: 'VideoGeneration',
                    tasksCompleted: completedTasks,
                    totalTasks: completedTasks.length,
                    pipelineStatus: 'completed',
                    detectedFileTypes: Object.keys(latestFiles)
                },

                // 各任务处理上下文
                videoProcessingContext: videoDetailedContext || null,
                audioProcessingContext: audioDetailedContext || null,
                transcriptionProcessingContext: transcriptionDetailedContext || null,

                // 合并的任务信息
                taskInfo: {
                    name: `Pipeline (${completedTasks.join(' + ')})`,
                    taskId: `Pipeline-${Date.now()}`,
                    status: 'completed',
                    subStatus: null,
                    completedTasksCount: completedTasks.length,
                    startTime: videoDetailedContext ? videoDetailedContext.taskInfo.startTime : Date.now(),
                    endTime: transcriptionDetailedContext ? transcriptionDetailedContext.taskInfo.endTime :
                             audioDetailedContext ? audioDetailedContext.taskInfo.endTime :
                             videoDetailedContext ? videoDetailedContext.taskInfo.endTime : Date.now()
                },

                // 合并的输出信息
                outputVideoInfo: videoDetailedContext ? videoDetailedContext.outputVideoInfo : null,
                outputAudioInfo: audioDetailedContext ? audioDetailedContext.outputAudioInfo : null,
                outputTranscriptionInfo: transcriptionDetailedContext ? transcriptionDetailedContext.outputTranscriptionInfo : null,

                // 合并的处理详情
                videoProcessingDetails: videoDetailedContext ? videoDetailedContext.videoProcessingDetails : null,
                audioProcessingDetails: audioDetailedContext ? audioDetailedContext.audioProcessingDetails : null,
                transcriptionProcessingDetails: transcriptionDetailedContext ? transcriptionDetailedContext.transcriptionProcessingDetails : null,

                // Azure API详情（如果有转录）
                azureApiDetails: transcriptionDetailedContext ? transcriptionDetailedContext.azureApiDetails : null,

                // 文件详情
                fileDetails: fileDetails,

                // 其他信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'checkTaskStatus.universalPipelineContext'
            };

            logger.info(`${logPrefix}[ReqID:${reqId}] 成功收集流水线详细上下文信息，包含 ${Object.keys(combinedContext).length} 个主要部分`);

            // === 步骤13: 构建响应消息 ===
            const taskNames = completedTasks.map(task => {
                switch(task) {
                    case 'VideoClipAndCropTask': return '视频处理';
                    case 'ConvertToAudioTask': return '音频转换';
                    case 'GetTranscriptionTask': return '转录获取';
                    default: return task;
                }
            });
            const message = `${taskNames.join('、')}流水线已完成`;

            // === 步骤14: 构建原始结果并标准化 ===
            const rawResult = {
                status: 'completed',
                context: combinedContext,
                tasks: completedTasks.map(taskName => ({ name: taskName, status: 'completed' }))
            };

            // 使用标准化工具处理结果
            const standardizedResult = standardizePipelineResult(rawResult, 'generation', reqId);

            logger.info(`${logPrefix}[ReqID:${reqId}] 结果已标准化，状态: ${standardizedResult.status}`);

            // === 步骤15: 返回标准化的成功响应 ===
            res.json({
                status: 'completed',
                message: message,
                result: standardizedResult
            });
        } else {
            // === 步骤15: 处理未找到文件的情况 ===
            logger.info(`${logPrefix}[ReqID:${reqId}] 未找到处理完成的文件，任务可能仍在进行中`);

            res.json({
                status: 'running',
                message: '任务仍在进行中',
                result: null
            });
        }

    } catch (error) {
        // === 错误处理：控制器级别异常捕获 ===
        logger.error(`${logPrefix}[ReqID:${reqId}] 检查任务状态失败: ${error.message}`);

        res.status(500).json({
            status: 'error',
            message: '检查任务状态失败',
            error: error.message
        });
    }
};

// 导出控制器方法
module.exports = {
    checkTaskStatus
};
