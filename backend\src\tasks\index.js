/**
 * @功能概述: 任务模块索引文件，用于统一导出所有 Task 类。
 */

const ConvertToAudioTask = require('./convertToAudioTask');
const ConvertToAudioForCloudflareTask = require('./ConvertToAudioForCloudflareTask');
const GetTranscriptionTask = require('./GetTranscriptionTask');
const GetTranscriptionTaskByCloudflare = require('./GetTranscriptionTaskByCloudflare');
//const ProcessTranscriptionTask = require('./ProcessTranscriptionTask');
const TranscriptionCorrectionTask = require('./TranscriptionCorrectionTask');
const TranslateSubtitleTask = require('./TranslateSubtitleTask');
const SubtitleOptimizationTask = require('./SubtitleOptimizationTask');
const ClipMediaTask = require('./ClipMediaTask');
const SubtitleClozeTask = require('./SubtitleClozeTask');

// 视频生成相关任务
const VideoClipAndCropTask = require('./VideoClipAndCropTask');

// 短视频生成相关任务
const BilingualSubtitleMergeTask = require('./BilingualSubtitleMergeTask');
const GenerateASSTask = require('./GenerateASSTask');
// const VideoCompositionTask = require('./VideoCompositionTask');

// Web录制相关任务
const PuppeteerRecorderTask = require('./PuppeteerRecorderTask');

// ... 未来可以导出更多 Task

module.exports = {
    ConvertToAudioTask,
    ConvertToAudioForCloudflareTask,
    GetTranscriptionTask,
    GetTranscriptionTaskByCloudflare,
    //ProcessTranscriptionTask,
    TranscriptionCorrectionTask,
    TranslateSubtitleTask,
    SubtitleOptimizationTask,
    ClipMediaTask,
    SubtitleClozeTask,

    // 视频生成相关任务
    VideoClipAndCropTask,

    // 短视频生成相关任务
    BilingualSubtitleMergeTask,
    GenerateASSTask,
    // VideoCompositionTask,

    // Web录制相关任务
    PuppeteerRecorderTask,

    // ... 未来可以添加更多 Task
};