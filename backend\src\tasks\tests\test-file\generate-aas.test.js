

/**
 * @文件名: generate-aas.test.js
 * @功能概述: 将JSON字幕数据转换为ASS字幕文件，实现视频动态效果
 * @技术栈: Node.js + ASS字幕格式
 * @创建时间: 2025-06-09
 * @作者: AI Assistant
 * @位置: backend/src/tasks/tests/test-output/ (测试文件)
 * @用途: 测试JSON到ASS字幕转换功能
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const { createCanvas } = require('canvas');
const { CSSToASSMapper } = require('./css-to-ass-mapper');

// 引用视频配置文件
const VIDEO_CONFIG = require('./video-config.json');



// Canvas测量引擎缓存
const textMeasurementCache = new Map();

/**
 * @功能概述: 使用Canvas API精确测量文本尺寸和换行
 * @参数说明:
 *   - text: {string} 要测量的文本内容
 *   - fontSize: {number} 字体大小（像素）
 *   - fontFamily: {string} 字体名称
 *   - maxWidth: {number} 最大宽度（像素）
 *   - lineHeight: {number} 行高倍数，默认1.3
 * @返回值: {Object} 测量结果
 *   - lines: {Array<string>} 换行后的文本行数组
 *   - lineCount: {number} 行数
 *   - totalHeight: {number} 总高度（像素）
 *   - maxLineWidth: {number} 最宽行的宽度（像素）
 *   - actualLineHeight: {number} 实际行高（像素）
 * @技术实现:
 *   - 使用Canvas 2D Context的measureText API
 *   - 实现智能换行算法
 *   - 缓存测量结果提高性能
 */
function measureTextWithCanvas(text, fontSize, fontFamily, maxWidth, lineHeight = 1.3) {
    const functionName = 'measureTextWithCanvas';

    try {
        // 生成缓存键
        const cacheKey = `${text}|${fontSize}|${fontFamily}|${maxWidth}|${lineHeight}`;

        // 检查缓存
        if (textMeasurementCache.has(cacheKey)) {
            return textMeasurementCache.get(cacheKey);
        }

        // 创建Canvas和Context
        const canvas = createCanvas(maxWidth + 100, 200); // 额外宽度用于测量
        const ctx = canvas.getContext('2d');

        // 设置字体
        ctx.font = `${fontSize}px ${fontFamily}`;

        // 计算实际行高
        const actualLineHeight = fontSize * lineHeight;

        // 文本换行处理
        const lines = wrapTextToLines(ctx, text, maxWidth);

        // 计算总高度
        const totalHeight = lines.length * actualLineHeight;

        // 计算最大行宽
        const maxLineWidth = Math.max(...lines.map(line => ctx.measureText(line).width));

        // 构建结果对象
        const result = {
            lines: lines,
            lineCount: lines.length,
            totalHeight: totalHeight,
            maxLineWidth: maxLineWidth,
            actualLineHeight: actualLineHeight
        };

        // 缓存结果
        textMeasurementCache.set(cacheKey, result);

        console.log(`[${functionName}] 文本测量完成: "${text.substring(0, 30)}..." → ${lines.length}行, 高度${totalHeight.toFixed(1)}px`);

        return result;

    } catch (error) {
        console.error(`[${functionName}] 文本测量失败: ${error.message}`);
        // 返回默认值
        return {
            lines: [text],
            lineCount: 1,
            totalHeight: fontSize * lineHeight,
            maxLineWidth: fontSize * text.length * 0.6, // 估算宽度
            actualLineHeight: fontSize * lineHeight
        };
    }
}

/**
 * @功能概述: 智能文本换行算法（支持中英文）
 * @参数说明:
 *   - ctx: {CanvasRenderingContext2D} Canvas 2D上下文
 *   - text: {string} 要换行的文本
 *   - maxWidth: {number} 最大宽度（像素）
 * @返回值: {Array<string>} 换行后的文本行数组
 * @算法特点:
 *   - 自动检测中文和英文
 *   - 中文按字符换行，英文按单词换行
 *   - 支持中英文混合文本
 */
function wrapTextToLines(ctx, text, maxWidth) {
    // 第一层分支：检测是否包含ASS标记
    const hasAssTag = /\{\\[^}]*\}/.test(text);

    if (hasAssTag) {
        // 包含ASS标记，使用ASS标记感知的处理逻辑
        return wrapTextWithAssSupport(ctx, text, maxWidth);
    } else {
        // 不包含ASS标记，使用原有的文本类型检测逻辑
        const hasChinese = /[\u4e00-\u9fff]/.test(text);
        const hasEnglish = /[a-zA-Z]/.test(text);

        if (hasChinese && !hasEnglish) {
            // 纯中文文本
            return wrapChineseText(ctx, text, maxWidth);
        } else if (!hasChinese && hasEnglish) {
            // 纯英文文本
            return wrapEnglishText(ctx, text, maxWidth);
        } else if (hasChinese && hasEnglish) {
            // 中英文混合文本
            return wrapMixedText(ctx, text, maxWidth);
        } else {
            // 其他文本（数字、符号等）
            return wrapEnglishText(ctx, text, maxWidth);
        }
    }
}

/**
 * @功能概述: ASS标记感知的文本换行处理函数
 * @参数说明:
 *   - ctx: Canvas 2D Context
 *   - text: 包含ASS标记的文本
 *   - maxWidth: 最大宽度
 * @返回值: 换行后的文本数组
 * @处理逻辑: 使用smartSplitText保护ASS标记，智能空格插入避免破坏标记格式
 */
function wrapTextWithAssSupport(ctx, text, maxWidth) {
    // 使用smartSplitText保护ASS标记
    const words = smartSplitText(text);
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
        const word = words[i];

        // 智能空格插入：检查是否需要在word前添加空格
        let testLine;
        if (currentLine === '') {
            // 行首不需要空格
            testLine = word;
        } else if (word.startsWith('{\\')) {
            // ASS标记开头，需要前置空格
            testLine = currentLine + ' ' + word;
        } else if (currentLine.endsWith('}')) {
            // 前一个单词以ASS标记结尾，需要空格
            testLine = currentLine + ' ' + word;
        } else {
            // 正常情况需要空格
            testLine = currentLine + ' ' + word;
        }

        // 测量文本宽度时忽略ASS标记
        const testWidth = measureTextWidthIgnoringAssTags(ctx, testLine);

        if (testWidth > maxWidth && currentLine !== '') {
            lines.push(currentLine);
            currentLine = word;
        } else {
            currentLine = testLine;
        }
    }

    if (currentLine) {
        lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [text];
}

/**
 * @功能概述: 英文文本换行（按单词）- 原有逻辑，不包含ASS标记
 * @修复说明: 专注处理纯英文文本，使用简单的空格分割和换行逻辑
 */
function wrapEnglishText(ctx, text, maxWidth) {
    // 使用简单的空格分割（不包含ASS标记）
    const words = text.split(' ').filter(word => word.length > 0);
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const testLine = currentLine + (currentLine ? ' ' : '') + word;
        const testWidth = ctx.measureText(testLine).width;

        if (testWidth > maxWidth && currentLine !== '') {
            lines.push(currentLine);
            currentLine = word;
        } else {
            currentLine = testLine;
        }
    }

    if (currentLine) {
        lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [text];
}

/**
 * @功能概述: 测量文本宽度时忽略ASS标记
 * @参数说明:
 *   - ctx: Canvas 2D Context
 *   - text: 包含ASS标记的文本
 * @返回值: 忽略ASS标记后的文本实际显示宽度
 */
function measureTextWidthIgnoringAssTags(ctx, text) {
    // 临时移除ASS标记进行宽度测量
    const textWithoutAssTags = text.replace(/\{\\[^}]*\}/g, '');
    return ctx.measureText(textWithoutAssTags).width;
}







/**
 * @功能概述: 智能分割文本，保护ASS标记不被破坏
 * @参数说明:
 *   - text: {string} 包含ASS标记的文本
 * @返回值: {Array<string>} 分割后的单词数组，ASS标记保持完整
 * @处理逻辑:
 *   1. 使用占位符临时替换ASS标记
 *   2. 正常分割文本
 *   3. 恢复ASS标记到正确位置
 */
function smartSplitText(text) {
    // 如果没有ASS标记，直接分割
    if (!/\{\\[^}]*\}/.test(text)) {
        return text.split(' ').filter(word => word.length > 0);
    }

    // 保存ASS标记
    const assTags = [];
    let placeholderIndex = 0;

    // 用占位符替换ASS标记
    const textWithPlaceholders = text.replace(/\{\\[^}]*\}/g, (match) => {
        assTags.push(match);
        return `__ASS_PLACEHOLDER_${placeholderIndex++}__`;
    });

    // 正常分割文本
    const words = textWithPlaceholders.split(' ').filter(word => word.length > 0);

    // 恢复ASS标记
    const restoredWords = words.map(word => {
        return word.replace(/__ASS_PLACEHOLDER_(\d+)__/g, (match, index) => {
            return assTags[parseInt(index)] || match;
        });
    });

    return restoredWords;
}

/**
 * @功能概述: 中文文本换行（按字符）
 */
function wrapChineseText(ctx, text, maxWidth) {
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        const testLine = currentLine + char;
        const testWidth = ctx.measureText(testLine).width;

        if (testWidth > maxWidth && currentLine !== '') {
            lines.push(currentLine);
            currentLine = char;
        } else {
            currentLine = testLine;
        }
    }

    if (currentLine) {
        lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [text];
}

/**
 * @功能概述: 中英文混合文本换行
 * @实现逻辑:
 * 1. 逐字符处理文本，根据字符类型采用不同换行策略
 * 2. 中文按字符换行，英文按单词换行
 * 3. 处理混合排版时的空格和标点问题
 * @参数说明:
 *   - ctx: Canvas 2D上下文，用于测量文本宽度
 *   - text: 需要换行的原始文本
 *   - maxWidth: 单行最大允许宽度（像素）
 */
function wrapMixedText(ctx, text, maxWidth) {
    const lines = [];      // 存储最终换行结果
    let currentLine = '';  // 当前正在构建的文本行
    let i = 0;             // 文本遍历索引

    while (i < text.length) {
        const char = text[i];

        // 中文处理逻辑（Unicode中文范围）
        if (/[\u4e00-\u9fff]/.test(char)) {
            const testLine = currentLine + char;
            const testWidth = ctx.measureText(testLine).width;

            // 超宽处理：提交当前行，开始新行（保留当前中文字符）
            if (testWidth > maxWidth && currentLine !== '') {
                lines.push(currentLine);
                currentLine = char;
            } else {
                currentLine = testLine;
            }
            i++;
        }
        // 英文单词处理（字母和数字视为单词部分）
        else if (/[a-zA-Z]/.test(char)) {
            let word = '';
            // 提取完整英文单词（包含后续连续字母/数字）
            while (i < text.length && /[a-zA-Z0-9]/.test(text[i])) {
                word += text[i];
                i++;
            }

            // 处理单词前的空格（如果当前行非空且不以空格结尾）
            const separator = currentLine && !/\s$/.test(currentLine) ? ' ' : '';
            const testLine = currentLine + separator + word;
            const testWidth = ctx.measureText(testLine).width;

            // 超宽处理：提交当前行，单词作为新行开始
            if (testWidth > maxWidth && currentLine !== '') {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = testLine;
            }
        }
        // 其他字符处理（空格、标点等直接追加）
        else {
            currentLine += char;
            i++;
        }
    }

    // 处理最后剩余内容
    if (currentLine) {
        lines.push(currentLine);
    }

    return lines.length > 0 ? lines : [text];
}

/**
 * @功能概述: 智能计算字幕位置（通用版本）
 * @参数说明:
 *   - text: {string} 字幕文本
 *   - intelligentLayout: {Object} 智能布局配置
 *   - styleConfig: {Object} 样式配置
 *   - isClozedText: {boolean} 是否为填空字幕（需要在textarea中居中）
 * @返回值: {Object} 位置信息
 *   - x: {number} X坐标（居中）
 *   - y: {number} Y坐标
 *   - lines: {Array<string>} 换行后的文本行
 *   - totalHeight: {number} 总高度
 */
function calculateIntelligentPosition(text, intelligentLayout, styleConfig, isClozedText = false) {
    const functionName = 'calculateIntelligentPosition';

    try {
        // 提取配置参数
        const fontSize = parseInt(styleConfig.fontSize.replace('px', ''));
        const fontFamily = styleConfig.fontFamily;
        const maxWidth = parseInt(intelligentLayout.textWrapping.maxWidth.replace('px', ''));
        const lineHeight = intelligentLayout.textWrapping.lineHeight;
        const anchorY = parseInt(intelligentLayout.positioning.anchor.y.replace('px', ''));

        // 使用Canvas测量文本
        const measurement = measureTextWithCanvas(text, fontSize, fontFamily, maxWidth, lineHeight);

        // 计算居中X坐标
        const centerX = 540; // 1080px / 2

        let finalY;

        if (isClozedText) {
            // 填空字幕需要在textarea中垂直居中
            // 从video-config.json获取正确的textarea配置
            const textAreaConfig = VIDEO_CONFIG.textArea;
            const textAreaHeight = parseInt(textAreaConfig.height.replace('px', ''));

            // textarea的垂直位置：从原视频+进度条下方开始
            const originalVideoHeight = 608; // 原视频高度 (1080 * 9/16 = 608)
            const progressBarHeight = 16; // 进度条高度
            const textAreaTopY = originalVideoHeight + progressBarHeight; // 624px
            const textAreaBottomY = textAreaTopY + textAreaHeight; // 624 + 608 = 1232px
            const actualTextAreaHeight = textAreaBottomY - textAreaTopY;

            // 获取填空字幕的边界配置
            const leftMargin = parseInt(intelligentLayout.boundaries.leftMargin.replace('px', ''));
            const rightMargin = parseInt(intelligentLayout.boundaries.rightMargin.replace('px', ''));
            const topMargin = parseInt(intelligentLayout.boundaries.topMargin.replace('px', ''));
            const bottomMargin = parseInt(intelligentLayout.boundaries.bottomMargin.replace('px', ''));

            // 计算安全的textarea区域（考虑边界距离）
            const safeTextAreaTopY = textAreaTopY + topMargin;
            const safeTextAreaBottomY = textAreaBottomY - bottomMargin;
            const safeTextAreaHeight = safeTextAreaBottomY - safeTextAreaTopY;

            // 在安全的textarea区域中垂直居中
            const safeTextAreaCenterY = safeTextAreaTopY + (safeTextAreaHeight / 2);
            finalY = safeTextAreaCenterY - (measurement.totalHeight / 2);

            // 确保文本不超出安全边界
            const minY = safeTextAreaTopY;
            const maxY = safeTextAreaBottomY - measurement.totalHeight;
            finalY = Math.max(minY, Math.min(finalY, maxY));

            console.log(`[${functionName}] 填空字幕textarea居中计算:`);
            console.log(`[${functionName}]   原始textarea区域: ${textAreaTopY}px → ${textAreaBottomY}px (高度${actualTextAreaHeight}px)`);
            console.log(`[${functionName}]   安全边界: 上${topMargin}px, 下${bottomMargin}px, 左${leftMargin}px, 右${rightMargin}px`);
            console.log(`[${functionName}]   安全textarea区域: ${safeTextAreaTopY}px → ${safeTextAreaBottomY}px (高度${safeTextAreaHeight}px)`);
            console.log(`[${functionName}]   文本高度: ${measurement.totalHeight}px`);
            console.log(`[${functionName}]   居中位置: ${finalY}px`);
        } else {
            // 其他字幕使用原有逻辑
            const topMargin = parseInt(intelligentLayout.boundaries.topMargin.replace('px', ''));
            const bottomMargin = parseInt(intelligentLayout.boundaries.bottomMargin.replace('px', ''));

            const safeY = Math.max(anchorY, topMargin);
            const maxY = 1920 - bottomMargin - measurement.totalHeight;
            finalY = Math.min(safeY, maxY);
        }

        console.log(`[${functionName}] 位置计算: "${text.substring(0, 20)}..." → (${centerX}, ${finalY}), ${measurement.lineCount}行`);

        return {
            x: centerX,
            y: finalY,
            lines: measurement.lines,
            totalHeight: measurement.totalHeight,
            actualLineHeight: measurement.actualLineHeight
        };

    } catch (error) {
        console.error(`[${functionName}] 位置计算失败: ${error.message}`);
        // 返回默认位置
        return {
            x: 540,
            y: parseInt(intelligentLayout.positioning.anchor.y.replace('px', '')),
            lines: [text],
            totalHeight: parseInt(styleConfig.fontSize.replace('px', '')) * 1.3,
            actualLineHeight: parseInt(styleConfig.fontSize.replace('px', '')) * 1.3
        };
    }
}

/**
 * @功能概述: 英文关键词处理函数 - 添加中文翻译注释并创建黄色背景按钮效果
 * @参数说明:
 *   - text: {string} 原始英文文本（需要处理的字幕内容）
 *   - wordsExplanation: {Object} 关键词解释对象（键为英文单词/短语，值为对应中文翻译）
 *   - baseY: {number} 基础Y坐标（当前保留参数，用于后续可能的位置计算扩展）
 * @返回值: {Object} 包含处理后文本和上标信息的对象（当前实现暂时直接返回字符串，需后续改进）
 * @技术实现:
 *   - 步骤1：预处理 - 验证输入参数有效性，按关键词长度降序排序
 *   - 步骤2：正则匹配 - 根据关键词类型（单词/短语）构建不同匹配模式
 *   - 步骤3：文本替换 - 在匹配到的关键词后添加中文翻译注释
 *   - 步骤4：样式处理 - 通过ASS特效标签实现字体缩小和样式重置
 * @技术细节:
 *   - 关键词排序：长词优先处理，避免短词匹配覆盖（如先处理"wildfire evacuation"再处理"wildfire"）
 *   - 正则优化：单词使用\b边界匹配，短语直接精确匹配，提升匹配准确性
 *   - 样式重置：使用{\r}恢复默认样式，防止特效标签影响后续文本
 * @ASS效果实现说明:
 *   - 当前实际实现效果：
 *     - 中文翻译字体：{\fs30} 30px字体（相比主文本缩小）
 *     - 样式重置：{\r} 恢复默认样式
 *     - 翻译标记格式：[中文翻译]
 *   - 设计文档预期效果（待实现）：
 *     - 黄色背景：{\3c&H00FFFF&\bord8} 8像素黄色边框
 *     - 文字颜色：{\c&H000000&} 黑色字体
 *     - 上标位置：需结合baseY参数计算垂直位置
 * @示例详细分析:
 *   - 输入: "Good afternoon, there is another wildfire evacuation alert tonight."
 *   - 处理过程:
 *     1. 匹配"wildfire evacuation" → 添加"[疏散警报]"
 *     2. 匹配"alert" → 添加"[警报]"
 *   - 输出: "Good afternoon, there is another wildfire evacuation{\fs25}[疏散警报]{\r} alert{\fs25}[警报]{\r} tonight."
 *   - 渲染效果: 主文本保持原样式，中文翻译以较小字体显示在对应关键词后方
 * @已知问题:
 *   - baseY参数当前未实际使用
 *   - 返回类型声明为Object但实际返回String
 *   - 黄色背景效果尚未实现
 * @优化方向:
 *   - 实现完整的ASS特效标签
 *   - 增加上标元素的独立位置计算
 *   - 完善返回对象的结构
 */
function processEnglishKeywordsWithTranslation(text, wordsExplanation, keywordBlockStyle = null) {
    // 初始化函数名称常量，用于统一日志标识
    const functionName = 'processEnglishKeywordsWithTranslation';

    try {
        // 空值安全检查：如果解释字典为空或不存在，直接返回原文本
        if (!wordsExplanation || Object.keys(wordsExplanation).length === 0) {
            return text; // 提前返回减少不必要的计算
        }

        // 创建文本处理副本，保留原始文本不可变
        let processedText = text;

        // 关键词预处理：按长度降序排序（关键算法：避免短词覆盖长词匹配）
        // 类型: string[]
        const sortedKeywords = Object.keys(wordsExplanation).sort((a, b) => b.length - a.length);

        // 调试日志：记录处理开始时的关键信息
        console.log(`[${functionName}] 开始处理英文关键词翻译: ${sortedKeywords.length}个关键词`);
        console.log(`[${functionName}] 关键词列表: [${sortedKeywords.join(', ')}]`);  // 输出排序后的关键词列表
        console.log(`[${functionName}] 原始文本: "${text}"`);  // 显示原始文本前30字符

        // 主处理循环：遍历所有已排序的关键词
        for (const englishKeyword of sortedKeywords) {
            // 获取当前关键词对应的中文翻译
            const chineseTranslation = wordsExplanation[englishKeyword];

            // 空值跳过：确保关键词和翻译都存在
            if (!englishKeyword || !chineseTranslation) {
                continue;  // 跳过无效条目
            }

            // 文本清理：去除首尾空白字符
            const trimmedKeyword = englishKeyword.trim();
            const trimmedTranslation = chineseTranslation.trim();

            // 正则表达式构建策略：
            let keywordRegex;
            if (trimmedKeyword.includes(' ')) {
                // 多词短语处理：直接精确匹配（避免单词边界问题）
                // 示例："wildfire evacuation" → 匹配完整短语
                keywordRegex = new RegExp(`(${escapeRegExp(trimmedKeyword)})`, 'gi');
            } else {
                // 单词处理：使用单词边界匹配
                // 示例：\b(alert)\b 避免匹配到"alerts"等情况
                keywordRegex = new RegExp(`\\b(${escapeRegExp(trimmedKeyword)})\\b`, 'gi');
            }

            // 匹配检查：判断当前文本是否包含关键词
            if (keywordRegex.test(processedText)) {
                // 重置正则表达式状态（重要：避免test()影响后续replace()）
                keywordRegex.lastIndex = 0;

                // 构建ASS特效替换模板：
                // - $1 保留原始匹配文本
                // - 关键词块颜色（如果配置了的话）
                // - {\fs25} 设置25px字体（需与主文本样式配合）
                // - [翻译内容] 中文注释
                // - {\r} 重置样式避免影响后续文本
                let buttonEffect;
                if (keywordBlockStyle && keywordBlockStyle.color) {
                    // 使用CSS到ASS映射器的颜色转换功能
                    const configPath = path.join(__dirname, 'video-config.json');
                    const cssMapper = new CSSToASSMapper(configPath);
                    const keywordColor = cssMapper.convertColorToASS(keywordBlockStyle.color);
                    // 尝试使用1c（PrimaryColour）标记，这是最明确的颜色控制
                    buttonEffect = `{\\1c${keywordColor}}$1{\\fs30} [${trimmedTranslation}]{\\r}`;
                } else {
                    // 使用默认样式（青色高亮）
                    buttonEffect = `{\\1c&H00FFFF00}$1{\\fs30} [${trimmedTranslation}]{\\r}`;
                }

                // 执行替换操作
                processedText = processedText.replace(keywordRegex, buttonEffect);

                // 成功日志：记录处理后的关键词映射
                console.log(`[${functionName}] ✅ 处理关键词: "${trimmedKeyword}" → "[ ${trimmedTranslation} ]"`);
            } else {
                // 未匹配日志：帮助排查词典与文本不匹配的情况
                console.log(`[${functionName}] ❌ 未找到关键词: "${trimmedKeyword}" 在文本中`);
            }
        }

        // 处理结果汇总日志
        console.log(`[${functionName}] 英文关键词处理完成: "${text.substring(0, 30)}..." → 包含${sortedKeywords.length}个翻译注释`);
        console.log(`[${functionName}] 处理后文本: "${processedText}"`);  // 输出完整处理结果

        return processedText;  // 注意：根据JSDoc应返回Object，当前实际返回String需后续改进

    } catch (error) {
        // 异常处理：记录详细错误信息并返回安全值
        console.error(`[${functionName}] 英文关键词处理失败: ${error.message}`);
        return text; // 降级处理：返回原始文本保证基本功能可用
    }
}





/**
 * @功能概述: 正则表达式特殊字符转义函数
 * @参数说明:
 *   - string: {string} 需要转义的字符串
 * @返回值: {string} 转义后的字符串
 * @用途: 确保关键词中的特殊字符不会被正则表达式误解释
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * @功能概述: 智能计算双语字幕位置（分离的英文和中文样式）
 * @参数说明:
 *   - englishText: {string} 英文文本
 *   - chineseText: {string} 中文文本
 *   - bilingualConfig: {Object} 双语字幕配置（包含englishStyle和chineseStyle）
 *   - wordsExplanation: {Object} 英文关键词解释对象（用于翻译注释）
 * @返回值: {Object} 双语位置信息
 *   - english: {Object} 英文位置信息
 *   - chinese: {Object} 中文位置信息
 */
function calculateBilingualIntelligentPositions(englishText, chineseText, bilingualConfig, wordsExplanation = {}) {
    const functionName = 'calculateBilingualIntelligentPositions';

    try {
        // 提取英文样式配置
        const englishStyle = bilingualConfig.englishStyle;
        const englishFontSize = parseInt(englishStyle.fontSize.replace('px', ''));
        const englishFontFamily = englishStyle.fontFamily;
        const englishAnchorY = parseInt(englishStyle.intelligentLayout.positioning.anchor.y.replace('px', ''));





        // 提取中文样式配置
        const chineseStyle = bilingualConfig.chineseStyle;
        const chineseFontSize = parseInt(chineseStyle.fontSize.replace('px', ''));

        const chineseSpacing = parseInt(chineseStyle.intelligentLayout.positioning.followEnglish.spacing.replace('px', ''));

        // 🎯 样式控制模式：简化文本测量，只计算基础高度
        const englishLineHeight = englishFontSize * 1.3; // 固定行高比例
        const englishMeasurement = {
            lines: [englishText],
            lineCount: 1,
            totalHeight: englishLineHeight,
            actualLineHeight: englishLineHeight
        };

        // 提取英文边界配置（已在上面声明）
        const englishTopMargin = parseInt(englishStyle.intelligentLayout.boundaries.topMargin.replace('px', ''));
        const englishBottomMargin = parseInt(englishStyle.intelligentLayout.boundaries.bottomMargin.replace('px', ''));

        // 计算英文位置（锚点）- 添加边距检查
        let englishX = 540; // 默认居中位置
        let englishY = englishAnchorY;

        // 🔧 修复：模仿ClozedText的简单居中逻辑
        // ClozedText直接使用540px居中，我们也应该这样做
        englishX = 540; // 直接居中，就像ClozedText一样

        // 英文Y坐标边距检查
        const englishMinAllowedY = englishTopMargin;
        const englishMaxAllowedY = 1920 - englishBottomMargin - englishMeasurement.totalHeight;

        if (englishY < englishMinAllowedY) {
            englishY = englishMinAllowedY;
            console.log(`[${functionName}] 英文Y坐标调整到顶部边界安全位置: ${englishAnchorY} → ${englishY}`);
        } else if (englishY > englishMaxAllowedY) {
            englishY = englishMaxAllowedY;
            console.log(`[${functionName}] 英文Y坐标调整到底部边界安全位置: ${englishAnchorY} → ${englishY}`);
        }

        const englishBottomY = englishY + englishMeasurement.totalHeight;

        // 🎯 样式控制模式：简化中文测量，只计算基础高度
        const chineseLineHeight = chineseFontSize * 1.4; // 固定行高比例
        const chineseMeasurement = {
            lines: [chineseText],
            lineCount: 1,
            totalHeight: chineseLineHeight,
            actualLineHeight: chineseLineHeight
        };

        // 计算中文位置（跟随英文）
        const chineseY = englishBottomY + chineseSpacing;

        // 中文边界检查
        const chineseBottomMargin = parseInt(chineseStyle.intelligentLayout.boundaries.bottomMargin.replace('px', ''));

        // 检查中文字幕是否超出底部边界
        const maxAllowedY = 1920 - chineseBottomMargin - chineseMeasurement.totalHeight;
        const finalChineseY = Math.min(chineseY, maxAllowedY);

        if (chineseY > maxAllowedY) {
            console.warn(`[${functionName}] 中文位置超出边界，调整中: ${chineseY} → ${finalChineseY}`);
        }

        // 🔧 修复：模仿ClozedText的简单居中逻辑
        // ClozedText直接使用540px居中，中文字幕也应该这样做
        const finalChineseX = 540; // 直接居中，就像ClozedText一样

        // 处理英文关键词翻译注释
        console.log(`[${functionName}] 开始处理英文关键词翻译注释...`);

        // 提取关键词块样式配置
        const keywordBlockStyle = bilingualConfig.keywordBlockStyle || null;

        // 对英文文本进行关键词翻译注释处理
        // 先将所有行合并，处理跨行关键词，然后重新分行
        const fullEnglishText = englishMeasurement.lines.join(' ');
        const processedFullText = processEnglishKeywordsWithTranslation(fullEnglishText, wordsExplanation, keywordBlockStyle);



        // 🎯 样式控制模式：不进行自动换行，保持单行，由CSS样式控制换行
        const processedEnglishLines = [processedFullText];

        // 中文文本不进行任何特殊处理，保持原样
        const originalChineseLines = chineseMeasurement.lines;

        console.log(`[${functionName}] 英文关键词翻译注释处理完成:`);
        console.log(`[${functionName}]   关键词数量: ${Object.keys(wordsExplanation).length}`);
        console.log(`[${functionName}]   关键词列表: [${Object.keys(wordsExplanation).join(', ')}]`);

        console.log(`[${functionName}] 双语位置计算完成:`);
        console.log(`[${functionName}]   英文: (${englishX}, ${englishY}), ${englishMeasurement.lineCount}行, 高度${englishMeasurement.totalHeight.toFixed(1)}px`);
        console.log(`[${functionName}]   中文: (${finalChineseX}, ${finalChineseY}), ${chineseMeasurement.lineCount}行, 间距${chineseSpacing}px`);

        return {
            english: {
                x: englishX, // 英文使用边距检查后的位置
                y: englishY,
                lines: processedEnglishLines, // 使用翻译注释处理后的文本行
                totalHeight: englishMeasurement.totalHeight,
                actualLineHeight: englishMeasurement.actualLineHeight,
                style: englishStyle
            },
            chinese: {
                x: finalChineseX,
                y: finalChineseY,
                lines: originalChineseLines, // 使用原始中文文本行（不进行特殊处理）
                totalHeight: chineseMeasurement.totalHeight,
                actualLineHeight: chineseMeasurement.actualLineHeight,
                style: chineseStyle
            }
        };

    } catch (error) {
        console.error(`[${functionName}] 双语位置计算失败: ${error.message}`);
        // 返回默认位置
        return {
            english: {
                x: 540,
                y: 800,
                lines: [englishText],
                totalHeight: 40 * 1.3,
                actualLineHeight: 40 * 1.3,
                style: bilingualConfig.englishStyle
            },
            chinese: {
                x: 540,
                y: 900,
                lines: [chineseText],
                totalHeight: 36 * 1.4,
                actualLineHeight: 36 * 1.4,
                style: bilingualConfig.chineseStyle
            }
        };
    }
}




/**
 * 视频标识符 - 用于生成输出文件名的基础标识
 */
const videoIdentifier = 'test_123';

/**
 * 原始音频文件路径 - 用于烧录到生成视频中的原始视频内容
 */
const ORIGINAL_AUDIO_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-_744348a8_audio.mp3';

/**
 * 输出ASS文件名 - 生成的ASS字幕文件名
 */
const OUTPUT_ASS_FILENAME = `${videoIdentifier}_extended_ass.ass`;

/**
 * 输出文件保存目录路径 - 生成的拼接音频和视频文件存储位置
 */
const SAVE_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data';

// 音频重复次数现在在VIDEO_CONFIG.repeatCount中配置

/**
 * 硬编码JSON文件路径
 */
const CLOZED_SUBTITLE_JSON_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\test_123_clozed_subtitle.json';
const BILINGUAL_SUBTITLE_JSON_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\test_123_fine_bilingual_subtitle.json';

// VIDEO_CONFIG 现在从 video-config.json 文件中加载



/**
 * @功能概述: 读取并解析JSON字幕文件
 * @参数说明:
 *   - filePath: JSON文件路径
 * @返回值: Promise<Object> - 解析后的JSON对象
 */
async function readJsonFile(filePath) {
    const functionName = 'readJsonFile';

    try {
        console.log(`[${functionName}] 开始读取JSON文件: ${filePath}`);

        // 检查文件是否存在
        const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
        if (!fileExists) {
            throw new Error(`JSON文件不存在: ${filePath}`);
        }

        // 读取文件内容
        const fileContent = await fs.readFile(filePath, 'utf8');
        console.log(`[${functionName}] 文件读取成功，内容长度: ${fileContent.length} 字符`);

        // 解析JSON
        const jsonData = JSON.parse(fileContent);
        console.log(`[${functionName}] JSON解析成功`);

        return jsonData;

    } catch (error) {
        console.error(`[${functionName}] 读取JSON文件失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 获取音频文件的时长信息
 * @参数说明:
 *   - audioPath: 音频文件路径
 * @返回值: Promise<number> - 音频时长（秒）
 * @技术实现:
 *   1. 使用FFprobe获取音频文件信息
 *   2. 解析JSON格式输出
 *   3. 从format或streams中提取duration
 *   4. 返回时长数值（秒）
 */
async function getAudioDuration(audioPath) {
    const functionName = 'getAudioDuration';

    return new Promise((resolve, reject) => {
        try {
            console.log(`[${functionName}] 开始获取音频时长信息...`);
            console.log(`[${functionName}] 音频文件路径: ${audioPath}`);

            // 构建FFprobe命令参数 - 获取JSON格式的媒体信息
            const ffprobeArgs = [
                '-v', 'quiet',                    // 静默模式，减少输出
                '-print_format', 'json',          // 输出JSON格式
                '-show_format',                   // 显示格式信息
                '-show_streams',                  // 显示流信息
                audioPath                         // 音频文件路径
            ];

            console.log(`[${functionName}] FFprobe命令: ffprobe ${ffprobeArgs.join(' ')}`);

            // 启动FFprobe进程
            const ffprobeProcess = spawn('ffprobe', ffprobeArgs);

            let ffprobeOutput = '';
            let ffprobeError = '';

            // 监听stdout输出（JSON数据）
            ffprobeProcess.stdout.on('data', (data) => {
                ffprobeOutput += data.toString();
            });

            // 监听stderr输出（错误信息）
            ffprobeProcess.stderr.on('data', (data) => {
                ffprobeError += data.toString();
            });

            // 监听进程退出事件
            ffprobeProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        // 解析JSON输出
                        const mediaInfo = JSON.parse(ffprobeOutput);

                        // 从format信息中获取时长
                        let duration = null;
                        if (mediaInfo.format && mediaInfo.format.duration) {
                            duration = parseFloat(mediaInfo.format.duration);
                        }

                        // 如果format中没有时长，尝试从音频流中获取
                        if (!duration && mediaInfo.streams) {
                            const audioStream = mediaInfo.streams.find(stream => stream.codec_type === 'audio');
                            if (audioStream && audioStream.duration) {
                                duration = parseFloat(audioStream.duration);
                            }
                        }

                        if (duration && duration > 0) {
                            console.log(`[${functionName}] 音频时长获取成功: ${duration.toFixed(2)} 秒`);
                            resolve(duration);
                        } else {
                            const errorMsg = '无法获取有效的音频时长信息';
                            console.error(`[${functionName}] ${errorMsg}`);
                            reject(new Error(errorMsg));
                        }

                    } catch (parseError) {
                        const errorMsg = `解析音频信息失败: ${parseError.message}`;
                        console.error(`[${functionName}] ${errorMsg}`);
                        console.error(`[${functionName}] FFprobe输出: ${ffprobeOutput}`);
                        reject(new Error(errorMsg));
                    }
                } else {
                    const errorMsg = `FFprobe执行失败，退出码: ${code}`;
                    console.error(`[${functionName}] ${errorMsg}`);
                    console.error(`[${functionName}] FFprobe错误输出: ${ffprobeError}`);
                    reject(new Error(errorMsg));
                }
            });

            // 监听进程启动错误
            ffprobeProcess.on('error', (error) => {
                const errorMsg = `FFprobe进程启动失败: ${error.message}`;
                console.error(`[${functionName}] ${errorMsg}`);
                reject(new Error(errorMsg));
            });

        } catch (error) {
            console.error(`[${functionName}] 获取音频时长失败: ${error.message}`);
            reject(error);
        }
    });
}

/**
 * @功能概述: 加载并模拟context中的字幕数据
 * @返回值: Promise<Object> - 包含clozedSubtitleJsonArray和bilingualSubtitleJsonArray的对象
 */
async function loadSubtitleData() {
    const functionName = 'loadSubtitleData';

    try {
        console.log(`[${functionName}] 开始加载字幕数据...`);

        // 读取填空字幕JSON
        console.log(`[${functionName}] 读取填空字幕JSON...`);
        const clozedSubtitleData = await readJsonFile(CLOZED_SUBTITLE_JSON_PATH);

        // 读取双语字幕JSON
        console.log(`[${functionName}] 读取双语字幕JSON...`);
        const bilingualSubtitleData = await readJsonFile(BILINGUAL_SUBTITLE_JSON_PATH);

        // 模拟context对象
        const context = {
            clozedSubtitleJsonArray: clozedSubtitleData,
            bilingualSubtitleJsonArray: bilingualSubtitleData
        };

        console.log(`[${functionName}] 字幕数据加载完成`);
        console.log(`[${functionName}] 填空字幕数据类型: ${Array.isArray(clozedSubtitleData) ? 'Array' : typeof clozedSubtitleData}`);
        console.log(`[${functionName}] 双语字幕数据类型: ${Array.isArray(bilingualSubtitleData) ? 'Array' : typeof bilingualSubtitleData}`);

        if (Array.isArray(clozedSubtitleData)) {
            console.log(`[${functionName}] 填空字幕条目数量: ${clozedSubtitleData.length}`);
        }

        if (Array.isArray(bilingualSubtitleData)) {
            console.log(`[${functionName}] 双语字幕条目数量: ${bilingualSubtitleData.length}`);
        }

        return context;

    } catch (error) {
        console.error(`[${functionName}] 加载字幕数据失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 生成视频引导语JSON数据（只有一个元素）
 * @返回值: Promise<Array> - 包含一个元素的数组，如果videoGuide.enabled为false则返回空数组
 * @数据结构:
 *   - id: 固定为 "1"
 *   - start: 固定为 0
 *   - end: 音频时长 * VIDEO_CONFIG.repeatCount
 *   - text_1: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.title1
 *   - text_2: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.title2
 *   - style: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.style
 */
async function generateSingleElementJson() {
    const functionName = 'generateSingleElementJson';

    try {
        console.log(`[${functionName}] 开始生成视频引导语JSON数据...`);

        // 检查videoGuide是否启用
        const videoGuideConfig = VIDEO_CONFIG.subtitleConfig.videoGuide;
        if (!videoGuideConfig.enabled) {
            console.log(`[${functionName}] 视频引导语已禁用，返回空数组`);
            return [];
        }

        // 获取原始音频时长
        console.log(`[${functionName}] 获取音频时长...`);
        const originalAudioDuration = await getAudioDuration(ORIGINAL_AUDIO_PATH);

        // 计算总时长（原时长 * 重复次数）
        const totalDuration = originalAudioDuration * VIDEO_CONFIG.repeatCount;
        console.log(`[${functionName}] 原音频时长: ${originalAudioDuration.toFixed(2)} 秒`);
        console.log(`[${functionName}] 重复次数: ${VIDEO_CONFIG.repeatCount}`);
        console.log(`[${functionName}] 总时长: ${totalDuration.toFixed(2)} 秒`);

        // 生成视频引导语JSON数据
        const videoGuideData = [
            {
                "id": "1",
                "start": 0,
                "end": totalDuration,
                "text_1": videoGuideConfig.title1,
                "text_2": videoGuideConfig.title2,
                "style": videoGuideConfig.style
            }
        ];

        console.log(`[${functionName}] 视频引导语JSON数据生成完成`);
        console.log(`[${functionName}] 数据结构:`, Object.keys(videoGuideData[0]));
        console.log(`[${functionName}] id: ${videoGuideData[0].id}`);
        console.log(`[${functionName}] start: ${videoGuideData[0].start}`);
        console.log(`[${functionName}] end: ${videoGuideData[0].end}`);
        console.log(`[${functionName}] text_1: "${videoGuideData[0].text_1}"`);
        console.log(`[${functionName}] text_2: "${videoGuideData[0].text_2}"`);
        console.log(`[${functionName}] style配置:`, JSON.stringify(videoGuideData[0].style, null, 2));

        return videoGuideData;

    } catch (error) {
        console.error(`[${functionName}] 生成视频引导语JSON数据失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 生成单元引导字幕JSON数据
 * @参数说明:
 *   - repeatCount: 重复次数，用于确定生成多少个引导字幕
 *   - originalAudioDuration: 原始音频时长（秒）
 * @返回值: Promise<Array> - 包含单元引导字幕的数组
 * @数据结构:
 *   - id: 字符串，从"1"开始递增
 *   - start: 开始时间（秒）
 *   - end: 结束时间（秒）
 *   - text: 显示文本，来自repeatModes[i].displayText
 * @时间逻辑:
 *   - 第1个元素: start=0, end=1*audioDuration
 *   - 第2个元素: start=1*audioDuration, end=2*audioDuration
 *   - 第n个元素: start=(n-1)*audioDuration, end=n*audioDuration
 */
async function generateRepeatModeGuideJson(repeatCount, originalAudioDuration) {
    const functionName = 'generateRepeatModeGuideJson';

    try {
        console.log(`[${functionName}] 开始生成单元引导字幕JSON数据...`);
        console.log(`[${functionName}] 重复次数: ${repeatCount}`);
        console.log(`[${functionName}] 原音频时长: ${originalAudioDuration.toFixed(2)} 秒`);

        // 检查repeatModes数量和repeatCount是否一致
        const repeatModes = VIDEO_CONFIG.subtitleConfig.repeatModes;
        if (repeatModes.length !== repeatCount) {
            throw new Error(`repeatModes数量(${repeatModes.length})与repeatCount(${repeatCount})不一致`);
        }

        console.log(`[${functionName}] ✅ repeatModes数量验证通过: ${repeatModes.length} 个模式`);

        // 生成单元引导字幕数据
        const repeatModeGuideData = [];

        for (let i = 0; i < repeatCount; i++) {
            const mode = repeatModes[i];
            const startTime = i * originalAudioDuration;
            const endTime = (i + 1) * originalAudioDuration;

            const guideItem = {
                "id": (i + 1).toString(),
                "start": startTime,
                "end": endTime,
                "text": mode.displayText
            };

            repeatModeGuideData.push(guideItem);

            console.log(`[${functionName}] 生成第${i + 1}个引导字幕:`);
            console.log(`[${functionName}]   - 模式: ${mode.name}`);
            console.log(`[${functionName}]   - 文本: "${mode.displayText}"`);
            console.log(`[${functionName}]   - 时间: ${startTime.toFixed(2)}s → ${endTime.toFixed(2)}s`);
        }

        console.log(`[${functionName}] 单元引导字幕JSON数据生成完成，共 ${repeatModeGuideData.length} 个条目`);

        return repeatModeGuideData;

    } catch (error) {
        console.error(`[${functionName}] 生成单元引导字幕JSON数据失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 根据repeatModes配置处理字幕数据的时间偏移和显示控制
 * @参数说明:
 *   - context: 原始context对象，包含clozedSubtitleJsonArray和bilingualSubtitleJsonArray
 *   - originalAudioDuration: 原始音频时长（秒）
 * @返回值: Promise<Object> - 处理后的context对象
 * @处理逻辑:
 *   1. 检查repeatModes中是否包含"clozedSubtitle"和"bilingualSubtitle"
 *   2. 根据在repeatModes中的位置计算时间偏移量
 *   3. 对相应的字幕数据进行时间戳调整
 *   4. 如果repeatModes中没有对应类型，则从context中移除该字幕数据
 */
async function processSubtitleDataByRepeatModes(context, originalAudioDuration) {
    const functionName = 'processSubtitleDataByRepeatModes';

    try {
        console.log(`[${functionName}] 开始根据repeatModes处理字幕数据...`);
        console.log(`[${functionName}] 原始音频时长: ${originalAudioDuration.toFixed(2)} 秒`);

        const repeatModes = VIDEO_CONFIG.subtitleConfig.repeatModes;
        console.log(`[${functionName}] repeatModes配置:`, repeatModes.map(mode => `${mode.name}: "${mode.displayText}"`));

        // 创建处理后的context副本
        const processedContext = {
            ...context,
            clozedSubtitleJsonArray: null,
            bilingualSubtitleJsonArray: null
        };

        // 处理填空字幕 (clozedSubtitle)
        const clozedModeIndex = repeatModes.findIndex(mode => mode.name === "clozedSubtitle");
        if (clozedModeIndex !== -1) {
            console.log(`[${functionName}] 找到clozedSubtitle模式，位于第${clozedModeIndex + 1}个位置`);

            // 计算时间偏移量：(位置索引) * 原始音频时长
            const timeOffset = clozedModeIndex * originalAudioDuration;
            console.log(`[${functionName}] clozedSubtitle时间偏移量: ${timeOffset.toFixed(2)} 秒`);

            // 复制并调整填空字幕数据的时间戳
            if (context.clozedSubtitleJsonArray && Array.isArray(context.clozedSubtitleJsonArray)) {
                processedContext.clozedSubtitleJsonArray = context.clozedSubtitleJsonArray.map(item => ({
                    ...item,
                    start: item.start + timeOffset,
                    end: item.end + timeOffset
                }));

                console.log(`[${functionName}] ✅ 填空字幕数据处理完成，共${processedContext.clozedSubtitleJsonArray.length}条`);
                console.log(`[${functionName}] 填空字幕时间范围: ${processedContext.clozedSubtitleJsonArray[0].start.toFixed(2)}s → ${processedContext.clozedSubtitleJsonArray[processedContext.clozedSubtitleJsonArray.length-1].end.toFixed(2)}s`);
            }
        } else {
            console.log(`[${functionName}] ❌ repeatModes中未找到clozedSubtitle模式，填空字幕将不显示`);
            processedContext.clozedSubtitleJsonArray = [];
        }

        // 处理双语字幕 (bilingualSubtitle)
        const bilingualModeIndex = repeatModes.findIndex(mode => mode.name === "bilingualSubtitle");
        if (bilingualModeIndex !== -1) {
            console.log(`[${functionName}] 找到bilingualSubtitle模式，位于第${bilingualModeIndex + 1}个位置`);

            // 计算时间偏移量：(位置索引) * 原始音频时长
            const timeOffset = bilingualModeIndex * originalAudioDuration;
            console.log(`[${functionName}] bilingualSubtitle时间偏移量: ${timeOffset.toFixed(2)} 秒`);

            // 复制并调整双语字幕数据的时间戳
            if (context.bilingualSubtitleJsonArray && Array.isArray(context.bilingualSubtitleJsonArray)) {
                processedContext.bilingualSubtitleJsonArray = context.bilingualSubtitleJsonArray.map(item => ({
                    ...item,
                    start: item.start + timeOffset,
                    end: item.end + timeOffset
                }));

                console.log(`[${functionName}] ✅ 双语字幕数据处理完成，共${processedContext.bilingualSubtitleJsonArray.length}条`);
                console.log(`[${functionName}] 双语字幕时间范围: ${processedContext.bilingualSubtitleJsonArray[0].start.toFixed(2)}s → ${processedContext.bilingualSubtitleJsonArray[processedContext.bilingualSubtitleJsonArray.length-1].end.toFixed(2)}s`);
            }
        } else {
            console.log(`[${functionName}] ❌ repeatModes中未找到bilingualSubtitle模式，双语字幕将不显示`);
            processedContext.bilingualSubtitleJsonArray = [];
        }

        // 输出处理结果摘要
        console.log(`[${functionName}] ========== 字幕数据处理完成 ==========`);
        console.log(`[${functionName}] 填空字幕: ${processedContext.clozedSubtitleJsonArray ? processedContext.clozedSubtitleJsonArray.length : 0} 条`);
        console.log(`[${functionName}] 双语字幕: ${processedContext.bilingualSubtitleJsonArray ? processedContext.bilingualSubtitleJsonArray.length : 0} 条`);

        return processedContext;

    } catch (error) {
        console.error(`[${functionName}] 处理字幕数据失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 将时间戳（秒）转换为ASS格式时间戳
 * @参数说明:
 *   - seconds: 时间戳（秒，浮点数）
 * @返回值: string - ASS格式时间戳 (H:MM:SS.CC)
 * @示例: 65.5 → "1:05:30.50"
 */
function secondsToAssTimestamp(seconds) {
    const totalCentiseconds = Math.round(seconds * 100);
    const hours = Math.floor(totalCentiseconds / 360000);
    const minutes = Math.floor((totalCentiseconds % 360000) / 6000);
    const secs = Math.floor((totalCentiseconds % 6000) / 100);
    const centiseconds = totalCentiseconds % 100;

    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
}

/**
 * @功能概述: Canvas文本测量 + 动态Y坐标计算（方案B优化版）
 * @参数说明:
 *   - englishText: {string} 英文文本（包含ASS标记）
 *   - englishStyle: {Object} 英文样式配置
 * @返回值: {Object} 包含chineseY, englishHeight, textWidth的对象
 * @技术实现:
 *   - 第一步：读取ASS样式定义（BilingualEnglish）
 *   - 第二步：精确清理ASS标记（{\1c&H0000FFFF}、{\fs30}、{\r}等）
 *   - 第三步：使用Canvas测量纯文本尺寸
 *   - 第四步：基于文本长度动态调整高度
 *   - 不修改BilingualEnglish逻辑
 */
function calculateBilingualChinesePosition(englishText, englishStyle) {
    const functionName = 'calculateBilingualChinesePosition';

    try {
        console.log(`[${functionName}] 开始测量英文文本: "${englishText.substring(0, 50)}..."`);

        // 第一步：读取ASS样式定义（BilingualEnglish）
        // Style: BilingualEnglish,Arial,50,&H00FFFFFF,&H00FF0000,&H00000000,&H00FFFFFF,-1,0,0,0,100,100,0,0,1,0,0,5,150,150,10,1
        const fontSize = englishStyle.fontSize.replace('px', '');
        const fontFamily = englishStyle.fontFamily;
        console.log(`[${functionName}] ASS样式定义: 字体=${fontFamily}, 大小=${fontSize}px`);

        // 第二步：精确清理ASS标记
        let cleanText = englishText;

        // 清理颜色标记：{\1c&H0000FFFF}
        cleanText = cleanText.replace(/\{\\1c&H[0-9A-Fa-f]+\}/g, '');

        // 清理字体大小标记：{\fs30}
        cleanText = cleanText.replace(/\{\\fs\d+\}/g, '');

        // 清理重置标记：{\r}
        cleanText = cleanText.replace(/\{\\r\}/g, '');

        // 清理其他可能的ASS标记
        cleanText = cleanText.replace(/\{[^}]*\}/g, '');

        // 清理多余空格
        cleanText = cleanText.trim();

        console.log(`[${functionName}] 原始文本: "${englishText}"`);
        console.log(`[${functionName}] 清理后文本: "${cleanText}"`);

        // 第三步：使用Canvas测量纯文本尺寸
        const canvas = createCanvas(1080, 200);
        const ctx = canvas.getContext('2d');
        ctx.font = `${fontSize}px ${fontFamily}`;

        const measurement = ctx.measureText(cleanText);
        console.log(`[${functionName}] Canvas字体设置: ${ctx.font}`);
        console.log(`[${functionName}] 测量宽度: ${measurement.width.toFixed(1)}px`);

        // 第四步：基于文本长度动态调整高度
        const fontSizeNum = parseInt(fontSize);
        const baseLineHeight = fontSizeNum * 1.2; // 基础行高

        // 方案B优化：基于精确测量的文本长度动态调整高度
        let englishHeight = baseLineHeight;
        const screenWidth = 1080; // 屏幕宽度

        if (measurement.width > screenWidth) {
            // 超长文本可能换行，增加高度
            const estimatedLines = Math.ceil(measurement.width / screenWidth);
            englishHeight = baseLineHeight * estimatedLines;
            console.log(`[${functionName}] 检测到超长文本，估算行数: ${estimatedLines}, 调整高度: ${englishHeight.toFixed(1)}px`);
        } else {
            console.log(`[${functionName}] 文本宽度${measurement.width.toFixed(1)}px <= 屏幕宽度${screenWidth}px，使用单行高度`);
        }

        console.log(`[${functionName}] 字体大小: ${fontSizeNum}px, 估算高度: ${englishHeight.toFixed(1)}px`);

        // 5. 动态计算中文Y坐标
        const englishY = 800;
        const spacing = 120; // 固定间距（调整为120px）
        const chineseY = englishY + englishHeight + spacing;

        console.log(`[${functionName}] 英文Y: ${englishY}, 高度: ${englishHeight.toFixed(1)}, 间距: ${spacing}`);
        console.log(`[${functionName}] 计算得出中文Y: ${chineseY.toFixed(1)}`);

        const result = {
            chineseY: Math.round(chineseY),
            englishHeight: Math.round(englishHeight),
            textWidth: Math.round(measurement.width)
        };

        console.log(`[${functionName}] 最终结果:`, result);
        return result;

    } catch (error) {
        console.error(`[${functionName}] 测量失败: ${error.message}`);
        // 返回默认值（当前固定值）
        return { chineseY: 945, englishHeight: 60, textWidth: 800 };
    }
}

/**
 * @功能概述: 为中文字幕添加智能换行（专为BilingualChinese设计）
 * @参数说明:
 *   - chineseText: {string} 中文文本
 *   - maxCharsPerLine: {number} 每行最大字符数（默认15）
 * @返回值: {string} 添加ASS换行符的文本
 * @技术实现:
 *   - 基于字符数量和标点符号智能换行
 *   - 优先在标点符号处换行
 *   - 使用ASS换行符\\N
 *   - 保持与BilingualEnglish对齐
 */
function addChineseSmartLineBreaks(chineseText, maxCharsPerLine = 15) {
    const functionName = 'addChineseSmartLineBreaks';

    if (!chineseText || typeof chineseText !== 'string') {
        console.log(`[${functionName}] 输入为空或非字符串，返回空字符串`);
        return '';
    }

    console.log(`[${functionName}] 开始处理中文换行: "${chineseText}"`);
    console.log(`[${functionName}] 换行参数: maxCharsPerLine=${maxCharsPerLine}`);

    // 中文标点符号列表
    const chinesePunctuation = '，。！？；：、""\'\'（）【】《》';
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < chineseText.length; i++) {
        const char = chineseText[i];
        currentLine += char;

        // 检查是否需要换行
        const shouldBreak =
            currentLine.length >= maxCharsPerLine || // 达到最大字符数
            chinesePunctuation.includes(char); // 遇到标点符号

        if (shouldBreak) {
            const trimmedLine = currentLine.trim();
            if (trimmedLine) {
                lines.push(trimmedLine);
                console.log(`[${functionName}] 添加行: "${trimmedLine}" (长度: ${trimmedLine.length})`);
            }
            currentLine = '';
        }
    }

    // 处理最后一行
    const finalLine = currentLine.trim();
    if (finalLine) {
        lines.push(finalLine);
        console.log(`[${functionName}] 添加最后行: "${finalLine}" (长度: ${finalLine.length})`);
    }

    const result = lines.join('\\N');
    console.log(`[${functionName}] 换行完成: ${lines.length}行`);
    console.log(`[${functionName}] 原文: "${chineseText}"`);
    console.log(`[${functionName}] 结果: "${result}"`);

    return result;
}

/**
 * @功能概述: 为中文文本添加智能换行（旧版本，保留兼容性）
 * @参数说明:
 *   - text: 中文文本
 *   - textWrapping: 换行配置对象
 * @返回值: string - 处理后的文本（包含ASS换行符）
 * @技术实现:
 *   - 根据maxWidth配置计算换行位置
 *   - 在标点符号处优先换行
 *   - 使用ASS换行符\\N
 */
function addChineseLineBreaks(text, textWrapping) {
    if (!text || !textWrapping) return text;

    const maxWidth = parseInt(textWrapping.maxWidth) || 500;
    const forceWrap = textWrapping.forceWrap;

    console.log(`[addChineseLineBreaks] 处理中文换行:`, {
        text: text,
        maxWidth: maxWidth,
        forceWrap: forceWrap
    });

    // 如果文本很短，不需要换行
    if (text.length <= 15 && !forceWrap) {
        return text;
    }

    // 简单的中文换行逻辑：每15-20个字符换行
    const maxCharsPerLine = Math.floor(maxWidth / 30); // 假设每个中文字符约30px宽
    const targetLength = Math.max(15, Math.min(20, maxCharsPerLine));

    console.log(`[addChineseLineBreaks] 计算换行参数:`, {
        maxCharsPerLine: maxCharsPerLine,
        targetLength: targetLength
    });

    if (text.length <= targetLength && !forceWrap) {
        return text;
    }

    // 在标点符号处换行
    const punctuations = ['，', '。', '、', '；', '：', '！', '？', ',', '.', ';', ':', '!', '?'];
    let result = '';
    let currentLine = '';

    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        currentLine += char;

        // 检查是否需要换行
        const shouldBreak = (
            currentLine.length >= targetLength && punctuations.includes(char)
        ) || (
            forceWrap && currentLine.length >= targetLength
        );

        if (shouldBreak && i < text.length - 1) {
            result += currentLine + '\\N';
            currentLine = '';
        }
    }

    // 添加最后一行
    result += currentLine;

    console.log(`[addChineseLineBreaks] 换行结果:`, {
        original: text,
        processed: result,
        lineCount: (result.match(/\\N/g) || []).length + 1
    });

    return result;
}

/**
 * @功能概述: 生成ASS字幕文件
 * @参数说明:
 *   - context: 包含所有字幕数据的上下文对象
 *     - clozedSubtitleJsonArray: 填空字幕数据
 *     - bilingualSubtitleJsonArray: 双语字幕数据
 *     - videoTitleJsonArray: 视频标题数据
 *     - SAVE_PATH: 保存路径
 * @返回值: Promise<string> - 生成的ASS文件路径
 * @技术实现:
 *   1. 根据ASS格式规范生成文件头部
 *   2. 定义多种样式用于不同类型的字幕
 *   3. 将JSON数据转换为ASS Dialogue事件
 *   4. 写入文件并处理写入错误
 */
async function generateAssSubtitle(context) {
    const functionName = 'generateAssSubtitle';

    try {
        console.log(`[${functionName}] 开始生成ASS字幕文件...`);

        // 构建ASS文件路径
        const assFilePath = path.join(context.SAVE_PATH, OUTPUT_ASS_FILENAME);
        console.log(`[${functionName}] ASS文件路径: ${assFilePath}`);

        // 生成ASS文件内容
        let assContent = '';

        // [V4+ Styles] 部分 - 使用CSS到ASS映射器
        console.log(`[${functionName}] 开始生成ASS样式定义...`);

        // 创建CSS到ASS映射器实例
        const configPath = path.join(__dirname, 'video-config.json');
        const cssMapper = new CSSToASSMapper(configPath);

        // [Script Info] 部分
        assContent += '[Script Info]\n';
        assContent += 'Title: Generated ASS Subtitle\n';
        assContent += 'PlayResX: 1080\n';
        assContent += 'PlayResY: 1920\n';

        // 根据配置设置WrapStyle - 优先检查中文字幕的换行配置
        const chineseTextWrapping = VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.intelligentLayout?.textWrapping;
        const wrapConfig = cssMapper.convertTextWrapToASS(
            VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.whiteSpace || VIDEO_CONFIG.subtitleConfig.clozedTextStyle?.whiteSpace,
            VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.wordWrap || VIDEO_CONFIG.subtitleConfig.clozedTextStyle?.wordWrap,
            VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.overflowWrap || VIDEO_CONFIG.subtitleConfig.clozedTextStyle?.overflowWrap,
            chineseTextWrapping
        );
        console.log(`[${functionName}] 换行配置检测:`, {
            chineseWhiteSpace: VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.whiteSpace,
            chineseWordWrap: VIDEO_CONFIG.subtitleConfig.bilingualTextStyle?.chineseStyle?.wordWrap,
            chineseTextWrapping: chineseTextWrapping,
            finalWrapStyle: wrapConfig.wrapStyle
        });
        assContent += `WrapStyle: ${wrapConfig.wrapStyle}\n`;
        assContent += '\n';

        // 加载配置并映射所有样式
        await cssMapper.loadConfig();
        await cssMapper.mapAllStyles();

        // 生成ASS样式头部
        const assStylesHeader = cssMapper.generateASSStylesHeader();
        assContent += assStylesHeader;

        console.log(`[${functionName}] ✅ ASS样式定义生成完成，使用CSS映射器`);
        console.log(`[${functionName}] 样式数量: ${cssMapper.assStyles.size}个`);

        assContent += '\n';

        // [Events] 部分
        assContent += '[Events]\n';
        assContent += 'Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n';

        // 添加视频标题事件
        if (context.videoTitleJsonArray && context.videoTitleJsonArray.length > 0) {
            const titleData = context.videoTitleJsonArray[0];
            const startTime = secondsToAssTimestamp(titleData.start);
            const endTime = secondsToAssTimestamp(titleData.end);

            // 使用style配置中的位置信息，处理CSS格式
            const style = titleData.style;
            const x = parseInt(style.position.x.replace('px', ''));
            const y1 = parseInt(style.position.y1.replace('px', ''));
            const y2 = parseInt(style.position.y2.replace('px', ''));

            // 第一行标题
            if (titleData.text_1) {
                assContent += `Dialogue: 0,${startTime},${endTime},VideoTitle,,0,0,0,,{\\pos(${x},${y1})}${titleData.text_1}\n`;
            }

            // 第二行标题
            if (titleData.text_2) {
                assContent += `Dialogue: 0,${startTime},${endTime},VideoTitle,,0,0,0,,{\\pos(${x},${y2})}${titleData.text_2}\n`;
            }
        }

        // 添加填空字幕事件（使用智能布局）
        if (context.clozedSubtitleJsonArray && Array.isArray(context.clozedSubtitleJsonArray)) {
            const clozedStyle = VIDEO_CONFIG.subtitleConfig.clozedTextStyle;

            // 使用纯CSS样式控制（移除自动换行逻辑）
            console.log('[generateAssSubtitle] 使用纯CSS样式控制生成填空字幕（无自动换行）');

            context.clozedSubtitleJsonArray.forEach(item => {
                const startTime = secondsToAssTimestamp(item.start);
                const endTime = secondsToAssTimestamp(item.end);
                const text = item.text.replace(/\n/g, ' '); // 保持单行文本

                // 使用固定位置，不进行自动换行，由CSS样式控制显示效果
                const centerX = 540; // 1080px / 2，居中对齐
                const centerY = 928; // 固定Y位置，在textarea区域中心

                // 生成单行ASS事件，由CSS样式控制显示效果
                assContent += `Dialogue: 1,${startTime},${endTime},ClozedText,,0,0,0,,{\\pos(${centerX},${centerY})}${text}\n`;
            });
        }

        // 添加双语字幕事件（使用分离的英文和中文样式）
        if (context.bilingualSubtitleJsonArray && Array.isArray(context.bilingualSubtitleJsonArray)) {
            const bilingualConfig = VIDEO_CONFIG.subtitleConfig.bilingualTextStyle;

            // 使用纯CSS样式控制（移除自动换行逻辑）
            console.log('[generateAssSubtitle] 使用纯CSS样式控制生成双语字幕（无自动换行）');

            context.bilingualSubtitleJsonArray.forEach(item => {
                const startTime = secondsToAssTimestamp(item.start);
                const endTime = secondsToAssTimestamp(item.end);

                // BilingualChinese智能换行策略：英文单行，中文智能换行
                // 英文强制单行（保持与原策略一致）
                const englishText = item.text_english ? item.text_english.replace(/\n/g, ' ') : '';

                // 中文智能换行（专门为BilingualChinese设计）
                let chineseText = item.text_chinese || '';
                if (chineseText) {
                    console.log(`[generateAssSubtitle] 对BilingualChinese应用智能中文换行`);
                    chineseText = addChineseSmartLineBreaks(chineseText, 15); // 每行最多15个字符
                }

                if (englishText && chineseText) {
                    // 提取关键词解释对象（用于英文翻译注释处理）
                    const wordsExplanation = item.words_explanation || {};

                    console.log(`[generateAssSubtitle] 处理英文关键词翻译注释: ${Object.keys(wordsExplanation).length}个关键词`);
                    console.log(`[generateAssSubtitle] 关键词列表: [${Object.keys(wordsExplanation).join(', ')}]`);

                    // 处理英文关键词翻译注释
                    const processedEnglishText = processEnglishKeywordsWithTranslation(englishText, wordsExplanation, bilingualConfig.keywordBlockStyle);

                    // 方案1：Canvas文本测量 + 动态Y坐标计算
                    console.log(`[generateAssSubtitle] 应用方案1：动态测量英文文本体积`);
                    const englishStyle = bilingualConfig.englishStyle;
                    const positionResult = calculateBilingualChinesePosition(processedEnglishText, englishStyle);

                    console.log(`[generateAssSubtitle] 英文文本体积测量结果:`, positionResult);

                    // 使用测量结果，不进行自动换行，由CSS样式控制显示效果
                    const centerX = 540; // 1080px / 2，居中对齐
                    const englishY = 800; // 英文字幕Y位置（固定）
                    const chineseY = positionResult.chineseY; // 中文字幕Y位置（动态计算）

                    // 生成英文字幕事件（单行，包含关键词翻译注释）
                    assContent += `Dialogue: 2,${startTime},${endTime},BilingualEnglish,,0,0,0,,{\\pos(${centerX},${englishY})}${processedEnglishText}\n`;

                    // 生成中文字幕事件（单行）
                    assContent += `Dialogue: 2,${startTime},${endTime},BilingualChinese,,0,0,0,,{\\pos(${centerX},${chineseY})}${chineseText}\n`;
                }
            });
        }

        // 添加单元引导字幕事件（使用智能布局+动画特效）
        if (context.repeatModeGuideJsonArray && Array.isArray(context.repeatModeGuideJsonArray)) {
            const repeatModeStyle = VIDEO_CONFIG.subtitleConfig.repeatModeStyle;

            // 使用纯CSS样式控制（移除自动换行逻辑）
            console.log('[generateAssSubtitle] 使用纯CSS样式控制生成单元引导字幕（无自动换行）');

            const effectDuration = 0.1; // 特效持续时间（秒）

            context.repeatModeGuideJsonArray.forEach(item => {
                const originalStart = item.start;
                const originalEnd = item.end;
                const text = item.text.replace(/\n/g, ' '); // 保持单行文本

                // 使用固定位置，不进行自动换行，由CSS样式控制显示效果
                const centerX = 540; // 1080px / 2，居中对齐
                const centerY = 1450; // 固定Y位置，在屏幕底部

                // 计算特效时间点
                const fadeInStart = originalStart;
                const fadeInEnd = originalStart + effectDuration;
                const stableStart = fadeInEnd;
                const stableEnd = originalEnd - effectDuration;
                const fadeOutStart = stableEnd;
                const fadeOutEnd = originalEnd;

                // 转换为ASS时间戳
                const fadeInStartTime = secondsToAssTimestamp(fadeInStart);
                const fadeInEndTime = secondsToAssTimestamp(fadeInEnd);
                const stableStartTime = secondsToAssTimestamp(stableStart);
                const stableEndTime = secondsToAssTimestamp(stableEnd);
                const fadeOutStartTime = secondsToAssTimestamp(fadeOutStart);
                const fadeOutEndTime = secondsToAssTimestamp(fadeOutEnd);

                const enterY = centerY + 50; // 起始位置向下偏移
                const exitY = centerY - 50; // 结束位置向上偏移

                // 进入特效：从下方上浮并淡入
                assContent += `Dialogue: 3,${fadeInStartTime},${fadeInEndTime},RepeatModeGuide,,0,0,0,,{\\move(${centerX},${enterY},${centerX},${centerY})\\fad(100,0)\\t(0,100,\\alpha&H00&)}${text}\n`;

                // 稳定显示阶段
                if (stableStart < stableEnd) {
                    assContent += `Dialogue: 3,${stableStartTime},${stableEndTime},RepeatModeGuide,,0,0,0,,{\\pos(${centerX},${centerY})}${text}\n`;
                }

                // 退出特效：向上浮动并淡出
                assContent += `Dialogue: 3,${fadeOutStartTime},${fadeOutEndTime},RepeatModeGuide,,0,0,0,,{\\move(${centerX},${centerY},${centerX},${exitY})\\fad(0,100)\\t(0,100,\\alpha&HFF&)}${text}\n`;
            });
        }

        console.log(`[${functionName}] ASS内容生成完成，总长度: ${assContent.length} 字符`);

        // 写入文件
        try {
            await fs.writeFile(assFilePath, assContent, 'utf8');
            console.log(`[${functionName}] ✅ ASS文件写入成功: ${assFilePath}`);

            // 验证文件是否存在
            const stats = await fs.stat(assFilePath);
            console.log(`[${functionName}] 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);

            return assFilePath;

        } catch (writeError) {
            if (writeError.code === 'EBUSY') {
                const errorMsg = `❌ 文件写入失败: ${OUTPUT_ASS_FILENAME} 文件可能正在被其他程序打开，请关闭后重试`;
                console.error(`[${functionName}] ${errorMsg}`);
                throw new Error(errorMsg);
            } else if (writeError.code === 'EACCES') {
                const errorMsg = `❌ 文件写入失败: 没有权限写入 ${assFilePath}`;
                console.error(`[${functionName}] ${errorMsg}`);
                throw new Error(errorMsg);
            } else {
                const errorMsg = `❌ 文件写入失败: ${writeError.message}`;
                console.error(`[${functionName}] ${errorMsg}`);
                throw new Error(errorMsg);
            }
        }

    } catch (error) {
        console.error(`[${functionName}] 生成ASS字幕文件失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 主执行函数，协调整个JSON到ASS转换测试流程
 * @执行流程:
 *   1. 加载填空字幕和双语字幕JSON数据
 *   2. 模拟context对象结构
 *   3. 输出数据结构信息用于验证
 * @错误处理:
 *   - 任何步骤失败都会终止整个流程
 *   - 记录详细错误信息并以错误码退出
 */
async function main() {
    const functionName = 'main';

    console.log('main函数开始执行...');

    try {
        console.log(`[${functionName}] ========== 开始JSON到ASS转换测试 ==========`);

        // 步骤 1: 加载字幕数据
        console.log(`[${functionName}] 步骤1: 加载字幕数据`);
        const context = await loadSubtitleData();

        // 步骤 2: 输出数据结构信息
        console.log(`[${functionName}] 步骤2: 验证数据结构`);
        console.log(`[${functionName}] context.clozedSubtitleJsonArray:`,
                   typeof context.clozedSubtitleJsonArray,
                   Array.isArray(context.clozedSubtitleJsonArray) ? `(${context.clozedSubtitleJsonArray.length} items)` : '');
        console.log(`[${functionName}] context.bilingualSubtitleJsonArray:`,
                   typeof context.bilingualSubtitleJsonArray,
                   Array.isArray(context.bilingualSubtitleJsonArray) ? `(${context.bilingualSubtitleJsonArray.length} items)` : '');

        // 输出第一个条目的结构（如果存在）
        if (Array.isArray(context.clozedSubtitleJsonArray) && context.clozedSubtitleJsonArray.length > 0) {
            console.log(`[${functionName}] 填空字幕第一个条目结构:`, Object.keys(context.clozedSubtitleJsonArray[0]));
        }

        if (Array.isArray(context.bilingualSubtitleJsonArray) && context.bilingualSubtitleJsonArray.length > 0) {
            console.log(`[${functionName}] 双语字幕第一个条目结构:`, Object.keys(context.bilingualSubtitleJsonArray[0]));
        }

        // 步骤 3: 生成单元素JSON数据
        console.log(`[${functionName}] 步骤3: 生成视频引导语JSON数据`);
        const singleElementJsonArray = await generateSingleElementJson();

        // 步骤 4: 生成单元引导字幕JSON数据
        console.log(`[${functionName}] 步骤4: 生成单元引导字幕JSON数据`);
        // 获取原始音频时长用于计算时间段
        const originalAudioDuration = await getAudioDuration(ORIGINAL_AUDIO_PATH);
        const repeatModeGuideJsonArray = await generateRepeatModeGuideJson(VIDEO_CONFIG.repeatCount, originalAudioDuration);

        // 步骤 5: 根据repeatModes处理字幕数据时间偏移
        console.log(`[${functionName}] 步骤5: 根据repeatModes处理字幕数据时间偏移`);
        const processedContext = await processSubtitleDataByRepeatModes(context, originalAudioDuration);

        // 步骤 6: 模拟完整的context对象
        console.log(`[${functionName}] 步骤6: 模拟完整context对象`);
        const fullContext = {
            clozedSubtitleJsonArray: processedContext.clozedSubtitleJsonArray,
            bilingualSubtitleJsonArray: processedContext.bilingualSubtitleJsonArray,
            videoTitleJsonArray: singleElementJsonArray, // 视频标题JSON数组
            repeatModeGuideJsonArray: repeatModeGuideJsonArray, // 单元引导字幕JSON数组
            SAVE_PATH: SAVE_PATH // 保存路径
        };

        console.log(`[${functionName}] ========== JSON数据加载和验证完成 ==========`);
        console.log(`[${functionName}] ✅ 成功加载填空字幕数据`);
        console.log(`[${functionName}] ✅ 成功加载双语字幕数据`);
        console.log(`[${functionName}] ✅ 视频引导语JSON数据生成完成`);
        console.log(`[${functionName}] ✅ 单元引导字幕JSON数据生成完成`);
        console.log(`[${functionName}] ✅ 字幕数据时间偏移处理完成`);
        console.log(`[${functionName}] ✅ 完整context对象模拟完成`);
        console.log(`[${functionName}] 📊 VIDEO_CONFIG.subtitleConfig.videoGuide配置: title1="${VIDEO_CONFIG.subtitleConfig.videoGuide.title1}", title2="${VIDEO_CONFIG.subtitleConfig.videoGuide.title2}"`);
        console.log(`[${functionName}] 📊 videoTitleJsonArray:`, JSON.stringify(singleElementJsonArray[0], null, 2));
        console.log(`[${functionName}] 📊 repeatModeGuideJsonArray:`, JSON.stringify(repeatModeGuideJsonArray, null, 2));
        console.log(`[${functionName}] 📊 处理后的填空字幕数量: ${fullContext.clozedSubtitleJsonArray ? fullContext.clozedSubtitleJsonArray.length : 0}`);
        console.log(`[${functionName}] 📊 处理后的双语字幕数量: ${fullContext.bilingualSubtitleJsonArray ? fullContext.bilingualSubtitleJsonArray.length : 0}`);
        console.log(`[${functionName}] 📊 context.SAVE_PATH: ${fullContext.SAVE_PATH}`);

        // 步骤 7: 生成ASS字幕文件
        console.log(`[${functionName}] 步骤7: 生成ASS字幕文件`);
        const assFilePath = await generateAssSubtitle(fullContext);

        console.log(`[${functionName}] ========== ASS字幕文件生成完成 ==========`);
        console.log(`[${functionName}] ✅ ASS文件生成成功: ${assFilePath}`);
        console.log(`[${functionName}] ✅ 包含视频标题字幕`);
        console.log(`[${functionName}] ✅ 包含填空字幕 (${fullContext.clozedSubtitleJsonArray.length} 条)`);
        console.log(`[${functionName}] ✅ 包含双语字幕 (${fullContext.bilingualSubtitleJsonArray.length} 条)`);

        console.log('测试完成，准备退出...');
        process.exit(0);

    } catch (error) {
        console.error(`[${functionName}] ❌ JSON到ASS转换测试失败: ${error.message}`);
        console.error('错误详情:', error);
        process.exit(1);
    }
}

/**
 * @脚本启动: 立即执行主函数并处理未捕获异常
 */
console.log('开始执行JSON到ASS转换测试脚本...');

main().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
});

/**
 * @使用说明:
 *
 * 运行测试:
 * cd backend/src/tasks/tests/test-output
 * node generate-aas.test.js
 *
 * 预期结果:
 * - 成功读取两个JSON文件
 * - 输出数据结构信息
 * - 模拟context对象完成
 *
 * 如果测试失败:
 * 1. 检查JSON文件是否存在
 * 2. 检查JSON文件格式是否正确
 * 3. 检查文件权限
 */

