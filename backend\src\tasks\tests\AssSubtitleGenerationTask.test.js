/**
 * @fileoverview AssSubtitleGenerationTask 单元测试
 * @description 测试ASS字幕生成任务的各种场景和边界情况
 */

const AssSubtitleGenerationTask = require('../AssSubtitleGenerationTask');
const { TASK_STATUS } = require('../../constants/progress');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

describe('AssSubtitleGenerationTask', () => {
    let task;
    let testContext;
    let tempDir;

    beforeEach(async () => {
        // 创建任务实例
        task = new AssSubtitleGenerationTask();
        
        // 创建临时目录
        tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'ass-test-'));
        
        // 准备测试上下文
        testContext = {
            videoIdentifier: 'test_video_001',
            bilingualSubtitleJsonArray: [
                {
                    id: "1",
                    start: 0,
                    end: 3.5,
                    text_english: "Hello, welcome to our video tutorial.",
                    text_chinese: "你好，欢迎观看我们的视频教程。"
                },
                {
                    id: "2",
                    start: 3.5,
                    end: 7.2,
                    text_english: "Today we will learn about video processing.",
                    text_chinese: "今天我们将学习视频处理。"
                },
                {
                    id: "3",
                    start: 7.2,
                    end: 11.8,
                    text_english: "This is an important topic for content creators.",
                    text_chinese: "这对内容创作者来说是一个重要话题。"
                }
            ],
            compositionConfig: {
                assStyleParams: {
                    fontSize: 32,
                    fontName: 'Arial',
                    primaryColor: '#FFFFFF',
                    secondaryColor: '#000000',
                    outlineColor: '#000000',
                    backColor: '#80000000',
                    bold: false,
                    highlightWords: {
                        enabled: true,
                        color: '#FF0000',
                        words: ['important', 'video']
                    }
                }
            },
            savePath: tempDir
        };
    });

    afterEach(async () => {
        // 清理临时目录
        try {
            await fs.rmdir(tempDir, { recursive: true });
        } catch (error) {
            // 忽略清理错误
        }
    });

    describe('构造函数', () => {
        test('应该正确初始化任务', () => {
            expect(task.name).toBe('AssSubtitleGenerationTask');
            expect(task.status).toBe(TASK_STATUS.PENDING);
            expect(task.taskId).toContain('AssSubtitleGenerationTask');
        });
    });

    describe('execute方法', () => {
        test('应该成功生成ASS字幕', async () => {
            const progressCallback = jest.fn();
            
            const result = await task.execute(testContext, progressCallback);
            
            // 验证返回结果
            expect(result).toHaveProperty('assSubtitlePath');
            expect(result).toHaveProperty('assSubtitleContent');
            expect(result).toHaveProperty('assStyleParams');
            expect(result).toHaveProperty('assSubtitleGenerationTaskStatus', 'success');
            
            // 验证ASS内容包含必要部分
            const assContent = result.assSubtitleContent;
            expect(assContent).toContain('[Script Info]');
            expect(assContent).toContain('[V4+ Styles]');
            expect(assContent).toContain('[Events]');
            expect(assContent).toContain('Dialogue:');
            
            // 验证任务状态
            expect(task.status).toBe(TASK_STATUS.COMPLETED);
            
            // 验证进度回调被调用
            expect(progressCallback).toHaveBeenCalled();
        });

        test('应该正确保存ASS文件', async () => {
            const result = await task.execute(testContext);
            
            // 验证文件是否存在
            const filePath = result.assSubtitlePath;
            expect(await fs.access(filePath).then(() => true).catch(() => false)).toBe(true);
            
            // 验证文件内容
            const fileContent = await fs.readFile(filePath, 'utf8');
            expect(fileContent).toContain('[Script Info]');
            expect(fileContent).toContain('Hello, welcome to our video tutorial.');
        });

        test('应该处理缺少必需字段的情况', async () => {
            const invalidContext = { ...testContext };
            delete invalidContext.videoIdentifier;
            
            await expect(task.execute(invalidContext)).rejects.toThrow();
            expect(task.status).toBe(TASK_STATUS.FAILED);
        });

        test('应该处理空双语字幕数组', async () => {
            const emptyContext = {
                ...testContext,
                bilingualSubtitleJsonArray: []
            };
            
            await expect(task.execute(emptyContext)).rejects.toThrow('双语字幕数组不能为空');
        });

        test('应该处理无效的双语字幕结构', async () => {
            const invalidStructureContext = {
                ...testContext,
                bilingualSubtitleJsonArray: [
                    { id: "1", start: 0 } // 缺少必需字段
                ]
            };
            
            await expect(task.execute(invalidStructureContext)).rejects.toThrow();
        });

        test('应该使用默认样式参数', async () => {
            const contextWithoutStyle = {
                ...testContext,
                compositionConfig: null
            };
            
            const result = await task.execute(contextWithoutStyle);
            
            // 验证使用了默认样式参数
            expect(result.assStyleParams.fontSize).toBe(32);
            expect(result.assStyleParams.fontName).toBe('Arial');
            expect(result.assStyleParams.primaryColor).toBe('#FFFFFF');
        });
    });

    describe('parseAssStyleParams方法', () => {
        test('应该正确解析样式参数', () => {
            const config = {
                assStyleParams: {
                    fontSize: 36,
                    fontName: 'Microsoft YaHei',
                    primaryColor: '#FF0000'
                }
            };
            
            const result = task.parseAssStyleParams(config, '[TEST]');
            
            expect(result.fontSize).toBe(36);
            expect(result.fontName).toBe('Microsoft YaHei');
            expect(result.primaryColor).toBe('#FF0000');
        });

        test('应该使用默认值填充缺失参数', () => {
            const config = {
                assStyleParams: {
                    fontSize: 28
                }
            };
            
            const result = task.parseAssStyleParams(config, '[TEST]');
            
            expect(result.fontSize).toBe(28);
            expect(result.fontName).toBe('Arial'); // 默认值
            expect(result.primaryColor).toBe('#FFFFFF'); // 默认值
        });

        test('应该验证fontSize范围', () => {
            const config = {
                assStyleParams: {
                    fontSize: 100 // 超出范围
                }
            };
            
            const result = task.parseAssStyleParams(config, '[TEST]');
            
            expect(result.fontSize).toBe(32); // 应该使用默认值
        });
    });

    describe('convertColorToAss方法', () => {
        test('应该正确转换6位十六进制颜色', () => {
            const result = task.convertColorToAss('#FF0000');
            expect(result).toBe('&H000000FF'); // 红色：RRGGBB → BBGGRR
        });

        test('应该正确转换3位十六进制颜色', () => {
            const result = task.convertColorToAss('#F00');
            expect(result).toBe('&H000000FF'); // 红色：RGB → RRGGBB → BBGGRR
        });

        test('应该处理8位颜色值', () => {
            const result = task.convertColorToAss('#80FF0000');
            expect(result).toBe('&H000000FF'); // 忽略透明度
        });

        test('应该处理无效颜色值', () => {
            const result = task.convertColorToAss('invalid');
            expect(result).toBe('&H00FFFFFF'); // 默认白色
        });

        test('应该处理没有#号的颜色值', () => {
            const result = task.convertColorToAss('FF0000');
            expect(result).toBe('&H000000FF');
        });
    });

    describe('formatAssTime方法', () => {
        test('应该正确格式化时间', () => {
            expect(task.formatAssTime(0)).toBe('0:00:00.00');
            expect(task.formatAssTime(3.5)).toBe('0:00:03.50');
            expect(task.formatAssTime(65.25)).toBe('0:01:05.25');
            expect(task.formatAssTime(3661.75)).toBe('1:01:01.75');
        });

        test('应该处理小数秒', () => {
            expect(task.formatAssTime(1.234)).toBe('0:00:01.23');
            expect(task.formatAssTime(1.999)).toBe('0:00:01.99');
        });
    });

    describe('applyTextStyling方法', () => {
        test('应该应用重点词汇高亮', () => {
            const styleParams = {
                highlightWords: {
                    enabled: true,
                    color: '#FF0000',
                    words: ['important', 'video']
                }
            };
            
            const result = task.applyTextStyling('This is important video content', styleParams, '[TEST]');
            
            expect(result).toContain('{\\c&H000000FF\\b1}important{\\r}');
            expect(result).toContain('{\\c&H000000FF\\b1}video{\\r}');
        });

        test('应该转义ASS特殊字符', () => {
            const styleParams = { highlightWords: { enabled: false } };
            
            const result = task.applyTextStyling('Text with {brackets} and \\backslash', styleParams, '[TEST]');
            
            expect(result).toContain('\\{brackets\\}');
            expect(result).toContain('\\\\backslash');
        });

        test('应该处理换行符', () => {
            const styleParams = { highlightWords: { enabled: false } };
            
            const result = task.applyTextStyling('Line 1\nLine 2', styleParams, '[TEST]');
            
            expect(result).toContain('Line 1\\NLine 2');
        });
    });

    describe('generateAssContent方法', () => {
        test('应该生成完整的ASS内容', () => {
            const styleParams = {
                fontSize: 32,
                fontName: 'Arial',
                primaryColor: '#FFFFFF',
                highlightWords: { enabled: false }
            };
            
            const result = task.generateAssContent(testContext.bilingualSubtitleJsonArray, styleParams, '[TEST]');
            
            expect(result).toContain('[Script Info]');
            expect(result).toContain('[V4+ Styles]');
            expect(result).toContain('[Events]');
            expect(result).toContain('Hello, welcome to our video tutorial.');
        });

        test('应该包含正确的样式定义', () => {
            const styleParams = {
                fontSize: 36,
                fontName: 'Microsoft YaHei',
                primaryColor: '#FF0000',
                bold: true,
                highlightWords: { enabled: false }
            };
            
            const result = task.generateAssContent(testContext.bilingualSubtitleJsonArray, styleParams, '[TEST]');
            
            expect(result).toContain('Microsoft YaHei,36');
            expect(result).toContain('&H000000FF'); // 红色
            expect(result).toContain(',1,'); // bold = true
        });
    });
});
