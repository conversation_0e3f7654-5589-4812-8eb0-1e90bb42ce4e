# &lt;OffthreadVideo&gt;

## 概述

`<OffthreadVideo>` 组件用于导入和显示视频，类似于 [`<Video>`](./Video.md)，但在渲染期间，它会使用 FFmpeg 在浏览器外部提取视频的确切帧，并在 [`<Img>`](./Img.md) 标签中显示。

这个组件旨在解决默认 `<Video>` 元素的限制。参见：[`<Video>` vs `<OffthreadVideo>`](/docs/video-vs-offthreadvideo)。

**版本要求**: Remotion 3.0.11+

## 语法

```typescript
import { OffthreadVideo } from "remotion";

<OffthreadVideo src="视频路径" />
```

## 核心属性

### src (必需)
- **类型**: `string`
- **描述**: 要渲染的视频 URL，可以是远程 URL 或使用 [`staticFile()`](./staticFile.md) 引用的本地文件

### trimBefore (可选)
- **类型**: `number`
- **版本要求**: v4.0.319+
- **描述**: 移除视频开头的部分（左侧），以帧为单位

### trimAfter (可选)
- **类型**: `number`
- **版本要求**: v4.0.319+
- **描述**: 移除视频结尾的部分（右侧），以帧为单位

### transparent (可选)
- **类型**: `boolean`
- **默认值**: `false`
- **版本要求**: v4.0.0+
- **描述**: 如果设置为 `true`，帧将提取为 PNG 格式，支持透明度但渲染较慢

### volume (可选)
- **类型**: `number | ((frame: number) => number)`
- **描述**: 控制整个轨道的音量或基于帧的音量变化

### playbackRate (可选)
- **类型**: `number`
- **默认值**: `1`
- **版本要求**: v2.2.0+
- **描述**: 控制视频播放速度

### muted (可选)
- **类型**: `boolean`
- **描述**: 静音视频音频

## 基础用法

### 1. 本地视频文件

```typescript
import { AbsoluteFill, OffthreadVideo, staticFile } from 'remotion';

export const MyVideo = () => {
  return (
    <AbsoluteFill>
      <OffthreadVideo src={staticFile('video.webm')} />
    </AbsoluteFill>
  );
};
```

### 2. 远程视频 URL

```typescript
import { AbsoluteFill, OffthreadVideo } from 'remotion';

export const MyComposition = () => {
  return (
    <AbsoluteFill>
      <OffthreadVideo src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" />
    </AbsoluteFill>
  );
};
```

### 3. 视频裁剪

```typescript
import { AbsoluteFill, OffthreadVideo, staticFile } from 'remotion';

export const TrimmedVideo = () => {
  return (
    <AbsoluteFill>
      <OffthreadVideo 
        src={staticFile('video.webm')} 
        trimBefore={60}  // 跳过前2秒 (假设fps=30)
        trimAfter={120}  // 在4秒处结束
      />
    </AbsoluteFill>
  );
};
```

## 实际应用场景

### 1. 背景视频处理

```typescript
import React from 'react';
import { OffthreadVideo, staticFile, useVideoConfig, interpolate, useCurrentFrame } from 'remotion';

interface BackgroundVideoProps {
  videoSrc: string;
  opacity?: number;
  blur?: number;
}

const BackgroundVideo: React.FC<BackgroundVideoProps> = ({ 
  videoSrc, 
  opacity = 0.7, 
  blur = 0 
}) => {
  const { width, height } = useVideoConfig();
  const frame = useCurrentFrame();

  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      overflow: 'hidden'
    }}>
      <OffthreadVideo
        src={staticFile(videoSrc)}
        style={{
          width: width * 1.1, // 稍微放大以避免边缘
          height: height * 1.1,
          objectFit: 'cover',
          opacity: opacity,
          filter: blur > 0 ? `blur(${blur}px)` : 'none',
          transform: 'translate(-5%, -5%)' // 居中放大的视频
        }}
        volume={0} // 背景视频通常静音
        transparent={false} // 背景不需要透明度
      />
      
      {/* 可选的遮罩层 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: `rgba(0,0,0,${0.3})`,
        pointerEvents: 'none'
      }} />
    </div>
  );
};

export default BackgroundVideo;
```

### 2. 多视频拼接

```typescript
import React from 'react';
import { OffthreadVideo, staticFile, Sequence, useVideoConfig } from 'remotion';

interface VideoClip {
  src: string;
  startFrame: number;
  durationInFrames: number;
  trimBefore?: number;
  trimAfter?: number;
}

interface VideoMontageProps {
  clips: VideoClip[];
}

const VideoMontage: React.FC<VideoMontageProps> = ({ clips }) => {
  const { width, height } = useVideoConfig();

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {clips.map((clip, index) => (
        <Sequence
          key={index}
          from={clip.startFrame}
          durationInFrames={clip.durationInFrames}
        >
          <OffthreadVideo
            src={staticFile(clip.src)}
            trimBefore={clip.trimBefore}
            trimAfter={clip.trimAfter}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
            transparent={false}
          />
        </Sequence>
      ))}
    </div>
  );
};

// 使用示例
export const MontageExample = () => {
  const videoClips: VideoClip[] = [
    {
      src: 'clip1.mp4',
      startFrame: 0,
      durationInFrames: 90,
      trimBefore: 30,
      trimAfter: 120
    },
    {
      src: 'clip2.mp4',
      startFrame: 90,
      durationInFrames: 120,
      trimBefore: 0,
      trimAfter: 150
    },
    {
      src: 'clip3.mp4',
      startFrame: 210,
      durationInFrames: 150
    }
  ];

  return <VideoMontage clips={videoClips} />;
};
```

### 3. 透明视频叠加

```typescript
import React from 'react';
import { OffthreadVideo, staticFile, AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';

interface TransparentOverlayProps {
  backgroundVideo: string;
  overlayVideo: string;
  overlayOpacity?: number;
}

const TransparentOverlay: React.FC<TransparentOverlayProps> = ({ 
  backgroundVideo, 
  overlayVideo, 
  overlayOpacity = 1 
}) => {
  const frame = useCurrentFrame();

  // 动态透明度效果
  const dynamicOpacity = interpolate(
    frame,
    [0, 30, 60, 90],
    [0, overlayOpacity, overlayOpacity, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <AbsoluteFill>
      {/* 背景视频 */}
      <OffthreadVideo
        src={staticFile(backgroundVideo)}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
        transparent={false}
      />
      
      {/* 透明叠加视频 */}
      <OffthreadVideo
        src={staticFile(overlayVideo)}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          opacity: dynamicOpacity
        }}
        transparent={true} // 启用透明度支持
        volume={0} // 叠加视频通常静音
      />
    </AbsoluteFill>
  );
};

export default TransparentOverlay;
```

### 4. 视频速度控制

```typescript
import React from 'react';
import { OffthreadVideo, staticFile, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface SpeedControlledVideoProps {
  videoSrc: string;
  speedCurve?: 'linear' | 'ease-in' | 'ease-out' | 'custom';
}

const SpeedControlledVideo: React.FC<SpeedControlledVideoProps> = ({ 
  videoSrc, 
  speedCurve = 'linear' 
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const getPlaybackRate = () => {
    switch (speedCurve) {
      case 'ease-in':
        return interpolate(
          frame,
          [0, durationInFrames / 2, durationInFrames],
          [0.5, 1, 2],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );
      case 'ease-out':
        return interpolate(
          frame,
          [0, durationInFrames / 2, durationInFrames],
          [2, 1, 0.5],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );
      case 'custom':
        // 自定义速度曲线：慢-快-慢
        return interpolate(
          frame,
          [0, durationInFrames * 0.25, durationInFrames * 0.75, durationInFrames],
          [0.3, 2, 2, 0.3],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );
      default:
        return 1;
    }
  };

  return (
    <OffthreadVideo
      src={staticFile(videoSrc)}
      playbackRate={getPlaybackRate()}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      }}
    />
  );
};

export default SpeedControlledVideo;
```

### 5. 音量控制和淡入淡出

```typescript
import React from 'react';
import { OffthreadVideo, staticFile, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface AudioControlledVideoProps {
  videoSrc: string;
  fadeInDuration?: number;
  fadeOutDuration?: number;
  maxVolume?: number;
}

const AudioControlledVideo: React.FC<AudioControlledVideoProps> = ({ 
  videoSrc, 
  fadeInDuration = 30, 
  fadeOutDuration = 30,
  maxVolume = 1
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const getVolume = (currentFrame: number) => {
    // 淡入效果
    if (currentFrame < fadeInDuration) {
      return interpolate(
        currentFrame,
        [0, fadeInDuration],
        [0, maxVolume],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );
    }
    
    // 淡出效果
    if (currentFrame > durationInFrames - fadeOutDuration) {
      return interpolate(
        currentFrame,
        [durationInFrames - fadeOutDuration, durationInFrames],
        [maxVolume, 0],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );
    }
    
    // 中间部分保持最大音量
    return maxVolume;
  };

  return (
    <OffthreadVideo
      src={staticFile(videoSrc)}
      volume={getVolume}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      }}
    />
  );
};

export default AudioControlledVideo;
```

## 与 Video 组件的区别

| 特性 | OffthreadVideo | Video |
|------|----------------|-------|
| 渲染方式 | FFmpeg 提取帧 | 浏览器原生播放 |
| 性能 | 渲染时更稳定 | 预览时更流畅 |
| 透明度支持 | 支持（PNG 提取） | 有限支持 |
| 循环播放 | 需要自定义实现 | 原生支持 |
| 色彩准确性 | 更高（色调映射） | 依赖浏览器 |
| 渲染速度 | 较慢（帧提取） | 较快 |

## 支持的编解码器

- H.264 ("MP4")
- H.265 ("HEVC")
- VP8 和 VP9 ("WebM")
- AV1 (v4.0.6+)
- ProRes

## 性能优化建议

1. **透明度**: 只在需要透明度时设置 `transparent={true}`
2. **色调映射**: 如果不关心色彩准确性，可设置 `toneMapped={false}`
3. **音频处理**: 背景视频考虑设置 `volume={0}` 或 `muted={true}`
4. **文件格式**: 选择适合的视频编解码器和容器格式

## 最佳实践

1. **文件优化**: 使用适当的视频编解码器和比特率
2. **错误处理**: 提供 `onError` 回调处理加载失败
3. **性能考虑**: 根据需求选择是否启用透明度和色调映射
4. **音频管理**: 合理控制音量和音频处理
5. **循环播放**: 使用自定义循环组件实现循环播放

## 常见用例

- 背景视频处理
- 多视频拼接
- 透明视频叠加
- 视频速度控制
- 音频淡入淡出
- 高质量视频渲染

## 相关 API

- [`<Video>`](./Video.md) - 标准视频组件
- [`staticFile()`](./staticFile.md) - 静态文件访问
- [`interpolate()`](./interpolate.md) - 数值插值
- [`<Sequence>`](./Sequence.md) - 时间序列
- [`<Loop>`](./Loop.md) - 循环播放

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/video/OffthreadVideo.tsx)
