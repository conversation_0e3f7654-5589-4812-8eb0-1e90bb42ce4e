/**
 * Cloudflare API Segments 验证测试
 * 验证修复后的segments数量和words数据
 */

const GetTranscriptionTaskByCloudflare = require('../GetTranscriptionTaskByCloudflare');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    reqId: 'segments-verification-test',
    videoIdentifier: 'test_segments_verification',
    audioFilePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3',
    savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
};

async function verifyCloudflareSegments() {
    console.log('🔍 开始验证 Cloudflare API segments 修复结果');
    console.log('📊 测试音频文件:', TEST_CONFIG.audioFilePath);
    
    // 检查音频文件是否存在
    if (!fs.existsSync(TEST_CONFIG.audioFilePath)) {
        console.error('❌ 测试音频文件不存在:', TEST_CONFIG.audioFilePath);
        return;
    }
    
    const audioStats = fs.statSync(TEST_CONFIG.audioFilePath);
    console.log('📁 音频文件大小:', (audioStats.size / 1024 / 1024).toFixed(2), 'MB');
    
    try {
        // 创建任务实例
        const task = new GetTranscriptionTaskByCloudflare();
        
        // 构建上下文
        const context = {
            reqId: TEST_CONFIG.reqId,
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            audioFilePathInUploads: TEST_CONFIG.audioFilePath,
            savePath: TEST_CONFIG.savePath
        };
        
        console.log('🚀 开始调用 Cloudflare Workers AI API...');
        const startTime = Date.now();
        
        // 执行转录任务
        const result = await task.execute(context);
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log('✅ API 调用成功，耗时:', duration, '秒');
        console.log('📊 转录状态:', result.transcriptionStatus);
        
        // 读取生成的转录文件
        const transcriptionPath = path.join(TEST_CONFIG.savePath, `${TEST_CONFIG.videoIdentifier}_transcription.json`);
        if (fs.existsSync(transcriptionPath)) {
            const transcriptionData = JSON.parse(fs.readFileSync(transcriptionPath, 'utf8'));
            
            console.log('\n📋 转录结果分析:');
            console.log('- 语言:', transcriptionData.language);
            console.log('- 时长:', transcriptionData.duration, '秒');
            console.log('- 文本长度:', transcriptionData.text.length, '字符');
            console.log('- segments 数量:', transcriptionData.segments.length);
            
            // 分析segments详情
            let totalWords = 0;
            transcriptionData.segments.forEach((segment, index) => {
                const wordsCount = segment.words ? segment.words.length : 0;
                totalWords += wordsCount;
                console.log(`  - Segment ${index}: ${segment.start.toFixed(2)}s-${segment.end.toFixed(2)}s, ${wordsCount} words, "${segment.text.substring(0, 50)}..."`);
            });
            
            console.log('- 总计 words:', totalWords);
            
            // 验证修复结果
            console.log('\n🎯 修复验证结果:');
            if (transcriptionData.segments.length > 1) {
                console.log('✅ segments 数量正常:', transcriptionData.segments.length, '个 (之前只有1个)');
            } else {
                console.log('❌ segments 数量仍然异常:', transcriptionData.segments.length, '个');
            }
            
            if (totalWords > 0) {
                console.log('✅ words 数据正常:', totalWords, '个 (之前为0个)');
            } else {
                console.log('❌ words 数据仍然缺失:', totalWords, '个');
            }
            
            // 检查第一个segment的words详情
            if (transcriptionData.segments[0] && transcriptionData.segments[0].words && transcriptionData.segments[0].words.length > 0) {
                console.log('✅ words 时间戳数据完整');
                const firstWord = transcriptionData.segments[0].words[0];
                console.log('  - 第一个word示例:', firstWord);
            } else {
                console.log('❌ words 时间戳数据缺失');
            }
            
        } else {
            console.error('❌ 转录文件未生成:', transcriptionPath);
        }
        
        console.log('\n🎉 验证测试完成！');
        
    } catch (error) {
        console.error('❌ 验证测试失败:', error.message);
        console.error('📋 错误详情:', error);
    }
}

// 运行验证测试
if (require.main === module) {
    verifyCloudflareSegments().catch(console.error);
}

module.exports = { verifyCloudflareSegments };
