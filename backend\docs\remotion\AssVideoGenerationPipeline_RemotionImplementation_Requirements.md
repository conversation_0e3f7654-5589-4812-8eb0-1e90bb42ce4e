# AssVideoGenerationPipeline Remotion实现需求文档

## 项目背景

尊敬的Remotion API专家，我们诚挚邀请您协助我们使用Remotion技术重新实现现有的视频生成流水线。我们希望保持完全相同的输入输出接口和视觉效果，但使用Remotion的强大功能来替代当前基于FFmpeg的实现方案。

## 当前流水线概述

### 流水线名称
`AssVideoGenerationPipelineService` - ASS字幕和视频生成流水线

### 核心任务序列
1. **GenerateASSTask** - ASS字幕文件生成任务
2. **GenerateVideoTask** - 视频生成任务

## 详细需求规格

### 1. 流水线输入上下文 (Context Input)

流水线接收以下必需参数：

```javascript
{
  // 基础标识
  videoIdentifier: string,           // 视频唯一标识符，如 "news_20250103_001"
  
  // 文件路径
  audioFilePath: string,             // 音频文件路径，如 "/uploads/audio.mp3"
  originalVideoPath: string,         // 原始视频文件路径，如 "/uploads/video.mp4"
  savePath: string,                  // 文件保存目录路径，如 "/uploads/output/"
  
  // 字幕数据
  clozedSubtitleJsonArray: Array,    // 填空字幕数组
  enhancedBilingualSubtitleJsonArray: Array, // 增强双语字幕数组
  
  // 配置对象
  videoConfig: Object                // 完整的视频配置对象（详见下文）
}
```

### 2. 字幕数据结构

#### 2.1 填空字幕数组 (clozedSubtitleJsonArray)
```javascript
[
  {
    id: "1",
    start: 0.5,                      // 开始时间（秒）
    end: 3.2,                        // 结束时间（秒）
    text: "The weather is _____ today", // 填空文本，_____ 表示需要填空的位置
    words: ["sunny", "cloudy"]       // 可选的单词提示
  },
  // ... 更多字幕条目
]
```

#### 2.2 增强双语字幕数组 (enhancedBilingualSubtitleJsonArray)
```javascript
[
  {
    id: "1",
    start: 0.5,                      // 开始时间（秒）
    end: 3.2,                        // 结束时间（秒）
    text_english: "The weather is sunny today",  // 英文文本
    text_chinese: "今天天气很晴朗",    // 中文翻译
    words_explanation: {             // 关键词解释对象
      "weather": "天气",
      "sunny": "晴朗的"
    }
  },
  // ... 更多字幕条目
]
```

### 3. 视频配置对象 (videoConfig)

这是一个包含所有视频生成参数的复杂配置对象：

#### 3.1 基础视频参数
```javascript
{
  width: 1080,                       // 视频宽度（像素）
  height: 1920,                      // 视频高度（像素，9:16竖屏格式）
  framerate: 30,                     // 帧率
  backgroundColor: "white",          // 背景颜色
  backgroundStyle: "newspaper",      // 背景风格："newspaper" 或 "abstract"
  
  codec: "libx264",                  // 视频编码器
  preset: "slow",                    // 编码预设
  crf: 20,                          // 视频质量参数
  
  repeatCount: 3                     // 音频重复次数（对应3个学习模式）
}
```

#### 3.2 进度条配置
```javascript
progressBar: {
  height: 16,                        // 进度条高度（像素）
  backgroundColor: "#333333",        // 进度条背景色
  foregroundColor: "#FFFF00",        // 进度条前景色（黄色）
  position: "below_video"            // 进度条位置
}
```

#### 3.3 文本区域配置
```javascript
textArea: {
  backgroundColor: "#3B3B3B",        // 文本区域背景色（深灰色）
  width: "1080px",                   // 文本区域宽度
  height: "608px"                    // 文本区域高度
}
```

#### 3.4 字幕配置 (subtitleConfig)

##### 视频引导语配置
```javascript
videoGuide: {
  enabled: true,                     // 是否启用视频引导语
  title1: "坚持30天",                // 第一行标题
  title2: "听懂国外新闻",            // 第二行标题
  style: {
    fontSize: "100px",               // 字体大小
    fontFamily: "Arial",             // 字体族
    color: "#FFFFFF",                // 字体颜色（白色）
    fontWeight: "bold",              // 字体粗细
    textShadow: "2px 2px 4px rgba(0,0,0,0.8)", // 文字阴影
    position: {
      x: "540px",                    // X坐标（居中）
      y1: "300px",                   // 第一行Y坐标
      y2: "420px"                    // 第二行Y坐标
    }
  }
}
```

##### 广告配置
```javascript
advertisement: {
  enabled: true,                     // 是否启用广告
  titles: [                          // 广告文本数组（随机选择一组）
    {
      line1: "🌍关注水蜜桃英语",
      line2: "摆脱字幕，听力涨得快！"
    },
    {
      line1: "🌍关注水蜜桃英语", 
      line2: "每天2分钟，陪你听力打卡"
    },
    {
      line1: "🌍关注水蜜桃英语",
      line2: "每天2分钟，听全球要闻!"
    }
  ],
  startTime: 6,                      // 广告开始时间（秒）
  endTime: "firstLoopEnd",           // 广告结束时间（第一轮结束）
  animationDuration: 0.5,            // 动画持续时间
  style: {
    fontSize: "34px",                // 字体大小
    fontFamily: "Microsoft YaHei",   // 字体族
    fontWeight: "bold",              // 字体粗细
    color: "#FFFF00",                // 字体颜色（黄色）
    textShadow: "1px 1px 2px rgba(0,0,0,0.8)", // 文字阴影
    border: "2px solid #000000",     // 边框
    position: {
      x: "880px",                    // X坐标
      y1: "680px",                   // 第一行Y坐标
      y2: "715px"                    // 第二行Y坐标
    }
  }
}
```

##### 重复模式配置
```javascript
repeatModes: [
  {
    name: "blindListen",             // 第一遍：盲听模式
    displayText: "第一遍 盲听"
  },
  {
    name: "clozedSubtitle",          // 第二遍：填空字幕模式
    displayText: "第二遍 单词填空"
  },
  {
    name: "bilingualSubtitle",       // 第三遍：双语字幕模式
    displayText: "第三遍 中英翻译"
  }
]
```

##### 双语字幕样式配置
```javascript
bilingualTextStyle: {
  keywordBlockStyle: {               // 关键词高亮样式
    color: "#FFFF00",                // 关键词颜色（黄色）
    backgroundColor: "transparent",   // 关键词背景色
    fontWeight: "normal",            // 关键词字体粗细
    fontStyle: "normal",             // 关键词字体样式
    textDecoration: "none",          // 关键词文本装饰
    textShadow: "none"               // 关键词文字阴影
  },
  englishStyle: {                    // 英文字幕样式
    fontSize: "50px",                // 英文字体大小
    fontFamily: "Arial",             // 英文字体族
    fontWeight: "bold",              // 英文字体粗细
    color: "#FFFFFF",                // 英文字体颜色（白色）
    backgroundColor: "transparent",   // 英文背景色
    textAlign: "center",             // 英文对齐方式
    marginLeft: "150px",             // 英文左边距
    marginRight: "150px",            // 英文右边距
    marginBottom: "10px"             // 英文下边距
  },
  chineseStyle: {                    // 中文字幕样式
    fontSize: "50px",                // 中文字体大小
    fontFamily: "Arial",             // 中文字体族
    fontWeight: "bold",              // 中文字体粗细
    color: "#FFFFFF",                // 中文字体颜色（白色）
    backgroundColor: "transparent",   // 中文背景色
    textAlign: "center",             // 中文对齐方式
    letterSpacing: "2px",            // 中文字符间距
    marginLeft: "150px",             // 中文左边距
    marginRight: "150px",            // 中文右边距
    marginBottom: "10px"             // 中文下边距
  }
}
```

### 4. 流水线输出上下文 (Context Output)

流水线执行完成后，上下文应包含以下新增字段：

```javascript
{
  // === 来自 GenerateASSTask 的输出 ===
  assFilePath: string,               // 生成的ASS字幕文件路径
  assContent: string,                // ASS字幕文件内容
  subtitleStats: Object,             // 字幕统计信息
  
  // === 来自 GenerateVideoTask 的输出 ===
  finalVideoPath: string,            // 最终生成的视频文件路径
  videoGenerationStats: Object,      // 视频生成统计信息
  videoDuration: number,             // 视频时长（秒）
  videoResolution: string            // 视频分辨率，如 "1080x1920"
}
```

## Task 1: GenerateASSTask 详细需求

### 功能概述
生成完整的ASS格式字幕文件，包含视频标题、填空字幕、双语字幕、单元引导字幕等所有类型。

### 输入依赖
- videoIdentifier: 视频标识符
- audioFilePath: 音频文件路径（用于获取时长）
- clozedSubtitleJsonArray: 填空字幕数组
- enhancedBilingualSubtitleJsonArray: 增强双语字幕数组
- savePath: 文件保存路径
- videoConfig: 视频配置对象

### 核心处理逻辑

#### 1. 音频时长获取
使用FFprobe获取音频文件的精确时长（秒）

#### 2. 视频引导语生成
根据videoConfig.subtitleConfig.videoGuide配置生成视频标题字幕：
- 时间范围：0秒 到 (音频时长 × 重复次数)
- 包含两行文本：title1 和 title2
- 使用配置的样式和位置

#### 3. 单元引导字幕生成
根据repeatModes配置生成单元引导字幕：
- 第1个单元：0秒 到 音频时长
- 第2个单元：音频时长 到 2×音频时长
- 第3个单元：2×音频时长 到 3×音频时长
- 每个单元显示对应的displayText

#### 4. 字幕时间偏移处理
根据repeatModes中的位置计算字幕时间偏移：
- 如果clozedSubtitle在第2位，则填空字幕时间 += 1×音频时长
- 如果bilingualSubtitle在第3位，则双语字幕时间 += 2×音频时长

#### 5. 关键词翻译处理
对双语字幕中的英文文本进行关键词翻译处理：
- 根据words_explanation对象查找关键词
- 为关键词添加中文翻译注释
- 使用ASS特效标签：`{\1c&H00FFFF00}keyword{\fs30} [翻译]{\r}`
- 按关键词长度降序排序，避免短词覆盖长词

#### 6. 广告字幕生成
根据advertisement配置生成广告字幕：
- 随机选择一组广告文本
- 在指定时间范围内显示
- 包含进入和离开动画效果

### 输出结果
- assFilePath: 生成的ASS字幕文件完整路径
- assContent: 完整的ASS字幕内容
- subtitleStats: 处理统计信息

## Task 2: GenerateVideoTask 详细需求

### 功能概述
使用ASS字幕文件和原始视频生成最终的9:16短视频，包含音频重复拼接、进度条生成、背景视频生成、ASS字幕烧录等功能。

### 输入依赖
- videoIdentifier: 视频标识符
- originalVideoPath: 原始视频文件路径
- audioDuration: 音频时长（来自GenerateASSTask）
- assContent: ASS字幕内容（来自GenerateASSTask）
- audioFilePath: 音频文件路径
- savePath: 文件保存路径
- videoConfig: 视频配置对象

### 核心处理流程

#### 1. 音频重复拼接
将原始音频重复拼接指定次数（repeatCount）：
- 使用FFmpeg的concat filter进行无缝连接
- 输出格式：MP3，192k比特率
- 生成扩展音频文件

#### 2. 进度条视频生成
使用Canvas生成动态进度条视频：
- 尺寸：1080 × 16像素
- 背景色：#333333
- 前景色：#FFFF00（黄色）
- 动画：从0%到100%的进度填充
- 时长：单次音频时长

#### 3. 进度条视频重复
将单次进度条视频重复拼接，匹配总音频时长

#### 4. 背景视频生成
使用Canvas生成背景视频：
- 基础背景：报纸纹理图片（newspaper_9_16.png）
- 遮罩：80%透明度的黑色遮罩
- 尺寸：1080 × 1920像素
- 时长：总音频时长

#### 5. 最终视频合成
使用FFmpeg复杂滤镜进行多层视频合成：

**视频层次结构（从下到上）：**
1. **背景层**：报纸背景 + 80%黑色遮罩
2. **原视频层**：原始视频，缩放并居中放置
3. **文本区域层**：深灰色文本背景区域
4. **进度条层**：动态进度条
5. **字幕层**：ASS字幕烧录

**具体布局：**
- 原视频：居中放置，保持宽高比
- 文本区域：1080×608像素，深灰色背景
- 进度条：位于视频下方
- 字幕：根据ASS文件定位

### 输出结果
- finalVideoPath: 最终生成的视频文件路径
- videoGenerationStats: 视频生成统计信息

## 视觉效果要求

### 1. 视频布局
- **总尺寸**：1080×1920像素（9:16竖屏）
- **背景**：报纸纹理 + 80%黑色遮罩
- **原视频**：居中放置，保持原始宽高比
- **文本区域**：深灰色背景，用于显示字幕
- **进度条**：黄色进度条，显示播放进度

### 2. 字幕样式
- **视频标题**：白色大字体，居中显示，带阴影
- **填空字幕**：白色字体，_____ 表示填空位置
- **双语字幕**：
  - 英文：白色字体，50px，粗体
  - 中文：白色字体，50px，粗体，2px字符间距
  - 关键词：黄色高亮，带中文翻译注释
- **单元引导**：显示当前学习模式
- **广告字幕**：黄色字体，带边框，包含动画效果

### 3. 时间轴安排
- **第一遍（0-音频时长）**：盲听模式，只显示原视频和引导语
- **第二遍（音频时长-2×音频时长）**：填空字幕模式
- **第三遍（2×音频时长-3×音频时长）**：双语字幕模式
- **广告**：在第6秒到第一轮结束期间显示

## 技术实现建议

### 推荐使用的Remotion API
1. **useCurrentFrame()** - 获取当前帧数
2. **useVideoConfig()** - 获取视频配置
3. **interpolate()** - 实现动画效果
4. **Sequence** - 管理时间序列
5. **AbsoluteFill** - 绝对定位布局
6. **Video** - 显示原始视频
7. **Audio** - 处理音频
8. **Img** - 显示背景图片
9. **calculateMetadata()** - 动态计算视频参数

### 关键技术挑战
1. **多层视频合成**：需要精确控制各层的显示时机和样式
2. **字幕时间同步**：确保字幕与音频完美同步
3. **动态进度条**：实现平滑的进度条动画
4. **关键词高亮**：实现复杂的文本样式处理
5. **响应式布局**：适配不同的原视频尺寸

## 期望合作方式

我们希望您能够：
1. **分析现有实现**：理解当前FFmpeg方案的技术细节
2. **设计Remotion架构**：提出基于Remotion的实现方案
3. **保持接口一致**：确保输入输出完全兼容
4. **优化性能表现**：利用Remotion的优势提升渲染效率
5. **提供实现指导**：协助我们完成技术迁移

感谢您的宝贵时间和专业建议！我们期待与您的深入合作。
