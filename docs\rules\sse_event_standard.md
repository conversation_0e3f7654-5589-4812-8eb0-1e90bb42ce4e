---
description: SSE (Server-Sent Events) 事件标准化规范和最佳实践，包含finalContextPreview完整性要求
globs: backend/src/**/*.js, frontend-test/**/*.js
alwaysApply: true
---
# SSE 事件数据结构标准

本规则定义了项目中使用 Server-Sent Events (SSE) 进行实时进度报告的标准和数据结构。

## SSE 概述

Server-Sent Events (SSE) 是一种服务器推送技术，允许服务器通过 HTTP 连接向客户端推送事件流。该项目使用 SSE 向前端实时报告后端任务和流水线的执行进度。

## 实现层级

SSE 实现涉及多个层级：

1. **任务层** (`TaskBase` 子类)：通过 `progressCallback` 报告任务内部状态变化
2. **流水线层** (`PipelineBase`)：整合和管理任务进度，提供 `tasksSnapshot`
3. **服务层** (`VideoProcessingPipelineService` 等)：转发或补充流水线的进度回调
4. **控制器层** (`videoController.js` 等)：设置 SSE 响应头并将进度发送给客户端
5. **客户端**：使用 `EventSource` API 订阅和处理 SSE 事件流

## 标准化SSE基础设施

### 核心组件

项目使用标准化的SSE基础设施，位于 `backend/src/constants/progress.js`：

- **SSE事件工厂函数**：`createSSEEventData()`, `createControllerStatusSSE()`, `createHeartbeatSSE()`
- **SSE连接管理器**：`SSEConnectionManager` 类
- **标准响应头**：`getSSEHeaders()` 函数
- **事件类型常量**：`SSE_EVENT_TYPES` 枚举

### 控制器SSE实现标准

控制器应使用标准化的SSE基础设施，遵循以下模式：

```javascript
// 1. 导入标准化SSE基础设施
const {
    SSE_EVENT_TYPES,
    SSE_CONNECTION_STATUS,
    CONTROLLER_STATUS,
    PIPELINE_STATUS,
    createSSEEventData,
    createControllerStatusSSE,
    createHeartbeatSSE,
    getSSEHeaders,
    SSEConnectionManager
} = require('../constants/progress');

// 2. 初始化SSE连接管理器
const connectionId = `video-upload-${reqId}-${Date.now()}`;
const sseManager = new SSEConnectionManager(res, connectionId);

// 3. 初始化连接并启动心跳
sseManager.initialize();

// 启动心跳机制（每30秒）
const heartbeatInterval = setInterval(() => {
    if (sseManager && sseManager.isActive) {
        const heartbeat = createHeartbeatSSE({
            connectionId: connectionId,
            uptime: Date.now() - sseManager.startTime
        });
        sseManager.sendEvent(heartbeat);
    } else {
        clearInterval(heartbeatInterval);
    }
}, 30000);

// 4. 处理客户端断开连接
req.on('close', () => {
    logger.info(`${logPrefix}[SSE_CLOSE] 客户端连接已关闭。ReqID: ${reqId}`);
    if (sseManager && sseManager.isActive) {
        sseManager.close();
    }
});

// 5. 发送特殊状态事件
const acceptedEvent = createControllerStatusSSE(
    CONTROLLER_STATUS.ACCEPTED,
    {
        message: `视频 '${req.file.originalname}' 已接收，准备进入处理流水线`,
        videoIdentifier: videoIdentifier,
        fileInfo: {
            originalName: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype
        }
    }
);
sseManager.sendEvent(acceptedEvent);

// 6. 定义标准化进度回调函数
const progressCallbackFromController = (progressData) => {
    try {
        const progressEvent = createSSEEventData(
            SSE_EVENT_TYPES.PIPELINE_PROGRESS,
            progressData
        );
        
        if (sseManager && sseManager.isActive) {
            const sent = sseManager.sendEvent(progressEvent);
            if (!sent) {
                logger.warn(`${logPrefix}[PROGRESS_CALLBACK] SSE事件发送失败`);
            }
        }
    } catch (error) {
        logger.error(`${logPrefix}[PROGRESS_CALLBACK] 进度回调处理异常: ${error.message}`);
    }
};

// 7. 调用服务层方法并传入回调
const pipelineResult = await pipelineService.processUploadedVideo(
    initialContext,
    progressCallbackFromController
);

// 8. 发送最终结果事件
const finalEvent = createSSEEventData(
    SSE_EVENT_TYPES.PIPELINE_PROGRESS,
    finalEventData
);
sseManager.sendEvent(finalEvent);

// 9. 清理和关闭连接
if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
}
if (sseManager && sseManager.isActive) {
    sseManager.close();
}
```

## SSE 事件数据结构

### 事件格式

SSE 事件使用以下标准格式：

```
id: {timestamp}
event: {eventType}
data: {JSON data object}

```

### 任务进度回调数据结构

从任务发出的回调数据结构：

```javascript
{
    taskName: String,          // 任务名称
    status: String,            // 任务状态：'started', 'processing_*', 'completed', 'failed_*'
    detail: String,            // 状态详情描述
    result: Object,            // [可选] 任务结果（仅完成时）
    error: {                   // [可选] 错误信息（仅失败时）
        message: String,       // 错误消息
        name: String           // 错误类型
    }
}
```

### 流水线进度回调数据结构

从 `PipelineBase` 发出的回调数据结构：

```javascript
{
    pipelineName: String,      // 流水线名称
    pipelineStatus: String,    // 流水线状态：'running', 'completed', 'failed'
    timestamp: String,         // ISO 格式的时间戳
    
    // 当前任务信息（如果有）
    currentTaskName: String,   // [可选] 当前执行任务的名称
    currentTaskStatus: String, // [可选] 当前任务的状态
    currentTaskDetail: String, // [可选] 当前任务的详情
    
    // 任务快照
    tasksSnapshot: [           // 所有任务的状态快照
        {
            name: String,      // 任务名称
            status: String,    // 任务状态
            resultPreview: String, // [可选] 结果预览
            errorSummary: String   // [可选] 错误摘要
        }
    ],
    
    // 错误信息（仅失败时）
    failedTaskName: String,    // [可选] 失败任务的名称
    failedTaskErrorDetails: {  // [可选] 失败详情
        message: String,
        name: String,
        stackPreview: String
    },
    
    // 最终结果（仅完成时）
    finalContextPreview: String, // [可选] 最终上下文预览
    finalMessage: String,        // [可选] 最终消息
    finalResult: Object          // [可选] 最终处理结果
}
```

### 特殊状态事件

控制器可能发送的特殊状态事件：

1. **接收确认事件** (`'ACCEPTED'`)：
```javascript
{
    pipelineStatus: 'ACCEPTED',
    message: String,           // 确认信息
    videoIdentifier: String,   // 视频标识符
    timestamp: String          // 时间戳
}
```

2. **控制器错误事件** (`'CONTROLLER_ERROR'`, `'FAILED_PREFLIGHT'`)：
```javascript
{
    pipelineStatus: 'CONTROLLER_ERROR', // 或 'FAILED_PREFLIGHT'
    errorDetails: {
        message: String,
        stackPreview: String   // [可选] 堆栈预览
    },
    timestamp: String
}
```

## 状态码规范

### 任务状态码

| 状态码 | 说明 |
|--------|------|
| `'started'` | 任务开始执行 |
| `'processing'` | 任务正在处理（通用） |
| `'processing_ffmpeg_started'` | FFmpeg处理开始 |
| `'processing_ffmpeg_completed'` | FFmpeg处理完成 |
| `'processing_api_call_started'` | API调用开始 |
| `'processing_api_call_completed'` | API调用完成 |
| `'failed'` | 任务失败（通用） |
| `'failed_ffmpeg'` | FFmpeg处理失败 |
| `'failed_api_call'` | API调用失败 |
| `'completed'` | 任务成功完成 |

### 流水线状态码

| 状态码 | 说明 |
|--------|------|
| `'ACCEPTED'` | 请求已接受，准备处理 |
| `'started'` | 流水线开始执行 |
| `'running'` | 流水线正在运行 |
| `'completed'` | 流水线成功完成 |
| `'failed'` | 流水线失败 |
| `'FAILED_PREFLIGHT'` | 预检失败（如文件缺失） |
| `'CONTROLLER_ERROR'` | 控制器级别错误 |

## **JSON 数据完整性准则**

### **数据完整性要求**
- **所有 JSON 字符串必须保持完整性**，不得随意截断
- **预览字段应提供完整数据**，而非截断的片段
- **前端必须安全解析 JSON**，处理可能的解析异常

### **后端数据发送标准**
```javascript
// ✅ DO: 发送完整的 JSON 数据
const eventData = {
    finalContextPreview: JSON.stringify(context), // 完整数据
    resultData: JSON.stringify(result)            // 完整结果
};

// ❌ DON'T: 截断 JSON 字符串
const eventData = {
    finalContextPreview: JSON.stringify(context).substring(0, 150) + '...', // 破坏JSON结构
    resultData: result.toString().slice(0, 100) + '...'                     // 不可解析
};
```

### **前端解析安全准则**
```javascript
// ✅ DO: 安全解析 JSON 数据
function safeParseJSON(jsonString, fallback = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON解析失败:', error.message);
        return fallback;
    }
}

// 使用安全解析
const contextData = safeParseJSON(result.finalContextPreview, {});

// ❌ DON'T: 直接解析未验证的 JSON
const contextData = JSON.parse(result.finalContextPreview); // 可能抛出异常
```

### **数据传输最佳实践**
- **优先传输结构化对象**而非字符串化的JSON
- **使用适当的数据压缩**处理大型数据对象
- **实现多层次数据获取**，提供降级方案
- **验证数据完整性**，确保关键字段存在

## 注意事项

1. **超时处理**：SSE 连接应设置合理的超时机制
2. **错误恢复**：客户端应能处理连接中断并尝试重新连接
3. **数据量控制**：避免在事件中包含过大的数据对象
4. **浏览器兼容性**：确保目标浏览器支持 `EventSource` API
5. **代理配置**：确保 Nginx 等反向代理配置正确支持 SSE（禁用缓冲）

