# &lt;Loop&gt;

## 概述

`<Loop>` 组件允许您快速布局一个重复播放的动画。它是一个高阶组件，可以让内容在指定的时间内循环播放。

**版本要求**: v2.5.0+

## 语法

```typescript
import { Loop } from "remotion";

<Loop durationInFrames={50} times={2}>
  <YourComponent />
</Loop>
```

## 核心属性

### durationInFrames (必需)
- **类型**: `number`
- **描述**: 一次循环迭代应该持续的帧数

### times (可选)
- **类型**: `number | Infinity`
- **默认值**: `Infinity`
- **描述**: 循环内容的次数

### layout (可选)
- **类型**: `"absolute-fill" | "none"`
- **默认值**: `"absolute-fill"`
- **描述**: 布局模式
  - `"absolute-fill"`: 内容将被绝对定位
  - `"none"`: 禁用布局副作用

### style (v3.3.4+)
- **类型**: `React.CSSProperties`
- **描述**: 应用到容器的 CSS 样式
- **注意**: 当 `layout="none"` 时不可用

## 基础用法

### 1. 基础循环动画

```typescript
import { Loop, useCurrentFrame, interpolate } from "remotion";

const BlueSquare = () => {
  const frame = useCurrentFrame();
  
  const rotation = interpolate(frame, [0, 50], [0, 360], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      width: 100,
      height: 100,
      backgroundColor: "blue",
      transform: `rotate(${rotation}deg)`
    }} />
  );
};

const MyComp = () => {
  return (
    <Loop durationInFrames={50}>
      <BlueSquare />
    </Loop>
  );
};
```

### 2. 固定次数循环

```typescript
import { Loop } from "remotion";

const FixedLoop = () => {
  return (
    <Loop durationInFrames={50} times={3}>
      <BlueSquare />
    </Loop>
  );
};
```

## 实际应用场景

### 1. 背景动画循环

```typescript
import { Loop, useCurrentFrame, interpolate } from "remotion";

const FloatingParticles = () => {
  const frame = useCurrentFrame();
  
  const particles = Array.from({ length: 10 }, (_, i) => {
    const y = interpolate(
      frame,
      [0, 60],
      [100 + i * 50, -50],
      { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
    );
    
    return (
      <div
        key={i}
        style={{
          position: "absolute",
          left: 50 + i * 80,
          top: y,
          width: 20,
          height: 20,
          borderRadius: "50%",
          backgroundColor: `hsl(${i * 36}, 70%, 60%)`,
          opacity: 0.7
        }}
      />
    );
  });

  return <div>{particles}</div>;
};

const BackgroundLoop = () => {
  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      {/* 循环背景动画 */}
      <Loop durationInFrames={60}>
        <FloatingParticles />
      </Loop>
      
      {/* 前景内容 */}
      <div style={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        zIndex: 10,
        color: "white",
        fontSize: 48,
        textShadow: "2px 2px 4px rgba(0,0,0,0.5)"
      }}>
        主要内容
      </div>
    </div>
  );
};
```

### 2. 嵌套循环

```typescript
import { Loop, useCurrentFrame, interpolate } from "remotion";

const InnerAnimation = () => {
  const frame = useCurrentFrame();
  
  const scale = interpolate(frame, [0, 30], [1, 1.5, 1], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      width: 80,
      height: 80,
      backgroundColor: "#e74c3c",
      borderRadius: "50%",
      transform: `scale(${scale})`
    }} />
  );
};

const OuterAnimation = () => {
  const frame = useCurrentFrame();
  
  const rotation = interpolate(frame, [0, 75], [0, 360], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      width: 200,
      height: 200,
      border: "2px solid #3498db",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      transform: `rotate(${rotation}deg)`
    }}>
      {/* 内层循环 - 30帧一个周期 */}
      <Loop durationInFrames={30}>
        <InnerAnimation />
      </Loop>
    </div>
  );
};

const NestedLoop = () => {
  return (
    <div style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%"
    }}>
      {/* 外层循环 - 75帧一个周期 */}
      <Loop durationInFrames={75}>
        <OuterAnimation />
      </Loop>
    </div>
  );
};
```

### 3. 使用 useLoop() 钩子

```typescript
import { Loop, useCurrentFrame } from "remotion";

const LoopAwareComponent = () => {
  const frame = useCurrentFrame();
  const loop = Loop.useLoop();

  if (loop === null) {
    return <div>不在循环内</div>;
  }

  const { durationInFrames, iteration } = loop;
  const progress = (frame % durationInFrames) / durationInFrames;

  return (
    <div style={{
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: 20
    }}>
      <div style={{
        width: 200,
        height: 200,
        backgroundColor: `hsl(${iteration * 60}, 70%, 50%)`,
        borderRadius: "50%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "white",
        fontSize: 24
      }}>
        循环 {iteration + 1}
      </div>
      
      <div style={{
        width: 300,
        height: 20,
        backgroundColor: "#f0f0f0",
        borderRadius: 10,
        overflow: "hidden"
      }}>
        <div style={{
          width: `${progress * 100}%`,
          height: "100%",
          backgroundColor: "#4caf50",
          transition: "width 0.1s ease"
        }} />
      </div>
      
      <div style={{ fontSize: 16 }}>
        进度: {Math.round(progress * 100)}%
      </div>
    </div>
  );
};

const LoopWithHook = () => {
  return (
    <Loop durationInFrames={60} times={4}>
      <LoopAwareComponent />
    </Loop>
  );
};
```

### 4. 多元素循环序列

```typescript
import { Loop, useCurrentFrame, interpolate } from "remotion";

const AnimatedElement = ({ 
  color, 
  delay = 0 
}: { 
  color: string; 
  delay?: number; 
}) => {
  const frame = useCurrentFrame();
  const adjustedFrame = Math.max(0, frame - delay);
  
  const y = interpolate(
    adjustedFrame,
    [0, 20, 40],
    [0, -100, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  const opacity = interpolate(
    adjustedFrame,
    [0, 10, 30, 40],
    [0.3, 1, 1, 0.3],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <div style={{
      width: 60,
      height: 60,
      backgroundColor: color,
      borderRadius: "50%",
      transform: `translateY(${y}px)`,
      opacity,
      margin: "0 10px"
    }} />
  );
};

const SequentialLoop = () => {
  const elements = [
    { color: "#e74c3c", delay: 0 },
    { color: "#f39c12", delay: 5 },
    { color: "#f1c40f", delay: 10 },
    { color: "#2ecc71", delay: 15 },
    { color: "#3498db", delay: 20 }
  ];

  return (
    <div style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%"
    }}>
      <Loop durationInFrames={50}>
        <div style={{ display: "flex", alignItems: "center" }}>
          {elements.map((element, index) => (
            <AnimatedElement
              key={index}
              color={element.color}
              delay={element.delay}
            />
          ))}
        </div>
      </Loop>
    </div>
  );
};
```

### 5. 条件循环

```typescript
import { Loop, useCurrentFrame, getInputProps } from "remotion";

interface LoopProps {
  enableLoop: boolean;
  loopCount: number;
  animationSpeed: number;
}

const ConditionalLoop = () => {
  const { enableLoop, loopCount, animationSpeed } = getInputProps() as LoopProps;
  const frame = useCurrentFrame();

  const AnimationContent = () => {
    const rotation = (frame * animationSpeed) % 360;
    
    return (
      <div style={{
        width: 150,
        height: 150,
        backgroundColor: "#9b59b6",
        transform: `rotate(${rotation}deg)`,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "white",
        fontSize: 18
      }}>
        旋转方块
      </div>
    );
  };

  if (!enableLoop) {
    return (
      <div style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%"
      }}>
        <AnimationContent />
      </div>
    );
  }

  return (
    <div style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%"
    }}>
      <Loop durationInFrames={60} times={loopCount}>
        <AnimationContent />
      </Loop>
    </div>
  );
};
```

### 6. 复杂循环模式

```typescript
import { Loop, useCurrentFrame, interpolate } from "remotion";

const ComplexPattern = () => {
  const frame = useCurrentFrame();
  
  // 创建多个同心圆动画
  const circles = Array.from({ length: 5 }, (_, i) => {
    const radius = 50 + i * 30;
    const speed = 1 + i * 0.5;
    const angle = (frame * speed * 2) % 360;
    
    const x = Math.cos((angle * Math.PI) / 180) * radius;
    const y = Math.sin((angle * Math.PI) / 180) * radius;
    
    return (
      <div
        key={i}
        style={{
          position: "absolute",
          left: "50%",
          top: "50%",
          width: 20,
          height: 20,
          borderRadius: "50%",
          backgroundColor: `hsl(${i * 72}, 70%, 60%)`,
          transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`,
          opacity: 0.8
        }}
      />
    );
  });

  return <div style={{ position: "relative", width: "100%", height: "100%" }}>{circles}</div>;
};

const PatternLoop = () => {
  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      {/* 背景循环 - 慢速 */}
      <Loop durationInFrames={180}>
        <div style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: "linear-gradient(45deg, #667eea 0%, #764ba2 100%)",
          opacity: 0.3
        }} />
      </Loop>
      
      {/* 主要图案循环 - 中速 */}
      <Loop durationInFrames={120}>
        <ComplexPattern />
      </Loop>
      
      {/* 前景装饰循环 - 快速 */}
      <Loop durationInFrames={60}>
        <div style={{
          position: "absolute",
          top: "10%",
          left: "10%",
          right: "10%",
          bottom: "10%",
          border: "2px solid rgba(255,255,255,0.3)",
          borderRadius: "50%",
          animation: "pulse 1s ease-in-out infinite"
        }} />
      </Loop>
    </div>
  );
};
```

## 高级特性

### 1. 循环信息获取
- 使用 `Loop.useLoop()` 获取当前循环状态
- 返回 `{ durationInFrames, iteration }` 或 `null`

### 2. 嵌套循环
- 支持无限层级的嵌套循环
- 内层循环会级联外层循环的效果

### 3. 布局控制
- `layout="absolute-fill"`: 默认绝对定位
- `layout="none"`: 禁用布局副作用

## 最佳实践

1. **性能优化**: 避免在循环内创建过于复杂的动画
2. **时间规划**: 合理设置 `durationInFrames` 以匹配动画节奏
3. **嵌套使用**: 利用嵌套循环创建复杂的动画模式
4. **条件循环**: 根据输入参数动态控制循环行为
5. **进度反馈**: 使用 `useLoop()` 提供循环进度信息

## 常见用例

- 背景动画循环
- 加载动画
- 装饰性动画元素
- 重复性内容展示
- 节拍同步动画

## 相关 API

- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`interpolate()`](./interpolate.md) - 插值函数
- [`<Freeze>`](./Freeze.md) - 冻结组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/loop/index.tsx)
