const winston = require('winston');
const path = require('path');
const fs = require('fs');

// 配置日志目录
const logDirectory = path.join(__dirname, '..', '..', 'logs');
const logFilePath = path.join(logDirectory, 'app.log');

// 确保目录存在
if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory, { recursive: true });
}

// 直接文件写入函数（备用）
function writeToFile(level, message) {
    const timestamp = new Date().toISOString().replace('T', ' ').substr(0, 19);
    const logLine = `${timestamp} ${level.toUpperCase()}: ${message}\n`;
    try {
        fs.appendFileSync(logFilePath, logLine);
    } catch (error) {
        console.error('文件写入失败:', error.message);
    }
}

// 创建基础 Logger
const logger = winston.createLogger({
    level: 'debug',
    transports: [
        // 只保留控制台输出，文件输出用自定义方法
        new winston.transports.Console({
            level: 'info',
            format: winston.format.simple()
        })
    ]
});

// 重写 Logger 方法，同时进行文件写入
const originalInfo = logger.info.bind(logger);
const originalDebug = logger.debug.bind(logger);
const originalError = logger.error.bind(logger);
const originalWarn = logger.warn.bind(logger);

logger.info = function(message) {
    writeToFile('INFO', message);
    return originalInfo(message);
};

logger.debug = function(message) {
    writeToFile('DEBUG', message);
    return originalDebug(message);
};

logger.error = function(message) {
    writeToFile('ERROR', message);
    return originalError(message);
};

logger.warn = function(message) {
    writeToFile('WARN', message);
    return originalWarn(message);
};

// 扩展方法
logger.consoleLog = (message) => {
    console.log(message);
    logger.info(message);
};

logger.consoleError = (message) => {
    console.error(message);
    logger.error(message);
};

// 强制刷新方法
logger.flush = function() {
    return Promise.resolve(); // 同步写入无需刷新
};

// 测试输出
console.log(`日志文件路径: ${logFilePath}`);
logger.info('Logger模块初始化完成 - 使用混合写入方案');
logger.debug('这是一条Debug级别的测试日志');
logger.error('这是一条Error级别的测试日志');

module.exports = logger; 