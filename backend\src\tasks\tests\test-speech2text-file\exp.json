{"task": "transcribe", "language": "english", "duration": 12.5600004196167, "text": "And today's question period comes after yesterday's throne speech read by King <PERSON>, covering everything from housing to the economy. But Alberta's premier argues something was missing. <PERSON> has more.", "segments": [{"id": 0, "seek": 0, "start": 0, "end": 5.579999923706055, "text": " And today's question period comes after yesterday's throne speech read by King <PERSON>, covering", "tokens": [50364, 400, 965, 311, 1168, 2896, 1487, 934, 5186, 311, 17678, 6218, 1401, 538, 3819, 10523, 11, 10322, 50643], "temperature": 0, "avg_logprob": -0.31683218479156494, "compression_ratio": 1.3612903356552124, "no_speech_prob": 0.010003873147070408}, {"id": 1, "seek": 0, "start": 5.579999923706055, "end": 7.960000038146973, "text": " everything from housing to the economy.", "tokens": [50643, 1203, 490, 6849, 281, 264, 5010, 13, 50762], "temperature": 0, "avg_logprob": -0.31683218479156494, "compression_ratio": 1.3612903356552124, "no_speech_prob": 0.010003873147070408, "words": [{"text": "everything", "start": 5.579999923706055, "end": 7.960000038146973}]}, {"id": 2, "seek": 0, "start": 7.960000038146973, "end": 11.359999656677246, "text": " But Alberta's premier argues something was missing.", "tokens": [50762, 583, 43279, 311, 12689, 38218, 746, 390, 5361, 13, 50932], "temperature": 0, "avg_logprob": -0.31683218479156494, "compression_ratio": 1.3612903356552124, "no_speech_prob": 0.010003873147070408}, {"id": 3, "seek": 0, "start": 11.359999656677246, "end": 12.5600004196167, "text": " <PERSON> has more.", "tokens": [50932, 16724, 4076, 575, 544, 13, 50992], "temperature": 0, "avg_logprob": -0.31683218479156494, "compression_ratio": 1.3612903356552124, "no_speech_prob": 0.010003873147070408}]}