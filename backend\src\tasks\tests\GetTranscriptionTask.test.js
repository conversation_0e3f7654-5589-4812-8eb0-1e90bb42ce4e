/**
 * @功能概述: GetTranscriptionTask 任务类的单元测试
 * @说明: 测试Azure Speech Service Fast Transcription API转录任务的各种执行场景
 * @架构位置: 测试层，验证GetTranscriptionTask的功能正确性
 * @测试覆盖: 正常流程、参数验证、错误处理、API交互、数据格式转换等
 */

// 导入测试所需的核心模块
const fs = require('fs');
const path = require('path');
const GetTranscriptionTask = require('../GetTranscriptionTask');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const logger = require('../../utils/logger');

// 测试日志前缀，用于标识测试输出
const testLogPrefix = '[测试：GetTranscriptionTask.test.js]';

// 记录测试文件加载
logger.info(`${testLogPrefix} 测试文件已加载。`);

// 临时覆盖配置模块，提供测试用的配置
const originalConfig = require('../../config');

// 创建测试配置，包含最小必需的配置项
const testConfig = {
    ...originalConfig,
    speechEndpoint: process.env.SPEECH_ENDPOINT || 'https://test-speech-service.cognitiveservices.azure.com',
    speechKey: process.env.SPEECH_KEY || 'test_speech_key_for_testing',
    apiVersion: process.env.API_VERSION || '2024-11-15',
    defaultLocale: process.env.DEFAULT_LOCALE || 'en-US',
    profanityFilter: process.env.PROFANITY_FILTER || 'Masked',
    requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 300000
};

// 在模块缓存中替换配置（仅在测试环境中）
require.cache[require.resolve('../../config')] = {
    exports: testConfig,
    loaded: true,
    id: require.resolve('../../config')
};

/**
 * @功能概述: 断言函数，用于验证条件是否为真
 * @param {boolean} condition - 要验证的条件
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当条件为假时抛出错误
 */
function assert(condition, message) {
    if (!condition) {
        const fullMessage = `断言失败: ${message}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

/**
 * @功能概述: 相等断言函数，验证两个值是否相等
 * @param {any} actual - 实际值
 * @param {any} expected - 期望值
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当值不相等时抛出错误
 */
function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: "${expected}", 实际: "${actual}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: "${actual}")`);
}

/**
 * @功能概述: 包含断言函数，验证字符串或数组是否包含指定子串
 * @param {string|Array} arrayOrString - 要检查的字符串或数组
 * @param {string} substring - 要查找的子串
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当不包含指定子串时抛出错误
 */
function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

/**
 * @功能概述: 创建测试用的音频文件
 * @param {string} filePath - 文件路径
 * @returns {string} 创建的文件路径
 */
function createTestAudioFile(filePath) {
    const testContent = Buffer.from('fake audio content for testing');
    fs.writeFileSync(filePath, testContent);
    return filePath;
}

/**
 * @功能概述: 清理测试文件
 * @param {string} filePath - 要删除的文件路径
 */
function cleanupTestFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logger.debug(`${testLogPrefix} 清理测试文件: ${filePath}`);
        }
    } catch (error) {
        logger.warn(`${testLogPrefix} 清理测试文件失败: ${error.message}`);
    }
}

/**
 * @功能概述: 主测试执行函数
 */
async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 GetTranscriptionTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new GetTranscriptionTask();
        assert(task instanceof GetTranscriptionTask, '任务应为 GetTranscriptionTask 的实例');
        assertEquals(task.name, 'GetTranscriptionTask', '任务名称应为 GetTranscriptionTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
        assert(task.taskId.includes('GetTranscriptionTask'), '任务ID应包含任务名称');
    });

    await runSingleTest('2. 缺少必需字段 - reqId', async () => {
        const task = new GetTranscriptionTask();
        const context = { 
            videoIdentifier: 'test-video',
            audioFilePathInUploads: '/test/audio.mp3',
            savePath: '/test/save'
        }; // 缺少 reqId
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需或无效的字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'reqId', '错误消息应指明缺少reqId字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('3. 缺少必需字段 - videoIdentifier', async () => {
        const task = new GetTranscriptionTask();
        const context = { 
            reqId: 'test-req-id',
            audioFilePathInUploads: '/test/audio.mp3',
            savePath: '/test/save'
        }; // 缺少 videoIdentifier
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需或无效的字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'videoIdentifier', '错误消息应指明缺少videoIdentifier字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('4. 缺少必需字段 - audioFilePathInUploads', async () => {
        const task = new GetTranscriptionTask();
        const context = { 
            reqId: 'test-req-id',
            videoIdentifier: 'test-video',
            savePath: '/test/save'
        }; // 缺少 audioFilePathInUploads
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需或无效的字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'audioFilePathInUploads', '错误消息应指明缺少audioFilePathInUploads字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('5. 缺少必需字段 - savePath', async () => {
        const task = new GetTranscriptionTask();
        const context = { 
            reqId: 'test-req-id',
            videoIdentifier: 'test-video',
            audioFilePathInUploads: '/test/audio.mp3'
        }; // 缺少 savePath
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需或无效的字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'savePath', '错误消息应指明缺少savePath字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('6. 音频文件不存在', async () => {
        const task = new GetTranscriptionTask();
        const nonExistentPath = path.join(__dirname, 'non-existent-audio.mp3');
        const context = {
            reqId: 'test-file-not-exist',
            videoIdentifier: 'test-video',
            audioFilePathInUploads: nonExistentPath,
            savePath: __dirname
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '指定的音频文件不存在', '错误消息应指明文件不存在');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('7. Azure Speech Service 真实数据测试', async () => {
        const task = new GetTranscriptionTask();
        // 使用用户指定的真实数据路径
        const context = {
            reqId: 'test-azure-speech-real-data-001',
            videoIdentifier: 'videoFile-1749952703908-294201027',
            audioFilePathInUploads: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\videoFile-1749952703908-294201027_e92bf9ba_audio.mp3',
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output',
            originalVideoName: 'test-video.mp4' // 可选参数
        };

        const progressLogs = [];

        // 检查音频文件是否存在
        if (!fs.existsSync(context.audioFilePathInUploads)) {
            logger.warn(`${testLogPrefix} 跳过真实数据测试：音频文件不存在 ${context.audioFilePathInUploads}`);
            return; // 跳过此测试
        }

        // 确保输出目录存在
        if (!fs.existsSync(context.savePath)) {
            fs.mkdirSync(context.savePath, { recursive: true });
            logger.info(`${testLogPrefix} 创建输出目录: ${context.savePath}`);
        }

        try {
            const result = await task.execute(context, (data) => {
                progressLogs.push(data);
                logger.info(`${testLogPrefix} 进度更新: ${data.status} - ${data.detail || data.llmProgressDetail}`);
            });

            // 验证任务执行结果
            assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
            assert(result, '任务应返回结果对象');
            assertEquals(result.transcriptionStatus, 'success', '转录状态应为成功');
            assert(result.apiResponse, '结果应包含API响应数据');

            // 验证转换后的数据格式（应为Whisper兼容格式）
            const apiResponse = result.apiResponse;
            assert(apiResponse.task === 'transcribe', 'API响应应包含task字段并值为transcribe');
            assert(apiResponse.language, 'API响应应包含language字段');
            assert(typeof apiResponse.duration === 'number', 'duration应为数字类型');
            assert(apiResponse.text && typeof apiResponse.text === 'string', 'text应为字符串');
            assert(Array.isArray(apiResponse.segments), 'segments应为数组');

            // 验证segments格式
            if (apiResponse.segments.length > 0) {
                const segment = apiResponse.segments[0];
                assert(typeof segment.id === 'number', 'segment.id应为数字');
                assert(segment.seek === 0, 'segment.seek应为0');
                assert(typeof segment.start === 'number', 'segment.start应为数字');
                assert(typeof segment.end === 'number', 'segment.end应为数字');
                assert(typeof segment.text === 'string', 'segment.text应为字符串');
                assert(Array.isArray(segment.tokens), 'segment.tokens应为数组');
                assert(segment.temperature === 0, 'segment.temperature应为0');
                assert(typeof segment.avg_logprob === 'number', 'segment.avg_logprob应为数字');
                assert(typeof segment.compression_ratio === 'number', 'segment.compression_ratio应为数字');
                assert(typeof segment.no_speech_prob === 'number', 'segment.no_speech_prob应为数字');

                // 验证words字段（如果存在）
                if (segment.words && segment.words.length > 0) {
                    const word = segment.words[0];
                    assert(typeof word.text === 'string', 'word.text应为字符串');
                    assert(typeof word.start === 'number', 'word.start应为数字');
                    assert(typeof word.end === 'number', 'word.end应为数字');
                }
            }

            // 验证JSON文件保存
            assert(result.transcriptionJsonPath, '结果应包含JSON文件路径');
            assert(fs.existsSync(result.transcriptionJsonPath), 'JSON文件应存在');

            // 验证保存的JSON文件内容
            const savedContent = JSON.parse(fs.readFileSync(result.transcriptionJsonPath, 'utf8'));
            assertEquals(savedContent.task, apiResponse.task, '保存的JSON应与API响应一致');
            assertEquals(savedContent.segments.length, apiResponse.segments.length, '保存的segments数量应与API响应一致');

            // 验证进度回调
            assert(progressLogs.length > 0, '应记录进度回调');
            const hasDataConversionProgress = progressLogs.some(log => 
                log.detail && log.detail.includes('转换Azure Speech数据格式')
            );
            assert(hasDataConversionProgress, '应包含数据格式转换的进度更新');

            logger.info(`${testLogPrefix} 真实数据测试成功完成:`);
            logger.info(`${testLogPrefix}   - 转录文本长度: ${apiResponse.text.length}`);
            logger.info(`${testLogPrefix}   - segments数量: ${apiResponse.segments.length}`);
            logger.info(`${testLogPrefix}   - 音频时长: ${apiResponse.duration}秒`);
            logger.info(`${testLogPrefix}   - 语言: ${apiResponse.language}`);
            logger.info(`${testLogPrefix}   - JSON文件路径: ${result.transcriptionJsonPath}`);

        } catch (error) {
            logger.error(`${testLogPrefix} 真实数据测试失败: ${error.message}`);
            
            // 如果是配置错误（缺少Azure配置），给出提示但不失败测试
            if (error.message.includes('Azure Speech Service 配置参数') || 
                error.message.includes('speechEndpoint') || 
                error.message.includes('speechKey')) {
                logger.warn(`${testLogPrefix} 跳过真实数据测试：缺少Azure Speech Service配置`);
                return; // 跳过此测试
            }
            
            throw error; // 重新抛出其他错误
        }
    });

    await runSingleTest('8. collectDetailedContext 方法', async () => {
        const task = new GetTranscriptionTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.transcriptionProcessingDetails, '上下文应包含transcriptionProcessingDetails');
        assert(context.azureApiDetails, '上下文应包含azureApiDetails');
        assertEquals(context.collectionMethod, 'GetTranscriptionTask.collectDetailedContext',
                    '收集方法应正确标识');
                    
        // 验证Azure Speech Service相关信息
        assert(context.azureApiDetails.provider === 'Azure Speech Service', 'API提供商应为Azure Speech Service');
        assert(context.azureApiDetails.model === 'Fast Transcription', 'API模型应为Fast Transcription');
    });

    await runSingleTest('9. 进度回调功能', async () => {
        const task = new GetTranscriptionTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'GetTranscriptionTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');
    });

    await runSingleTest('10. LLM进度报告功能', async () => {
        const task = new GetTranscriptionTask();
        const progressLogs = [];

        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试LLM专用进度报告
        task.reportLLMProgress('preparing', '准备Azure Speech Service API请求', {
            current: 30,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录LLM进度回调');
        assertEquals(progressLogs[0].taskName, 'GetTranscriptionTask', 'LLM进度回调应包含正确的任务名称');
        assert(progressLogs[0].technicalDetail, 'LLM进度回调应包含技术详情');
    });

    await runSingleTest('11. 任务状态管理', async () => {
        const task = new GetTranscriptionTask();

        // 测试初始状态
        assertEquals(task.status, TASK_STATUS.PENDING, '初始状态应为PENDING');

        // 测试开始状态
        task.start();
        assertEquals(task.status, TASK_STATUS.STARTED, '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');

        // 测试完成状态
        const testResult = { test: 'result' };
        task.complete(testResult);
        assertEquals(task.status, TASK_STATUS.COMPLETED, '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');

        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('12. 错误处理和失败状态', async () => {
        const task = new GetTranscriptionTask();
        const testError = new Error('测试错误');

        // 测试失败状态
        task.fail(testError);
        assertEquals(task.status, TASK_STATUS.FAILED, '失败后状态应为FAILED');
        assertEquals(task.error, testError, '应保存错误对象');
        assert(task.endTime, '失败时应记录结束时间');
    });

    await runSingleTest('13. saveTranscriptionJson 方法', async () => {
        const task = new GetTranscriptionTask();
        const testData = { 
            task: 'transcribe',
            language: 'en',
            duration: 12.34,
            text: '测试转录文本',
            segments: [
                {
                    id: 0,
                    seek: 0,
                    start: 0.0,
                    end: 5.0,
                    text: '测试转录文本',
                    tokens: [],
                    temperature: 0,
                    avg_logprob: -0.5,
                    compression_ratio: 1.2,
                    no_speech_prob: 0.1
                }
            ]
        };
        const videoIdentifier = 'test-video-123';
        const execLogPrefix = '[测试]';
        const savePath = __dirname;

        try {
            const savedPath = await task.saveTranscriptionJson(testData, videoIdentifier, execLogPrefix, savePath);

            assert(savedPath, 'saveTranscriptionJson应返回保存路径');
            assertIncludes(savedPath, `${videoIdentifier}_transcription.json`, '保存路径应包含正确的文件名');
            assert(fs.existsSync(savedPath), '保存的文件应存在');

            // 验证文件内容
            const savedContent = JSON.parse(fs.readFileSync(savedPath, 'utf8'));
            assertEquals(savedContent.task, testData.task, '保存的task应正确');
            assertEquals(savedContent.language, testData.language, '保存的语言应正确');
            assertEquals(savedContent.duration, testData.duration, '保存的时长应正确');
            assertEquals(savedContent.segments.length, testData.segments.length, '保存的segments数量应正确');

            // 清理测试文件
            cleanupTestFile(savedPath);

        } catch (error) {
            // 如果保存失败，确保清理可能创建的文件
            const expectedPath = path.join(savePath, `${videoIdentifier}_transcription.json`);
            cleanupTestFile(expectedPath);
            throw error;
        }
    });

    await runSingleTest('14. 数据格式转换测试', async () => {
        const task = new GetTranscriptionTask();
        
        // 模拟Azure Speech Service响应数据
        const azureSpeechData = {
            durationMilliseconds: 12672,
            combinedPhrases: [
                {
                    channel: 0,
                    text: "Today's question period comes after yesterday's throne speech."
                }
            ],
            phrases: [
                {
                    channel: 0,
                    offsetMilliseconds: 160,
                    durationMilliseconds: 7840,
                    text: "Today's question period comes after yesterday's throne speech.",
                    words: [
                        {
                            text: "Today's",
                            offsetMilliseconds: 160,
                            durationMilliseconds: 360
                        },
                        {
                            text: "question",
                            offsetMilliseconds: 520,
                            durationMilliseconds: 600
                        }
                    ],
                    locale: "en-US",
                    confidence: 0.9069429
                }
            ]
        };

        const logPrefix = '[测试数据转换]';
        const convertedData = task.convertAzureSpeechToWhisperFormat(azureSpeechData, logPrefix);

        // 验证转换结果
        assert(convertedData, '转换应返回数据对象');
        assertEquals(convertedData.task, 'transcribe', 'task字段应为transcribe');
        assertEquals(convertedData.language, 'en', 'language字段应为en');
        assertEquals(convertedData.duration, 12.672, 'duration应为毫秒转秒的结果');
        assertEquals(convertedData.text, "Today's question period comes after yesterday's throne speech.", 'text应来自combinedPhrases');
        assert(Array.isArray(convertedData.segments), 'segments应为数组');
        assertEquals(convertedData.segments.length, 1, 'segments数量应与phrases一致');

        // 验证segment格式
        const segment = convertedData.segments[0];
        assertEquals(segment.id, 0, 'segment.id应从0开始');
        assertEquals(segment.seek, 0, 'segment.seek应为0');
        assertEquals(segment.start, 0.16, 'segment.start应为offsetMilliseconds/1000');
        assertEquals(segment.end, 8.0, 'segment.end应为(offsetMilliseconds+durationMilliseconds)/1000');
        assertEquals(segment.text, azureSpeechData.phrases[0].text, 'segment.text应与phrase.text一致');
        assert(Array.isArray(segment.tokens), 'segment.tokens应为数组');
        assertEquals(segment.tokens.length, 0, 'segment.tokens应为空数组');
        assertEquals(segment.temperature, 0, 'segment.temperature应为0');
        assert(typeof segment.avg_logprob === 'number', 'segment.avg_logprob应为数字');
        assert(typeof segment.compression_ratio === 'number', 'segment.compression_ratio应为数字');
        assert(typeof segment.no_speech_prob === 'number', 'segment.no_speech_prob应为数字');

        // 验证words字段转换
        assert(Array.isArray(segment.words), 'segment.words应为数组');
        assertEquals(segment.words.length, 2, 'words数量应与原始数据一致');
        
        const word = segment.words[0];
        assertEquals(word.text, "Today's", 'word.text应正确');
        assertEquals(word.start, 0.16, 'word.start应为offsetMilliseconds/1000');
        assertEquals(word.end, 0.52, 'word.end应为(offsetMilliseconds+durationMilliseconds)/1000');

        logger.info(`${testLogPrefix} 数据格式转换测试成功完成`);
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== GetTranscriptionTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1); // 以错误码退出，方便CI/CD集成
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0); // 成功退出
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    if (error.stack) {
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    }
    process.exit(1);
});
