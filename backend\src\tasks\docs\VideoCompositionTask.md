# VideoCompositionTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **finalVideoPath** (string): 最终视频文件路径，来自GenerateVideoTask
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **videoConfig** (object): 视频配置对象
- **compositionParams** (object): 合成参数
  - **outputFormat** (string): 输出格式，默认'mp4'
  - **quality** (string): 视频质量，默认'high'
  - **codec** (string): 视频编码，默认'libx264'

## 2. 输出上下文参数 (Output Context)

- **composedVideoPath** (string): 合成后视频文件完整路径
- **composedVideoFileName** (string): 合成后视频文件名
- **compositionStats** (object): 合成统计信息
  - **inputVideoSize** (number): 输入视频大小（字节）
  - **outputVideoSize** (number): 输出视频大小（字节）
  - **compressionRatio** (number): 压缩比例
  - **processingTime** (number): 处理时间（毫秒）
  - **videoResolution** (string): 视频分辨率
  - **videoDuration** (number): 视频时长（秒）
- **compositionStatus** (string): 合成状态，成功时为'success'
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 合成参数格式
```json
{
  "outputFormat": "mp4",
  "quality": "high",
  "codec": "libx264",
  "preset": "medium",
  "crf": 23,
  "maxBitrate": "2M",
  "bufsize": "4M"
}
```

### FFmpeg编码参数
```
-c:v libx264 -preset medium -crf 23 -maxrate 2M -bufsize 4M
```

## 4. 文件操作

### 保存的文件格式
- **.mp4**: 最终合成的视频文件

### 文件命名规则
- **模式**: `{videoIdentifier}_final_composed.mp4`
- **示例**: `video123_final_composed.mp4`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 自动创建目录（如果不存在）
- 支持多种输出格式和质量设置

## 5. 执行逻辑概述

视频合成任务负责对GenerateVideoTask生成的视频进行最终的质量优化和格式标准化。任务首先验证输入视频文件的存在性和完整性，然后根据配置参数进行视频重编码和优化。合成过程包括视频质量调整、比特率控制、格式转换等操作，确保输出视频符合发布标准。任务使用FFmpeg的高级编码参数，支持CRF质量控制、预设优化和比特率限制。处理过程提供详细的统计信息，包括文件大小变化、压缩效果等。最终生成适合分发和播放的高质量视频文件，完成整个视频制作流水线的最后环节。
