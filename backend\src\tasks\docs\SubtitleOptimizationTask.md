# SubtitleOptimizationTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **transcriptionData** (object): 转录数据对象，来自GetTranscriptionTask或GetTranscriptionTaskByCloudflare
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **transcriptionSegments** (Array): 转录片段数组
- **processingStats** (object): 处理统计信息

## 2. 输出上下文参数 (Output Context)

- **optimizationStatus** (string): 优化状态，成功时为'success'
- **originalSegmentsCount** (number): 原始片段数量
- **optimizedSegmentsCount** (number): 优化后片段数量
- **optimizedData** (object): 优化后的数据对象
- **optimizedFilePath** (string): 优化后数据文件路径
- **simplifiedSubtitleJsonArray** (Array): 简化字幕JSON数组
- **simplifiedSubtitleJsonPath** (string): 简化字幕JSON文件路径
- **optimizedEnglishSrtContent** (string): 优化后的英文SRT内容
- **englishSrtPath** (string): 英文SRT文件路径
- **optimizationSteps** (Array): 优化步骤列表
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 简化字幕JSON格式
```json
[
  {
    "id": 1,
    "start": 0.0,
    "end": 2.5,
    "text": "Hello world",
    "words": [
      {
        "text": "Hello",
        "start": 0.0,
        "end": 1.0
      },
      {
        "text": "world",
        "start": 1.0,
        "end": 2.5
      }
    ]
  }
]
```

### SRT格式输出
```
1
00:00:00,000 --> 00:00:02,500
Hello world

2
00:00:02,500 --> 00:00:05,000
This is another subtitle
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 优化后数据文件和简化字幕JSON文件
- **.srt**: 英文SRT字幕文件

### 文件命名规则
- **优化数据**: `{videoIdentifier}_optimized_transcription.json`
- **简化字幕**: `{videoIdentifier}_simplified_subtitle.json`
- **SRT字幕**: `{videoIdentifier}_english.srt`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保字符正确保存

## 5. 执行逻辑概述

字幕优化任务负责将原始转录数据转换为适合视频制作的优化字幕格式。任务首先验证输入数据的完整性，然后执行多步骤优化处理：短segments合并、句子片段合并、智能拆分长句子等。核心功能包括将Azure或Cloudflare的转录格式转换为标准化的简化JSON格式，提取词级别的时间戳信息，并生成符合SRT标准的字幕文件。优化算法考虑了字幕显示的最佳实践，如合适的字幕长度、时间间隔和文本断句。整个过程提供详细的统计信息和优化步骤记录，确保字幕质量和可读性。
