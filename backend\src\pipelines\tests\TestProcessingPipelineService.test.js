/**
 * @文件名: processing.test.js
 * @功能概述: TestProcessingPipelineService完整流水线集成测试脚本
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 *
 * @功能描述:
 *   对TestProcessingPipelineService进行全面测试，包含8个任务的完整流水线：
 *   1. ConvertToAudioTask - 视频转音频
 *   2. GetTranscriptionTask - 语音转录
 *   3. TranscriptionCorrectionTask - 转录校正
 *   4. TranslateSubtitleTask - 字幕翻译
 *   5. SubtitleClozeTask - 字幕挖空
 *   6. BilingualSubtitleMergeTask - 双语字幕合并增强
 *   7. GenerateASSTask - ASS字幕生成
 *   8. GenerateVideoTask - 9:16视频生成
 *
 * @测试数据（简化输入）:
 *   - videoIdentifier: test_0613
 *   - savePath: C:/Users/<USER>/Desktop/codebase/express/backend/uploads/output
 *   - originalVideoPath: C:/Users/<USER>/Desktop/codebase/express/backend/uploads/input/test_0612.mp4
 *
 * @运行方式: node backend/src/pipelines/tests/processing.test.js
 * @注意事项:
 *   - LLM调用是真实发生的，可能会产生费用并耗时较长
 *   - 使用最高细粒度的日志记录整个流水线执行情况
 *   - 测试会生成文件到指定的output目录
 *   - 流水线会自动从原始视频开始，完成所有处理步骤
 */

const fs = require('fs');
const path = require('path');
const TestProcessingPipelineService = require('../TestProcessingPipelineService');
const logger = require('../../utils/logger');
const { TASK_STATUS } = require('../../constants/progress');

// 测试日志前缀
const testLogPrefix = '[文件：processing.test.js][完整流水线集成测试]';

// ========== 简化测试数据配置 ==========
const TEST_CONFIG = {
    videoIdentifier: 'test_0613',
    savePath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/output',
    originalVideoPath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/input/test_0612.mp4'
};





// 确保输出目录存在
if (!fs.existsSync(TEST_CONFIG.savePath)) {
    try {
        fs.mkdirSync(TEST_CONFIG.savePath, { recursive: true });
        logger.info(`${testLogPrefix} 输出目录已创建: ${TEST_CONFIG.savePath}`);
    } catch (err) {
        logger.error(`${testLogPrefix} 创建输出目录失败: ${TEST_CONFIG.savePath}, Error: ${err.message}`);
        process.exit(1);
    }
} else {
    logger.info(`${testLogPrefix} 输出目录已存在: ${TEST_CONFIG.savePath}`);
}

// 简易断言函数 (内部使用硬编码的 uploadDirForTest 或传入的路径)
function assert(condition, message) {
    if (!condition) {
        const fullMessage = `[断言失败] ${message}`;
        logger.error(`${testLogPrefix}${fullMessage}`);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${JSON.stringify(expected)}, 实际: ${JSON.stringify(actual)}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${JSON.stringify(actual)})`);
}

// assertExists 现在直接接收完整路径
function assertExists(fullFilePath, message) {
    if (!fs.existsSync(fullFilePath)) {
        const fullMessage = `${message} - 文件不存在: ${fullFilePath}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (文件存在: ${fullFilePath})`);
    const stats = fs.statSync(fullFilePath);
    if (stats.size === 0) {
        logger.warn(`${testLogPrefix}[警告] 文件为空: ${fullFilePath}`);
    }
    assert(stats.size > 0, `文件 ${fullFilePath} 不应为空`);
}


async function runCompleteProcessingPipelineTest() {
    logger.info(`${testLogPrefix} ========== 开始执行完整流水线集成测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    await runSingleTest('1. 完整8任务流水线测试 (ConvertToAudio → GetTranscription → TranscriptionCorrection → Translate → Cloze → BilingualMerge → GenerateASS → GenerateVideo)', async () => {
        const reqId = `test-pipeline-${Date.now()}`;

        logger.info(`${testLogPrefix}[步骤 1] 准备简化的初始上下文...`);

        // 使用简化的输入上下文（添加ConvertToAudioTask需要的字段）
        const originalVideoName = path.basename(TEST_CONFIG.originalVideoPath);
        const uploadedVideoDirPath = path.dirname(TEST_CONFIG.originalVideoPath);

        const initialContext = {
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            reqId: reqId,
            originalVideoPath: TEST_CONFIG.originalVideoPath,
            originalVideoName: originalVideoName,
            uploadedVideoDirPath: uploadedVideoDirPath,
            savePath: TEST_CONFIG.savePath
        };

        logger.info(`${testLogPrefix}[步骤 1.1] 简化初始上下文构建完成:`);
        logger.info(`${testLogPrefix}  - videoIdentifier: ${initialContext.videoIdentifier}`);
        logger.info(`${testLogPrefix}  - reqId: ${initialContext.reqId}`);
        logger.info(`${testLogPrefix}  - originalVideoPath: ${initialContext.originalVideoPath}`);
        logger.info(`${testLogPrefix}  - originalVideoName: ${initialContext.originalVideoName}`);
        logger.info(`${testLogPrefix}  - uploadedVideoDirPath: ${initialContext.uploadedVideoDirPath}`);
        logger.info(`${testLogPrefix}  - savePath: ${initialContext.savePath}`);

        // 步骤 2: 实例化并执行完整流水线服务
        logger.info(`${testLogPrefix}[步骤 2] 实例化TestProcessingPipelineService并执行完整8任务流水线...`);
        const pipelineService = new TestProcessingPipelineService(reqId);

        // 定义进度回调数组，收集所有进度事件（最高细粒度日志）
        const progressEvents = [];
        const serviceProgressCallback = (data) => {
            // 最高细粒度的进度日志记录
            logger.info(`${testLogPrefix}[🔄进度回调] ${JSON.stringify(data, null, 2)}`);
            progressEvents.push(data);
        };

        // 记录流水线开始执行时间
        const startTime = Date.now();
        logger.info(`${testLogPrefix}[步骤 2.1] 🚀 开始执行完整流水线，包含8个任务:`);
        logger.info(`${testLogPrefix}  1️⃣ ConvertToAudioTask - 视频转音频`);
        logger.info(`${testLogPrefix}  2️⃣ GetTranscriptionTask - 语音转录`);
        logger.info(`${testLogPrefix}  3️⃣ TranscriptionCorrectionTask - 转录校正`);
        logger.info(`${testLogPrefix}  4️⃣ TranslateSubtitleTask - 字幕翻译`);
        logger.info(`${testLogPrefix}  5️⃣ SubtitleClozeTask - 字幕挖空`);
        logger.info(`${testLogPrefix}  6️⃣ BilingualSubtitleMergeTask - 双语字幕合并增强`);
        logger.info(`${testLogPrefix}  7️⃣ GenerateASSTask - ASS字幕生成`);
        logger.info(`${testLogPrefix}  8️⃣ GenerateVideoTask - 9:16视频生成`);

        // 执行完整流水线（包含真实LLM调用）
        const result = await pipelineService.processSubtitles(initialContext, serviceProgressCallback);

        // 计算总执行时间
        const duration = Date.now() - startTime;
        logger.info(`${testLogPrefix}[步骤 2.2] ✅ 完整流水线执行完成，总耗时: ${(duration / 1000).toFixed(2)}秒`);

        // 步骤 3: 验证流水线执行结果
        logger.info(`${testLogPrefix}[步骤 3] 🔍 验证完整流水线执行结果...`);
        assertEquals(result.status, 'completed', '流水线状态应为 completed');
        assert(result.context, '流水线结果应包含 context 对象');

        // 解构所有预期的输出字段（8个任务的完整输出）
        const {
            // ConvertToAudioTask输出
            audioFilePath,
            audioDuration,
            // GetTranscriptionTask输出
            apiResponse,
            transcriptionStatus,
            // TranscriptionCorrectionTask输出
            simplifiedSubtitleJsonPath,
            englishSrtPath,
            correctedFullText,
            simplifiedSubtitleJsonArray,
            // TranslateSubtitleTask输出
            translatedSubtitleJsonPath,
            chineseSrtPath,
            translatedSubtitleJsonArray,
            // SubtitleClozeTask输出
            clozedSubtitleJsonPath,
            clozedEnglishSrtPath,  // 注意：SubtitleClozeTask输出的是clozedEnglishSrtPath，不是clozedSubtitleSrtPath
            clozedSubtitleJsonArray,
            // BilingualSubtitleMergeTask输出
            enhancedBilingualSubtitleJsonPath,
            enhancedBilingualSubtitleJsonArray,
            // GenerateASSTask输出
            assFilePath,
            assContent: contextAssContent,
            assContentLength,
            videoConfig, // 注意：这个videoConfig现在来自GenerateASSTask从配置文件读取
            // GenerateVideoTask输出（临时文件已删除）
            finalVideoPath,
            videoGenerationStats
        } = result.context;

        // 验证所有8个任务的关键产物字段
        logger.info(`${testLogPrefix}[步骤 3.1] 验证任务1-ConvertToAudioTask输出...`);
        assert(audioFilePath, 'Context 应包含 audioFilePath');
        assert(audioDuration, 'Context 应包含 audioDuration');

        logger.info(`${testLogPrefix}[步骤 3.2] 验证任务2-GetTranscriptionTask输出...`);
        assert(apiResponse, 'Context 应包含 apiResponse');
        assert(transcriptionStatus, 'Context 应包含 transcriptionStatus');

        logger.info(`${testLogPrefix}[步骤 3.3] 验证任务3-TranscriptionCorrectionTask输出...`);
        assert(simplifiedSubtitleJsonPath, 'Context 应包含 simplifiedSubtitleJsonPath');
        assert(englishSrtPath, 'Context 应包含 englishSrtPath');
        assert(correctedFullText, 'Context 应包含 correctedFullText');
        assert(simplifiedSubtitleJsonArray && Array.isArray(simplifiedSubtitleJsonArray), 'Context 应包含 simplifiedSubtitleJsonArray 数组');

        logger.info(`${testLogPrefix}[步骤 3.4] 验证任务4-TranslateSubtitleTask输出...`);
        assert(translatedSubtitleJsonPath, 'Context 应包含 translatedSubtitleJsonPath');
        assert(chineseSrtPath, 'Context 应包含 chineseSrtPath');
        assert(translatedSubtitleJsonArray && Array.isArray(translatedSubtitleJsonArray), 'Context 应包含 translatedSubtitleJsonArray 数组');

        logger.info(`${testLogPrefix}[步骤 3.5] 验证任务5-SubtitleClozeTask输出...`);
        assert(clozedSubtitleJsonPath, 'Context 应包含 clozedSubtitleJsonPath');
        assert(clozedEnglishSrtPath, 'Context 应包含 clozedEnglishSrtPath');
        assert(clozedSubtitleJsonArray && Array.isArray(clozedSubtitleJsonArray), 'Context 应包含 clozedSubtitleJsonArray 数组');

        logger.info(`${testLogPrefix}[步骤 3.6] 验证任务6-BilingualSubtitleMergeTask输出...`);
        assert(enhancedBilingualSubtitleJsonPath, 'Context 应包含 enhancedBilingualSubtitleJsonPath');
        assert(enhancedBilingualSubtitleJsonArray && Array.isArray(enhancedBilingualSubtitleJsonArray), 'Context 应包含 enhancedBilingualSubtitleJsonArray 数组');

        logger.info(`${testLogPrefix}[步骤 3.7] 验证任务7-GenerateASSTask输出...`);
        assert(assFilePath, 'Context 应包含 assFilePath');
        assert(contextAssContent, 'Context 应包含 assContent');
        assert(assContentLength, 'Context 应包含 assContentLength');
        assert(videoConfig, 'Context 应包含 videoConfig');

        logger.info(`${testLogPrefix}[步骤 3.8] 验证任务8-GenerateVideoTask输出...`);
        assert(finalVideoPath, 'Context 应包含 finalVideoPath');
        assert(videoGenerationStats, 'Context 应包含 videoGenerationStats');

        logger.info(`${testLogPrefix}[步骤 3.9] ✅ 所有8个任务的上下文关键字段验证通过。`);

        // 步骤 4: 验证所有文件产物是否存在且非空
        logger.info(`${testLogPrefix}[步骤 4] 🗂️ 验证所有8个任务的文件产物...`);

        logger.info(`${testLogPrefix}[步骤 4.1] 验证任务1-ConvertToAudioTask文件产物...`);
        assertExists(audioFilePath, 'Audio 文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.2] 验证任务3-TranscriptionCorrectionTask文件产物...`);
        assertExists(simplifiedSubtitleJsonPath, 'Simplified JSON 文件应存在且非空');
        assertExists(englishSrtPath, 'English SRT 文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.3] 验证任务4-TranslateSubtitleTask文件产物...`);
        assertExists(translatedSubtitleJsonPath, 'Translated JSON 文件应存在且非空');
        assertExists(chineseSrtPath, 'Chinese SRT 文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.4] 验证任务5-SubtitleClozeTask文件产物...`);
        assertExists(clozedSubtitleJsonPath, 'Clozed JSON 文件应存在且非空');
        assertExists(clozedEnglishSrtPath, 'Clozed SRT 文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.5] 验证任务6-BilingualSubtitleMergeTask文件产物...`);
        assertExists(enhancedBilingualSubtitleJsonPath, 'Enhanced Bilingual JSON 文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.6] 验证任务7-GenerateASSTask文件产物...`);
        assertExists(assFilePath, 'ASS 字幕文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.7] 验证任务8-GenerateVideoTask文件产物...`);
        assertExists(finalVideoPath, '最终视频文件应存在且非空');

        logger.info(`${testLogPrefix}[步骤 4.8] ✅ 所有文件产物存在性与非空验证通过。`);
        
        // 步骤 6: 验证进度回调
        logger.info(`${testLogPrefix}[步骤 6] 验证进度回调...`);
        assert(progressEvents.length > 0, '应至少有一个进度回调事件');
        const pipelineEvents = progressEvents.filter(p => p.pipelineName);
        assert(pipelineEvents.some(p => p.pipelineStatus === 'running'), '应有 running 状态的流水线进度回调');
        assert(pipelineEvents.some(p => p.pipelineStatus === 'completed'), '应有 completed 状态的流水线进度回调');
        
        // 验证任务级回调 (简化验证)
        logger.info(`${testLogPrefix} 进度回调事件总数: ${progressEvents.length}`);
        logger.info(`${testLogPrefix} 进度回调基本验证通过。`);

        // 步骤 7: 验证文件内容格式和质量
        logger.info(`${testLogPrefix}[步骤 7] 📄 验证文件内容格式和质量...`);

        logger.info(`${testLogPrefix}[步骤 7.1] 验证中文SRT文件内容...`);
        const chineseSrtContent = fs.readFileSync(chineseSrtPath, 'utf-8');
        assert(chineseSrtContent.includes(' --> '), '中文SRT文件内容应包含时间轴标记');
        assert(/[\u4e00-\u9fa5]/.test(chineseSrtContent), '中文SRT文件应包含中文字符');

        logger.info(`${testLogPrefix}[步骤 7.2] 验证挖空字幕SRT文件内容...`);
        const clozedSrtContent = fs.readFileSync(clozedEnglishSrtPath, 'utf-8');
        assert(clozedSrtContent.includes(' --> '), '挖空SRT文件内容应包含时间轴标记');
        assert(clozedSrtContent.includes('()'), '挖空SRT文件应包含挖空占位符');

        logger.info(`${testLogPrefix}[步骤 7.3] 验证增强双语字幕JSON内容...`);
        const enhancedBilingualContent = JSON.parse(fs.readFileSync(enhancedBilingualSubtitleJsonPath, 'utf-8'));
        assert(Array.isArray(enhancedBilingualContent), '增强双语字幕应为数组格式');
        assert(enhancedBilingualContent.length > 0, '增强双语字幕数组不应为空');
        assert(enhancedBilingualContent[0].words_explanation, '增强双语字幕应包含词汇解释');

        logger.info(`${testLogPrefix}[步骤 7.4] 验证ASS字幕文件内容...`);
        const assContent = fs.readFileSync(assFilePath, 'utf-8');
        assert(assContent.includes('[Script Info]'), 'ASS文件应包含Script Info部分');
        assert(assContent.includes('[V4+ Styles]'), 'ASS文件应包含V4+ Styles部分');
        assert(assContent.includes('[Events]'), 'ASS文件应包含Events部分');
        assert(assContent.includes('Dialogue:'), 'ASS文件应包含Dialogue事件');

        logger.info(`${testLogPrefix}[步骤 7.5] ✅ 所有文件内容格式验证通过。`);
        
        // 记录完整流水线的最终产物信息
        logger.info(`${testLogPrefix}[步骤 8] 📋 完整流水线最终产物汇总:`);
        logger.info(`${testLogPrefix}  📝 Corrected Full Text (length): ${correctedFullText?.length}字符`);
        logger.info(`${testLogPrefix}  📄 Simplified JSON Path: ${simplifiedSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}  📄 English SRT Path: ${englishSrtPath}`);
        logger.info(`${testLogPrefix}  📄 Translated JSON Path: ${translatedSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}  📄 Chinese SRT Path: ${chineseSrtPath}`);
        logger.info(`${testLogPrefix}  📄 Clozed JSON Path: ${clozedSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}  📄 Clozed SRT Path: ${clozedEnglishSrtPath}`);
        logger.info(`${testLogPrefix}  📄 Enhanced Bilingual JSON Path: ${enhancedBilingualSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}  🎬 ASS Subtitle File Path: ${assFilePath}`);
        logger.info(`${testLogPrefix}  🎥 Final Video Path: ${finalVideoPath}`);
        logger.info(`${testLogPrefix}  ⏱️ Audio Duration: ${audioDuration}秒`);
        logger.info(`${testLogPrefix}  📈 Video Generation Stats: ${JSON.stringify(videoGenerationStats, null, 2)}`);
        logger.info(`${testLogPrefix}  🎥 Video Config: ${JSON.stringify(videoConfig, null, 2)}`);
        logger.info(`${testLogPrefix}  📝 注意: 临时文件（扩展音频、进度条视频、背景视频）已自动清理`);

        // 记录进度回调统计
        logger.info(`${testLogPrefix}[步骤 9] 📊 进度回调统计:`);
        logger.info(`${testLogPrefix}  - 总进度回调次数: ${progressEvents.length}`);
        logger.info(`${testLogPrefix}  - 流水线级回调: ${progressEvents.filter(p => p.pipelineName).length}`);
        logger.info(`${testLogPrefix}  - 任务级回调: ${progressEvents.filter(p => p.taskName).length}`);
        logger.info(`${testLogPrefix}  - LLM调用回调: ${progressEvents.filter(p => p.llmStage).length}`);

    });

    // --- 完整流水线测试总结 ---
    logger.info(`${testLogPrefix} ========== 完整流水线集成测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 🎯 测试范围: 8个任务的完整流水线`);
    logger.info(`${testLogPrefix} 📊 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} ✅ 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} ❌ 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} 💥 测试未全部通过，存在失败用例。`);
        process.exit(1); // 以错误码退出，方便CI/CD集成
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试用例通过！完整流水线运行正常！`);
        process.exit(0); // 成功退出
    }
}

// 错误处理（顶层捕获）
process.on('uncaughtException', (error) => {
    logger.error(`${testLogPrefix}[UNCAUGHT_EXCEPTION] 💥 未捕获异常: ${error.message}`);
    logger.error(`${testLogPrefix} 异常堆栈: ${error.stack}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error(`${testLogPrefix}[UNHANDLED_REJECTION] 💥 未处理的Promise拒绝: ${reason}`);
    // logger.error(`${testLogPrefix} Promise对象:`, promise); // Promise对象可能很大
    if (reason instanceof Error) {
        logger.error(`${testLogPrefix} Reason stack: ${reason.stack}`);
    }
    process.exit(1);
});

// 立即执行完整流水线测试
runCompleteProcessingPipelineTest().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层 .catch 捕获到未处理异常: ${error.message}`);
    if (error.stack) {
        logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
    }
    process.exit(1);
});
