{"name": "express-pipeline-system", "version": "1.0.0", "description": "A multi-task pipeline processing system backend, built with Express.js, designed for orchestrating complex workflows (e.g., video processing, LLM interactions).", "main": "index.js", "scripts": {"start:backend": "npm start --prefix backend", "dev:backend": "npm run dev --prefix backend", "start": "npm run start:backend", "dev": "npm run dev:backend"}, "keywords": ["express", "pipeline", "workflow", "task-orchestration", "backend-system", "video", "llm"], "author": "", "license": "ISC", "devDependencies": {"@stagewise/toolbar": "^0.4.6", "nodemon": "^3.1.10"}, "dependencies": {"ffmpeg-static": "^4.2.7"}}