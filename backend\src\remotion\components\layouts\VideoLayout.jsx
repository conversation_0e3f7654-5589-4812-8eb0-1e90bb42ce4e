/**
 * @功能概述: 视频布局组件，纯渲染组件，所有样式通过props传入
 * @组件类型: Remotion布局组件（数据驱动）
 * @使用场景: 视频内容的区域划分和布局
 */

import React from 'react';
import { useCurrentFrame, useVideoConfig } from 'remotion';

/**
 * @功能概述: 原视频区域组件（纯渲染器）
 * @param {Object} props - 组件属性
 * @param {Object} props.config - 区域配置对象（来自template）
 * @returns {JSX.Element} 原视频区域组件
 */
export const OriginalVideoArea = ({ config = {} }) => {
    const frame = useCurrentFrame();
    const { fps } = useVideoConfig();
    const currentTime = frame / fps;

    // 检查是否在显示时间范围内
    const duration = config.duration || {};
    const isVisible = currentTime >= (duration.start || 0) &&
                     (duration.end === "auto" || currentTime <= (duration.end || 3));

    if (!isVisible) return null;

    const position = config.position || {};
    const style = config.style || {};

    return (
        <div style={{
            position: 'absolute',
            top: position.top,
            left: position.left,
            width: position.width,
            height: position.height,
            backgroundColor: style.backgroundColor,
            border: style.border,
            borderRadius: style.borderRadius,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: style.color,
            fontSize: style.fontSize,
            fontFamily: style.fontFamily,
            zIndex: 2
        }}>
            {style.placeholder || '原视频播放区域'}
        </div>
    );
};

/**
 * @功能概述: 文本区域组件（纯渲染器）
 * @param {Object} props - 组件属性
 * @param {Object} props.config - 区域配置对象（来自template）
 * @returns {JSX.Element} 文本区域组件
 */
export const TextArea = ({ config = {} }) => {
    const frame = useCurrentFrame();
    const { fps } = useVideoConfig();
    const currentTime = frame / fps;

    // 检查是否在显示时间范围内
    const duration = config.duration || {};
    const isVisible = currentTime >= (duration.start || 3) &&
                     (duration.end === "auto" || currentTime <= duration.end);

    if (!isVisible) return null;

    const position = config.position || {};
    const style = config.style || {};

    return (
        <div style={{
            position: 'absolute',
            top: position.top,
            left: position.left,
            width: position.width,
            height: position.height,
            backgroundColor: style.backgroundColor,
            borderRadius: style.borderRadius,
            padding: style.padding,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            zIndex: 1
        }}>
            {/* 文本区域内容由字幕组件填充 */}
        </div>
    );
};
