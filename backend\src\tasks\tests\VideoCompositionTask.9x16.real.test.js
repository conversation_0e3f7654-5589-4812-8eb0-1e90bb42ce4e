/**
 * @fileoverview VideoCompositionTask 9:16短视频真实执行测试
 * @description 专门测试16:9视频转9:16短视频的完整流程
 * @version 1.0.0
 * <AUTHOR>
 * @created 2025-01-07
 */

const VideoCompositionTask = require('../VideoCompositionTask');
const { createHardcodedTestConfig } = require('../../utils/compositionConfigConverter');
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs').promises;

// 测试日志前缀
const testLogPrefix = '[文件：VideoCompositionTask.9x16.real.test.js][9:16短视频真实测试]';

/**
 * 创建9:16短视频测试上下文
 * @param {string} scenario - 测试场景
 * @returns {Object} 测试上下文对象
 */
function create9x16TestContext(scenario = 'balanced') {
    const testDataDir = "C:\\Users\\<USER>\\Desktop\\代码库\\express\\backend\\uploads\\test-data";
    const baseFileName = "videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z";
    
    // 获取场景配置
    const compositionConfig = createHardcodedTestConfig(scenario);
    
    return {
        // 基础信息
        videoIdentifier: `9x16_${scenario}_test`,
        reqId: `9x16-${scenario}-${Date.now()}`,
        
        // 文件路径
        processedVideoPath: path.join(testDataDir, `${baseFileName}.mp4`),
        processedAudioPath: path.join(testDataDir, `videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-_744348a8_audio.mp3`),
        assSubtitlePath: path.join(testDataDir, `test_123_subtitle.ass`),
        savePath: testDataDir,
        
        // 9:16短视频专用配置
        compositionConfig: compositionConfig,
        
        // 字幕数据（模拟双语字幕）
        bilingualSubtitles: [
            {
                startTime: 0,
                endTime: 3.32,
                english: "Good afternoon, there is another wildfire evacuation alert tonight.",
                chinese: "下午好，今晚又发布了野火疏散警报。"
            },
            {
                startTime: 3.32,
                endTime: 8.56,
                english: "The County of Northern Lights telling residents of Hawk Hills, which is near Twin Lakes Provincial",
                chinese: "北极光县通知霍克希尔斯的居民，该地靠近双湖省立公园，"
            },
            {
                startTime: 8.56,
                endTime: 11.8,
                english: "Park, to be ready to evacuate at any time.",
                chinese: "要随时做好撤离准备。"
            }
        ],
        
        // 测试元数据
        testMetadata: {
            scenario: scenario,
            expectedOutputs: [
                `${scenario}_9x16_extended_audio.mp3`,
                `${scenario}_9x16_final_video.mp4`
            ],
            testDescription: `16:9视频转9:16短视频 - ${scenario}模式测试`
        }
    };
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 文件是否存在
 */
async function fileExists(filePath) {
    try {
        await fs.access(filePath);
        return true;
    } catch {
        return false;
    }
}

/**
 * 获取文件大小
 * @param {string} filePath - 文件路径
 * @returns {Promise<number>} 文件大小（字节）
 */
async function getFileSize(filePath) {
    try {
        const stats = await fs.stat(filePath);
        return stats.size;
    } catch {
        return 0;
    }
}

/**
 * 测试1: 平衡模式9:16短视频生成
 */
async function test1_BalancedMode9x16Video() {
    logger.info(`${testLogPrefix} ========== 测试1: 平衡模式9:16短视频生成 ==========`);
    
    try {
        const task = new VideoCompositionTask();
        const context = create9x16TestContext('balanced');
        
        logger.info(`${testLogPrefix} 测试配置:`);
        logger.info(`${testLogPrefix}   - 音频重复: ${context.compositionConfig.audioRepeatCount}次`);
        logger.info(`${testLogPrefix}   - 输出尺寸: ${context.compositionConfig.outputWidth}x${context.compositionConfig.outputHeight}`);
        logger.info(`${testLogPrefix}   - 视频质量: CRF=${context.compositionConfig.crf}`);
        
        // 检查输入文件
        const videoExists = await fileExists(context.processedVideoPath);
        const audioExists = await fileExists(context.processedAudioPath);
        const assExists = await fileExists(context.assSubtitlePath);
        
        logger.info(`${testLogPrefix} 输入文件检查:`);
        logger.info(`${testLogPrefix}   - 视频文件: ${videoExists ? '✅' : '❌'} ${context.processedVideoPath}`);
        logger.info(`${testLogPrefix}   - 音频文件: ${audioExists ? '✅' : '❌'} ${context.processedAudioPath}`);
        logger.info(`${testLogPrefix}   - ASS文件: ${assExists ? '✅' : '❌'} ${context.assSubtitlePath}`);
        
        if (!videoExists || !audioExists || !assExists) {
            logger.warn(`${testLogPrefix} ⚠️ 部分输入文件不存在，跳过测试`);
            return true;
        }
        
        // 设置进度回调
        const progressUpdates = [];
        const progressCallback = (progressData) => {
            progressUpdates.push(progressData);
            logger.info(`${testLogPrefix}[进度] ${progressData.taskName}: ${progressData.detail} (${progressData.current}/${progressData.total})`);
        };
        
        // 执行任务
        logger.info(`${testLogPrefix} 开始执行9:16短视频生成...`);
        const startTime = Date.now();
        
        const result = await task.execute(context, progressCallback);
        
        const executionTime = Date.now() - startTime;
        logger.info(`${testLogPrefix} 执行完成，耗时: ${executionTime}ms`);
        
        // 验证结果
        if (result && result.finalVideoPath) {
            const outputExists = await fileExists(result.finalVideoPath);
            const outputSize = await getFileSize(result.finalVideoPath);
            
            logger.info(`${testLogPrefix} 输出验证:`);
            logger.info(`${testLogPrefix}   - 输出文件: ${outputExists ? '✅' : '❌'} ${result.finalVideoPath}`);
            logger.info(`${testLogPrefix}   - 文件大小: ${(outputSize / 1024 / 1024).toFixed(2)}MB`);
            
            if (outputExists && outputSize > 1024 * 1024) { // 至少1MB
                logger.info(`${testLogPrefix} ✅ 测试1通过: 平衡模式9:16短视频生成成功`);
                return true;
            } else {
                logger.error(`${testLogPrefix} ❌ 测试1失败: 输出文件无效`);
                return false;
            }
        } else {
            logger.error(`${testLogPrefix} ❌ 测试1失败: 任务执行失败`);
            return false;
        }
        
    } catch (error) {
        logger.error(`${testLogPrefix} ❌ 测试1异常: ${error.message}`);
        return false;
    }
}

/**
 * 测试2: 快速模式9:16短视频生成
 */
async function test2_FastMode9x16Video() {
    logger.info(`${testLogPrefix} ========== 测试2: 快速模式9:16短视频生成 ==========`);
    
    try {
        const task = new VideoCompositionTask();
        const context = create9x16TestContext('fast');
        
        logger.info(`${testLogPrefix} 快速模式配置:`);
        logger.info(`${testLogPrefix}   - 音频重复: ${context.compositionConfig.audioRepeatCount}次`);
        logger.info(`${testLogPrefix}   - 输出尺寸: ${context.compositionConfig.outputWidth}x${context.compositionConfig.outputHeight}`);
        logger.info(`${testLogPrefix}   - 编码预设: ${context.compositionConfig.preset}`);
        
        // 检查输入文件
        const videoExists = await fileExists(context.processedVideoPath);
        const audioExists = await fileExists(context.processedAudioPath);
        const assExists = await fileExists(context.assSubtitlePath);
        
        if (!videoExists || !audioExists || !assExists) {
            logger.warn(`${testLogPrefix} ⚠️ 部分输入文件不存在，跳过测试`);
            return true;
        }
        
        // 执行任务（快速模式应该更快完成）
        const startTime = Date.now();
        const result = await task.execute(context);
        const executionTime = Date.now() - startTime;
        
        logger.info(`${testLogPrefix} 快速模式执行时间: ${executionTime}ms`);
        
        // 验证结果
        if (result && result.finalVideoPath) {
            const outputExists = await fileExists(result.finalVideoPath);
            const outputSize = await getFileSize(result.finalVideoPath);
            
            if (outputExists && outputSize > 512 * 1024) { // 至少512KB
                logger.info(`${testLogPrefix} ✅ 测试2通过: 快速模式9:16短视频生成成功`);
                logger.info(`${testLogPrefix}   - 输出: ${result.finalVideoPath}`);
                logger.info(`${testLogPrefix}   - 大小: ${(outputSize / 1024 / 1024).toFixed(2)}MB`);
                return true;
            } else {
                logger.error(`${testLogPrefix} ❌ 测试2失败: 输出文件无效`);
                return false;
            }
        } else {
            logger.error(`${testLogPrefix} ❌ 测试2失败: 任务执行失败`);
            return false;
        }
        
    } catch (error) {
        logger.error(`${testLogPrefix} ❌ 测试2异常: ${error.message}`);
        return false;
    }
}

/**
 * 测试3: 配置验证测试
 */
async function test3_ConfigValidation() {
    logger.info(`${testLogPrefix} ========== 测试3: 配置验证测试 ==========`);
    
    try {
        const task = new VideoCompositionTask();
        
        // 测试所有场景配置
        const scenarios = ['fast', 'balanced', 'high_quality', 'custom'];
        
        for (const scenario of scenarios) {
            const context = create9x16TestContext(scenario);
            const config = context.compositionConfig;
            
            // 验证配置解析
            const parsedConfig = task.parseCompositionConfig(config, `[TEST-${scenario}]`);
            
            // 验证基础参数
            if (parsedConfig.audioRepeatCount < 2 || parsedConfig.audioRepeatCount > 5) {
                logger.error(`${testLogPrefix} ❌ ${scenario}场景音频重复次数无效: ${parsedConfig.audioRepeatCount}`);
                return false;
            }
            
            // 验证9:16比例
            const aspectRatio = parsedConfig.outputWidth / parsedConfig.outputHeight;
            if (Math.abs(aspectRatio - 9/16) > 0.01) {
                logger.error(`${testLogPrefix} ❌ ${scenario}场景宽高比不是9:16: ${aspectRatio}`);
                return false;
            }
            
            // 验证视频布局配置
            if (!parsedConfig.videoLayout || 
                !parsedConfig.videoLayout.firstSegment || 
                !parsedConfig.videoLayout.laterSegments) {
                logger.error(`${testLogPrefix} ❌ ${scenario}场景缺少视频布局配置`);
                return false;
            }
            
            logger.info(`${testLogPrefix} ✅ ${scenario}场景配置验证通过`);
        }
        
        logger.info(`${testLogPrefix} ✅ 测试3通过: 所有配置验证成功`);
        return true;
        
    } catch (error) {
        logger.error(`${testLogPrefix} ❌ 测试3异常: ${error.message}`);
        return false;
    }
}

/**
 * 主测试函数
 */
async function run9x16VideoCompositionTests() {
    logger.info(`${testLogPrefix} ========== 开始9:16短视频真实执行测试 ==========`);
    
    const tests = [
        { name: '平衡模式9:16短视频生成', func: test1_BalancedMode9x16Video },
        { name: '快速模式9:16短视频生成', func: test2_FastMode9x16Video },
        { name: '配置验证测试', func: test3_ConfigValidation }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        logger.info(`${testLogPrefix} 执行: ${test.name}`);
        const result = await test.func();
        if (result) {
            passedTests++;
        }
        logger.info(`${testLogPrefix} ${test.name}: ${result ? '✅ 通过' : '❌ 失败'}`);
    }
    
    logger.info(`${testLogPrefix} ========== 测试完成 ==========`);
    logger.info(`${testLogPrefix} 测试结果: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        logger.info(`${testLogPrefix} 🎉 所有9:16短视频测试通过！`);
        return true;
    } else {
        logger.error(`${testLogPrefix} ❌ 部分9:16短视频测试失败`);
        return false;
    }
}

// 立即执行测试
run9x16VideoCompositionTests().then(success => {
    if (success) {
        logger.info(`${testLogPrefix} ✅ 9:16短视频真实执行测试完成`);
        process.exit(0);
    } else {
        logger.error(`${testLogPrefix} ❌ 9:16短视频真实执行测试失败`);
        process.exit(1);
    }
}).catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
