/**
 * @功能概述: VideoProcessingPipelineService 的独立测试脚本
 *           测试改造后的3任务流水线：ConvertToAudioTask → GetTranscriptionTask → SubtitleOptimizationTask
 * 
 * @运行方式: node videoProcessingPipelineService.test.js
 * 
 * @环境变量:
 *   - RUN_LLM_TESTS=true  启用真实LLM调用测试（耗时较长）
 *   - TEST_MODE=fast      仅运行快速测试（默认）
 *   - TEST_TIMEOUT=300000 设置测试超时时间（毫秒）
 * 
 * @测试目标:
 *   1. 验证3任务流水线的正确编排和执行
 *   2. 测试参数校验和错误处理机制
 *   3. 验证进度监控和回调功能
 *   4. 测试标准化结果返回格式
 * 
 * @注意事项:
 *   - 使用真实的小型测试视频文件进行测试
 *   - 测试文件应控制在较小尺寸，避免长时间等待
 *   - 验证每个任务的输出是否正确传递给下一个任务
 */

const VideoProcessingPipelineService = require('../videoProcessingPipelineService');
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs');
const config = require('../../config');

// 测试配置
const TEST_CONFIG = {
    runLLMTests: true,  // 启用真实LLM调用测试
    testMode: 'full',   // 改为完整测试模式
    timeout: 600000,    // 10分钟超时，给大视频文件足够时间
    maxTestDuration: 300000, // 单个测试最大5分钟
};

// 硬编码的真实测试数据
const REAL_TEST_DATA = {
    savePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output",
    originalVideoPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\[ABC News] ABC World News Tonight with David Muir Full Broadcast - June 15, 2025 (1i1tQYhBPmU).mp4",
    uploadedVideoDirPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads",
    
    // 随机生成的数据
    generateVideoIdentifier: () => `test_video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    generateReqId: () => `test_req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    generateOriginalVideoName: () => `test_video_${Date.now()}.mp4`
};

// 测试日志前缀
const testLogPrefix = '[文件：videoProcessingPipelineService.test.js][视频处理流水线测试][测试执行]';

logger.info(`${testLogPrefix} 🧪 开始测试 VideoProcessingPipelineService`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} 🤖 LLM测试: ${TEST_CONFIG.runLLMTests ? '启用' : '禁用'}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);
logger.info(`${testLogPrefix} 🔄 任务序列: ConvertToAudioTask → GetTranscriptionTask → SubtitleOptimizationTask`);

// 测试统计
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
};

// 简单测试框架
function test(name, testFn, skipCondition = false) {
    testStats.total++;
    
    if (skipCondition) {
        logger.info(`${testLogPrefix} ⏭️  SKIP: ${name}`);
        testStats.skipped++;
        return Promise.resolve();
    }
    
    logger.debug(`${testLogPrefix} ▶️  START: ${name}`);
    
    return Promise.resolve()
        .then(() => testFn())
        .then(() => {
            logger.info(`${testLogPrefix} ✅ PASS: ${name}`);
            testStats.passed++;
        })
        .catch((error) => {
            logger.error(`${testLogPrefix} ❌ FAIL: ${name}`);
            logger.error(`${testLogPrefix}    错误: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix}    堆栈: ${error.stack.split('\n')[1]?.trim()}`);
            }
            testStats.failed++;
        });
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`期望 ${expected}，实际 ${actual}`);
            }
        },
        toEqual: (expected) => {
            if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                throw new Error(`期望 ${JSON.stringify(expected)}，实际 ${JSON.stringify(actual)}`);
            }
        },
        toHaveProperty: (prop) => {
            if (!(prop in actual)) {
                throw new Error(`期望包含属性 ${prop}`);
            }
        },
        toContain: (item) => {
            if (!actual.includes(item)) {
                throw new Error(`期望包含 ${item}`);
            }
        },
        toBeGreaterThan: (value) => {
            if (actual <= value) {
                throw new Error(`期望 ${actual} 大于 ${value}`);
            }
        }
    };
}

// 测试数据构建器
function buildValidContext(overrides = {}) {
    const baseContext = {
        reqId: REAL_TEST_DATA.generateReqId(),
        videoIdentifier: REAL_TEST_DATA.generateVideoIdentifier(),
        uploadedVideoDirPath: REAL_TEST_DATA.uploadedVideoDirPath,
        originalVideoName: REAL_TEST_DATA.generateOriginalVideoName(),
        savePath: REAL_TEST_DATA.savePath,
        ...overrides
    };
    return baseContext;
}

function buildRealTestContext() {
    return {
        reqId: REAL_TEST_DATA.generateReqId(),
        videoIdentifier: REAL_TEST_DATA.generateVideoIdentifier(),
        uploadedVideoDirPath: REAL_TEST_DATA.uploadedVideoDirPath,
        originalVideoName: REAL_TEST_DATA.generateOriginalVideoName(),
        savePath: REAL_TEST_DATA.savePath,
        // 添加真实视频文件路径信息
        originalVideoPath: REAL_TEST_DATA.originalVideoPath
    };
}

function buildInvalidContext() {
    return {
        reqId: 'invalid_test_req'
        // 故意缺少必需字段
    };
}

// 检查真实测试文件是否存在
function checkRealTestFile() {
    const videoPath = REAL_TEST_DATA.originalVideoPath;
    if (!fs.existsSync(videoPath)) {
        logger.warn(`${testLogPrefix} ⚠️  真实测试视频文件不存在: ${videoPath}`);
        return false;
    }
    
    const stats = fs.statSync(videoPath);
    const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    logger.info(`${testLogPrefix} 📁 找到真实测试视频文件: ${videoPath}`);
    logger.info(`${testLogPrefix} 📏 文件大小: ${fileSizeMB} MB`);
    return true;
}

// 主要测试套件
async function runTests() {
    const startTime = Date.now();
    
    // 检查真实测试文件
    const realFileExists = checkRealTestFile();
    logger.info(`${testLogPrefix} 🎬 真实测试文件状态: ${realFileExists ? '可用' : '不可用'}`);
    
    // 1. 基础功能测试（快速）
    await test('1. 服务实例化应该成功', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        expect(service.reqId).toBe('test_req');
        expect(service.processingPipeline).toHaveProperty('execute');
        
        // 验证任务序列
        const tasks = service.processingPipeline.tasks;
        expect(tasks.length).toBe(3);
        expect(tasks[0].name).toBe('ConvertToAudioTask');
        expect(tasks[1].name).toBe('GetTranscriptionTask');
        expect(tasks[2].name).toBe('SubtitleOptimizationTask');
    });

    // 2. 参数校验测试（快速）
    await test('2. 参数校验失败应该返回错误', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        const invalidContext = buildInvalidContext();
        
        const result = await service.processUploadedVideo(invalidContext);
        
        expect(result.status).toBe('failed');
        expect(result.error.message).toContain('必须包含以下字段');
    });

    // 3. 任务序列验证测试（快速）
    await test('3. 任务序列应该按正确顺序添加', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        const tasks = service.processingPipeline.tasks;
        
        expect(tasks.length).toBe(3);
        expect(tasks[0].constructor.name).toBe('ConvertToAudioTask');
        expect(tasks[1].constructor.name).toBe('GetTranscriptionTask');
        expect(tasks[2].constructor.name).toBe('SubtitleOptimizationTask');
    });

    // 4. 进度回调功能测试（快速）
    await test('4. 进度回调机制应该工作正常', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        const progressEvents = [];
        
        const progressCallback = (data) => {
            logger.debug(`${testLogPrefix}[进度回调测试]: ${JSON.stringify(data)}`);
            progressEvents.push(data);
        };
        
        // 使用无效上下文触发参数校验失败，验证回调机制
        const invalidContext = buildInvalidContext();
        await service.processUploadedVideo(invalidContext, progressCallback);
        
        expect(progressEvents.length).toBeGreaterThan(0);
        expect(progressEvents[0]).toHaveProperty('pipelineStatus');
    });

    // 5. 标准化结果格式测试（快速）
    await test('5. 标准化结果格式应该正确', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        const invalidContext = buildInvalidContext();
        
        const result = await service.processUploadedVideo(invalidContext);
        
        // 验证标准化结果结构
        expect(result).toHaveProperty('status');
        expect(result).toHaveProperty('context');
        expect(result).toHaveProperty('tasks');
        expect(result.status).toBe('failed');
    });

    // 6. 硬编码数据验证测试（快速）
    await test('6. 硬编码测试数据验证', async () => {
        const realContext = buildRealTestContext();
        
        // 验证生成的随机数据格式
        expect(realContext.reqId).toContain('test_req_');
        expect(realContext.videoIdentifier).toContain('test_video_');
        expect(realContext.originalVideoName).toContain('.mp4');
        
        // 验证硬编码数据
        expect(realContext.savePath).toBe(REAL_TEST_DATA.savePath);
        expect(realContext.uploadedVideoDirPath).toBe(REAL_TEST_DATA.uploadedVideoDirPath);
        expect(realContext.originalVideoPath).toBe(REAL_TEST_DATA.originalVideoPath);
        
        logger.info(`${testLogPrefix} 📋 生成的测试数据:`);
        logger.info(`${testLogPrefix}   reqId: ${realContext.reqId}`);
        logger.info(`${testLogPrefix}   videoIdentifier: ${realContext.videoIdentifier}`);
        logger.info(`${testLogPrefix}   originalVideoName: ${realContext.originalVideoName}`);
        logger.info(`${testLogPrefix}   savePath: ${realContext.savePath}`);
        logger.info(`${testLogPrefix}   uploadedVideoDirPath: ${realContext.uploadedVideoDirPath}`);
        logger.info(`${testLogPrefix}   originalVideoPath: ${realContext.originalVideoPath}`);
    });

    // 7. 真实视频文件完整流水线测试（现在强制执行）
    await test('7. 真实视频文件完整流水线测试', async () => {
        if (!realFileExists) {
            throw new Error('真实测试视频文件不存在，无法进行完整流水线测试');
        }
        
        logger.warn(`${testLogPrefix} ⚠️  正在执行真实视频处理流水线，这可能需要几分钟...`);
        
        const service = new VideoProcessingPipelineService();
        const realContext = buildRealTestContext();
        
        logger.info(`${testLogPrefix} 🎬 开始真实流水线测试:`);
        logger.info(`${testLogPrefix}   视频文件: ${REAL_TEST_DATA.originalVideoPath}`);
        logger.info(`${testLogPrefix}   输出目录: ${REAL_TEST_DATA.savePath}`);
        
        const progressEvents = [];
        const progressCallback = (data) => {
            logger.info(`${testLogPrefix}[真实测试进度]: ${data.pipelineStatus || data.status} - ${data.currentTaskName || data.taskName || '流水线状态'}`);
            if (data.detail) {
                logger.info(`${testLogPrefix}[真实测试详情]: ${data.detail}`);
            }
            progressEvents.push(data);
        };
        
        const testStartTime = Date.now();
        
        try {
            const result = await service.processUploadedVideo(realContext, progressCallback);
            
            const duration = Date.now() - testStartTime;
            logger.info(`${testLogPrefix} ⏱️  真实流水线测试耗时: ${(duration / 1000).toFixed(2)}秒`);
            
            // 验证结果
            expect(result.status).toBe('completed');
            expect(result).toHaveProperty('files');
            expect(result).toHaveProperty('urls');
            expect(result).toHaveProperty('stats');
            
            // 验证具体输出
            if (result.originalContext) {
                expect(result.originalContext).toHaveProperty('optimizedSrtContent');
                logger.info(`${testLogPrefix} ✅ 字幕优化完成，内容长度: ${result.originalContext.optimizedSrtContent?.length || 0} 字符`);
            }
            
            logger.info(`${testLogPrefix} 🎉 真实流水线测试成功完成！`);
            logger.info(`${testLogPrefix} 📊 处理统计: ${JSON.stringify(result.stats, null, 2)}`);
            
        } catch (error) {
            logger.error(`${testLogPrefix} ❌ 真实流水线测试失败: ${error.message}`);
            logger.error(`${testLogPrefix} 📋 进度事件数量: ${progressEvents.length}`);
            throw error;
        }
        
    }); // 移除跳过条件，强制执行

    // 8. 错误恢复测试（快速）
    await test('8. 流水线错误恢复机制测试', async () => {
        const service = new VideoProcessingPipelineService('test_req');
        
        // 构建会导致第一个任务失败的上下文（视频文件不存在）
        const contextWithMissingFile = buildValidContext({
            uploadedVideoDirPath: "/nonexistent/path",
            originalVideoName: "nonexistent.mp4"
        });
        
        const progressEvents = [];
        const progressCallback = (data) => progressEvents.push(data);
        
        const result = await service.processUploadedVideo(contextWithMissingFile, progressCallback);
        
        // 验证错误处理
        expect(result.status).toBe('failed');
        expect(progressEvents.length).toBeGreaterThan(0);
        
        // 检查是否有失败状态的进度事件
        const hasFailedEvent = progressEvents.some(event => 
            event.pipelineStatus === 'failed' || event.status === 'failed'
        );
        expect(hasFailedEvent).toBe(true);
        
        logger.info(`${testLogPrefix} ✅ 错误恢复机制工作正常`);
    });

    // 输出测试结果
    const totalTime = Date.now() - startTime;
    logger.info(`${testLogPrefix} 📊 测试结果统计:`);
    logger.info(`${testLogPrefix}    总计: ${testStats.total}`);
    logger.info(`${testLogPrefix}    通过: ${testStats.passed} ✅`);
    logger.info(`${testLogPrefix}    失败: ${testStats.failed} ❌`);
    logger.info(`${testLogPrefix}    跳过: ${testStats.skipped} ⏭️`);
    logger.info(`${testLogPrefix}    耗时: ${(totalTime / 1000).toFixed(2)}秒`);
    
    if (testStats.failed > 0) {
        logger.error(`${testLogPrefix} 💥 测试失败！有 ${testStats.failed} 个用例失败`);
        console.log(`❌ 测试失败: ${testStats.failed}/${testStats.total} 用例失败`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试通过！`);
        console.log(`✅ 测试成功: ${testStats.passed}/${testStats.total} 用例通过 (${testStats.skipped} 个跳过)`);
        process.exit(0);
    }
}

// 错误处理
process.on('uncaughtException', (error) => {
    logger.error(`${testLogPrefix} 💥 未捕获异常: ${error.message}`);
    logger.error(`${testLogPrefix} 异常堆栈: ${error.stack}`);
    console.log(`❌ 测试异常终止: ${error.message}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error(`${testLogPrefix} 💥 未处理的Promise拒绝:`, reason);
    logger.error(`${testLogPrefix} Promise对象:`, promise);
    console.log(`❌ 测试Promise拒绝: ${reason}`);
    process.exit(1);
});

// 超时处理
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 运行测试
logger.info(`${testLogPrefix} 🚀 开始执行测试套件...`);
runTests().catch((error) => {
    logger.error(`${testLogPrefix} 💥 测试执行失败: ${error.message}`);
    logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    console.log(`❌ 测试执行失败: ${error.message}`);
    process.exit(1);
}); 