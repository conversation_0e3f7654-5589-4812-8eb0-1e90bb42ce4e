# &lt;Audio&gt;

## 概述

`<Audio>` 组件用于在视频中添加音频。支持所有 Chromium 支持的音频格式。

## 语法

```typescript
import { Audio, staticFile } from "remotion";

<Audio src={staticFile("audio.mp3")} />
```

## 基础用法

### 1. 基础音频播放

```typescript
import { AbsoluteFill, Audio, staticFile } from "remotion";

export const MyVideo = () => {
  return (
    <AbsoluteFill>
      <Audio src={staticFile("audio.mp3")} />
    </AbsoluteFill>
  );
};
```

### 2. 远程音频文件

```typescript
export const MyVideo = () => {
  return (
    <AbsoluteFill>
      <Audio src="https://example.com/audio.mp3" />
    </AbsoluteFill>
  );
};
```

## 核心属性

### src
- **类型**: `string`
- **描述**: 音频文件的URL，可以是本地文件（使用 `staticFile()`）或远程URL

### volume
- **类型**: `number | ((frame: number) => number)`
- **描述**: 控制音频音量，支持静态值或基于帧的动态变化

```typescript
// 静态音量
<Audio volume={0.5} src={staticFile("background.mp3")} />

// 动态音量 - 30帧内从0渐变到1
<Audio 
  volume={(f) => interpolate(f, [0, 30], [0, 1], {extrapolateLeft: 'clamp'})} 
  src={staticFile("voice.mp3")} 
/>
```

### trimBefore / trimAfter (v4.0.319+)
- **类型**: `number`
- **描述**: 裁剪音频的开始和结束部分

```typescript
// 假设fps为30，移除前2秒和4秒后的内容
<Audio 
  src={staticFile("audio.mp3")} 
  trimBefore={60} 
  trimAfter={120} 
/>
```

### playbackRate (v2.2.0+)
- **类型**: `number`
- **默认值**: `1`
- **描述**: 控制音频播放速度

```typescript
// 2倍速播放
<Audio src={staticFile("audio.mp3")} playbackRate={2} />
```

### muted (v2.0.0+)
- **类型**: `boolean`
- **描述**: 静音音频，可以动态控制

```typescript
import { useCurrentFrame } from "remotion";

const MyVideo = () => {
  const frame = useCurrentFrame();
  
  return (
    <Audio 
      src={staticFile("audio.mp3")} 
      muted={frame < 30} // 前30帧静音
    />
  );
};
```

### loop (v3.2.29+)
- **类型**: `boolean`
- **描述**: 循环播放音频

```typescript
<Audio loop src={staticFile("audio.mp3")} />
```

### toneFrequency (v4.0.47+)
- **类型**: `number`
- **范围**: `0.01` - `2`
- **描述**: 调整音频音调，仅在渲染时生效

```typescript
// 降低音调50%
<Audio src={staticFile("voice.mp3")} toneFrequency={0.5} />

// 提高音调50%
<Audio src={staticFile("music.mp3")} toneFrequency={1.5} />
```

## 实际应用场景

### 1. 背景音乐

```typescript
import { Audio, staticFile } from "remotion";

const BackgroundMusic = () => {
  return (
    <Audio 
      src={staticFile("background-music.mp3")}
      volume={0.3} // 降低音量作为背景
      loop // 循环播放
    />
  );
};
```

### 2. 音频淡入淡出

```typescript
import { Audio, staticFile, useCurrentFrame, interpolate } from "remotion";

const FadeAudio = () => {
  const frame = useCurrentFrame();
  
  // 前30帧淡入，后30帧淡出
  const volume = interpolate(
    frame,
    [0, 30, 270, 300],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <Audio 
      src={staticFile("narration.mp3")}
      volume={volume}
    />
  );
};
```

### 3. 多轨音频混合

```typescript
import { Audio, staticFile } from "remotion";

const MultiTrackAudio = () => {
  return (
    <>
      {/* 背景音乐 */}
      <Audio 
        src={staticFile("background.mp3")}
        volume={0.2}
        loop
      />
      
      {/* 主要旁白 */}
      <Audio 
        src={staticFile("narration.mp3")}
        volume={0.8}
      />
      
      {/* 音效 */}
      <Audio 
        src={staticFile("sound-effect.mp3")}
        volume={0.5}
        trimBefore={120} // 4秒后开始播放
      />
    </>
  );
};
```

### 4. 音频同步和时序控制

```typescript
import { Audio, Sequence, staticFile } from "remotion";

const TimedAudio = () => {
  return (
    <>
      {/* 开场音乐: 0-120帧 */}
      <Sequence from={0} durationInFrames={120}>
        <Audio src={staticFile("intro.mp3")} />
      </Sequence>
      
      {/* 主要内容音频: 120-480帧 */}
      <Sequence from={120} durationInFrames={360}>
        <Audio 
          src={staticFile("main-content.mp3")}
          playbackRate={1.2} // 稍微加速
        />
      </Sequence>
      
      {/* 结尾音乐: 480-600帧 */}
      <Sequence from={480} durationInFrames={120}>
        <Audio src={staticFile("outro.mp3")} />
      </Sequence>
    </>
  );
};
```

### 5. 条件性音频播放

```typescript
import { Audio, staticFile, useCurrentFrame } from "remotion";

const ConditionalAudio = ({ includeMusic }: { includeMusic: boolean }) => {
  const frame = useCurrentFrame();
  const isNightScene = frame > 300 && frame < 600;

  return (
    <>
      {/* 基础旁白 */}
      <Audio src={staticFile("narration.mp3")} />
      
      {/* 可选背景音乐 */}
      {includeMusic && (
        <Audio 
          src={staticFile("background-music.mp3")}
          volume={0.3}
          loop
        />
      )}
      
      {/* 夜晚场景音效 */}
      {isNightScene && (
        <Audio 
          src={staticFile("night-ambience.mp3")}
          volume={0.4}
        />
      )}
    </>
  );
};
```

### 6. 音频可视化数据

```typescript
import { Audio, staticFile, useCurrentFrame } from "remotion";

const AudioWithVisualization = () => {
  const frame = useCurrentFrame();
  
  // 模拟音频频谱数据（实际项目中可能来自音频分析）
  const getAudioLevel = (frame: number) => {
    return Math.sin(frame * 0.1) * 0.5 + 0.5;
  };
  
  const audioLevel = getAudioLevel(frame);

  return (
    <>
      <Audio src={staticFile("music.mp3")} />
      
      {/* 基于音频级别的视觉效果 */}
      <div style={{
        width: 100,
        height: 100,
        backgroundColor: `rgba(255, 0, 0, ${audioLevel})`,
        borderRadius: "50%"
      }} />
    </>
  );
};
```

### 7. 音频错误处理

```typescript
import { Audio, staticFile } from "remotion";

const ErrorHandledAudio = () => {
  const handleAudioError = (error: Event) => {
    console.error("音频加载失败:", error);
    // 可以设置备用音频或显示错误信息
  };

  return (
    <Audio 
      src={staticFile("audio.mp3")}
      onError={handleAudioError}
      volume={0.8}
    />
  );
};
```

### 8. 动态音调调整

```typescript
import { Audio, staticFile, useCurrentFrame, interpolate } from "remotion";

const DynamicPitchAudio = () => {
  const frame = useCurrentFrame();
  
  // 音调随时间变化
  const toneFrequency = interpolate(
    frame,
    [0, 150, 300],
    [0.8, 1.2, 1.0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <Audio 
      src={staticFile("voice.mp3")}
      toneFrequency={toneFrequency}
    />
  );
};
```

## 高级配置

### 1. Web Audio API 集成

```typescript
import { Audio, staticFile } from "remotion";

const WebAudioIntegration = () => {
  return (
    <Audio 
      src={staticFile("complex-audio.mp3")}
      useWebAudioApi={true} // 启用Web Audio API
      volume={1.5} // 可以超过1的音量
    />
  );
};
```

### 2. 缓冲控制

```typescript
import { Audio, staticFile } from "remotion";

const BufferedAudio = () => {
  return (
    <Audio 
      src={staticFile("large-audio.mp3")}
      pauseWhenBuffering={true} // 缓冲时暂停播放器
      acceptableTimeShiftInSeconds={0.2} // 自定义同步阈值
    />
  );
};
```

### 3. 延迟渲染配置

```typescript
import { Audio, staticFile } from "remotion";

const DelayConfiguredAudio = () => {
  return (
    <Audio 
      src={staticFile("audio.mp3")}
      delayRenderTimeoutInMilliseconds={8000} // 8秒超时
      delayRenderRetries={3} // 重试3次
    />
  );
};
```

### 4. 时间轴管理

```typescript
import { Audio, staticFile } from "remotion";

const TimelineAudio = () => {
  return (
    <>
      <Audio 
        src={staticFile("main-audio.mp3")}
        name="主要音频" // 在时间轴中显示的名称
        showInTimeline={true}
      />
      
      <Audio 
        src={staticFile("hidden-audio.mp3")}
        showInTimeline={false} // 在时间轴中隐藏
      />
    </>
  );
};
```

### 5. 循环音量曲线行为

```typescript
import { Audio, staticFile, useCurrentFrame, interpolate } from "remotion";

const LoopVolumeAudio = () => {
  const frame = useCurrentFrame();
  
  // 每次循环重新开始音量曲线
  const volumeRepeat = (f: number) => interpolate(f % 60, [0, 30, 60], [0, 1, 0]);
  
  // 音量曲线持续增长
  const volumeExtend = (f: number) => Math.min(f / 100, 1);

  return (
    <>
      <Audio 
        src={staticFile("loop-audio.mp3")}
        loop
        volume={volumeRepeat}
        loopVolumeCurveBehavior="repeat" // 默认行为
      />
      
      <Audio 
        src={staticFile("another-loop.mp3")}
        loop
        volume={volumeExtend}
        loopVolumeCurveBehavior="extend" // 持续增长
      />
    </>
  );
};
```

## 音频格式支持

Remotion 支持所有 Chromium 支持的音频格式：
- **MP3** (最常用)
- **WAV** (无损)
- **OGG** (开源格式)
- **AAC** (高质量压缩)
- **FLAC** (无损压缩)
- **WebM Audio** (现代格式)

## 性能优化

### 1. 音频预加载

```typescript
import { Audio, staticFile } from "remotion";

const PreloadedAudio = () => {
  return (
    <Audio 
      src={staticFile("audio.mp3")}
      // 浏览器会自动预加载音频
    />
  );
};
```

### 2. 条件性加载

```typescript
import { Audio, staticFile, useCurrentFrame } from "remotion";

const ConditionalLoadAudio = () => {
  const frame = useCurrentFrame();
  const shouldPlayAudio = frame > 60;

  return (
    <>
      {shouldPlayAudio && (
        <Audio src={staticFile("audio.mp3")} />
      )}
    </>
  );
};
```

## 与其他组件结合

### 1. 与 Video 组合

```typescript
import { Video, Audio, staticFile } from "remotion";

const VideoWithSeparateAudio = () => {
  return (
    <>
      <Video 
        src={staticFile("video.mp4")}
        muted // 视频静音
      />
      <Audio 
        src={staticFile("high-quality-audio.mp3")}
        volume={0.8}
      />
    </>
  );
};
```

### 2. 与 Sequence 组合

```typescript
import { Sequence, Audio, staticFile } from "remotion";

const SequencedAudio = () => {
  return (
    <>
      <Sequence from={0} durationInFrames={120}>
        <Audio src={staticFile("intro-audio.mp3")} />
      </Sequence>
      
      <Sequence from={120} durationInFrames={240}>
        <Audio src={staticFile("main-audio.mp3")} />
      </Sequence>
    </>
  );
};
```

## 最佳实践

1. **音量控制**: 使用合理的音量级别，避免音频失真
2. **格式选择**: 优先使用 MP3 格式以获得最佳兼容性
3. **文件大小**: 压缩音频文件以减少加载时间
4. **错误处理**: 总是提供错误处理回调
5. **性能考虑**: 对于长音频文件，考虑分段加载

## 相关 API

- [`<Video>`](./Video.md) - 视频组件
- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`staticFile()`](./staticFile.md) - 静态文件引用
- [`interpolate()`](./interpolate.md) - 值插值
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/audio/Audio.tsx)
