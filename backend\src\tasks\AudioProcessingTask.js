/**
 * @功能概述: 音频处理任务，负责音频重复拼接、时长计算和格式标准化
 * @输入依赖: context需包含audioFilePath、repeatCount字段
 * @输出结果: 向context添加processedAudioPath、totalDuration、durationInFrames字段
 * @外部依赖: FFmpeg命令行工具
 * @失败策略: 处理失败时抛出详细错误信息
 */

const TaskBase = require('../class/TaskBase');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class AudioProcessingTask extends TaskBase {
    constructor() {
        super('AudioProcessingTask', '音频处理任务');
        this.description = '处理音频文件，包括重复拼接、时长计算和格式标准化';
    }

    /**
     * @功能概述: 执行音频处理任务
     * @param {Object} context - 任务上下文
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Object} 处理结果
     * @执行流程:
     *   1. 验证输入参数和文件
     *   2. 获取原始音频信息
     *   3. 执行音频重复拼接
     *   4. 计算总时长和帧数
     *   5. 返回处理结果
     */
    async execute(context, progressCallback) {
        const reqId = context.reqId || 'unknown';
        const logPrefix = `[文件：AudioProcessingTask.js][音频处理任务][execute][ReqID:${reqId}] `;
        
        logger.info(`${logPrefix}[步骤 1] 开始音频处理任务`);
        this.status = 'running';
        
        // 报告任务开始
        if (progressCallback) progressCallback({
            taskName: this.name,
            status: 'started',
            detail: '开始处理音频文件'
        });

        try {
            // 步骤 1: 验证输入参数
            const { audioFilePath, repeatCount = 3, fps = 30 } = context;
            if (!audioFilePath) {
                throw new Error('缺少必需的audioFilePath参数');
            }
            
            if (!await this._fileExists(audioFilePath)) {
                throw new Error(`音频文件不存在: ${audioFilePath}`);
            }
            
            logger.debug(`${logPrefix}[步骤 1] 输入验证通过 - 文件: ${audioFilePath}, 重复次数: ${repeatCount}`);

            // 步骤 2: 获取原始音频信息
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'analyzing_audio',
                detail: '分析音频文件信息'
            });
            
            const audioInfo = await this._getAudioInfo(audioFilePath, logPrefix);
            logger.info(`${logPrefix}[步骤 2] 音频信息获取完成 - 时长: ${audioInfo.duration}秒`);

            // 步骤 3: 执行音频重复拼接
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'processing_audio',
                detail: `正在拼接音频 (重复${repeatCount}次)`
            });
            
            const processedAudioPath = await this._processAudio(audioFilePath, repeatCount, logPrefix);
            logger.info(`${logPrefix}[步骤 3] 音频拼接完成: ${processedAudioPath}`);

            // 步骤 4: 计算总时长和帧数
            const totalDuration = audioInfo.duration * repeatCount;
            const durationInFrames = Math.ceil(totalDuration * fps);
            
            logger.info(`${logPrefix}[步骤 4] 时长计算完成 - 总时长: ${totalDuration}秒, 帧数: ${durationInFrames}`);

            // 步骤 5: 构建返回结果
            this.status = 'completed';
            const result = {
                processedAudioPath,
                originalDuration: audioInfo.duration,
                totalDuration,
                durationInFrames,
                repeatCount,
                fps,
                audioInfo
            };
            this.result = result;
            
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'completed',
                result: this.result,
                detail: '音频处理任务完成'
            });
            
            logger.info(`${logPrefix}[SUCCESS] 音频处理任务完成`);
            return result;
            
        } catch (error) {
            logger.error(`${logPrefix}[ERROR] 音频处理失败: ${error.message}`);
            this.status = 'failed';
            this.error = error;
            
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'failed_audio_processing',
                error: { message: error.message, name: error.name },
                detail: `音频处理失败: ${error.message}`
            });
            
            throw error;
        }
    }

    /**
     * @功能概述: 检查文件是否存在
     * @param {string} filePath - 文件路径
     * @returns {Promise<boolean>} 文件是否存在
     */
    async _fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * @功能概述: 获取音频文件信息
     * @param {string} audioFilePath - 音频文件路径
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<Object>} 音频信息对象
     */
    async _getAudioInfo(audioFilePath, logPrefix) {
        return new Promise((resolve, reject) => {
            logger.debug(`${logPrefix}[步骤 2.1] 开始获取音频信息`);
            
            ffmpeg.ffprobe(audioFilePath, (err, metadata) => {
                if (err) {
                    logger.error(`${logPrefix}[步骤 2.1][ERROR] FFprobe失败: ${err.message}`);
                    reject(new Error(`获取音频信息失败: ${err.message}`));
                    return;
                }
                
                try {
                    const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
                    if (!audioStream) {
                        throw new Error('未找到音频流');
                    }
                    
                    const duration = parseFloat(metadata.format.duration);
                    if (isNaN(duration) || duration <= 0) {
                        throw new Error('无效的音频时长');
                    }
                    
                    const audioInfo = {
                        duration,
                        codec: audioStream.codec_name,
                        sampleRate: parseInt(audioStream.sample_rate),
                        channels: audioStream.channels,
                        bitrate: parseInt(metadata.format.bit_rate) || 0,
                        size: parseInt(metadata.format.size) || 0
                    };
                    
                    logger.debug(`${logPrefix}[步骤 2.1] 音频信息: ${JSON.stringify(audioInfo)}`);
                    resolve(audioInfo);
                    
                } catch (parseError) {
                    logger.error(`${logPrefix}[步骤 2.1][ERROR] 解析音频信息失败: ${parseError.message}`);
                    reject(new Error(`解析音频信息失败: ${parseError.message}`));
                }
            });
        });
    }

    /**
     * @功能概述: 处理音频文件（重复拼接）
     * @param {string} inputPath - 输入音频文件路径
     * @param {number} repeatCount - 重复次数
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<string>} 处理后的音频文件路径
     */
    async _processAudio(inputPath, repeatCount, logPrefix) {
        return new Promise((resolve, reject) => {
            logger.debug(`${logPrefix}[步骤 3.1] 开始音频拼接处理`);
            
            // 生成输出文件路径
            const inputDir = path.dirname(inputPath);
            const inputName = path.basename(inputPath, path.extname(inputPath));
            const outputPath = path.join(inputDir, `${inputName}_repeated_${repeatCount}x.wav`);
            
            // 构建FFmpeg命令
            let command = ffmpeg();
            
            // 添加多个输入文件（重复添加同一个文件）
            for (let i = 0; i < repeatCount; i++) {
                command = command.input(inputPath);
            }
            
            // 配置输出参数
            command
                .complexFilter([
                    // 使用concat滤镜拼接音频
                    `concat=n=${repeatCount}:v=0:a=1[outa]`
                ])
                .outputOptions([
                    '-map', '[outa]',
                    '-acodec', 'pcm_s16le', // 使用PCM编码确保质量
                    '-ar', '44100', // 标准采样率
                    '-ac', '2' // 立体声
                ])
                .output(outputPath)
                .on('start', (commandLine) => {
                    logger.debug(`${logPrefix}[步骤 3.1] FFmpeg命令: ${commandLine}`);
                })
                .on('progress', (progress) => {
                    logger.debug(`${logPrefix}[步骤 3.1] 处理进度: ${progress.percent || 0}%`);
                })
                .on('end', () => {
                    logger.info(`${logPrefix}[步骤 3.1][SUCCESS] 音频拼接完成: ${outputPath}`);
                    resolve(outputPath);
                })
                .on('error', (err) => {
                    logger.error(`${logPrefix}[步骤 3.1][ERROR] FFmpeg处理失败: ${err.message}`);
                    reject(new Error(`音频拼接失败: ${err.message}`));
                })
                .run();
        });
    }

    /**
     * @功能概述: 清理任务资源
     */
    async cleanup() {
        const logPrefix = `[AudioProcessingTask][cleanup]`;
        
        try {
            logger.debug(`${logPrefix} 开始清理任务资源`);
            
            // 如果有临时文件需要清理，在这里添加清理逻辑
            if (this.result && this.result.processedAudioPath) {
                // 注意：通常不删除处理后的音频文件，因为后续任务可能需要使用
                logger.debug(`${logPrefix} 保留处理后的音频文件: ${this.result.processedAudioPath}`);
            }
            
            logger.debug(`${logPrefix} 资源清理完成`);
            
        } catch (error) {
            logger.error(`${logPrefix} 资源清理失败: ${error.message}`);
            // 清理失败不应该影响主流程，只记录错误
        }
    }
}

module.exports = AudioProcessingTask;
