# ConvertToAudioTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **originalVideoPath** (string): 原始视频文件完整路径
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 音频文件保存路径

### 可选参数
- **processedVideoPath** (string): 来自VideoClipAndCropTask的处理后视频路径（自动映射）
- **processedVideoFileName** (string): 处理后视频文件名（自动映射）
- **audioFormat** (string): 音频格式，默认'mp3'
- **audioBitrate** (string): 音频比特率，默认'128k'
- **audioFrequency** (string): 音频采样率，默认'44100'

## 2. 输出上下文参数 (Output Context)

- **audioFilePath** (string): 转换后音频文件完整路径
- **audioFileName** (string): 音频文件名
- **audioFileIdentifier** (string): 音频文件标识符（不含扩展名）
- **audioFileSize** (number): 音频文件大小（字节）
- **audioFileSizeMB** (string): 音频文件大小（MB，保留2位小数）
- **audioDuration** (number): 音频时长（秒）
- **audioConversionStats** (object): 转换统计信息
  - **originalVideoSize** (number): 原始视频大小
  - **audioSize** (number): 音频文件大小
  - **compressionRatio** (number): 压缩比例
  - **processingTime** (number): 处理时间（毫秒）
- **finalAudioCodec** (string): 最终音频编码格式
- **finalAudioFormat** (string): 最终音频格式
- **finalAudioBitrate** (string): 最终音频比特率
- **finalAudioFrequency** (string): 最终音频采样率
- **conversionSuccess** (boolean): 转换成功标识
- **conversionMethod** (string): 转换方法标识
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 音频转换参数
```json
{
  "audioCodec": "libmp3lame",
  "audioBitrate": "128k",
  "audioFrequency": "44100",
  "audioChannels": "2",
  "audioFormat": "mp3"
}
```

### FFmpeg转换命令格式
```
ffmpeg -i input.mp4 -vn -acodec libmp3lame -ab 128k -ar 44100 -ac 2 output.mp3
```

## 4. 文件操作

### 保存的文件格式
- **.mp3**: 标准质量MP3音频文件（默认）
- **.wav**: 无损WAV音频文件（可选）
- **.aac**: AAC音频文件（可选）

### 文件命名规则
- **模式**: `{cleanId}_{hash}_audio.{format}`
- **cleanId**: 清理后的videoIdentifier（替换非法字符，最多64字符）
- **hash**: SHA1哈希值前8位
- **示例**: `video123_a1b2c3d4_audio.mp3`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 自动创建目录（如果不存在）
- 支持VideoClipAndCropTask输出的自动映射

## 5. 执行逻辑概述

视频转音频任务负责从视频文件中提取音频轨道，生成高质量的音频文件用于后续的语音转录处理。任务首先进行上下文映射，支持VideoClipAndCropTask输出的自动转换。然后验证输入参数并准备工作目录。使用FFmpeg进行音频提取，支持多种音频格式和质量设置。转换过程采用标准的音频编码参数，确保音质和文件大小的平衡。任务提供详细的转换统计信息，包括文件大小、压缩比例、处理时间等。转换完成后进行音频时长检测和质量验证，确保生成的音频文件适合语音识别处理。整个过程支持完善的错误处理和进度报告机制。
