/**
 * @功能概述: Remotion模板服务，负责配置转换和模板管理
 * @服务类型: 配置管理服务
 * @使用场景: 将通用配置转换为Remotion模板格式
 */

const fs = require('fs').promises;
const path = require('path');
const VideoConfigManager = require('../config/converters/VideoConfigManager');
const logger = require('../utils/logger');

// 模块级日志前缀
const moduleLogPrefix = `[文件：RemotionTemplateService.js][Remotion模板服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);

class RemotionTemplateService {
    /**
     * @功能概述: 构造函数，初始化模板服务
     * @param {string} [reqId='unknown_req'] - 请求ID，用于日志追踪
     */
    constructor(reqId = 'unknown_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：RemotionTemplateService.js][Remotion模板服务][ReqID:${this.reqId}]`;
        
        // 模板目录配置
        this.templatesDir = path.resolve(__dirname, '../remotion/templates');
        this.defaultTemplatePath = path.join(this.templatesDir, 'video-template.json');
        
        logger.info(`${this.logPrefix}[构造函数] Remotion模板服务实例已创建`);
        logger.debug(`${this.logPrefix}[构造函数] 模板目录: ${this.templatesDir}`);
    }

    /**
     * @功能概述: 将老配置转换为Remotion模板格式
     * @param {string} legacyConfigPath - 老配置文件路径
     * @param {string} [outputPath] - 输出模板路径，默认为video-template.json
     * @returns {Promise<Object>} 转换后的模板配置
     * @执行流程:
     *   1. 加载老配置文件
     *   2. 转换为通用格式
     *   3. 转换为Remotion模板格式
     *   4. 保存到templates目录
     */
    async convertLegacyToTemplate(legacyConfigPath, outputPath = null) {
        const methodLogPrefix = `${this.logPrefix}[convertLegacyToTemplate]`;
        
        try {
            logger.info(`${methodLogPrefix} 开始转换老配置为Remotion模板`);
            logger.debug(`${methodLogPrefix} 输入配置: ${legacyConfigPath}`);
            
            // 步骤 1: 加载老配置并转换为通用格式
            const universalConfig = await VideoConfigManager.loadConfig(legacyConfigPath);
            logger.info(`${methodLogPrefix} 老配置已转换为通用格式`);
            
            // 步骤 2: 转换为Remotion模板格式
            const remotionTemplate = this._convertUniversalToTemplate(universalConfig);
            logger.info(`${methodLogPrefix} 通用配置已转换为Remotion模板格式`);
            
            // 步骤 3: 确保模板目录存在
            await fs.mkdir(this.templatesDir, { recursive: true });
            
            // 步骤 4: 保存模板文件
            const finalOutputPath = outputPath || this.defaultTemplatePath;
            await fs.writeFile(finalOutputPath, JSON.stringify(remotionTemplate, null, 2), 'utf8');
            
            logger.info(`${methodLogPrefix} 模板文件已保存: ${finalOutputPath}`);
            return remotionTemplate;
            
        } catch (error) {
            logger.error(`${methodLogPrefix} 配置转换失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 加载Remotion模板配置
     * @param {string} [templateName='video-template'] - 模板名称
     * @returns {Promise<Object>} 模板配置对象
     */
    async loadTemplate(templateName = 'video-template') {
        const methodLogPrefix = `${this.logPrefix}[loadTemplate]`;
        
        try {
            const templatePath = path.join(this.templatesDir, `${templateName}.json`);
            logger.debug(`${methodLogPrefix} 加载模板: ${templatePath}`);
            
            const templateContent = await fs.readFile(templatePath, 'utf8');
            const template = JSON.parse(templateContent);
            
            logger.info(`${methodLogPrefix} 模板加载成功: ${templateName}`);
            return template;
            
        } catch (error) {
            logger.error(`${methodLogPrefix} 模板加载失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 将通用配置转换为Remotion模板格式
     * @param {Object} universalConfig - 通用配置对象
     * @returns {Object} Remotion模板配置对象
     * @私有方法: 内部转换逻辑
     */
    _convertUniversalToTemplate(universalConfig) {
        const methodLogPrefix = `${this.logPrefix}[_convertUniversalToTemplate]`;
        
        try {
            logger.debug(`${methodLogPrefix} 开始转换通用配置为模板格式`);
            
            const template = {
                version: "1.0",
                templateType: "video-template",
                name: "双语字幕视频模板",
                description: "从通用配置转换生成的双语字幕视频模板",
                createdAt: new Date().toISOString(),

                // Composition配置
                composition: {
                    id: "BilingualSubtitleVideo",
                    width: universalConfig.output.width,
                    height: universalConfig.output.height,
                    fps: universalConfig.output.fps,
                    durationInFrames: 2000, // 默认值，将在运行时根据音频长度调整
                    defaultProps: {}
                },

                // 布局配置
                layout: {
                    type: "vertical",
                    regions: {
                        originalVideo: {
                            enabled: true,
                            position: { 
                                top: "10%", 
                                left: "10%", 
                                width: "80%", 
                                height: "40%" 
                            },
                            duration: { start: 0, end: 3 },
                            style: {
                                backgroundColor: "rgba(50,50,50,0.9)",
                                border: "2px solid #666",
                                borderRadius: "8px",
                                color: "#fff",
                                fontSize: "24px",
                                fontFamily: "Arial",
                                placeholder: "原视频播放区域"
                            }
                        },
                        textArea: {
                            enabled: universalConfig.ui?.textArea?.enabled !== false,
                            position: { 
                                top: "55%", 
                                left: "5%", 
                                width: "90%", 
                                height: "35%" 
                            },
                            duration: { start: 3, end: "auto" },
                            style: {
                                backgroundColor: universalConfig.ui?.textArea?.style?.backgroundColor || "rgba(128,128,128,0.9)",
                                borderRadius: "8px",
                                padding: "20px"
                            }
                        },
                        progressBar: {
                            enabled: universalConfig.ui?.progressBar?.enabled !== false,
                            position: { 
                                top: "75%", 
                                left: "0%", 
                                width: "100%", 
                                height: "5%" 
                            },
                            style: {
                                backgroundColor: "rgba(0,0,0,0.8)",
                                progressColor: universalConfig.ui?.progressBar?.style?.color || "#FFFF00",
                                borderRadius: "0px"
                            }
                        }
                    }
                },

                // 背景配置
                background: {
                    type: universalConfig.content?.background?.type === "image" ? "pattern" : "color",
                    pattern: {
                        name: "newspaper",
                        svgData: "data:image/svg+xml,%3Csvg width=\"100\" height=\"100\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cpattern id=\"newspaper\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"%3E%3Crect width=\"20\" height=\"20\" fill=\"%23f0f0f0\"/%3E%3Cline x1=\"0\" y1=\"5\" x2=\"20\" y2=\"5\" stroke=\"%23ddd\" stroke-width=\"0.5\"/%3E%3Cline x1=\"0\" y1=\"10\" x2=\"20\" y2=\"10\" stroke=\"%23ddd\" stroke-width=\"0.5\"/%3E%3Cline x1=\"0\" y1=\"15\" x2=\"20\" y2=\"15\" stroke=\"%23ddd\" stroke-width=\"0.5\"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\"100\" height=\"100\" fill=\"url(%23newspaper)\"/%3E%3C/svg%3E",
                        size: "100px 100px",
                        position: "center",
                        repeat: "repeat"
                    },
                    overlay: {
                        enabled: universalConfig.content?.background?.overlay?.enabled !== false,
                        color: universalConfig.content?.background?.overlay?.color || "#000000",
                        opacity: universalConfig.content?.background?.overlay?.opacity || 0.8
                    },
                    fallback: {
                        type: "color",
                        color: universalConfig.content?.background?.fallback?.color || "#1a1a2e"
                    }
                },

                // 字幕配置
                subtitles: {
                    bilingual: {
                        enabled: universalConfig.subtitles?.enabled !== false,
                        english: this._convertSubtitleStyle(universalConfig.subtitles?.styles?.english),
                        chinese: this._convertSubtitleStyle(universalConfig.subtitles?.styles?.chinese),
                        keywords: {
                            enabled: true,
                            fontSize: { inVideo: "24px", inTextArea: "20px" },
                            color: "#00FFFF",
                            fontFamily: "Arial",
                            fontWeight: "normal",
                            textShadow: "2px 2px 4px rgba(0,0,0,0.9)",
                            prefix: "关键词: "
                        }
                    }
                },

                // 时间轴配置
                timeline: {
                    originalVideoDuration: 3,
                    textAreaStart: 3,
                    subtitleTransition: {
                        enabled: true,
                        type: "fade",
                        duration: 0.3
                    }
                },

                // 音频配置
                audio: {
                    enabled: true,
                    source: "dynamic",
                    format: "wav",
                    volume: 1.0
                },

                // 组件映射
                components: {
                    background: "Background",
                    originalVideoArea: "OriginalVideoArea", 
                    textArea: "TextArea",
                    subtitles: "BilingualSubtitle",
                    progressBar: "LinearProgress",
                    audio: "Audio"
                },

                // 元数据
                metadata: {
                    author: "Remotion Video Generation System",
                    tags: ["bilingual", "subtitle", "educational", "vertical"],
                    notes: "从通用配置转换生成的双语字幕视频模板"
                }
            };

            logger.debug(`${methodLogPrefix} 模板格式转换完成`);
            return template;
            
        } catch (error) {
            logger.error(`${methodLogPrefix} 模板格式转换失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 转换字幕样式配置
     * @param {Object} subtitleStyle - 字幕样式对象
     * @returns {Object} 转换后的字幕样式
     * @私有方法: 字幕样式转换辅助方法
     */
    _convertSubtitleStyle(subtitleStyle) {
        if (!subtitleStyle) {
            return {
                fontSize: { inVideo: "36px", inTextArea: "28px" },
                color: "#FFFFFF",
                fontFamily: "Arial",
                fontWeight: "bold",
                textShadow: "2px 2px 4px rgba(0,0,0,0.9)",
                backgroundColor: { inVideo: "rgba(0,0,0,0.7)", inTextArea: "transparent" },
                padding: { inVideo: "8px 16px", inTextArea: "0" },
                borderRadius: { inVideo: "4px", inTextArea: "0" }
            };
        }

        return {
            fontSize: { 
                inVideo: subtitleStyle.fontSize || "36px", 
                inTextArea: subtitleStyle.fontSize || "28px" 
            },
            color: subtitleStyle.color || "#FFFFFF",
            fontFamily: subtitleStyle.fontFamily || "Arial",
            fontWeight: subtitleStyle.fontWeight || "bold",
            textShadow: subtitleStyle.textShadow || "2px 2px 4px rgba(0,0,0,0.9)",
            backgroundColor: { 
                inVideo: "rgba(0,0,0,0.7)", 
                inTextArea: "transparent" 
            },
            padding: { 
                inVideo: "8px 16px", 
                inTextArea: "0" 
            },
            borderRadius: { 
                inVideo: "4px", 
                inTextArea: "0" 
            }
        };
    }
}

module.exports = RemotionTemplateService;
