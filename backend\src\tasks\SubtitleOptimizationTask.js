// 导入文件系统操作模块，用于文件存在性检查
const fs = require('fs');
// 导入路径处理模块，用于文件路径解析
const path = require('path');
// 导入TaskBase基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');
// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入文件保存工具，用于保存优化后的字幕文件
const fileSaver = require('../utils/fileSaver');

// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：SubtitleOptimizationTask.js][字幕优化任务][模块初始化]';

// 记录模块加载成功的日志
logger.info(`${taskModuleLogPrefix}模块已加载。`);

/**
 * @功能概述: 字幕优化任务类，负责优化转录后的字幕质量
 *           包括合并过短segments、拆分过长segments、语义边界优化等
 * 
 * @继承关系: 继承自 TaskBase，获得标准化的进度监控和状态管理能力
 * 
 * @进度阶段: 提供细粒度的进度阶段：
 *   - INITIALIZING: 初始化和输入验证阶段
 *   - PROCESSING: 字幕数据处理阶段
 *   - FINALIZING: 保存优化后的字幕文件
 * 
 * @优化步骤:
 *   - 第一步: 合并过短的segments (≤2个单词)
 *   - 第二步: 拆分过长的segments (后续实现)
 *   - 第三步: 语义边界优化 (后续实现)
 */
class SubtitleOptimizationTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建字幕优化任务实例
     * @param {string} [name='SubtitleOptimizationTask'] - 任务名称，用于日志标识和进度追踪
     * @param {object} [options={}] - 优化选项配置
     */
    constructor(name = 'SubtitleOptimizationTask', options = {}) {
        super(name);
        // 设置实例级日志前缀
        this.instanceLogPrefix = `[文件：SubtitleOptimizationTask.js][字幕优化任务][${this.name}]`;
        
        // 优化配置参数
        this.options = {
            minWordCount: options.minWordCount || 2,          // 最小单词数，≤此数值的segment需要合并
            maxGapSeconds: options.maxGapSeconds || 0.5,      // 最大时间间隙，超过此值不合并
            mergeDirection: options.mergeDirection || 'forward', // 合并方向：forward(向后) 或 backward(向前)
            ...options
        };
        
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} SubtitleOptimizationTask 实例已创建。优化配置: ${JSON.stringify(this.options)}`);
    }

    /**
     * @功能概述: 主执行方法，执行字幕优化流程
     * @param {object} context - 执行上下文
     * @param {string} context.reqId - 请求ID
     * @param {string} context.videoIdentifier - 视频标识符
     * @param {object} context.apiResponse - 来自GetTranscriptionTask的转录数据
     * @param {string} context.savePath - 保存路径
     * @param {function} progressCallback - 进度回调函数
     * @returns {Promise<object>} 优化结果
     */
    async execute(context, progressCallback) {
        // 步骤 1.1: 设置执行级日志前缀
        const reqId = context.reqId || 'unknown_optimization_req';
        const videoIdentifier = context.videoIdentifier; 
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${videoIdentifier}]`;

        // 步骤 1.2: 校验必需的上下文参数
        const requiredFields = ['reqId', 'videoIdentifier', 'apiResponse', 'savePath'];

        for (const field of requiredFields) {
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                const errorMsg = `执行失败：上下文缺少必需或无效的字段 "${field}". 当前值: '${context[field]}'`;
                logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
                const validationError = new Error(errorMsg);
                this.fail(validationError);
                throw validationError;
            }
        }

        // 特别验证apiResponse结构
        if (!context.apiResponse.segments || !Array.isArray(context.apiResponse.segments)) {
            const errorMsg = `执行失败：apiResponse缺少segments数组或格式不正确`;
            logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
            const validationError = new Error(errorMsg);
            this.fail(validationError);
            throw validationError;
        }

        logger.debug(`${execLogPrefix} 输入参数验证通过。segments数量: ${context.apiResponse.segments.length}`);
        
        // 步骤 1.3: 设置进度回调函数和开始任务
        this.setProgressCallback(progressCallback);
        this.start(); 
        logger.info(`${execLogPrefix} SubtitleOptimizationTask execute 方法开始。`);

        // 从上下文中解构参数
        const { apiResponse, savePath } = context;

        try {
            // 步骤 2.1: 报告初始化阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '开始字幕优化处理',
                current: 10,
                total: 100,
                technicalDetail: `原始segments数量: ${apiResponse.segments.length}`
            });

            // 步骤 2.2: 创建原始数据的深拷贝，避免修改原始数据
            const originalSegments = JSON.parse(JSON.stringify(apiResponse.segments));
            logger.info(`${execLogPrefix}[步骤 2.2] 创建segments深拷贝完成，原始数量: ${originalSegments.length}`);

            // 步骤 3.1: 报告第一步处理阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '执行第一步：合并过短segments',
                current: 25,
                total: 100,
                technicalDetail: '基于词数和时间间隙的合并策略'
            });

            // 步骤 3.2: 执行第一步优化 - 合并过短的segments
            const mergedSegments = this.mergeShortSegments(originalSegments, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 3.2] 合并完成。原始: ${originalSegments.length} -> 优化后: ${mergedSegments.length}`);

            // 步骤 3.3: 报告第二步处理阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '执行第二步：合并句子片段',
                current: 50,
                total: 100,
                technicalDetail: '检测无句终标点+小写开始的segment组合'
            });

            // 步骤 3.4: 执行第二步优化 - 合并句子片段
            const sentenceMergedSegments = this.mergeSentenceFragments(mergedSegments, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 3.4] 句子片段合并完成。合并后: ${mergedSegments.length} -> 最终: ${sentenceMergedSegments.length}`);

            // 步骤 3.5: 报告第三步处理阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '执行第三步：智能拆分长句子',
                current: 75,
                total: 100,
                technicalDetail: '基于words间隙(0.2s)的自然断点拆分策略'
            });

            // 步骤 3.6: 执行第三步优化 - 智能拆分长句子
            const finalOptimizedSegments = this.splitLongSegments(sentenceMergedSegments, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 3.6] 长句子拆分完成。拆分后: ${sentenceMergedSegments.length} -> 最终: ${finalOptimizedSegments.length}`);

            // 步骤 4: 重新分配ID并构建最终结果
            const reindexedSegments = this.reassignSegmentIds(finalOptimizedSegments, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 4] ID重新分配完成。最终segments数量: ${reindexedSegments.length}`);

            // 步骤 4.1: 构建最终优化结果
            const optimizedResult = {
                ...apiResponse,
                segments: reindexedSegments
            };

            // 步骤 4.2: 报告保存阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '准备保存优化结果',
                current: 75,
                total: 100,
                technicalDetail: '构建最终数据结构并保存文件'
            });

            // 步骤 4.3: 保存优化后的字幕文件
            const optimizedFilePath = await this.saveOptimizedSubtitle(optimizedResult, videoIdentifier, execLogPrefix, savePath);
            logger.info(`${execLogPrefix}[步骤 4.3] 优化后字幕文件保存成功: ${optimizedFilePath}`);

            // 步骤 4.4: 转换为简化字幕JSON格式（5字段：id, start, end, text, words）
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '转换为简化字幕JSON格式',
                current: 85,
                total: 100,
                technicalDetail: '生成包含words对象的5字段简化JSON'
            });

            logger.info(`${execLogPrefix}[步骤 4.4] 开始转换为简化字幕JSON格式。`);
            const simplifiedSubtitleJsonArray = this.convertToSimplifiedSubtitleJson(reindexedSegments, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 4.4.1] 简化字幕JSON转换完成。条目数: ${simplifiedSubtitleJsonArray.length}。`);

            // 步骤 4.5: 保存简化字幕JSON文件
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '保存简化字幕JSON文件',
                current: 90,
                total: 100,
                technicalDetail: '保存5字段简化JSON到文件'
            });

            logger.info(`${execLogPrefix}[步骤 4.5] 开始保存简化字幕JSON文件。`);
            const simplifiedSubtitleJsonPath = await this.saveSimplifiedSubtitleJson(simplifiedSubtitleJsonArray, videoIdentifier, execLogPrefix, savePath);
            logger.info(`${execLogPrefix}[步骤 4.5.1] 简化字幕JSON文件保存成功: ${simplifiedSubtitleJsonPath}。`);

            // 步骤 4.6: 生成优化后的英文字幕SRT内容
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '生成优化后的英文字幕SRT内容',
                current: 92,
                total: 100,
                technicalDetail: '基于简化字幕JSON生成SRT格式'
            });

            logger.info(`${execLogPrefix}[步骤 4.6] 开始生成优化后的英文字幕SRT内容。`);
            const optimizedEnglishSrtContent = this.generateOptimizedSRT(simplifiedSubtitleJsonArray, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 4.6.1] 优化后的英文字幕SRT内容生成成功。长度: ${optimizedEnglishSrtContent.length} 字符。`);

            // 步骤 4.7: 保存英文SRT字幕文件
            logger.info(`${execLogPrefix}[步骤 4.7] 开始保存英文SRT字幕文件。`);
            const englishSrtFilename = `${videoIdentifier}_english.srt`;
            const englishSrtPath = await fileSaver.saveDataToFile(
                optimizedEnglishSrtContent,
                englishSrtFilename,
                savePath,
                execLogPrefix
            );
            logger.info(`${execLogPrefix}[步骤 4.7.1] 英文SRT字幕文件保存成功: ${englishSrtPath}`);

            // 步骤 5: 报告完成阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '字幕优化任务完成',
                current: 95,
                total: 100,
                technicalDetail: `原始: ${originalSegments.length} -> 最终: ${reindexedSegments.length} segments, 简化JSON: ${simplifiedSubtitleJsonArray.length} 条目, SRT: ${optimizedEnglishSrtContent.length} 字符`
            });

            // 步骤 5.1: 构建任务结果
            const result = {
                optimizationStatus: 'success',
                originalSegmentsCount: originalSegments.length,
                optimizedSegmentsCount: reindexedSegments.length,
                optimizedData: reindexedSegments,
                optimizedFilePath: optimizedFilePath,
                simplifiedSubtitleJsonArray: simplifiedSubtitleJsonArray,
                simplifiedSubtitleJsonPath: simplifiedSubtitleJsonPath,
                optimizedEnglishSrtContent: optimizedEnglishSrtContent,
                englishSrtPath: englishSrtPath, // 新增：英文SRT文件路径

                optimizationSteps: ['短segments合并', '句子片段合并', '智能拆分长句子', '转换简化JSON', '生成SRT内容'],
                videoIdentifier: videoIdentifier,
                reqId: reqId,
                savePath: savePath
            };

            // 步骤 5.2: 标记任务成功完成
            this.complete(result);
            logger.info(`${execLogPrefix}[步骤 5.2] 字幕优化任务执行成功。`);

            return result;

        } catch (error) {
            logger.error(`${execLogPrefix}[ERROR_HANDLER] 字幕优化过程中发生错误: ${error.message}`);
            logger.error(`${execLogPrefix}[错误堆栈] ${error.stack}`);

            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: 合并过短的segments
     * @param {Array} segments - 原始segments数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 合并后的segments数组
     */
    mergeShortSegments(segments, logPrefix) {
        logger.info(`${logPrefix}[合并处理] 开始合并过短segments，总数: ${segments.length}`);
        
        let result = [...segments];
        let mergeCount = 0;
        let hasChanges = true;
        
        // 循环处理直到没有可合并的短segment
        while (hasChanges) {
            hasChanges = false;
            
            for (let i = 0; i < result.length; i++) {
                if (this.shouldMergeSegment(result[i], logPrefix)) {
                    const mergeTargetIndex = this.findMergeTarget(result, i, logPrefix);
                    
                    if (mergeTargetIndex !== -1) {
                        logger.debug(`${logPrefix}[合并] 将segment[${i}]("${result[i].text}") 与 segment[${mergeTargetIndex}] 合并`);
                        
                        result = this.performMerge(result, i, mergeTargetIndex, logPrefix);
                        mergeCount++;
                        hasChanges = true;
                        break; // 重新开始检查，因为数组结构已改变
                    }
                }
            }
        }
        
        // 重新分配ID
        result = this.reassignIds(result, logPrefix);
        
        logger.info(`${logPrefix}[合并处理] 合并完成。执行了 ${mergeCount} 次合并操作`);
        return result;
    }

    /**
     * @功能概述: 判断segment是否需要合并
     * @param {object} segment - 要检查的segment
     * @param {string} logPrefix - 日志前缀
     * @returns {boolean} 是否需要合并
     */
    shouldMergeSegment(segment, logPrefix) {
        if (!segment.text || typeof segment.text !== 'string') {
            return false;
        }
        
        // 按单词数量判断（去除标点符号，按空格分割）
        const words = segment.text.trim().replace(/[^\w\s]/g, '').split(/\s+/).filter(word => word.length > 0);
        const shouldMerge = words.length <= this.options.minWordCount;
        
        if (shouldMerge) {
            logger.debug(`${logPrefix}[判断] segment "${segment.text}" 单词数: ${words.length}, 需要合并`);
        }
        
        return shouldMerge;
    }

    /**
     * @功能概述: 寻找合并目标segment的索引
     * @param {Array} segments - segments数组
     * @param {number} currentIndex - 当前短segment的索引
     * @param {string} logPrefix - 日志前缀
     * @returns {number} 合并目标的索引，-1表示无法合并
     */
    findMergeTarget(segments, currentIndex, logPrefix) {
        const currentSegment = segments[currentIndex];
        
        if (this.options.mergeDirection === 'forward') {
            // 向后合并：与下一个segment合并
            const nextIndex = currentIndex + 1;
            if (nextIndex < segments.length) {
                const nextSegment = segments[nextIndex];
                const gap = nextSegment.start - currentSegment.end;
                
                if (gap <= this.options.maxGapSeconds) {
                    logger.debug(`${logPrefix}[合并目标] 向后合并，时间间隙: ${gap.toFixed(3)}s`);
                    return nextIndex;
                }
            }
        } else {
            // 向前合并：与前一个segment合并
            const prevIndex = currentIndex - 1;
            if (prevIndex >= 0) {
                const prevSegment = segments[prevIndex];
                const gap = currentSegment.start - prevSegment.end;
                
                if (gap <= this.options.maxGapSeconds) {
                    logger.debug(`${logPrefix}[合并目标] 向前合并，时间间隙: ${gap.toFixed(3)}s`);
                    return prevIndex;
                }
            }
        }
        
        logger.debug(`${logPrefix}[合并目标] 无法找到合适的合并目标`);
        return -1;
    }

    /**
     * @功能概述: 执行两个segments的合并操作
     * @param {Array} segments - segments数组
     * @param {number} index1 - 第一个segment索引
     * @param {number} index2 - 第二个segment索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 合并后的segments数组
     */
    performMerge(segments, index1, index2, logPrefix) {
        const seg1 = segments[index1];
        const seg2 = segments[index2];
        
        // 确定合并顺序（按时间顺序）
        const [firstSeg, secondSeg] = seg1.start <= seg2.start ? [seg1, seg2] : [seg2, seg1];
        const [firstIndex, secondIndex] = seg1.start <= seg2.start ? [index1, index2] : [index2, index1];
        
        // 创建合并后的segment
        const mergedSegment = {
            id: firstSeg.id, // 使用较小的id，稍后会重新分配
            seek: firstSeg.seek,
            start: firstSeg.start,
            end: Math.max(firstSeg.end, secondSeg.end),
            text: this.mergeTexts(firstSeg.text, secondSeg.text),
            tokens: [...(firstSeg.tokens || []), ...(secondSeg.tokens || [])],
            temperature: firstSeg.temperature,
            avg_logprob: (firstSeg.avg_logprob + secondSeg.avg_logprob) / 2,
            compression_ratio: (firstSeg.compression_ratio + secondSeg.compression_ratio) / 2,
            no_speech_prob: Math.min(firstSeg.no_speech_prob, secondSeg.no_speech_prob),
            words: [...(firstSeg.words || []), ...(secondSeg.words || [])]
        };
        
        // 验证words数组的时间顺序
        mergedSegment.words = this.validateAndSortWords(mergedSegment.words, logPrefix);
        
        logger.debug(`${logPrefix}[合并操作] 合并结果: "${mergedSegment.text}"`);
        
        // 创建新数组，移除原来的两个segments，添加合并后的segment
        const result = [...segments];
        const indexToRemove = [firstIndex, secondIndex].sort((a, b) => b - a); // 从大到小排序，避免索引问题
        
        // 移除原segments
        indexToRemove.forEach(idx => result.splice(idx, 1));
        
        // 在正确位置插入合并后的segment
        const insertIndex = Math.min(firstIndex, secondIndex);
        result.splice(insertIndex, 0, mergedSegment);
        
        return result;
    }

    /**
     * @功能概述: 合并两个文本，处理空格和标点
     * @param {string} text1 - 第一个文本
     * @param {string} text2 - 第二个文本
     * @returns {string} 合并后的文本
     */
    mergeTexts(text1, text2) {
        const cleanText1 = (text1 || '').trim();
        const cleanText2 = (text2 || '').trim();
        
        if (!cleanText1) return cleanText2;
        if (!cleanText2) return cleanText1;
        
        // 检查第一个文本是否以标点结尾
        const needsSpace = !/[.!?,:;]$/.test(cleanText1);
        
        return needsSpace ? `${cleanText1} ${cleanText2}` : `${cleanText1} ${cleanText2}`;
    }

    /**
     * @功能概述: 验证并排序words数组，确保时间顺序正确
     * @param {Array} words - words数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 验证和排序后的words数组
     */
    validateAndSortWords(words, logPrefix) {
        if (!Array.isArray(words) || words.length === 0) {
            return [];
        }
        
        // 按开始时间排序
        const sortedWords = words.sort((a, b) => a.start - b.start);
        
        // 检查时间重叠或异常
        for (let i = 1; i < sortedWords.length; i++) {
            if (sortedWords[i].start < sortedWords[i-1].end) {
                logger.warn(`${logPrefix}[words验证] 发现时间重叠: word[${i-1}]="${sortedWords[i-1].text}"(${sortedWords[i-1].end}) 与 word[${i}]="${sortedWords[i].text}"(${sortedWords[i].start})`);
                // 修正重叠：将当前word的start设为前一个word的end
                sortedWords[i].start = sortedWords[i-1].end;
            }
        }
        
        return sortedWords;
    }

    /**
     * @功能概述: 重新分配所有segments的ID
     * @param {Array} segments - segments数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 重新分配ID后的segments数组
     */
    reassignIds(segments, logPrefix) {
        const result = segments.map((segment, index) => ({
            ...segment,
            id: index
        }));
        
        logger.debug(`${logPrefix}[ID重分配] 完成ID重新分配，segments数量: ${result.length}`);
        return result;
    }

    /**
     * @功能概述: 保存优化后的字幕文件
     * @param {object} optimizedData - 优化后的字幕数据
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @param {string} savePath - 保存路径
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveOptimizedSubtitle(optimizedData, videoIdentifier, execLogPrefix, savePath) {
        // 生成时间戳确保文件名唯一性
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${videoIdentifier}_optimized_subtitle_${timestamp}.json`;

        try {
            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(optimizedData, null, 2),
                filename,
                savePath,
                execLogPrefix
            );

            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }
            return savedPath;
        } catch (saveError) {
            const errorMsg = `保存优化后字幕文件失败: ${saveError.message}，路径：${savePath}/${filename}`;
            logger.error(`${execLogPrefix}[ERROR][saveOptimizedSubtitle] ${errorMsg}`);
            throw new Error(errorMsg);
        }
    }

    /**
     * @功能概述: 合并句子片段 - 检测无句终标点的segment与以小写开始的下一个segment
     * @param {Array} segments - segments数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 合并后的segments数组
     */
    mergeSentenceFragments(segments, logPrefix) {
        logger.info(`${logPrefix}[句子片段合并] 开始检测和合并句子片段，总数: ${segments.length}`);
        
        let result = [...segments];
        let mergeCount = 0;
        let hasChanges = true;
        
        // 循环处理直到没有可合并的句子片段
        while (hasChanges) {
            hasChanges = false;
            
            for (let i = 0; i < result.length - 1; i++) {
                if (this.shouldMergeSentenceFragment(result[i], result[i + 1], logPrefix)) {
                    logger.debug(`${logPrefix}[句子片段合并] 将segment[${i}]("${result[i].text}") 与 segment[${i + 1}]("${result[i + 1].text}") 合并`);
                    
                    result = this.performMerge(result, i, i + 1, logPrefix);
                    mergeCount++;
                    hasChanges = true;
                    break; // 重新开始检查，因为数组结构已改变
                }
            }
        }
        
        // 重新分配ID
        result = this.reassignIds(result, logPrefix);
        
        logger.info(`${logPrefix}[句子片段合并] 合并完成。执行了 ${mergeCount} 次句子片段合并操作`);
        return result;
    }

    /**
     * @功能概述: 判断是否应该合并句子片段
     * @param {object} segment1 - 第一个segment
     * @param {object} segment2 - 第二个segment
     * @param {string} logPrefix - 日志前缀
     * @returns {boolean} 是否应该合并
     */
    shouldMergeSentenceFragment(segment1, segment2, logPrefix) {
        if (!segment1.text || !segment2.text) {
            return false;
        }
        
        const text1 = segment1.text.trim();
        const text2 = segment2.text.trim();
        
        // 检查第一个segment是否没有句终标点
        const hasNoSentenceEnding = !/[.!?]$/.test(text1);
        
        // 检查第二个segment是否以小写字母开始
        const startsWithLowercase = /^[a-z]/.test(text2);
        
        // 检查时间间隙是否合理（使用更宽松的限制，比如1.0秒）
        const gap = segment2.start - segment1.end;
        const reasonableGap = gap <= 1.0; // 句子片段可以允许更大的间隙
        
        const shouldMerge = hasNoSentenceEnding && startsWithLowercase && reasonableGap;
        
        if (shouldMerge) {
            logger.debug(`${logPrefix}[句子片段判断] 检测到合并条件: 无句终标点("${text1}") + 小写开始("${text2}") + 合理间隙(${gap.toFixed(3)}s)`);
        } else {
            if (!hasNoSentenceEnding) {
                logger.debug(`${logPrefix}[句子片段判断] 第一个segment有句终标点: "${text1}"`);
            }
            if (!startsWithLowercase) {
                logger.debug(`${logPrefix}[句子片段判断] 第二个segment不以小写开始: "${text2}"`);
            }
            if (!reasonableGap) {
                logger.debug(`${logPrefix}[句子片段判断] 时间间隙过大: ${gap.toFixed(3)}s > 1.0s`);
            }
        }
        
        return shouldMerge;
    }

    /**
     * @功能概述: 第三步优化 - 智能拆分超过6秒的长句子
     * @param {Array} segments - 经过前两步优化的segments数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 拆分后的segments数组
     * 
     * @拆分策略:
     *   1. 识别超过6秒的长句子
     *   2. 基于words间隙(≥0.2秒)寻找自然断点
     *   3. 确保拆分后每部分≥4个单词
     *   4. 重新计算时间戳和统计字段
     */
    splitLongSegments(segments, logPrefix) {
        logger.info(`${logPrefix}[长句拆分] 开始拆分长句子，输入segments数量: ${segments.length}`);
        
        const SPLIT_CONFIG = {
            FORCE_SPLIT_TIME: 4.0,       // 超过4秒强制拆分
            MIN_PART_WORDS: 4,           // 拆分后每部分最少4个单词
            WORD_GAP_THRESHOLD: 0.2,     // 词间隙阈值0.2秒
            SEARCH_RANGE_RATIO: 0.3      // 在中点±30%范围搜索最佳断点
        };
        
        let result = [...segments]; // 从原始segments开始
        let splitCount = 0;

        // 递归拆分：持续拆分直到所有segments都在4秒以内
        let hasLongSegments = true;
        let maxIterations = 10; // 防止无限循环
        let iteration = 0;

        while (hasLongSegments && iteration < maxIterations) {
            hasLongSegments = false;
            iteration++;

            logger.debug(`${logPrefix}[长句拆分] 开始第${iteration}轮拆分检查，当前segments数量: ${result.length}`);

            // 创建新的结果数组
            let newResult = [];

            for (let i = 0; i < result.length; i++) {
                const segment = result[i];
                const duration = segment.end - segment.start;

                if (duration > SPLIT_CONFIG.FORCE_SPLIT_TIME && segment.words && segment.words.length >= 6) {
                    logger.debug(`${logPrefix}[长句拆分] 第${iteration}轮检测到长句子 ID:${segment.id}, 时长:${duration.toFixed(2)}s, 词数:${segment.words.length}`);

                    // 寻找最佳拆分点
                    const splitPoint = this.findOptimalSplitPoint(segment, SPLIT_CONFIG, logPrefix);

                    if (splitPoint !== -1) {
                        // 执行拆分
                        const [part1, part2] = this.splitSegmentAtPoint(segment, splitPoint, logPrefix);

                        if (part1 && part2) {
                            newResult.push(part1, part2);
                            splitCount++;
                            hasLongSegments = true; // 标记需要继续检查

                            const part1Duration = (part1.end - part1.start).toFixed(2);
                            const part2Duration = (part2.end - part2.start).toFixed(2);
                            logger.info(`${logPrefix}[长句拆分] 第${iteration}轮成功拆分ID:${segment.id} -> 两部分 (${part1.words.length}词/${part1Duration}s + ${part2.words.length}词/${part2Duration}s)`);
                        } else {
                            newResult.push(segment);
                            logger.warn(`${logPrefix}[长句拆分] 第${iteration}轮拆分失败，保持原segment ID:${segment.id}`);
                        }
                    } else {
                        newResult.push(segment);
                        logger.debug(`${logPrefix}[长句拆分] 第${iteration}轮未找到合适拆分点，保持原segment ID:${segment.id}`);
                    }
                } else {
                    newResult.push(segment);
                }
            }

            // 更新结果数组
            result = newResult;

            // 重新分配ID
            this.reassignSegmentIds(result);
        }

        if (iteration >= maxIterations) {
            logger.warn(`${logPrefix}[长句拆分] 达到最大迭代次数${maxIterations}，停止拆分`);
        }
        
        logger.info(`${logPrefix}[长句拆分] 拆分完成。执行${splitCount}次拆分，输出segments数量: ${result.length}`);

        // 详细记录每个字幕块的内容，便于语义分析
        logger.info(`${logPrefix}[字幕块详情] ========== 拆分后字幕块详细内容 ==========`);
        result.forEach((segment, index) => {
            const duration = (segment.end - segment.start).toFixed(2);
            const wordCount = segment.words ? segment.words.length : 0;
            logger.info(`${logPrefix}[字幕块${index + 1}] 时长:${duration}s | 词数:${wordCount} | 内容:"${segment.text}"`);
        });
        logger.info(`${logPrefix}[字幕块详情] ========== 字幕块记录完毕 ==========`);

        return result;
    }

    /**
     * @功能概述: 寻找segment的最佳拆分点
     * @param {Object} segment - 要拆分的segment
     * @param {Object} config - 拆分配置参数
     * @param {string} logPrefix - 日志前缀
     * @returns {number} 最佳拆分点的词索引，-1表示未找到合适点
     */
    findOptimalSplitPoint(segment, config, logPrefix) {
        const { words } = segment;
        const wordsCount = words.length;
        
        // 计算理想中点位置
        const idealMidPoint = Math.floor(wordsCount / 2);
        const searchStart = Math.max(config.MIN_PART_WORDS, Math.floor(idealMidPoint * (1 - config.SEARCH_RANGE_RATIO)));
        const searchEnd = Math.min(wordsCount - config.MIN_PART_WORDS, Math.floor(idealMidPoint * (1 + config.SEARCH_RANGE_RATIO)));
        
        logger.debug(`${logPrefix}[拆分点搜索] 词数:${wordsCount}, 理想中点:${idealMidPoint}, 搜索范围:[${searchStart}, ${searchEnd}]`);
        
        // 收集候选拆分点
        const candidates = [];
        
        // 优先级1: 寻找words间隙≥0.2秒的位置
        for (let i = searchStart; i < searchEnd; i++) {
            if (i < words.length - 1) {
                const gap = words[i + 1].start - words[i].end;
                if (gap >= config.WORD_GAP_THRESHOLD) {
                    // 检查是否应该避免在此位置拆分
                    const validation = this.shouldAvoidSplitAt(words, i + 1, logPrefix);
                    if (!validation.shouldAvoid) {
                        candidates.push({
                            position: i + 1,
                            priority: 1,
                            score: gap, // 间隙越大分数越高
                            reason: `词间隙${gap.toFixed(3)}s`
                        });
                    } else {
                        logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i + 1}(词间隙): ${validation.reason}`);
                    }
                }
            }
        }
        
        // 优先级2: NP-VP边界检测（基于NLTK研究）
        for (let i = searchStart; i < searchEnd; i++) {
            if (i < words.length - 1) {
                const currentWord = words[i];
                const nextWord = words[i + 1];

                if (currentWord && nextWord && currentWord.text && nextWord.text) {
                    const currentText = currentWord.text.toLowerCase().replace(/[^\w]/g, '');
                    const nextText = nextWord.text.toLowerCase().replace(/[^\w]/g, '');

                    // 检测NP结束 + VP开始的模式
                    const isNPEnd = this.isNounPhaseEnd(words, i, logPrefix);
                    const isVPStart = this.isVerbPhraseStart(nextText, logPrefix);

                    if (isNPEnd && isVPStart) {
                        // 确保前面有足够的名词短语内容（至少3个词）
                        if (i >= 2) {
                            const validation = this.shouldAvoidSplitAt(words, i + 1, logPrefix);
                            if (!validation.shouldAvoid) {
                                candidates.push({
                                    position: i + 1,
                                    priority: 2,
                                    score: Math.abs(i + 1 - idealMidPoint),
                                    reason: `NP-VP边界(${currentText}|${nextText})`
                                });
                                logger.debug(`${logPrefix}[拆分点筛选] 发现NP-VP边界: ${currentText} | ${nextText}`);
                            } else {
                                logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i + 1}(NP-VP边界): ${validation.reason}`);
                            }
                        }
                    }
                }
            }
        }

        // 优先级3: 时态保护检查（避免拆分完整时态）
        // 这个优先级主要在避免监测中实现，这里不添加候选点

        // 优先级4: 寻找标点符号后的位置
        for (let i = searchStart; i < searchEnd; i++) {
            const word = words[i];
            if (word.text && /[,.;:]$/.test(word.text.trim())) {
                // 检查是否应该避免在此位置拆分
                const validation = this.shouldAvoidSplitAt(words, i + 1, logPrefix);
                if (!validation.shouldAvoid) {
                    candidates.push({
                        position: i + 1,
                        priority: 4,
                        score: Math.abs(i + 1 - idealMidPoint), // 距离中点越近分数越低(越好)
                        reason: `标点符号后`
                    });
                } else {
                    logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i + 1}(标点符号后): ${validation.reason}`);
                }
            }
        }
        
        // 优先级5: 寻找连词前的位置
        const connectiveWords = ['and', 'but', 'or', 'so', 'however', 'therefore', 'because', 'although', 'while', 'when', 'where', 'which', 'that'];
        for (let i = searchStart; i < searchEnd; i++) {
            const word = words[i];
            if (word.text && connectiveWords.includes(word.text.toLowerCase().replace(/[^\w]/g, ''))) {
                // 检查是否应该避免在此位置拆分
                const validation = this.shouldAvoidSplitAt(words, i, logPrefix);
                if (!validation.shouldAvoid) {
                    candidates.push({
                        position: i,
                        priority: 5,
                        score: Math.abs(i - idealMidPoint),
                        reason: `连词前`
                    });
                } else {
                    logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i}(连词前): ${validation.reason}`);
                }
            }
        }

        // 优先级6: 寻找介词前的位置
        const prepositions = ['in', 'on', 'at', 'for', 'with', 'by', 'from', 'to', 'of', 'about', 'during', 'after', 'before', 'under', 'over'];
        for (let i = searchStart; i < searchEnd; i++) {
            const word = words[i];
            if (word.text && prepositions.includes(word.text.toLowerCase().replace(/[^\w]/g, ''))) {
                // 检查是否应该避免在此位置拆分
                const validation = this.shouldAvoidSplitAt(words, i, logPrefix);
                if (!validation.shouldAvoid) {
                    candidates.push({
                        position: i,
                        priority: 6,
                        score: Math.abs(i - idealMidPoint),
                        reason: `介词前`
                    });
                } else {
                    logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i}(介词前): ${validation.reason}`);
                }
            }
        }

        // 优先级7: 寻找助动词/情态动词后的位置（降级，避免与时态保护冲突）
        const auxiliaryVerbs = ['will', 'would', 'can', 'could', 'should', 'must', 'might', 'may'];
        for (let i = searchStart; i < searchEnd; i++) {
            const word = words[i];
            if (word.text && auxiliaryVerbs.includes(word.text.toLowerCase().replace(/[^\w]/g, ''))) {
                // 检查是否应该避免在此位置拆分（特别是时态保护）
                const validation = this.shouldAvoidSplitAt(words, i + 1, logPrefix);
                if (!validation.shouldAvoid) {
                    candidates.push({
                        position: i + 1,
                        priority: 7,
                        score: Math.abs(i + 1 - idealMidPoint),
                        reason: `助动词后`
                    });
                } else {
                    logger.debug(`${logPrefix}[拆分点筛选] 跳过位置${i + 1}(助动词后): ${validation.reason}`);
                }
            }
        }
        
        // 排序候选点：优先级越小越好，同优先级下分数规则不同
        candidates.sort((a, b) => {
            if (a.priority !== b.priority) {
                return a.priority - b.priority;
            }
            
            // 同优先级排序规则
            if (a.priority === 1) {
                return b.score - a.score; // 间隙越大越好
            } else {
                return a.score - b.score; // 距离中点越近越好
            }
        });
        
        // 选择最佳候选点
        if (candidates.length > 0) {
            const best = candidates[0];
            logger.debug(`${logPrefix}[拆分点选择] 选中位置:${best.position}, 原因:${best.reason}, 优先级:${best.priority}`);
            return best.position;
        }
        
        // 如果没有找到理想断点，选择最接近中点的位置作为降级方案
        if (searchEnd > searchStart) {
            const fallbackPosition = idealMidPoint;
            // 检查降级方案是否也需要避免
            const validation = this.shouldAvoidSplitAt(words, fallbackPosition, logPrefix);
            if (!validation.shouldAvoid) {
                logger.debug(`${logPrefix}[拆分点选择] 使用降级方案，位置:${fallbackPosition}`);
                return fallbackPosition;
            } else {
                logger.warn(`${logPrefix}[拆分点选择] 降级方案也被避免: ${validation.reason}，强制使用中点`);
                return fallbackPosition; // 强制使用，避免无法拆分
            }
        }
        
        logger.debug(`${logPrefix}[拆分点选择] 未找到合适拆分点`);
        return -1;
    }

    /**
     * @功能概述: 在指定位置拆分segment
     * @param {Object} segment - 原始segment
     * @param {number} splitPosition - 拆分位置（词索引）
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} [part1, part2] 拆分后的两个segment
     */
    splitSegmentAtPoint(segment, splitPosition, logPrefix) {
        try {
            const { words, text } = segment;
            
            // 拆分words数组
            const words1 = words.slice(0, splitPosition);
            const words2 = words.slice(splitPosition);
            
            // 验证拆分结果
            if (words1.length === 0 || words2.length === 0) {
                throw new Error(`拆分后某部分为空: part1=${words1.length}词, part2=${words2.length}词`);
            }
            
            // 计算时间边界
            const part1Start = segment.start;
            const part1End = words1[words1.length - 1].end;
            const part2Start = words2[0].start;
            const part2End = segment.end;
            
            // 构建文本内容
            const text1 = words1.map(w => w.text).join(' ').trim();
            const text2 = words2.map(w => w.text).join(' ').trim();
            
            // 计算统计字段（继承原有值，适度调整）
            const part1 = {
                ...segment,
                id: segment.id, // ID会在后续重新分配
                text: text1,
                start: part1Start,
                end: part1End,
                words: words1,
                // 统计字段按词数比例调整
                avg_logprob: segment.avg_logprob,
                compression_ratio: segment.compression_ratio * (words1.length / words.length),
                no_speech_prob: segment.no_speech_prob
            };
            
            const part2 = {
                ...segment,
                id: segment.id + 0.5, // 临时ID，会在后续重新分配
                text: text2,
                start: part2Start,
                end: part2End,
                words: words2,
                // 统计字段按词数比例调整
                avg_logprob: segment.avg_logprob,
                compression_ratio: segment.compression_ratio * (words2.length / words.length),
                no_speech_prob: segment.no_speech_prob
            };
            
            logger.debug(`${logPrefix}[段落拆分] 拆分详情: "${text1}" (${words1.length}词, ${(part1End-part1Start).toFixed(2)}s) + "${text2}" (${words2.length}词, ${(part2End-part2Start).toFixed(2)}s)`);
            
            return [part1, part2];
            
        } catch (error) {
            logger.error(`${logPrefix}[段落拆分] 拆分失败: ${error.message}`);
            return [null, null];
        }
    }

    /**
     * @功能概述: 重新分配segments的ID，确保从0开始连续递增
     * @param {Array} segments - 要重新分配ID的segments数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} ID重新分配后的segments数组
     */
    reassignSegmentIds(segments, logPrefix) {
        logger.debug(`${logPrefix}[ID重分配] 开始重新分配segments ID，数量: ${segments.length}`);
        
        const reindexedSegments = segments.map((segment, index) => ({
            ...segment,
            id: index  // 重新分配ID为0, 1, 2, 3...
        }));
        
        logger.debug(`${logPrefix}[ID重分配] ID重新分配完成，确保连续性: 0 -> ${reindexedSegments.length - 1}`);
        return reindexedSegments;
    }

    /**
     * @功能概述: 收集详细上下文信息
     * @returns {object} 包含字幕优化特定信息的详细上下文
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            const baseContext = super.collectDetailedContext();
            const taskResult = this.result || {};

            // 扩展上下文信息
            const optimizationDetails = {
                taskType: 'SubtitleOptimization',
                optimizationSteps: ['短segments合并', '句子片段合并', '智能拆分长句子'],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '任务完成' : '执行中',
                originalSegmentsCount: taskResult.originalSegmentsCount || 'N/A',
                optimizedSegmentsCount: taskResult.optimizedSegmentsCount || 'N/A',
                optimizationEfficiency: taskResult.originalSegmentsCount && taskResult.optimizedSegmentsCount 
                    ? `${(((taskResult.originalSegmentsCount - taskResult.optimizedSegmentsCount) / taskResult.originalSegmentsCount) * 100).toFixed(1)}%` 
                    : 'N/A',
                optimizationConfig: this.options,
                collectedAt: new Date().toISOString(),
                collectionMethod: 'SubtitleOptimizationTask.collectDetailedContext'
            };

            return {
                ...baseContext,
                optimizationDetails
            };

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);
            return {
                ...super.collectDetailedContext(),
                optimizationError: {
                    message: error.message,
                    stack: error.stack
                }
            };
        }
    }

    /**
     * @功能概述: 将优化后的segments转换为简化字幕JSON格式（5字段：id, start, end, text, words）
     * @param {Array} segments - 优化后的segments数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Array} 简化字幕JSON数组，每个对象包含5个字段：id, start, end, text, words
     * 
     * @字段说明:
     *   - id: 字幕块ID（从1开始递增）
     *   - start: 开始时间（秒数，浮点型）
     *   - end: 结束时间（秒数，浮点型）
     *   - text: 字幕文本内容
     *   - words: 单词级别的时间戳数组
     */
    convertToSimplifiedSubtitleJson(segments, execLogPrefix) {
        logger.info(`${execLogPrefix}[convertToSimplifiedSubtitleJson] 开始转换segments为简化字幕JSON格式。`);
        
        if (!Array.isArray(segments)) {
            const errorMsg = '输入的segments不是有效的数组';
            logger.error(`${execLogPrefix}[convertToSimplifiedSubtitleJson][ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        if (segments.length === 0) {
            logger.warn(`${execLogPrefix}[convertToSimplifiedSubtitleJson][WARN] segments数组为空，返回空的简化JSON数组。`);
            return [];
        }

        const simplifiedArray = segments.map((segment, index) => {
            try {
                // 验证必要字段
                if (typeof segment.start !== 'number' || typeof segment.end !== 'number') {
                    throw new Error(`segment[${index}] 缺少有效的start/end时间戳`);
                }

                if (typeof segment.text !== 'string') {
                    throw new Error(`segment[${index}] 缺少有效的text字段`);
                }

                // 构建简化的字幕对象（5字段）
                const simplifiedSegment = {
                    id: String(index + 1), // ID从1开始，确保为字符串类型
                    start: segment.start,   // 保持浮点秒数格式
                    end: segment.end,       // 保持浮点秒数格式
                    text: segment.text.trim(), // 清理文本前后空格
                    words: this.validateAndProcessWords(segment.words || [], execLogPrefix) // 处理words数组
                };

                return simplifiedSegment;

            } catch (conversionError) {
                logger.error(`${execLogPrefix}[convertToSimplifiedSubtitleJson][ERROR] 转换segment[${index}]失败: ${conversionError.message}`);
                logger.debug(`${execLogPrefix}[convertToSimplifiedSubtitleJson][DEBUG] 失败的segment数据: ${JSON.stringify(segment).substring(0, 200)}...`);
                
                // 返回一个基础的简化对象，避免整个转换过程失败
                return {
                    id: String(index + 1),
                    start: segment.start || 0,
                    end: segment.end || 0,
                    text: segment.text || '',
                    words: []
                };
            }
        });

        logger.info(`${execLogPrefix}[convertToSimplifiedSubtitleJson] 转换完成。原始: ${segments.length} -> 简化: ${simplifiedArray.length} 个字幕块`);
        
        // 验证转换结果
        const validSegments = simplifiedArray.filter(seg => seg.text && seg.text.trim() !== '');
        if (validSegments.length !== simplifiedArray.length) {
            logger.warn(`${execLogPrefix}[convertToSimplifiedSubtitleJson][WARN] 发现 ${simplifiedArray.length - validSegments.length} 个空文本的字幕块`);
        }

        return simplifiedArray;
    }

    /**
     * @功能概述: 验证和处理words数组，确保数据完整性
     * @param {Array} words - 原始words数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Array} 处理后的words数组
     */
    validateAndProcessWords(words, execLogPrefix) {
        if (!Array.isArray(words)) {
            logger.debug(`${execLogPrefix}[validateAndProcessWords] words不是数组，返回空数组`);
            return [];
        }

        if (words.length === 0) {
            return [];
        }

        // 验证和清理words数组
        const processedWords = words.map((word, index) => {
            try {
                // 验证必要字段
                if (typeof word.start !== 'number' || typeof word.end !== 'number') {
                    throw new Error(`word[${index}] 缺少有效的时间戳`);
                }

                if (typeof word.text !== 'string' || word.text.trim() === '') {
                    throw new Error(`word[${index}] 缺少有效的text字段`);
                }

                return {
                    text: word.text.trim(),
                    start: word.start,
                    end: word.end
                };

            } catch (wordError) {
                logger.debug(`${execLogPrefix}[validateAndProcessWords] 处理word[${index}]失败: ${wordError.message}`);
                return null; // 标记为无效，后续过滤
            }
        }).filter(word => word !== null); // 过滤无效的word

        // 按时间排序确保顺序正确
        processedWords.sort((a, b) => a.start - b.start);

        // 检查时间重叠并修正
        for (let i = 1; i < processedWords.length; i++) {
            if (processedWords[i].start < processedWords[i-1].end) {
                logger.debug(`${execLogPrefix}[validateAndProcessWords] 修正时间重叠: word[${i-1}]="${processedWords[i-1].text}"(${processedWords[i-1].end}) -> word[${i}]="${processedWords[i].text}"(${processedWords[i].start})`);
                processedWords[i].start = processedWords[i-1].end;
            }
        }

        logger.debug(`${execLogPrefix}[validateAndProcessWords] 处理完成。原始: ${words.length} -> 有效: ${processedWords.length} 个单词`);
        return processedWords;
    }

    /**
     * @功能概述: 保存简化字幕JSON文件
     * @param {Array} simplifiedJsonArray - 简化字幕JSON数组
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @param {string} savePath - 保存路径
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveSimplifiedSubtitleJson(simplifiedJsonArray, videoIdentifier, execLogPrefix, savePath) {
        const filename = `${videoIdentifier}_simplified_subtitle.json`;

        try {
            logger.debug(`${execLogPrefix}[saveSimplifiedSubtitleJson] 开始保存文件: ${filename}`);
            
            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(simplifiedJsonArray, null, 2),
                filename,
                savePath,
                execLogPrefix
            );

            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }

            logger.info(`${execLogPrefix}[saveSimplifiedSubtitleJson] 文件保存成功: ${savedPath}`);
            return savedPath;

        } catch (saveError) {
            const errorMsg = `保存简化字幕JSON文件失败: ${saveError.message}，路径：${savePath}/${filename}`;
            logger.error(`${execLogPrefix}[saveSimplifiedSubtitleJson][ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }
    }

    /**
     * @功能概述: 生成优化后的英文字幕SRT内容
     * @param {Array} simplifiedJsonArray - 简化字幕JSON数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {string} 优化后的英文字幕SRT内容
     */
    generateOptimizedSRT(simplifiedJsonArray, execLogPrefix) {
        logger.info(`${execLogPrefix}[generateOptimizedSRT] 开始生成优化后的英文字幕SRT内容。`);
        
        if (!Array.isArray(simplifiedJsonArray)) {
            const errorMsg = '输入的simplifiedJsonArray不是有效的数组';
            logger.error(`${execLogPrefix}[generateOptimizedSRT][ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        if (simplifiedJsonArray.length === 0) {
            logger.warn(`${execLogPrefix}[generateOptimizedSRT][WARN] simplifiedJsonArray为空，返回空的SRT内容。`);
            return '';
        }

        let srtContent = '';
        simplifiedJsonArray.forEach((segment, index) => {
            try {
                // 验证必要字段
                if (typeof segment.start !== 'number' || typeof segment.end !== 'number') {
                    throw new Error(`segment[${index}] 缺少有效的start/end时间戳`);
                }

                if (typeof segment.text !== 'string') {
                    throw new Error(`segment[${index}] 缺少有效的text字段`);
                }

                // 构建SRT格式
                const segmentId = segment.id || String(index + 1);
                const startTimeFormatted = this.formatTime(segment.start);
                const endTimeFormatted = this.formatTime(segment.end);
                const text = segment.text.trim();

                srtContent += `${segmentId}\r\n`;
                srtContent += `${startTimeFormatted} --> ${endTimeFormatted}\r\n`;
                srtContent += `${text}\r\n\r\n`;

            } catch (srtError) {
                logger.error(`${execLogPrefix}[generateOptimizedSRT][ERROR] 处理segment[${index}]失败: ${srtError.message}`);
                logger.debug(`${execLogPrefix}[generateOptimizedSRT][DEBUG] 失败的segment数据: ${JSON.stringify(segment).substring(0, 200)}...`);
                
                // 跳过有问题的segment，继续处理其他的
                return;
            }
        });

        // 移除末尾可能多余的空行
        if (srtContent.endsWith('\r\n\r\n')) {
            srtContent = srtContent.slice(0, -2);
        }

        // 验证生成结果
        if (typeof srtContent !== 'string') {
            const errorMsg = '生成的SRT内容无效，不是字符串类型';
            logger.error(`${execLogPrefix}[generateOptimizedSRT][ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        logger.info(`${execLogPrefix}[generateOptimizedSRT] 生成完成。原始: ${simplifiedJsonArray.length} -> 生成SRT: ${srtContent.length} 字符`);
        logger.debug(`${execLogPrefix}[generateOptimizedSRT] SRT内容预览 (前200字符): ${srtContent.substring(0, 200)}...`);
        
        return srtContent;
    }



    /**
     * @功能概述: 将秒数时间戳转换为 HH:MM:SS,ms 格式
     * @param {number} seconds - 秒数时间戳
     * @returns {string} HH:MM:SS,ms 格式的时间字符串
     */
    formatTime(seconds) {
        if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
            logger.warn(`[WARN] formatTime 输入无效秒数: ${seconds}`);
            return '00:00:00,000'; // 返回默认值
        }
        const date = new Date(seconds * 1000);
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const sec = String(date.getUTCSeconds()).padStart(2, '0');
        const ms = String(date.getUTCMilliseconds()).padStart(3, '0');
        return `${hours}:${minutes}:${sec},${ms}`;
    }

    /**
     * @功能概述: 核心避免监测函数，检测指定位置是否应该避免拆分
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    shouldAvoidSplitAt(words, position, logPrefix) {
        try {
            // 依次调用所有避免监测函数（包括新增的时态保护）
            const checks = [
                this.ensureMinimumWords(words, position, logPrefix),
                this.avoidTenseBreaking(words, position, logPrefix), // 新增：时态保护（优先级3）
                this.avoidFunctionWordEnding(words, position, logPrefix),
                this.avoidNumberSplitting(words, position, logPrefix),
                this.avoidCapitalizedNameSplitting(words, position, logPrefix)
            ];

            // 如果任何一个检查失败，返回避免拆分
            for (const check of checks) {
                if (check.shouldAvoid) {
                    logger.debug(`${logPrefix}[避免监测] 位置${position}被避免: ${check.reason}`);
                    return check;
                }
            }

            return {shouldAvoid: false, reason: 'validation passed'};

        } catch (error) {
            logger.error(`${logPrefix}[避免监测] 检测位置${position}时出错: ${error.message}`);
            return {shouldAvoid: true, reason: `检测异常: ${error.message}`};
        }
    }

    /**
     * @功能概述: 确保拆分后两部分都至少4个单词
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    ensureMinimumWords(words, position, logPrefix) {
        const part1Length = position;
        const part2Length = words.length - position;

        if (part1Length < 4) {
            return {shouldAvoid: true, reason: `第一部分只有${part1Length}个单词，少于最小要求4个`};
        }

        if (part2Length < 4) {
            return {shouldAvoid: true, reason: `第二部分只有${part2Length}个单词，少于最小要求4个`};
        }

        return {shouldAvoid: false, reason: 'minimum words check passed'};
    }

    /**
     * @功能概述: 避免在功能词结尾处拆分
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    avoidFunctionWordEnding(words, position, logPrefix) {
        if (position <= 0 || position >= words.length) {
            return {shouldAvoid: false, reason: 'position out of range'};
        }

        const functionWords = ['the', 'a', 'an', 'of', 'to', 'in', 'on', 'at', 'for', 'with',
                              'and', 'or', 'but', 'it', 'they', 'this', 'that', 'he', 'she'];

        const previousWord = words[position - 1];
        if (previousWord && previousWord.text) {
            const wordText = previousWord.text.toLowerCase().replace(/[^\w]/g, '');
            if (functionWords.includes(wordText)) {
                return {shouldAvoid: true, reason: `前一个词"${previousWord.text}"是功能词`};
            }
        }

        return {shouldAvoid: false, reason: 'function word check passed'};
    }

    /**
     * @功能概述: 避免在数字表达中间拆分
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    avoidNumberSplitting(words, position, logPrefix) {
        if (position <= 0 || position >= words.length) {
            return {shouldAvoid: false, reason: 'position out of range'};
        }

        const numberWords = ['one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten',
                            'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen', 'twenty',
                            'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety', 'hundred', 'thousand', 'million'];

        const unitWords = ['dollars', 'dollar', 'years', 'year', 'minutes', 'minute', 'seconds', 'second',
                          'hours', 'hour', 'days', 'day', 'weeks', 'week', 'months', 'month', 'percent', 'percentage'];

        const previousWord = words[position - 1];
        const currentWord = words[position];

        // 检查是否在数字+单位之间拆分
        if (previousWord && currentWord) {
            const prevText = previousWord.text.toLowerCase().replace(/[^\w]/g, '');
            const currText = currentWord.text.toLowerCase().replace(/[^\w]/g, '');

            if (numberWords.includes(prevText) && unitWords.includes(currText)) {
                return {shouldAvoid: true, reason: `避免在数字"${previousWord.text}"和单位"${currentWord.text}"之间拆分`};
            }

            // 检查是否在连续数字词之间拆分
            if (numberWords.includes(prevText) && numberWords.includes(currText)) {
                return {shouldAvoid: true, reason: `避免在连续数字词"${previousWord.text}"和"${currentWord.text}"之间拆分`};
            }
        }

        return {shouldAvoid: false, reason: 'number splitting check passed'};
    }

    /**
     * @功能概述: 避免在人名/地名中间拆分
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    avoidCapitalizedNameSplitting(words, position, logPrefix) {
        if (position <= 1 || position >= words.length - 1) {
            return {shouldAvoid: false, reason: 'position out of range for name check'};
        }

        // 检查拆分点前后是否有连续的大写词
        const checkRange = 2; // 检查前后2个词
        let capitalizedSequence = [];

        for (let i = Math.max(0, position - checkRange); i < Math.min(words.length, position + checkRange); i++) {
            const word = words[i];
            if (word && word.text && /^[A-Z]/.test(word.text.trim())) {
                capitalizedSequence.push({index: i, text: word.text});
            } else {
                // 如果遇到非大写词，重置序列
                if (capitalizedSequence.length >= 2 && i > position) {
                    break; // 如果已经有连续大写词且超过拆分点，停止检查
                }
                capitalizedSequence = [];
            }
        }

        // 如果有连续2个以上大写词且拆分点在其中间
        if (capitalizedSequence.length >= 2) {
            const firstCapIndex = capitalizedSequence[0].index;
            const lastCapIndex = capitalizedSequence[capitalizedSequence.length - 1].index;

            if (position > firstCapIndex && position <= lastCapIndex) {
                const nameText = capitalizedSequence.map(w => w.text).join(' ');
                return {shouldAvoid: true, reason: `避免在可能的人名/地名"${nameText}"中间拆分`};
            }
        }

        return {shouldAvoid: false, reason: 'capitalized name check passed'};
    }

    /**
     * @功能概述: 检测当前位置是否为名词短语结尾
     * @param {Array} words - words数组
     * @param {number} position - 检测位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {boolean} 是否为名词短语结尾
     */
    isNounPhaseEnd(words, position, logPrefix) {
        if (position < 0 || position >= words.length) {
            return false;
        }

        const word = words[position];
        if (!word || !word.text) {
            return false;
        }

        const text = word.text.toLowerCase().replace(/[^\w]/g, '');

        // 检测名词结尾模式
        const nounPatterns = [
            // 常见名词后缀
            /ers?$/, /ors?$/, /ists?$/, /ants?$/, /ents?$/, // 职业名词
            /tion$/, /sion$/, /ment$/, /ness$/, /ity$/, // 抽象名词
            /ing$/, /ed$/, // 动名词和过去分词作名词
        ];

        // 专有名词（首字母大写）
        if (/^[A-Z]/.test(word.text)) {
            return true;
        }

        // 常见名词词汇
        const commonNouns = [
            'people', 'person', 'man', 'woman', 'child', 'children',
            'company', 'organization', 'government', 'department',
            'system', 'program', 'project', 'service', 'product',
            'area', 'region', 'country', 'city', 'place', 'location',
            'time', 'year', 'month', 'day', 'week', 'hour',
            'firefighters', 'police', 'officers', 'workers', 'employees'
        ];

        if (commonNouns.includes(text)) {
            return true;
        }

        // 检查名词模式
        for (const pattern of nounPatterns) {
            if (pattern.test(text)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @功能概述: 检测当前词是否为动词短语开始
     * @param {string} text - 词文本（已清理）
     * @param {string} logPrefix - 日志前缀
     * @returns {boolean} 是否为动词短语开始
     */
    isVerbPhraseStart(text, logPrefix) {
        // 助动词（基于NLTK研究）
        const auxiliaryVerbs = [
            'have', 'has', 'had', 'will', 'would', 'shall', 'should',
            'can', 'could', 'may', 'might', 'must', 'ought'
        ];

        // be动词
        const beVerbs = ['am', 'is', 'are', 'was', 'were', 'be', 'been', 'being'];

        // 常见动词
        const commonVerbs = [
            'do', 'does', 'did', 'go', 'goes', 'went', 'come', 'comes', 'came',
            'get', 'gets', 'got', 'take', 'takes', 'took', 'make', 'makes', 'made',
            'see', 'sees', 'saw', 'know', 'knows', 'knew', 'think', 'thinks', 'thought',
            'say', 'says', 'said', 'tell', 'tells', 'told', 'give', 'gives', 'gave',
            'find', 'finds', 'found', 'work', 'works', 'worked', 'try', 'tries', 'tried',
            'contain', 'contains', 'contained', 'trying', 'been'
        ];

        return auxiliaryVerbs.includes(text) || beVerbs.includes(text) || commonVerbs.includes(text);
    }

    /**
     * @功能概述: 时态保护检查，避免拆分完整时态结构
     * @param {Array} words - words数组
     * @param {number} position - 拆分位置索引
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} {shouldAvoid: boolean, reason: string}
     */
    avoidTenseBreaking(words, position, logPrefix) {
        if (position <= 0 || position >= words.length) {
            return {shouldAvoid: false, reason: 'position out of range'};
        }

        const prevWord = words[position - 1];
        const currWord = words[position];

        if (!prevWord || !currWord || !prevWord.text || !currWord.text) {
            return {shouldAvoid: false, reason: 'invalid words'};
        }

        const prevText = prevWord.text.toLowerCase().replace(/[^\w]/g, '');
        const currText = currWord.text.toLowerCase().replace(/[^\w]/g, '');

        // 检测需要保护的时态组合
        const tensePatterns = [
            // 现在完成进行时
            {prev: ['have', 'has'], curr: ['been'], name: '现在完成进行时'},
            // 将来时
            {prev: ['will', 'would', 'shall'], curr: ['be', 'have', 'go', 'do'], name: '将来时'},
            // 情态动词+动词
            {prev: ['can', 'could', 'should', 'must', 'might', 'may'], curr: ['be', 'have', 'do', 'go'], name: '情态动词+动词'},
            // 助动词+过去分词
            {prev: ['have', 'has', 'had'], curr: ['been', 'done', 'gone', 'taken', 'made'], name: '完成时态'}
        ];

        for (const pattern of tensePatterns) {
            if (pattern.prev.includes(prevText) && pattern.curr.includes(currText)) {
                return {shouldAvoid: true, reason: `避免拆分${pattern.name}: ${prevText} ${currText}`};
            }
        }

        return {shouldAvoid: false, reason: 'tense protection check passed'};
    }
}

// 导出SubtitleOptimizationTask类
module.exports = SubtitleOptimizationTask;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}SubtitleOptimizationTask 类已导出。`); 