# GetTranscriptionTaskByCloudflare 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **audioFilePathInUploads** (string): 音频文件在uploads目录中的路径，来自ConvertToAudioForCloudflareTask
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **cloudflareOptimized** (boolean): 标识是否为Cloudflare优化版本
- **timeout** (number): API超时时间（毫秒），默认300000（5分钟）

## 2. 输出上下文参数 (Output Context)

- **transcriptionData** (object): 完整的Cloudflare转录数据对象
- **transcriptionJsonPath** (string): 保存的转录JSON文件路径
- **transcriptionSegments** (Array): 转录片段数组
- **transcriptionStatus** (string): 转录状态，成功时为'success'
- **audioFileIdentifier** (string): 音频文件标识符
- **processingStats** (object): 处理统计信息
  - **totalSegments** (number): 总片段数
  - **totalDuration** (number): 总时长
  - **apiResponseTime** (number): API响应时间
- **cloudflareTranscriptionUsed** (boolean): 标识使用了Cloudflare转录服务
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）



## 3. 文件操作

### 保存的文件格式
- **.json**: Cloudflare转录结果JSON文件

### 文件命名规则
- **模式**: `{videoIdentifier}_cloudflare_transcription.json`
- **示例**: `video123_cloudflare_transcription.json`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保多语言字符正确保存

## 5. 执行逻辑概述

Cloudflare语音转录任务专门针对Cloudflare Workers AI进行优化，处理来自ConvertToAudioForCloudflareTask的压缩音频文件。任务首先验证输入参数和音频文件的存在性，然后构建适合Cloudflare Workers AI的请求格式。通过HTTP POST请求调用Cloudflare API，获取包含词级别时间戳的转录结果。核心功能包括将Cloudflare特有的响应格式转换为标准的Azure兼容格式，确保下游任务的兼容性。任务提供详细的进度报告和错误处理，特别针对Cloudflare Workers AI的限制进行了优化，如文件大小检查和超时控制。
