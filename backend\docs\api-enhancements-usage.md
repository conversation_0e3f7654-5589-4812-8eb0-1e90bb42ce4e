# LLM Service API 增强功能使用指南

## 概述

`llmService.js` 已升级为支持 OpenRouter 的高级功能，同时保持与现有代码的完全向前兼容性。新功能通过 `apiEnhancements` 参数启用。

## 基本使用（向前兼容）

现有代码无需修改即可继续工作：

```javascript
// 传统用法（完全兼容）
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    modelName: 'google/gemini-2.5-flash-preview-05-20',
    temperature: 0.3,
    max_tokens: 20000,
    forceJsonOutput: true,
    validateJsonOutput: true
});
```

## 增强功能使用

### 1. OpenRouter Structured Outputs

强制特定 JSON Schema 格式的输出：

```javascript
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    
    // === 新增：API增强配置 ===
    apiEnhancements: {
        structuredOutput: {
            enabled: true,
            schema: {
                name: 'subtitle_correction',
                strict: true,
                schema: {
                    type: 'object',
                    properties: {
                        corrected_segments: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'integer' },
                                    start: { type: 'number' },
                                    end: { type: 'number' },
                                    text: { type: 'string' }
                                },
                                required: ['id', 'start', 'end', 'text'],
                                additionalProperties: false
                            }
                        }
                    },
                    required: ['corrected_segments'],
                    additionalProperties: false
                }
            }
        }
    }
});
```

### 2. 高级重试策略

指数退避和随机抖动：

```javascript
const result = await callLLM('TRANSLATE_SUBTITLE', {
    promptParams: { 
        simplifiedSubtitleJsonArray: subtitleData,
        target_language: 'Chinese'
    },
    retryCount: 3,
    
    apiEnhancements: {
        advancedRetry: {
            exponentialBackoff: true,  // 启用指数退避
            jitter: true,              // 启用随机抖动
            maxDelay: 30000           // 最大延迟30秒
        }
    }
});
```

### 3. 自定义请求头

添加特殊的请求头：

```javascript
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    
    apiEnhancements: {
        customHeaders: {
            'X-Custom-Client': 'MyApp-v1.0',
            'X-Priority': 'high',
            'X-Request-ID': generateUniqueId()
        }
    }
});
```

### 4. Message Transforms

启用 OpenRouter 的消息转换功能：

```javascript
const result = await callLLM('TRANSLATE_SUBTITLE', {
    promptParams: { 
        simplifiedSubtitleJsonArray: subtitleData,
        target_language: 'Chinese'
    },
    
    apiEnhancements: {
        transforms: ['middle-out']  // 启用中间-外部压缩
    }
});
```

### 5. 流式输出

启用流式响应（需要特殊处理）：

```javascript
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    
    apiEnhancements: {
        streaming: {
            enabled: true
        }
    }
});
```

### 6. 自定义响应处理

自定义 JSON 验证和响应解析：

```javascript
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    forceJsonOutput: true,
    validateJsonOutput: true,
    
    apiEnhancements: {
        responseProcessing: {
            // 自定义 JSON 验证器
            customValidator: (text) => {
                try {
                    const data = JSON.parse(text);
                    // 自定义验证逻辑
                    if (!data.corrected_segments || !Array.isArray(data.corrected_segments)) {
                        return { success: false, error: 'Missing corrected_segments array' };
                    }
                    return { success: true, data };
                } catch (error) {
                    return { success: false, error: error.message };
                }
            },
            
            // 自定义响应解析器
            customParser: (processedText, rawResponse) => {
                // 对处理后的文本进行后处理
                const data = JSON.parse(processedText);
                
                // 添加元数据
                data.metadata = {
                    processingTime: rawResponse.processing_time,
                    modelVersion: rawResponse.model,
                    requestId: rawResponse.id
                };
                
                return JSON.stringify(data);
            }
        }
    }
});
```

## 组合使用

可以同时启用多个增强功能：

```javascript
const result = await callLLM('CORRECT_TRANSCRIPTION', {
    promptParams: { segments_json_array_string: jsonData },
    modelName: 'google/gemini-2.5-flash-preview-05-20',
    temperature: 0.3,
    max_tokens: 20000,
    forceJsonOutput: true,
    validateJsonOutput: true,
    retryCount: 3,
    
    apiEnhancements: {
        // OpenRouter Structured Outputs
        structuredOutput: {
            enabled: true,
            schema: { /* ... */ }
        },
        
        // 高级重试策略
        advancedRetry: {
            exponentialBackoff: true,
            jitter: true,
            maxDelay: 30000
        },
        
        // 自定义请求头
        customHeaders: {
            'X-Custom-Client': 'VideoProcessor-v2.0',
            'X-Priority': 'high'
        },
        
        // Message Transforms
        transforms: ['middle-out'],
        
        // 自定义响应处理
        responseProcessing: {
            customValidator: customValidator,
            customParser: customParser
        }
    }
});
```

## 响应格式变化

启用增强功能后，响应对象会包含额外的元数据：

```javascript
{
    status: 'success',
    processedText: '...', 
    modelUsed: 'google/gemini-2.5-flash-preview-05-20',
    usage: { /* token 使用统计 */ },
    originalOptions: { /* 原始选项 */ },
    
    // === 新增：增强功能元数据 ===
    enhancedFeatures: {
        structuredOutputUsed: true,
        streamingUsed: false,
        customHeadersUsed: true,
        transformsUsed: true,
        advancedRetryUsed: true,
        customProcessingUsed: true
    }
}
```

## 在 Task 中的使用示例

在 `TranscriptionCorrectionTask.js` 中使用：

```javascript
// 在 callLLMForCorrection 方法中
const correctionOptions = {
    promptParams: {
        segments_json_array_string: JSON.stringify(simplifiedSubtitleJsonArray),
        // ... 其他参数
    },
    
    // === 传统参数（保持不变）===
    modelName: modelName,
    temperature: temperature,
    max_tokens: max_tokens,
    forceJsonOutput: true,
    validateJsonOutput: true,
    
    // === 新增：增强配置 ===
    apiEnhancements: {
        structuredOutput: {
            enabled: true,
            schema: {
                name: 'subtitle_correction',
                strict: true,
                schema: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'integer' },
                            start: { type: 'number' },
                            end: { type: 'number' },
                            text: { type: 'string' }
                        },
                        required: ['id', 'start', 'end', 'text'],
                        additionalProperties: false
                    }
                }
            }
        },
        advancedRetry: {
            exponentialBackoff: true,
            jitter: true
        }
    }
};

const result = await llmService.callLLM('CORRECT_TRANSCRIPTION', correctionOptions);
```

## 配置建议

1. **开发环境**：启用详细日志和自定义验证器进行调试
2. **生产环境**：启用指数退避重试和结构化输出以提高稳定性
3. **高负载场景**：使用自定义请求头进行请求追踪和优先级管理

## 向前兼容保证

- 所有现有代码无需修改即可继续工作
- 新功能通过可选的 `apiEnhancements` 参数启用
- 传统参数优先级保持不变
- 错误处理行为保持一致 