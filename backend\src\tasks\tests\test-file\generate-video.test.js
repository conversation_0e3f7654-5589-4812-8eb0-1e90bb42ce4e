/**
 * @功能概述: 音频重复拼接和视频生成的独立测试脚本，验证音频处理和视频合成功能
 * @核心能力: 
 *   - 原音频文件读取和验证
 *   - 音频重复拼接处理（无缝连接）
 *   - FFmpeg音频处理命令构建和执行
 *   - 输出文件生成验证和质量检查
 * @输入依赖: 
 *   - 原音频文件路径 (ORIGINAL_AUDIO_PATH)
 *   - 输出保存目录路径 (SAVE_PATH)
 *   - 音频重复次数 (VIDEO_CONFIG.repeatCount)
 *   - 系统FFmpeg可执行文件
 * @输出结果: 
 *   - 重复拼接后的MP3音频文件
 *   - 详细的执行日志和进度信息
 *   - 文件大小和质量验证报告
 * @测试范围: 端到端音频重复拼接流程，不包含LLM调用
 * @执行方式: node backend/src/tasks/tests/test-output/generate-video.test.js
 * @version 1.0.0
 * <AUTHOR>
 * @created 2025-06-08
 * @updated 2025-06-08
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

// 引用视频配置文件
const VIDEO_CONFIG = require('./video-config.json');

// 引入Canvas进度条生成工具函数
const { generateProgressBarVideoWithCanvas } = require('../../../utils/progressBarVideoGenerator');

// 引入Canvas背景视频生成工具函数
const { generateBackgroundVideoWithCanvas } = require('../../../utils/backgroundVideoGenerator');

// 模块级日志前缀 - 遵循标准格式
const moduleLogPrefix = '[文件：generate-video.test.js][音频重复拼接测试][模块初始化]';

/**
 * @功能概述: 标准化日志输出函数，提供统一的时间戳和级别标记
 * @参数说明: 
 *   - message: {string} 日志消息内容
 *   - level: {string} 日志级别 (INFO, ERROR, WARN, DEBUG)
 *   - functionName: {string} [可选] 调用函数名，用于构建完整日志前缀
 * @返回值: 无返回值，直接输出到控制台
 * @执行流程:
 *   1. 生成ISO格式时间戳
 *   2. 构建标准化日志前缀
 *   3. 格式化并输出日志消息
 */
function log(message, level = 'INFO', functionName = 'general') {
    const timestamp = new Date().toISOString();
    const logPrefix = `[文件：generate-video.test.js][音频重复拼接测试][${functionName}]`;
    console.log(`${timestamp} [${level}] ${logPrefix} ${message}`);
}

// 记录模块初始化
log('音频重复拼接测试模块已加载', 'INFO', '模块初始化');

/**
 * @配置说明: 测试数据文件路径和参数配置
 * @注意事项:
 *   - 路径使用绝对路径确保测试稳定性
 *   - 所有测试文件应位于 backend/uploads/test-data/ 目录
 *   - 修改路径时需确保文件实际存在
 */

/**
 * 视频标识符 - 用于生成输出文件名的基础标识
 */
const videoIdentifier = 'test_123';



/**
 * 原始视频文件路径 - 用于烧录到生成视频中的原始视频内容
 */
const ORIGINAL_VIDEO_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z.mp4';

/**
 * 原音频文件路径 - 用于重复拼接的基础音频文件
 */
const ORIGINAL_AUDIO_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-_744348a8_audio.mp3';

/**
 * 输出文件保存目录路径 - 生成的拼接音频和视频文件存储位置
 */
const SAVE_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data';

/**
 * 输出音频文件名 - 拼接后的音频文件名称（基于videoIdentifier动态生成）
 */
const OUTPUT_AUDIO_FILENAME = `${videoIdentifier}_extended_audio.mp3`;

/**
 * 输出视频文件名 - 生成的9:16短视频文件名称（基于videoIdentifier动态生成）
 */
const OUTPUT_VIDEO_FILENAME = `${videoIdentifier}_extended_video.mp4`;

// 音频重复次数现在在VIDEO_CONFIG.repeatCount中配置

// 硬编码：报纸背景图片路径
const NEWSPAPER_BACKGROUND_PATH = path.join(__dirname, '../../../assets/newspaper_9_16.png');

// 硬编码：ASS字幕文件路径
const ASS_SUBTITLE_PATH = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\test_123_extended_ass.ass';

// VIDEO_CONFIG 现在从 video-config.json 文件中加载





/**
 * @功能概述: 检查原音频文件是否存在并获取基本文件信息
 * @返回值: {Promise<boolean>} 音频文件是否存在且可访问
 * @错误处理: 
 *   - 文件不存在时返回false而非抛出异常
 *   - 记录详细的错误信息用于问题诊断
 * @执行流程:
 *   1. 尝试访问音频文件路径
 *   2. 获取文件统计信息（大小、修改时间等）
 *   3. 计算并记录文件大小（MB单位）
 *   4. 返回文件存在状态
 */
async function checkOriginalAudio() {
    const functionName = 'checkOriginalAudio';
    
    try {
        log('检查原音频文件是否存在...', 'INFO', functionName); // 记录检查开始
        log(`原音频文件路径: ${ORIGINAL_AUDIO_PATH}`, 'DEBUG', functionName); // 记录文件路径

        // 步骤 1: 检查文件访问权限
        await fs.access(ORIGINAL_AUDIO_PATH);

        // 步骤 2: 获取文件统计信息
        const stats = await fs.stat(ORIGINAL_AUDIO_PATH);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        log(`原音频文件存在，大小: ${fileSizeMB}MB`, 'INFO', functionName); // 记录文件存在和大小信息
        return true;

    } catch (error) {
        log(`原音频文件不存在或无法访问: ${error.message}`, 'ERROR', functionName); // 记录访问失败错误
        return false; // 返回false而非抛出异常，便于上层逻辑处理
    }
}

/**
 * @功能概述: 检查原始视频文件是否存在并获取基本文件信息
 * @返回值: {Promise<boolean>} 原始视频文件是否存在且可访问
 * @错误处理:
 *   - 文件不存在时返回false而非抛出异常
 *   - 记录详细的错误信息用于问题诊断
 * @执行流程:
 *   1. 尝试访问原始视频文件路径
 *   2. 获取文件统计信息（大小、修改时间等）
 *   3. 计算并记录文件大小（MB单位）
 *   4. 返回文件存在状态
 */
async function checkOriginalVideo() {
    const functionName = 'checkOriginalVideo';

    try {
        log('检查原始视频文件是否存在...', 'INFO', functionName); // 记录检查开始
        log(`原始视频文件路径: ${ORIGINAL_VIDEO_PATH}`, 'DEBUG', functionName); // 记录文件路径

        // 步骤 1: 检查文件访问权限
        await fs.access(ORIGINAL_VIDEO_PATH);

        // 步骤 2: 获取文件统计信息
        const stats = await fs.stat(ORIGINAL_VIDEO_PATH);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        log(`原始视频文件存在，大小: ${fileSizeMB}MB`, 'INFO', functionName); // 记录文件存在和大小信息
        return true;

    } catch (error) {
        log(`原始视频文件不存在或无法访问: ${error.message}`, 'ERROR', functionName); // 记录访问失败错误
        return false; // 返回false而非抛出异常，便于上层逻辑处理
    }
}

/**
 * @功能概述: 检查输出保存目录是否存在且具有写入权限
 * @返回值: {Promise<boolean>} 保存路径是否存在且可写入
 * @错误处理:
 *   - 目录不存在时返回false
 *   - 权限不足时记录详细错误信息
 * @执行流程:
 *   1. 验证保存目录的访问权限
 *   2. 确认目录可用于文件写入操作
 *   3. 返回目录可用状态
 */
async function checkSavePath() {
    const functionName = 'checkSavePath';

    try {
        log('检查保存路径是否存在...', 'INFO', functionName); // 记录检查开始
        log(`保存路径: ${SAVE_PATH}`, 'DEBUG', functionName); // 记录保存路径

        // 步骤 1: 检查目录访问权限
        await fs.access(SAVE_PATH);
        log('保存路径存在', 'INFO', functionName); // 记录路径存在确认
        return true;

    } catch (error) {
        log(`保存路径不存在或无法访问: ${error.message}`, 'ERROR', functionName); // 记录访问失败错误
        return false; // 返回false便于上层处理
    }
}

/**
 * @功能概述: 使用FFmpeg将原音频重复拼接指定次数，生成扩展音频文件
 * @返回值: {Promise<string>} 生成的输出音频文件完整路径
 * @错误处理: 
 *   - FFmpeg进程启动失败时抛出进程错误
 *   - 转换超时（5分钟）时强制终止并抛出超时错误
 *   - 输出文件验证失败时抛出文件错误
 * @执行流程:
 *   1. 构建输出文件完整路径
 *   2. 生成FFmpeg音频拼接命令参数
 *   3. 启动FFmpeg子进程并监听输出
 *   4. 解析进度信息并实时报告
 *   5. 验证输出文件并返回路径
 * @技术参数:
 *   - 音频编码器: copy (直接复制，无重编码)
 *   - 拼接方式: concat filter (无缝连接)
 *   - 输出格式: MP3
 */
async function repeatAudioConcat() {
    const functionName = 'repeatAudioConcat';
    
    return new Promise((resolve, reject) => {
        try {
            log('开始音频重复拼接处理...', 'INFO', functionName); // 记录拼接开始

            // 步骤 1: 构建输出文件完整路径
            const outputPath = path.join(SAVE_PATH, OUTPUT_AUDIO_FILENAME);
            log(`输出文件路径: ${outputPath}`, 'DEBUG', functionName); // 记录输出路径

            // 步骤 2: 构建FFmpeg命令参数
            // 使用concat filter进行音频无缝拼接
            const inputArgs = [];
            const filterInputs = [];
            
            // 为每次重复添加输入参数
            for (let i = 0; i < VIDEO_CONFIG.repeatCount; i++) {
                inputArgs.push('-i', ORIGINAL_AUDIO_PATH);
                filterInputs.push(`[${i}:a]`);
            }

            const concatFilter = `${filterInputs.join('')}concat=n=${VIDEO_CONFIG.repeatCount}:v=0:a=1[outa]`;
            
            const ffmpegArgs = [
                ...inputArgs,                               // 多个输入文件
                '-filter_complex', concatFilter,            // 音频拼接滤镜
                '-map', '[outa]',                          // 映射输出音频流
                '-c:a', 'libmp3lame',                      // MP3编码器
                '-b:a', '192k',                            // 音频比特率
                '-y',                                      // 覆盖输出文件
                outputPath                                 // 输出路径
            ];

            log(`FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`, 'DEBUG', functionName); // 记录完整FFmpeg命令
            log(`音频重复次数: ${VIDEO_CONFIG.repeatCount}`, 'INFO', functionName); // 记录重复次数

            // 步骤 3: 启动FFmpeg进程
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

            let ffmpegOutput = '';
            let ffmpegError = '';

            // 步骤 4: 监听stdout输出（标准输出）
            ffmpegProcess.stdout.on('data', (data) => {
                ffmpegOutput += data.toString();
                // 标准输出通常较少，主要用于收集完整输出
            });

            // 步骤 5: 监听stderr输出（FFmpeg的主要输出和进度信息）
            ffmpegProcess.stderr.on('data', (data) => {
                const output = data.toString();
                ffmpegError += output;

                // 步骤 5.1: 解析时间进度信息
                if (output.includes('time=')) {
                    const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                    if (timeMatch) {
                        const timeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
                        log(`FFmpeg进度: ${timeStr}`, 'INFO', functionName); // 记录时间进度
                    }
                }

                // 步骤 5.2: 解析比特率信息
                if (output.includes('bitrate=')) {
                    const bitrateMatch = output.match(/bitrate=\s*(\d+\.?\d*kbits\/s)/);
                    if (bitrateMatch) {
                        log(`当前处理比特率: ${bitrateMatch[1]}`, 'DEBUG', functionName); // 记录比特率信息（调试级别）
                    }
                }
            });

            // 步骤 6: 监听进程退出事件
            ffmpegProcess.on('close', async (code) => {
                if (code === 0) {
                    log('FFmpeg执行成功', 'INFO', functionName); // 记录执行成功

                    try {
                        // 步骤 6.1: 验证输出文件是否存在
                        await fs.access(outputPath);

                        // 步骤 6.2: 获取输出文件统计信息
                        const stats = await fs.stat(outputPath);
                        const outputSizeMB = (stats.size / 1024 / 1024).toFixed(2);

                        log(`输出文件生成成功: ${outputPath}`, 'INFO', functionName); // 记录文件生成成功
                        log(`输出文件大小: ${outputSizeMB}MB`, 'INFO', functionName); // 记录文件大小

                        resolve(outputPath); // 返回输出文件路径

                    } catch (error) {
                        log(`输出文件验证失败: ${error.message}`, 'ERROR', functionName); // 记录文件验证失败
                        reject(new Error(`输出文件不存在: ${outputPath}`)); // 抛出文件不存在错误
                    }
                } else {
                    const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName); // 记录执行失败
                    log(`FFmpeg错误输出: ${ffmpegError.slice(-500)}`, 'ERROR', functionName); // 记录错误输出（最后500字符）
                    reject(new Error(errorMsg)); // 抛出执行失败错误
                }
            });

            // 步骤 7: 监听进程启动错误
            ffmpegProcess.on('error', (error) => {
                const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName); // 记录进程启动失败
                reject(new Error(errorMsg)); // 抛出进程启动错误
            });

            // 步骤 8: 设置超时保护机制（5分钟）
            setTimeout(() => {
                if (!ffmpegProcess.killed) {
                    log('FFmpeg执行超时，强制终止进程', 'WARN', functionName); // 记录超时警告
                    ffmpegProcess.kill('SIGTERM'); // 优雅终止

                    // 5秒后强制杀死进程
                    setTimeout(() => {
                        if (!ffmpegProcess.killed) {
                            ffmpegProcess.kill('SIGKILL'); // 强制杀死
                        }
                    }, 5000);

                    reject(new Error('FFmpeg执行超时')); // 抛出超时错误
                }
            }, 300000); // 5分钟超时

        } catch (error) {
            log(`音频重复拼接失败: ${error.message}`, 'ERROR', functionName); // 记录拼接失败
            reject(error); // 重新抛出错误
        }
    });
}

/**
 * @功能概述: 获取音频文件的时长信息，用于确定视频生成的时长
 * @参数说明:
 *   - audioPath: {string} 音频文件的完整路径
 * @返回值: {Promise<number>} 音频时长（秒）
 * @错误处理:
 *   - FFprobe进程启动失败时抛出进程错误
 *   - 音频信息解析失败时抛出解析错误
 * @执行流程:
 *   1. 使用FFprobe获取音频文件信息
 *   2. 解析JSON格式的媒体信息
 *   3. 提取音频流的时长数据
 *   4. 返回时长数值（秒）
 * @技术参数:
 *   - 使用FFprobe的JSON输出格式
 *   - 获取第一个音频流的duration信息
 */
async function getAudioDuration(audioPath) {
    const functionName = 'getAudioDuration';

    return new Promise((resolve, reject) => {
        try {
            log('开始获取音频时长信息...', 'INFO', functionName); // 记录获取开始
            log(`音频文件路径: ${audioPath}`, 'DEBUG', functionName); // 记录音频路径

            // 构建FFprobe命令参数 - 获取JSON格式的媒体信息
            const ffprobeArgs = [
                '-v', 'quiet',                    // 静默模式，减少输出
                '-print_format', 'json',          // 输出JSON格式
                '-show_format',                   // 显示格式信息
                '-show_streams',                  // 显示流信息
                audioPath                         // 音频文件路径
            ];

            log(`FFprobe命令: ffprobe ${ffprobeArgs.join(' ')}`, 'DEBUG', functionName); // 记录完整FFprobe命令

            // 启动FFprobe进程
            const ffprobeProcess = spawn('ffprobe', ffprobeArgs);

            let ffprobeOutput = '';
            let ffprobeError = '';

            // 监听stdout输出（JSON数据）
            ffprobeProcess.stdout.on('data', (data) => {
                ffprobeOutput += data.toString();
            });

            // 监听stderr输出（错误信息）
            ffprobeProcess.stderr.on('data', (data) => {
                ffprobeError += data.toString();
            });

            // 监听进程退出事件
            ffprobeProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        // 解析JSON输出
                        const mediaInfo = JSON.parse(ffprobeOutput);

                        // 从format信息中获取时长
                        let duration = null;
                        if (mediaInfo.format && mediaInfo.format.duration) {
                            duration = parseFloat(mediaInfo.format.duration);
                        }

                        // 如果format中没有时长，尝试从音频流中获取
                        if (!duration && mediaInfo.streams) {
                            const audioStream = mediaInfo.streams.find(stream => stream.codec_type === 'audio');
                            if (audioStream && audioStream.duration) {
                                duration = parseFloat(audioStream.duration);
                            }
                        }

                        if (duration && duration > 0) {
                            log(`音频时长获取成功: ${duration.toFixed(2)} 秒`, 'INFO', functionName); // 记录时长信息
                            resolve(duration);
                        } else {
                            const errorMsg = '无法获取有效的音频时长信息';
                            log(errorMsg, 'ERROR', functionName);
                            reject(new Error(errorMsg));
                        }

                    } catch (parseError) {
                        const errorMsg = `解析音频信息失败: ${parseError.message}`;
                        log(errorMsg, 'ERROR', functionName);
                        log(`FFprobe输出: ${ffprobeOutput}`, 'DEBUG', functionName);
                        reject(new Error(errorMsg));
                    }
                } else {
                    const errorMsg = `FFprobe执行失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName);
                    log(`FFprobe错误输出: ${ffprobeError}`, 'ERROR', functionName);
                    reject(new Error(errorMsg));
                }
            });

            // 监听进程启动错误
            ffprobeProcess.on('error', (error) => {
                const errorMsg = `FFprobe进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName);
                reject(new Error(errorMsg));
            });

        } catch (error) {
            log(`获取音频时长失败: ${error.message}`, 'ERROR', functionName);
            reject(error);
        }
    });
}



/**
 * @功能概述: 生成动态进度条视频，通过Canvas生成并组合多个视频元素
 * @参数说明:
 *   - originalAudioDuration: 音频原始时长（秒），用于控制进度条动画时长和最终视频时长
 * @返回值: Promise<string> - 解析为生成的进度条视频文件绝对路径
 * @进度条配置:
 *   - 宽度: 从VIDEO_CONFIG继承主视频宽度
 *   - 高度: 使用VIDEO_CONFIG.progressBar.height配置项
 *   - 背景色: 使用VIDEO_CONFIG.progressBar.backgroundColor配置项
 *   - 前景色: 使用VIDEO_CONFIG.progressBar.foregroundColor配置项
 *   - 帧率: 与主视频保持一致(VIDEO_CONFIG.framerate)
 * @实现细节:
 *   - 使用时间戳生成唯一文件名，避免文件冲突
 *   - 输出路径拼接使用系统统一存储路径(SAVE_PATH)
 *   - 依赖generateProgressBarVideoWithCanvas实现核心动画逻辑
 *   - 完整处理链包含成功/错误日志记录和异常传播
 */
async function generateProgressBarVideo(originalAudioDuration) {
    const functionName = 'generateProgressBarVideo';

    log('开始使用Canvas生成进度条视频...', 'INFO', functionName);
    log(`原音频时长: ${originalAudioDuration.toFixed(2)} 秒`, 'INFO', functionName);

    // 构建进度条视频文件路径
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const progressBarFilename = `${videoIdentifier}_progress_bar_${timestamp}.mp4`;
    const progressBarPath = path.join(SAVE_PATH, progressBarFilename);

    try {
        // 使用Canvas生成进度条视频
        const result = await generateProgressBarVideoWithCanvas({
            duration: originalAudioDuration,
            width: VIDEO_CONFIG.width,
            height: VIDEO_CONFIG.progressBar.height,
            backgroundColor: VIDEO_CONFIG.progressBar.backgroundColor,
            foregroundColor: VIDEO_CONFIG.progressBar.foregroundColor,
            framerate: VIDEO_CONFIG.framerate,
            outputPath: progressBarPath
        });

        log(`Canvas进度条视频生成成功: ${result}`, 'INFO', functionName);
        return result;

    } catch (error) {
        log(`Canvas进度条视频生成失败: ${error.message}`, 'ERROR', functionName);
        throw error;
    }
}

/**
 * @功能概述: 生成带报纸背景+80%黑色遮罩的背景视频
 * @参数说明:
 *   - totalDuration: 总视频时长（秒）= getAudioDuration * VIDEO_CONFIG.repeatCount
 * @返回值: Promise<string> - 生成的背景视频文件路径
 */
async function generateBackgroundVideo(totalDuration) {
    const functionName = 'generateBackgroundVideo';

    log('开始使用Canvas生成背景视频...', 'INFO', functionName);
    log(`总视频时长: ${totalDuration.toFixed(2)} 秒`, 'INFO', functionName);

    // 构建背景视频文件路径
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backgroundFilename = `${videoIdentifier}_background_${timestamp}.mp4`;
    const backgroundPath = path.join(SAVE_PATH, backgroundFilename);

    try {
        // 使用Canvas生成背景视频
        const result = await generateBackgroundVideoWithCanvas({
            duration: totalDuration,
            width: VIDEO_CONFIG.width,
            height: VIDEO_CONFIG.height,
            backgroundImagePath: NEWSPAPER_BACKGROUND_PATH,
            framerate: VIDEO_CONFIG.framerate,
            outputPath: backgroundPath
        });

        log(`Canvas背景视频生成成功: ${result}`, 'INFO', functionName);
        return result;

    } catch (error) {
        log(`Canvas背景视频生成失败: ${error.message}`, 'ERROR', functionName);
        throw error;
    }
}

/**
 * @功能概述: 解析和验证ASS字幕文件
 * @参数说明:
 *   - assFilePath: ASS字幕文件路径
 * @返回值: Promise<string> - 验证通过的ASS文件路径
 * @技术实现:
 *   1. 检查ASS文件是否存在
 *   2. 读取文件内容进行基本验证
 *   3. 验证ASS格式的基本结构
 *   4. 返回可用于FFmpeg的文件路径
 */
async function parseAndValidateAssSubtitle(assFilePath) {
    const functionName = 'parseAndValidateAssSubtitle';

    try {
        log('开始解析和验证ASS字幕文件...', 'INFO', functionName);
        log(`ASS文件路径: ${assFilePath}`, 'INFO', functionName);

        // 检查文件是否存在
        try {
            await fs.access(assFilePath);
        } catch (error) {
            throw new Error(`ASS字幕文件不存在: ${assFilePath}`);
        }

        // 读取文件内容
        const assContent = await fs.readFile(assFilePath, 'utf8');
        log(`ASS文件读取成功，内容长度: ${assContent.length} 字符`, 'INFO', functionName);

        // 基本格式验证
        const requiredSections = ['[Script Info]', '[V4+ Styles]', '[Events]'];
        const missingSections = requiredSections.filter(section => !assContent.includes(section));

        if (missingSections.length > 0) {
            throw new Error(`ASS文件格式不完整，缺少部分: ${missingSections.join(', ')}`);
        }

        // 统计Dialogue事件数量
        const dialogueLines = assContent.split('\n').filter(line => line.startsWith('Dialogue:'));
        log(`ASS文件验证通过，包含 ${dialogueLines.length} 个字幕事件`, 'INFO', functionName);

        // 检查是否包含视频标题
        const titleEvents = dialogueLines.filter(line => line.includes('VideoTitle'));
        log(`包含视频标题事件: ${titleEvents.length} 个`, 'INFO', functionName);

        // 检查文件大小
        const stats = await fs.stat(assFilePath);
        const fileSizeKB = (stats.size / 1024).toFixed(2);
        log(`ASS文件大小: ${fileSizeKB} KB`, 'INFO', functionName);

        log('ASS字幕文件解析和验证完成', 'INFO', functionName);
        return assFilePath;

    } catch (error) {
        log(`ASS字幕文件解析失败: ${error.message}`, 'ERROR', functionName);
        throw error;
    }
}

/**
 * @功能概述: 执行generate-aas.test.js脚本生成最新的ASS字幕文件
 * @返回值: Promise<void> - 执行完成
 * @技术实现:
 *   1. 使用child_process.spawn执行node generate-aas.test.js
 *   2. 监听脚本执行过程和输出
 *   3. 确保ASS文件生成成功
 *   4. 处理执行错误和异常情况
 */
async function executeGenerateAssScript() {
    const functionName = 'executeGenerateAssScript';

    return new Promise((resolve, reject) => {
        try {
            log('开始执行generate-aas.test.js脚本...', 'INFO', functionName);

            // 构建执行命令
            const scriptPath = 'generate-aas.test.js';
            const workingDirectory = __dirname; // 当前脚本所在目录

            log(`脚本路径: ${scriptPath}`, 'INFO', functionName);
            log(`工作目录: ${workingDirectory}`, 'INFO', functionName);

            // 启动generate-aas.test.js进程
            const assProcess = spawn('node', [scriptPath], {
                cwd: workingDirectory,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let assOutput = '';
            let assError = '';

            // 监听stdout输出
            assProcess.stdout.on('data', (data) => {
                const output = data.toString();
                assOutput += output;
                // 实时输出关键信息
                if (output.includes('✅') || output.includes('❌') || output.includes('ASS文件')) {
                    log(`[generate-aas.test.js] ${output.trim()}`, 'INFO', functionName);
                }
            });

            // 监听stderr输出
            assProcess.stderr.on('data', (data) => {
                const error = data.toString();
                assError += error;
                log(`[generate-aas.test.js] ERROR: ${error.trim()}`, 'ERROR', functionName);
            });

            // 监听进程退出事件
            assProcess.on('close', (code) => {
                if (code === 0) {
                    log('generate-aas.test.js执行成功', 'INFO', functionName);
                    log('ASS字幕文件生成完成', 'INFO', functionName);
                    resolve();
                } else {
                    const errorMsg = `generate-aas.test.js执行失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName);
                    if (assError) {
                        log(`错误输出: ${assError}`, 'ERROR', functionName);
                    }
                    reject(new Error(errorMsg));
                }
            });

            // 监听进程启动错误
            assProcess.on('error', (error) => {
                const errorMsg = `generate-aas.test.js进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName);
                reject(new Error(errorMsg));
            });

        } catch (error) {
            log(`执行generate-aas.test.js失败: ${error.message}`, 'ERROR', functionName);
            reject(error);
        }
    });
}

/**
 * @功能概述: 重复进度条视频，使其与重复后的音频时长匹配
 * @参数说明:
 *   - progressBarPath: 单次进度条视频路径
 *   - totalDuration: 重复后的总时长
 * @返回值: Promise<string> - 成功时返回重复后的进度条视频路径
 */
async function repeatProgressBarVideo(progressBarPath, totalDuration) {
    const functionName = 'repeatProgressBarVideo';

    log('开始重复进度条视频...', 'INFO', functionName);
    log(`重复次数: ${VIDEO_CONFIG.repeatCount}, 总时长: ${totalDuration.toFixed(2)} 秒`, 'INFO', functionName);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const repeatedProgressBarFilename = `${videoIdentifier}_progress_bar_repeated_${timestamp}.mp4`;
    const repeatedProgressBarPath = path.join(SAVE_PATH, repeatedProgressBarFilename);

    // 检查并删除可能存在的同名文件
    try {
        await fs.access(repeatedProgressBarPath);
        await fs.unlink(repeatedProgressBarPath);
        log(`已删除存在的同名重复进度条文件: ${repeatedProgressBarPath}`, 'DEBUG', functionName);
    } catch (error) {
        log(`重复进度条文件不存在，可以直接创建: ${repeatedProgressBarPath}`, 'DEBUG', functionName);
    }

    return new Promise((resolve, reject) => {
        try {
            // 构建重复进度条的输入列表
            const inputArgs = [];
            const concatInputs = [];

            for (let i = 0; i < VIDEO_CONFIG.repeatCount; i++) {
                inputArgs.push('-i', progressBarPath);
                concatInputs.push(`[${i}]`);
            }

            const repeatArgs = [
                ...inputArgs,
                '-filter_complex', `${concatInputs.join('')}concat=n=${VIDEO_CONFIG.repeatCount}:v=1:a=0[repeated_progress]`,
                '-map', '[repeated_progress]',
                '-c:v', VIDEO_CONFIG.codec,
                '-preset', VIDEO_CONFIG.preset,
                '-crf', VIDEO_CONFIG.crf.toString(),
                '-r', VIDEO_CONFIG.framerate.toString(),
                '-y',
                repeatedProgressBarPath
            ];

            log(`重复进度条FFmpeg命令: ffmpeg ${repeatArgs.join(' ')}`, 'DEBUG', functionName);

            const ffmpegProcess = spawn('ffmpeg', repeatArgs);

            let ffmpegError = '';

            ffmpegProcess.stderr.on('data', (data) => {
                ffmpegError += data.toString();
            });

            ffmpegProcess.on('close', async (code) => {
                if (code === 0) {
                    try {
                        await fs.access(repeatedProgressBarPath);
                        const stats = await fs.stat(repeatedProgressBarPath);
                        const sizeMB = (stats.size / 1024 / 1024).toFixed(2);

                        log(`重复进度条视频生成成功: ${repeatedProgressBarPath}`, 'INFO', functionName);
                        log(`重复进度条视频大小: ${sizeMB}MB`, 'INFO', functionName);

                        resolve(repeatedProgressBarPath);
                    } catch (error) {
                        reject(new Error(`重复进度条视频不存在: ${repeatedProgressBarPath}`));
                    }
                } else {
                    const errorMsg = `重复进度条视频生成失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName);
                    log(`FFmpeg错误输出: ${ffmpegError.slice(-500)}`, 'ERROR', functionName);
                    reject(new Error(errorMsg));
                }
            });

            ffmpegProcess.on('error', (error) => {
                const errorMsg = `重复进度条FFmpeg进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName);
                reject(new Error(errorMsg));
            });

        } catch (error) {
            log(`重复进度条视频失败: ${error.message}`, 'ERROR', functionName);
            reject(error);
        }
    });
}

/**
 * @功能概述: 生成9:16比例的视频，将原始视频烧录到纯白背景上，叠加进度条视频和ASS字幕，时长与音频文件匹配
 * @参数说明:
 *   - audioDuration: {number} 音频时长（秒），用于确定视频时长
 *   - audioPath: {string} 音频文件路径，用于合成最终视频
 *   - progressBarVideoPath: {string} 进度条视频路径，用于叠加到主视频上
 *   - backgroundVideoPath: {string} 背景视频路径，Canvas生成的报纸背景
 *   - assSubtitlePath: {string} ASS字幕文件路径，用于烧录字幕
 * @返回值: {Promise<string>} 生成的输出视频文件完整路径
 * @技术方案: 使用 -filter_complex_script 将复杂的滤镜写入文件，并使用subtitles滤镜烧录ASS字幕。
 */
async function generateVideo(audioDuration, audioPath, progressBarVideoPath, backgroundVideoPath, assSubtitlePath) {
    const functionName = 'generateVideo';

    log('开始生成9:16视频...', 'INFO', functionName);
    log(`视频时长: ${audioDuration.toFixed(2)} 秒`, 'INFO', functionName);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueVideoFilename = `${videoIdentifier}_extended_video_${timestamp}.mp4`;
    const outputVideoPath = path.join(SAVE_PATH, uniqueVideoFilename);
    const filterScriptPath = path.join(SAVE_PATH, `${videoIdentifier}_filter_script_${timestamp}.txt`);

    log(`输出视频路径: ${outputVideoPath}`, 'DEBUG', functionName);
    log(`临时滤镜脚本路径: ${filterScriptPath}`, 'DEBUG', functionName);

    // 步骤 1: 定义最纯净、最正确的滤镜链，无需任何转义
    const maxOriginalVideoDuration = audioDuration / VIDEO_CONFIG.repeatCount;
    const textAreaWidth = VIDEO_CONFIG.width;
    const textAreaHeight = Math.round(VIDEO_CONFIG.width * 9 / 16);
    const progressBarWidth = VIDEO_CONFIG.width;
    const progressBarX = (VIDEO_CONFIG.width - progressBarWidth) / 2;
    const originalVideoHeight = Math.round(VIDEO_CONFIG.width * 9 / 16);
    const originalVideoY = (VIDEO_CONFIG.height - originalVideoHeight) / 2;
    const progressBarY = originalVideoY + originalVideoHeight;



     const filterChain = [
        // 步骤1: 把原视频缩放到合适大小
        `[1:v]scale=${VIDEO_CONFIG.width}:-1[scaled_video]`,

        // 步骤2: 把缩放后的原视频叠加到Canvas背景视频上
        `[0:v][scaled_video]overlay=(W-w)/2:(H-h)/2:enable='between(t,0,${maxOriginalVideoDuration})'[with_video]`,

        // 步骤3: 把Canvas生成的进度条视频叠加上去
        `[with_video][3:v]overlay=${progressBarX}:${progressBarY}[with_progress]`,

        // 步骤6: 最后叠加灰色文本区背景
        // 我们先画一个完整的绿色矩形，然后用geq滤镜根据时间“擦除”掉超出进度的部分。
        // [with_progress_bg]drawbox...[with_full_fg]：在背景之上，先画一个填满的、静态的绿色前景。
        // [with_full_fg]geq...[with_progress]：这是核心。geq滤镜逐像素进行计算。
        //   - 'if(gte(X, W*T/TD), R(X,Y), r(X,Y))':...  这是一个条件语句
        //   - X 是当前像素的X坐标，W是视频宽度，T是当前时间，TD是总时长。
        //   - W*T/TD 计算出当前时间点进度条应该有的宽度。
        //   - gte(X, W*T/TD) 判断当前像素的X坐标是否超出了进度条应有的宽度。
        //   - 如果超出(true)，就用R(X,Y)的值，即原始输入像素（深灰色背景）来填充。
        //   - 如果没超出(false)，就用r(X,Y)的值，即第二个输入流（绿色前景）来填充。
        //   - 这个方法绕过了所有动态尺寸参数的问题，直接在像素层面实现了动画。


        // 步骤5: 叠加文本区背景
        // [with_progress] = 输入流，来自步骤4的完整进度条画面
        // [2:v] = 输入3的视频流（文本区背景色块）
        // overlay=(W-w)/2:(H-h)/2 = 居中叠加文本区背景
        // enable='gte(t,${maxOriginalVideoDuration})' = 从22.29秒开始显示文本区，直到视频结束
        // [final_video] = 最终输出流标签，包含完整的视频效果
        // 步骤4: 叠加灰色文本区背景
        `[with_progress][2:v]overlay=(W-w)/2:(H-h)/2:enable='gte(t,${maxOriginalVideoDuration})'[with_text_area]`,

        // 步骤5: 烧录ASS字幕到视频上
        `[with_text_area]subtitles='${assSubtitlePath.replace(/\\/g, '\\\\').replace(/:/g, '\\:')}'[final_video]`
    ];


    
    const filterScriptContent = filterChain.join(';');

    return new Promise(async (resolve, reject) => {
        try {
            // 步骤 2: 将滤镜指令写入临时文件
            await fs.writeFile(filterScriptPath, filterScriptContent);
            log(`成功创建滤镜脚本文件: ${filterScriptPath}`, 'INFO', functionName);

            // 步骤 3: 构建FFmpeg命令，使用 -filter_complex_script
            const ffmpegArgs = [
                // 输入0: Canvas生成的背景视频（报纸+80%黑色遮罩）
                '-i', backgroundVideoPath,
                // 输入1: 原视频
                '-t', maxOriginalVideoDuration.toString(), '-i', ORIGINAL_VIDEO_PATH,
                // 输入2: 文本区
                '-f', 'lavfi', '-i', `color=${VIDEO_CONFIG.textArea.backgroundColor}:size=${textAreaWidth}x${textAreaHeight}:rate=${VIDEO_CONFIG.framerate}:duration=${audioDuration}`,
                // 输入3: 进度条视频
                '-i', progressBarVideoPath,
                // 输入4: 音频
                '-i', audioPath,
                // 核心：从文件读取复杂滤镜
                '-filter_complex_script', filterScriptPath,
                // 映射输出
                '-map', '[final_video]', '-map', '4:a',
                // 编码设置
                '-c:v', VIDEO_CONFIG.codec, '-preset', VIDEO_CONFIG.preset, '-crf', VIDEO_CONFIG.crf.toString(), '-r', VIDEO_CONFIG.framerate.toString(),
                '-c:a', 'aac', '-b:a', '192k',
                '-y', outputVideoPath
            ];

            log(`FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`, 'DEBUG', functionName);
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
            
            let ffmpegError = '';
            ffmpegProcess.stderr.on('data', (data) => {
                const output = data.toString();
                ffmpegError += output;
                if (output.includes('time=')) {
                    const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                    if (timeMatch) {
                        log(`FFmpeg进度: ${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`, 'INFO', functionName);
                    }
                }
            });

            ffmpegProcess.on('close', async (code) => {
                // 步骤 4: 无论成功或失败，都清理临时文件
                try {
                    await fs.unlink(filterScriptPath);
                    log(`已删除临时滤镜脚本: ${filterScriptPath}`, 'INFO', functionName);
                } catch (e) {
                    log(`删除临时滤镜脚本失败: ${e.message}`, 'WARN', functionName);
                }

                if (code === 0) {
                    log('FFmpeg执行成功', 'INFO', functionName);
                    resolve(outputVideoPath);
                } else {
                    const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                    log(errorMsg, 'ERROR', functionName);
                    log(`FFmpeg错误输出: ${ffmpegError.slice(-1000)}`, 'ERROR', functionName);
                    reject(new Error(errorMsg));
                }
            });

            ffmpegProcess.on('error', (error) => {
                const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                log(errorMsg, 'ERROR', functionName);
                reject(new Error(errorMsg));
            });

        } catch (error) {
            log(`生成视频失败: ${error.message}`, 'ERROR', functionName);
            // 确保如果文件创建失败，也尝试删除
            try { await fs.unlink(filterScriptPath); } catch (e) {}
            reject(error);
        }
    });
}





/**
 * @功能概述: 主执行函数，协调整个音频重复拼接和9:16视频生成测试流程
 * @执行流程:
 *   1. 检查原音频文件存在性
 *   2. 验证输出保存路径可用性
 *   3. 执行FFmpeg音频重复拼接操作
 *   4. 获取拼接后音频的时长信息
 *   5. 生成9:16比例的纯白背景视频
 *   6. 验证最终输出结果
 * @错误处理:
 *   - 任何步骤失败都会终止整个流程
 *   - 记录详细错误信息并以错误码退出
 * @测试结果:
 *   - 成功时生成重复拼接的MP3音频文件和9:16纯白背景视频文件
 *   - 失败时输出详细错误诊断信息
 */
async function main() {
    const functionName = 'main';

    console.log('main函数开始执行...'); // 控制台直接输出，便于脚本监控

    try {
        log('========== 开始音频重复拼接和9:16视频生成测试 ==========', 'INFO', functionName); // 记录测试开始

        // 步骤 1: 检查原音频文件
        log('步骤1: 检查原音频文件', 'INFO', functionName); // 记录步骤1开始
        const audioExists = await checkOriginalAudio();
        if (!audioExists) {
            throw new Error('原音频文件不存在，无法继续'); // 抛出音频文件不存在错误
        }

        // 步骤 2: 检查原始视频文件
        log('步骤2: 检查原始视频文件', 'INFO', functionName); // 记录步骤2开始
        const videoExists = await checkOriginalVideo();
        if (!videoExists) {
            throw new Error('原始视频文件不存在，无法继续'); // 抛出视频文件不存在错误
        }

        // 步骤 3: 检查保存路径
        log('步骤3: 检查保存路径', 'INFO', functionName); // 记录步骤3开始
        const savePathExists = await checkSavePath();
        if (!savePathExists) {
            throw new Error('保存路径不存在，无法继续'); // 抛出保存路径不存在错误
        }

        // 步骤 4: 执行音频重复拼接
        log('步骤4: 执行音频重复拼接', 'INFO', functionName); // 记录步骤4开始
        log(`配置参数 - 重复次数: ${VIDEO_CONFIG.repeatCount}, 输出音频文件名: ${OUTPUT_AUDIO_FILENAME}`, 'INFO', functionName); // 记录配置参数
        const extendedAudioPath = await repeatAudioConcat();

        // 步骤 5: 获取拼接后音频的时长
        log('步骤5: 获取拼接后音频时长', 'INFO', functionName); // 记录步骤5开始
        const audioDuration = await getAudioDuration(extendedAudioPath);
        log(`拼接后音频时长: ${audioDuration.toFixed(2)} 秒`, 'INFO', functionName); // 记录音频时长

        // 步骤 6: 生成进度条视频
        log('步骤6: 生成进度条视频', 'INFO', functionName); // 记录步骤6开始
        const originalAudioDuration = audioDuration / VIDEO_CONFIG.repeatCount; // 计算原音频时长
        const progressBarVideoPath = await generateProgressBarVideo(originalAudioDuration);

        // 步骤 7: 重复进度条视频
        log('步骤7: 重复进度条视频', 'INFO', functionName); // 记录步骤7开始
        const repeatedProgressBarVideoPath = await repeatProgressBarVideo(progressBarVideoPath, audioDuration);

        // 步骤 8: 生成背景视频（报纸+80%黑色遮罩）
        log('步骤8: 生成背景视频（报纸+80%黑色遮罩）', 'INFO', functionName); // 记录步骤8开始
        const backgroundVideoPath = await generateBackgroundVideo(audioDuration);

        // 步骤 9: 执行generate-aas.test.js生成最新ASS字幕文件
        log('步骤9: 执行generate-aas.test.js生成最新ASS字幕文件', 'INFO', functionName); // 记录步骤9开始
        await executeGenerateAssScript();

        // 步骤 10: 解析和验证ASS字幕文件
        log('步骤10: 解析和验证ASS字幕文件', 'INFO', functionName); // 记录步骤10开始
        const validatedAssPath = await parseAndValidateAssSubtitle(ASS_SUBTITLE_PATH);

        // 步骤 11: 生成9:16视频（含原视频烧录、进度条和ASS字幕）
        log('步骤11: 生成9:16视频（含原视频烧录、进度条和ASS字幕）', 'INFO', functionName); // 记录步骤11开始
        log(`视频配置 - 标识符: ${videoIdentifier}, 分辨率: ${VIDEO_CONFIG.width}x${VIDEO_CONFIG.height}, 背景: Canvas生成`, 'INFO', functionName); // 记录视频配置
        log(`原视频烧录 - 路径: ${ORIGINAL_VIDEO_PATH}`, 'INFO', functionName); // 记录原视频路径
        log(`进度条视频 - 路径: ${repeatedProgressBarVideoPath}`, 'INFO', functionName); // 记录进度条视频路径
        log(`背景视频 - 路径: ${backgroundVideoPath}`, 'INFO', functionName); // 记录背景视频路径
        log(`ASS字幕文件 - 路径: ${validatedAssPath}`, 'INFO', functionName); // 记录ASS字幕路径
        const outputVideoPath = await generateVideo(audioDuration, extendedAudioPath, repeatedProgressBarVideoPath, backgroundVideoPath, validatedAssPath);

        log('========== 音频重复拼接和9:16视频生成测试完成 ==========', 'INFO', functionName); // 记录测试完成
        log(`✅ 成功生成重复拼接音频: ${extendedAudioPath}`, 'INFO', functionName); // 记录音频成功结果
        log(`✅ 成功生成9:16视频（含原视频烧录和ASS字幕）: ${outputVideoPath}`, 'INFO', functionName); // 记录视频成功结果
        log(`✅ 成功烧录ASS字幕: ${validatedAssPath}`, 'INFO', functionName); // 记录ASS字幕烧录结果
        log(`📊 视频参数总结:`, 'INFO', functionName); // 记录参数总结
        log(`   - 视频标识符: ${videoIdentifier}`, 'INFO', functionName);
        log(`   - 分辨率: ${VIDEO_CONFIG.width}x${VIDEO_CONFIG.height} (9:16比例)`, 'INFO', functionName);
        log(`   - 总时长: ${audioDuration.toFixed(2)} 秒`, 'INFO', functionName);
        log(`   - 原视频时长: ${(audioDuration / VIDEO_CONFIG.repeatCount).toFixed(2)} 秒`, 'INFO', functionName);
        log(`   - 文本区时长: ${(audioDuration - audioDuration / VIDEO_CONFIG.repeatCount).toFixed(2)} 秒`, 'INFO', functionName);
        log(`   - 文本区背景色: ${VIDEO_CONFIG.textArea.backgroundColor}`, 'INFO', functionName);
        log(`   - 进度条位置: 紧贴原视频下方`, 'INFO', functionName);
        log(`   - 进度条高度: ${VIDEO_CONFIG.progressBar.height}px`, 'INFO', functionName);
        log(`   - 进度条宽度: 与原视频等宽 (${VIDEO_CONFIG.width}px)`, 'INFO', functionName);
        log(`   - 进度条背景色: ${VIDEO_CONFIG.progressBar.backgroundColor}`, 'INFO', functionName);
        log(`   - 进度条前景色: ${VIDEO_CONFIG.progressBar.foregroundColor} (与视频播放同步)`, 'INFO', functionName);
        log(`   - 帧率: ${VIDEO_CONFIG.framerate}fps`, 'INFO', functionName);
        log(`   - 音频文件: ${OUTPUT_AUDIO_FILENAME}`, 'INFO', functionName);
        log(`   - 视频文件: ${OUTPUT_VIDEO_FILENAME}`, 'INFO', functionName);
        log(`   - 原视频源: ${ORIGINAL_VIDEO_PATH}`, 'INFO', functionName);

        console.log('测试完成，准备退出...'); // 控制台输出
        process.exit(0); // 成功退出

    } catch (error) {
        log(`❌ 音频重复拼接和9:16视频生成测试失败: ${error.message}`, 'ERROR', functionName); // 记录测试失败
        console.error('错误详情:', error); // 控制台输出错误详情
        process.exit(1); // 错误退出
    }
}

/**
 * @脚本启动: 立即执行主函数并处理未捕获异常
 * @错误处理:
 *   - 捕获Promise拒绝和未处理异常
 *   - 确保脚本在任何情况下都能正确退出
 * @日志记录: 在控制台和日志系统中同时记录关键信息
 */

// 立即执行主函数
console.log('开始执行音频重复拼接和9:16视频生成测试脚本...'); // 控制台输出脚本启动信息
log('音频重复拼接和9:16视频生成测试脚本启动', 'INFO', '脚本启动'); // 日志记录脚本启动

main().catch(error => {
    console.error('脚本执行失败:', error); // 控制台输出错误
    log(`脚本执行失败: ${error.message}`, 'ERROR', '脚本启动'); // 日志记录错误
    process.exit(1); // 错误退出
});
