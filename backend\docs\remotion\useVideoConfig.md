# useVideoConfig()

## 概述

`useVideoConfig()` 是 Remotion 的核心 Hook，用于获取当前组合的配置信息，包括尺寸、帧率、持续时间等重要属性。

## 语法

```typescript
import { useVideoConfig } from "remotion";

const { width, height, fps, durationInFrames, id, defaultProps, props, defaultCodec } = useVideoConfig();
```

## 返回值

返回一个包含以下属性的对象：

### width
- **类型**: `number`
- **描述**: 组合的宽度（像素）
- **特殊情况**: 如果组件是 `<Sequence>` 的子组件且该序列定义了宽度，则返回序列的宽度

### height
- **类型**: `number`
- **描述**: 组合的高度（像素）
- **特殊情况**: 如果组件是 `<Sequence>` 的子组件且该序列定义了高度，则返回序列的高度

### fps
- **类型**: `number`
- **描述**: 组合的帧率（每秒帧数）

### durationInFrames
- **类型**: `number`
- **描述**: 组合的持续时间（帧数）
- **特殊情况**: 如果组件是 `<Sequence>` 的子组件且该序列定义了持续时间，则返回序列的持续时间

### id
- **类型**: `string`
- **描述**: 组合的 ID，与 `<Composition>` 组件的 `id` 属性相同

### defaultProps
- **类型**: `object`
- **描述**: 在组合中定义的默认属性对象

### props
- **类型**: `object`
- **版本**: v4.0.0+
- **描述**: 传递给组合的属性，经过所有转换后的最终值

### defaultCodec
- **类型**: `string`
- **版本**: v4.0.54+
- **描述**: 用于渲染此组合的默认编解码器

## 基础示例

### 1. 获取基本配置信息

```typescript
import React from "react";
import { useVideoConfig } from "remotion";

export const MyComponent: React.FC = () => {
  const { width, height, fps, durationInFrames } = useVideoConfig();
  
  console.log(width);           // 1920
  console.log(height);          // 1080
  console.log(fps);             // 30
  console.log(durationInFrames); // 300
  
  return (
    <div>
      视频尺寸: {width} x {height}
      <br />
      帧率: {fps} FPS
      <br />
      总帧数: {durationInFrames}
    </div>
  );
};
```

### 2. 响应式布局

```typescript
import { useVideoConfig } from "remotion";

const ResponsiveComponent = () => {
  const { width, height } = useVideoConfig();
  
  // 根据视频尺寸调整布局
  const isVertical = height > width;
  const fontSize = Math.min(width, height) / 20;
  
  return (
    <div style={{
      display: 'flex',
      flexDirection: isVertical ? 'column' : 'row',
      fontSize,
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <div>响应式内容</div>
      <div>格式: {isVertical ? '竖屏' : '横屏'}</div>
    </div>
  );
};
```

### 3. 基于时长的动画

```typescript
import { useVideoConfig, useCurrentFrame, interpolate } from "remotion";

const TimingBasedAnimation = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  
  // 基于总时长创建动画
  const progress = interpolate(frame, [0, durationInFrames], [0, 1]);
  const rotation = interpolate(frame, [0, durationInFrames], [0, 360]);
  
  return (
    <div style={{
      transform: `rotate(${rotation}deg)`,
      opacity: progress,
      width: 100,
      height: 100,
      backgroundColor: 'blue'
    }}>
      基于时长的动画
    </div>
  );
};
```

## 实际应用场景

### 1. 动态字体大小

```typescript
const DynamicFontSize = () => {
  const { width, height } = useVideoConfig();
  
  // 根据视频尺寸计算合适的字体大小
  const baseFontSize = Math.min(width, height) / 30;
  
  return (
    <div style={{
      fontSize: baseFontSize,
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      padding: baseFontSize / 2
    }}>
      自适应字体大小的文本
    </div>
  );
};
```

### 2. 居中定位

```typescript
const CenteredElement = () => {
  const { width, height } = useVideoConfig();
  
  return (
    <div style={{
      position: 'absolute',
      left: width / 2 - 50,  // 元素宽度的一半
      top: height / 2 - 25,  // 元素高度的一半
      width: 100,
      height: 50,
      backgroundColor: 'red',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white'
    }}>
      居中元素
    </div>
  );
};
```

### 3. 进度条

```typescript
const ProgressBar = () => {
  const frame = useCurrentFrame();
  const { width, durationInFrames } = useVideoConfig();
  
  const progress = frame / durationInFrames;
  const progressWidth = progress * (width - 40); // 留出边距
  
  return (
    <div style={{
      position: 'absolute',
      bottom: 20,
      left: 20,
      right: 20,
      height: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      borderRadius: 2
    }}>
      <div style={{
        width: progressWidth,
        height: '100%',
        backgroundColor: '#ff6b6b',
        borderRadius: 2,
        transition: 'width 0.1s ease'
      }} />
    </div>
  );
};
```

### 4. 格式检测和适配

```typescript
const FormatAdaptiveComponent = () => {
  const { width, height, id } = useVideoConfig();
  
  // 检测视频格式
  const aspectRatio = width / height;
  let format: string;
  
  if (Math.abs(aspectRatio - 16/9) < 0.1) {
    format = '16:9 横屏';
  } else if (Math.abs(aspectRatio - 9/16) < 0.1) {
    format = '9:16 竖屏';
  } else if (Math.abs(aspectRatio - 1) < 0.1) {
    format = '1:1 方形';
  } else {
    format = '自定义比例';
  }
  
  return (
    <div style={{
      padding: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      position: 'absolute',
      top: 20,
      left: 20,
      borderRadius: 8
    }}>
      <div>组合ID: {id}</div>
      <div>尺寸: {width} x {height}</div>
      <div>格式: {format}</div>
      <div>宽高比: {aspectRatio.toFixed(2)}</div>
    </div>
  );
};
```

### 5. 使用默认属性

```typescript
interface MyProps {
  title: string;
  color: string;
}

const PropsBasedComponent: React.FC<MyProps> = () => {
  const { defaultProps, props } = useVideoConfig();
  
  // 使用传入的属性或默认属性
  const title = props.title || defaultProps.title || '默认标题';
  const color = props.color || defaultProps.color || '#000000';
  
  return (
    <div style={{ color, fontSize: 48, textAlign: 'center' }}>
      {title}
    </div>
  );
};
```

### 6. FPS 相关计算

```typescript
const FPSBasedAnimation = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 计算当前时间（秒）
  const timeInSeconds = frame / fps;
  
  // 每秒旋转 180 度
  const rotation = timeInSeconds * 180;
  
  return (
    <div style={{
      transform: `rotate(${rotation}deg)`,
      width: 100,
      height: 100,
      backgroundColor: 'green',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white'
    }}>
      {timeInSeconds.toFixed(1)}s
    </div>
  );
};
```

## 在序列中的行为

当组件是 `<Sequence>` 的子组件时，某些属性可能会被序列覆盖：

```typescript
import { Sequence, useVideoConfig } from "remotion";

const SequenceChild = () => {
  const { width, height, durationInFrames } = useVideoConfig();
  // 这些值可能来自父序列而不是根组合
  
  return <div>序列子组件</div>;
};

const ParentComponent = () => {
  return (
    <Sequence 
      from={30} 
      durationInFrames={60}
      width={800}
      height={600}
    >
      <SequenceChild />
    </Sequence>
  );
};
```

## 最佳实践

### 1. 缓存计算结果

```typescript
import { useMemo } from "react";

const OptimizedComponent = () => {
  const config = useVideoConfig();
  
  // 缓存基于配置的计算
  const layoutValues = useMemo(() => ({
    centerX: config.width / 2,
    centerY: config.height / 2,
    fontSize: Math.min(config.width, config.height) / 20,
    isVertical: config.height > config.width
  }), [config.width, config.height]);
  
  return (
    <div style={{
      position: 'absolute',
      left: layoutValues.centerX - 50,
      top: layoutValues.centerY - 25,
      fontSize: layoutValues.fontSize
    }}>
      优化的组件
    </div>
  );
};
```

### 2. 类型安全

```typescript
interface VideoConfig {
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
  id: string;
  defaultProps: any;
  props: any;
  defaultCodec: string;
}

const TypeSafeComponent = () => {
  const config: VideoConfig = useVideoConfig();
  
  return <div>类型安全的组件</div>;
};
```

## 相关 API

- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`<Composition>`](./Composition.md) - 定义组合
- [`<Sequence>`](./Sequence.md) - 时间序列
- [`interpolate()`](./interpolate.md) - 值插值

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/use-video-config.ts)
