# 代码注释标准


目标: 建立符合 JSDoc 规范的注释体系，通过分层注释策略提升代码可读性、可维护性和文档自动化生成能力。

!!! 严格执行: AI 在编写/修改代码时必须遵循以下标准：
1. JSDoc 合规性: 所有块注释必须使用标准 JSDoc 语法 (`/** ... */`)，包含以下元数据标签：
   - 方法级：@功能概述（如 convertToAudioTask.js#L109 的"视频转音频任务执行方法"）@param @returns @throws @执行流程（需标注具体函数）@进度监控 @错误处理
   - 类级：@功能概述（如 ConvertToAudioTask 类的"视频转音频任务类"）@继承关系 @进度阶段（含7个阶段说明）@技术参数（含音频编码/比特率等参数）
   - 文件级：@功能概述（如 videoController.js 的"视频上传控制器"）@核心能力 @输入依赖 @输出结果

2. 分层注释策略:
   - 顶层注释: 文件头部需包含 @file 描述（如 convertToAudioTask.js 的模块初始化说明）和 @module 声明
   - 类注释: 必须包含 @class（如 ConvertToAudioTask）@extends（如 TaskBase）@进度阶段（含7个阶段百分比）@技术参数（如 libmp3lame 编码器）
   - 方法注释: 必须包含 @功能概述（如 execute 方法的"视频转音频任务执行方法"）@param（含 context 结构说明）@returns（标准化结果对象）@throws（含错误码）@执行流程（需标注具体函数，如示例中的 validateRequiredFields 和 generateSafeFileName）

3. 内容要求:
   - 使用中文技术术语描述设计意图（如 convertToAudioTask.js#L57 的"模块初始化：配置FFmpeg和FFprobe的可执行文件路径"）
   - 执行流程需标注具体函数调用（如示例中的"1. [参数校验] 验证必需参数 (validateRequiredFields)"到"6. [结果处理] 校验输出文件并更新状态"）
   - 依赖说明需具体到变量/配置项（如示例中的 context.uploadedVideoPath 和 config.ffmpegPath）
   - 设计决策需包含取舍原因（如 videoController.js#L202 文件命名规则中"防止客户端可能包含特殊字符"的说明）

4. 上下文关联增强:
   - 使用 @see 链接具体代码位置（如 @see convertToAudioTask.js#L109 方法注释中的7个执行阶段）
   - @todo 需标注预期实现版本（如 generateSafeFileName 函数可添加 @todo v2.1 增加文件名碰撞检测）
   - @deprecated 必须注明替代方案和迁移指南（如 ffmpeg.setFfmpegPath 若弃用需说明替代方案）



## 示例


```javascript

    /**
     * @功能概述: 视频转音频任务执行方法，实现从视频文件提取音频、文件路径管理、转换过程监控等核心逻辑
     * @param {object} context - 任务上下文对象，包含以下关键参数：
     *                           - uploadedVideoPath: {string} - 通过multer中间件保存的原始视频文件绝对路径
     *                           - videoIdentifier: {string} - 视频唯一标识符（格式：videoFile-时间戳-随机字符串）
     *                           - originalVideoName: {string} [可选] - 原始视频文件名（仅用于日志记录）
     *                           - reqId: {string} [可选] - 请求追踪ID，用于跨系统日志关联
     * @param {function} progressCallback - 标准化进度回调函数，接收包含以下字段的对象：
     *                                     - taskName: 当前任务名称
     *                                     - status: 状态码（参考 TASK_STATUS 枚举）
     *                                     - subStatus: 子状态码（参考 TASK_SUBSTATUS 枚举）
     *                                     - detail: 人类可读的进度描述
     * 
     * @returns {Promise<object>} 标准化结果对象，包含：
     *                           - originalVideoPathInUploads: {string} 原始视频在uploads目录的规范路径
     *                           - audioFilePathInUploads: {string} 生成的MP3文件绝对路径
     * 
     * @throws {Error} 在以下情况抛出标准化错误：
     *                 - 参数校验失败（错误码：MISSING_REQUIRED_FIELD）
     *                 - 文件操作异常（错误码：FILE_OPERATION_ERROR）
     *                 - FFmpeg进程异常（错误码：FFMPEG_PROCESS_ERROR）
     * 
     * @执行流程:
     *   1. [参数校验] 验证必需参数 (validateRequiredFields)
     *   2. [目录准备] 创建/校验uploads工作目录 (fs.existsSync + fs.mkdirSync)
     *   3. [路径构建] 生成标准化文件名 (generateSafeFileName)
     *   4. [转换配置] 初始化FFmpeg参数 (ffmpeg.setFfmpegPath)
     *   5. [转换执行] 执行音频提取 (executeFfmpegConversion)
     *   6. [结果处理] 校验输出文件并更新状态 (handleConversionResult)
     * 
     * @进度监控:
     *   - 通过继承自 TaskBase 的 reportProgress 方法，按预定义的7个阶段报告进度
     *   - 每个阶段包含 RUNNING 主状态和 INITIALIZING/PROCESSING 等子状态
     * 
     * @错误处理:
     *   - 使用 TaskBase 标准化的 fail 方法处理错误
     *   - 自动捕获异常并生成包含错误码的标准化错误对象
     * 
     * @技术参数: 继承自类定义的技术参数（@see ConvertToAudioTask 类注释）
     * @see {@link ConvertToAudioTask} 类注释查看音频编码参数细节
     */

```



## 注释优先级策略

### 必须注释 (严格执行)
- 所有公开API接口和控制器函数
- 所有流水线类 (继承自 `PipelineBase`)
- 所有任务类 (继承自 `TaskBase`)
- 复杂的业务逻辑函数 (超过20行或包含3个以上分支)
- 关键的错误处理逻辑
- 与外部服务交互的函数 (LLM API、数据库等)

### 推荐注释 (重要功能)
- 中间件函数和配置初始化
- 工具函数和辅助方法
- 数据处理和转换逻辑
- 文件操作和路径处理

### 可选注释 (简单逻辑)
- 简单的getter/setter方法
- 一目了然的CRUD操作
- 标准的Express路由定义

## 注释类型

主要使用两种类型的注释：块注释和行内注释。

### 1. 块注释 (Block Comments)

用于注释整个文件、类、函数、方法或复杂的代码块。

#### 基础结构要求

*   位置: 紧邻在被注释代码块的正上方。
*   语法: 使用目标语言的标准块注释语法 (JavaScript/TypeScript: `/** ... */`)。
*   格式: 使用 JSDoc 风格，星号对齐，内部保持统一缩进。

#### 通用函数和方法注释模板

```javascript
/**
 * @功能概述: [1-3句话描述功能、目的和核心逻辑]
 * @param {类型} 参数名 - [参数用途和约束]
 * @returns {类型} [返回值描述或系统影响]
 * @执行流程: [可选，列出关键处理步骤]
 */
```

#### 项目特定注释模板

**控制器函数模板:**
```javascript
/**
 * @功能概述: [API功能描述]
 * @API路径: [HTTP方法] [路径]
 * @请求参数: [关键参数说明]
 * @响应格式: [成功/失败响应结构]
 * @进度推送: [如果使用SSE，说明事件类型]
 */
```

**流水线服务模板:**
```javascript
/**
 * @功能概述: [流水线整体目标和处理流程]
 * @任务序列: [Task1 → Task2 → Task3...]
 * @预期时长: [大概处理时间]
 * @进度回调: [SSE事件推送说明]
 */
```

**任务类模板:**
```javascript
/**
 * @功能概述: [任务具体功能和处理逻辑]
 * @输入依赖: context需包含[字段列表]
 * @输出结果: 向context添加[字段列表]
 * @外部依赖: [如FFmpeg、API服务等]
 * @失败策略: [错误处理方式]
 */
```

**LLM交互函数模板:**
```javascript
/**
 * @功能概述: [LLM交互的具体用途]
 * @提示词模板: [使用的模板文件路径]
 * @LLM参数: [temperature、max_tokens等关键参数]
 * @错误处理: [LLM调用失败时的处理策略]
 * @输出格式: [期望的LLM响应格式]
 */
```

#### 复杂代码段注释

对于函数内部的复杂逻辑段，使用分步说明：

```javascript
/**
 * @分步说明: [复杂逻辑段的总体描述]
 * 
 *   1. [主要步骤描述]
 *       1.1. [具体子步骤]
 *       1.2. [具体子步骤]
 *   2. [主要步骤描述]
 *       2.1. [具体子步骤]
 */
// --- 复杂代码段开始 ---
// 步骤 1.1: [对应具体实现]
// 步骤 1.2: [对应具体实现]
// --- 复杂代码段结束 ---
```

### 2. 行内注释 (Inline Comments)

用于解释单行或小段代码的具体逻辑。

#### 使用场景
*   解释复杂的表达式、算法或非显而易见的逻辑
*   说明特定选择或"魔法值"的原因
*   标记临时解决方案 (`TODO`、`FIXME`)
*   解释关键变量的含义
*   异步操作的流程说明
*   **函数内部步骤标注**: 使用 `// 步骤 X:` 格式标记主要逻辑步骤
*   **日志语句说明**: 对所有 `logger.xxx()` 语句添加行内注释

#### 行内注释示例

```javascript
// 步骤 1: 验证上传文件的格式和大小
const allowedTypes = ['mp4', 'avi', 'mov']; // 支持的视频格式列表
if (!allowedTypes.includes(fileExtension)) {
    logger.warn(`${logPrefix}[步骤 1.1][WARN] 不支持的文件格式: ${fileExtension}`); // 记录不支持格式的警告
    return res.status(400).json({ error: 'Unsupported file format' }); // 返回格式错误响应
}

// 步骤 2: 初始化流水线处理实例
const pipeline = new VideoProcessingPipeline(reqId); // 创建视频处理流水线
logger.info(`${logPrefix}[步骤 2] 流水线实例创建成功`); // 记录流水线创建成功

// 等待异步文件转换完成
const audioFile = await convertVideoToAudio(videoPath); // FFmpeg转换操作
```

## 通用原则

*   **清晰胜于冗余**: 优先编写清晰、自解释的代码，注释作为辅助
*   **关注"为什么"**: 注释不仅说明代码"做什么"，更要解释"为什么"这么做
*   **保持更新**: 修改代码时，务必同步更新相关注释
*   **分层应用**: 根据注释优先级策略，合理分配注释详细程度
*   **模板化**: 使用项目提供的注释模板，保持一致性

## 调试与追踪日志 (Debugging and Tracing Logs)

**注意：以下日志规范保持原有标准不变**

除了上述规范化的代码块注释和行内注释外，在开发和调试过程中，使用详细的追踪日志至关重要。这有助于理解代码的实际执行流程、诊断问题和监控关键操作。请务必使用中文记录日志。

*   目标:
    *   清晰展示代码执行的关键步骤和分支。
    *   记录重要的数据状态（输入、中间处理、输出）。
    *   在出现问题时，能够快速定位错误来源和上下文。
*   主要工具: 使用导入的日志库实例 (`logger`) 的方法，例如 `logger.debug()`, `logger.info()`, `logger.warn()`, `logger.error()`。
    ```javascript
    // 确保在文件顶部导入了 logger 实例
    const logger = require('./utils/logger'); // 根据实际文件路径调整
    ```
*   日志位置: 按照配置（例如使用 Winston），日志通常记录到指定文件 (`doc/logs/app.log`) 或控制台。

### 日志结构化最佳实践

1.  统一模块/处理器前缀 (Consistent Module/Handler Prefix):
    *   为每个主要模块、类或处理器函数定义一个独特且一致的日志前缀，必须严格遵循以下格式：
    *   规范格式: `[文件：文件名.js][文件名中文翻译][函数名][ReqID:${req.id || 'unknown'}]`
    *   格式说明:
        *   `[文件：文件名.js]`: 当前文件的名称，包含扩展名
        *   `[文件名中文翻译]`: 文件名的中文含义，如 `videoController.js` 翻译为 `视频控制器`
        *   `[函数名]`: 当前执行的函数名，如 `uploadVideo`
        *   `[ReqID:${req.id || 'unknown'}]`: 请求ID，用于追踪完整的请求生命周期
    *   示例:
        ```javascript
        // 在 videoController.js 文件中，uploadVideo 函数内定义日志前缀
        const logPrefix = `[文件：videoController.js][视频控制器][uploadVideo][ReqID:${req.id || 'unknown'}] `;
        
        // 在 uploadMiddleware.js 文件中，destination 函数内定义日志前缀
        const logPrefix = `[文件：uploadMiddleware.js][上传中间件][storage.destination][ReqID:${req.id || 'unknown'}] `;
        
        // 对于模块级别的日志（函数外部），可使用模块初始化作为函数名部分
        const moduleLogPrefix = `[文件：videoProcessingService.js][视频处理服务][模块初始化]`;
        ```
    *   目的: 确保所有日志具有统一且清晰的标识，便于在大量日志中筛选和识别特定模块的输出，同时提供文件和函数的准确上下文信息。

2.  分步编号与清晰描述 (Step Numbering and Clear Descriptions):
    *   对一个复杂流程中的关键执行步骤使用层级化编号。
    *   重要：日志中的步骤编号应与代码注释中（无论是函数/方法级别的 `@执行流程`，还是内部复杂代码块的 `@分步说明` 或详细的行内注释，特别是指导性的 `// 步骤 X:` 注释）对应的步骤编号保持一致或清晰关联。这确保了通过阅读日志可以快速定位到代码中相应的解释和逻辑。
    *   示例:
        ```javascript
        // 步骤 1: 校验用户输入参数的有效性...
        logger.info(`${logPrefix}[步骤 1] 开始处理请求。`);
        // ...
        // 步骤 2: 根据 itemId 从数据库或其他服务异步查询核心业务数据。
        logger.info(`${logPrefix}[步骤 2] 验证输入数据。`);
        logger.debug(`${logPrefix}[步骤 2.1] 检查字段 'name'。`);
        // ...
        logger.warn(`${logPrefix}[步骤 2.5.1] 数据验证失败：...`);
        ```
    *   目的: 清晰展示执行路径，尤其在有多个分支或条件判断时，能快速看出代码走了哪个分支，以及在哪里中断。

3.  记录关键数据状态 (Logging Key Data States):
    *   输入参数: 记录从请求或函数参数中获取的核心数据及其类型。
        ```javascript
        // 假设使用 Express，参数可能在 req.params, req.query, req.body
        const userId = req.params.userId;
        logger.info(`${logPrefix}[步骤 1.1] 接收到的用户ID: ${userId}`); // 日志：记录接收到的用户ID。
        const requestBody = req.body;
        // 对对象或数组使用 JSON.stringify 记录
        logger.debug(`${logPrefix}[步骤 1.2] 请求体内容: ${JSON.stringify(requestBody)}`); // 日志：记录请求体的完整内容（调试级别）。
        ```
    *   重要变量: 记录影响逻辑走向或重要的中间计算结果。
    *   复杂数据结构: 使用 `JSON.stringify()` 来记录数组或对象的结构和内容，并考虑使用日志库提供的对象日志功能（如果支持）。
        ```javascript
        const processedResult = { success: true, data: { id: 123, status: 'completed' } };
        // 直接记录对象，日志库可能会自动序列化
        logger.debug(`${logPrefix}[步骤 4.1] 处理结果对象: ${JSON.stringify(processedResult)}`); // 日志：记录处理结果对象的完整内容（调试级别）。
        // 记录数组示例
        const dataArray = ['item1', 'item2'];
        logger.debug(`${logPrefix}[步骤 4.2] 数据数组: ${JSON.stringify(dataArray)}`); // 日志：记录数据数组的内容（调试级别）。
        ```
    *   目的: 验证数据在处理过程中的正确性，以及在出错时了解当时的数据状态。

4.  上下文信息 (Contextual Information):
    *   在日志中包含关键的上下文标识，如请求 ID、用户 ID、任务 ID、订单号等。通常作为日志前缀的一部分。
    *   示例: `logger.info(`${logPrefix}[步骤 3] 用户ID: ${userId} 尝试更新配置。配置项: ${configKey}`);` // 日志：记录用户尝试更新配置的操作及相关上下文。
    *   目的: 能够将单条日志与特定的用户请求、会话或业务实体关联起来。

5.  明确记录操作结果 (Logging Operation Outcomes):
    *   成功标记: 清晰记录关键操作的成功完成，通常使用 `info` 或 `debug` 级别。
        ```javascript
        logger.info(`${logPrefix}[步骤 6.1][SUCCESS] 数据保存成功。记录ID: ${newRecordId}`); // 日志：明确记录数据保存操作成功及其生成的记录ID。
        ```
    *   失败/错误标记: 记录操作失败、参数验证不通过或任何不符合预期的情况，使用 `warn` 或 `error` 级别。
        ```javascript
        logger.warn(`${logPrefix}[步骤 2.3][WARN] 参数验证失败：字段 'email' 格式错误。`); // 日志：记录参数验证失败的具体字段和原因（警告级别）。
        logger.error(`${logPrefix}[步骤 5.1][ERROR] 数据库插入失败：${dbError.message}`); // 日志：记录数据库插入操作失败的具体错误信息（错误级别）。
        ```
    *   异常捕获: 在 `try-catch` 块中，务必使用 `error` 级别记录捕获到的异常的详细信息，包括异常消息和堆栈跟踪。
        ```javascript
        try {
            // ... 业务逻辑 ...
        } catch (error) {
            // 记录错误信息和堆栈跟踪
            logger.error(`${logPrefix}[UNEXPECTED_EXCEPTION] 发生未预期错误: ${error.message}
Stack: ${error.stack}`); // 日志：记录捕获到的未预期异常的详细消息和堆栈。
            // ... 返回错误响应 ...
        }
        ```
    *   目的: 快速识别操作是否成功，以及失败的具体原因和位置。

6.  日志标记/级别 (Log Markers/Levels):
    *   日志库（如 Winston）直接支持日志级别。使用不同的方法 (`.debug()`, `.info()`, `.warn()`, `.error()`) 来标记日志的重要性。
    *   可以在日志消息内容中添加额外的标记，但主要依靠日志级别进行筛选。
    *   目的: 提高日志的可读性和可筛选性。

### 示例：综合应用场景

#### 控制器函数完整示例

```javascript
/**
 * @功能概述: 处理视频文件上传并启动处理流水线
 * @API路径: POST /api/video/upload
 * @请求参数: multipart/form-data 包含视频文件
 * @响应格式: SSE事件流推送处理进度
 * @进度推送: pipelineProgress事件，包含任务状态和结果
 */
async function uploadVideo(req, res) {
    const reqId = req.id || Date.now().toString(); // 生成请求追踪ID
    const logPrefix = `[文件：videoController.js][视频控制器][uploadVideo][ReqID:${reqId}] `;
    
    logger.info(`${logPrefix}[步骤 1] 开始处理视频上传请求`); // 记录请求开始处理

    try {
        // 步骤 1: 验证上传文件
        if (!req.file) {
            logger.warn(`${logPrefix}[步骤 1.1][WARN] 未找到上传文件`); // 记录文件缺失警告
            return res.status(400).json({ error: 'No file uploaded' });
        }
        
        const videoPath = req.file.path; // 获取上传文件路径
        logger.info(`${logPrefix}[步骤 1.2] 文件上传成功: ${videoPath}`); // 记录文件路径

        // 步骤 2: 初始化流水线处理
        const pipelineService = new VideoProcessingPipelineService(reqId); // 创建流水线服务实例
        logger.debug(`${logPrefix}[步骤 2] 流水线服务实例创建完成`); // 记录实例创建

        // 步骤 3: 设置SSE响应并执行流水线
        setupSSEResponse(res, reqId); // 配置SSE响应头
        const progressCallback = (data) => sendSSEEvent(res, data, logPrefix); // 定义进度回调函数
        
        const result = await pipelineService.processVideo(videoPath, progressCallback); // 执行视频处理流水线
        logger.info(`${logPrefix}[步骤 3][SUCCESS] 视频处理完成`); // 记录处理成功
        
    } catch (error) {
        logger.error(`${logPrefix}[UNEXPECTED_EXCEPTION] 视频处理失败: ${error.message}`); // 记录异常详情
        res.status(500).json({ error: 'Processing failed' });
    }
}
```

#### 任务类完整示例

```javascript
/**
 * @功能概述: 将视频文件转换为音频文件供后续语音识别使用
 * @输入依赖: context需包含videoFilePath字段
 * @输出结果: 向context添加audioFilePath字段
 * @外部依赖: FFmpeg命令行工具
 * @失败策略: 转换失败时抛出详细错误信息
 */
class ConvertToAudioTask extends TaskBase {
    async execute(context, progressCallback) {
        const reqId = context.reqId || 'unknown'; // 获取请求ID
        const logPrefix = `[文件：convertToAudioTask.js][视频转音频任务][execute][ReqID:${reqId}] `;
        
        logger.info(`${logPrefix}[步骤 1] 开始视频转音频任务`); // 记录任务开始
        this.status = 'running'; // 更新任务状态
        
        // 报告任务开始
        if (progressCallback) progressCallback({ 
            taskName: this.name, 
            status: 'started', 
            detail: '开始转换视频为音频格式' 
        });

        try {
            // 步骤 1: 验证输入文件
            const { videoFilePath } = context; // 从上下文获取视频文件路径
            if (!videoFilePath || !fs.existsSync(videoFilePath)) {
                throw new Error(`视频文件不存在: ${videoFilePath}`); // 抛出文件不存在错误
            }
            logger.debug(`${logPrefix}[步骤 1] 输入文件验证通过: ${videoFilePath}`); // 记录文件验证成功

            // 步骤 2: 执行FFmpeg转换
            if (progressCallback) progressCallback({ 
                taskName: this.name, 
                status: 'processing_ffmpeg_started', 
                detail: 'FFmpeg转换进行中' 
            });
            
            const audioFilePath = await this.runFFmpegConversion(videoFilePath, logPrefix); // 执行FFmpeg转换
            logger.info(`${logPrefix}[步骤 2][SUCCESS] 音频转换完成: ${audioFilePath}`); // 记录转换成功

            // 步骤 3: 返回结果
            this.status = 'completed'; // 更新任务状态为完成
            const result = { audioFilePath }; // 构建结果对象
            this.result = result; // 保存任务结果
            
            if (progressCallback) progressCallback({ 
                taskName: this.name, 
                status: 'completed', 
                result: this.result,
                detail: '视频转音频任务完成' 
            });
            
            return result; // 返回结果供流水线使用
            
        } catch (error) {
            logger.error(`${logPrefix}[ERROR] 视频转音频失败: ${error.message}`); // 记录转换失败错误
            this.status = 'failed'; // 更新任务状态为失败
            this.error = error; // 保存错误信息
            
            if (progressCallback) progressCallback({ 
                taskName: this.name, 
                status: 'failed_ffmpeg', 
                error: { message: error.message, name: error.name },
                detail: `视频转音频失败: ${error.message}` 
            });
            
            throw error; // 重新抛出错误终止流水线
        }
    }
}
```

### Node.js/Express.js 代码注释与日志规范总结

*   **注释风格**: 使用 JSDoc 块注释 (`/** ... */`) 和结构化标签，包括项目特定的注释模板
*   **优先级策略**: 根据代码重要性分层应用注释，确保核心功能有完整注释
*   **模板化**: 使用控制器、流水线、任务类等专用注释模板，保持一致性
*   **行内注释**: 对复杂逻辑、异步操作、关键步骤和所有日志语句添加说明
*   **日志记录**: 使用 Winston 日志库，严格遵循现有的日志前缀和步骤编号规范
*   **步骤对应**: 确保代码注释中的步骤编号与日志记录中的步骤编号一致
*   **上下文追踪**: 在日志中记录关键数据状态和上下文信息，便于问题定位

请 AI 严格遵循以上优化后的注释规范，特别是针对流水线架构项目的特定要求，以提高代码库的整体质量和可维护性。

