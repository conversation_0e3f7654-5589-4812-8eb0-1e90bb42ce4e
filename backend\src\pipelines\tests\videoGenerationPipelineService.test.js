/**
 * @功能概述: VideoGenerationPipelineService 的集成测试脚本。
 *           验证重构后的流水线服务是否正确集成ClipMediaTask。
 * @注意事项:
 *   - 测试流水线的任务序列和基本配置
 *   - 验证ClipMediaTask的集成
 *   - 验证PipelineBase架构的正确使用
 */

const VideoGenerationPipelineService = require('../videoGenerationPipelineService');
const logger = require('../../utils/logger');

// 统一的测试日志前缀
const testLogPrefix = '[文件：videoGenerationPipelineService.test.js][流水线集成测试]';

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 VideoGenerationPipelineService 集成测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info('');
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 流水线服务实例化', async () => {
        const service = new VideoGenerationPipelineService('test-req-001');
        assert(service instanceof VideoGenerationPipelineService, '服务应为 VideoGenerationPipelineService 的实例');
        assertEquals(service.reqId, 'test-req-001', '请求ID应正确设置');
        assert(service.processingPipeline, '应包含处理流水线实例');
    });

    await runSingleTest('2. 流水线任务序列验证', async () => {
        const service = new VideoGenerationPipelineService('test-req-002');
        const pipeline = service.processingPipeline;

        // 验证任务数量
        assertEquals(pipeline.tasks.length, 9, '流水线应包含9个任务');

        // 验证任务序列（包含ClipMediaTask + 7个新任务）
        const expectedTaskNames = [
            'ClipMediaTask',
            'ConvertToAudioTask',
            'GetTranscriptionTask',
            'TranscriptionCorrectionTask',
            'TranslateSubtitleTask',
            'SubtitleClozeTask',
            'BilingualSubtitleMergeTask',
            'GenerateASSTask',
            'GenerateVideoTask'
        ];

        for (let i = 0; i < expectedTaskNames.length; i++) {
            const actualTaskName = pipeline.tasks[i].constructor.name;
            assertEquals(actualTaskName, expectedTaskNames[i], `任务 ${i + 1} 应为 ${expectedTaskNames[i]}`);
        }
    });

    await runSingleTest('3. 完整任务集成验证', async () => {
        const service = new VideoGenerationPipelineService('test-req-003');
        const pipeline = service.processingPipeline;

        // 验证ClipMediaTask（第一个任务）
        const clipMediaTask = pipeline.tasks.find(task => task.constructor.name === 'ClipMediaTask');
        assert(clipMediaTask, 'ClipMediaTask应存在于流水线中');
        assertEquals(pipeline.tasks[0].constructor.name, 'ClipMediaTask', 'ClipMediaTask应为流水线的第一个任务');

        // 验证ConvertToAudioTask（第二个任务）
        const convertToAudioTask = pipeline.tasks.find(task => task.constructor.name === 'ConvertToAudioTask');
        assert(convertToAudioTask, 'ConvertToAudioTask应存在于流水线中');
        assertEquals(pipeline.tasks[1].constructor.name, 'ConvertToAudioTask', 'ConvertToAudioTask应为流水线的第二个任务');

        // 验证SubtitleClozeTask（第六个任务）
        const subtitleClozeTask = pipeline.tasks.find(task => task.constructor.name === 'SubtitleClozeTask');
        assert(subtitleClozeTask, 'SubtitleClozeTask应存在于流水线中');
        assertEquals(pipeline.tasks[5].constructor.name, 'SubtitleClozeTask', 'SubtitleClozeTask应为流水线的第六个任务');

        // 验证GenerateASSTask（第八个任务）
        const generateASSTask = pipeline.tasks.find(task => task.constructor.name === 'GenerateASSTask');
        assert(generateASSTask, 'GenerateASSTask应存在于流水线中');
        assertEquals(pipeline.tasks[7].constructor.name, 'GenerateASSTask', 'GenerateASSTask应为流水线的第八个任务');

        // 验证GenerateVideoTask（最后一个任务）
        const generateVideoTask = pipeline.tasks.find(task => task.constructor.name === 'GenerateVideoTask');
        assert(generateVideoTask, 'GenerateVideoTask应存在于流水线中');
        assertEquals(pipeline.tasks[8].constructor.name, 'GenerateVideoTask', 'GenerateVideoTask应为流水线的最后一个任务');
    });

    await runSingleTest('4. 流水线状态管理', async () => {
        const service = new VideoGenerationPipelineService('test-req-004');
        const pipeline = service.processingPipeline;
        
        // 验证初始状态
        assertEquals(pipeline.status, 'pending', '流水线初始状态应为pending');
        assert(pipeline.pipelineId.includes('VideoGeneration'), '流水线ID应包含VideoGeneration');
        assert(pipeline.tasks.length > 0, '流水线应包含任务');
    });

    await runSingleTest('5. 参数验证功能', async () => {
        const service = new VideoGenerationPipelineService('test-req-005');

        // 测试缺少必需参数的情况
        const incompleteContext = {
            // 缺少originalVideoPath和videoIdentifier等必需参数
        };

        const result = await service.processVideoGeneration(incompleteContext, null);
        assertEquals(result.status, 'failed', '缺少必需参数时应返回失败状态');
        assert(result.error, '应包含错误信息');
        assert(result.error.message.includes('originalVideoPath') || result.error.message.includes('videoIdentifier'), '错误信息应提及缺少的参数');
    });

    await runSingleTest('6. 流水线配置验证', async () => {
        const service = new VideoGenerationPipelineService('test-req-006');

        // 验证基本配置
        assert(service.processingPipeline, '应包含处理流水线实例');
        assert(service.processingPipeline.pipelineId.includes('VideoGeneration'), '流水线ID应包含VideoGeneration');

        // 验证日志前缀
        assert(service.logPrefix.includes('test-req-006'), '日志前缀应包含请求ID');
        assert(service.logPrefix.includes('videoGenerationPipelineService.js'), '日志前缀应包含文件名');
    });

    // 测试总结
    logger.info(`${testLogPrefix} ========== VideoGenerationPipelineService 集成测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0);
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
