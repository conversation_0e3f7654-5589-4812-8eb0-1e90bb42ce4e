# ClipMediaTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **originalVideoPath** (string): 原始视频文件的完整路径
- **cropData** (object): 画面裁剪参数对象
  - **cropWidth** (number): 裁剪宽度
  - **cropHeight** (number): 裁剪高度
  - **cropXOffset** (number): 裁剪X偏移量
  - **cropYOffset** (number): 裁剪Y偏移量
- **clipSegments** (Array): 时间片段数组
  - **startTime** (number): 片段开始时间（秒）
  - **endTime** (number): 片段结束时间（秒）
- **savePath** (string): 文件保存路径

### 可选参数
- **reqId** (string): 请求ID，用于日志追踪

## 2. 输出上下文参数 (Output Context)

- **processedVideoPath** (string): 处理后视频文件的完整路径
- **processedVideoFileName** (string): 处理后视频文件名
- **originalVideoPath** (string): 更新后的原始视频路径（指向处理后的视频）
- **originalVideoFileName** (string): 更新后的原始视频文件名
- **cropData** (object): 裁剪参数（原样返回）
- **clipSegments** (Array): 片段信息（原样返回）
- **segmentCount** (number): 处理的片段数量

## 3. 重要数据格式

### clipSegments数组格式
```json
[
  {
    "startTime": 10.5,
    "endTime": 25.8
  },
  {
    "startTime": 45.2,
    "endTime": 60.0
  }
]
```

### cropData对象格式
```json
{
  "cropWidth": 1080,
  "cropHeight": 1920,
  "cropXOffset": 100,
  "cropYOffset": 50
}
```

## 4. 文件操作

### 保存的文件格式
- **.mp4**: 处理后的视频文件（与原视频格式保持一致）

### 文件命名规则
- **片段文件**: `{原文件名}_raw_segment_{序号}_{开始时间}s_{结束时间}s.{扩展名}`
- **合并文件**: `{原文件名}_merged_raw_{片段数量}segments.{扩展名}`
- **最终文件**: `{原文件名}_final_cropped_{宽度}x{高度}_{片段数量}segments.{扩展名}`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 自动创建目录（如果不存在）
- 所有临时文件和最终文件都保存在同一目录

## 5. 执行逻辑概述

媒体剪辑任务采用"先筛选再处理"的优化策略，大幅提升长视频处理效率。首先验证输入参数的完整性，包括视频路径、裁剪参数和时间片段数组。然后直接从原视频中提取指定时间片段，避免对整个视频进行处理。如果有多个片段，使用FFmpeg的filter_complex进行自动合并。最后对合并后的短视频进行画面裁剪处理，生成最终的处理结果。整个流程通过FFmpeg命令行工具实现，支持详细的进度报告和错误处理，确保视频处理的高效性和可靠性。
