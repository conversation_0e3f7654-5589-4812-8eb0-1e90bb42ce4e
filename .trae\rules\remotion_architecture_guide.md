# Remotion 开发规则与架构指南

**版本: 2.0**
**状态: 已生效**

本文档是 `remotion` 集成开发的**唯一**标准，旨在为团队提供一套清晰、统一的架构共识和开发流程。**所有与 Remotion 相关的开发工作都必须严格遵守此文档。**

## 1. 核心架构：渐进式的“微模板”模式

我们的核心架构是一种**渐进式、图层化的“微模板”模式**。此模式旨在支持从旧的、扁平化的 `videoConfig` 配置，平滑过渡到新的、结构化的、面向未来的 `remotionTemplate` 配置体系。

### 1.1. 配置体系

任务将同时支持两种配置输入，但**优先使用新配置**。

1.  **新配置体系 (未来方向)**:
    *   **来源**: `context.remotionTemplate` 对象。
    *   **结构**: 包含一个 `layers` 数组。每个图层对象通过 `type` 和 `template` 字段，**引用** `templates` 目录下的“微模板”JSON 文件。
    *   **角色**: 这是**动态编排指令集**，是未来前端配置界面的主要数据结构。

2.  **旧配置体系 (兼容层)**:
    *   **来源**: `context.videoConfig` 对象。
    *   **结构**: 扁平化的键值对（如 `backgroundStyle`, `repeatCount`）。
    *   **角色**: 作为**向后兼容**的配置层。任务内部会将其“翻译”成一个临时的 `layers` 结构进行处理。**新功能不应再向此对象添加字段。**

### 1.2. 架构图

```mermaid
graph TD
    subgraph "输入层 (Pipeline Context)"
        A["Context"];
        A --> Z{优先使用 remotionTemplate?};
        Z -- "是" --> B["remotionTemplate (含 layers)"];
        Z -- "否 (回退)" --> Y["videoConfig (旧版)"];
    end

    subgraph "编排层 (TSX 生成任务)"
        C["RemotionTSXGeneratorTask"];
        B --> C;
        Y --> C;
        C -- "读取或翻译为 layers" --> D{"遍历图层指令"};
    end

    subgraph "配置层 (Templates - 微模板)"
        E["/templates/backgrounds/newspaper.json"];
        F["/templates/videos/default-muted.json"];
        D -- "指令: {type:'background', template:'newspaper'}" --> E;
        D -- "指令: {type:'video', template:'default-muted'}" --> F;
    end
    
    subgraph "生成与渲染"
        G["生成的 VideoComponent.tsx"];
        E & F --> G;
        G -- "调用 React 组件" --> H["视图层 (Components)"];
        H -- "引用静态资源" --> I["资源层 (Public)"];
    end
```

### 1.3. 文件夹职责 (规则)

`src/remotion/` 目录下的结构必须严格遵守以下职责划分：

| 路径 | 角色 | 职责 (必须遵守) |
| :--- | :--- | :--- |
| `public/` | **资源库** | 存放所有静态资源文件。**必须**按类型分子目录（如 `backgrounds/`, `videos/`）。 |
| `templates/` | **配置库** | 存放**组件级**的 `.json` 配置文件（微模板）。**必须**按组件类型分子目录。 |
| `components/` | **组件库** | 存放可重用的 React 组件。组件**必须**是纯粹的渲染器，所有动态数据通过 `props` 传入。 |
| `generated/` | **生成物** | 由任务自动创建，**严禁手动修改**。 |
| `index.ts` & `Root.tsx` | **入口与根** | **通常无需修改**。`Root.tsx` 只应在独立调试时临时修改。 |

---

## 2. 开发流程：为视频添加一个新的“静音视频”图层

这是为 Remotion 视频添加任何新功能的**标准流程**。

### 步骤 1: 创建/修改视图组件 (`components` 目录)

**目标**: 确保我们有一个 React 组件，其行为可以被 `props` 控制。

1.  **定位文件**: `src/remotion/components/videos/CenteredVideo.tsx`。
2.  **修改组件**: 增加一个 `isMuted` prop，并将其传递给 `<OffthreadVideo>`。

**示例**: `CenteredVideo.tsx`
```tsx
interface CenteredVideoProps {
  src: string;
  isMuted?: boolean; // 新增: 可选的静音属性
}

export const CenteredVideo: React.FC<CenteredVideoProps> = ({ src, isMuted = false }) => {
  return (
    // ...
    <OffthreadVideo src={staticFile(src)} muted={isMuted} />
    // ...
  );
};
```

### 步骤 2: 创建组件的配置文件 (`templates` 目录)

**目标**: 创建一个“微模板”，定义我们想要的具体行为。

1.  **创建文件**: 在 `src/remotion/templates/videos/` 目录下创建一个新文件 `default-muted.json`。
2.  **编写配置**: 在 JSON 中明确指定要使用的组件和要传递的 `props`。

**示例**: `src/remotion/templates/videos/default-muted.json`
```json
{
  "component": "CenteredVideo",
  "props": {
    "isMuted": true
  }
}
```

### 步骤 3: 在流水线上下文中编排图层 (新标准)

**目标**: 使用**新的 `remotionTemplate` 配置体系**来指令任务。

*   上游服务或测试脚本在调用 `RemotionTSXGeneratorTask` 时，应在 `context` 中构建一个 `remotionTemplate` 对象。

**示例**: 一个标准的 `context` 输入
```json
{
  "originalVideoPath": "/path/to/video.mp4",
  "audioFilePath": "/path/to/audio.mp3",
  "audioDuration": 22,
  "videoConfig": { ... }, // 旧配置，任务会忽略其中的样式指令
  "remotionTemplate": {
    "layers": [
      { "type": "background", "template": "newspaper" },
      { "type": "video", "template": "default-muted" } 
    ]
  }
}
```

### 步骤 4: 扩展 TSX 生成任务

**目标**: 让任务能够理解新的 `remotionTemplate` 并兼容旧的 `videoConfig`。

*   开发者需要修改 `RemotionTSXGeneratorTask.js`，实现我们讨论的**“渐进式配置迁移”**逻辑。
*   **核心逻辑**:
    *   检查 `context.remotionTemplate.layers` 是否存在。
    *   如果**存在**，则遍历 `layers` 数组，加载对应的微模板来构建视频。
    *   如果**不存在**，则**回退**到解析 `context.videoConfig` 的旧逻辑。

---

## 3. 微模板标准格式规范

**版本: 1.0**
**状态: 强制执行**

### 3.1. 标准数据结构

所有微模板文件必须严格遵循以下JSON Schema格式：

```json
{
  "metadata": {
    "name": "模板名称",
    "description": "模板描述"
  },
  "component": "组件名称",
  "config": {
    // 组件特定的配置属性
  }
}
```

### 3.2. 字段定义

#### 3.2.1. metadata (必需)
- **类型**: Object
- **描述**: 模板元数据信息
- **必需字段**:
  - `name` (string): 模板的显示名称，最少1个字符
  - `description` (string): 模板的详细描述

#### 3.2.2. component (必需)
- **类型**: String
- **描述**: 要使用的React组件名称
- **允许值**: 
  - `"Background"` - 背景组件
  - `"CenteredVideo"` - 居中视频组件

#### 3.2.3. config (必需)
- **类型**: Object
- **描述**: 传递给组件的配置属性
- **条件验证**:
  - 当 `component` 为 `"Background"` 时，必须包含:
    - `type` (string): 背景类型 ("image" | "video" | "color")
    - `source` (string): 资源路径
    - `overlay` (boolean): 是否显示覆盖层
  - 当 `component` 为 `"CenteredVideo"` 时，必须包含:
    - `isMuted` (boolean): 是否静音

### 3.3. 示例模板

#### 3.3.1. 背景模板示例
```json
{
  "metadata": {
    "name": "报纸背景",
    "description": "经典报纸风格的背景模板"
  },
  "component": "Background",
  "config": {
    "type": "image",
    "source": "newspaper.jpg",
    "overlay": true
  }
}
```

#### 3.3.2. 视频模板示例
```json
{
  "metadata": {
    "name": "静音视频",
    "description": "默认静音的居中视频模板"
  },
  "component": "CenteredVideo",
  "config": {
    "isMuted": true
  }
}
```

### 3.4. 验证机制

#### 3.4.1. JSON Schema验证
- 所有微模板在加载时必须通过JSON Schema验证
- Schema定义位置: `src/remotion/schemas/microTemplateSchema.js`
- 验证失败时记录警告但保持向后兼容性

#### 3.4.2. 向后兼容性
- 系统必须支持旧格式的微模板文件
- 旧格式检测: 缺少 `metadata`、`component` 或 `config` 字段
- 旧格式处理: 将根级别属性作为 `props` 传递给组件

### 3.5. 文件组织规范

#### 3.5.1. 目录结构
```
src/remotion/templates/
├── backgrounds/          # 背景模板
│   ├── newspaper.json
│   ├── abstract.json
│   └── ...
├── videos/              # 视频模板
│   ├── default-muted.json
│   └── ...
└── schemas/             # Schema定义
    └── microTemplateSchema.js
```

#### 3.5.2. 命名规范
- 文件名使用小写字母和连字符
- 文件名应具有描述性
- 扩展名必须为 `.json`

---

## 4. Remotion相关任务测试标准

**版本: 1.0**
**状态: 强制执行**

### 4.1. 测试文件组织

#### 4.1.1. 位置要求
- 所有Remotion相关任务的测试文件必须位于: `backend/src/tasks/tests/`
- 测试文件命名格式: `[TaskName].test.js`
- 例如: `RemotionTSXGeneratorTask.test.js`

#### 4.1.2. 执行方式
- 测试文件必须是自包含的，可通过以下命令直接执行:
  ```bash
  node backend/src/tasks/tests/[TaskName].test.js
  ```
- 不依赖外部测试运行器（如Jest）

### 4.2. 测试结构标准

#### 4.2.1. 必需导入
```javascript
const [TaskName] = require('../[TaskName]');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const fs = require('fs');
const path = require('path');
```

#### 4.2.2. 日志前缀
```javascript
const testLogPrefix = '[文件：[TaskName].test.js][任务中文名测试]';
```

#### 4.2.3. 断言函数
必须包含以下标准断言函数:
- `assert(condition, message)` - 基础断言
- `assertEquals(actual, expected, message)` - 相等断言
- `assertIncludes(arrayOrString, substring, message)` - 包含断言

### 4.3. 必需测试用例

#### 4.3.1. 基础测试用例
1. **任务实例化测试**
   - 验证任务正确继承TaskBase
   - 验证任务名称和初始状态

2. **参数验证测试**
   - 测试缺少必需字段的错误处理
   - 验证错误状态和进度回调

#### 4.3.2. 微模板相关测试用例
1. **Schema验证测试**
   - 测试新标准格式微模板的加载和验证
   - 验证Schema验证机制正常工作

2. **向后兼容性测试**
   - 测试旧格式微模板的兼容处理
   - 验证新旧格式混合使用的场景

3. **错误处理测试**
   - 测试不存在的微模板文件处理
   - 测试无效JSON格式的处理
   - 测试Schema验证失败的处理

#### 4.3.3. 功能测试用例
1. **TSX生成测试**
   - 验证生成的TSX内容包含正确的组件
   - 验证组件属性正确传递

2. **图层处理测试**
   - 测试多图层配置的处理
   - 验证图层顺序和属性映射

### 4.4. 测试数据管理

#### 4.4.1. 测试模板创建
- 在测试过程中需要创建临时微模板文件时，必须:
  - 使用测试专用目录（如 `templates/test/`）
  - 在测试完成后清理临时文件
  - 确保不影响现有的微模板文件

#### 4.4.2. 测试资源
- 使用现有的微模板文件进行测试
- 优先使用已重构为新标准格式的模板
- 避免依赖外部资源文件

### 4.5. 测试执行标准

#### 4.5.1. 测试结果报告
```javascript
// 测试结果汇总格式
logger.info(`${testLogPrefix} ========== 测试执行完毕 ==========`);
logger.info(`${testLogPrefix} 通过: ${testsPassed} 个测试`);
logger.info(`${testLogPrefix} 失败: ${testsFailed} 个测试`);
```

#### 4.5.2. 退出码规范
- 所有测试通过: `process.exit(0)`
- 存在失败测试: `process.exit(1)`
- 未捕获错误: `process.exit(1)`

#### 4.5.3. 模块导出
```javascript
// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTests().catch(error => {
        logger.error(`${testLogPrefix} 测试执行过程中发生未捕获错误: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        process.exit(1);
    });
}

module.exports = { runTests };
```

### 4.6. 强制执行要求

1. **所有新创建的Remotion相关任务都必须配备符合此标准的测试文件**
2. **现有任务的测试文件必须按此标准进行重构**
3. **测试文件必须包含微模板相关的测试用例**
4. **测试必须验证新标准格式和向后兼容性**
5. **违反此标准的代码不得合并到主分支**

---

## 5. RemotionTSXGeneratorTask 新旧兼容转换详解

**版本: 3.0**
**状态: 已生效**

### 5.1. 核心转换逻辑概述

`RemotionTSXGeneratorTask` 是整个 Remotion 架构的核心编排层，负责将配置指令转换为可执行的 TSX 代码。该任务实现了**渐进式配置迁移模式**，确保新旧配置体系的平滑过渡。

#### 5.1.1. 转换流程总览

```
输入配置检查 → 配置体系判断 → 微模板加载 → 配置合并 → TSX生成 → 文件输出
     ↓              ↓              ↓           ↓          ↓          ↓
  context      优先新配置      JSON解析    props合并   React代码   文件写入
验证必需字段    回退旧配置      Schema验证   图层构建    组件导入    Root更新
```

### 5.2. 配置体系判断机制

#### 5.2.1. 优先级规则

任务按以下优先级处理配置：

1. **第一优先级**: `context.remotionTemplate.layers` 存在且非空
   - 使用新配置体系
   - 遍历 `layers` 数组进行微模板加载
   - 支持动态图层编排

2. **回退机制**: `context.remotionTemplate` 不存在或 `layers` 为空
   - 使用旧配置体系 `context.videoConfig`
   - 固定图层结构：背景层 + 视频层
   - 向后兼容现有配置

#### 5.2.2. 判断代码逻辑

```javascript
// 核心判断逻辑
if (remotionTemplate && remotionTemplate.layers) {
    // 新配置体系处理路径
    logger.info('使用新版 remotionTemplate 配置');
    return await processNewConfigSystem();
} else {
    // 旧配置体系回退路径
    logger.warn('正在使用旧版 videoConfig 进行回退兼容');
    return await processLegacyConfigSystem();
}
```

### 5.3. 新配置体系处理流程

#### 5.3.1. 微模板加载机制

**步骤 1: 路径构建**
- 根据图层类型和模板名称构建文件路径
- 路径格式: `templates/{type}s/{template}.json`
- 示例: `templates/backgrounds/newspaper.json`

**步骤 2: 文件读取与解析**
- 异步读取微模板 JSON 文件
- 解析 JSON 内容为 JavaScript 对象
- 错误处理：文件不存在或 JSON 格式错误

**步骤 3: Schema 验证**
- 使用 Ajv 库进行 JSON Schema 验证
- 验证微模板是否符合标准格式
- 记录验证警告但保持向后兼容

#### 5.3.2. 配置提取与合并

**新标准格式处理**:
```javascript
if (templateJson.config) {
    // 新标准格式：{metadata, component, config}
    templateProps = { ...templateJson.config };
    logger.debug('使用新标准格式微模板');
}
```

**旧格式兼容处理**:
```javascript
else {
    // 兼容旧格式：从根级别提取配置
    templateProps = { ...templateJson };
    // 清理元数据字段
    delete templateProps.name;
    delete templateProps.component;
    delete templateProps.description;
}
```

**配置优先级合并**:
```javascript
// 指令层配置覆盖模板层配置
let finalProps = { ...templateProps, ...(layer.props || {}) };
```

#### 5.3.3. 特殊处理逻辑

**视频图层路径注入**:
```javascript
if (layer.type === 'video') {
    finalProps.src = copiedVideoPath; // 注入复制后的视频文件路径
}
```

### 5.4. 旧配置体系回退处理

#### 5.4.1. 固定图层结构

旧配置体系使用固定的两层结构：

1. **背景图层**
   - 组件: `Background`
   - 配置来源: `_convertBackgroundStyleToProps()` 方法
   - 支持的样式: `newspaper`, `abstract`, `color`

2. **视频图层**
   - 组件: `CenteredVideo`
   - 固定配置: `src` 和 `isMuted` 属性
   - 视频路径自动注入

#### 5.4.2. 背景配置转换详解

**转换方法**: `_convertBackgroundStyleToProps(backgroundStyle, fallbackColor)`

**处理流程**:
1. **回退配置构建**: 创建符合 Background 组件期望的纯色背景配置
2. **样式检查**: 如果为空或为 "color"，直接返回回退配置
3. **模板加载**: 从 `templates/backgrounds/` 目录加载对应 JSON 文件
4. **配置提取**: 从模板的 `config` 字段提取背景配置
5. **错误处理**: 加载失败时返回回退配置并记录警告

**关键修复点**:
```javascript
// 修复前：直接使用模板根级别字段
const backgroundProps = { ...templateJson };

// 修复后：从config字段提取配置
const backgroundProps = templateJson.config ? 
    { ...templateJson.config } : 
    { ...templateJson }; // 兼容旧格式
```

### 5.5. TSX 生成机制

#### 5.5.1. 组件导入处理

**动态导入生成**:
- 遍历所有图层的 `component` 字段
- 自动生成对应的 import 语句
- 避免重复导入（使用 Set 数据结构）

**导入路径规则**:
```javascript
// 组件导入路径映射
const componentPaths = {
    'Background': '../components/backgrounds/Background',
    'CenteredVideo': '../components/videos/CenteredVideo'
};
```

#### 5.5.2. Props 序列化

**不同数据类型处理**:
- **对象类型**: 使用 `JSON.stringify()` 序列化
- **字符串类型**: 添加引号包装
- **其他类型**: 直接转换为字符串

**特殊处理**:
```javascript
// Background组件特殊处理
if (layer.component === 'Background') {
    return `<${layer.component} config={${JSON.stringify(layer.props)}} />`;
}
// 其他组件标准处理
else {
    const propsString = Object.entries(layer.props)
        .map(([key, value]) => `${key}={${formatPropValue(value)}}`)
        .join(' ');
    return `<${layer.component} ${propsString} />`;
}
```

### 5.6. 最新架构目录结构

```
src/remotion/
├── components/           # React组件库（按功能分类）
│   ├── backgrounds/      # 背景相关组件
│   │   └── Background.tsx
│   ├── videos/          # 视频相关组件
│   │   └── CenteredVideo.tsx
│   ├── layouts/         # 布局相关组件
│   ├── progress/        # 进度相关组件
│   └── subtitles/       # 字幕相关组件
├── templates/           # 微模板配置库（按组件类型分类）
│   ├── backgrounds/     # 背景模板
│   │   ├── newspaper.json
│   │   └── abstract.json
│   └── videos/          # 视频模板
│       └── default-muted.json
├── schemas/             # JSON Schema定义
│   └── microTemplateSchema.js
├── generated/           # 自动生成的TSX文件（禁止手动修改）
│   └── VideoComponent_*.tsx
├── public/              # 静态资源库（运行时创建）
│   ├── backgrounds/
│   ├── videos/
│   └── audios/
├── Root.tsx             # Remotion根组件（自动更新）
└── index.ts             # 导出入口
```

### 5.7. 关键修复案例：背景配置问题

#### 5.7.1. 问题描述

在实际使用中发现，生成的 TSX 文件中 Background 组件只包含回退颜色配置，缺少背景图片、叠加层等完整配置。

#### 5.7.2. 根因分析

**问题根源**: `_convertBackgroundStyleToProps` 方法直接使用微模板根级别字段，而新标准格式的配置位于 `config` 字段中。

**修复前代码**:
```javascript
const backgroundProps = { ...templateJson }; // 错误：直接使用根级别
```

**修复后代码**:
```javascript
const backgroundProps = templateJson.config ? 
    { ...templateJson.config } :     // 新标准格式
    { ...templateJson };             // 兼容旧格式
```

#### 5.7.3. 修复效果对比

**修复前生成的配置**:
```javascript
<Background config={{"fallback":{"color":"white"}}} />
```

**修复后生成的配置**:
```javascript
<Background config={{
    "type":"image",
    "source":"backgrounds/newspaper.png",
    "overlay":{"enabled":true,"color":"#000000","opacity":0.6},
    "fallback":{"color":"white"}
}} />
```

### 5.8. 开发者指导原则

#### 5.8.1. 新功能开发

1. **优先使用新配置体系**: 新功能应基于 `remotionTemplate.layers` 设计
2. **创建标准微模板**: 遵循 `{metadata, component, config}` 格式
3. **保持向后兼容**: 确保旧配置仍能正常工作
4. **添加Schema验证**: 为新微模板添加相应的Schema定义

#### 5.8.2. 调试与测试

1. **使用测试文件**: 运行 `RemotionTSXGeneratorTask.test.js` 验证功能
2. **检查生成文件**: 查看 `generated/` 目录下的 TSX 文件内容
3. **验证配置转换**: 确认微模板配置正确传递给组件
4. **测试新旧兼容**: 同时测试新旧配置体系的处理结果

#### 5.8.3. 常见问题排查

1. **配置缺失**: 检查微模板的 `config` 字段是否正确
2. **组件导入错误**: 确认组件名称与文件路径匹配
3. **Props传递问题**: 验证组件期望的props格式
4. **Schema验证失败**: 检查微模板是否符合标准格式

---

**此文档是团队开发的共识，请严格遵守。所有与Remotion相关的开发工作都必须严格按照本文档执行，特别是微模板标准和测试标准部分。**