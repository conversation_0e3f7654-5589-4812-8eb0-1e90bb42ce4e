/**
 * @功能概述: 测试优化后的字幕挖空功能
 * @测试目标: 验证新的optimized提示词模板是否正确生成____占位符，不添加额外字段
 * @输入数据: 使用真实的字幕JSON数据
 * @预期结果: 生成符合要求的挖空字幕，使用____占位符，保持原始JSON结构
 */

const SubtitleClozeTask = require('../../SubtitleClozeTask');
const path = require('path');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
    videoIdentifier: 'test_optimized_cloze_123',
    savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data',
    inputJsonPath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data\\videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z_corrected.json'
};

/**
 * @功能概述: 执行优化挖空测试
 */
async function testOptimizedCloze() {
    const functionName = 'testOptimizedCloze';
    
    try {
        console.log(`[${functionName}] 🚀 开始测试优化后的字幕挖空功能`);
        console.log(`[${functionName}] 测试配置:`, TEST_CONFIG);
        
        // 1. 读取输入数据
        console.log(`[${functionName}] 📖 读取输入字幕JSON文件...`);
        const inputJsonContent = fs.readFileSync(TEST_CONFIG.inputJsonPath, 'utf8');
        const simplifiedSubtitleJsonArray = JSON.parse(inputJsonContent);
        
        console.log(`[${functionName}] ✅ 成功读取 ${simplifiedSubtitleJsonArray.length} 条字幕数据`);
        console.log(`[${functionName}] 示例数据:`, simplifiedSubtitleJsonArray.slice(0, 2));
        
        // 2. 准备上下文
        const correctedFullText = simplifiedSubtitleJsonArray.map(item => item.text).join(' ');
        console.log(`[${functionName}] 📝 完整上下文长度: ${correctedFullText.length} 字符`);
        
        const context = {
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            simplifiedSubtitleJsonArray: simplifiedSubtitleJsonArray,
            savePath: TEST_CONFIG.savePath,
            correctedFullText: correctedFullText
        };
        
        // 3. 创建任务实例
        console.log(`[${functionName}] 🔧 创建SubtitleClozeTask实例...`);
        const clozeTask = new SubtitleClozeTask('OptimizedClozeTest');
        
        // 4. 执行挖空任务
        console.log(`[${functionName}] ⚡ 开始执行挖空任务...`);
        const startTime = Date.now();
        
        const result = await clozeTask.execute(context, (status, subStatus, progress) => {
            console.log(`[${functionName}] 📊 进度更新: ${status} - ${subStatus} - ${progress?.detail || 'N/A'}`);
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`[${functionName}] ✅ 挖空任务完成，耗时: ${duration}ms`);
        console.log(`[${functionName}] 📋 任务结果:`, {
            clozedSubtitleJsonPath: result.clozedSubtitleJsonPath,
            clozedEnglishSrtPath: result.clozedEnglishSrtPath,
            arrayLength: result.clozedSubtitleJsonArray.length,
            status: result.subtitleClozeTaskStatus
        });
        
        // 5. 验证结果
        console.log(`[${functionName}] 🔍 验证挖空结果...`);
        await validateClozedResult(result.clozedSubtitleJsonArray, simplifiedSubtitleJsonArray, functionName);
        
        // 6. 显示挖空示例
        console.log(`[${functionName}] 📝 挖空示例对比:`);
        for (let i = 0; i < Math.min(3, result.clozedSubtitleJsonArray.length); i++) {
            const original = simplifiedSubtitleJsonArray[i];
            const clozed = result.clozedSubtitleJsonArray[i];
            
            console.log(`[${functionName}] 条目${i + 1}:`);
            console.log(`  原文: "${original.text}"`);
            console.log(`  挖空: "${clozed.text}"`);
            console.log(`  变化: ${original.text === clozed.text ? '无变化' : '已挖空'}`);
            console.log('');
        }
        
        console.log(`[${functionName}] 🎉 优化挖空测试完成！`);
        return result;
        
    } catch (error) {
        console.error(`[${functionName}] ❌ 测试失败:`, error.message);
        if (error.stack) {
            console.error(`[${functionName}] 错误堆栈:`, error.stack);
        }
        throw error;
    }
}

/**
 * @功能概述: 验证挖空结果的质量
 * @参数说明:
 *   - clozedArray: 挖空后的字幕数组
 *   - originalArray: 原始字幕数组
 *   - logPrefix: 日志前缀
 */
async function validateClozedResult(clozedArray, originalArray, logPrefix) {
    console.log(`[${logPrefix}] 🔍 开始验证挖空结果质量...`);
    
    // 1. 基础结构验证
    if (clozedArray.length !== originalArray.length) {
        throw new Error(`数组长度不匹配: 挖空=${clozedArray.length}, 原始=${originalArray.length}`);
    }
    
    // 2. 字段完整性验证
    let structureValid = true;
    let placeholderCount = 0;
    let changedCount = 0;
    let extraFieldsCount = 0;
    
    for (let i = 0; i < clozedArray.length; i++) {
        const original = originalArray[i];
        const clozed = clozedArray[i];
        
        // 检查必需字段
        if (!clozed.hasOwnProperty('id') || !clozed.hasOwnProperty('start') || 
            !clozed.hasOwnProperty('end') || !clozed.hasOwnProperty('text')) {
            console.error(`[${logPrefix}] ❌ 条目${i + 1}缺少必需字段`);
            structureValid = false;
        }
        
        // 检查额外字段（如words字段）
        const expectedFields = ['id', 'start', 'end', 'text'];
        const actualFields = Object.keys(clozed);
        const extraFields = actualFields.filter(field => !expectedFields.includes(field));
        if (extraFields.length > 0) {
            console.warn(`[${logPrefix}] ⚠️ 条目${i + 1}包含额外字段: ${extraFields.join(', ')}`);
            extraFieldsCount++;
        }
        
        // 检查ID、时间戳是否保持不变
        if (clozed.id !== original.id || clozed.start !== original.start || clozed.end !== original.end) {
            console.error(`[${logPrefix}] ❌ 条目${i + 1}的ID或时间戳被修改`);
            structureValid = false;
        }
        
        // 统计挖空情况
        if (clozed.text !== original.text) {
            changedCount++;
            const underscoreCount = (clozed.text.match(/____/g) || []).length;
            placeholderCount += underscoreCount;
            
            // 检查是否使用了错误的占位符
            if (clozed.text.includes('()')) {
                console.error(`[${logPrefix}] ❌ 条目${i + 1}使用了错误的()占位符`);
                structureValid = false;
            }
        }
    }
    
    // 3. 输出验证结果
    console.log(`[${logPrefix}] 📊 验证结果统计:`);
    console.log(`  - 结构完整性: ${structureValid ? '✅ 通过' : '❌ 失败'}`);
    console.log(`  - 总条目数: ${clozedArray.length}`);
    console.log(`  - 发生变化的条目: ${changedCount} (${(changedCount / clozedArray.length * 100).toFixed(1)}%)`);
    console.log(`  - 总挖空数量: ${placeholderCount}`);
    console.log(`  - 平均每条挖空: ${(placeholderCount / clozedArray.length).toFixed(1)}个`);
    console.log(`  - 包含额外字段的条目: ${extraFieldsCount}`);
    
    if (!structureValid) {
        throw new Error('挖空结果结构验证失败');
    }
    
    console.log(`[${logPrefix}] ✅ 挖空结果验证通过！`);
}

// 执行测试
if (require.main === module) {
    testOptimizedCloze()
        .then(() => {
            console.log('🎉 所有测试完成！');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 测试失败:', error.message);
            process.exit(1);
        });
}

module.exports = { testOptimizedCloze, validateClozedResult };
