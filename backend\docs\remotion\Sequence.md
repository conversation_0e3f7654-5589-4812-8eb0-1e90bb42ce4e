# &lt;Sequence&gt;

## 概述

`<Sequence>` 组件用于在视频中对组件或动画部分进行时间偏移。通过使用序列，您可以控制内容在时间轴上的显示时机和持续时间。

## 语法

```typescript
import { Sequence } from "remotion";

<Sequence from={30} durationInFrames={60}>
  <YourComponent />
</Sequence>
```

## 核心概念

### 时间偏移
所有调用 `useCurrentFrame()` 的 `<Sequence>` 子组件都会接收到被 `from` 属性偏移的值。

### 默认布局
默认情况下，`<Sequence>` 的子组件被包装在 `<AbsoluteFill>` 组件中，实现绝对定位和层叠效果。

## 属性

### from
- **类型**: `number`
- **默认值**: `0` (v3.2.36+)
- **描述**: 子组件应该假设视频开始的帧数
- **说明**: 当序列在第 `frame` 帧时，其子组件在第 `0` 帧

### durationInFrames
- **类型**: `number`
- **默认值**: `Infinity`
- **描述**: 序列应该显示的帧数
- **说明**: 超出时间范围的子组件将被卸载

### width / height
- **类型**: `number`
- **版本**: v4.0.80+
- **描述**: 为序列设置特定的宽度/高度样式
- **用途**: 覆盖 `useVideoConfig()` 返回的尺寸

### name
- **类型**: `string`
- **描述**: 序列的名称，显示在 Remotion Studio 时间轴中
- **用途**: 帮助在时间轴中跟踪序列

### layout
- **类型**: `"absolute-fill"` | `"none"`
- **默认值**: `"absolute-fill"`
- **描述**: 布局模式
- **说明**: `"none"` 时不使用容器，需要自行处理布局

### style
- **类型**: `React.CSSProperties`
- **版本**: v3.0.27+
- **描述**: 应用到容器的 CSS 样式
- **限制**: `layout="none"` 时不允许设置

### className
- **类型**: `string`
- **版本**: v3.3.45+
- **描述**: 应用到容器的类名
- **限制**: `layout="none"` 时不允许设置

### premountFor
- **类型**: `number`
- **版本**: v4.0.140+
- **描述**: 预挂载序列的帧数

### showInTimeline
- **类型**: `boolean`
- **版本**: v4.0.110+
- **描述**: 是否在 Studio 时间轴中显示轨道

## 基础示例

### 1. 简单的时间序列

```typescript
import { Sequence } from "remotion";
import { Intro, Clip, Outro } from "./components";

const MyTrailer = () => {
  return (
    <>
      {/* 第 0-29 帧显示 */}
      <Sequence durationInFrames={30}>
        <Intro />
      </Sequence>
      
      {/* 第 30-59 帧显示 */}
      <Sequence from={30} durationInFrames={30}>
        <Clip />
      </Sequence>
      
      {/* 第 60 帧开始显示到结束 */}
      <Sequence from={60}>
        <Outro />
      </Sequence>
    </>
  );
};
```

### 2. 时间偏移演示

```typescript
import { Sequence, useCurrentFrame } from "remotion";

const FrameDisplay = () => <div>{useCurrentFrame()}</div>;

const TimeShiftExample = () => {
  return (
    <>
      <FrameDisplay />
      <Sequence from={30}>
        <FrameDisplay />
      </Sequence>
    </>
  );
};

// 在第 0 帧: 显示 "0"
// 在第 30 帧: 显示 "30" 和 "0"
```

## 常见用法模式

### 1. 延迟显示

```typescript
const DelayExample = () => {
  return (
    <Sequence from={30}>
      <BlueSquare />
    </Sequence>
  );
};
```

### 2. 限制结束时间

```typescript
const TrimEndExample = () => {
  return (
    <Sequence durationInFrames={45}>
      <BlueSquare />
    </Sequence>
  );
};
```

### 3. 裁剪开始部分

```typescript
const TrimStartExample = () => {
  return (
    <Sequence from={-15}>
      <BlueSquare />
    </Sequence>
  );
};
```

### 4. 裁剪并延迟

```typescript
const TrimAndDelayExample = () => {
  return (
    <Sequence from={30}>
      <Sequence from={-15}>
        <BlueSquare />
      </Sequence>
    </Sequence>
  );
};
```

## 高级应用

### 1. 嵌套序列（级联）

```typescript
const CascadingExample = () => {
  return (
    <Sequence from={30} name="外层序列">
      <Sequence from={60} name="内层序列">
        <Content />
      </Sequence>
    </Sequence>
  );
};
// 内容将在第 90 帧 (30 + 60) 开始显示
```

### 2. 自定义布局

```typescript
const CustomLayoutExample = () => {
  return (
    <div style={{ display: 'flex' }}>
      <Sequence from={0} layout="none">
        <LeftContent />
      </Sequence>
      <Sequence from={30} layout="none">
        <RightContent />
      </Sequence>
    </div>
  );
};
```

### 3. 响应式序列

```typescript
const ResponsiveSequence = () => {
  const { width, height } = useVideoConfig();
  const isVertical = height > width;
  
  return (
    <Sequence 
      from={isVertical ? 15 : 30}
      width={isVertical ? width : width * 0.8}
      height={isVertical ? height * 0.8 : height}
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        borderRadius: 8
      }}
    >
      <AdaptiveContent />
    </Sequence>
  );
};
```

### 4. 动态序列生成

```typescript
interface SequenceData {
  id: string;
  component: React.ComponentType;
  startFrame: number;
  duration: number;
}

const DynamicSequences = () => {
  const sequences: SequenceData[] = [
    { id: 'intro', component: Intro, startFrame: 0, duration: 60 },
    { id: 'main', component: Main, startFrame: 60, duration: 240 },
    { id: 'outro', component: Outro, startFrame: 300, duration: 60 }
  ];
  
  return (
    <>
      {sequences.map((seq) => (
        <Sequence
          key={seq.id}
          from={seq.startFrame}
          durationInFrames={seq.duration}
          name={seq.id}
        >
          <seq.component />
        </Sequence>
      ))}
    </>
  );
};
```

### 5. 条件序列

```typescript
const ConditionalSequences = () => {
  const { durationInFrames } = useVideoConfig();
  const showIntro = durationInFrames > 120;
  const showOutro = durationInFrames > 180;
  
  return (
    <>
      {showIntro && (
        <Sequence from={0} durationInFrames={60}>
          <Intro />
        </Sequence>
      )}
      
      <Sequence from={showIntro ? 60 : 0}>
        <MainContent />
      </Sequence>
      
      {showOutro && (
        <Sequence from={durationInFrames - 60}>
          <Outro />
        </Sequence>
      )}
    </>
  );
};
```

## 动画序列

### 1. 淡入淡出序列

```typescript
const FadeSequence = ({ children, from, duration }: {
  children: React.ReactNode;
  from: number;
  duration: number;
}) => {
  const frame = useCurrentFrame();
  
  const opacity = interpolate(
    frame,
    [from, from + 15, from + duration - 15, from + duration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  return (
    <Sequence from={from} durationInFrames={duration}>
      <div style={{ opacity }}>
        {children}
      </div>
    </Sequence>
  );
};
```

### 2. 滑动序列

```typescript
const SlideSequence = ({ children, from, direction = 'left' }: {
  children: React.ReactNode;
  from: number;
  direction?: 'left' | 'right' | 'up' | 'down';
}) => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();
  
  const getTransform = () => {
    const progress = interpolate(frame, [0, 30], [1, 0], {
      extrapolateRight: 'clamp'
    });
    
    switch (direction) {
      case 'left': return `translateX(${-width * progress}px)`;
      case 'right': return `translateX(${width * progress}px)`;
      case 'up': return `translateY(${-height * progress}px)`;
      case 'down': return `translateY(${height * progress}px)`;
    }
  };
  
  return (
    <Sequence from={from}>
      <div style={{ transform: getTransform() }}>
        {children}
      </div>
    </Sequence>
  );
};
```

## 与其他组件结合

### 1. 与 Series 组件结合

```typescript
import { Series } from "remotion";

const SequentialContent = () => {
  return (
    <Series>
      <Series.Sequence durationInFrames={60}>
        <Scene1 />
      </Series.Sequence>
      <Series.Sequence durationInFrames={90}>
        <Scene2 />
      </Series.Sequence>
      <Series.Sequence durationInFrames={120}>
        <Scene3 />
      </Series.Sequence>
    </Series>
  );
};
```

### 2. 与 Audio/Video 组件结合

```typescript
const MediaSequence = () => {
  return (
    <>
      <Sequence from={0} durationInFrames={180}>
        <Audio src={staticFile("background-music.mp3")} />
      </Sequence>
      
      <Sequence from={30} durationInFrames={120}>
        <Video src={staticFile("intro-video.mp4")} />
      </Sequence>
      
      <Sequence from={60}>
        <TextOverlay />
      </Sequence>
    </>
  );
};
```

## 性能优化

### 1. 使用 premountFor

```typescript
const OptimizedSequence = () => {
  return (
    <Sequence 
      from={60} 
      premountFor={30} // 提前 30 帧预挂载
    >
      <HeavyComponent />
    </Sequence>
  );
};
```

### 2. 条件渲染优化

```typescript
const ConditionalRender = () => {
  const frame = useCurrentFrame();
  
  return (
    <>
      {frame >= 0 && frame < 60 && (
        <Sequence from={0} durationInFrames={60}>
          <Scene1 />
        </Sequence>
      )}
      
      {frame >= 60 && frame < 120 && (
        <Sequence from={60} durationInFrames={60}>
          <Scene2 />
        </Sequence>
      )}
    </>
  );
};
```

## 添加 Ref

```typescript
import { useRef } from "react";

const SequenceWithRef = () => {
  const sequenceRef = useRef<HTMLDivElement>(null);
  
  return (
    <Sequence from={30} ref={sequenceRef}>
      <Content />
    </Sequence>
  );
};
```

## 注意事项

### 1. @remotion/three 兼容性
在 `<ThreeCanvas>` 中使用时，必须设置 `layout="none"`：

```typescript
<ThreeCanvas>
  <Sequence layout="none" from={30}>
    <ThreeContent />
  </Sequence>
</ThreeCanvas>
```

### 2. 性能考虑
- 避免创建过多嵌套序列
- 使用 `premountFor` 优化重型组件
- 合理使用 `durationInFrames` 及时卸载组件

### 3. 时间轴管理
- 使用有意义的 `name` 属性
- 合理使用 `showInTimeline` 控制显示
- 避免时间重叠导致的视觉冲突

## 相关 API

- [`<Series>`](./Series.md) - 顺序播放序列
- [`<AbsoluteFill>`](./AbsoluteFill.md) - 绝对定位填充
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置
- [`interpolate()`](./interpolate.md) - 值插值

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/Sequence.tsx)
