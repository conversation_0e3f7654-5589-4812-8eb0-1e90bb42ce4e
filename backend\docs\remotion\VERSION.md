# VERSION

## 概述

`VERSION` 常量用于获取当前 Remotion 的版本号。这个常量只报告 [`remotion`](/docs/remotion) 包的版本，无法排除与其他 Remotion 包的[版本冲突](/docs/cli/versions)。

## 语法

```typescript
import { VERSION } from "remotion";
// 或者
import { VERSION } from "remotion/version";
```

## 返回值

- **类型**: `string`
- **格式**: 语义化版本号 (例如: "4.0.57")

## 基础用法

### 1. 从 remotion 导入

```typescript
import { VERSION } from "remotion";

console.log(VERSION); // "4.0.57"
console.log(`当前 Remotion 版本: ${VERSION}`);
```

### 2. 从 remotion/version 导入

```typescript
import { VERSION } from "remotion/version";

console.log(VERSION); // "4.0.57"
```

**优势**: 避免导入 Remotion 及其依赖项（如 `react` 和 `react-dom`），适用于只需要版本信息的场景。

## 实际应用场景

### 1. 版本兼容性检查

```typescript
import { VERSION } from "remotion/version";

const checkCompatibility = () => {
  const currentVersion = VERSION;
  const requiredVersion = "4.0.0";
  
  const isCompatible = compareVersions(currentVersion, requiredVersion);
  
  if (!isCompatible) {
    throw new Error(
      `Remotion 版本不兼容。当前版本: ${currentVersion}，要求版本: ${requiredVersion} 或更高`
    );
  }
  
  console.log(`✅ Remotion 版本兼容: ${currentVersion}`);
};

const compareVersions = (current: string, required: string): boolean => {
  const currentParts = current.split('.').map(Number);
  const requiredParts = required.split('.').map(Number);
  
  for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
    const currentPart = currentParts[i] || 0;
    const requiredPart = requiredParts[i] || 0;
    
    if (currentPart > requiredPart) return true;
    if (currentPart < requiredPart) return false;
  }
  
  return true;
};

// 使用示例
try {
  checkCompatibility();
} catch (error) {
  console.error(error.message);
}
```

### 2. 调试信息显示

```typescript
import React from 'react';
import { VERSION, getRemotionEnvironment } from "remotion";

const DebugInfo: React.FC = () => {
  const { isStudio, isRendering, isPlayer } = getRemotionEnvironment();
  
  // 只在开发环境中显示调试信息
  if (!isStudio) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      backgroundColor: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: 10,
      borderRadius: 5,
      fontSize: 12,
      fontFamily: 'monospace',
      zIndex: 9999
    }}>
      <div><strong>调试信息</strong></div>
      <div>Remotion 版本: {VERSION}</div>
      <div>环境: {isStudio ? 'Studio' : isRendering ? 'Rendering' : isPlayer ? 'Player' : 'Unknown'}</div>
      <div>时间: {new Date().toLocaleTimeString()}</div>
    </div>
  );
};

export default DebugInfo;
```

### 3. 错误报告

```typescript
import { VERSION } from "remotion/version";

class RemotionError extends Error {
  constructor(message: string, context?: any) {
    super(message);
    this.name = 'RemotionError';
    
    // 在错误信息中包含版本信息
    const errorDetails = {
      message,
      remotionVersion: VERSION,
      timestamp: new Date().toISOString(),
      context
    };
    
    console.error('Remotion 错误详情:', errorDetails);
  }
}

const handleRenderError = (error: Error, additionalContext?: any) => {
  throw new RemotionError(
    `渲染失败: ${error.message}`,
    {
      originalError: error.message,
      stack: error.stack,
      ...additionalContext
    }
  );
};

// 使用示例
try {
  // 一些可能失败的渲染操作
  throw new Error("模拟渲染错误");
} catch (error) {
  handleRenderError(error as Error, {
    compositionId: "my-composition",
    frame: 120
  });
}
```

### 4. 功能特性检测

```typescript
import { VERSION } from "remotion/version";

const getVersionInfo = () => {
  const version = VERSION;
  const [major, minor, patch] = version.split('.').map(Number);
  
  return {
    version,
    major,
    minor,
    patch,
    isStable: major >= 4,
    supportsNewFeatures: major >= 4 && minor >= 0
  };
};

const FeatureDetection = () => {
  const versionInfo = getVersionInfo();
  
  const features = {
    // 基于版本检测功能可用性
    supportsGifComponent: versionInfo.major >= 4,
    supportsSeriesComponent: versionInfo.major >= 4,
    supportsAdvancedAnimations: versionInfo.major >= 4 && versionInfo.minor >= 0,
    supportsWebCodecs: versionInfo.major >= 4 && versionInfo.minor >= 0
  };

  return (
    <div style={{ padding: 20 }}>
      <h2>Remotion 功能检测</h2>
      <p>当前版本: <strong>{versionInfo.version}</strong></p>
      
      <h3>可用功能:</h3>
      <ul>
        <li>GIF 组件: {features.supportsGifComponent ? '✅' : '❌'}</li>
        <li>Series 组件: {features.supportsSeriesComponent ? '✅' : '❌'}</li>
        <li>高级动画: {features.supportsAdvancedAnimations ? '✅' : '❌'}</li>
        <li>WebCodecs: {features.supportsWebCodecs ? '✅' : '❌'}</li>
      </ul>
      
      {!versionInfo.isStable && (
        <div style={{
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: 5,
          padding: 10,
          marginTop: 15
        }}>
          ⚠️ 您正在使用预发布版本，某些功能可能不稳定
        </div>
      )}
    </div>
  );
};
```

### 5. 版本更新提醒

```typescript
import { VERSION } from "remotion/version";

const checkForUpdates = async () => {
  try {
    const response = await fetch('https://api.github.com/repos/remotion-dev/remotion/releases/latest');
    const latestRelease = await response.json();
    const latestVersion = latestRelease.tag_name.replace('v', '');
    
    const currentVersion = VERSION;
    
    if (isNewerVersion(latestVersion, currentVersion)) {
      return {
        hasUpdate: true,
        currentVersion,
        latestVersion,
        releaseUrl: latestRelease.html_url
      };
    }
    
    return {
      hasUpdate: false,
      currentVersion,
      latestVersion
    };
  } catch (error) {
    console.error('检查更新失败:', error);
    return null;
  }
};

const isNewerVersion = (latest: string, current: string): boolean => {
  const latestParts = latest.split('.').map(Number);
  const currentParts = current.split('.').map(Number);
  
  for (let i = 0; i < Math.max(latestParts.length, currentParts.length); i++) {
    const latestPart = latestParts[i] || 0;
    const currentPart = currentParts[i] || 0;
    
    if (latestPart > currentPart) return true;
    if (latestPart < currentPart) return false;
  }
  
  return false;
};

const UpdateNotification: React.FC = () => {
  const [updateInfo, setUpdateInfo] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    checkForUpdates().then(info => {
      setUpdateInfo(info);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <div>检查更新中...</div>;
  }

  if (!updateInfo || !updateInfo.hasUpdate) {
    return null;
  }

  return (
    <div style={{
      backgroundColor: '#d4edda',
      border: '1px solid #c3e6cb',
      borderRadius: 5,
      padding: 15,
      margin: 10,
      color: '#155724'
    }}>
      <h4>🎉 有新版本可用！</h4>
      <p>
        当前版本: <strong>{updateInfo.currentVersion}</strong><br/>
        最新版本: <strong>{updateInfo.latestVersion}</strong>
      </p>
      <a 
        href={updateInfo.releaseUrl} 
        target="_blank" 
        rel="noopener noreferrer"
        style={{
          backgroundColor: '#28a745',
          color: 'white',
          padding: '8px 16px',
          textDecoration: 'none',
          borderRadius: 3,
          display: 'inline-block'
        }}
      >
        查看更新详情
      </a>
    </div>
  );
};
```

### 6. 配置文件生成

```typescript
import { VERSION } from "remotion/version";

const generateProjectConfig = () => {
  const config = {
    project: {
      name: "我的 Remotion 项目",
      version: "1.0.0",
      createdAt: new Date().toISOString()
    },
    remotion: {
      version: VERSION,
      minRequiredVersion: "4.0.0"
    },
    build: {
      target: "es2020",
      format: "mp4",
      quality: "high"
    },
    metadata: {
      author: "开发者",
      description: "使用 Remotion 创建的视频项目",
      tags: ["remotion", "video", "react"]
    }
  };

  return config;
};

const saveProjectConfig = () => {
  const config = generateProjectConfig();
  const configJson = JSON.stringify(config, null, 2);
  
  console.log('项目配置:');
  console.log(configJson);
  
  // 在实际应用中，您可能会将配置保存到文件
  // fs.writeFileSync('remotion.config.json', configJson);
};

// 使用示例
saveProjectConfig();
```

## 导入方式对比

| 导入方式 | 优势 | 劣势 | 适用场景 |
|----------|------|------|----------|
| `from "remotion"` | 可同时使用其他 Remotion API | 导入完整的 Remotion 包 | 在组件中需要多个 API |
| `from "remotion/version"` | 轻量级，不导入依赖 | 只能获取版本信息 | 工具脚本、配置文件 |

## 最佳实践

1. **轻量级使用**: 如果只需要版本信息，使用 `remotion/version` 导入
2. **版本检查**: 在项目初始化时检查版本兼容性
3. **错误报告**: 在错误信息中包含版本号便于调试
4. **功能检测**: 基于版本号检测功能可用性
5. **更新提醒**: 定期检查是否有新版本可用

## 常见用例

- 版本兼容性检查
- 调试信息显示
- 错误报告增强
- 功能特性检测
- 更新通知
- 项目配置生成

## 相关 API

- [`getRemotionEnvironment()`](./getRemotionEnvironment.md) - 获取运行环境信息
- [版本冲突检查](/docs/cli/versions) - CLI 版本管理

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/version.ts)
