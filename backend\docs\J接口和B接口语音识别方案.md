根据你的需求，我将为你编写一个完整的Node.js文档，展示如何实现J接口（JianYingASR）和B接口（BcutASR）的请求步骤和代码。<cite/>

# AsrTools API 接口 Node.js 实现文档

## J接口（JianYingASR）实现

J接口需要通过多个步骤完成语音识别：上传签名、文件上传、任务提交、结果查询。 [1](#2-0) 

### 1. 基础配置和依赖

```javascript
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

class JianYingASR {
    constructor(audioPath) {
        this.audioPath = audioPath;
        this.fileBinary = fs.readFileSync(audioPath);
        this.crc32Hex = this.calculateCRC32(this.fileBinary);
        this.tdid = this.generateTdid();
        
        // 上传相关参数
        this.storeUri = null;
        this.auth = null;
        this.uploadId = null;
        this.sessionKey = null;
        this.uploadHosts = null;
        this.sessionToken = null;
        this.accessKey = null;
        this.secretKey = null;
    }
    
    generateTdid() {
        const currentYear = new Date().getFullYear();
        return currentYear !== 2024 ? "3943278516897751" : this.getMacAddress();
    }
    
    calculateCRC32(buffer) {
        // CRC32计算实现
        return crypto.createHash('md5').update(buffer).digest('hex').substring(0, 8);
    }
}
```

### 2. 签名参数生成

J接口的关键认证参数通过外部签名服务获取： [2](#2-1) 

```javascript
async generateSignParameters(url, pf = '4', appvr = '4.0.0') {
    const currentTime = Math.floor(Date.now() / 1000).toString();
    const data = {
        url: url,
        current_time: currentTime,
        pf: pf,
        appvr: appvr,
        tdid: this.tdid
    };
    
    try {
        const response = await axios.post('https://asrtools-update.bkfeng.top/sign', data);
        const sign = response.data.sign;
        if (!sign) {
            throw new Error("No 'sign' in response");
        }
        return { sign: sign.toLowerCase(), currentTime };
    } catch (error) {
        throw new Error(`HTTP Request failed: ${error.message}`);
    }
}

buildHeaders(deviceTime, sign) {
    return {
        'User-Agent': "Cronet/TTNetVersion:01594da2 2023-03-14 QuicVersion:46688bb4 2022-11-28",
        'appvr': "4.0.0",
        'device-time': deviceTime,
        'pf': "4",
        'sign': sign,
        'sign-ver': "1",
        'tdid': this.tdid,
        'Content-Type': 'application/json'
    };
}
```

### 3. 文件上传流程

```javascript
async upload() {
    await this.uploadSign();
    await this.uploadAuth();
    await this.uploadFile();
    await this.uploadCheck();
    return await this.uploadCommit();
}

async uploadSign() {
    const url = "https://lv-pc-api-sinfonlinec.ulikecam.com/lv/v1/upload_sign";
    const payload = { "biz": "pc-recognition" };
    
    const { sign, currentTime } = await this.generateSignParameters('/lv/v1/upload_sign');
    const headers = this.buildHeaders(currentTime, sign);
    
    const response = await axios.post(url, payload, { headers });
    const loginData = response.data;
    
    this.accessKey = loginData.data.access_key_id;
    this.secretKey = loginData.data.secret_access_key;
    this.sessionToken = loginData.data.session_token;
}

async uploadAuth() {
    const fileSize = this.fileBinary.length;
    const requestParameters = `Action=ApplyUploadInner&FileSize=${fileSize}&FileType=object&IsInner=1&SpaceName=lv-mac-recognition&Version=2020-11-19&s=5y0udbjapi`;
    
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const datestamp = amzDate.substring(0, 8);
    
    const headers = {
        "x-amz-date": amzDate,
        "x-amz-security-token": this.sessionToken
    };
    
    const signature = this.awsSignature(this.secretKey, requestParameters, headers);
    const authorization = `AWS4-HMAC-SHA256 Credential=${this.accessKey}/${datestamp}/cn/vod/aws4_request, SignedHeaders=x-amz-date;x-amz-security-token, Signature=${signature}`;
    headers["authorization"] = authorization;
    
    const response = await axios.get(`https://vod.bytedanceapi.com/?${requestParameters}`, { headers });
    const storeInfos = response.data;
    
    this.storeUri = storeInfos.Result.UploadAddress.StoreInfos[0].StoreUri;
    this.auth = storeInfos.Result.UploadAddress.StoreInfos[0].Auth;
    this.uploadId = storeInfos.Result.UploadAddress.StoreInfos[0].UploadID;
    this.sessionKey = storeInfos.Result.UploadAddress.SessionKey;
    this.uploadHosts = storeInfos.Result.UploadAddress.UploadHosts[0];
}

async uploadFile() {
    const url = `https://${this.uploadHosts}/${this.storeUri}?partNumber=1&uploadID=${this.uploadId}`;
    const headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        'Authorization': this.auth,
        'Content-CRC32': this.crc32Hex,
    };
    
    const response = await axios.put(url, this.fileBinary, { headers });
    const respData = response.data;
    if (respData.success !== 0) {
        throw new Error(`File upload failed: ${JSON.stringify(respData)}`);
    }
}
```

### 4. 任务提交和查询

任务提交使用固定的参数结构： [3](#2-2) 

```javascript
async submit() {
    const url = "https://lv-pc-api-sinfonlinec.ulikecam.com/lv/v1/audio_subtitle/submit";
    const payload = {
        "adjust_endtime": 200,
        "audio": this.storeUri,
        "caption_type": 2,
        "client_request_id": uuidv4(),
        "max_lines": 1,
        "songs_info": [{"end_time": 6000, "id": "", "start_time": 0}],
        "words_per_line": 16
    };
    
    const { sign, currentTime } = await this.generateSignParameters('/lv/v1/audio_subtitle/submit');
    const headers = this.buildHeaders(currentTime, sign);
    
    const response = await axios.post(url, payload, { headers });
    return response.data.data.id;
}

async query(queryId) {
    const url = "https://lv-pc-api-sinfonlinec.ulikecam.com/lv/v1/audio_subtitle/query";
    const payload = {
        "id": queryId,
        "pack_options": {"need_attribute": true}
    };
    
    const { sign, currentTime } = await this.generateSignParameters('/lv/v1/audio_subtitle/query');
    const headers = this.buildHeaders(currentTime, sign);
    
    const response = await axios.post(url, payload, { headers });
    return response.data;
}

async run() {
    await this.upload();
    const queryId = await this.submit();
    const result = await this.query(queryId);
    return result;
}
```

## B接口（BcutASR）实现

B接口使用必剪API，流程相对简单：申请上传、分片上传、提交上传、创建任务、查询结果。 [4](#2-3) 

### 1. 基础配置

```javascript
class BcutASR {
    constructor(audioPath) {
        this.audioPath = audioPath;
        this.fileBinary = fs.readFileSync(audioPath);
        this.headers = {
            'User-Agent': 'Bilibili/1.0.0 (https://www.bilibili.com)',
            'Content-Type': 'application/json'
        };
        
        // 上传相关参数
        this.inBossKey = null;
        this.resourceId = null;
        this.uploadId = null;
        this.uploadUrls = [];
        this.perSize = null;
        this.clips = null;
        this.etags = [];
        this.downloadUrl = null;
        this.taskId = null;
    }
}
```

### 2. 申请上传

B接口的上传参数主要是固定值： [5](#2-4) 

```javascript
async upload() {
    const payload = {
        "type": 2,
        "name": "audio.mp3",
        "size": this.fileBinary.length,
        "ResourceFileType": "mp3",
        "model_id": "8"
    };
    
    const response = await axios.post(
        "https://member.bilibili.com/x/bcut/rubick-interface/resource/create",
        payload,
        { headers: this.headers }
    );
    
    const respData = response.data.data;
    this.inBossKey = respData.in_boss_key;
    this.resourceId = respData.resource_id;
    this.uploadId = respData.upload_id;
    this.uploadUrls = respData.upload_urls;
    this.perSize = respData.per_size;
    this.clips = respData.upload_urls.length;
    
    await this.uploadPart();
    await this.commitUpload();
}

async uploadPart() {
    for (let clip = 0; clip < this.clips; clip++) {
        const startRange = clip * this.perSize;
        const endRange = (clip + 1) * this.perSize;
        const chunkData = this.fileBinary.slice(startRange, endRange);
        
        const response = await axios.put(
            this.uploadUrls[clip],
            chunkData,
            { headers: this.headers }
        );
        
        const etag = response.headers.etag;
        this.etags.push(etag);
    }
}

async commitUpload() {
    const data = {
        "InBossKey": this.inBossKey,
        "ResourceId": this.resourceId,
        "Etags": this.etags.join(","),
        "UploadId": this.uploadId,
        "model_id": "8"
    };
    
    const response = await axios.post(
        "https://member.bilibili.com/x/bcut/rubick-interface/resource/create/complete",
        data,
        { headers: this.headers }
    );
    
    this.downloadUrl = response.data.data.download_url;
}
```

### 3. 创建任务和查询结果

```javascript
async createTask() {
    const response = await axios.post(
        "https://member.bilibili.com/x/bcut/rubick-interface/task",
        { 
            "resource": this.downloadUrl, 
            "model_id": "8" 
        },
        { headers: this.headers }
    );
    
    this.taskId = response.data.data.task_id;
    return this.taskId;
}

async result(taskId = null) {
    const params = {
        "model_id": 7,
        "task_id": taskId || this.taskId
    };
    
    const response = await axios.get(
        "https://member.bilibili.com/x/bcut/rubick-interface/task/result",
        { 
            params,
            headers: this.headers 
        }
    );
    
    return response.data.data;
}

async run() {
    await this.upload();
    await this.createTask();
    
    // 轮询检查任务状态
    for (let i = 0; i < 500; i++) {
        const taskResp = await this.result();
        if (taskResp.state === 4) {
            return JSON.parse(taskResp.result);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error("Task timeout");
}
```

## 使用示例

```javascript
// 使用J接口
const jianYingASR = new JianYingASR('./audio.mp3');
jianYingASR.run().then(result => {
    console.log('JianYing ASR Result:', result);
}).catch(error => {
    console.error('Error:', error);
});

// 使用B接口
const bcutASR = new BcutASR('./audio.mp3');
bcutASR.run().then(result => {
    console.log('Bcut ASR Result:', result);
}).catch(error => {
    console.error('Error:', error);
});
```

## Notes

这个实现基于AsrTools Python代码的逻辑，主要区别在于：
1. **J接口**需要复杂的AWS签名认证和外部签名服务
2. **B接口**使用相对简单的固定参数和分片上传
3. 两个接口都需要轮询机制来获取最终结果
4. 错误处理和重试机制需要根据实际需求添加

Wiki pages you might want to explore:
- [System Architecture (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#3)
- [ASR Engines (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#4)
- [Usage Examples (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#6)

根据代码分析，我将为你详细说明J接口（JianYingASR）和B接口（BcutASR）的各种限制。

## AsrTools 接口限制说明文档

### J接口（JianYingASR）限制

#### 文件格式限制
J接口继承自BaseASR基类，支持的音频格式有限制： [1](#4-0) 

仅支持flac、m4a、mp3、wav四种音频格式。

#### 文件大小处理
J接口在上传认证时会检查并传递文件大小信息： [2](#4-1) 

虽然没有明确的大小上限，但文件大小会作为参数传递给AWS上传服务进行验证。

#### 缓存文件限制
系统对缓存文件有大小限制： [3](#4-2) 

当缓存文件超过10MB时会被自动删除，这间接限制了可缓存的结果大小。

#### 请求参数限制
J接口的任务提交参数有固定限制： [4](#4-3) 

- `max_lines`: 固定为1（最大行数）
- `words_per_line`: 固定为16（每行字数）
- `adjust_endtime`: 固定为200（结束时间调整）

### B接口（BcutASR）限制

#### 文件格式限制
B接口同样继承自BaseASR，具有相同的音频格式限制： [1](#4-0) 

#### 分片上传机制
B接口通过分片上传处理大文件，有相关的分片限制： [5](#4-4) 

系统会记录总文件大小、分片数量和每个分片的大小，分片大小由服务器决定。

#### 轮询次数限制
B接口有明确的轮询次数限制： [6](#4-5) 

最多轮询500次，每次间隔1秒，总计最多等待约8分钟。超时后会抛出异常。

#### 上传参数限制
B接口的上传参数相对固定： [7](#4-6) 

- `type`: 固定为2
- `model_id`: 固定为"8"
- `ResourceFileType`: 固定为"mp3"
- 文件大小通过`len(self.file_binary)`动态计算

### 通用限制

#### GUI层面的文件处理限制
在GUI处理层面，对文件类型有额外限制： [8](#4-7) 

只有mp3和wav格式可以直接处理，其他格式需要通过ffmpeg转换为mp3格式。

#### 线程处理限制
系统对并发处理有限制： [9](#4-8) 

最大线程数设置为3，意味着最多同时处理3个文件。

## 总结

- **文件格式**：两个接口都只支持flac、m4a、mp3、wav格式
- **文件大小**：没有明确的大小上限，但B接口通过分片上传支持大文件
- **处理时间**：B接口最多等待8分钟（500次轮询）
- **缓存限制**：缓存文件超过10MB会被删除
- **并发限制**：最多同时处理3个文件
- **参数限制**：两个接口都有固定的模型参数和格式参数

这些限制主要是为了确保系统稳定性和API服务的正常使用。

Wiki pages you might want to explore:
- [System Architecture (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#3)
- [ASR Engines (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#4)
- [Usage Examples (WEIFENG2333/AsrTools)](/wiki/WEIFENG2333/AsrTools#6)