/**
 * @功能概述: 文件信息提取器 - 提取视频和字幕文件的详细信息
 * @作者: AI Assistant
 * @创建时间: 2025-07-26
 * 
 * @功能范围:
 *   - 获取文件基本信息（大小、修改时间）
 *   - 提取视频文件时长信息
 *   - 解析字幕文件条目数量
 *   - 格式化文件大小显示
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const logger = require('./logger');

const execAsync = promisify(exec);

class FileInfoExtractor {
    /**
     * @功能概述: 格式化文件大小
     * @参数说明:
     *   - bytes: 字节数
     * @返回值: string - 格式化后的大小字符串
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * @功能概述: 获取文件基本信息
     * @参数说明:
     *   - filePath: 文件路径
     * @返回值: {size, sizeFormatted, modifiedTime}
     */
    static async getFileBasicInfo(filePath) {
        try {
            const stats = await fs.stat(filePath);
            return {
                size: stats.size,
                sizeFormatted: this.formatFileSize(stats.size),
                modifiedTime: stats.mtime.toISOString()
            };
        } catch (error) {
            logger.error(`[getFileBasicInfo] 获取文件信息失败: ${filePath}, 错误: ${error.message}`);
            return {
                size: 0,
                sizeFormatted: '未知',
                modifiedTime: null
            };
        }
    }

    /**
     * @功能概述: 获取视频文件时长
     * @参数说明:
     *   - videoPath: 视频文件路径
     * @返回值: {duration, durationFormatted}
     */
    static async getVideoDuration(videoPath) {
        try {
            // 使用ffprobe获取视频时长
            const command = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${videoPath}"`;
            const { stdout } = await execAsync(command);
            
            const duration = parseFloat(stdout.trim());
            if (isNaN(duration)) {
                throw new Error('无法解析视频时长');
            }

            // 格式化时长显示
            const minutes = Math.floor(duration / 60);
            const seconds = Math.floor(duration % 60);
            const durationFormatted = minutes > 0 
                ? `${minutes}分${seconds}秒` 
                : `${seconds}秒`;

            return {
                duration,
                durationFormatted
            };
        } catch (error) {
            logger.error(`[getVideoDuration] 获取视频时长失败: ${videoPath}, 错误: ${error.message}`);
            return {
                duration: 0,
                durationFormatted: '未知'
            };
        }
    }

    /**
     * @功能概述: 解析字幕文件信息
     * @参数说明:
     *   - subtitlePath: 字幕文件路径
     * @返回值: {entryCount, hasContent}
     */
    static async parseSubtitleInfo(subtitlePath) {
        try {
            const content = await fs.readFile(subtitlePath, 'utf8');
            const subtitleData = JSON.parse(content);
            
            // 检查是否为数组格式
            if (Array.isArray(subtitleData)) {
                return {
                    entryCount: subtitleData.length,
                    hasContent: subtitleData.length > 0
                };
            }
            
            return {
                entryCount: 0,
                hasContent: false
            };
        } catch (error) {
            logger.error(`[parseSubtitleInfo] 解析字幕文件失败: ${subtitlePath}, 错误: ${error.message}`);
            return {
                entryCount: 0,
                hasContent: false
            };
        }
    }

    /**
     * @功能概述: 提取视频文件完整信息
     * @参数说明:
     *   - videoFile: 视频文件对象
     * @返回值: 增强后的视频文件信息
     */
    static async extractVideoInfo(videoFile) {
        const functionName = 'extractVideoInfo';
        
        try {
            logger.debug(`[${functionName}] 开始提取视频信息: ${videoFile.filename}`);
            
            // 获取基本文件信息
            const basicInfo = await this.getFileBasicInfo(videoFile.path);
            
            // 获取视频时长
            const durationInfo = await this.getVideoDuration(videoFile.path);
            
            const result = {
                ...videoFile,
                size: basicInfo.sizeFormatted,
                duration: durationInfo.durationFormatted,
                modifiedTime: basicInfo.modifiedTime,
                // 保留原始数值用于排序等操作
                _rawSize: basicInfo.size,
                _rawDuration: durationInfo.duration
            };
            
            logger.debug(`[${functionName}] 视频信息提取完成: ${videoFile.filename}, 大小: ${result.size}, 时长: ${result.duration}`);
            return result;
            
        } catch (error) {
            logger.error(`[${functionName}] 提取视频信息失败: ${videoFile.filename}, 错误: ${error.message}`);
            return {
                ...videoFile,
                size: '未知',
                duration: '未知',
                modifiedTime: null,
                _rawSize: 0,
                _rawDuration: 0
            };
        }
    }

    /**
     * @功能概述: 提取字幕文件完整信息
     * @参数说明:
     *   - subtitleFile: 字幕文件对象
     * @返回值: 增强后的字幕文件信息
     */
    static async extractSubtitleInfo(subtitleFile) {
        const functionName = 'extractSubtitleInfo';
        
        try {
            logger.debug(`[${functionName}] 开始提取字幕信息: ${subtitleFile.filename}`);
            
            // 获取基本文件信息
            const basicInfo = await this.getFileBasicInfo(subtitleFile.path);
            
            // 解析字幕内容
            const subtitleInfo = await this.parseSubtitleInfo(subtitleFile.path);
            
            const result = {
                ...subtitleFile,
                size: basicInfo.sizeFormatted,
                entryCount: subtitleInfo.entryCount,
                hasContent: subtitleInfo.hasContent,
                modifiedTime: basicInfo.modifiedTime,
                // 保留原始数值
                _rawSize: basicInfo.size
            };
            
            logger.debug(`[${functionName}] 字幕信息提取完成: ${subtitleFile.filename}, 大小: ${result.size}, 条目: ${result.entryCount}`);
            return result;
            
        } catch (error) {
            logger.error(`[${functionName}] 提取字幕信息失败: ${subtitleFile.filename}, 错误: ${error.message}`);
            return {
                ...subtitleFile,
                size: '未知',
                entryCount: 0,
                hasContent: false,
                modifiedTime: null,
                _rawSize: 0
            };
        }
    }

    /**
     * @功能概述: 批量提取文件信息
     * @参数说明:
     *   - pairs: 配对结果数组
     * @返回值: 增强后的配对结果
     */
    static async extractPairsInfo(pairs) {
        const functionName = 'extractPairsInfo';
        logger.info(`[${functionName}] 开始批量提取 ${pairs.length} 个配对的文件信息`);
        
        const enhancedPairs = await Promise.all(
            pairs.map(async (pair) => {
                const enhancedPair = { ...pair };
                
                // 提取视频信息
                if (pair.video) {
                    enhancedPair.video = await this.extractVideoInfo(pair.video);
                }
                
                // 提取字幕信息
                if (pair.subtitle) {
                    enhancedPair.subtitle = await this.extractSubtitleInfo(pair.subtitle);
                }
                
                return enhancedPair;
            })
        );
        
        logger.info(`[${functionName}] 批量文件信息提取完成`);
        return enhancedPairs;
    }
}

module.exports = FileInfoExtractor;
