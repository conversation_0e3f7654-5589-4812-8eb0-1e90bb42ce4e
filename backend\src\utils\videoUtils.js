/**
 * @功能概述: 提供视频处理相关的工具函数。
 * @创建日期: 2025-05-26
 */

const { execFile } = require('child_process');
const path = require('path');
const logger = require('./logger'); // 假设logger在同级或可解析路径
const config = require('../config'); // 已修正为直接导入配置对象
const UPLOAD_DIR = config.uploadDir; // 修正：从config对象中直接获取uploadDir

const utilsModuleLogPrefix = '[文件：videoUtils.js][视频工具集][模块初始化]';
logger.info(`${utilsModuleLogPrefix}模块已加载。`); // 日志：确认 videoUtils 模块已加载。

/**
 * @功能概述: 使用 ffprobe 获取指定视频文件的宽度和高度。
 * @param {string} videoPath - 视频文件的绝对路径。
 * @returns {Promise<{width: number, height: number}>} 一个 Promise，resolve 时返回一个包含 { width: number, height: number } 的对象。
 *                                                   如果失败，则 reject 一个带详细错误信息的 Error 对象。
 * @throws {Error} 如果 videoPath 无效或 ffprobe 执行失败等。
 * @依赖: ffprobe (FFmpeg 工具套件的一部分) 必须在系统路径中或通过配置指定。
 * @执行流程:
 *   1. 校验 videoPath 参数。
 *   2. 确定 ffprobe 可执行文件的路径。
 *   3. 构建 ffprobe 命令参数。
 *   4. 使用 child_process.execFile 执行 ffprobe 命令。
 *   5. 解析 ffprobe 的 JSON 输出。
 *   6. 提取并校验 width 和 height。
 *   7. 返回包含 width 和 height 的对象或抛出错误。
 */
async function getVideoDimensions(videoPath) {
    const funcLogPrefix = `${utilsModuleLogPrefix}[getVideoDimensions]`;
    logger.info(`${funcLogPrefix} 开始获取视频尺寸，路径: "${videoPath}"`); // 日志：记录函数调用开始和视频路径。

    if (!videoPath || typeof videoPath !== 'string') {
        const errMsg = '无效的 videoPath 参数。必须是一个非空字符串。';
        logger.error(`${funcLogPrefix}[参数错误] ${errMsg}`); // 日志：记录 videoPath 参数无效的错误。
        return Promise.reject(new Error(errMsg));
    }

    // 向上两级是 src，再向上一级是 backend，目标是 backend/src/utils/logger.js
    // 对于 videoPath, 假设它是相对于项目根目录的，或者是一个绝对路径
    // fs.existsSync 需要绝对路径或者相对于 process.cwd() 的路径
    // 由于此工具函数可能在不同上下文调用，直接依赖传入的 videoPath 是否可访问

    const ffprobeExecutable = config.ffprobePath || 'ffprobe'; // 从配置获取或使用默认
    logger.debug(`${funcLogPrefix} 使用 ffprobe 执行路径: ${ffprobeExecutable}`); // 日志：记录使用的 ffprobe 执行文件路径。

    const args = [
        '-v', 'error',              // 只输出错误信息
        '-select_streams', 'v:0',   // 只选择第一个视频流
        '-show_entries', 'stream=width,height,display_aspect_ratio,sample_aspect_ratio,duration,bit_rate,codec_name,r_frame_rate', // 获取更多信息以备将来使用
        '-of', 'json',              // 输出格式为 JSON
        videoPath                   // 视频文件路径作为最后一个参数
    ];

    logger.debug(`${funcLogPrefix} 执行 ffprobe 命令参数: ${JSON.stringify(args.slice(0, -1))} "${path.basename(videoPath)}"`); // 日志：记录执行的命令参数（隐藏完整路径）。

    return new Promise((resolve, reject) => {
        execFile(ffprobeExecutable, args, (error, stdout, stderr) => {
            if (error) {
                const errMsg = `ffprobe 执行失败。代码: ${error.code}, 信号: ${error.signal}. Path: "${videoPath}"`;
                logger.error(`${funcLogPrefix}[FFPROBE_EXEC_ERROR] ${errMsg}`); // 日志：记录 ffprobe 执行失败。
                if (stderr) logger.error(`${funcLogPrefix}[FFPROBE_STDERR] ${stderr.trim()}`); // 日志：记录 ffprobe 的 stderr。
                return reject(new Error(`${errMsg}
FFprobe stderr: ${stderr ? stderr.trim() : 'N/A'}`));
            }

            if (stderr && stderr.trim() !== '') {
                // 有些情况下，ffprobe 可能在 stderr 中输出警告或其他信息，即使命令成功。
                logger.warn(`${funcLogPrefix}[FFPROBE_STDERR_NON_FATAL] ffprobe stderr 有输出 (非致命错误或警告): ${stderr.trim()}`); // 日志：记录 ffprobe 的非致命 stderr 输出。
            }
            
            logger.debug(`${funcLogPrefix}[FFPROBE_STDOUT_RAW] ffprobe stdout (原始): ${stdout.substring(0, 500)}${stdout.length > 500 ? '...' : ''}`); // 日志：记录 ffprobe 的 stdout 原始输出（截断）。

            let ffprobeData;
            try {
                ffprobeData = JSON.parse(stdout);
            } catch (parseError) {
                const errMsg = `解析 ffprobe JSON 输出失败。Error: ${parseError.message}. Path: "${videoPath}"`;
                logger.error(`${funcLogPrefix}[JSON_PARSE_ERROR] ${errMsg}`); // 日志：记录 JSON 解析失败。
                logger.debug(`${funcLogPrefix}[FFPROBE_STDOUT_FULL_ON_PARSE_ERROR] ffprobe stdout (完整): ${stdout}`); // 日志：在解析错误时记录完整的 stdout。
                return reject(new Error(`${errMsg}
FFprobe stdout: ${stdout.substring(0,500)}...`));
            }

            if (!ffprobeData.streams || ffprobeData.streams.length === 0) {
                const errMsg = `在 ffprobe 输出中未找到视频流。Path: "${videoPath}"`;
                logger.error(`${funcLogPrefix}[NO_VIDEO_STREAM] ${errMsg}`); // 日志：记录未找到视频流的错误。
                return reject(new Error(errMsg));
            }

            const videoStream = ffprobeData.streams[0];
            const { width, height, duration, bit_rate, codec_name, r_frame_rate, display_aspect_ratio, sample_aspect_ratio } = videoStream;

            if (typeof width !== 'number' || width <= 0 || typeof height !== 'number' || height <= 0) {
                const errMsg = `从 ffprobe 获取的视频尺寸无效。Width: ${width}, Height: ${height}. Path: "${videoPath}"`;
                logger.error(`${funcLogPrefix}[INVALID_DIMENSIONS] ${errMsg}`); // 日志：记录获取的视频尺寸无效。
                return reject(new Error(errMsg));
            }
            
            const dimensions = {
                width,
                height,
                duration: duration ? parseFloat(duration) : null,
                bit_rate: bit_rate ? parseInt(bit_rate, 10) : null,
                codec_name: codec_name || null,
                frame_rate: r_frame_rate || null,
                display_aspect_ratio: display_aspect_ratio || null,
                sample_aspect_ratio: sample_aspect_ratio || null,
            };

            logger.info(`${funcLogPrefix} 成功获取视频尺寸及元数据。Path: "${videoPath}", 尺寸: ${width}x${height}, 时长: ${dimensions.duration}s`); // 日志：记录成功获取视频尺寸。
            resolve(dimensions);
        });
    });
}

module.exports = {
    getVideoDimensions
};

logger.info(`${utilsModuleLogPrefix}模块功能已导出: getVideoDimensions。`); // 日志：确认模块功能已导出。 