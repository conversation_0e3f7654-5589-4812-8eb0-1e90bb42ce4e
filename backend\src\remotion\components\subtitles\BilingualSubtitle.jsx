/**
 * @功能概述: 双语字幕组件，纯渲染组件，所有样式通过props传入
 * @组件类型: Remotion字幕组件（数据驱动）
 * @使用场景: 教育类视频的字幕显示
 */

import React from 'react';
import { useCurrentFrame, useVideoConfig } from 'remotion';

/**
 * @功能概述: 双语字幕组件（纯渲染器）
 * @param {Object} props - 组件属性
 * @param {Array} props.subtitleData - 字幕数据数组
 * @param {Object} props.config - 字幕配置对象（来自template）
 * @param {number} props.textAreaStartTime - 文本区域开始时间（秒）
 * @returns {JSX.Element} 字幕组件
 */
const BilingualSubtitle = ({
    subtitleData = [],
    config = {},
    textAreaStartTime = 3
}) => {
    const frame = useCurrentFrame();
    const { fps } = useVideoConfig();
    const currentTime = frame / fps;

    // 查找当前时间对应的字幕
    const currentSubtitle = subtitleData.find(sub =>
        currentTime >= sub.start && currentTime <= sub.end
    );

    if (!currentSubtitle) return null;

    // 判断是否在文本区域内显示
    const showInTextArea = currentTime > textAreaStartTime;

    // 获取样式配置（完全来自props，无默认值）
    const englishStyle = config.english || {};
    const chineseStyle = config.chinese || {};
    const keywordsStyle = config.keywords || {};

    return (
        <div style={{
            position: 'absolute',
            top: showInTextArea ? '60%' : '70%',
            left: '5%',
            right: '5%',
            textAlign: 'center',
            zIndex: 10
        }}>
            {/* 英文字幕 */}
            <div style={{
                color: englishStyle.color,
                fontSize: showInTextArea ?
                    englishStyle.fontSize?.inTextArea :
                    englishStyle.fontSize?.inVideo,
                fontFamily: englishStyle.fontFamily,
                fontWeight: englishStyle.fontWeight,
                marginBottom: '8px',
                textShadow: englishStyle.textShadow,
                backgroundColor: showInTextArea ?
                    englishStyle.backgroundColor?.inTextArea :
                    englishStyle.backgroundColor?.inVideo,
                padding: showInTextArea ?
                    englishStyle.padding?.inTextArea :
                    englishStyle.padding?.inVideo,
                borderRadius: showInTextArea ?
                    englishStyle.borderRadius?.inTextArea :
                    englishStyle.borderRadius?.inVideo
            }}>
                {currentSubtitle.english}
            </div>

            {/* 中文字幕 */}
            <div style={{
                color: chineseStyle.color,
                fontSize: showInTextArea ?
                    chineseStyle.fontSize?.inTextArea :
                    chineseStyle.fontSize?.inVideo,
                fontFamily: chineseStyle.fontFamily,
                fontWeight: chineseStyle.fontWeight,
                textShadow: chineseStyle.textShadow,
                backgroundColor: showInTextArea ?
                    chineseStyle.backgroundColor?.inTextArea :
                    chineseStyle.backgroundColor?.inVideo,
                padding: showInTextArea ?
                    chineseStyle.padding?.inTextArea :
                    chineseStyle.padding?.inVideo,
                borderRadius: showInTextArea ?
                    chineseStyle.borderRadius?.inTextArea :
                    chineseStyle.borderRadius?.inVideo
            }}>
                {currentSubtitle.chinese}
            </div>

            {/* 关键词高亮 */}
            {keywordsStyle.enabled && currentSubtitle.keywords && currentSubtitle.keywords.length > 0 && (
                <div style={{
                    color: keywordsStyle.color,
                    fontSize: showInTextArea ?
                        keywordsStyle.fontSize?.inTextArea :
                        keywordsStyle.fontSize?.inVideo,
                    fontFamily: keywordsStyle.fontFamily,
                    fontWeight: keywordsStyle.fontWeight,
                    marginTop: '8px',
                    textShadow: keywordsStyle.textShadow
                }}>
                    {keywordsStyle.prefix}{currentSubtitle.keywords.join(', ')}
                </div>
            )}
        </div>
    );
};

export default BilingualSubtitle;
