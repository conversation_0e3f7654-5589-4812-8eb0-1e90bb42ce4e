/**
 * @文件名: GenerateASSTask.js
 * @功能概述: ASS字幕文件生成任务 - 完全照抄generate-aas.test.js的逻辑
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 *
 * @功能描述:
 *   基于generate-aas.test.js的逻辑，生成一个完整的ASS格式字幕文件。
 *   包含视频标题、填空字幕、双语字幕（含关键词翻译）、单元引导字幕等所有类型。
 *   使用video-config.json配置文件定义样式和布局。
 *   通过css-to-ass-mapper.js工具将CSS样式转换为ASS格式。
 *   将生成的ASS内容保存到context中，供后续任务直接使用。
 *
 * @依赖模块:
 *   - TaskBase: 任务基类
 *   - css-to-ass-mapper: CSS到ASS样式映射工具
 *   - video-config.json: 视频配置文件
 *   - Canvas: 文本测量引擎
 *   - spawn: FFprobe音频时长获取
 *
 * @上下文输入 (Context Input):
 *   必需字段:
 *   - videoIdentifier: {string} 视频唯一标识符
 *   - audioFilePath: {string} 音频文件路径 (来自ConvertToAudioTask)
 *   - clozedSubtitleJsonArray: {Array} 挖空字幕数组 (来自SubtitleClozeTask)
 *     └── 结构: [{id, start, end, text, words}, ...]
 *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组 (来自BilingualSubtitleMergeTask)
 *     └── 结构: [{id, start, end, text_english, text_chinese, words_explanation}, ...]
 *   - savePath: {string} 文件保存路径
 *
 * @上下文输出 (Context Output):
 *   新增字段:
 *   - assFilePath: {string} 生成的ASS字幕文件完整路径
 *   - assContent: {string} 完整的ASS字幕内容 (供后续任务直接使用，无需读取文件)
 *   - assContentLength: {number} ASS内容长度（字符数）
 *   - videoConfig: {Object} 完整的视频配置对象
 *   - audioDuration: {number} 音频时长（秒）
 *   - processedSubtitles: {Object} 处理统计信息
 *     └── {clozedCount, bilingualCount, videoTitleCount, repeatModeGuideCount}
 *
 * @关键词翻译功能:
 *   - 完全照抄generate-aas.test.js的processEnglishKeywordsWithTranslation逻辑
 *   - 支持words_explanation对象的关键词翻译处理
 *   - 生成ASS特效标签: {\1c&H00FFFF00}keyword{\fs30} [翻译]{\r}
 *   - 关键词按长度降序排序，避免短词覆盖长词匹配
 *   - 支持单词边界匹配和短语精确匹配
 */

const TaskBase = require('../class/TaskBase');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const { createCanvas } = require('canvas');
const { CSSToASSMapper } = require('../utils/video/css-to-ass-mapper');

/**
 * @类名: GenerateASSTask
 * @继承: TaskBase
 * @功能: 生成ASS格式字幕文件的任务类 - 照抄generate-aas.test.js逻辑
 */
class GenerateASSTask extends TaskBase {
    constructor() {
        super('GenerateASSTask');
        this.instanceLogPrefix = `[${this.taskName}][${this.instanceId}]`;

        // 注意：不再从文件读取配置，而是从context中获取videoConfig对象
        this.cssMapper = null; // 将在execute方法中初始化

        // Canvas测量引擎缓存
        this.textMeasurementCache = new Map();
    }

    /**
     * @功能概述: 执行ASS字幕生成任务 - 完全照抄generate-aas.test.js的main函数逻辑
     * @参数说明:
     *   - context: {object} 任务上下文，包含以下必需字段:
     *                       - videoIdentifier: {string} 视频唯一标识符 (必需)。
     *                       - audioFilePath: {string} 音频文件路径 (必需)。
     *                       - clozedSubtitleJsonArray: {Array} 挖空字幕JSON数组 (必需)。
     *                       - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕JSON数组 (必需)。
     *                       - savePath: {string} 文件保存路径 (必需)。
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度。
     * @returns {Promise<object>} 包含ASS文件路径和配置信息的对象。
     * @throws {Error} 如果参数校验失败、配置加载失败、ASS生成失败或文件保存失败时，则抛出错误。
     * @执行流程: 完全照抄generate-aas.test.js的main函数逻辑
     */
    async execute(context, progressCallback) {
        const execLogPrefix = `${this.instanceLogPrefix}[execute]`;

        try {
            // 步骤 1: 初始化和进度报告
            logger.info(`${execLogPrefix} ========== 开始JSON到ASS转换任务 ==========`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '初始化ASS字幕生成任务',
                current: 0,
                total: 100
            });

            // 步骤 2: 验证必需的输入参数
            logger.info(`${execLogPrefix}[步骤 1] 验证输入参数...`);
            const requiredFields = [
                'videoIdentifier',
                'audioFilePath',
                'clozedSubtitleJsonArray',
                'enhancedBilingualSubtitleJsonArray',
                'savePath',
                'videoConfig'
            ];
            this.validateRequiredFields(context, requiredFields, execLogPrefix);

            const {
                videoIdentifier,
                audioFilePath,
                clozedSubtitleJsonArray,
                enhancedBilingualSubtitleJsonArray,
                savePath,
                videoConfig
            } = context;

            // 步骤 2.1: 初始化CSS映射器（使用context中的videoConfig）
            logger.info(`${execLogPrefix}[步骤 1.1] 初始化CSS映射器...`);
            this.cssMapper = new CSSToASSMapper(null, videoConfig); // 传入videoConfig对象而不是文件路径
            logger.info(`${execLogPrefix} CSS映射器初始化成功`);

            // 模拟context对象（照抄generate-aas.test.js的loadSubtitleData逻辑）
            const subtitleContext = {
                clozedSubtitleJsonArray: clozedSubtitleJsonArray,
                bilingualSubtitleJsonArray: enhancedBilingualSubtitleJsonArray
            };

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '参数验证完成',
                current: 10,
                total: 100
            });

            // 步骤 3: 使用context中的配置对象（不再从文件加载）
            logger.info(`${execLogPrefix}[步骤 2] 使用context中的videoConfig配置对象...`);
            const config = videoConfig; // 直接使用context中的配置
            logger.info(`${execLogPrefix} 配置对象获取成功`);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '配置文件加载完成',
                current: 20,
                total: 100
            });

            // 步骤 4: 获取音频时长（照抄generate-aas.test.js的getAudioDuration逻辑）
            logger.info(`${execLogPrefix}[步骤 3] 获取音频时长信息...`);
            const originalAudioDuration = await this.getAudioDuration(audioFilePath);
            logger.info(`${execLogPrefix} 音频时长: ${originalAudioDuration.toFixed(2)}秒`);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '音频时长获取完成',
                current: 30,
                total: 100
            });

            // 步骤 5: 生成视频引导语JSON数据（照抄generate-aas.test.js的generateSingleElementJson逻辑）
            logger.info(`${execLogPrefix}[步骤 4] 生成视频引导语JSON数据...`);
            const videoTitleJsonArray = await this.generateVideoTitleJson(config, originalAudioDuration);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '视频引导语JSON数据生成完成',
                current: 40,
                total: 100
            });

            // 步骤 6: 生成单元引导字幕JSON数据（照抄generate-aas.test.js的generateRepeatModeGuideJson逻辑）
            logger.info(`${execLogPrefix}[步骤 5] 生成单元引导字幕JSON数据...`);
            const repeatModeGuideJsonArray = await this.generateRepeatModeGuideJson(config.repeatCount, originalAudioDuration);

            // 步骤 6.1: 修复字幕时间重叠问题
            logger.info(`${execLogPrefix}[步骤 5.1] 修复字幕时间重叠问题...`);
            this.fixSubtitleTimeOverlaps(clozedSubtitleJsonArray, enhancedBilingualSubtitleJsonArray, originalAudioDuration, config);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '单元引导字幕JSON数据生成完成',
                current: 50,
                total: 100
            });

            // 步骤 7: 根据repeatModes处理字幕数据时间偏移（照抄generate-aas.test.js的processSubtitleDataByRepeatModes逻辑）
            logger.info(`${execLogPrefix}[步骤 6] 根据repeatModes处理字幕数据时间偏移...`);
            const processedContext = await this.processSubtitleDataByRepeatModes(subtitleContext, originalAudioDuration, config);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '字幕数据时间偏移处理完成',
                current: 60,
                total: 100
            });

            // 步骤 8: 模拟完整的context对象（照抄generate-aas.test.js的fullContext逻辑）
            logger.info(`${execLogPrefix}[步骤 7] 模拟完整context对象...`);
            const fullContext = {
                clozedSubtitleJsonArray: processedContext.clozedSubtitleJsonArray,
                bilingualSubtitleJsonArray: processedContext.bilingualSubtitleJsonArray,
                videoTitleJsonArray: videoTitleJsonArray,
                repeatModeGuideJsonArray: repeatModeGuideJsonArray,
                SAVE_PATH: savePath
            };

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '完整context对象模拟完成',
                current: 70,
                total: 100
            });

            // 步骤 9: 生成ASS字幕文件（照抄generate-aas.test.js的generateAssSubtitle逻辑）
            logger.info(`${execLogPrefix}[步骤 8] 生成ASS字幕文件...`);
            const assFilePath = await this.generateAssSubtitle(fullContext, config, videoIdentifier, originalAudioDuration);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: 'ASS字幕文件生成完成',
                current: 90,
                total: 100
            });

            // 步骤 10: 构建任务结果
            const taskResult = {
                // ASS文件信息
                assFilePath: assFilePath,
                assContent: fullContext.assContent,
                assContentLength: fullContext.assContentLength,

                // 配置信息
                videoConfig: config,
                audioDuration: originalAudioDuration,

                // 处理统计
                processedSubtitles: {
                    clozedCount: fullContext.clozedSubtitleJsonArray ? fullContext.clozedSubtitleJsonArray.length : 0,
                    bilingualCount: fullContext.bilingualSubtitleJsonArray ? fullContext.bilingualSubtitleJsonArray.length : 0,
                    videoTitleCount: fullContext.videoTitleJsonArray ? fullContext.videoTitleJsonArray.length : 0,
                    repeatModeGuideCount: fullContext.repeatModeGuideJsonArray ? fullContext.repeatModeGuideJsonArray.length : 0
                },

                // 任务信息
                videoIdentifier: videoIdentifier,
                savePath: savePath,
                taskStatus: 'completed',
                taskResult: 'success'
            };

            this.reportProgress(TASK_STATUS.COMPLETED, TASK_SUBSTATUS.COMPLETED, {
                detail: 'ASS字幕生成任务完成',
                current: 100,
                total: 100
            });

            logger.info(`${execLogPrefix} ========== ASS字幕文件生成完成 ==========`);
            logger.info(`${execLogPrefix} ✅ ASS文件生成成功: ${assFilePath}`);
            logger.info(`${execLogPrefix} ✅ 包含视频标题字幕 (${taskResult.processedSubtitles.videoTitleCount} 条)`);
            logger.info(`${execLogPrefix} ✅ 包含填空字幕 (${taskResult.processedSubtitles.clozedCount} 条)`);
            logger.info(`${execLogPrefix} ✅ 包含双语字幕 (${taskResult.processedSubtitles.bilingualCount} 条)`);
            logger.info(`${execLogPrefix} ✅ 包含单元引导字幕 (${taskResult.processedSubtitles.repeatModeGuideCount} 条)`);

            this.complete(taskResult);
            return taskResult;

        } catch (error) {
            this.fail(error);
            logger.error(`${execLogPrefix} ASS字幕生成任务执行失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出错误
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (!context[field]) {
                const errorMsg = `执行失败：上下文缺少必需字段 ${field}`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。`);
    }

    /**
     * @功能概述: 英文关键词处理函数 - 添加中文翻译注释并创建黄色背景按钮效果
     * @参数说明:
     *   - text: {string} 原始英文文本（需要处理的字幕内容）
     *   - wordsExplanation: {Object} 关键词解释对象（键为英文单词/短语，值为对应中文翻译）
     *   - keywordBlockStyle: {Object} 关键词块样式配置（可选）
     * @返回值: {string} 处理后的包含ASS特效标签的文本
     * @技术实现:
     *   - 步骤1：预处理 - 验证输入参数有效性，按关键词长度降序排序
     *   - 步骤2：正则匹配 - 根据关键词类型（单词/短语）构建不同匹配模式
     *   - 步骤3：文本替换 - 在匹配到的关键词后添加中文翻译注释
     *   - 步骤4：样式处理 - 通过ASS特效标签实现字体缩小和样式重置
     */
    processEnglishKeywordsWithTranslation(text, wordsExplanation, keywordBlockStyle = null) {
        const functionName = 'processEnglishKeywordsWithTranslation';

        try {
            // 输入验证
            if (!text || typeof text !== 'string') {
                logger.warn(`[${functionName}] 无效的文本输入，返回空字符串`);
                return '';
            }

            if (!wordsExplanation || typeof wordsExplanation !== 'object') {
                logger.info(`[${functionName}] 无关键词解释对象，返回原始文本`);
                return text;
            }

            // 创建文本处理副本，保留原始文本不可变
            let processedText = text;

            // 关键词预处理：按长度降序排序（关键算法：避免短词覆盖长词匹配）
            const sortedKeywords = Object.keys(wordsExplanation).sort((a, b) => b.length - a.length);

            logger.debug(`[${functionName}] 开始处理英文关键词翻译: ${sortedKeywords.length}个关键词`);
            logger.debug(`[${functionName}] 关键词列表: [${sortedKeywords.join(', ')}]`);
            logger.debug(`[${functionName}] 原始文本: "${text}"`);

            // 主处理循环：遍历所有已排序的关键词
            for (const englishKeyword of sortedKeywords) {
                // 获取当前关键词对应的中文翻译
                const chineseTranslation = wordsExplanation[englishKeyword];

                // 空值跳过：确保关键词和翻译都存在
                if (!englishKeyword || !chineseTranslation) {
                    continue;
                }

                // 文本清理：去除首尾空白字符
                const trimmedKeyword = englishKeyword.trim();
                const trimmedTranslation = chineseTranslation.trim();

                // 构建正则表达式：根据关键词类型选择匹配模式
                let keywordRegex;
                if (trimmedKeyword.includes(' ')) {
                    // 短语处理：直接精确匹配
                    keywordRegex = new RegExp(`(${this.escapeRegExp(trimmedKeyword)})`, 'gi');
                } else {
                    // 单词处理：使用单词边界匹配
                    keywordRegex = new RegExp(`\\b(${this.escapeRegExp(trimmedKeyword)})\\b`, 'gi');
                }

                // 匹配检查：判断当前文本是否包含关键词
                if (keywordRegex.test(processedText)) {
                    // 重置正则表达式状态（重要：避免test()影响后续replace()）
                    keywordRegex.lastIndex = 0;

                    // 构建ASS特效替换模板
                    let buttonEffect;
                    if (keywordBlockStyle && keywordBlockStyle.color) {
                        // 使用配置的颜色
                        const keywordColor = this.cssMapper.convertColorToASS(keywordBlockStyle.color);
                        buttonEffect = `{\\1c${keywordColor}}$1{\\fs30} [${trimmedTranslation}]{\\r}`;
                    } else {
                        // 使用默认样式（青色高亮）
                        buttonEffect = `{\\1c&H00FFFF00}$1{\\fs30} [${trimmedTranslation}]{\\r}`;
                    }

                    // 执行替换操作
                    processedText = processedText.replace(keywordRegex, buttonEffect);

                    logger.debug(`[${functionName}] ✅ 处理关键词: "${trimmedKeyword}" → "[${trimmedTranslation}]"`);
                } else {
                    logger.debug(`[${functionName}] ❌ 未找到关键词: "${trimmedKeyword}" 在文本中`);
                }
            }

            logger.debug(`[${functionName}] 英文关键词处理完成: "${text.substring(0, 30)}..." → 包含${sortedKeywords.length}个翻译注释`);
            logger.debug(`[${functionName}] 处理后文本: "${processedText}"`);

            return processedText;

        } catch (error) {
            logger.error(`[${functionName}] 英文关键词处理失败: ${error.message}`);
            return text; // 降级处理：返回原始文本保证基本功能可用
        }
    }

    /**
     * @功能概述: 正则表达式特殊字符转义函数
     * @参数说明:
     *   - string: {string} 需要转义的字符串
     * @返回值: {string} 转义后的字符串
     * @用途: 确保关键词中的特殊字符不会被正则表达式误解释
     */
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * @功能概述: 处理包含emoji的文本，保持emoji彩色显示
     * @参数说明:
     *   - text: {string} 包含emoji的文本
     * @返回值: {string} 处理后的文本，emoji保持彩色显示
     * @技术实现:
     *   - 检测文本中的emoji字符
     *   - 对于emoji使用特殊的ASS标签确保彩色显示
     *   - 对于普通文字应用正常的字体样式
     */
    processEmojiText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // emoji正则表达式，匹配常见的emoji字符
        const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
        
        // 检查文本是否包含emoji
        if (!emojiRegex.test(text)) {
            // 没有emoji，直接返回原文本
            return text;
        }

        // 重置正则表达式
        emojiRegex.lastIndex = 0;
        
        // 分离emoji和普通文字
        let processedText = text;
        let match;
        const emojiPositions = [];
        
        while ((match = emojiRegex.exec(text)) !== null) {
            emojiPositions.push({
                emoji: match[0],
                index: match.index
            });
        }

        // 如果有emoji，为它们添加特殊处理
        if (emojiPositions.length > 0) {
            // 简化处理：直接返回原文本，让ASS渲染器处理emoji
            // 现代ASS渲染器（如VSFilter、xy-VSFilter）通常能正确显示emoji
            return text;
        }

        return text;
    }

    /**
     * @功能概述: 为ASS字幕处理emoji显示优化 - 基于深度技术研究的最终方案
     * @参数说明:
     *   - text: 需要处理的文本内容
     * @返回值: 处理后的文本，保留emoji但避免libass冲突
     * @技术方案:
     *   基于对libass、FreeType和emoji渲染的深入研究：
     *   1. libass确实不支持彩色emoji（CBDT/CBLC、SVG-in-OT等格式）
     *   2. 但libass会使用系统字体fallback机制
     *   3. 关键是不要干扰libass的字体选择，让它自然fallback
     *   4. 避免使用任何可能冲突的ASS标签
     */
    processEmojiForASS(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // 简单emoji检测 - 专注最常用范围
        const emojiRegex = /[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
        
        // 如果文本中没有emoji，直接返回
        if (!emojiRegex.test(text)) {
            return text;
        }

        // 关键技术实现：
        // 1. 保留原始emoji字符，不做任何替换
        // 2. 不添加任何ASS字体标签（这些标签会干扰libass的fallback机制）
        // 3. 让libass自然使用系统的emoji字体fallback
        // 4. 这是唯一可能在某些系统上显示彩色emoji的方法
        
        // 如果系统有正确配置的emoji字体（如Noto Color Emoji、Apple Color Emoji等）
        // 并且libass/FreeType支持该字体格式，emoji就会正确显示
        // 如果不支持，至少会显示为单色emoji轮廓，比文字替代好
        
        return text; // 保持原始emoji不变，让系统处理
    }

    /**
     * @功能概述: 获取音频文件的时长信息 - 完全照抄generate-aas.test.js的getAudioDuration函数
     * @参数说明:
     *   - audioPath: 音频文件路径
     * @返回值: Promise<number> - 音频时长（秒）
     * @技术实现:
     *   1. 使用FFprobe获取音频文件信息
     *   2. 解析JSON格式输出
     *   3. 从format或streams中提取duration
     *   4. 返回时长数值（秒）
     */
    async getAudioDuration(audioPath) {
        const functionName = 'getAudioDuration';

        return new Promise((resolve, reject) => {
            try {
                logger.info(`[${functionName}] 开始获取音频时长信息...`);
                logger.info(`[${functionName}] 音频文件路径: ${audioPath}`);

                // 构建FFprobe命令参数 - 获取JSON格式的媒体信息
                const ffprobeArgs = [
                    '-v', 'quiet',                    // 静默模式，减少输出
                    '-print_format', 'json',          // 输出JSON格式
                    '-show_format',                   // 显示格式信息
                    '-show_streams',                  // 显示流信息
                    audioPath                         // 音频文件路径
                ];

                logger.info(`[${functionName}] FFprobe命令: ffprobe ${ffprobeArgs.join(' ')}`);

                // 启动FFprobe进程
                const ffprobeProcess = spawn('ffprobe', ffprobeArgs);

                let ffprobeOutput = '';
                let ffprobeError = '';

                // 监听stdout输出（JSON数据）
                ffprobeProcess.stdout.on('data', (data) => {
                    ffprobeOutput += data.toString();
                });

                // 监听stderr输出（错误信息）
                ffprobeProcess.stderr.on('data', (data) => {
                    ffprobeError += data.toString();
                });

                // 监听进程退出事件
                ffprobeProcess.on('close', (code) => {
                    if (code === 0) {
                        try {
                            // 解析JSON输出
                            const mediaInfo = JSON.parse(ffprobeOutput);

                            // 从format信息中获取时长
                            let duration = null;
                            if (mediaInfo.format && mediaInfo.format.duration) {
                                duration = parseFloat(mediaInfo.format.duration);
                            }

                            // 如果format中没有时长，尝试从音频流中获取
                            if (!duration && mediaInfo.streams) {
                                const audioStream = mediaInfo.streams.find(stream => stream.codec_type === 'audio');
                                if (audioStream && audioStream.duration) {
                                    duration = parseFloat(audioStream.duration);
                                }
                            }

                            if (duration && duration > 0) {
                                logger.info(`[${functionName}] 音频时长获取成功: ${duration.toFixed(2)} 秒`);
                                resolve(duration);
                            } else {
                                const errorMsg = '无法获取有效的音频时长信息，使用默认时长';
                                logger.warn(`[${functionName}] ${errorMsg}`);
                                resolve(60); // 使用默认时长60秒
                            }

                        } catch (parseError) {
                            const errorMsg = `解析音频信息失败，使用默认时长: ${parseError.message}`;
                            logger.warn(`[${functionName}] ${errorMsg}`);
                            logger.error(`[${functionName}] FFprobe输出: ${ffprobeOutput}`);
                            resolve(60); // 使用默认时长60秒
                        }
                    } else {
                        const errorMsg = `FFprobe执行失败，退出码: ${code}，使用默认时长`;
                        logger.warn(`[${functionName}] ${errorMsg}`);
                        logger.error(`[${functionName}] FFprobe错误输出: ${ffprobeError}`);
                        resolve(60); // 使用默认时长60秒
                    }
                });

                // 监听进程启动错误
                ffprobeProcess.on('error', (error) => {
                    const errorMsg = `FFprobe进程启动失败，使用默认时长: ${error.message}`;
                    logger.warn(`[${functionName}] ${errorMsg}`);
                    resolve(60); // 使用默认时长60秒
                });

            } catch (error) {
                logger.warn(`[${functionName}] 获取音频时长失败，使用默认时长: ${error.message}`);
                resolve(60); // 使用默认时长60秒
            }
        });
    }

    /**
     * @功能概述: 生成视频引导语JSON数据（只有一个元素）- 完全照抄generate-aas.test.js的generateSingleElementJson函数
     * @返回值: Promise<Array> - 包含一个元素的数组，如果videoGuide.enabled为false则返回空数组
     * @数据结构:
     *   - id: 固定为 "1"
     *   - start: 固定为 0
     *   - end: 音频时长 * VIDEO_CONFIG.repeatCount
     *   - text_1: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.title1
     *   - text_2: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.title2
     *   - style: 来自VIDEO_CONFIG.subtitleConfig.videoGuide.style
     */
    async generateVideoTitleJson(config, originalAudioDuration) {
        const functionName = 'generateVideoTitleJson';

        try {
            logger.info(`[${functionName}] 开始生成视频引导语JSON数据...`);

            // 检查videoGuide是否启用
            const videoGuideConfig = config.subtitleConfig.videoGuide;
            if (!videoGuideConfig.enabled) {
                logger.info(`[${functionName}] 视频引导语已禁用，返回空数组`);
                return [];
            }

            // 计算总时长（原时长 * 重复次数）
            const totalDuration = originalAudioDuration * config.repeatCount;
            logger.info(`[${functionName}] 原音频时长: ${originalAudioDuration.toFixed(2)} 秒`);
            logger.info(`[${functionName}] 重复次数: ${config.repeatCount}`);
            logger.info(`[${functionName}] 总时长: ${totalDuration.toFixed(2)} 秒`);

            // 生成视频引导语JSON数据
            const videoGuideData = [
                {
                    "id": "1",
                    "start": 0,
                    "end": totalDuration,
                    "text_1": videoGuideConfig.title1,
                    "text_2": videoGuideConfig.title2,
                    "style": videoGuideConfig.style
                }
            ];

            logger.info(`[${functionName}] 视频引导语JSON数据生成完成`);
            logger.info(`[${functionName}] 数据结构:`, Object.keys(videoGuideData[0]));
            logger.info(`[${functionName}] id: ${videoGuideData[0].id}`);
            logger.info(`[${functionName}] start: ${videoGuideData[0].start}`);
            logger.info(`[${functionName}] end: ${videoGuideData[0].end}`);
            logger.info(`[${functionName}] text_1: "${videoGuideData[0].text_1}"`);
            logger.info(`[${functionName}] text_2: "${videoGuideData[0].text_2}"`);
            logger.info(`[${functionName}] style配置:`, JSON.stringify(videoGuideData[0].style, null, 2));

            return videoGuideData;

        } catch (error) {
            logger.error(`[${functionName}] 生成视频引导语JSON数据失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 生成单元引导字幕JSON数据 - 完全照抄generate-aas.test.js的generateRepeatModeGuideJson函数
     * @参数说明:
     *   - repeatCount: 重复次数，用于确定生成多少个引导字幕
     *   - originalAudioDuration: 原始音频时长（秒）
     * @返回值: Promise<Array> - 包含单元引导字幕的数组
     * @数据结构:
     *   - id: 字符串，从"1"开始递增
     *   - start: 开始时间（秒）
     *   - end: 结束时间（秒）
     *   - text: 显示文本，来自repeatModes[i].displayText
     * @时间逻辑:
     *   - 第1个元素: start=0, end=1*audioDuration
     *   - 第2个元素: start=1*audioDuration, end=2*audioDuration
     *   - 第n个元素: start=(n-1)*audioDuration, end=n*audioDuration
     */
    async generateRepeatModeGuideJson(repeatCount, originalAudioDuration) {
        const functionName = 'generateRepeatModeGuideJson';

        try {
            logger.info(`[${functionName}] 开始生成单元引导字幕JSON数据...`);
            logger.info(`[${functionName}] 重复次数: ${repeatCount}`);
            logger.info(`[${functionName}] 原音频时长: ${originalAudioDuration.toFixed(2)} 秒`);

            // 加载配置
            const config = await this.cssMapper.loadConfig();

            // 检查repeatModes数量和repeatCount是否一致
            const repeatModes = config.subtitleConfig.repeatModes;
            if (repeatModes.length !== repeatCount) {
                throw new Error(`repeatModes数量(${repeatModes.length})与repeatCount(${repeatCount})不一致`);
            }

            logger.info(`[${functionName}] ✅ repeatModes数量验证通过: ${repeatModes.length} 个模式`);

            // 生成单元引导字幕数据
            const repeatModeGuideData = [];

            for (let i = 0; i < repeatCount; i++) {
                const mode = repeatModes[i];
                const startTime = i * originalAudioDuration;
                const endTime = (i + 1) * originalAudioDuration;

                const guideItem = {
                    "id": (i + 1).toString(),
                    "start": startTime,
                    "end": endTime,
                    "text": mode.displayText
                };

                repeatModeGuideData.push(guideItem);

                logger.info(`[${functionName}] 生成第${i + 1}个引导字幕:`);
                logger.info(`[${functionName}]   - 模式: ${mode.name}`);
                logger.info(`[${functionName}]   - 文本: "${mode.displayText}"`);
                logger.info(`[${functionName}]   - 时间: ${startTime.toFixed(2)}s → ${endTime.toFixed(2)}s`);
            }

            logger.info(`[${functionName}] 单元引导字幕JSON数据生成完成，共 ${repeatModeGuideData.length} 个条目`);

            return repeatModeGuideData;

        } catch (error) {
            logger.error(`[${functionName}] 生成单元引导字幕JSON数据失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 根据repeatModes配置处理字幕数据的时间偏移和显示控制 - 完全照抄generate-aas.test.js的processSubtitleDataByRepeatModes函数
     * @参数说明:
     *   - context: 原始context对象，包含clozedSubtitleJsonArray和bilingualSubtitleJsonArray
     *   - originalAudioDuration: 原始音频时长（秒）
     *   - config: 配置对象
     * @返回值: Promise<Object> - 处理后的context对象
     * @处理逻辑:
     *   1. 检查repeatModes中是否包含"clozedSubtitle"和"bilingualSubtitle"
     *   2. 根据在repeatModes中的位置计算时间偏移量
     *   3. 对相应的字幕数据进行时间戳调整
     *   4. 如果repeatModes中没有对应类型，则从context中移除该字幕数据
     */
    async processSubtitleDataByRepeatModes(context, originalAudioDuration, config) {
        const functionName = 'processSubtitleDataByRepeatModes';

        try {
            logger.info(`[${functionName}] 开始根据repeatModes处理字幕数据...`);
            logger.info(`[${functionName}] 原始音频时长: ${originalAudioDuration.toFixed(2)} 秒`);

            const repeatModes = config.subtitleConfig.repeatModes;
            logger.info(`[${functionName}] repeatModes配置:`, repeatModes.map(mode => `${mode.name}: "${mode.displayText}"`));

            // 创建处理后的context副本
            const processedContext = {
                ...context,
                clozedSubtitleJsonArray: null,
                bilingualSubtitleJsonArray: null
            };

            // 处理填空字幕 (clozedSubtitle)
            const clozedModeIndex = repeatModes.findIndex(mode => mode.name === "clozedSubtitle");
            if (clozedModeIndex !== -1) {
                logger.info(`[${functionName}] 找到clozedSubtitle模式，位于第${clozedModeIndex + 1}个位置`);

                // 计算时间偏移量：(位置索引) * 原始音频时长
                const timeOffset = clozedModeIndex * originalAudioDuration;
                logger.info(`[${functionName}] clozedSubtitle时间偏移量: ${timeOffset.toFixed(2)} 秒`);

                // 复制并调整填空字幕数据的时间戳
                if (context.clozedSubtitleJsonArray && Array.isArray(context.clozedSubtitleJsonArray)) {
                    processedContext.clozedSubtitleJsonArray = context.clozedSubtitleJsonArray.map(item => ({
                        ...item,
                        start: item.start + timeOffset,
                        end: item.end + timeOffset
                    }));

                    logger.info(`[${functionName}] ✅ 填空字幕数据处理完成，共${processedContext.clozedSubtitleJsonArray.length}条`);
                    logger.info(`[${functionName}] 填空字幕时间范围: ${processedContext.clozedSubtitleJsonArray[0].start.toFixed(2)}s → ${processedContext.clozedSubtitleJsonArray[processedContext.clozedSubtitleJsonArray.length-1].end.toFixed(2)}s`);
                }
            } else {
                logger.info(`[${functionName}] ❌ repeatModes中未找到clozedSubtitle模式，填空字幕将不显示`);
                processedContext.clozedSubtitleJsonArray = [];
            }

            // 处理双语字幕 (bilingualSubtitle)
            const bilingualModeIndex = repeatModes.findIndex(mode => mode.name === "bilingualSubtitle");
            if (bilingualModeIndex !== -1) {
                logger.info(`[${functionName}] 找到bilingualSubtitle模式，位于第${bilingualModeIndex + 1}个位置`);

                // 计算时间偏移量：(位置索引) * 原始音频时长
                const timeOffset = bilingualModeIndex * originalAudioDuration;
                logger.info(`[${functionName}] bilingualSubtitle时间偏移量: ${timeOffset.toFixed(2)} 秒`);

                // 复制并调整双语字幕数据的时间戳
                if (context.bilingualSubtitleJsonArray && Array.isArray(context.bilingualSubtitleJsonArray)) {
                    processedContext.bilingualSubtitleJsonArray = context.bilingualSubtitleJsonArray.map(item => ({
                        ...item,
                        start: item.start + timeOffset,
                        end: item.end + timeOffset
                    }));

                    logger.info(`[${functionName}] ✅ 双语字幕数据处理完成，共${processedContext.bilingualSubtitleJsonArray.length}条`);
                    logger.info(`[${functionName}] 双语字幕时间范围: ${processedContext.bilingualSubtitleJsonArray[0].start.toFixed(2)}s → ${processedContext.bilingualSubtitleJsonArray[processedContext.bilingualSubtitleJsonArray.length-1].end.toFixed(2)}s`);
                }
            } else {
                logger.info(`[${functionName}] ❌ repeatModes中未找到bilingualSubtitle模式，双语字幕将不显示`);
                processedContext.bilingualSubtitleJsonArray = [];
            }

            // 输出处理结果摘要
            logger.info(`[${functionName}] ========== 字幕数据处理完成 ==========`);
            logger.info(`[${functionName}] 填空字幕: ${processedContext.clozedSubtitleJsonArray ? processedContext.clozedSubtitleJsonArray.length : 0} 条`);
            logger.info(`[${functionName}] 双语字幕: ${processedContext.bilingualSubtitleJsonArray ? processedContext.bilingualSubtitleJsonArray.length : 0} 条`);

            return processedContext;

        } catch (error) {
            logger.error(`[${functionName}] 处理字幕数据失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 将时间戳（秒）转换为ASS格式时间戳 - 完全照抄generate-aas.test.js的secondsToAssTimestamp函数
     * @参数说明:
     *   - seconds: 时间戳（秒，浮点数）
     * @返回值: string - ASS格式时间戳 (H:MM:SS.CC)
     * @示例: 65.5 → "1:05:30.50"
     */
    secondsToAssTimestamp(seconds) {
        const totalCentiseconds = Math.round(seconds * 100);
        const hours = Math.floor(totalCentiseconds / 360000);
        const minutes = Math.floor((totalCentiseconds % 360000) / 6000);
        const secs = Math.floor((totalCentiseconds % 6000) / 100);
        const centiseconds = totalCentiseconds % 100;

        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
    }

    /**
     * @功能概述: 智能计算双语字幕中文位置（基于英文文本动态测量）
     * @参数说明:
     *   - englishText: {string} 英文文本（可能包含ASS标记）
     *   - englishStyle: {Object} 英文样式配置
     * @返回值: {Object} 位置计算结果
     *   - chineseY: {number} 中文字幕Y坐标
     *   - englishHeight: {number} 英文文本高度
     *   - textWidth: {number} 英文文本宽度
     * @技术实现:
     *   - 清理ASS标记后使用Canvas测量文本尺寸
     *   - 基于文本长度动态调整高度
     *   - 计算中文字幕的合适位置
     */
    calculateBilingualChinesePosition(englishText, englishStyle) {
        const functionName = 'calculateBilingualChinesePosition';

        try {
            logger.debug(`[${functionName}] 开始测量英文文本: "${englishText.substring(0, 50)}..."`);

            // 第一步：读取ASS样式定义（BilingualEnglish）
            const fontSize = englishStyle.fontSize.replace('px', '');
            const fontFamily = englishStyle.fontFamily;
            logger.debug(`[${functionName}] ASS样式定义: 字体=${fontFamily}, 大小=${fontSize}px`);

            // 第二步：精确清理ASS标记
            let cleanText = englishText;

            // 清理颜色标记：{\1c&H0000FFFF}
            cleanText = cleanText.replace(/\{\\1c&H[0-9A-Fa-f]+\}/g, '');

            // 清理字体大小标记：{\fs30}
            cleanText = cleanText.replace(/\{\\fs\d+\}/g, '');

            // 清理重置标记：{\r}
            cleanText = cleanText.replace(/\{\\r\}/g, '');

            // 清理其他可能的ASS标记
            cleanText = cleanText.replace(/\{[^}]*\}/g, '');

            // 清理多余空格
            cleanText = cleanText.trim();

            logger.debug(`[${functionName}] 原始文本: "${englishText}"`);
            logger.debug(`[${functionName}] 清理后文本: "${cleanText}"`);

            // 第三步：使用Canvas测量纯文本尺寸
            const { createCanvas } = require('canvas');
            const canvas = createCanvas(1080, 200);
            const ctx = canvas.getContext('2d');
            ctx.font = `${fontSize}px ${fontFamily}`;

            const measurement = ctx.measureText(cleanText);
            logger.debug(`[${functionName}] Canvas字体设置: ${ctx.font}`);
            logger.debug(`[${functionName}] 测量宽度: ${measurement.width.toFixed(1)}px`);

            // 第四步：基于文本长度动态调整高度
            const fontSizeNum = parseInt(fontSize);
            const baseLineHeight = fontSizeNum * 1.2; // 基础行高

            // 方案B优化：基于精确测量的文本长度动态调整高度
            let englishHeight = baseLineHeight;
            const screenWidth = 1080; // 屏幕宽度

            if (measurement.width > screenWidth) {
                // 超长文本可能换行，增加高度
                const estimatedLines = Math.ceil(measurement.width / screenWidth);
                englishHeight = baseLineHeight * estimatedLines;
                logger.debug(`[${functionName}] 检测到超长文本，估算行数: ${estimatedLines}, 调整高度: ${englishHeight.toFixed(1)}px`);
            } else {
                logger.debug(`[${functionName}] 文本宽度${measurement.width.toFixed(1)}px <= 屏幕宽度${screenWidth}px，使用单行高度`);
            }

            logger.debug(`[${functionName}] 字体大小: ${fontSizeNum}px, 估算高度: ${englishHeight.toFixed(1)}px`);

            // 5. 动态计算中文Y坐标
            const englishY = 800;
            const spacing = 120; // 固定间距（调整为120px）
            const chineseY = englishY + englishHeight + spacing;

            logger.debug(`[${functionName}] 英文Y: ${englishY}, 高度: ${englishHeight.toFixed(1)}, 间距: ${spacing}`);
            logger.debug(`[${functionName}] 计算得出中文Y: ${chineseY.toFixed(1)}`);

            const result = {
                chineseY: Math.round(chineseY),
                englishHeight: Math.round(englishHeight),
                textWidth: Math.round(measurement.width)
            };

            logger.debug(`[${functionName}] 最终结果:`, result);
            return result;

        } catch (error) {
            logger.error(`[${functionName}] 测量失败: ${error.message}`);
            // 返回默认值（当前固定值）
            return { chineseY: 945, englishHeight: 60, textWidth: 800 };
        }
    }

    /**
     * @功能概述: 为中文字幕添加智能换行（专为BilingualChinese设计）
     * @参数说明:
     *   - chineseText: {string} 中文文本
     *   - maxCharsPerLine: {number} 每行最大字符数（默认12）
     * @返回值: {string} 添加ASS换行符的文本
     * @技术实现:
     *   - 基于字符数量换行（忽略标点符号）
     *   - 超过16个字符换行
     *   - 使用ASS换行符\\N
     *   - 保持与BilingualEnglish对齐
     */
    addChineseSmartLineBreaks(chineseText, maxCharsPerLine = 16) {
        const functionName = 'addChineseSmartLineBreaks';

        if (!chineseText || typeof chineseText !== 'string') {
            logger.debug(`[${functionName}] 输入为空或非字符串，返回空字符串`);
            return '';
        }

        logger.debug(`[${functionName}] 开始处理中文换行: "${chineseText}"`);
        logger.debug(`[${functionName}] 换行参数: maxCharsPerLine=${maxCharsPerLine}`);

        const lines = [];
        let currentLine = '';

        for (let i = 0; i < chineseText.length; i++) {
            const char = chineseText[i];
            currentLine += char;

            // 检查是否需要换行（只基于字符数量，忽略标点符号）
            if (currentLine.length >= maxCharsPerLine) {
                const trimmedLine = currentLine.trim();
                if (trimmedLine) {
                    lines.push(trimmedLine);
                    logger.debug(`[${functionName}] 添加行: "${trimmedLine}" (长度: ${trimmedLine.length})`);
                }
                currentLine = '';
            }
        }

        // 处理最后一行
        const finalLine = currentLine.trim();
        if (finalLine) {
            lines.push(finalLine);
            logger.debug(`[${functionName}] 添加最后行: "${finalLine}" (长度: ${finalLine.length})`);
        }

        const result = lines.join('\\N');
        logger.debug(`[${functionName}] 换行完成: ${lines.length}行`);
        logger.debug(`[${functionName}] 原文: "${chineseText}"`);
        logger.debug(`[${functionName}] 结果: "${result}"`);

        return result;
    }

    /**
     * @功能概述: 修复字幕时间重叠问题
     * @参数说明:
     *   - clozedSubtitleJsonArray: {Array} 填空字幕数组
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕数组
     *   - originalAudioDuration: {number} 原始音频时长（秒）
     *   - config: {Object} 配置对象
     * @修复逻辑:
     *   1. 找到最后一行ClozedText的结束时间
     *   2. 找到其后的BilingualEnglish/BilingualChinese的开始时间
     *   3. 如果存在重叠，将ClozedText的结束时间改为BilingualText开始时间减去0.3秒
     */
    fixSubtitleTimeOverlaps(clozedSubtitleJsonArray, enhancedBilingualSubtitleJsonArray, originalAudioDuration, config) {
        const functionName = 'fixSubtitleTimeOverlaps';

        try {
            logger.info(`[${functionName}] 开始修复字幕时间重叠问题...`);

            // 检查输入数据
            if (!clozedSubtitleJsonArray || !Array.isArray(clozedSubtitleJsonArray) || clozedSubtitleJsonArray.length === 0) {
                logger.info(`[${functionName}] 没有填空字幕数据，跳过修复`);
                return;
            }

            if (!enhancedBilingualSubtitleJsonArray || !Array.isArray(enhancedBilingualSubtitleJsonArray) || enhancedBilingualSubtitleJsonArray.length === 0) {
                logger.info(`[${functionName}] 没有双语字幕数据，跳过修复`);
                return;
            }

            // 获取repeatModes配置
            const repeatModes = config.subtitleConfig.repeatModes;
            const clozedModeIndex = repeatModes.findIndex(mode => mode.name === "clozedSubtitle");
            const bilingualModeIndex = repeatModes.findIndex(mode => mode.name === "bilingualSubtitle");

            if (clozedModeIndex === -1 || bilingualModeIndex === -1) {
                logger.info(`[${functionName}] repeatModes中未找到clozedSubtitle或bilingualSubtitle，跳过修复`);
                return;
            }

            // 计算时间偏移
            const clozedTimeOffset = clozedModeIndex * originalAudioDuration;
            const bilingualTimeOffset = bilingualModeIndex * originalAudioDuration;

            logger.info(`[${functionName}] clozedSubtitle时间偏移: ${clozedTimeOffset.toFixed(2)}秒`);
            logger.info(`[${functionName}] bilingualSubtitle时间偏移: ${bilingualTimeOffset.toFixed(2)}秒`);

            // 找到最后一行ClozedText（应用时间偏移后）
            const lastClozedText = clozedSubtitleJsonArray[clozedSubtitleJsonArray.length - 1];
            const lastClozedEndTime = lastClozedText.end + clozedTimeOffset;

            // 找到第一行BilingualText（应用时间偏移后）
            const firstBilingualText = enhancedBilingualSubtitleJsonArray[0];
            const firstBilingualStartTime = firstBilingualText.start + bilingualTimeOffset;

            logger.info(`[${functionName}] 最后一行ClozedText结束时间: ${lastClozedEndTime.toFixed(2)}秒`);
            logger.info(`[${functionName}] 第一行BilingualText开始时间: ${firstBilingualStartTime.toFixed(2)}秒`);

            // 检查是否存在重叠
            if (lastClozedEndTime > firstBilingualStartTime) {
                logger.warn(`[${functionName}] 检测到时间重叠！`);
                logger.warn(`[${functionName}] 重叠时长: ${(lastClozedEndTime - firstBilingualStartTime).toFixed(2)}秒`);

                // 计算新的结束时间：双语字幕开始时间 - 0.3秒 - 时间偏移
                const newEndTime = firstBilingualStartTime - 0.3 - clozedTimeOffset;

                logger.info(`[${functionName}] 修复ClozedText结束时间: ${lastClozedText.end.toFixed(2)}秒 → ${newEndTime.toFixed(2)}秒`);

                // 直接修改原始数据
                lastClozedText.end = newEndTime;

                logger.info(`[${functionName}] ✅ 时间重叠修复完成`);

                // 验证修复结果
                const newLastClozedEndTime = lastClozedText.end + clozedTimeOffset;
                const gap = firstBilingualStartTime - newLastClozedEndTime;
                logger.info(`[${functionName}] 修复后间隔: ${gap.toFixed(2)}秒`);

            } else {
                logger.info(`[${functionName}] ✅ 未检测到时间重叠，无需修复`);
                const gap = firstBilingualStartTime - lastClozedEndTime;
                logger.info(`[${functionName}] 当前间隔: ${gap.toFixed(2)}秒`);
            }

        } catch (error) {
            logger.error(`[${functionName}] 修复字幕时间重叠失败: ${error.message}`);
            logger.error(`[${functionName}] 继续执行，不影响主流程`);
        }
    }


    /**
     * @功能概述: 解析Dialogue行
     * @参数说明:
     *   - line: {string} Dialogue行内容
     *   - index: {number} 行号
     * @返回值: {Object|null} 解析结果
     */
    parseDialogueLine(line, index) {
        const functionName = 'parseDialogueLine';

        try {
            // Dialogue格式: Dialogue: Layer,Start,End,Style,Name,MarginL,MarginR,MarginV,Effect,Text
            const parts = line.split(',');
            if (parts.length < 10) {
                logger.debug(`[${functionName}] 行${index}: Dialogue格式不正确，字段数量不足`);
                return null;
            }

            const layer = parts[0].replace('Dialogue: ', '').trim();
            const startTime = parts[1].trim();
            const endTime = parts[2].trim();
            const style = parts[3].trim();

            // 转换时间为秒数
            const startTimeSeconds = this.assTimestampToSeconds(startTime);
            const endTimeSeconds = this.assTimestampToSeconds(endTime);

            return {
                originalLine: line,
                index,
                layer,
                startTime,
                endTime,
                style,
                startTimeSeconds,
                endTimeSeconds
            };

        } catch (error) {
            logger.debug(`[${functionName}] 行${index}: 解析失败 - ${error.message}`);
            return null;
        }
    }

    /**
     * @功能概述: 将ASS时间戳转换为秒数
     * @参数说明:
     *   - assTimestamp: {string} ASS时间戳 (H:MM:SS.CC)
     * @返回值: {number} 秒数
     */
    assTimestampToSeconds(assTimestamp) {
        const parts = assTimestamp.split(':');
        if (parts.length !== 3) {
            throw new Error(`无效的ASS时间戳格式: ${assTimestamp}`);
        }

        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);
        const secondsParts = parts[2].split('.');
        const seconds = parseInt(secondsParts[0]);
        const centiseconds = parseInt(secondsParts[1] || '0');

        return hours * 3600 + minutes * 60 + seconds + centiseconds / 100;
    }

    /**
     * @功能概述: 生成ASS字幕文件 - 简化版本，直接生成基本的ASS内容
     * @参数说明:
     *   - context: 包含所有字幕数据的上下文对象
     *   - config: 视频配置对象
     *   - videoIdentifier: 视频标识符
     * @返回值: Promise<string> - 生成的ASS文件路径
     */
    async generateAssSubtitle(context, config, videoIdentifier, originalAudioDuration) {
        const functionName = 'generateAssSubtitle';

        try {
            logger.info(`[${functionName}] ========== 开始生成ASS字幕文件 ==========`);

            // 生成时间戳确保文件名唯一性
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            // 生成短随机ID确保唯一性（避免长标题导致文件名过长）
            const randomId = Math.random().toString(36).substring(2, 8);

            // 构建输出文件名（使用简短命名避免Windows文件名长度限制）
            const outputAssFilename = `temp_ass_${timestamp}_${randomId}.ass`;
            const outputAssPath = path.join(context.SAVE_PATH, outputAssFilename);

            logger.info(`[${functionName}] 输出ASS文件路径: ${outputAssPath}`);

            // 生成ASS文件内容
            let assContent = '';

            // [Script Info] 部分
            assContent += '[Script Info]\n';
            assContent += 'Title: Generated ASS Subtitle\n';
            assContent += 'PlayResX: 1080\n';
            assContent += 'PlayResY: 1920\n';
            assContent += 'WrapStyle: 0\n';
            assContent += '\n';

            // [V4+ Styles] 部分
            await this.cssMapper.loadConfig();
            await this.cssMapper.mapAllStyles();
            const assStylesHeader = this.cssMapper.generateASSStylesHeader();
            assContent += assStylesHeader;
            assContent += '\n';

            // [Events] 部分
            assContent += '[Events]\n';
            assContent += 'Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n';

            // 添加视频标题事件
            if (context.videoTitleJsonArray && context.videoTitleJsonArray.length > 0) {
                const titleData = context.videoTitleJsonArray[0];
                const startTime = this.secondsToAssTimestamp(titleData.start);
                const endTime = this.secondsToAssTimestamp(titleData.end);

                const style = titleData.style;
                const x = parseInt(style.position.x.replace('px', ''));
                const y1 = parseInt(style.position.y1.replace('px', ''));
                const y2 = parseInt(style.position.y2.replace('px', ''));

                if (titleData.text_1) {
                    assContent += `Dialogue: 0,${startTime},${endTime},VideoTitle,,0,0,0,,{\\pos(${x},${y1})}${titleData.text_1}\n`;
                }
                if (titleData.text_2) {
                    assContent += `Dialogue: 0,${startTime},${endTime},VideoTitle,,0,0,0,,{\\pos(${x},${y2})}${titleData.text_2}\n`;
                }
            }

            // 添加广告字幕事件（根据video-config.json配置，带动态进入和离开效果）
            const advertisementConfig = config.subtitleConfig.advertisement;
            if (advertisementConfig && advertisementConfig.enabled) {
                logger.info(`[${functionName}] 开始生成广告字幕...`);
                
                // 增强随机选择一组广告文本（使用加密级随机性确保真正均匀分布）
                const titles = advertisementConfig.titles;
                if (!titles || !Array.isArray(titles) || titles.length === 0) {
                    logger.warn(`[${functionName}] 广告标语配置为空，跳过生成`);
                } else {
                    // 使用Node.js crypto模块的随机数生成器（更好的随机性）
                    const crypto = require('crypto');
                    const randomBytes = crypto.randomBytes(4); // 生成4字节随机数
                    const randomValue = randomBytes.readUInt32BE(0); // 读取为32位无符号整数
                    const normalizedRandom = randomValue / 0xFFFFFFFF; // 归一化到[0,1)
                    const randomIndex = Math.floor(normalizedRandom * titles.length);
                    const selectedTitle = titles[randomIndex];
                    
                    logger.info(`[${functionName}] 加密级随机选择算法:`);
                    logger.info(`[${functionName}]   - 随机字节: ${Array.from(randomBytes).map(b => b.toString(16).padStart(2, '0')).join('')}`);
                    logger.info(`[${functionName}]   - 原始随机值: ${randomValue}`);
                    logger.info(`[${functionName}]   - 归一化随机值: ${normalizedRandom.toFixed(6)}`);
                    logger.info(`[${functionName}]   - 选择索引: ${randomIndex} (共${titles.length}个选项)`);
                    logger.info(`[${functionName}] 随机选择第${randomIndex + 1}组广告文本:`);
                    logger.info(`[${functionName}]   - 第一行: "${selectedTitle.line1}"`);
                    logger.info(`[${functionName}]   - 第二行: "${selectedTitle.line2}"`);
                    
                    // 记录选择历史（用于调试随机性）
                    if (!this.adSelectionHistory) {
                        this.adSelectionHistory = [];
                    }
                    this.adSelectionHistory.push({
                        timestamp: Date.now(),
                        selectedIndex: randomIndex,
                        totalOptions: titles.length,
                        randomValue: normalizedRandom
                    });
                    
                    // 如果历史记录超过10条，只保留最近的10条
                    if (this.adSelectionHistory.length > 10) {
                        this.adSelectionHistory = this.adSelectionHistory.slice(-10);
                    }
                    
                    logger.debug(`[${functionName}] 广告选择历史: ${this.adSelectionHistory.map(h => `${h.selectedIndex}(${h.randomValue.toFixed(3)})`).join(', ')}`);
                    
                    // 计算广告字幕的时间范围（使用已传入的originalAudioDuration参数）
                    const advertisementStartTime = advertisementConfig.startTime; // 6秒
                    let advertisementEndTime;
                    
                    if (advertisementConfig.endTime === "firstLoopEnd") {
                        advertisementEndTime = originalAudioDuration - 1; // 第一遍结束前3秒退出
                    } else {
                        advertisementEndTime = parseFloat(advertisementConfig.endTime);
                    }
                    
                    logger.info(`[${functionName}] 广告字幕时间范围: ${advertisementStartTime}s → ${advertisementEndTime}s`);
                    
                    // 获取动画持续时间（毫秒）
                    const animationDuration = advertisementConfig.animationDuration || 0.5; // 默认0.5秒
                    const animationDurationMs = Math.round(animationDuration * 1000); // 转换为毫秒
                    
                    logger.info(`[${functionName}] 动画持续时间: ${animationDuration}秒 (${animationDurationMs}ms)`);
                    
                    // 转换为ASS时间戳
                    const adStartTime = this.secondsToAssTimestamp(advertisementStartTime);
                    const adEndTime = this.secondsToAssTimestamp(advertisementEndTime);
                    
                    // 获取广告字幕位置（使用position配置）
                    const adStyle = advertisementConfig.style;
                    const adX = parseInt(adStyle.position.x.replace('px', ''));
                    const adY1 = parseInt(adStyle.position.y1.replace('px', ''));
                    const adY2 = parseInt(adStyle.position.y2.replace('px', ''));
                    
                    // 计算动画起始和结束位置（从上方进入，向上方离开）
                    const enterOffsetY = -100; // 从上方100像素外进入
                    const exitOffsetY = -100;  // 向上方100像素外离开
                    
                    // 应用广告字幕样式配置
                    const adStyleConfig = advertisementConfig.style;
                    
                    // 处理字体设置
                    const fontSize = parseInt(adStyleConfig.fontSize.replace('px', ''));
                    const fontFamily = this.cssMapper.convertFontFamilyToASS(adStyleConfig.fontFamily);
                    const fontColor = this.cssMapper.convertColorToASS(adStyleConfig.color);
                    
                    // 处理文本阴影
                    const shadowConfig = this.cssMapper.convertTextShadowToASS(adStyleConfig.textShadow);

                    // 处理边框
                    const borderConfig = this.cssMapper.convertBorderToASS(adStyleConfig.border);

                    logger.info(`[${functionName}] 广告字幕样式配置:`);
                    logger.info(`[${functionName}]   - 字体: ${fontFamily}, 大小: ${fontSize}px`);
                    logger.info(`[${functionName}]   - 颜色: ${fontColor}`);
                    logger.info(`[${functionName}]   - 边框: 宽度=${borderConfig.outline}, 颜色=${borderConfig.outlineColor}`);
                    logger.info(`[${functionName}]   - 阴影: 轮廓=${shadowConfig.outline}, 偏移=${shadowConfig.shadow}, 颜色=${shadowConfig.outlineColor}`);

                    // 生成字体样式标签（优先使用边框，然后是阴影）
                    let fontStyleTag;
                    if (adStyleConfig.border && adStyleConfig.border !== 'none') {
                        // 有边框：使用边框配置，同时应用阴影
                        const shadowOffset = shadowConfig.shadow || 1;
                        fontStyleTag = `\\fn${fontFamily}\\fs${fontSize}\\1c${fontColor}\\3c${borderConfig.outlineColor}\\bord${borderConfig.outline}\\shad${shadowOffset}\\4c${shadowConfig.outlineColor}`;
                    } else if (adStyleConfig.textShadow && adStyleConfig.textShadow !== 'none') {
                        // 仅有阴影：使用阴影配置
                        fontStyleTag = `\\fn${fontFamily}\\fs${fontSize}\\1c${fontColor}\\3c${shadowConfig.outlineColor}\\bord${shadowConfig.outline}\\shad${shadowConfig.shadow}`;
                    } else {
                        // 无边框无阴影
                        fontStyleTag = `\\fn${fontFamily}\\fs${fontSize}\\1c${fontColor}\\bord0\\shad0`;
                    }
                    
                    // 生成双行广告字幕事件（保持两行显示，确保emoji彩色显示）
                    if (selectedTitle.line1) {
                        // 增强的弹跳缩放+旋转特效
                        // 起始位置：稍微偏上一点，不要太远
                        const startY = adY1 - 30; // 只偏移30像素，不会太远
                        const moveEffect = `\\move(${adX},${startY},${adX},${adY1},0,${animationDurationMs})`;
                        
                        // 淡入淡出：慢淡入(1200ms)，快淡出(300ms)
                        const fadeEffect = `\\fad(1200,300)`;
                        
                        // 增强的弹跳缩放特效：更明显的缩放和旋转 + 退出时的弹跳效果
                        const totalDuration = (advertisementEndTime - advertisementStartTime) * 1000; // 转换为毫秒
                        const exitStartTime = totalDuration - 800; // 退出前800ms开始退出动画
                        
                        // 进入阶段：从30%缩放到150%再回到100%，旋转从-20°到5°再到0°
                        // 退出阶段：从100%缩放到130%再缩小到20%，同时旋转15°
                        const bounceEffect = `\\fscx30\\fscy30\\frz-20\\t(0,${Math.round(animationDurationMs * 0.6)},\\fscx150\\fscy150\\frz5)\\t(${Math.round(animationDurationMs * 0.6)},${animationDurationMs},\\fscx100\\fscy100\\frz0)\\t(${exitStartTime},${exitStartTime + 400},\\fscx130\\fscy130\\frz15)\\t(${exitStartTime + 400},${totalDuration},\\fscx20\\fscy20\\frz-10)`;
                        
                        // 处理emoji和文字在同一行的彩色显示
                        const processedLine1 = this.processEmojiForASS(selectedTitle.line1);
                        
                        assContent += `Dialogue: 4,${adStartTime},${adEndTime},Advertisement,,0,0,0,,{${moveEffect}${fadeEffect}${bounceEffect}${fontStyleTag}}${processedLine1}\n`;
                        logger.info(`[${functionName}] ✅ 广告字幕第一行已添加（增强动效+样式）: "${selectedTitle.line1}" 位置(${adX},${adY1})`);
                    }
                    
                    if (selectedTitle.line2) {
                        // 第二行增强的延迟弹跳特效，营造层次感
                        const startY2 = adY2 - 30; // 同样只偏移30像素
                        const delayMs = Math.round(animationDurationMs * 0.3); // 延迟30%时长
                        const moveEffect2 = `\\move(${adX},${startY2},${adX},${adY2},${delayMs},${animationDurationMs + delayMs})`;
                        
                        // 淡入淡出：慢淡入(1200ms)，快淡出(300ms)
                        const fadeEffect2 = `\\fad(1200,300)`;
                        
                        // 第二行增强的弹跳特效：反向旋转，营造对比效果 + 退出动画
                        const totalDuration = (advertisementEndTime - advertisementStartTime) * 1000; // 转换为毫秒
                        const exitStartTime = totalDuration - 600; // 退出前600ms开始（比第一行早一点）
                        
                        // 进入阶段：从30%缩放到150%再回到100%，反向旋转从+25°到-5°再到0°
                        // 退出阶段：从100%缩放到140%再缩小到15%，同时旋转-20°
                        const bounceEffect2 = `\\fscx30\\fscy30\\frz25\\t(${delayMs},${Math.round(animationDurationMs * 0.6) + delayMs},\\fscx150\\fscy150\\frz-5)\\t(${Math.round(animationDurationMs * 0.6) + delayMs},${animationDurationMs + delayMs},\\fscx100\\fscy100\\frz0)\\t(${exitStartTime},${exitStartTime + 350},\\fscx140\\fscy140\\frz-20)\\t(${exitStartTime + 350},${totalDuration},\\fscx15\\fscy15\\frz10)`;
                        
                        // 处理emoji和文字在同一行的彩色显示
                        const processedLine2 = this.processEmojiForASS(selectedTitle.line2);
                        
                        assContent += `Dialogue: 4,${adStartTime},${adEndTime},Advertisement,,0,0,0,,{${moveEffect2}${fadeEffect2}${bounceEffect2}${fontStyleTag}}${processedLine2}\n`;
                        logger.info(`[${functionName}] ✅ 广告字幕第二行已添加（增强动效+延迟+样式）: "${selectedTitle.line2}" 位置(${adX},${adY2})`);
                    }
                    
                    logger.info(`[${functionName}] 广告字幕增强动画效果配置:`);
                    logger.info(`[${functionName}]   - 进入特效: 弹跳缩放(30%→150%→100%) + 增强旋转(-20°→5°→0°)`);
                    logger.info(`[${functionName}]   - 退出特效: 弹跳缩放(100%→130%→20%) + 旋转(0°→15°→-10°)`);
                    logger.info(`[${functionName}]   - 移动距离: 仅30像素偏移，避免过远`);
                    logger.info(`[${functionName}]   - 动画时长: ${animationDuration}秒`);
                    logger.info(`[${functionName}]   - 淡入淡出: 慢淡入(1200ms)，快淡出(300ms)`);
                    logger.info(`[${functionName}]   - 第二行延迟: ${Math.round(animationDurationMs * 0.3)}ms，反向旋转，错位退出`);
                }
            } else {
                logger.info(`[${functionName}] 广告字幕已禁用，跳过生成`);
            }

            // 添加填空字幕事件
            if (context.clozedSubtitleJsonArray && Array.isArray(context.clozedSubtitleJsonArray)) {
                context.clozedSubtitleJsonArray.forEach(item => {
                    const startTime = this.secondsToAssTimestamp(item.start);
                    const endTime = this.secondsToAssTimestamp(item.end);
                    const text = item.text.replace(/\n/g, ' ');
                    const centerX = 540;
                    const centerY = 928;
                    
                    assContent += `Dialogue: 1,${startTime},${endTime},ClozedText,,0,0,0,,{\\pos(${centerX},${centerY})}${text}\n`;
                });
            }

            // 添加双语字幕事件（使用智能位置计算和中文换行）
            if (context.bilingualSubtitleJsonArray && Array.isArray(context.bilingualSubtitleJsonArray)) {
                const bilingualConfig = config.subtitleConfig.bilingualTextStyle;

                context.bilingualSubtitleJsonArray.forEach(item => {
                    const startTime = this.secondsToAssTimestamp(item.start);
                    const endTime = this.secondsToAssTimestamp(item.end);

                    // BilingualChinese智能换行策略：英文单行，中文智能换行
                    // 英文强制单行（保持与原策略一致）
                    const englishText = item.text_english ? item.text_english.replace(/\n/g, ' ') : '';

                    // 中文智能换行（专门为BilingualChinese设计）
                    let chineseText = item.text_chinese || '';
                    if (chineseText) {
                        logger.debug(`[generateAssSubtitle] 对BilingualChinese应用智能中文换行`);
                        chineseText = this.addChineseSmartLineBreaks(chineseText, 16); // 每行最多16个字符
                    }

                    if (englishText && chineseText) {
                        // 提取关键词解释对象（用于英文翻译注释处理）
                        const wordsExplanation = item.words_explanation || {};

                        logger.debug(`[generateAssSubtitle] 处理英文关键词翻译注释: ${Object.keys(wordsExplanation).length}个关键词`);
                        logger.debug(`[generateAssSubtitle] 关键词列表: [${Object.keys(wordsExplanation).join(', ')}]`);

                        // 处理英文关键词翻译注释
                        let processedEnglishText = englishText;
                        if (Object.keys(wordsExplanation).length > 0) {
                            // 提取关键词块样式配置
                            const keywordBlockStyle = bilingualConfig.keywordBlockStyle || null;

                            // 使用完整的关键词处理函数
                            processedEnglishText = this.processEnglishKeywordsWithTranslation(
                                englishText,
                                wordsExplanation,
                                keywordBlockStyle
                            );
                        }

                        // 方案1：Canvas文本测量 + 动态Y坐标计算
                        logger.debug(`[generateAssSubtitle] 应用方案1：动态测量英文文本体积`);
                        const englishStyle = bilingualConfig.englishStyle;
                        const positionResult = this.calculateBilingualChinesePosition(processedEnglishText, englishStyle);

                        logger.debug(`[generateAssSubtitle] 英文文本体积测量结果:`, positionResult);

                        // 使用测量结果，不进行自动换行，由CSS样式控制显示效果
                        const centerX = 540; // 1080px / 2，居中对齐
                        const englishY = 800; // 英文字幕Y位置（固定）
                        const chineseY = positionResult.chineseY; // 中文字幕Y位置（动态计算）

                        // 生成英文字幕事件（单行，包含关键词翻译注释）
                        assContent += `Dialogue: 2,${startTime},${endTime},BilingualEnglish,,0,0,0,,{\\pos(${centerX},${englishY})}${processedEnglishText}\n`;

                        // 生成中文字幕事件（智能换行）
                        assContent += `Dialogue: 2,${startTime},${endTime},BilingualChinese,,0,0,0,,{\\pos(${centerX},${chineseY})}${chineseText}\n`;
                    }
                });
            }

            // 添加单元引导字幕事件（使用上浮动画特效）
            if (context.repeatModeGuideJsonArray && Array.isArray(context.repeatModeGuideJsonArray)) {
                const repeatModeStyle = config.subtitleConfig.repeatModeStyle;

                // 使用上浮动画特效生成单元引导字幕
                logger.info('[generateAssSubtitle] 使用上浮动画特效生成单元引导字幕');

                context.repeatModeGuideJsonArray.forEach(item => {
                    const originalStart = item.start;
                    const originalEnd = item.end;
                    const text = item.text.replace(/\n/g, ' '); // 保持单行文本

                    // 使用固定位置，居中对齐
                    const centerX = 540; // 1080px / 2，居中对齐
                    const centerY = 1450; // 固定Y位置，在屏幕底部

                    // 特效时长配置
                    const effectDuration = 0.5; // 进入和退出特效各0.5秒

                    // 计算特效时间点
                    const fadeInStart = originalStart;
                    const fadeInEnd = originalStart + effectDuration;
                    const stableStart = fadeInEnd;
                    const stableEnd = originalEnd - effectDuration;
                    const fadeOutStart = stableEnd;
                    const fadeOutEnd = originalEnd;

                    // 转换为ASS时间戳
                    const fadeInStartTime = this.secondsToAssTimestamp(fadeInStart);
                    const fadeInEndTime = this.secondsToAssTimestamp(fadeInEnd);
                    const stableStartTime = this.secondsToAssTimestamp(stableStart);
                    const stableEndTime = this.secondsToAssTimestamp(stableEnd);
                    const fadeOutStartTime = this.secondsToAssTimestamp(fadeOutStart);
                    const fadeOutEndTime = this.secondsToAssTimestamp(fadeOutEnd);

                    const enterY = centerY + 50; // 起始位置向下偏移
                    const exitY = centerY - 50; // 结束位置向上偏移

                    // 进入特效：从下方上浮并淡入
                    assContent += `Dialogue: 3,${fadeInStartTime},${fadeInEndTime},RepeatModeGuide,,0,0,0,,{\\move(${centerX},${enterY},${centerX},${centerY})\\fad(100,0)\\t(0,100,\\alpha&H00&)}${text}\n`;

                    // 稳定显示阶段
                    if (stableStart < stableEnd) {
                        assContent += `Dialogue: 3,${stableStartTime},${stableEndTime},RepeatModeGuide,,0,0,0,,{\\pos(${centerX},${centerY})}${text}\n`;
                    }

                    // 退出特效：向上浮动并淡出
                    assContent += `Dialogue: 3,${fadeOutStartTime},${fadeOutEndTime},RepeatModeGuide,,0,0,0,,{\\move(${centerX},${centerY},${centerX},${exitY})\\fad(0,100)\\t(0,100,\\alpha&HFF&)}${text}\n`;
                });
            }

            logger.info(`[${functionName}] ASS内容生成完成，总长度: ${assContent.length} 字符`);

            // // 步骤：ASS时间重叠审查和修复
            // logger.info(`[${functionName}] ========== 开始ASS时间重叠审查 ==========`);
            // assContent = this.reviewAndFixTimeOverlaps(assContent);
            // logger.info(`[${functionName}] ASS时间重叠审查完成`);

            // 保存ASS文件
            await fs.writeFile(outputAssPath, assContent, 'utf8');

            // 将ASS内容保存到context中，供后续任务使用
            context.assContent = assContent;
            context.assContentLength = assContent.length;

            logger.info(`[${functionName}] ✅ ASS字幕文件生成成功: ${outputAssPath}`);
            logger.info(`[${functionName}] ✅ ASS内容已保存到context中，长度: ${assContent.length} 字符`);
            logger.info(`[${functionName}] 文件大小: ${assContent.length} 字符`);

            return outputAssPath;

        } catch (error) {
            logger.error(`[${functionName}] 生成ASS字幕文件失败: ${error.message}`);
            throw error;
        }
    }
}

module.exports = GenerateASSTask;
