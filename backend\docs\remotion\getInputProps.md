# getInputProps()

## 概述

`getInputProps()` 用于获取从命令行通过 `--props` 参数传入的输入属性，或者在使用 Node.js API 时通过 `inputProps` 参数传入的属性。此方法在根组件中获取输入属性时特别有用。

**版本要求**: v2.0+

## 语法

```typescript
import { getInputProps } from "remotion";

const props = getInputProps();
```

## 返回值

- **类型**: `object`
- **描述**: 包含传入属性的对象

## 重要说明

### 何时不需要此API

优先使用以下方式获取输入属性：

1. **组合组件**: 作为组合渲染的组件会将输入属性作为常规 React props 接收
2. **根组件**: 可以使用 [`calculateMetadata()`](./calculateMetadata.md) 函数获取输入属性

这两种方式都支持类型安全，比使用返回非类型安全对象的 `getInputProps()` 更好。

### 使用限制

此方法在 Remotion Player 中不可用。在 Player 中，应从传递给 Player 的 `component` 属性的组件中获取 props。

## 基础用法

### 1. 命令行传递JSON

```bash
# 直接传递JSON字符串
npx remotion render --props='{"title": "我的视频", "duration": 300}'

# 在Studio中使用
npx remotion studio --props='{"title": "我的视频", "duration": 300}'
```

### 2. 从文件读取JSON

```bash
# 从文件读取属性
npx remotion render --props=./path/to/props.json
```

props.json 文件内容：
```json
{
  "title": "我的视频",
  "subtitle": "精彩内容",
  "duration": 300,
  "theme": {
    "primaryColor": "#ff6b6b",
    "secondaryColor": "#4ecdc4"
  }
}
```

### 3. 在根组件中使用

```typescript
import { getInputProps } from "remotion";
import { Composition } from "remotion";

export const Root: React.FC = () => {
  const { title, duration, theme } = getInputProps();

  return (
    <>
      <Composition
        id="my-video"
        component={MyVideo}
        durationInFrames={duration || 900}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{
          title: title || "默认标题",
          theme: theme || { primaryColor: "#000", secondaryColor: "#fff" }
        }}
      />
    </>
  );
};
```

## 实际应用场景

### 1. 动态视频配置

```typescript
import { getInputProps, Composition } from "remotion";

interface VideoProps {
  title: string;
  duration: number;
  format: "16:9" | "9:16" | "1:1";
  language: "zh" | "en";
}

export const Root: React.FC = () => {
  const props = getInputProps() as VideoProps;
  
  // 根据格式设置尺寸
  const getDimensions = (format: string) => {
    switch (format) {
      case "16:9": return { width: 1920, height: 1080 };
      case "9:16": return { width: 1080, height: 1920 };
      case "1:1": return { width: 1080, height: 1080 };
      default: return { width: 1920, height: 1080 };
    }
  };

  const { width, height } = getDimensions(props.format || "16:9");

  return (
    <Composition
      id="dynamic-video"
      component={DynamicVideo}
      durationInFrames={props.duration || 900}
      fps={30}
      width={width}
      height={height}
      defaultProps={props}
    />
  );
};
```

### 2. 多语言支持

```typescript
interface LocalizedProps {
  language: "zh" | "en" | "es" | "fr";
  content: {
    title: string;
    subtitle: string;
    description: string;
  };
}

export const Root: React.FC = () => {
  const props = getInputProps() as LocalizedProps;
  
  // 默认语言配置
  const defaultContent = {
    zh: {
      title: "默认标题",
      subtitle: "默认副标题", 
      description: "默认描述"
    },
    en: {
      title: "Default Title",
      subtitle: "Default Subtitle",
      description: "Default Description"
    }
  };

  const language = props.language || "zh";
  const content = props.content || defaultContent[language];

  return (
    <Composition
      id="localized-video"
      component={LocalizedVideo}
      durationInFrames={900}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={{
        language,
        content
      }}
    />
  );
};
```

### 3. 主题和样式配置

```typescript
interface ThemeProps {
  theme: {
    name: string;
    colors: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts: {
      heading: string;
      body: string;
    };
  };
}

export const Root: React.FC = () => {
  const props = getInputProps() as ThemeProps;
  
  // 预定义主题
  const themes = {
    modern: {
      colors: {
        primary: "#ff6b6b",
        secondary: "#4ecdc4",
        background: "#f8f9fa",
        text: "#2c3e50"
      },
      fonts: {
        heading: "Montserrat",
        body: "Open Sans"
      }
    },
    classic: {
      colors: {
        primary: "#2c3e50",
        secondary: "#3498db",
        background: "#ecf0f1",
        text: "#34495e"
      },
      fonts: {
        heading: "Georgia",
        body: "Times New Roman"
      }
    }
  };

  const theme = props.theme || themes.modern;

  return (
    <Composition
      id="themed-video"
      component={ThemedVideo}
      durationInFrames={900}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={{ theme }}
    />
  );
};
```

### 4. 数据驱动的视频生成

```typescript
interface DataDrivenProps {
  data: {
    charts: Array<{
      type: "bar" | "line" | "pie";
      title: string;
      data: number[];
      labels: string[];
    }>;
    animations: {
      duration: number;
      easing: "linear" | "ease" | "bounce";
    };
  };
}

export const Root: React.FC = () => {
  const props = getInputProps() as DataDrivenProps;
  
  // 根据数据计算视频时长
  const calculateDuration = (data: any) => {
    const baseFrames = 300; // 5秒基础时长
    const chartFrames = data.charts.length * 180; // 每个图表3秒
    const animationFrames = data.animations.duration * 30; // 动画时长
    return baseFrames + chartFrames + animationFrames;
  };

  const duration = props.data ? calculateDuration(props.data) : 900;

  return (
    <Composition
      id="data-driven-video"
      component={DataDrivenVideo}
      durationInFrames={duration}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={props}
    />
  );
};
```

### 5. 条件性组合注册

```typescript
interface ConditionalProps {
  videoType: "intro" | "main" | "outro" | "full";
  includeAudio: boolean;
  quality: "low" | "medium" | "high";
}

export const Root: React.FC = () => {
  const props = getInputProps() as ConditionalProps;
  
  const getQualitySettings = (quality: string) => {
    switch (quality) {
      case "low": return { width: 1280, height: 720, fps: 24 };
      case "medium": return { width: 1920, height: 1080, fps: 30 };
      case "high": return { width: 3840, height: 2160, fps: 60 };
      default: return { width: 1920, height: 1080, fps: 30 };
    }
  };

  const { width, height, fps } = getQualitySettings(props.quality || "medium");

  return (
    <>
      {(props.videoType === "intro" || props.videoType === "full") && (
        <Composition
          id="intro"
          component={IntroVideo}
          durationInFrames={300}
          fps={fps}
          width={width}
          height={height}
          defaultProps={{ includeAudio: props.includeAudio }}
        />
      )}
      
      {(props.videoType === "main" || props.videoType === "full") && (
        <Composition
          id="main"
          component={MainVideo}
          durationInFrames={1800}
          fps={fps}
          width={width}
          height={height}
          defaultProps={{ includeAudio: props.includeAudio }}
        />
      )}
      
      {(props.videoType === "outro" || props.videoType === "full") && (
        <Composition
          id="outro"
          component={OutroVideo}
          durationInFrames={300}
          fps={fps}
          width={width}
          height={height}
          defaultProps={{ includeAudio: props.includeAudio }}
        />
      )}
    </>
  );
};
```

## 类型安全的替代方案

### 1. 使用 calculateMetadata

```typescript
import { calculateMetadata } from "remotion";

interface MyProps {
  title: string;
  duration: number;
}

export const Root: React.FC = () => {
  return (
    <Composition
      id="my-video"
      component={MyVideo}
      durationInFrames={900}
      fps={30}
      width={1920}
      height={1080}
      calculateMetadata={({ props }: { props: MyProps }) => {
        return {
          durationInFrames: props.duration * 30, // 秒转帧
        };
      }}
    />
  );
};
```

### 2. 组件级别的类型安全

```typescript
interface VideoProps {
  title: string;
  subtitle: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
  };
}

const MyVideo: React.FC<VideoProps> = ({ title, subtitle, theme }) => {
  // 类型安全的属性访问
  return (
    <div style={{ color: theme.primaryColor }}>
      <h1>{title}</h1>
      <h2>{subtitle}</h2>
    </div>
  );
};

export const Root: React.FC = () => {
  const props = getInputProps() as VideoProps;
  
  return (
    <Composition
      id="typed-video"
      component={MyVideo}
      durationInFrames={900}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={props}
    />
  );
};
```

## 调试和验证

### 1. 属性验证

```typescript
const validateProps = (props: any) => {
  const errors: string[] = [];
  
  if (!props.title || typeof props.title !== 'string') {
    errors.push('title 必须是字符串');
  }
  
  if (!props.duration || typeof props.duration !== 'number') {
    errors.push('duration 必须是数字');
  }
  
  if (errors.length > 0) {
    throw new Error(`属性验证失败: ${errors.join(', ')}`);
  }
  
  return props;
};

export const Root: React.FC = () => {
  const rawProps = getInputProps();
  const props = validateProps(rawProps);
  
  return (
    <Composition
      id="validated-video"
      component={ValidatedVideo}
      durationInFrames={props.duration * 30}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={props}
    />
  );
};
```

### 2. 开发模式调试

```typescript
export const Root: React.FC = () => {
  const props = getInputProps();
  
  // 开发模式下打印属性
  if (process.env.NODE_ENV === 'development') {
    console.log('输入属性:', JSON.stringify(props, null, 2));
  }
  
  return (
    <Composition
      id="debug-video"
      component={DebugVideo}
      durationInFrames={900}
      fps={30}
      width={1920}
      height={1080}
      defaultProps={props}
    />
  );
};
```

## 最佳实践

1. **类型安全**: 尽可能使用类型断言或验证
2. **默认值**: 总是提供合理的默认值
3. **验证**: 在生产环境中验证输入属性
4. **文档**: 清楚地记录期望的属性结构
5. **错误处理**: 优雅地处理无效或缺失的属性

## 相关 API

- [`calculateMetadata()`](./calculateMetadata.md) - 计算元数据
- [`<Composition>`](./Composition.md) - 组合组件
- [命令行参数](/docs/cli) - CLI 使用方式
- [Node.js API](/docs/ssr) - 服务端渲染

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/config/input-props.ts)
