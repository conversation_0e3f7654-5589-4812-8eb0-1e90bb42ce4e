# registerRoot()

## 概述

`registerRoot()` 是注册 Remotion 项目根组件的函数。在根组件中，应该返回一个或多个组合（如果是多个组合，应该包装在 React Fragment 中）。

## 语法

```typescript
import { registerRoot } from "remotion";

registerRoot(RootComponent);
```

## 参数

### RootComponent
- **类型**: `React.ComponentType`
- **描述**: 包含所有组合定义的根组件

## 重要说明

⚠️ **文件分离原则**: `registerRoot()` 应该位于与组合列表分离的文件中。这是因为使用 React Fast Refresh 时，被刷新文件中的所有代码都会重新执行，但此函数应该只调用一次。

## 基础示例

### 1. 标准项目结构

**src/index.ts** (入口文件)
```typescript
import { registerRoot } from "remotion";
import { RemotionRoot } from "./Root";

registerRoot(RemotionRoot);
```

**src/Root.tsx** (根组件)
```typescript
import React from "react";
import { Composition } from "remotion";
import { MyComponent } from "./MyComponent";
import { MyOtherComponent } from "./MyOtherComponent";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="main-video"
        fps={30}
        height={1080}
        width={1920}
        durationInFrames={300}
        component={MyComponent}
      />
      <Composition
        id="secondary-video"
        fps={30}
        height={1080}
        width={1920}
        durationInFrames={180}
        component={MyOtherComponent}
      />
    </>
  );
};
```

### 2. 多格式视频项目

**src/index.ts**
```typescript
import { registerRoot } from "remotion";
import { VideoRoot } from "./VideoRoot";

registerRoot(VideoRoot);
```

**src/VideoRoot.tsx**
```typescript
import React from "react";
import { Composition, Folder } from "remotion";
import { HorizontalVideo } from "./components/HorizontalVideo";
import { VerticalVideo } from "./components/VerticalVideo";
import { SquareVideo } from "./components/SquareVideo";

export const VideoRoot: React.FC = () => {
  return (
    <>
      <Folder name="横屏格式">
        <Composition
          id="youtube-horizontal"
          component={HorizontalVideo}
          width={1920}
          height={1080}
          fps={30}
          durationInFrames={600}
          defaultProps={{
            title: "YouTube 横屏视频",
            format: "16:9"
          }}
        />
      </Folder>
      
      <Folder name="竖屏格式">
        <Composition
          id="tiktok-vertical"
          component={VerticalVideo}
          width={1080}
          height={1920}
          fps={30}
          durationInFrames={300}
          defaultProps={{
            title: "TikTok 竖屏视频",
            format: "9:16"
          }}
        />
        
        <Composition
          id="instagram-story"
          component={VerticalVideo}
          width={1080}
          height={1920}
          fps={30}
          durationInFrames={450}
          defaultProps={{
            title: "Instagram 故事",
            format: "9:16"
          }}
        />
      </Folder>
      
      <Folder name="方形格式">
        <Composition
          id="instagram-post"
          component={SquareVideo}
          width={1080}
          height={1080}
          fps={30}
          durationInFrames={180}
          defaultProps={{
            title: "Instagram 帖子",
            format: "1:1"
          }}
        />
      </Folder>
    </>
  );
};
```

## 高级用法

### 1. 延迟注册根组件

在某些情况下，如动态导入根组件或加载 WebAssembly，您可能希望延迟加载 `registerRoot()`：

```typescript
import { registerRoot } from "remotion";

// 异步加载根组件
async function initializeApp() {
  // 加载 WebAssembly 或其他异步资源
  await loadWebAssembly();
  
  // 动态导入根组件
  const { RemotionRoot } = await import("./Root");
  
  // 注册根组件
  registerRoot(RemotionRoot);
}

initializeApp();
```

### 2. 条件性组合注册

```typescript
import React from "react";
import { Composition } from "remotion";
import { DevelopmentComponent } from "./DevelopmentComponent";
import { ProductionComponent } from "./ProductionComponent";

export const ConditionalRoot: React.FC = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return (
    <>
      <Composition
        id="main-video"
        component={ProductionComponent}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={300}
      />
      
      {/* 仅在开发环境中显示 */}
      {isDevelopment && (
        <Composition
          id="dev-test"
          component={DevelopmentComponent}
          width={1920}
          height={1080}
          fps={30}
          durationInFrames={60}
        />
      )}
    </>
  );
};
```

### 3. 动态组合生成

```typescript
import React from "react";
import { Composition } from "remotion";
import { TemplateComponent } from "./TemplateComponent";

const videoFormats = [
  { id: "youtube", width: 1920, height: 1080, name: "YouTube" },
  { id: "tiktok", width: 1080, height: 1920, name: "TikTok" },
  { id: "instagram", width: 1080, height: 1080, name: "Instagram" }
];

export const DynamicRoot: React.FC = () => {
  return (
    <>
      {videoFormats.map((format) => (
        <Composition
          key={format.id}
          id={`template-${format.id}`}
          component={TemplateComponent}
          width={format.width}
          height={format.height}
          fps={30}
          durationInFrames={300}
          defaultProps={{
            platform: format.name,
            aspectRatio: format.width / format.height
          }}
        />
      ))}
    </>
  );
};
```

### 4. 配置驱动的根组件

```typescript
import React from "react";
import { Composition } from "remotion";
import config from "./config.json";

interface VideoConfig {
  id: string;
  component: string;
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
  defaultProps?: any;
}

// 组件映射
const componentMap = {
  'IntroComponent': React.lazy(() => import('./components/IntroComponent')),
  'MainComponent': React.lazy(() => import('./components/MainComponent')),
  'OutroComponent': React.lazy(() => import('./components/OutroComponent'))
};

export const ConfigDrivenRoot: React.FC = () => {
  return (
    <>
      {config.videos.map((video: VideoConfig) => (
        <Composition
          key={video.id}
          id={video.id}
          lazyComponent={() => import(`./components/${video.component}`)}
          width={video.width}
          height={video.height}
          fps={video.fps}
          durationInFrames={video.durationInFrames}
          defaultProps={video.defaultProps || {}}
        />
      ))}
    </>
  );
};
```

## 项目结构最佳实践

### 推荐的文件结构

```
src/
├── index.ts                 # registerRoot() 调用
├── Root.tsx                 # 根组件定义
├── components/              # 视频组件
│   ├── IntroComponent.tsx
│   ├── MainComponent.tsx
│   └── OutroComponent.tsx
├── compositions/            # 组合定义（可选）
│   ├── YouTubeCompositions.tsx
│   ├── TikTokCompositions.tsx
│   └── InstagramCompositions.tsx
├── utils/                   # 工具函数
└── assets/                  # 静态资源
```

### 模块化根组件

**src/Root.tsx**
```typescript
import React from "react";
import { YouTubeCompositions } from "./compositions/YouTubeCompositions";
import { TikTokCompositions } from "./compositions/TikTokCompositions";
import { InstagramCompositions } from "./compositions/InstagramCompositions";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <YouTubeCompositions />
      <TikTokCompositions />
      <InstagramCompositions />
    </>
  );
};
```

**src/compositions/YouTubeCompositions.tsx**
```typescript
import React from "react";
import { Composition, Folder } from "remotion";
import { YouTubeIntro } from "../components/YouTubeIntro";
import { YouTubeMain } from "../components/YouTubeMain";

export const YouTubeCompositions: React.FC = () => {
  return (
    <Folder name="YouTube 视频">
      <Composition
        id="youtube-intro"
        component={YouTubeIntro}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={90}
      />
      <Composition
        id="youtube-main"
        component={YouTubeMain}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={1800}
      />
    </Folder>
  );
};
```

## 错误处理

### 1. 重复注册检测

```typescript
import { registerRoot } from "remotion";
import { RemotionRoot } from "./Root";

let isRegistered = false;

if (!isRegistered) {
  registerRoot(RemotionRoot);
  isRegistered = true;
}
```

### 2. 环境检查

```typescript
import { registerRoot } from "remotion";

if (typeof window !== 'undefined') {
  // 仅在浏览器环境中注册
  import("./Root").then(({ RemotionRoot }) => {
    registerRoot(RemotionRoot);
  });
}
```

## 常见问题

### 1. Fast Refresh 问题
**问题**: 开发时组合重复注册
**解决**: 将 `registerRoot()` 放在单独的文件中

### 2. 异步加载问题
**问题**: 组件未正确加载
**解决**: 使用 `lazyComponent` 或确保异步操作完成后再注册

### 3. 类型安全
**问题**: TypeScript 类型错误
**解决**: 确保根组件返回正确的 JSX 类型

```typescript
import React from "react";

export const RemotionRoot: React.FC = (): React.ReactElement => {
  return (
    <>
      {/* 组合定义 */}
    </>
  );
};
```

## 相关 API

- [`<Composition>`](./Composition.md) - 定义视频组合
- [`<Folder>`](./Folder.md) - 组织组合
- [`<Still>`](./Still.md) - 静态图像组合
- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`continueRender()`](./continueRender.md) - 继续渲染

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/register-root.ts)
