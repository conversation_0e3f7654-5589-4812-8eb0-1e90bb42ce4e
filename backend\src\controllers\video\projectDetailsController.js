/**
 * @文件名: projectDetailsController.js
 * @功能概述: 获取项目详细信息的控制器
 * @创建时间: 2025-07-16
 * @作者: Augment Agent
 * @描述: 
 *   此控制器负责获取特定项目的详细信息，包括完整的editorData格式数据。
 *   支持数据完整性检查和格式转换，确保前端可以直接使用返回的数据。
 *   遵循API创建指导规则，采用单一职责原则。
 */

// 导入必要的模块
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const PathHelper = require('../../utils/pathHelper');

/**
 * @功能概述: 获取项目详细信息
 * @接口路径: GET /api/video/projectDetails
 * @请求参数:
 *   - videoIdentifier (必需): 项目标识符
 * @响应格式: 
 *   - status: success/error
 *   - message: 响应消息
 *   - data: { videoIdentifier, editorData, projectInfo }
 * @错误处理: 
 *   - 项目不存在：返回404错误
 *   - 数据不完整：返回警告信息
 *   - 参数错误：返回参数验证错误
 */
const projectDetails = async (req, res) => {
    const logPrefix = '[文件：projectDetailsController.js][projectDetails]';
    
    try {
        logger.info(`${logPrefix} 开始获取项目详情`);
        
        // === 步骤1: 参数验证 ===
        const videoIdentifier = req.query.videoIdentifier;
        
        if (!videoIdentifier) {
            logger.warn(`${logPrefix} 缺少必需参数: videoIdentifier`);
            return res.status(400).json({
                status: 'error',
                message: '缺少必需参数: videoIdentifier',
                errorCode: 'MISSING_PARAMETER',
                details: {
                    parameter: 'videoIdentifier',
                    suggestion: '请提供有效的项目标识符'
                }
            });
        }
        
        logger.info(`${logPrefix} 请求项目详情: ${videoIdentifier}`);
        
        // === 步骤2: 验证项目存在性 ===
        if (!PathHelper.projectExists(videoIdentifier)) {
            logger.warn(`${logPrefix} 项目不存在: ${videoIdentifier}`);
            return res.status(404).json({
                status: 'error',
                message: '项目不存在',
                errorCode: 'PROJECT_NOT_FOUND',
                details: {
                    videoIdentifier: videoIdentifier,
                    suggestion: '请检查项目ID是否正确'
                }
            });
        }
        
        // === 步骤3: 收集项目文件信息 ===
        logger.info(`${logPrefix} 开始收集项目文件信息`);
        const files = await collectProjectFiles(videoIdentifier);
        
        if (!files.source || Object.keys(files.source).length === 0) {
            logger.warn(`${logPrefix} 项目缺少原始视频文件: ${videoIdentifier}`);
            return res.status(400).json({
                status: 'error',
                message: '项目缺少原始视频文件',
                errorCode: 'MISSING_SOURCE_FILE',
                details: {
                    videoIdentifier: videoIdentifier,
                    suggestion: '该项目可能已损坏，请重新上传视频'
                }
            });
        }
        
        // === 步骤4: 生成文件URLs ===
        logger.info(`${logPrefix} 生成文件访问URLs`);
        const fileUrls = PathHelper.generateFileUrls(videoIdentifier, files);
        
        // === 步骤5: 构建editorData ===
        logger.info(`${logPrefix} 构建editorData格式数据`);
        const editorData = await buildEditorData(videoIdentifier, fileUrls, files);
        
        // === 步骤6: 获取项目基础信息 ===
        logger.info(`${logPrefix} 获取项目基础信息`);
        const projectInfo = await getProjectInfo(videoIdentifier, files);
        
        // === 步骤7: 数据完整性检查 ===
        const dataIntegrity = checkDataIntegrity(editorData);
        
        // === 步骤8: 返回结果 ===
        const response = {
            status: 'success',
            message: '项目详情获取成功',
            data: {
                videoIdentifier: videoIdentifier,
                editorData: editorData,
                projectInfo: projectInfo,
                dataIntegrity: dataIntegrity
            }
        };
        
        logger.info(`${logPrefix} 项目详情获取成功: ${videoIdentifier}`);
        res.json(response);
        
    } catch (error) {
        logger.error(`${logPrefix} 获取项目详情失败: ${error.message}`);
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            status: 'error',
            message: '获取项目详情失败',
            errorCode: 'GET_PROJECT_DETAILS_FAILED',
            details: {
                error: error.message,
                suggestion: '请检查服务器状态或联系管理员'
            }
        });
    }
};

/**
 * @功能概述: 收集项目所有文件信息
 * @参数: videoIdentifier - 项目标识符
 * @返回: 文件信息对象 { source: {}, processed: {}, generated: {} }
 */
const collectProjectFiles = async (videoIdentifier) => {
    const logPrefix = '[文件：projectDetailsController.js][collectProjectFiles]';
    
    const files = {
        source: {},
        processed: {},
        generated: {}
    };
    
    try {
        // 收集source目录文件
        const sourceDir = PathHelper.getSourceDir(videoIdentifier);
        if (fs.existsSync(sourceDir)) {
            const sourceFiles = fs.readdirSync(sourceDir);
            sourceFiles.forEach(file => {
                files.source[file] = file;
            });
            logger.debug(`${logPrefix} source文件: ${sourceFiles.join(', ')}`);
        }
        
        // 收集processed目录文件
        const processedDir = PathHelper.getProcessedDir(videoIdentifier);
        if (fs.existsSync(processedDir)) {
            const processedFiles = fs.readdirSync(processedDir);
            processedFiles.forEach(file => {
                const key = getFileKey(file);
                files.processed[key] = file;
            });
            logger.debug(`${logPrefix} processed文件: ${processedFiles.join(', ')}`);
        }
        
        // 收集generated目录文件
        const generatedDir = PathHelper.getGeneratedDir(videoIdentifier);
        if (fs.existsSync(generatedDir)) {
            const generatedFiles = fs.readdirSync(generatedDir);
            generatedFiles.forEach(file => {
                const key = getFileKey(file);
                files.generated[key] = file;
            });
            logger.debug(`${logPrefix} generated文件: ${generatedFiles.join(', ')}`);
        }
        
        return files;
        
    } catch (error) {
        logger.error(`${logPrefix} 收集文件信息失败: ${error.message}`);
        throw error;
    }
};

/**
 * @功能概述: 根据文件名生成键名
 * @参数: fileName - 文件名
 * @返回: 键名字符串
 */
const getFileKey = (fileName) => {
    const ext = path.extname(fileName).toLowerCase();
    const baseName = path.basename(fileName, ext);
    
    // 根据文件类型和名称模式生成键名
    if (ext === '.mp3') {
        return 'audio';
    } else if (ext === '.srt') {
        if (baseName.includes('english')) {
            return 'englishSrt';
        } else if (baseName.includes('chinese')) {
            return 'chineseSrt';
        } else {
            return 'subtitle';
        }
    } else if (ext === '.json') {
        if (baseName.includes('transcription')) {
            return 'transcription';
        } else if (baseName.includes('corrected')) {
            return 'corrected';
        } else {
            return 'data';
        }
    } else if (ext === '.mp4') {
        return 'video';
    } else {
        return baseName;
    }
};

/**
 * @功能概述: 构建editorData格式数据
 * @参数: videoIdentifier, fileUrls, files
 * @返回: editorData对象
 */
const buildEditorData = async (videoIdentifier, fileUrls, files) => {
    const logPrefix = '[文件：projectDetailsController.js][buildEditorData]';
    
    try {
        // 获取原始视频信息
        const originalVideoFile = Object.keys(files.source)[0];
        const originalVideoName = originalVideoFile || 'unknown.mp4';
        
        // 读取英文字幕内容
        let englishSrtContent = null;

        // 优先尝试读取SRT文件
        if (fileUrls.processed.englishSrt) {
            try {
                const englishSrtPath = path.join(PathHelper.getProcessedDir(videoIdentifier), files.processed.englishSrt);
                if (fs.existsSync(englishSrtPath)) {
                    englishSrtContent = fs.readFileSync(englishSrtPath, 'utf8');
                    logger.debug(`${logPrefix} 成功读取英文SRT字幕内容`);
                }
            } catch (error) {
                logger.warn(`${logPrefix} 读取英文SRT字幕失败: ${error.message}`);
            }
        }

        // 如果没有SRT文件，尝试从JSON文件生成SRT内容
        if (!englishSrtContent && (fileUrls.processed.transcription || fileUrls.processed.data)) {
            try {
                let jsonFilePath = null;
                let jsonFileName = null;

                // 优先使用transcription文件
                if (fileUrls.processed.transcription) {
                    jsonFileName = files.processed.transcription;
                    jsonFilePath = path.join(PathHelper.getProcessedDir(videoIdentifier), jsonFileName);
                } else if (fileUrls.processed.data) {
                    jsonFileName = files.processed.data;
                    jsonFilePath = path.join(PathHelper.getProcessedDir(videoIdentifier), jsonFileName);
                }

                if (jsonFilePath && fs.existsSync(jsonFilePath)) {
                    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
                    const transcriptionData = JSON.parse(jsonContent);

                    // 从转录数据生成SRT内容
                    englishSrtContent = generateSrtFromTranscription(transcriptionData);
                    logger.debug(`${logPrefix} 成功从JSON文件生成英文SRT字幕内容，源文件: ${jsonFileName}`);
                }
            } catch (error) {
                logger.warn(`${logPrefix} 从JSON生成SRT字幕失败: ${error.message}`);
            }
        }

        // 如果没有SRT文件，尝试从JSON文件生成SRT内容
        if (!englishSrtContent && (fileUrls.processed.transcription || fileUrls.processed.data)) {
            try {
                let jsonFilePath = null;

                // 优先使用transcription文件
                if (fileUrls.processed.transcription) {
                    jsonFilePath = path.join(PathHelper.getProcessedDir(videoIdentifier), files.processed.transcription);
                } else if (fileUrls.processed.data) {
                    jsonFilePath = path.join(PathHelper.getProcessedDir(videoIdentifier), files.processed.data);
                }

                if (jsonFilePath && fs.existsSync(jsonFilePath)) {
                    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
                    const transcriptionData = JSON.parse(jsonContent);

                    // 从转录数据生成SRT内容
                    englishSrtContent = generateSrtFromTranscription(transcriptionData);
                    logger.debug(`${logPrefix} 成功从JSON文件生成英文SRT字幕内容`);
                }
            } catch (error) {
                logger.warn(`${logPrefix} 从JSON生成SRT字幕失败: ${error.message}`);
            }
        }
        
        // 构建editorData对象（与前端uploadVideo函数中的格式保持一致）
        const editorData = {
            // 视频播放URL（web链接）
            videoPlaybackUrl: fileUrls.source[originalVideoFile] || null,
            // 视频标识符
            videoIdentifier: videoIdentifier,
            // 原始视频文件路径（这里使用URL代替）
            originalVideoPath: fileUrls.source[originalVideoFile] || null,
            // 原始视频文件名
            originalVideoName: originalVideoName,
            // 英文字幕内容（SRT格式）
            englishSrtContent: englishSrtContent,
            // 额外的URL信息（供前端使用）
            audioFileUrl: fileUrls.processed.audio || null,
            transcriptionJsonUrl: fileUrls.processed.transcription || null,
            englishSrtUrl: fileUrls.processed.englishSrt || null,
            chineseSrtUrl: fileUrls.processed.chineseSrt || null
        };
        
        logger.info(`${logPrefix} editorData构建完成`);
        return editorData;
        
    } catch (error) {
        logger.error(`${logPrefix} 构建editorData失败: ${error.message}`);
        throw error;
    }
};

/**
 * @功能概述: 获取项目基础信息
 * @参数: videoIdentifier, files
 * @返回: 项目信息对象
 */
const getProjectInfo = async (videoIdentifier, files) => {
    const logPrefix = '[文件：projectDetailsController.js][getProjectInfo]';
    
    try {
        const sourceDir = PathHelper.getSourceDir(videoIdentifier);
        const originalVideoFile = Object.keys(files.source)[0];
        
        if (!originalVideoFile) {
            throw new Error('找不到原始视频文件');
        }
        
        const originalVideoPath = path.join(sourceDir, originalVideoFile);
        const stats = fs.statSync(originalVideoPath);
        
        const projectInfo = {
            uploadTime: stats.birthtime.toISOString(),
            originalVideoName: originalVideoFile,
            lastModified: stats.mtime.toISOString(),
            fileSize: formatFileSize(stats.size),
            hasProcessedFiles: Object.keys(files.processed).length > 0,
            hasGeneratedFiles: Object.keys(files.generated).length > 0
        };
        
        return projectInfo;
        
    } catch (error) {
        logger.error(`${logPrefix} 获取项目信息失败: ${error.message}`);
        throw error;
    }
};

/**
 * @功能概述: 检查数据完整性
 * @参数: editorData
 * @返回: 完整性检查结果
 */
const checkDataIntegrity = (editorData) => {
    const requiredFields = ['videoIdentifier', 'videoPlaybackUrl', 'originalVideoName'];
    const missingFields = requiredFields.filter(field => !editorData[field]);
    
    return {
        isComplete: missingFields.length === 0,
        missingFields: missingFields,
        hasEnglishSubtitle: !!editorData.englishSrtContent,
        hasAudioFile: !!editorData.audioFileUrl,
        hasTranscription: !!editorData.transcriptionJsonUrl
    };
};

/**
 * @功能概述: 从转录数据生成SRT字幕内容
 * @参数: transcriptionData - 转录数据对象
 * @返回: SRT格式的字幕内容字符串
 */
const generateSrtFromTranscription = (transcriptionData) => {
    const logPrefix = '[文件：projectDetailsController.js][generateSrtFromTranscription]';

    try {
        let srtContent = '';
        let segments = [];

        // 处理不同的JSON数据格式
        if (transcriptionData.segments && Array.isArray(transcriptionData.segments)) {
            // 标准转录格式
            segments = transcriptionData.segments;
        } else if (Array.isArray(transcriptionData)) {
            // 简化字幕JSON格式
            segments = transcriptionData;
        } else {
            logger.warn(`${logPrefix} 无法识别的转录数据格式`);
            return null;
        }

        // 生成SRT内容
        segments.forEach((segment, index) => {
            const id = segment.id || (index + 1);
            const startTime = formatTimeForSRT(segment.start);
            const endTime = formatTimeForSRT(segment.end);
            const text = segment.text || '';

            srtContent += `${id}\r\n`;
            srtContent += `${startTime} --> ${endTime}\r\n`;
            srtContent += `${text}\r\n\r\n`;
        });

        // 移除末尾多余的空行
        if (srtContent.endsWith('\r\n\r\n')) {
            srtContent = srtContent.slice(0, -2);
        }

        logger.debug(`${logPrefix} SRT内容生成成功，共 ${segments.length} 个片段`);
        return srtContent;

    } catch (error) {
        logger.error(`${logPrefix} 生成SRT内容失败: ${error.message}`);
        return null;
    }
};

/**
 * @功能概述: 将秒数转换为SRT时间格式
 * @参数: seconds - 秒数
 * @返回: SRT时间格式字符串 (HH:MM:SS,mmm)
 */
const formatTimeForSRT = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds % 1) * 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

/**
 * @功能概述: 格式化文件大小
 * @参数: bytes - 字节数
 * @返回: 格式化的文件大小字符串
 */
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 导出控制器函数
module.exports = {
    projectDetails
};
