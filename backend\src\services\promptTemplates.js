/**
 * @功能概述: 提示模板模块，负责存储、管理和生成与 LLM 交互所需的提示(prompts)。
 */

// 导入日志工具 logger
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

// 模块级日志前缀
const moduleLogPrefix = `[文件：promptTemplates.js][提示模板服务][模块初始化]`;
const promptsDir = path.join(__dirname, '../prompts'); // 定义模板文件目录的基路径

/**
 * @说明: loadedTemplates 用于缓存从文件系统加载的模板。
 *       结构为: 
 *       {
 *         "taskType1": {
 *           "templateNameA": { content: "...", filePath: "..." },
 *           "templateNameB": { content: "...", filePath: "..." }
 *         },
 *         "taskType2": {
 *           "templateNameC": { content: "...", filePath: "..." }
 *         }
 *       }
 */
let loadedTemplates = {}; // 用于缓存加载的模板

/**
 * @功能概述: 递归加载指定目录下的所有模板文件。
 *           文件内容作为模板字符串，文件名（不含扩展名）作为 templateName，
 *           目录名作为 taskType。
 * @param {string} currentPath - 当前要扫描的目录路径。
 * @param {string} currentTaskType - (可选) 当前路径对应的任务类型。
 */
function loadTemplatesRecursive(currentPath, currentTaskType = null) {
    const logPrefix = `[文件：promptTemplates.js][提示模板服务][loadTemplatesRecursive]`;
    try {
        const entries = fs.readdirSync(currentPath, { withFileTypes: true });
        for (const entry of entries) {
            const entryPath = path.join(currentPath, entry.name);
            if (entry.isDirectory()) {
                // 如果是目录，将其名称作为 taskType 递归加载
                loadTemplatesRecursive(entryPath, entry.name);
            } else if (entry.isFile() && (entry.name.endsWith('.txt') || entry.name.endsWith('.md'))) {
                // 如果是 .txt 或 .md 文件，并且我们已经确定了 taskType (即它在一个子目录里)
                if (currentTaskType) {
                    const templateName = path.parse(entry.name).name; // 文件名作为 templateName
                    const templateContent = fs.readFileSync(entryPath, 'utf-8');

                    if (!loadedTemplates[currentTaskType]) {
                        loadedTemplates[currentTaskType] = {};
                    }
                    // 存储模板内容和其原始文件路径
                    loadedTemplates[currentTaskType][templateName] = { 
                        content: templateContent,
                        filePath: entryPath 
                    };
                    // logger.debug(`${logPrefix}成功加载并缓存模板: ${currentTaskType}.${templateName} (源文件: ${entryPath})`);
                    // 新增：针对 CORRECT_TRANSCRIPTION.default 的特定日志
                    if (currentTaskType === 'CORRECT_TRANSCRIPTION' && templateName === 'default') {
                        logger.info(`${logPrefix}[特定模板加载日志] 成功加载并缓存 CORRECT_TRANSCRIPTION.default。源文件: ${entryPath}, 内容长度: ${templateContent.length}`);
                    } else {
                        logger.debug(`${logPrefix}成功加载并缓存模板: ${currentTaskType}.${templateName} (源文件: ${entryPath})`);
                    }
                } else {
                    // 如果模板文件直接放在 promptsDir 根目录下，可以考虑将其归类到一个通用的 taskType，如 'general'
                    // 或者要求所有模板必须在 taskType 子目录下
                    logger.warn(`${logPrefix}模板文件 ${entryPath} 直接位于根模板目录，未指定 taskType，已跳过。`);
                }
            }
        }
    } catch (error) {
        logger.error(`${logPrefix}加载模板文件时出错 (目录: ${currentPath}): ${error.message}`);
        // 根据需要决定是否抛出错误，或者允许应用在缺少部分模板的情况下启动
    }
}

// 应用启动时执行一次模板加载
try {
    logger.info(`${moduleLogPrefix}开始加载外部模板文件从目录: ${promptsDir}`);
    if (fs.existsSync(promptsDir)) {
        loadTemplatesRecursive(promptsDir); // 从 promptsDir 开始递归加载
        // 构建加载的模板类型及其文件数量的摘要，用于日志记录
        const loadedSummary = Object.keys(loadedTemplates).map(taskType => {
            return `${taskType} (${Object.keys(loadedTemplates[taskType]).length} 个模板)`;
        }).join(', ');
        logger.info(`${moduleLogPrefix}外部模板文件加载完成。已加载: ${loadedSummary || '无模板加载'}`);
    } else {
        logger.warn(`${moduleLogPrefix}模板目录 ${promptsDir} 不存在，未加载任何外部模板。`);
        loadedTemplates = {}; // 确保 loadedTemplates 是一个空对象
    }
} catch (error) {
    logger.error(`${moduleLogPrefix}初始化加载模板失败: ${error.message}`);
    loadedTemplates = {}; // 出错时确保 loadedTemplates 为空对象，避免后续出错
}





/**
 * @功能概述: 根据任务类型和可选的特定模板名称获取原始提示模板对象 (从缓存中)。
 * @param {string} taskType - 任务类型。
 * @param {string} [templateName='default'] - (可选) 特定模板的名称。
 * @returns {object|null} 包含 { content: string, filePath: string } 的对象，如果找不到则返回 null。
 */
function getRawTemplate(taskType, templateName = 'default') {
    const logPrefix = `[文件：promptTemplates.js][提示模板服务][getRawTemplate] `;
    logger.debug(`${logPrefix}请求模板对象 (从缓存)。任务类型: ${taskType}, 模板名: ${templateName}`);

    if (loadedTemplates[taskType]) {
        const taskTemplates = loadedTemplates[taskType];
        if (taskTemplates[templateName]) {
            const templateObject = taskTemplates[templateName];
            logger.debug(`${logPrefix}从缓存找到指定模板对象: ${taskType}.${templateName} (源文件: ${templateObject.filePath})`);
            return templateObject; // 返回模板对象 { content, filePath }
        }
        if (taskTemplates['default']) {
            const defaultTemplateObject = taskTemplates['default'];
            logger.warn(`${logPrefix}未从缓存找到指定模板对象 ${taskType}.${templateName}，回退到 ${taskType}.default (源文件: ${defaultTemplateObject.filePath})。`);
            return defaultTemplateObject; // 返回默认模板对象 { content, filePath }
        }
    }
    // logger.warn(`${logPrefix}未从缓存找到任务类型 ${taskType} 或其默认模板对象。`);
    // 新增：更详细的未找到模板时的日志
    if (!loadedTemplates[taskType]) {
        logger.warn(`${logPrefix}任务类型 '${taskType}' 在 loadedTemplates 中未找到。可用的任务类型有: ${Object.keys(loadedTemplates).join(', ') || '无'}`);
    } else {
        // taskType 存在, 但指定的 templateName (或其 'default' 回退) 不存在。
        const availableTemplatesForTask = Object.keys(loadedTemplates[taskType]).join(', ') || '无';
        logger.warn(`${logPrefix}在任务类型 '${taskType}' 下未找到模板 '${templateName}' (当请求的模板名称不是 'default' 时，也尝试了回退到 '${taskType}.default'，但仍未找到)。 '${taskType}' 下可用的模板有: ${availableTemplatesForTask}`);
    }
    return null;
}






/**
 * @功能概述: 将参数值插入到模板字符串中，替换对应的占位符。
 * @param {string} templateString - 包含占位符的模板字符串。
 * @param {object} params - 包含替换值的参数对象。
 * @returns {string} 插值后的结果字符串。
 */
function interpolatePrompt(templateString, params) {
    const logPrefix = `[文件：promptTemplates.js][提示模板服务][interpolatePrompt]`;
    
    if (!templateString) {
        logger.warn(`${logPrefix}[WARN] 模板字符串为空或未定义。`);
        return '';
    }

    if (!params || typeof params !== 'object') {
        logger.warn(`${logPrefix}[WARN] 参数对象为空或不是有效对象。`);
        return templateString;
    }
    
    // 记录所有参数，辅助调试
    logger.debug(`${logPrefix} 开始插值处理。参数键: ${Object.keys(params).join(', ')}`);
    
    // 原始模板副本，用于后续检查未替换占位符
    const originalTemplate = templateString;
    
    // 1. 使用正则表达式查找所有{{paramName}}格式的占位符
    const placeholderRegex = /\{\{([^{}]+)\}\}/g;
    const foundPlaceholders = [];
    let match;
    
    // 查找所有占位符并收集到数组
    while ((match = placeholderRegex.exec(templateString)) !== null) {
        foundPlaceholders.push(match[1].trim());
    }
    
    if (foundPlaceholders.length === 0) {
        logger.debug(`${logPrefix} 模板中未发现占位符。`);
        return templateString;
    }
    
    logger.debug(`${logPrefix} 模板中发现占位符: ${foundPlaceholders.join(', ')}`);
    
    // 检查参数中是否存在模板需要的所有占位符
    const missingParams = foundPlaceholders.filter(placeholder => 
        !(placeholder in params) && params[placeholder] !== '');
    
    if (missingParams.length > 0) {
        logger.warn(`${logPrefix}[WARN] 缺少占位符的参数值: ${missingParams.join(', ')}`);
        // 继续处理，仅替换有值的占位符
    }
    
    // 2. 执行占位符替换
    let interpolatedText = templateString;
    
    // 遍历参数对象进行替换
    for (const [key, value] of Object.entries(params)) {
        const placeholderRegex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
        
        // 检查该参数是否在模板中有对应占位符
        if (foundPlaceholders.includes(key) || foundPlaceholders.some(p => p.trim() === key.trim())) {
            if (value !== undefined && value !== null) {
                // 替换占位符为参数值
                interpolatedText = interpolatedText.replace(placeholderRegex, value);
                logger.debug(`${logPrefix} 替换占位符 {{${key}}} → 值长度: ${String(value).length}`);
            } else {
                logger.warn(`${logPrefix}[WARN] 参数 "${key}" 的值为 null 或 undefined。`);
                // 可以选择替换为空字符串或保留占位符
                interpolatedText = interpolatedText.replace(placeholderRegex, '');
            }
        }
    }
    
    // 3. 检查是否还有未替换的占位符
    const remainingPlaceholders = [];
    let remainingMatch;
    
    while ((remainingMatch = placeholderRegex.exec(interpolatedText)) !== null) {
        remainingPlaceholders.push(remainingMatch[1].trim());
    }
    
    if (remainingPlaceholders.length > 0) {
        logger.warn(`${logPrefix}[WARN] 结果中仍有未替换的占位符: ${remainingPlaceholders.join(', ')}`);
    } else {
        logger.debug(`${logPrefix} 所有占位符已成功替换。`);
    }
    
    // 如果原始模板和插值后的文本相同，但存在占位符，说明可能没有成功替换
    if (originalTemplate === interpolatedText && foundPlaceholders.length > 0) {
        logger.warn(`${logPrefix}[WARN] 插值后文本与原始模板相同，可能未成功替换占位符。`);
    }
    
    return interpolatedText;
}





/**
 * @功能概述: 根据任务类型、参数和可选的上下文动态选择并生成最终的提示字符串及源文件路径。
 * @param {string} taskType - 任务类型 (例如 'translate', 'optimize_subtitles')。
 * @param {object} params - 用于填充模板的参数对象。
 *                         对于 'translate', 可能需要 { textToTranslate: '...', targetLanguage: '...', sourceLanguage: '...', context: '...' (可选)}。
 *                         对于 'optimize_subtitles', 可能需要 { segmentsJson: '...' }。
 * @param {string} [preferredTemplateName='default'] - (可选) 优先选择的模板名称。
 * @returns {{finalPrompt: string, filePath: string}|null} 生成的提示对象；如果无法生成，则返回 null。
 * @执行流程:
 *   1. 根据 taskType 和 params 决定实际使用的 templateName (例如，如果 params 中有 context，则翻译任务可能使用 'withContext' 模板)。
 *   2.调用 getRawTemplate 获取原始模板。
 *   3. 如果获取到原始模板，则调用 interpolatePrompt 进行参数插值。
 *   4. 返回插值后的提示或 null。
 */
// 定义一个函数，用于根据任务类型、参数和可选的优先模板名称生成最终的提示字符串。
function generatePrompt(taskType, params, preferredTemplateName = 'default') {
    const logPrefix = `[文件：promptTemplates.js][提示模板服务][generatePrompt]`;
    
    // 新增：参数类型验证和详细日志记录
    logger.info(`${logPrefix}[步骤 1] 开始生成提示词。taskType: '${taskType}', preferredTemplateName: '${preferredTemplateName}'`);
    logger.debug(`${logPrefix}[步骤 1.1] 参数类型检查 - taskType: ${typeof taskType}, params: ${typeof params}, preferredTemplateName: ${typeof preferredTemplateName}`);
    
    // 参数类型验证
    if (typeof taskType !== 'string' || !taskType.trim()) {
        logger.error(`${logPrefix}[ERROR][步骤 1.2] taskType 参数无效：期望非空字符串，实际接收到 ${typeof taskType}: "${taskType}"`);
        return null;
    }
    
    if (typeof params !== 'object' || params === null) {
        logger.error(`${logPrefix}[ERROR][步骤 1.3] params 参数无效：期望对象类型，实际接收到 ${typeof params}: ${params}`);
        return null;
    }
    
    if (typeof preferredTemplateName !== 'string' || !preferredTemplateName.trim()) {
        logger.warn(`${logPrefix}[WARN][步骤 1.4] preferredTemplateName 参数无效，使用默认值 'default'。接收到: ${typeof preferredTemplateName}: "${preferredTemplateName}"`);
        preferredTemplateName = 'default';
    }
    
    logger.debug(`${logPrefix}[步骤 1.5] 参数验证通过，开始处理模板请求`);
    
    // 初始化实际使用的模板名称为优先模板名称。
    let templateNameToUse = preferredTemplateName;

    // B.1.1.2.3 动态模板选择逻辑示例：
    // 这是一个示例，展示如何根据输入参数动态选择不同的模板。
    // 如果任务类型是 'translate' 并且参数对象存在且包含 'context' 属性，
    if (taskType === 'translate' && params && params.context) {
        // 则将实际使用的模板名称更改为 'withContext'。
        templateNameToUse = 'withContext'; // 如果有上下文，则优先使用 withContext 模板
        // 记录信息级别日志，说明检测到上下文并尝试使用 'withContext' 模板。
        logger.info(`${logPrefix}检测到翻译任务包含上下文，尝试使用 'withContext' 模板。`);
    }
    // 未来可以根据 params 中的更多信息添加更复杂的动态选择逻辑
    // 这里可以添加更多基于 params 内容的条件判断，以选择更合适的模板。

    // 调用 getRawTemplate 函数，根据任务类型和确定的模板名称获取原始模板字符串。
    const templateObject = getRawTemplate(taskType, templateNameToUse);
    
    // 新增：详细的模板获取结果日志
    if (!templateObject) {
        logger.error(`${logPrefix}[ERROR][步骤 2.1] 无法获取模板对象。taskType: '${taskType}', templateName: '${templateNameToUse}'`);
        return null;
    }
    
    if (!templateObject.content) {
        logger.error(`${logPrefix}[ERROR][步骤 2.2] 模板对象缺少 content 属性。来源文件: ${templateObject.filePath || '未知'}`);
        return null;
    }
    
    logger.info(`${logPrefix}[步骤 2.3] 成功获取模板。来源: ${templateObject.filePath}, 内容长度: ${templateObject.content.length}`);

    // 使用 try-catch 块来捕获模板插值过程中可能发生的错误。
    try {
        // 调用 interpolatePrompt 函数，将原始模板字符串和参数对象进行插值，生成最终提示。
        const finalPrompt = interpolatePrompt(templateObject.content, params);
        // 记录信息级别日志，表示成功生成提示，并记录使用的任务类型和模板名称。
        logger.info(`${logPrefix}成功生成提示。任务类型: ${taskType}, 使用模板: ${templateNameToUse} (源文件: ${templateObject.filePath})`);
        // 记录调试级别日志，输出生成提示内容 (完整)
        logger.debug(`${logPrefix}生成提示内容 (完整):\n${finalPrompt}`);
        logger.info(`${logPrefix}[SUCCESS] 提示词生成完成。最终长度: ${finalPrompt.length}`);
        // 新增：返回值验证
        if (!finalPrompt || finalPrompt.trim().length === 0) {
            logger.error(`${logPrefix}[ERROR][步骤 X] 生成的最终提示词为空或无效`);
            return null;
        }
        
        const result = {
            prompt: finalPrompt,
            filePath: templateObject.filePath
        };
        
        return result;
    } catch (error) {
        // 如果在插值过程中发生错误，记录错误日志，包含错误信息。
        logger.error(`${logPrefix}生成提示时发生错误: ${error.message} (源模板文件: ${templateObject.filePath})`);
        // 记录错误堆栈，以便更好地定位问题。
        logger.error(`${logPrefix}错误堆栈: ${error.stack}`);
        // 返回 null，表示生成提示失败。
        return null;
    }
}










module.exports = {
    generatePrompt,    // @功能概述: 根据任务类型、参数和可选的上下文动态选择并生成最终的提示字符串。
    getRawTemplate,    // @功能概述: 根据任务类型和可选的特定模板名称获取原始提示模板对象。
    interpolatePrompt, // @功能概述: 将参数对象中的值填充到给定的模板字符串中。
    // templates, // 不再需要导出硬编码的 templates 对象
    loadedTemplates, // 可以选择性导出已加载的模板以供调试或查看
};

logger.info(`${moduleLogPrefix}提示模板服务方法已导出 (使用外部模板文件)。`); 