# TranscriptionCorrectionTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **simplifiedSubtitleJsonArray** (Array): 简化字幕JSON数组，来自SubtitleOptimizationTask
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **correctionModel** (string): LLM校正模型，默认'google/gemini-2.5-flash-lite-preview-06-17'
- **temperature** (number): 创造性控制参数，默认0.1
- **maxTokens** (number): 输出令牌限制，默认8000

## 2. 输出上下文参数 (Output Context)

- **correctedSubtitleJsonArray** (Array): 校正后的字幕JSON数组
- **correctedSubtitleJsonPath** (string): 校正后字幕JSON文件路径
- **correctedEnglishSrtContent** (string): 校正后的英文SRT内容
- **correctedEnglishSrtPath** (string): 校正后英文SRT文件路径
- **correctedFullText** (string): 校正后的完整文本（用于LLM上下文理解）
- **fullTranscriptText** (string): 完整转录文本（用于下游ContentSummarizationTask）
- **correctionStats** (object): 校正统计信息
  - **originalSegments** (number): 原始片段数
  - **correctedSegments** (number): 校正后片段数
  - **modelUsed** (string): 使用的LLM模型
  - **processingTime** (string): 处理时间戳
- **correctionStatus** (string): 校正状态，成功时为'success'
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 输入格式（simplifiedSubtitleJsonArray）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "hello everyone, how are you today",
    "words": [
      {"text": "hello", "start": 0.16, "end": 0.8},
      {"text": "everyone", "start": 0.8, "end": 1.5}
    ]
  }
]
```

### LLM提示词参数格式
```json
{
  "language_of_text": "English",
  "correctedFullText": "完整转录文本用于理解上下文...",
  "segments_json_array_string": "[{\"id\":\"1\",\"start\":0.16,\"end\":4.32,\"text\":\"hello everyone, how are you today\"}]"
}
```

### LLM输出格式（校正后）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "Hello everyone, how are you today?"
  }
]
```

### 最终输出格式（重新合并words字段）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "Hello everyone, how are you today?",
    "words": [
      {"text": "Hello", "start": 0.16, "end": 0.8},
      {"text": "everyone", "start": 0.8, "end": 1.5}
    ]
  }
]
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 校正后字幕JSON文件
- **.srt**: 校正后英文SRT文件

### 文件命名规则
- **校正字幕**: `{videoIdentifier}_corrected_subtitle.json`
- **校正SRT**: `{videoIdentifier}_corrected_english.srt`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保字符正确保存

## 5. 执行逻辑概述

转录校正任务负责使用LLM对字幕文本进行智能校正，提高转录质量和准确性。任务首先验证输入数据的完整性，然后构建包含上下文信息的LLM请求。使用TRANSCRIPTION_CORRECTION提示模板调用LLM服务，对语法错误、拼写错误和语义不通顺的地方进行修正。校正过程保持原有的时间戳和词级别信息不变，只修正文本内容。任务生成两个重要的文本字段：correctedFullText用于LLM上下文理解，fullTranscriptText用于下游的内容总结任务。整个过程提供详细的统计信息和错误处理，确保校正质量和数据完整性。
