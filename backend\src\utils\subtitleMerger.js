/**
 * @功能概述: 字幕合并工具，负责智能合并英文字幕块以减少字幕条数
 *           基于句子完整性和长度限制进行合并，确保时间同步准确性
 * @输入依赖: SRT格式字符串
 * @输出结果: 优化后的SRT格式字符串
 * @外部依赖: 无
 * @失败策略: 合并过程出错时返回原始内容，不中断整体流程
 */

const logger = require('./logger');

// 模块级日志前缀
const moduleLogPrefix = '[文件：subtitleMerger.js][字幕合并工具][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);

/**
 * @功能概述: 智能合并英文字幕块，减少字幕条数同时保持时间准确性
 * @param {string} srtContent - 原始SRT格式字符串
 * @param {string} logPrefix - 日志前缀，用于追踪调用来源
 * @returns {string} 合并优化后的SRT格式字符串
 * @执行流程:
 *   1. 解析SRT内容为结构化数据
 *   2. 遍历并判断合并条件
 *   3. 执行智能合并操作
 *   4. 重新编号和生成SRT
 */
function mergeSubtitleBlocks(srtContent, logPrefix = '[字幕合并]') {
    try {
        logger.info(`${logPrefix}[步骤 4.5] 开始英文字幕智能合并处理。`);
        
        // 步骤 1: 解析SRT内容
        const blocks = parseSrtToBlocks(srtContent, logPrefix);
        const originalCount = blocks.length;
        
        if (originalCount === 0) {
            logger.warn(`${logPrefix}[步骤 4.5.1] SRT内容为空，跳过合并处理。`);
            return srtContent;
        }
        
        logger.info(`${logPrefix}[步骤 4.5.1] SRT解析完成。原始字幕块数: ${originalCount}`);
        
        // 步骤 2: 执行智能合并
        const mergedBlocks = performSmartMerge(blocks, logPrefix);
        const finalCount = mergedBlocks.length;
        const reducedCount = originalCount - finalCount;
        
        logger.info(`${logPrefix}[步骤 4.5.2] 字幕合并完成。最终字幕块数: ${finalCount}, 减少: ${reducedCount}条`);
        
        // 步骤 3: 重新生成SRT
        const mergedSrtContent = blocksToSrt(mergedBlocks, logPrefix);
        
        logger.info(`${logPrefix}[步骤 4.5.3] 合并后SRT生成成功。内容长度: ${mergedSrtContent.length} 字符`);
        
        return mergedSrtContent;
        
    } catch (error) {
        logger.error(`${logPrefix}[ERROR][步骤 4.5] 字幕合并处理失败: ${error.message}`);
        logger.warn(`${logPrefix}[WARN][步骤 4.5] 返回原始SRT内容，继续后续处理。`);
        return srtContent; // 出错时返回原始内容，不中断流程
    }
}

/**
 * @功能概述: 解析SRT格式字符串为结构化字幕块数组
 * @param {string} srtContent - SRT格式字符串
 * @param {string} logPrefix - 日志前缀
 * @returns {Array} 字幕块对象数组
 */
function parseSrtToBlocks(srtContent, logPrefix) {
    const blocks = [];
    const lines = srtContent.trim().split('\n');
    let currentBlock = null;
    
    logger.debug(`${logPrefix}[parseSrtToBlocks] 开始解析，总行数: ${lines.length}`);
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // 空行表示一个字幕块结束
        if (line === '') {
            if (currentBlock && currentBlock.number && currentBlock.timeRange && currentBlock.text) {
                blocks.push(currentBlock);
                currentBlock = null;
            }
            continue;
        }
        
        // 判断是否为序号行（纯数字）
        if (/^\d+$/.test(line)) {
            if (currentBlock && currentBlock.number && currentBlock.timeRange && currentBlock.text) {
                blocks.push(currentBlock);
            }
            currentBlock = {
                number: parseInt(line),
                timeRange: '',
                text: '',
                startTime: '',
                endTime: ''
            };
        }
        // 判断是否为时间码行
        else if (line.includes('-->')) {
            if (currentBlock) {
                currentBlock.timeRange = line;
                const timeMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
                if (timeMatch) {
                    currentBlock.startTime = timeMatch[1];
                    currentBlock.endTime = timeMatch[2];
                }
            }
        }
        // 文本行
        else {
            if (currentBlock) {
                if (currentBlock.text) {
                    currentBlock.text += '\n' + line;
                } else {
                    currentBlock.text = line;
                }
            }
        }
    }
    
    // 处理最后一个块
    if (currentBlock && currentBlock.number && currentBlock.timeRange && currentBlock.text) {
        blocks.push(currentBlock);
    }
    
    logger.debug(`${logPrefix}[parseSrtToBlocks] 解析完成，得到 ${blocks.length} 个字幕块`);
    return blocks;
}

/**
 * @功能概述: 执行智能合并，根据句子完整性和长度限制合并字幕块
 * @param {Array} blocks - 原始字幕块数组
 * @param {string} logPrefix - 日志前缀
 * @returns {Array} 合并后的字幕块数组
 */
function performSmartMerge(blocks, logPrefix) {
    const mergedBlocks = [];
    let mergeCount = 0;
    
    for (let i = 0; i < blocks.length; i++) {
        const currentBlock = blocks[i];
        const nextBlock = blocks[i + 1];
        
        // 检查是否可以与下一个块合并
        if (nextBlock && shouldMerge(currentBlock, nextBlock, logPrefix)) {
            // 执行合并
            const mergedBlock = mergeBlocks(currentBlock, nextBlock, logPrefix);
            mergedBlocks.push(mergedBlock);
            mergeCount++;
            
            logger.debug(`${logPrefix}[步骤 4.5.2.${mergeCount}] 合并字幕块 ${currentBlock.number}+${nextBlock.number}: "${mergedBlock.text.substring(0, 50)}..." (${countWords(mergedBlock.text)}单词)`);
            
            i++; // 跳过下一个块，因为已经被合并了
        } else {
            // 不合并，直接添加当前块
            mergedBlocks.push({ ...currentBlock });
        }
    }
    
    logger.debug(`${logPrefix}[performSmartMerge] 执行了 ${mergeCount} 次合并操作`);
    return mergedBlocks;
}

/**
 * @功能概述: 判断两个字幕块是否应该合并
 * @param {object} currentBlock - 当前字幕块
 * @param {object} nextBlock - 下一个字幕块
 * @param {string} logPrefix - 日志前缀
 * @returns {boolean} 是否应该合并
 */
function shouldMerge(currentBlock, nextBlock, logPrefix) {
    // 条件1: 当前文本不以句号结尾
    const currentText = currentBlock.text.trim();
    if (currentText.endsWith('.')) {
        return false;
    }
    
    // 条件2: 下一个文本以句号结尾
    const nextText = nextBlock.text.trim();
    if (!nextText.endsWith('.')) {
        return false;
    }
    
    // 条件3: 合并后文本不超过20个单词
    const mergedText = currentText + ' ' + nextText;
    const wordCount = countWords(mergedText);
    if (wordCount > 20) {
        return false;
    }
    
    return true;
}

/**
 * @功能概述: 合并两个字幕块
 * @param {object} currentBlock - 当前字幕块
 * @param {object} nextBlock - 下一个字幕块
 * @param {string} logPrefix - 日志前缀
 * @returns {object} 合并后的字幕块
 */
function mergeBlocks(currentBlock, nextBlock, logPrefix) {
    return {
        number: currentBlock.number, // 保持当前块的序号（稍后重新编号）
        timeRange: `${currentBlock.startTime} --> ${nextBlock.endTime}`, // 合并时间范围
        text: currentBlock.text.trim() + ' ' + nextBlock.text.trim(), // 合并文本
        startTime: currentBlock.startTime, // 保持开始时间
        endTime: nextBlock.endTime // 使用结束时间
    };
}

/**
 * @功能概述: 计算文本中的单词数量
 * @param {string} text - 要计算的文本
 * @returns {number} 单词数量
 */
function countWords(text) {
    if (!text || typeof text !== 'string') {
        return 0;
    }
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * @功能概述: 将字幕块数组转换回SRT格式字符串
 * @param {Array} blocks - 字幕块数组
 * @param {string} logPrefix - 日志前缀
 * @returns {string} SRT格式字符串
 */
function blocksToSrt(blocks, logPrefix) {
    let srtContent = '';
    
    // 重新编号并生成SRT
    blocks.forEach((block, index) => {
        const newNumber = index + 1;
        srtContent += `${newNumber}\n`;
        srtContent += `${block.timeRange}\n`;
        srtContent += `${block.text}\n\n`;
    });
    
    logger.debug(`${logPrefix}[blocksToSrt] 生成SRT完成，重新编号为 1-${blocks.length}`);
    return srtContent.trim(); // 移除末尾额外的换行符
}

module.exports = {
    mergeSubtitleBlocks
};

logger.info(`${moduleLogPrefix}subtitleMerger 工具已导出。`); 