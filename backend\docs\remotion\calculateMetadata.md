# calculateMetadata()

**版本**: v4.0.0+  
**类型**: 函数  
**用途**: 动态计算和转换 Composition 元数据

## 功能概述

`calculateMetadata` 是传递给 `<Composition>` 组件的回调函数，用于动态转换视频元数据。它是实现数据驱动视频生成的核心功能，允许根据输入数据动态调整视频时长、分辨率、帧率等属性。

## 使用场景

1. **动态元数据**: 根据数据动态设置 `durationInFrames`、`width`、`height`、`fps`
2. **数据预处理**: 在渲染前进行数据获取和转换
3. **默认编解码器**: 为特定组合设置默认编解码器
4. **格式配置**: 设置默认视频图像格式或像素格式
5. **一次性初始化**: 在渲染开始前运行初始化代码

## 基本语法

```tsx
import { CalculateMetadataFunction, Composition } from 'remotion';

const calculateMetadata: CalculateMetadataFunction<MyComponentProps> = ({
  props,
  defaultProps,
  abortSignal,
  compositionId
}) => {
  return {
    durationInFrames: props.duration,
    props: transformedProps,
    defaultCodec: 'h264',
    defaultVideoImageFormat: 'png',
    defaultPixelFormat: 'yuv420p',
  };
};

export const Root: React.FC = () => {
  return (
    <Composition
      id="MyComp"
      component={MyComponent}
      durationInFrames={300}
      fps={30}
      width={1920}
      height={1080}
      calculateMetadata={calculateMetadata}
    />
  );
};
```

## 函数参数

### props
- **类型**: `T` (组件 props 类型)
- **描述**: 解析后的最终 props，包含输入 props
- **用途**: 基于最终 props 进行元数据计算

### defaultProps
- **类型**: `T` (组件 props 类型)
- **描述**: 仅包含默认 props
- **用途**: 访问组合的默认配置

### abortSignal
- **类型**: `AbortSignal`
- **描述**: 用于中止网络请求的信号
- **用途**: 当默认 props 改变时中止过期请求

### compositionId
- **类型**: `string`
- **描述**: 当前组合的 ID
- **版本**: v4.0.98+
- **用途**: 基于组合 ID 进行条件处理

## 返回值属性

### props (可选)
- **类型**: `T`
- **描述**: 组件接收的最终 props
- **限制**: 必须是纯 JSON 可序列化对象

### durationInFrames (可选)
- **类型**: `number`
- **描述**: 组合的帧数时长

### width (可选)
- **类型**: `number`
- **描述**: 组合宽度（像素）

### height (可选)
- **类型**: `number`
- **描述**: 组合高度（像素）

### fps (可选)
- **类型**: `number`
- **描述**: 组合帧率

### defaultCodec (可选)
- **类型**: `string`
- **描述**: 默认编解码器

### defaultOutName (可选)
- **类型**: `string`
- **描述**: 默认输出文件名（不含扩展名）
- **版本**: v4.0.268+

### defaultVideoImageFormat (可选)
- **类型**: `'png' | 'jpeg' | 'none'`
- **描述**: 默认视频图像格式
- **版本**: v4.0.316+

### defaultPixelFormat (可选)
- **类型**: `'yuv420p' | 'yuva420p' | 'yuv422p' | 'yuv444p' | 'yuv420p10le' | 'yuv422p10le' | 'yuv444p10le' | 'yuva444p10le'`
- **描述**: 默认像素格式
- **版本**: v4.0.316+

## 实际应用场景

### 1. 动态视频时长计算

```tsx
import { parseMedia } from '@remotion/media-parser';

const calculateMetadata: CalculateMetadataFunction<Props> = async ({ props }) => {
  const fps = 30;
  const videos = await Promise.all(
    props.videos.map(async (video) => {
      const { slowDurationInSeconds } = await parseMedia({
        src: video.src,
        fields: { slowDurationInSeconds: true },
      });
      
      return {
        durationInFrames: Math.floor(slowDurationInSeconds * fps),
        src: video.src,
      };
    })
  );

  const totalDurationInFrames = videos.reduce(
    (acc, video) => acc + (video.durationInFrames ?? 0), 
    0
  );

  return {
    props: { ...props, videos },
    fps,
    durationInFrames: totalDurationInFrames,
  };
};
```

### 2. 数据获取和预处理

```tsx
const calculateMetadata: CalculateMetadataFunction<Props> = async ({ 
  props, 
  abortSignal 
}) => {
  try {
    const response = await fetch(`/api/data/${props.dataId}`, {
      signal: abortSignal
    });
    
    if (!response.ok) {
      throw new Error('数据获取失败');
    }
    
    const data = await response.json();
    
    return {
      props: {
        ...props,
        processedData: data,
        title: data.title,
        description: data.description
      },
      durationInFrames: data.items.length * 30, // 每项1秒
    };
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('请求被中止');
    }
    throw error;
  }
};
```

### 3. 响应式视频尺寸

```tsx
const calculateMetadata: CalculateMetadataFunction<Props> = ({ props }) => {
  const aspectRatios = {
    '16:9': { width: 1920, height: 1080 },
    '9:16': { width: 1080, height: 1920 },
    '1:1': { width: 1080, height: 1080 },
    '4:3': { width: 1440, height: 1080 },
  };
  
  const dimensions = aspectRatios[props.aspectRatio] || aspectRatios['16:9'];
  
  return {
    ...dimensions,
    props: {
      ...props,
      calculatedWidth: dimensions.width,
      calculatedHeight: dimensions.height,
    },
  };
};
```

### 4. 条件编解码器选择

```tsx
const calculateMetadata: CalculateMetadataFunction<Props> = ({ props, compositionId }) => {
  let codec = 'h264';
  let pixelFormat = 'yuv420p';
  
  if (props.quality === 'high') {
    codec = 'h265';
    pixelFormat = 'yuv444p';
  } else if (props.transparent) {
    codec = 'prores';
    pixelFormat = 'yuva444p10le';
  }
  
  return {
    defaultCodec: codec,
    defaultPixelFormat: pixelFormat,
    defaultOutName: `${compositionId}-${props.quality || 'standard'}`,
  };
};
```

### 5. 多语言内容处理

```tsx
const calculateMetadata: CalculateMetadataFunction<Props> = async ({ props }) => {
  const translations = await fetch(`/api/translations/${props.language}`)
    .then(res => res.json());
  
  const textItems = props.textItems.map(item => ({
    ...item,
    text: translations[item.key] || item.text,
  }));
  
  // 根据文本长度调整时长
  const totalTextLength = textItems.reduce((sum, item) => sum + item.text.length, 0);
  const estimatedDuration = Math.max(180, totalTextLength * 2); // 最少6秒
  
  return {
    props: {
      ...props,
      textItems,
      language: props.language,
    },
    durationInFrames: estimatedDuration,
  };
};
```

## 重要特性

### 执行时机
- 函数仅执行一次，独立于渲染并发性
- 在单独的标签页中运行，作为 `selectComposition()` 的一部分
- 在实际渲染开始前执行

### 异步支持
- 函数可以是 `async`
- 必须在超时时间内完成
- 支持 `AbortSignal` 中止机制

### 优先级
- 返回的字段优先级高于直接传递给组合的 props
- `defaultCodec` 优先级高于配置文件，低于 `renderMedia()` 的显式选项

## 错误处理

```tsx
const calculateMetadata: CalculateMetadataFunction<Props> = async ({ props, abortSignal }) => {
  try {
    const data = await fetchData(props.id, { signal: abortSignal });
    return { props: { ...props, data } };
  } catch (error) {
    if (error.name === 'AbortError') {
      // 请求被中止，正常情况
      return { props };
    }
    
    // 记录错误并返回默认值
    console.error('数据获取失败:', error);
    return {
      props: {
        ...props,
        error: '数据加载失败，使用默认内容',
        data: getDefaultData(),
      },
    };
  }
};
```

## 性能优化建议

1. **缓存结果**: 对相同输入缓存计算结果
2. **并行处理**: 使用 `Promise.all` 并行处理多个异步操作
3. **增量更新**: 只在必要时重新计算
4. **超时控制**: 设置合理的超时时间

## 常见错误

- **非序列化数据**: 返回包含函数或类实例的 props
- **忘记处理 AbortSignal**: 导致内存泄漏
- **阻塞操作**: 执行耗时过长的同步操作
- **错误的类型**: 返回与组件 props 类型不匹配的数据

## 参考资源

- **官方文档**: https://www.remotion.dev/docs/calculate-metadata
- **数据获取指南**: https://www.remotion.dev/docs/data-fetching
- **动态元数据**: https://www.remotion.dev/docs/dynamic-metadata
- **Props 解析**: https://www.remotion.dev/docs/props-resolution
