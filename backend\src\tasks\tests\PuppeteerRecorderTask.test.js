/**
 * PuppeteerRecorderTask 测试文件
 * 运行方式：node PuppeteerRecorderTask.test.js
 * 环境变量：
 *   - TEST_MODE=fast      仅运行快速测试（默认）
 *   - TEST_MODE=full      运行完整测试（包含实际录制）
 */

const PuppeteerRecorderTask = require('../PuppeteerRecorderTask');
const logger = require('../../utils/logger');
const fs = require('fs');
const path = require('path');

// 测试配置（遵循任务开发标准）
const TEST_CONFIG = {
    testMode: process.env.TEST_MODE || 'normal',
    timeout: process.env.TEST_MODE === 'quick' ? 30000 : 600000 // 30秒 vs 10分钟
};

// 测试日志前缀
const testLogPrefix = '[文件：PuppeteerRecorderTask.test.js][Puppeteer录制任务测试][测试执行]';

logger.info(`${testLogPrefix} 🧪 开始测试 PuppeteerRecorderTask`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 测试数据构建器
function buildValidContext(overrides = {}) {
    return {
        reqId: 'test_req_' + Date.now(),
        renderPageUrl: 'http://localhost:9999/api/bbc-render',
        chineseTitle: 'BBC英语精听测试',
        englishTitle: 'Breaking News Test',
        subtitle: '测试突发新闻',
        countdown: 3,
        duration: 2, // 短时间录制用于测试
        fps: 30,
        width: 1920,  // 1920x1080 生产环境标准分辨率
        height: 1080,
        outputPath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output',
        ...overrides
    };
}

function buildInvalidContext() {
    return {
        // 故意缺少必需字段reqId
        renderPageUrl: 'http://localhost:9999/api/bbc-render'
    };
}

// 测试统计
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
};

// 简单测试框架（基于logger）
function test(name, testFn, skipCondition = false) {
    testStats.total++;
    
    if (skipCondition) {
        logger.info(`${testLogPrefix} ⏭️  SKIP: ${name}`);
        testStats.skipped++;
        return;
    }
    
    logger.debug(`${testLogPrefix} ▶️  START: ${name}`);
    
    return Promise.resolve()
        .then(() => testFn())
        .then(() => {
            logger.info(`${testLogPrefix} ✅ PASS: ${name}`);
            testStats.passed++;
        })
        .catch((error) => {
            logger.error(`${testLogPrefix} ❌ FAIL: ${name}`);
            logger.error(`${testLogPrefix}    错误: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix}    堆栈: ${error.stack.split('\n')[1]?.trim()}`);
            }
            testStats.failed++;
        });
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`期望 ${expected}，实际 ${actual}`);
            }
        },
        toEqual: (expected) => {
            if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                throw new Error(`期望 ${JSON.stringify(expected)}，实际 ${JSON.stringify(actual)}`);
            }
        },
        toHaveProperty: (prop) => {
            if (!(prop in actual)) {
                throw new Error(`期望包含属性 ${prop}`);
            }
        },
        toContain: (item) => {
            if (!actual.includes(item)) {
                throw new Error(`期望包含 ${item}`);
            }
        },
        toThrow: async (fn) => {
            let thrown = false;
            try {
                await fn();
            } catch (e) {
                thrown = true;
            }
            if (!thrown) {
                throw new Error('期望抛出异常但没有');
            }
        }
    };
}

// 主要测试套件
async function runTests() {
    const startTime = Date.now();
    
    // 1. 基础功能测试（快速）
    await test('实例化服务应该成功', async () => {
        const task = new PuppeteerRecorderTask();
        expect(task.name).toBe('PuppeteerRecorderTask');
        expect(task.status).toBe('pending');
    });

    // 2. 参数校验测试（快速）
    await test('参数校验失败应该返回错误', async () => {
        const task = new PuppeteerRecorderTask();
        const invalidContext = buildInvalidContext();
        
        try {
            await task.execute(invalidContext);
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            expect(error.message).toContain('缺少必需字段');
            expect(task.status).toBe('failed');
        }
    });

    // 3. 文件标识符提取测试（快速）
    await test('文件标识符提取应该正确', async () => {
        const task = new PuppeteerRecorderTask();

        // 测试有效URL
        const context1 = { renderPageUrl: 'http://localhost:9999/api/bbc-render' };
        const identifier1 = task.extractFileIdentifier(context1);
        expect(identifier1).toBe('_api_bbc_render');

        // 测试无效URL
        const context2 = { renderPageUrl: 'invalid-url' };
        const identifier2 = task.extractFileIdentifier(context2);
        expect(identifier2).toBe('invalid_url_recording');

        // 测试缺少URL
        const context3 = {};
        const identifier3 = task.extractFileIdentifier(context3);
        expect(identifier3).toBe('unknown_recording');
    });

    // 4. 进度回调功能测试（快速）
    await test('进度回调功能应该正常', async () => {
        const task = new PuppeteerRecorderTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试基础进度报告
        task.reportProgress('running', 'processing', {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        expect(progressLogs.length > 0).toBe(true);
        expect(progressLogs[0].taskName).toBe('PuppeteerRecorderTask');
        expect(progressLogs[0].status).toBe('running');
    });

    // 5. 详细上下文收集测试（快速）
    await test('collectDetailedContext 方法应该正常', async () => {
        const task = new PuppeteerRecorderTask();
        const context = task.collectDetailedContext();

        expect(context).toHaveProperty('taskInfo');
        expect(context).toHaveProperty('executionStats');
        expect(context).toHaveProperty('progressHistory');
        expect(context).toHaveProperty('inputContext');
        expect(context).toHaveProperty('outputContext');
        expect(context).toHaveProperty('technicalDetails');
        expect(context.collectionMethod).toBe('PuppeteerRecorderTask.collectDetailedContext');
    });

    // 6. 输出目录验证测试（快速）
    await test('输出目录应该存在', async () => {
        const outputDir = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output';

        // 验证目录存在（不创建）
        expect(fs.existsSync(outputDir)).toBe(true);
    });

    // 7. 中文参数URL编码测试（快速）
    await test('中文参数URL编码应该正确', async () => {

        // 测试中文参数编码
        const chineseParams = {
            chineseTitle: '今日新闻测试',
            englishTitle: 'Today News Test',
            subtitle: '测试副标题',
            countdown: 3,
            duration: 4,
            isVideoMode: 'true'
        };

        const queryParams = new URLSearchParams(chineseParams);
        const queryString = queryParams.toString();

        // 验证中文参数被正确编码
        expect(queryString).toContain('chineseTitle=');
        expect(queryString).toContain('subtitle=');

        // 验证解码后参数正确
        const decoded = Object.fromEntries(queryParams);
        expect(decoded.chineseTitle).toBe('今日新闻测试');
        expect(decoded.subtitle).toBe('测试副标题');

        logger.info(`${testLogPrefix} 🈳 中文参数编码测试通过`);
        logger.debug(`${testLogPrefix} 编码后: ${queryString}`);
    });

    // 8. 完整录制流程测试（可选，需要渲染服务运行）
    await test('完整录制流程测试', async () => {
        const task = new PuppeteerRecorderTask();
        const validContext = buildValidContext({
            chineseTitle: '测试中文标题',
            englishTitle: 'Test English Title',
            subtitle: '测试中文副标题'
        });

        logger.warn(`${testLogPrefix} ⚠️  正在执行完整录制测试，需要渲染服务运行...`);
        const startTime = Date.now();

        const progressLogs = [];
        const result = await task.execute(validContext, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        const duration = Date.now() - startTime;
        logger.info(`${testLogPrefix} ⏱️  录制测试耗时: ${(duration / 1000).toFixed(2)}秒`);

        expect(result).toHaveProperty('recordingStatus');
        expect(result.recordingStatus).toBe('SUCCESS');
        expect(result).toHaveProperty('recordedVideoPath');
        expect(result).toHaveProperty('videoFileName');
        expect(result).toHaveProperty('recordingStats');
        expect(result).toHaveProperty('chineseTitle');
        expect(result).toHaveProperty('englishTitle');
        expect(result).toHaveProperty('subtitle');
        expect(task.status).toBe('completed');

        // 验证中文参数传递
        expect(result.chineseTitle).toBe('测试中文标题');
        expect(result.subtitle).toBe('测试中文副标题');

        // 验证视频文件是否存在
        expect(fs.existsSync(result.recordedVideoPath)).toBe(true);

        // 验证进度回调
        const hasStartedProgress = progressLogs.some(p => p.status === 'started');
        const hasCompletedProgress = progressLogs.some(p => p.status === 'completed');
        expect(hasStartedProgress).toBe(true);
        expect(hasCompletedProgress).toBe(true);

        logger.info(`${testLogPrefix} 📁 录制文件已保存: ${result.recordedVideoPath}`);
        logger.info(`${testLogPrefix} 📊 文件大小: ${result.recordingStats.fileSizeMB}MB`);
        logger.info(`${testLogPrefix} 🈳 中文参数: ${result.chineseTitle} | ${result.subtitle}`);

    }, false); // 启用此测试，渲染服务正在运行

    // 输出测试结果
    const totalTime = Date.now() - startTime;
    logger.info(`${testLogPrefix} 📊 测试结果统计:`);
    logger.info(`${testLogPrefix}    总计: ${testStats.total}`);
    logger.info(`${testLogPrefix}    通过: ${testStats.passed} ✅`);
    logger.info(`${testLogPrefix}    失败: ${testStats.failed} ❌`);
    logger.info(`${testLogPrefix}    跳过: ${testStats.skipped} ⏭️`);
    logger.info(`${testLogPrefix}    耗时: ${(totalTime / 1000).toFixed(2)}秒`);
    
    if (testStats.failed > 0) {
        logger.error(`${testLogPrefix} 💥 测试失败！有 ${testStats.failed} 个用例失败`);
        console.log(`❌ 测试失败: ${testStats.failed}/${testStats.total} 用例失败`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试通过！`);
        console.log(`✅ 测试成功: ${testStats.passed}/${testStats.total} 用例通过`);
        process.exit(0);
    }
}

// 错误处理（使用logger）
process.on('uncaughtException', (error) => {
    logger.error(`${testLogPrefix} 💥 未捕获异常: ${error.message}`);
    logger.error(`${testLogPrefix} 异常堆栈: ${error.stack}`);
    console.log(`❌ 测试异常终止: ${error.message}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error(`${testLogPrefix} 💥 未处理的Promise拒绝:`, reason);
    logger.error(`${testLogPrefix} Promise对象:`, promise);
    console.log(`❌ 测试Promise拒绝: ${reason}`);
    process.exit(1);
});

// 超时处理（使用logger）
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 运行测试（使用logger）
runTests().catch((error) => {
    logger.error(`${testLogPrefix} 💥 测试执行失败: ${error.message}`);
    logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    console.log(`❌ 测试执行失败: ${error.message}`);
    process.exit(1);
});
