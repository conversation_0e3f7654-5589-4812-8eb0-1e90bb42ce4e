# API创建开发指导原则

## 📋 概述
本文档为开发者提供详细的API创建指导，确保项目架构的一致性和可维护性。遵循"一个接口一个Controller"和"分类管理"的设计原则。

## 🎯 核心原则
1. **单一职责原则**: 每个控制器只处理一个特定功能
2. **命名一致性原则**: 接口路径与控制器名称完全对应
3. **分类管理原则**: 按功能类目组织控制器和路由
4. **标准化原则**: 统一的代码结构、日志格式和错误处理

---

## 📝 第1步：接口分类规划

### 1.1 确定功能分类
在创建API之前，首先确定接口属于哪个功能分类：

**常见分类示例：**
- `video` - 视频相关功能（上传、生成、处理等）
- `user` - 用户相关功能（登录、注册、个人资料等）
- `admin` - 管理员功能（仪表板、设置、用户管理等）
- `auth` - 认证相关功能（登录、登出、令牌刷新等）
- `file` - 文件管理功能（上传、下载、删除等）

### 1.2 接口命名规则
接口名称应该包含分类名，遵循以下格式：
```
/api/{分类名}/{具体功能名}
```

**示例：**
```
/api/video/uploadVideo     # 视频上传
/api/video/generateVideo   # 视频生成
/api/user/loginUser        # 用户登录
/api/admin/manageUsers     # 管理用户
```

### 1.3 分类与命名的对应关系
| 分类 | 接口示例 | 控制器名称 | 说明 |
|------|----------|------------|------|
| video | `/api/video/uploadVideo` | `uploadVideoController.js` | 视频上传功能 |
| user | `/api/user/registerUser` | `registerUserController.js` | 用户注册功能 |
| admin | `/api/admin/dashboardAdmin` | `dashboardAdminController.js` | 管理员仪表板 |

---

## 🏗️ 第2步：创建控制器文件结构

### 2.1 创建分类文件夹
在 [backend/src/controllers/](mdc:backend/src/controllers/) 下创建分类文件夹（如果不存在）：

```bash
# 示例：创建user分类
mkdir backend/src/controllers/user
```

**文件位置说明：**
- 分类文件夹位置：[backend/src/controllers/{分类名}/](mdc:backend/src/controllers/)
- 示例：[backend/src/controllers/user/](mdc:backend/src/controllers/user/)（待创建）

### 2.2 控制器命名规范
控制器文件命名格式：`{接口功能名}Controller.js`

**命名示例：**
```
uploadVideoController.js    # 对应 /api/video/uploadVideo
loginUserController.js      # 对应 /api/user/loginUser
dashboardAdminController.js # 对应 /api/admin/dashboardAdmin
```

**文件创建位置：**
- [backend/src/controllers/video/uploadVideoController.js](mdc:backend/src/controllers/video/uploadVideoController.js)
- [backend/src/controllers/user/loginUserController.js](mdc:backend/src/controllers/user/loginUserController.js)（待创建）
- [backend/src/controllers/admin/dashboardAdminController.js](mdc:backend/src/controllers/admin/dashboardAdminController.js)（待创建）

### 2.3 目录结构示例
```
backend/src/controllers/
├── video/
│   ├── uploadVideoController.js
│   ├── generateVideoController.js
│   └── taskStatusController.js
├── user/
│   ├── loginUserController.js
│   ├── registerUserController.js
│   └── profileUserController.js
└── admin/
    ├── dashboardAdminController.js
    ├── settingsAdminController.js
    └── usersAdminController.js
```

**实际文件路径参考：**
- [backend/src/controllers/video/](mdc:backend/src/controllers/video/) - 视频相关控制器（已存在）
- [backend/src/controllers/user/](mdc:backend/src/controllers/user/) - 用户相关控制器（待创建）
- [backend/src/controllers/admin/](mdc:backend/src/controllers/admin/) - 管理员控制器（待创建）

---

## 💻 第3步：编写控制器代码

### 3.1 控制器文件模板
```javascript
/**
 * @功能概述: [具体功能描述] - 专门处理[具体业务]请求
 * @职责范围: 
 *   - [职责1]
 *   - [职责2]
 *   - [职责3]
 * 
 * @API接口: [HTTP方法] /api/[分类]/[功能名]
 * @请求格式: [请求参数说明]
 * @响应格式: JSON
 * 
 * @架构设计: 单一职责原则 - 只处理[具体功能]相关逻辑
 * @创建时间: [日期]
 */

// === 导入依赖模块 ===
const logger = require('../../utils/logger');
const path = require('path');

// 导入SSE基础设施（如果需要）
const {
    SSE_EVENT_TYPES,
    CONTROLLER_STATUS,
    createSSEEventData,
    createControllerStatusSSE,
    SSEConnectionManager
} = require('../../constants/progress');

// 导入业务服务（根据需要）
// const SomeService = require('../../services/someService');

/**
 * @文件位置: 控制器文件应创建在 [backend/src/controllers/{分类名}/{功能名}Controller.js](mdc:backend/src/controllers/)
 * @依赖文件:
 *   - [backend/src/utils/logger.js](mdc:backend/src/utils/logger.js) - 日志工具
 *   - [backend/src/constants/progress.js](mdc:backend/src/constants/progress.js) - SSE常量
 *   - [backend/src/services/](mdc:backend/src/services/) - 业务服务（按需导入）
 */

// 模块级日志前缀
const moduleLogPrefix = '[文件：{控制器名}][{功能描述}][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用{功能}控制器，遵循单一职责原则`);

/**
 * @功能概述: [主要处理方法的描述]
 * @参数说明:
 *   - req: Express请求对象
 *   - res: Express响应对象
 * 
 * @处理流程:
 *   1. [步骤1描述]
 *   2. [步骤2描述]
 *   3. [步骤3描述]
 * 
 * @响应格式:
 *   - success: 成功时的响应结构
 *   - error: 失败时的响应结构
 */
const mainFunction = async (req, res) => {
    const logPrefix = '[文件：{控制器名}][{方法名}]';
    const { reqId } = req.params || {};

    try {
        logger.info(`${logPrefix}[ReqID:${reqId}] 开始处理请求`);

        // === 步骤1: 参数验证 ===
        // 在这里添加参数验证逻辑

        // === 步骤2: 业务处理 ===
        // 在这里添加主要业务逻辑

        // === 步骤3: 响应返回 ===
        res.json({
            status: 'success',
            message: '处理成功',
            data: {
                // 响应数据
            }
        });

        logger.info(`${logPrefix}[ReqID:${reqId}] 请求处理完成`);

    } catch (error) {
        logger.error(`${logPrefix}[ReqID:${reqId}] 处理失败: ${error.message}`);
        
        res.status(500).json({
            status: 'error',
            message: '处理失败',
            error: error.message
        });
    }
};

// 导出控制器方法
module.exports = {
    mainFunction
};
```

### 3.2 日志引入标准
```javascript
// 1. 导入日志工具
const logger = require('../../utils/logger');

// 2. 定义模块级日志前缀
const moduleLogPrefix = '[文件：{控制器名}][{功能描述}][模块初始化]';

// 3. 模块加载日志
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用{功能}控制器，遵循单一职责原则`);

// 4. 方法级日志前缀
const logPrefix = '[文件：{控制器名}][{方法名}]';

// 5. 日志使用示例
logger.info(`${logPrefix}[ReqID:${reqId}] 开始处理请求`);
logger.error(`${logPrefix}[ReqID:${reqId}] 处理失败: ${error.message}`);
```

### 3.3 SSE规则定义
当需要实时通信时，使用SSE（Server-Sent Events）：

```javascript
// 1. 导入SSE基础设施
const {
    SSE_EVENT_TYPES,
    CONTROLLER_STATUS,
    PIPELINE_STATUS,
    createSSEEventData,
    createControllerStatusSSE,
    createHeartbeatSSE,
    SSEConnectionManager
} = require('../../constants/progress');

// 2. 创建SSE连接管理器
const sseManager = new SSEConnectionManager();

// 3. 建立SSE连接
const sseConnection = sseManager.createConnection(req, res, reqId);

// 4. 发送状态事件
sseConnection.sendEvent(createControllerStatusSSE(
    CONTROLLER_STATUS.PROCESSING,
    '正在处理请求...'
));

// 5. 发送心跳
const heartbeatInterval = setInterval(() => {
    sseConnection.sendEvent(createHeartbeatSSE());
}, 30000);

// 6. 清理连接
sseConnection.onClose(() => {
    clearInterval(heartbeatInterval);
    logger.info(`${logPrefix}[ReqID:${reqId}] SSE连接已关闭`);
});
```

---

## 🛣️ 第4步：配置路由映射

### 4.1 路由文件分类原则
路由文件应该与控制器分类保持一致，遵循以下原则：

**路由文件命名规范：**
- 格式：`{分类名}Routes.js`
- 位置：[backend/src/routes/](mdc:backend/src/routes/)
- 示例：
  - [videoRoutes.js](mdc:backend/src/routes/videoRoutes.js) - 处理所有video相关路由（已存在）
  - [userRoutes.js](mdc:backend/src/routes/userRoutes.js) - 处理所有user相关路由（待创建）
  - [adminRoutes.js](mdc:backend/src/routes/adminRoutes.js) - 处理所有admin相关路由（待创建）

**路由文件与分类对应关系：**
| 分类 | 路由文件 | 挂载路径 | 说明 |
|------|----------|----------|------|
| video | `videoRoutes.js` | `/api/video` | 视频相关功能路由 |
| user | `userRoutes.js` | `/api/user` | 用户相关功能路由 |
| admin | `adminRoutes.js` | `/api/admin` | 管理员功能路由 |
| auth | `authRoutes.js` | `/api/auth` | 认证相关功能路由 |
| file | `fileRoutes.js` | `/api/file` | 文件管理功能路由 |

### 4.2 路由文件目录结构
```
backend/src/routes/
├── videoRoutes.js    # 视频相关路由 (/api/video/*)
├── userRoutes.js     # 用户相关路由 (/api/user/*)
├── adminRoutes.js    # 管理员路由 (/api/admin/*)
├── authRoutes.js     # 认证相关路由 (/api/auth/*)
└── fileRoutes.js     # 文件管理路由 (/api/file/*)
```

**实际文件路径：**
- [backend/src/routes/videoRoutes.js](mdc:backend/src/routes/videoRoutes.js) - 已存在
- [backend/src/routes/userRoutes.js](mdc:backend/src/routes/userRoutes.js) - 待创建
- [backend/src/routes/adminRoutes.js](mdc:backend/src/routes/adminRoutes.js) - 待创建

### 4.3 创建分类路由文件
当创建新的功能分类时，需要在 [backend/src/routes/](mdc:backend/src/routes/) 目录下创建对应的路由文件：

```bash
# 示例：创建用户相关路由文件
touch backend/src/routes/userRoutes.js
```

### 4.4 路由文件模板
```javascript
/**
 * @功能概述: {分类名}相关路由配置
 * @路径前缀: /api/{分类名}
 * @创建时间: {日期}
 * @架构说明: 按功能分类组织路由，与控制器分类保持一致
 */

const express = require('express');
const router = express.Router();

// === 导入分类控制器 ===
const {功能1}Controller = require('../controllers/{分类名}/{功能1}Controller');
const {功能2}Controller = require('../controllers/{分类名}/{功能2}Controller');

// === 路由定义 ===

/**
 * @功能概述: {功能1描述}
 * @route {HTTP方法} /api/{分类名}/{功能1名}
 * @controller {功能1}Controller - 专用{功能1}控制器
 * @命名一致性: 路径 /{功能1名} 对应控制器 {功能1}Controller.js
 */
router.{http方法}('/{功能1名}',
    {功能1}Controller.{功能1方法}
);

/**
 * @功能概述: {功能2描述}
 * @route {HTTP方法} /api/{分类名}/{功能2名}
 * @controller {功能2}Controller - 专用{功能2}控制器
 * @命名一致性: 路径 /{功能2名} 对应控制器 {功能2}Controller.js
 */
router.{http方法}('/{功能2名}',
    {功能2}Controller.{功能2方法}
);

module.exports = router;
```

### 4.5 实际示例：用户路由文件
```javascript
/**
 * @功能概述: 用户相关路由配置
 * @路径前缀: /api/user
 * @创建时间: 2025-06-12
 * @架构说明: 按功能分类组织路由，与控制器分类保持一致
 */

const express = require('express');
const router = express.Router();

// === 导入用户分类控制器 ===
const loginUserController = require('../controllers/user/loginUserController');
const registerUserController = require('../controllers/user/registerUserController');
const profileUserController = require('../controllers/user/profileUserController');

// === 用户路由定义 ===

/**
 * @功能概述: 用户登录接口
 * @route POST /api/user/loginUser
 * @controller loginUserController - 专用登录控制器
 * @命名一致性: 路径 /loginUser 对应控制器 loginUserController.js
 */
router.post('/loginUser',
    loginUserController.loginUser
);

/**
 * @功能概述: 用户注册接口
 * @route POST /api/user/registerUser
 * @controller registerUserController - 专用注册控制器
 * @命名一致性: 路径 /registerUser 对应控制器 registerUserController.js
 */
router.post('/registerUser',
    registerUserController.registerUser
);

/**
 * @功能概述: 获取用户资料接口
 * @route GET /api/user/profileUser
 * @controller profileUserController - 专用资料控制器
 * @命名一致性: 路径 /profileUser 对应控制器 profileUserController.js
 */
router.get('/profileUser',
    profileUserController.profileUser
);

module.exports = router;
```

### 4.6 在主应用中挂载分类路由
在 [backend/src/app.js](mdc:backend/src/app.js) 中挂载所有分类路由：

```javascript
// === 导入分类路由 ===
const videoRoutes = require('./routes/videoRoutes');
const userRoutes = require('./routes/userRoutes');
const adminRoutes = require('./routes/adminRoutes');
const authRoutes = require('./routes/authRoutes');
const fileRoutes = require('./routes/fileRoutes');

// === 挂载分类路由 ===
app.use('/api/video', videoRoutes);   // 视频相关路由
app.use('/api/user', userRoutes);     // 用户相关路由
app.use('/api/admin', adminRoutes);   // 管理员路由
app.use('/api/auth', authRoutes);     // 认证相关路由
app.use('/api/file', fileRoutes);     // 文件管理路由

// 路由挂载日志
logger.info('[Express应用][路由挂载] 所有分类路由已成功挂载');
```

**文件修改位置：**
- 主应用文件：[backend/src/app.js](mdc:backend/src/app.js)
- 路由导入：从 [backend/src/routes/](mdc:backend/src/routes/) 目录导入各分类路由文件

---

## 🔮 未来扩展

### 建议的完整项目结构

#### 控制器目录结构
```
controllers/
├── video/
│   ├── uploadVideoController.js
│   ├── generateVideoController.js
│   └── taskStatusController.js
├── user/
│   ├── loginUserController.js
│   ├── registerUserController.js
│   └── profileUserController.js
├── admin/
│   ├── dashboardAdminController.js
│   ├── settingsAdminController.js
│   └── usersAdminController.js
├── auth/
│   ├── tokenAuthController.js
│   ├── refreshAuthController.js
│   └── logoutAuthController.js
└── file/
    ├── uploadFileController.js
    ├── downloadFileController.js
    └── deleteFileController.js
```

#### 路由目录结构
```
routes/
├── videoRoutes.js      # 视频相关路由
├── userRoutes.js       # 用户相关路由
├── adminRoutes.js      # 管理员路由
├── authRoutes.js       # 认证相关路由
└── fileRoutes.js       # 文件管理路由
```

#### 分类一致性架构
| 分类 | 控制器目录 | 路由文件 | API前缀 | 说明 |
|------|------------|----------|---------|------|
| video | `controllers/video/` | `videoRoutes.js` | `/api/video` | 视频处理功能 |
| user | `controllers/user/` | `userRoutes.js` | `/api/user` | 用户管理功能 |
| admin | `controllers/admin/` | `adminRoutes.js` | `/api/admin` | 管理员功能 |
| auth | `controllers/auth/` | `authRoutes.js` | `/api/auth` | 认证授权功能 |
| file | `controllers/file/` | `fileRoutes.js` | `/api/file` | 文件管理功能 |

### 对应的完整API结构
```
# 视频相关 (videoRoutes.js)
POST /api/video/uploadVideo
POST /api/video/generateVideo
GET  /api/video/taskStatus/:reqId

# 用户相关 (userRoutes.js)
POST /api/user/loginUser
POST /api/user/registerUser
GET  /api/user/profileUser
PUT  /api/user/updateUser

# 管理员相关 (adminRoutes.js)
GET  /api/admin/dashboardAdmin
POST /api/admin/settingsAdmin
GET  /api/admin/usersAdmin
PUT  /api/admin/updateAdmin

# 认证相关 (authRoutes.js)
POST /api/auth/tokenAuth
POST /api/auth/refreshAuth
POST /api/auth/logoutAuth

# 文件相关 (fileRoutes.js)
POST /api/file/uploadFile
GET  /api/file/downloadFile/:id
DELETE /api/file/deleteFile/:id
```

### 扩展新分类的完整流程
当需要添加新的功能分类时，按以下步骤操作：

1. **创建控制器目录**：
   ```bash
   mkdir backend/src/controllers/{新分类}/
   ```
   位置：[backend/src/controllers/{新分类}/](mdc:backend/src/controllers/)

2. **创建路由文件**：
   ```bash
   touch backend/src/routes/{新分类}Routes.js
   ```
   位置：[backend/src/routes/{新分类}Routes.js](mdc:backend/src/routes/)

3. **在app.js中挂载**：
   在 [backend/src/app.js](mdc:backend/src/app.js) 中添加：
   ```javascript
   app.use('/api/{新分类}', {新分类}Routes);
   ```

4. **按需创建控制器**：
   在 [backend/src/controllers/{新分类}/](mdc:backend/src/controllers/) 目录下创建具体功能控制器

---

## ✅ 开发检查清单

### 创建新API时请确认：

#### 架构规划
- [ ] 已确定功能分类（video、user、admin等）
- [ ] 接口命名符合规范（/api/{分类}/{功能}）
- [ ] 确认是否需要创建新的分类目录

#### 控制器创建
- [ ] 控制器文件放在正确的分类文件夹下（[backend/src/controllers/{分类}/](mdc:backend/src/controllers/)）
- [ ] 控制器名称与接口路径对应（{功能}Controller.js）
- [ ] 已添加完整的注释和日志
- [ ] 遵循控制器模板结构
- [ ] 正确导入依赖文件（[logger.js](mdc:backend/src/utils/logger.js)、[progress.js](mdc:backend/src/constants/progress.js)等）

#### 路由配置
- [ ] 已创建或更新对应的分类路由文件（[backend/src/routes/{分类}Routes.js](mdc:backend/src/routes/)）
- [ ] 路由文件放在正确位置（[backend/src/routes/](mdc:backend/src/routes/)）
- [ ] 路由映射配置正确
- [ ] 已在主应用中挂载分类路由（[backend/src/app.js](mdc:backend/src/app.js)）
- [ ] 路由注释完整，包含命名一致性说明

#### 测试验证
- [ ] 已进行功能测试
- [ ] 验证路由映射正确
- [ ] 验证控制器方法调用正常
- [ ] 已更新相关文档

### 代码质量标准：

#### 架构标准
- [ ] 遵循单一职责原则
- [ ] 遵循分类一致性原则
- [ ] 命名规范统一

#### 代码标准
- [ ] 包含详细的错误处理
- [ ] 使用标准化的日志格式
- [ ] 如需要，正确使用SSE
- [ ] 响应格式统一
- [ ] 代码注释完整

#### 文件组织标准
- [ ] 控制器按分类组织
- [ ] 路由按分类组织
- [ ] 文件命名符合规范

---

## 📚 参考示例

### 完整的控制器示例
参考现有的 [backend/src/controllers/video/uploadVideoController.js](mdc:backend/src/controllers/video/uploadVideoController.js) 文件，了解完整的实现模式。

### 路由配置示例
参考现有的 [backend/src/routes/videoRoutes.js](mdc:backend/src/routes/videoRoutes.js) 文件，了解路由配置的标准格式。

---

## 🎯 实际开发示例

### 示例：创建用户登录API

#### 步骤1：确定分类
- 功能：用户登录
- 分类：`user`
- 接口路径：`/api/user/loginUser`

#### 步骤2：创建控制器
```bash
# 创建user分类文件夹（如果不存在）
mkdir backend/src/controllers/user

# 创建控制器文件
touch backend/src/controllers/user/loginUserController.js
```

**文件创建位置：**
- 分类目录：[backend/src/controllers/user/](mdc:backend/src/controllers/user/)（待创建）
- 控制器文件：[backend/src/controllers/user/loginUserController.js](mdc:backend/src/controllers/user/loginUserController.js)（待创建）

#### 步骤3：编写控制器代码
```javascript
/**
 * @功能概述: 用户登录控制器 - 专门处理用户登录认证请求
 * @职责范围:
 *   - 验证用户凭据
 *   - 生成访问令牌
 *   - 记录登录日志
 *
 * @API接口: POST /api/user/loginUser
 * @请求格式: { username: string, password: string }
 * @响应格式: JSON
 */

const logger = require('../../utils/logger');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const moduleLogPrefix = '[文件：loginUserController.js][用户登录控制器][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);

const loginUser = async (req, res) => {
    const logPrefix = '[文件：loginUserController.js][loginUser]';
    const { username, password } = req.body;

    try {
        logger.info(`${logPrefix} 用户登录请求: ${username}`);

        // === 步骤1: 参数验证 ===
        if (!username || !password) {
            return res.status(400).json({
                status: 'error',
                message: '用户名和密码不能为空'
            });
        }

        // === 步骤2: 用户验证 ===
        // 这里添加用户验证逻辑

        // === 步骤3: 生成令牌 ===
        const token = jwt.sign({ username }, process.env.JWT_SECRET);

        res.json({
            status: 'success',
            message: '登录成功',
            data: { token, username }
        });

        logger.info(`${logPrefix} 用户登录成功: ${username}`);

    } catch (error) {
        logger.error(`${logPrefix} 登录失败: ${error.message}`);
        res.status(500).json({
            status: 'error',
            message: '登录失败'
        });
    }
};

module.exports = { loginUser };
```

#### 步骤4：配置路由
```javascript
// 在 backend/src/routes/userRoutes.js 中
const loginUserController = require('../controllers/user/loginUserController');

router.post('/loginUser', loginUserController.loginUser);
```

**文件修改位置：**
- 路由文件：[backend/src/routes/userRoutes.js](mdc:backend/src/routes/userRoutes.js)（待创建）
- 主应用文件：[backend/src/app.js](mdc:backend/src/app.js)（需要挂载新路由）

---

## 📋 常见问题与解决方案

### Q1: 如何处理文件上传？
```javascript
// 使用multer中间件
const uploadMiddleware = require('../../middleware/uploadMiddleware');

router.post('/uploadFile',
    uploadMiddleware, // 文件上传中间件
    uploadFileController.uploadFile
);
```

### Q2: 如何实现参数验证？
```javascript
// 在控制器中添加验证逻辑
const validateParams = (req) => {
    const { param1, param2 } = req.body;
    const errors = [];

    if (!param1) errors.push('param1不能为空');
    if (!param2) errors.push('param2不能为空');

    return errors;
};

// 在主方法中使用
const errors = validateParams(req);
if (errors.length > 0) {
    return res.status(400).json({
        status: 'error',
        message: '参数验证失败',
        errors
    });
}
```

### Q3: 如何处理异步操作？
```javascript
// 使用async/await
const processData = async (req, res) => {
    try {
        const result = await someAsyncOperation();
        res.json({ status: 'success', data: result });
    } catch (error) {
        logger.error(`处理失败: ${error.message}`);
        res.status(500).json({ status: 'error', message: '处理失败' });
    }
};
```

---

## 🔧 开发工具与命令

### 快速创建控制器脚本
```bash
#!/bin/bash
# create-controller.sh
# 用法: ./create-controller.sh user loginUser

CATEGORY=$1
FUNCTION=$2

# 创建分类文件夹
mkdir -p backend/src/controllers/$CATEGORY

# 创建控制器文件
cat > backend/src/controllers/$CATEGORY/${FUNCTION}Controller.js << EOF
/**
 * @功能概述: ${FUNCTION}控制器 - 专门处理${FUNCTION}请求
 */

const logger = require('../../utils/logger');

const moduleLogPrefix = '[文件：${FUNCTION}Controller.js][${FUNCTION}控制器][模块初始化]';
logger.info(\`\${moduleLogPrefix}模块已加载。\`);

const ${FUNCTION} = async (req, res) => {
    const logPrefix = '[文件：${FUNCTION}Controller.js][${FUNCTION}]';

    try {
        logger.info(\`\${logPrefix} 开始处理请求\`);

        // TODO: 添加业务逻辑

        res.json({
            status: 'success',
            message: '处理成功'
        });

    } catch (error) {
        logger.error(\`\${logPrefix} 处理失败: \${error.message}\`);
        res.status(500).json({
            status: 'error',
            message: '处理失败'
        });
    }
};

module.exports = { ${FUNCTION} };
EOF

echo "控制器已创建: backend/src/controllers/$CATEGORY/${FUNCTION}Controller.js"
```

---

**遵循本指导原则，确保项目架构的一致性和可维护性！**
