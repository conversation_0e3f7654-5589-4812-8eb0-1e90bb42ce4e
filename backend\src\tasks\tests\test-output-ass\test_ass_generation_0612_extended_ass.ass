[Script Info]
Title: Generated ASS Subtitle
PlayResX: 1080
PlayResY: 1920
WrapStyle: 0

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: VideoTitle,Arial,100,&H00FFFFFF,&H00FF0000,&H33000000,&H33000000,-1,0,0,0,100,100,0,0,1,2,2,5,10,10,10,1
Style: ClozedText,Arial,50,&H00FFFFFF,&H00FF0000,&H00000000,&H00FFFFFF,-1,0,0,0,100,100,2,0,1,0,0,5,100,100,140,1
Style: BilingualEnglish,Arial,50,&H00FFFFFF,&H00FF0000,&H00000000,&H00FFFFFF,-1,0,0,0,100,100,0,0,1,0,0,5,150,150,10,1
Style: BilingualChinese,Arial,50,&H00FFFFFF,&H00FF0000,&H00000000,&H00FFFFFF,-1,0,0,0,100,100,2,0,1,0,0,5,150,150,10,1
Style: RepeatModeGuide,Arial,80,&H0000FFFF,&H00FF0000,&H00000000,&H00FFFFFF,-1,0,0,0,100,100,0,0,1,0,0,5,60,60,100,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:03:00.00,VideoTitle,,0,0,0,,{\pos(540,300)}坚持30天
Dialogue: 0,0:00:00.00,0:03:00.00,VideoTitle,,0,0,0,,{\pos(540,420)}听懂国外新闻
Dialogue: 1,0:01:00.00,0:01:03.50,ClozedText,,0,0,0,,{\pos(540,928)}Residents say they were woken up by a () scene.
Dialogue: 1,0:01:03.50,0:01:07.00,ClozedText,,0,0,0,,{\pos(540,928)}A possible () driver wreaking havoc, damaging multiple vehicles.
Dialogue: 1,0:01:07.00,0:01:11.00,ClozedText,,0,0,0,,{\pos(540,928)}The () unit has been called in to investigate after a pedestrian was struck.
Dialogue: 1,0:01:11.00,0:01:15.00,ClozedText,,0,0,0,,{\pos(540,928)}Police are asking anyone with () to come forward.
Dialogue: 2,0:02:00.00,0:02:03.50,BilingualEnglish,,0,0,0,,{\pos(540,800)}{\1c&H0000FFFF}Residents{\fs30} [居民]{\r} say they were woken up by a {\1c&H0000FFFF}movie-like{\fs30} [电影般的]{\r} scene.
Dialogue: 2,0:02:00.00,0:02:03.50,BilingualChinese,,0,0,0,,{\pos(540,1040)}居民们说，\N他们被一场电影般的场景惊醒。
Dialogue: 2,0:02:03.50,0:02:07.00,BilingualEnglish,,0,0,0,,{\pos(540,800)}A possible {\1c&H0000FFFF}impaired{\fs30} [受损的]{\r} driver {\1c&H0000FFFF}wreaking{\fs30} [造成]{\r} {\1c&H0000FFFF}havoc{\fs30} [混乱]{\r}, damaging multiple vehicles.
Dialogue: 2,0:02:03.50,0:02:07.00,BilingualChinese,,0,0,0,,{\pos(540,1040)}一个可能的酒驾司机造成了混乱，\N损坏了多辆车。
Dialogue: 2,0:02:07.00,0:02:11.00,BilingualEnglish,,0,0,0,,{\pos(540,800)}The {\1c&H0000FFFF}homicide{\fs30} [凶杀]{\r} unit has been called in to {\1c&H0000FFFF}investigate{\fs30} [调查]{\r} after a {\1c&H0000FFFF}pedestrian{\fs30} [行人]{\r} was struck.
Dialogue: 2,0:02:07.00,0:02:11.00,BilingualChinese,,0,0,0,,{\pos(540,1100)}在一名行人被撞后，\N凶杀案调查组被召集进行调查。
Dialogue: 2,0:02:11.00,0:02:15.00,BilingualEnglish,,0,0,0,,{\pos(540,800)}Police are asking anyone with {\1c&H0000FFFF}information{\fs30} [信息]{\r} to {\1c&H0000FFFF}come forward{\fs30} [站出来]{\r}.
Dialogue: 2,0:02:11.00,0:02:15.00,BilingualChinese,,0,0,0,,{\pos(540,1040)}警方要求任何有信息的人站出来。
Dialogue: 3,0:00:00.00,0:00:00.10,RepeatModeGuide,,0,0,0,,{\move(540,1500,540,1450)\fad(100,0)\t(0,100,\alpha&H00&)}第一遍 盲听
Dialogue: 3,0:00:00.10,0:00:59.90,RepeatModeGuide,,0,0,0,,{\pos(540,1450)}第一遍 盲听
Dialogue: 3,0:00:59.90,0:01:00.00,RepeatModeGuide,,0,0,0,,{\move(540,1450,540,1400)\fad(0,100)\t(0,100,\alpha&HFF&)}第一遍 盲听
Dialogue: 3,0:01:00.00,0:01:00.10,RepeatModeGuide,,0,0,0,,{\move(540,1500,540,1450)\fad(100,0)\t(0,100,\alpha&H00&)}第二遍 单词填空
Dialogue: 3,0:01:00.10,0:01:59.90,RepeatModeGuide,,0,0,0,,{\pos(540,1450)}第二遍 单词填空
Dialogue: 3,0:01:59.90,0:02:00.00,RepeatModeGuide,,0,0,0,,{\move(540,1450,540,1400)\fad(0,100)\t(0,100,\alpha&HFF&)}第二遍 单词填空
Dialogue: 3,0:02:00.00,0:02:00.10,RepeatModeGuide,,0,0,0,,{\move(540,1500,540,1450)\fad(100,0)\t(0,100,\alpha&H00&)}第三遍 中英翻译
Dialogue: 3,0:02:00.10,0:02:59.90,RepeatModeGuide,,0,0,0,,{\pos(540,1450)}第三遍 中英翻译
Dialogue: 3,0:02:59.90,0:03:00.00,RepeatModeGuide,,0,0,0,,{\move(540,1450,540,1400)\fad(0,100)\t(0,100,\alpha&HFF&)}第三遍 中英翻译
