/**
 * @功能概述: 媒体剪辑任务，实现视频时间片段剪辑、自动合并和画面裁剪的优化处理流程。
 *           采用"先筛选再处理"的策略：先从原视频中提取指定时间片段，合并后再进行画面裁剪，
 *           大幅提升长视频处理效率，避免对整个原视频进行画面处理的性能问题。
 * @输入依赖: context需包含originalVideoPath、cropData、clipSegments、savePath字段
 * @输出结果: 向context添加processedVideoPath、processedVideoFileName、originalVideoPath、originalVideoFileName字段
 * @外部依赖: FFmpeg命令行工具，用于视频片段提取、合并和画面裁剪操作
 * @失败策略: 任何步骤失败时立即停止并抛出详细错误信息，支持临时文件清理
 * @执行流程:
 *   1. 参数校验：验证输入文件、裁剪参数和片段数组的有效性
 *   2. 片段剪辑：直接从原视频中提取指定时间片段（跳过完整裁剪）
 *   3. 自动合并：多个片段时使用filter_complex进行合并
 *   4. 画面裁剪：对合并后的短视频进行画面裁剪处理（效率最高）
 *   5. 结果处理：更新context并返回处理结果
 */

// 标准模块导入
const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const config = require('../config');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const { getVideoDimensions } = require('../utils/videoUtils'); // 导入新工具

// 模块级日志前缀
const taskModuleLogPrefix = '[文件：ClipMediaTask.js][媒体剪辑任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`); // 日志：确认 ClipMediaTask 模块已成功加载到应用中。

// FFmpeg路径配置 (参考 ConvertToAudioTask)
try {
    if (config.ffmpegPath) {
        ffmpeg.setFfmpegPath(config.ffmpegPath);
        logger.info(`${taskModuleLogPrefix} FFmpeg 路径已成功设置为: ${config.ffmpegPath}`); // 日志：记录 FFmpeg 可执行文件路径的配置结果。
    }
    if (config.ffprobePath) {
        ffmpeg.setFfprobePath(config.ffprobePath);
        logger.info(`${taskModuleLogPrefix} FFprobe 路径已成功设置为: ${config.ffprobePath}`); // 日志：记录 FFprobe 可执行文件路径的配置结果。
    }
} catch (e) {
    logger.error(`${taskModuleLogPrefix} 设置 FFmpeg/FFprobe 路径失败: ${e.message}`); // 日志：记录 FFmpeg/FFprobe 路径设置过程中发生的错误。
}

class ClipMediaTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建媒体剪辑任务实例并初始化日志前缀。
     * @param {string} [name='ClipMediaTask'] - 任务名称，用于日志标识和进度追踪
     */
    constructor(name = 'ClipMediaTask') {
        super(name); // 调用 TaskBase 构造函数
        this.instanceLogPrefix = `[文件：ClipMediaTask.js][媒体剪辑任务][${this.name}]`;
        logger.info(`${this.instanceLogPrefix} ClipMediaTask 实例已创建。`); // 日志：记录任务实例成功创建的事件。
    }

    /**
     * @功能概述: 从上下文对象中提取文件标识符，用于日志记录和任务追踪。
     * @param {object} context - 上下文对象，期望包含originalVideoPath字段
     * @returns {string} 文件标识符（不含扩展名），如果无法提取则返回'unknown_clip_file'
     */
    extractFileIdentifier(context) {
        if (context.originalVideoPath) {
            // 从完整路径中提取文件名（不含扩展名）
            const fullPath = context.originalVideoPath;
            const fileName = path.basename(fullPath); // 获取文件名部分
            const nameWithoutExt = path.parse(fileName).name; // 去掉扩展名
            return nameWithoutExt;
        }
        return 'unknown_clip_file';
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段，确保任务执行的前置条件满足。
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出详细错误信息
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (!context[field] && typeof context[field] !== 'number') { // 允许 startTime/endTime 为 0
                const errorMsg = `执行失败：上下文缺少必需字段 ${field} 或字段值无效。`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`); // 日志：记录因缺少必需字段或字段值无效导致的执行失败。
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。`); // 日志：确认所有必需的输入参数均已通过验证。
    }

    /**
     * @功能概述: 执行媒体剪辑任务的核心逻辑，实现优化的视频处理流程。
     *           采用"先筛选再处理"策略：先片段剪辑再画面裁剪，大幅提升长视频处理效率。
     * @param {object} context - 上下文对象，包含任务执行所需的所有输入数据
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度
     * @returns {Promise<object>} 包含处理结果的对象，包括processedVideoPath等关键信息
     * @throws {Error} 如果在执行过程中发生不可恢复的错误
     * @执行流程:
     *   1. 参数校验：验证输入文件、裁剪参数和片段数组的有效性
     *   2. 片段剪辑：直接从原视频中提取指定时间片段
     *   3. 自动合并：多个片段时使用filter_complex进行合并
     *   4. 画面裁剪：对合并后的短视频进行画面裁剪处理
     *   5. 结果处理：更新context并返回处理结果
     */
    async execute(context, progressCallback) {
        const reqId = context.reqId || 'unknown_clip_req';
        const fileIdentifier = this.extractFileIdentifier(context);
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${fileIdentifier}]`;

        this.setProgressCallback(progressCallback);
        this.start(); // 报告任务开始

        try {
            logger.info(`${execLogPrefix} 开始执行媒体剪辑任务（优化流程：片段剪辑→合并→画面裁剪）。`);

            // === 步骤1: 参数校验 ===
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.VALIDATING_INPUTS, {
                detail: '验证输入参数',
                current: 5,
                total: 100
            });

            const requiredFields = ['originalVideoPath', 'cropData', 'clipSegments', 'savePath'];
            this.validateRequiredFields(context, requiredFields, execLogPrefix);

            const { originalVideoPath, cropData, clipSegments, savePath } = context;

            // 验证cropData参数
            if (!cropData.cropWidth || !cropData.cropHeight ||
                typeof cropData.cropXOffset !== 'number' || typeof cropData.cropYOffset !== 'number') {
                throw new Error('cropData参数不完整，需要包含cropWidth, cropHeight, cropXOffset, cropYOffset');
            }

            // 验证clipSegments参数
            if (!Array.isArray(clipSegments) || clipSegments.length === 0) {
                throw new Error('clipSegments必须是非空数组');
            }

            for (let i = 0; i < clipSegments.length; i++) {
                const segment = clipSegments[i];
                if (typeof segment.startTime !== 'number' || typeof segment.endTime !== 'number') {
                    throw new Error(`clipSegments[${i}]的startTime和endTime必须是数字`);
                }
                if (segment.startTime >= segment.endTime) {
                    throw new Error(`clipSegments[${i}]的startTime必须小于endTime`);
                }
            }

            logger.info(`${execLogPrefix} 参数校验通过。原视频: ${originalVideoPath}`);
            logger.info(`${execLogPrefix} 裁剪参数: ${cropData.cropWidth}x${cropData.cropHeight} at (${cropData.cropXOffset},${cropData.cropYOffset})`);
            logger.info(`${execLogPrefix} 片段数量: ${clipSegments.length}`);

            // 确保保存目录存在
            if (!fs.existsSync(savePath)) {
                fs.mkdirSync(savePath, { recursive: true });
                logger.info(`${execLogPrefix} 创建保存目录: ${savePath}`);
            }

            // === 步骤2: 直接从原视频中进行时间片段剪辑 ===
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '第一步：从原视频中提取时间片段',
                current: 10,
                total: 100
            });

            const originalVideoBaseName = path.parse(originalVideoPath).name;
            const originalVideoExt = path.extname(originalVideoPath);
            const rawSegmentPaths = [];

            for (let i = 0; i < clipSegments.length; i++) {
                const segment = clipSegments[i];
                const segmentFileName = `${originalVideoBaseName}_raw_segment_${i + 1}_${segment.startTime}s_${segment.endTime}s${originalVideoExt}`;
                const segmentPath = path.join(savePath, segmentFileName);

                logger.info(`${execLogPrefix} 处理片段 ${i + 1}/${clipSegments.length}: ${segment.startTime}s - ${segment.endTime}s`);

                await new Promise((resolve, reject) => {
                    const ffmpegCommand = ffmpeg(originalVideoPath);
                    if (config.ffmpegPath) ffmpegCommand.setFfmpegPath(config.ffmpegPath);
                    if (config.ffprobePath) ffmpegCommand.setFfprobePath(config.ffprobePath);

                    const duration = segment.endTime - segment.startTime;

                    ffmpegCommand
                        .setStartTime(segment.startTime)
                        .setDuration(duration)
                        .videoCodec('libx264')
                        .audioCodec('aac')
                        .outputOptions('-y')
                        .on('start', (commandLine) => {
                            logger.info(`${execLogPrefix}[FFMPEG_CLIP] 开始从原视频剪辑片段 ${i + 1}: ${commandLine}`);
                            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_PROCESSING, {
                                detail: `FFmpeg正在从原视频剪辑片段 ${i + 1}/${clipSegments.length}`,
                                current: 15 + (i * 35 / clipSegments.length),
                                total: 100
                            });
                        })
                        .on('error', (err, stdout, stderr) => {
                            const errorMsg = `FFmpeg片段剪辑错误 (片段 ${i + 1}): ${err.message}`;
                            logger.error(`${execLogPrefix}[FFMPEG_ERROR] ${errorMsg}`);
                            if (stderr) logger.error(`${execLogPrefix} FFmpeg stderr: ${stderr}`);
                            reject(new Error(errorMsg));
                        })
                        .on('end', () => {
                            logger.info(`${execLogPrefix}[FFMPEG_SUCCESS] 片段 ${i + 1} 剪辑成功: ${segmentPath}`);
                            rawSegmentPaths.push(segmentPath);
                            resolve();
                        })
                        .save(segmentPath);
                });
            }

            logger.info(`${execLogPrefix} 第一步完成：所有片段从原视频剪辑成功，共 ${rawSegmentPaths.length} 个片段`);

            // === 步骤3: 如果有多个片段，进行合并 ===
            let mergedRawVideoPath;
            let mergedRawVideoFileName;

            if (rawSegmentPaths.length === 1) {
                // 只有一个片段，直接使用
                mergedRawVideoPath = rawSegmentPaths[0];
                mergedRawVideoFileName = path.basename(mergedRawVideoPath);
                logger.info(`${execLogPrefix} 只有一个片段，无需合并，直接进入裁剪步骤`);
            } else {
                // 多个片段，需要合并
                this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                    detail: '第二步：合并多个视频片段',
                    current: 55,
                    total: 100
                });

                mergedRawVideoFileName = `${originalVideoBaseName}_merged_raw_${clipSegments.length}segments${originalVideoExt}`;
                mergedRawVideoPath = path.join(savePath, mergedRawVideoFileName);

                logger.info(`${execLogPrefix} 开始合并 ${rawSegmentPaths.length} 个原始片段`);

                await new Promise((resolve, reject) => {
                    const ffmpegCommand = ffmpeg();
                    if (config.ffmpegPath) ffmpegCommand.setFfmpegPath(config.ffmpegPath);
                    if (config.ffprobePath) ffmpegCommand.setFfprobePath(config.ffprobePath);

                    // 使用filter_complex方式合并视频，更兼容
                    rawSegmentPaths.forEach((segmentPath, index) => {
                        ffmpegCommand.input(segmentPath);
                    });

                    // 构建filter_complex参数
                    const filterInputs = rawSegmentPaths.map((_, index) => `[${index}:v][${index}:a]`).join('');
                    const filterComplex = `${filterInputs}concat=n=${rawSegmentPaths.length}:v=1:a=1[outv][outa]`;

                    ffmpegCommand
                        .complexFilter(filterComplex)
                        .outputOptions('-map', '[outv]', '-map', '[outa]')
                        .videoCodec('libx264')
                        .audioCodec('aac')
                        .outputOptions('-y')
                        .on('start', (commandLine) => {
                            logger.info(`${execLogPrefix}[FFMPEG_MERGE] 开始合并原始片段: ${commandLine}`);
                            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_PROCESSING, {
                                detail: 'FFmpeg正在合并视频片段',
                                current: 65,
                                total: 100
                            });
                        })
                        .on('error', (err, stdout, stderr) => {
                            const errorMsg = `FFmpeg片段合并错误: ${err.message}`;
                            logger.error(`${execLogPrefix}[FFMPEG_ERROR] ${errorMsg}`);
                            if (stderr) logger.error(`${execLogPrefix} FFmpeg stderr: ${stderr}`);
                            reject(new Error(errorMsg));
                        })
                        .on('end', () => {
                            logger.info(`${execLogPrefix}[FFMPEG_SUCCESS] 原始片段合并成功: ${mergedRawVideoPath}`);
                            // 清理原始片段临时文件
                            try {
                                rawSegmentPaths.forEach(segmentPath => {
                                    if (fs.existsSync(segmentPath)) {
                                        fs.unlinkSync(segmentPath);
                                    }
                                });
                                logger.info(`${execLogPrefix} 清理原始片段临时文件完成`);
                            } catch (cleanupError) {
                                logger.warn(`${execLogPrefix} 清理原始片段文件时出错: ${cleanupError.message}`);
                            }
                            resolve();
                        })
                        .save(mergedRawVideoPath);
                });

                logger.info(`${execLogPrefix} 第二步完成：原始片段合并成功，输出文件: ${mergedRawVideoPath}`);
            }

            // === 步骤4: 对合并后的短视频进行画面裁剪 ===
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '第三步：对合并后的短视频进行画面裁剪',
                current: 75,
                total: 100
            });

            // 获取合并后视频尺寸以校验裁剪参数
            let mergedVideoWidth, mergedVideoHeight;
            try {
                const dimensions = await getVideoDimensions(mergedRawVideoPath);
                mergedVideoWidth = dimensions.width;
                mergedVideoHeight = dimensions.height;
                logger.info(`${execLogPrefix} 获取到合并后视频尺寸: ${mergedVideoWidth}x${mergedVideoHeight}`);
            } catch (dimError) {
                throw new Error(`获取合并后视频尺寸失败: ${dimError.message}`);
            }

            // 校验裁剪参数
            if (cropData.cropWidth > mergedVideoWidth) {
                throw new Error(`裁剪宽度 (${cropData.cropWidth}px) 不能大于视频宽度 (${mergedVideoWidth}px)`);
            }
            if (cropData.cropHeight > mergedVideoHeight) {
                throw new Error(`裁剪高度 (${cropData.cropHeight}px) 不能大于视频高度 (${mergedVideoHeight}px)`);
            }
            if (cropData.cropXOffset + cropData.cropWidth > mergedVideoWidth) {
                throw new Error(`裁剪X偏移 + 裁剪宽度超出视频宽度`);
            }
            if (cropData.cropYOffset + cropData.cropHeight > mergedVideoHeight) {
                throw new Error(`裁剪Y偏移 + 裁剪高度超出视频高度`);
            }

            logger.info(`${execLogPrefix} 裁剪参数校验通过`);

            // 生成最终裁剪后的视频文件名
            const finalVideoFileName = `${originalVideoBaseName}_final_cropped_${cropData.cropWidth}x${cropData.cropHeight}_${clipSegments.length}segments${originalVideoExt}`;
            const finalVideoPath = path.join(savePath, finalVideoFileName);

            // 执行画面裁剪
            const cropFilterString = `crop=w=${cropData.cropWidth}:h=${cropData.cropHeight}:x=${cropData.cropXOffset}:y=${cropData.cropYOffset}`;

            await new Promise((resolve, reject) => {
                const ffmpegCommand = ffmpeg(mergedRawVideoPath);
                if (config.ffmpegPath) ffmpegCommand.setFfmpegPath(config.ffmpegPath);
                if (config.ffprobePath) ffmpegCommand.setFfprobePath(config.ffprobePath);

                ffmpegCommand
                    .videoFilters(cropFilterString)
                    .videoCodec('libx264')
                    .audioCodec('aac')
                    .outputOptions('-y')
                    .on('start', (commandLine) => {
                        logger.info(`${execLogPrefix}[FFMPEG_CROP] 开始对合并后视频进行画面裁剪: ${commandLine}`);
                        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_PROCESSING, {
                            detail: 'FFmpeg正在对短视频进行画面裁剪',
                            current: 85,
                            total: 100
                        });
                    })
                    .on('error', (err, stdout, stderr) => {
                        const errorMsg = `FFmpeg画面裁剪错误: ${err.message}`;
                        logger.error(`${execLogPrefix}[FFMPEG_ERROR] ${errorMsg}`);
                        if (stderr) logger.error(`${execLogPrefix} FFmpeg stderr: ${stderr}`);
                        reject(new Error(errorMsg));
                    })
                    .on('end', () => {
                        logger.info(`${execLogPrefix}[FFMPEG_SUCCESS] 画面裁剪成功: ${finalVideoPath}`);
                        // 清理合并后的原始尺寸临时文件
                        try {
                            if (rawSegmentPaths.length > 1 && fs.existsSync(mergedRawVideoPath)) {
                                fs.unlinkSync(mergedRawVideoPath);
                                logger.info(`${execLogPrefix} 清理合并后原始尺寸临时文件: ${mergedRawVideoPath}`);
                            }
                        } catch (cleanupError) {
                            logger.warn(`${execLogPrefix} 清理合并临时文件时出错: ${cleanupError.message}`);
                        }
                        resolve();
                    })
                    .save(finalVideoPath);
            });

            logger.info(`${execLogPrefix} 第三步完成：画面裁剪成功，最终输出文件: ${finalVideoPath}`);

            // === 步骤5: 结果处理和完成 ===
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FINALIZING, {
                detail: '整理任务结果',
                current: 95,
                total: 100
            });

            // 更新context中的结果
            context.processedVideoPath = finalVideoPath;
            context.processedVideoFileName = finalVideoFileName;

            // 将最终处理后的视频路径赋值给原始视频路径字段（用于后续任务）
            context.originalVideoPath = finalVideoPath;
            context.originalVideoFileName = finalVideoFileName;

            const result = {
                processedVideoPath: finalVideoPath,
                processedVideoFileName: finalVideoFileName,
                originalVideoPath: context.originalVideoPath, // 使用更新后的originalVideoPath
                originalVideoFileName: context.originalVideoFileName, // 添加更新后的originalVideoFileName
                cropData: cropData,
                clipSegments: clipSegments,
                segmentCount: clipSegments.length,
                totalDuration: clipSegments.reduce((sum, seg) => sum + (seg.endTime - seg.startTime), 0),
                message: `ClipMediaTask 优化执行成功完成。先从原视频提取了 ${clipSegments.length} 个片段，合并后再进行画面裁剪，大幅提升了处理效率。`
            };

            this.complete(result);
            logger.info(`${execLogPrefix} ClipMediaTask 优化流程成功完成。最终输出: ${finalVideoPath}`);
            return result;

        } catch (error) {
            logger.error(`${execLogPrefix}[EXECUTE_CATCH_ERROR] ClipMediaTask 执行中捕获到未处理错误: ${error.message}`, { stack: error.stack }); // 日志：在最外层catch块中记录未处理的错误。
            this.fail(error);
            throw error;
        }
    }
}

module.exports = ClipMediaTask;
logger.info(`${taskModuleLogPrefix}ClipMediaTask 类已导出。`); // 日志：确认 ClipMediaTask 类已成功导出。 