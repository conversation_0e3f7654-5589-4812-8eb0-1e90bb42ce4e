/**
 * 模板配置文件
 * 
 * 定义不同风格模板的视觉参数和动画效果
 * 支持根据片段类型（header/background/footer）和视频比例（16:9/9:16）进行配置
 */

const templateConfig = {
    // 现代风格配置
    modern: {
        name: "现代风格",
        description: "简洁现代，渐变色彩，流畅动画",
        
        // 基础配置
        fontFamily: "'Helvetica Neue', 'Arial', sans-serif",
        
        // 头部片段配置
        header: {
            // 16:9 横屏配置
            "16:9": {
                backgroundColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                decorationGradient: "conic-gradient(from 0deg, #667eea, #764ba2, #667eea)",
                textColor: "#ffffff",
                textShadow: "0 2px 10px rgba(0,0,0,0.3)",
                
                titleFontSize: 72,
                titleFontWeight: "bold",
                titleMarginBottom: 30,
                titleAnimation: "fadeInUp",
                
                subtitleFontSize: 36,
                subtitleFontWeight: "normal",
                subtitleAnimation: "fadeInUp",
                subtitleDelay: 0.5,
                
                showTitle: true,
                showSubtitle: true
            },
            // 9:16 竖屏配置
            "9:16": {
                backgroundColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                decorationGradient: "conic-gradient(from 0deg, #667eea, #764ba2, #667eea)",
                textColor: "#ffffff",
                textShadow: "0 2px 8px rgba(0,0,0,0.3)",
                
                titleFontSize: 48,
                titleFontWeight: "bold",
                titleMarginBottom: 20,
                titleAnimation: "fadeInScale",
                
                subtitleFontSize: 24,
                subtitleFontWeight: "normal",
                subtitleAnimation: "fadeInScale",
                subtitleDelay: 0.3,
                
                showTitle: true,
                showSubtitle: true
            }
        },
        
        // 背景片段配置
        background: {
            // 16:9 横屏配置
            "16:9": {
                backgroundColor: "linear-gradient(45deg, #f0f2f5 0%, #e8ebf0 50%, #f0f2f5 100%)",
                decorationGradient: "conic-gradient(from 45deg, #e8ebf0, #f0f2f5, #e8ebf0)",
                textColor: "#333333",
                textShadow: "none",
                
                titleFontSize: 0,
                titleFontWeight: "normal",
                titleMarginBottom: 0,
                titleAnimation: "pulse",
                
                subtitleFontSize: 0,
                subtitleFontWeight: "normal",
                subtitleAnimation: "pulse",
                subtitleDelay: 0,
                
                showTitle: false,
                showSubtitle: false
            },
            // 9:16 竖屏配置
            "9:16": {
                backgroundColor: "linear-gradient(45deg, #f0f2f5 0%, #e8ebf0 50%, #f0f2f5 100%)",
                decorationGradient: "conic-gradient(from 45deg, #e8ebf0, #f0f2f5, #e8ebf0)",
                textColor: "#333333",
                textShadow: "none",
                
                titleFontSize: 0,
                titleFontWeight: "normal",
                titleMarginBottom: 0,
                titleAnimation: "pulse",
                
                subtitleFontSize: 0,
                subtitleFontWeight: "normal",
                subtitleAnimation: "pulse",
                subtitleDelay: 0,
                
                showTitle: false,
                showSubtitle: false
            }
        },
        
        // 尾部片段配置
        footer: {
            // 16:9 横屏配置
            "16:9": {
                backgroundColor: "linear-gradient(135deg, #764ba2 0%, #667eea 100%)",
                decorationGradient: "conic-gradient(from 180deg, #764ba2, #667eea, #764ba2)",
                textColor: "#ffffff",
                textShadow: "0 2px 10px rgba(0,0,0,0.3)",
                
                titleFontSize: 64,
                titleFontWeight: "bold",
                titleMarginBottom: 25,
                titleAnimation: "slideInLeft",
                
                subtitleFontSize: 32,
                subtitleFontWeight: "normal",
                subtitleAnimation: "slideInRight",
                subtitleDelay: 0.4,
                
                showTitle: true,
                showSubtitle: true
            },
            // 9:16 竖屏配置
            "9:16": {
                backgroundColor: "linear-gradient(135deg, #764ba2 0%, #667eea 100%)",
                decorationGradient: "conic-gradient(from 180deg, #764ba2, #667eea, #764ba2)",
                textColor: "#ffffff",
                textShadow: "0 2px 8px rgba(0,0,0,0.3)",
                
                titleFontSize: 42,
                titleFontWeight: "bold",
                titleMarginBottom: 18,
                titleAnimation: "fadeInDown",
                
                subtitleFontSize: 22,
                subtitleFontWeight: "normal",
                subtitleAnimation: "fadeInDown",
                subtitleDelay: 0.3,
                
                showTitle: true,
                showSubtitle: true
            }
        }
    }
};

/**
 * 获取指定风格和片段类型的配置
 * @param {string} styleName - 风格名称 (如: 'modern')
 * @param {string} segmentType - 片段类型 (如: 'header', 'background', 'footer')
 * @param {string} aspectRatio - 视频比例 (如: '16:9', '9:16')
 * @returns {Object} 配置对象
 */
function getTemplateConfig(styleName, segmentType, aspectRatio) {
    const style = templateConfig[styleName];
    if (!style) {
        throw new Error(`不支持的风格: ${styleName}`);
    }
    
    const segment = style[segmentType];
    if (!segment) {
        throw new Error(`风格 ${styleName} 不支持片段类型: ${segmentType}`);
    }
    
    const config = segment[aspectRatio];
    if (!config) {
        throw new Error(`风格 ${styleName} 的片段 ${segmentType} 不支持比例: ${aspectRatio}`);
    }
    
    return config;
}

/**
 * 获取可用的风格列表
 * @returns {Array} 风格列表
 */
function getAvailableStyles() {
    return Object.keys(templateConfig).map(key => ({
        name: key,
        displayName: templateConfig[key].name,
        description: templateConfig[key].description
    }));
}

/**
 * 获取默认文字内容
 * @param {string} segmentType - 片段类型
 * @returns {Object} 包含title和subtitle的对象
 */
function getDefaultContent(segmentType) {
    const defaultContents = {
        header: {
            title: "精彩内容即将开始",
            subtitle: "请准备好观看"
        },
        background: {
            title: "",
            subtitle: ""
        },
        footer: {
            title: "感谢观看",
            subtitle: "请关注我们获取更多内容"
        }
    };
    
    return defaultContents[segmentType] || defaultContents.background;
}

module.exports = {
    templateConfig,
    getTemplateConfig,
    getAvailableStyles,
    getDefaultContent
};
