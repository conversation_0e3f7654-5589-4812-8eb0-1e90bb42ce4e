# &lt;Still&gt;

## 概述

`<Still>` 是一个单帧的 [`<Composition>`](./Composition.md)。它是定义渲染图像而非视频的组合的便捷方式。Still 组件与 Composition 组件具有相同的 API，但不需要传递 `durationInFrames` 和 `fps` 属性。

## 语法

```typescript
import { Still } from "remotion";

<Still 
  id="my-image" 
  component={MyComponent} 
  width={1080} 
  height={1080} 
/>
```

## 核心属性

### id (必需)
- **类型**: `string`
- **描述**: Still 的唯一标识符

### component (必需)
- **类型**: `React.ComponentType<any>`
- **描述**: 要渲染的 React 组件

### width (必需)
- **类型**: `number`
- **描述**: 图像的宽度（像素）

### height (必需)
- **类型**: `number`
- **描述**: 图像的高度（像素）

### defaultProps (可选)
- **类型**: `object`
- **描述**: 传递给组件的默认属性

### schema (可选)
- **类型**: `TCompMetadata["schema"]`
- **描述**: 用于验证输入属性的模式

## 基础用法

### 1. 基础静态图像

```typescript
import { Still } from "remotion";

const MyImage = () => {
  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#3498db",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: 48
    }}>
      静态图像
    </div>
  );
};

export const MyVideo = () => {
  return (
    <Still 
      id="my-image" 
      component={MyImage} 
      width={1080} 
      height={1080} 
    />
  );
};
```

### 2. 带默认属性的 Still

```typescript
import { Still } from "remotion";

interface ImageProps {
  title: string;
  backgroundColor: string;
  textColor: string;
}

const CustomImage: React.FC<ImageProps> = ({ 
  title, 
  backgroundColor, 
  textColor 
}) => {
  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: textColor,
      fontSize: 64,
      fontWeight: "bold",
      textAlign: "center",
      padding: 40
    }}>
      {title}
    </div>
  );
};

export const MyVideo = () => {
  return (
    <Still 
      id="custom-image" 
      component={CustomImage} 
      width={1920} 
      height={1080}
      defaultProps={{
        title: "我的标题",
        backgroundColor: "#e74c3c",
        textColor: "white"
      }}
    />
  );
};
```

## 实际应用场景

### 1. 社交媒体图片生成

```typescript
import { Still, staticFile } from "remotion";

interface SocialMediaCardProps {
  title: string;
  subtitle: string;
  authorName: string;
  authorAvatar: string;
  backgroundImage: string;
}

const SocialMediaCard: React.FC<SocialMediaCardProps> = ({
  title,
  subtitle,
  authorName,
  authorAvatar,
  backgroundImage
}) => {
  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundImage: `url(${backgroundImage})`,
      backgroundSize: "cover",
      backgroundPosition: "center"
    }}>
      {/* 遮罩层 */}
      <div style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.5)"
      }} />
      
      {/* 内容区域 */}
      <div style={{
        position: "relative",
        zIndex: 1,
        height: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        padding: 60,
        color: "white"
      }}>
        {/* 主要内容 */}
        <div>
          <h1 style={{
            fontSize: 72,
            fontWeight: "bold",
            marginBottom: 20,
            lineHeight: 1.2
          }}>
            {title}
          </h1>
          <p style={{
            fontSize: 32,
            opacity: 0.9,
            lineHeight: 1.4
          }}>
            {subtitle}
          </p>
        </div>
        
        {/* 作者信息 */}
        <div style={{
          display: "flex",
          alignItems: "center",
          gap: 20
        }}>
          <img 
            src={authorAvatar}
            style={{
              width: 60,
              height: 60,
              borderRadius: "50%",
              border: "3px solid white"
            }}
            alt="作者头像"
          />
          <span style={{
            fontSize: 24,
            fontWeight: "500"
          }}>
            {authorName}
          </span>
        </div>
      </div>
    </div>
  );
};

// Instagram 正方形格式
export const InstagramPost = () => {
  return (
    <Still 
      id="instagram-post" 
      component={SocialMediaCard} 
      width={1080} 
      height={1080}
      defaultProps={{
        title: "探索 Remotion 的强大功能",
        subtitle: "使用 React 和 TypeScript 创建令人惊叹的视频和图像",
        authorName: "开发者",
        authorAvatar: staticFile("avatar.jpg"),
        backgroundImage: staticFile("background.jpg")
      }}
    />
  );
};

// Twitter 横向格式
export const TwitterCard = () => {
  return (
    <Still 
      id="twitter-card" 
      component={SocialMediaCard} 
      width={1200} 
      height={630}
      defaultProps={{
        title: "Remotion 开发技巧",
        subtitle: "学习如何创建动态视频内容",
        authorName: "技术博主",
        authorAvatar: staticFile("avatar.jpg"),
        backgroundImage: staticFile("tech-background.jpg")
      }}
    />
  );
};
```

### 2. 证书和奖状生成

```typescript
import { Still, staticFile } from "remotion";

interface CertificateProps {
  recipientName: string;
  courseName: string;
  completionDate: string;
  instructorName: string;
  certificateId: string;
}

const Certificate: React.FC<CertificateProps> = ({
  recipientName,
  courseName,
  completionDate,
  instructorName,
  certificateId
}) => {
  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#f8f9fa",
      border: "20px solid #2c3e50",
      position: "relative",
      fontFamily: "serif"
    }}>
      {/* 装饰边框 */}
      <div style={{
        position: "absolute",
        top: 40,
        left: 40,
        right: 40,
        bottom: 40,
        border: "3px solid #34495e",
        borderRadius: 10
      }} />
      
      {/* 内容区域 */}
      <div style={{
        padding: 100,
        textAlign: "center",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        gap: 40
      }}>
        {/* 标题 */}
        <h1 style={{
          fontSize: 64,
          color: "#2c3e50",
          marginBottom: 20,
          fontWeight: "bold",
          letterSpacing: 2
        }}>
          结业证书
        </h1>
        
        {/* 副标题 */}
        <p style={{
          fontSize: 24,
          color: "#7f8c8d",
          marginBottom: 40
        }}>
          特此证明
        </p>
        
        {/* 获得者姓名 */}
        <h2 style={{
          fontSize: 48,
          color: "#e74c3c",
          fontWeight: "bold",
          borderBottom: "2px solid #e74c3c",
          paddingBottom: 10,
          marginBottom: 30
        }}>
          {recipientName}
        </h2>
        
        {/* 课程信息 */}
        <p style={{
          fontSize: 28,
          color: "#2c3e50",
          lineHeight: 1.5,
          marginBottom: 40
        }}>
          已成功完成 <strong>{courseName}</strong> 课程的学习
        </p>
        
        {/* 底部信息 */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginTop: 60
        }}>
          <div style={{ textAlign: "left" }}>
            <p style={{ fontSize: 18, color: "#7f8c8d" }}>完成日期</p>
            <p style={{ fontSize: 20, color: "#2c3e50", fontWeight: "bold" }}>
              {completionDate}
            </p>
          </div>
          
          <div style={{ textAlign: "center" }}>
            <div style={{
              width: 150,
              height: 2,
              backgroundColor: "#2c3e50",
              marginBottom: 10
            }} />
            <p style={{ fontSize: 18, color: "#2c3e50" }}>
              {instructorName}
            </p>
            <p style={{ fontSize: 14, color: "#7f8c8d" }}>
              课程讲师
            </p>
          </div>
          
          <div style={{ textAlign: "right" }}>
            <p style={{ fontSize: 18, color: "#7f8c8d" }}>证书编号</p>
            <p style={{ fontSize: 16, color: "#2c3e50", fontFamily: "monospace" }}>
              {certificateId}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const CourseCertificate = () => {
  return (
    <Still 
      id="course-certificate" 
      component={Certificate} 
      width={1200} 
      height={800}
      defaultProps={{
        recipientName: "张三",
        courseName: "React 高级开发",
        completionDate: "2024年12月15日",
        instructorName: "李老师",
        certificateId: "CERT-2024-001"
      }}
    />
  );
};
```

### 3. 产品展示卡片

```typescript
import { Still, staticFile } from "remotion";

interface ProductCardProps {
  productName: string;
  productImage: string;
  price: string;
  originalPrice?: string;
  features: string[];
  badge?: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  productName,
  productImage,
  price,
  originalPrice,
  features,
  badge
}) => {
  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "white",
      display: "flex",
      boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
    }}>
      {/* 左侧图片区域 */}
      <div style={{
        flex: 1,
        position: "relative",
        backgroundColor: "#f8f9fa"
      }}>
        <img 
          src={productImage}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover"
          }}
          alt="产品图片"
        />
        
        {/* 徽章 */}
        {badge && (
          <div style={{
            position: "absolute",
            top: 30,
            left: 30,
            backgroundColor: "#e74c3c",
            color: "white",
            padding: "10px 20px",
            borderRadius: 20,
            fontSize: 16,
            fontWeight: "bold"
          }}>
            {badge}
          </div>
        )}
      </div>
      
      {/* 右侧信息区域 */}
      <div style={{
        flex: 1,
        padding: 60,
        display: "flex",
        flexDirection: "column",
        justifyContent: "center"
      }}>
        {/* 产品名称 */}
        <h1 style={{
          fontSize: 48,
          fontWeight: "bold",
          color: "#2c3e50",
          marginBottom: 30,
          lineHeight: 1.2
        }}>
          {productName}
        </h1>
        
        {/* 价格 */}
        <div style={{
          display: "flex",
          alignItems: "baseline",
          gap: 15,
          marginBottom: 40
        }}>
          <span style={{
            fontSize: 42,
            fontWeight: "bold",
            color: "#e74c3c"
          }}>
            {price}
          </span>
          {originalPrice && (
            <span style={{
              fontSize: 24,
              color: "#7f8c8d",
              textDecoration: "line-through"
            }}>
              {originalPrice}
            </span>
          )}
        </div>
        
        {/* 特性列表 */}
        <div style={{ marginBottom: 40 }}>
          <h3 style={{
            fontSize: 24,
            color: "#2c3e50",
            marginBottom: 20
          }}>
            主要特性
          </h3>
          <ul style={{
            listStyle: "none",
            padding: 0,
            margin: 0
          }}>
            {features.map((feature, index) => (
              <li key={index} style={{
                fontSize: 18,
                color: "#34495e",
                marginBottom: 12,
                display: "flex",
                alignItems: "center",
                gap: 10
              }}>
                <span style={{
                  width: 8,
                  height: 8,
                  backgroundColor: "#27ae60",
                  borderRadius: "50%"
                }} />
                {feature}
              </li>
            ))}
          </ul>
        </div>
        
        {/* 行动按钮 */}
        <div style={{
          backgroundColor: "#3498db",
          color: "white",
          padding: "15px 30px",
          borderRadius: 8,
          fontSize: 20,
          fontWeight: "bold",
          textAlign: "center",
          cursor: "pointer"
        }}>
          立即购买
        </div>
      </div>
    </div>
  );
};

export const ProductShowcase = () => {
  return (
    <Still 
      id="product-showcase" 
      component={ProductCard} 
      width={1200} 
      height={800}
      defaultProps={{
        productName: "专业摄影套装",
        productImage: staticFile("camera-product.jpg"),
        price: "¥2,999",
        originalPrice: "¥3,999",
        badge: "限时优惠",
        features: [
          "4K 超高清录制",
          "光学防抖技术",
          "专业镜头套装",
          "便携式三脚架",
          "2年质保服务"
        ]
      }}
    />
  );
};
```

### 4. 多格式 Still 组合

```typescript
import { Still } from "remotion";

const FlexibleCard: React.FC<{
  title: string;
  content: string;
  format: "square" | "landscape" | "portrait";
}> = ({ title, content, format }) => {
  const getLayoutStyles = () => {
    switch (format) {
      case "square":
        return {
          flexDirection: "column" as const,
          textAlign: "center" as const,
          fontSize: 32
        };
      case "landscape":
        return {
          flexDirection: "row" as const,
          textAlign: "left" as const,
          fontSize: 28
        };
      case "portrait":
        return {
          flexDirection: "column" as const,
          textAlign: "center" as const,
          fontSize: 24
        };
    }
  };

  const layoutStyles = getLayoutStyles();

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#34495e",
      color: "white",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: 40,
      ...layoutStyles
    }}>
      <h1 style={{
        fontSize: layoutStyles.fontSize * 1.5,
        marginBottom: 20,
        fontWeight: "bold"
      }}>
        {title}
      </h1>
      <p style={{
        fontSize: layoutStyles.fontSize,
        lineHeight: 1.5,
        opacity: 0.9
      }}>
        {content}
      </p>
    </div>
  );
};

// 正方形格式 (Instagram)
export const SquareFormat = () => {
  return (
    <Still 
      id="square-format" 
      component={FlexibleCard} 
      width={1080} 
      height={1080}
      defaultProps={{
        title: "正方形格式",
        content: "适用于 Instagram 和其他社交媒体平台",
        format: "square"
      }}
    />
  );
};

// 横向格式 (YouTube 缩略图)
export const LandscapeFormat = () => {
  return (
    <Still 
      id="landscape-format" 
      component={FlexibleCard} 
      width={1280} 
      height={720}
      defaultProps={{
        title: "横向格式",
        content: "适用于 YouTube 缩略图和网站横幅",
        format: "landscape"
      }}
    />
  );
};

// 竖向格式 (Stories)
export const PortraitFormat = () => {
  return (
    <Still 
      id="portrait-format" 
      component={FlexibleCard} 
      width={1080} 
      height={1920}
      defaultProps={{
        title: "竖向格式",
        content: "适用于 Instagram Stories 和 TikTok",
        format: "portrait"
      }}
    />
  );
};
```

### 5. 数据驱动的图表

```typescript
import { Still } from "remotion";

interface ChartData {
  label: string;
  value: number;
  color: string;
}

interface ChartProps {
  title: string;
  data: ChartData[];
  chartType: "bar" | "pie";
}

const DataChart: React.FC<ChartProps> = ({ title, data, chartType }) => {
  const maxValue = Math.max(...data.map(d => d.value));

  const renderBarChart = () => (
    <div style={{
      display: "flex",
      alignItems: "end",
      gap: 20,
      height: 300,
      padding: "0 40px"
    }}>
      {data.map((item, index) => (
        <div key={index} style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          flex: 1
        }}>
          <div style={{
            width: "100%",
            height: (item.value / maxValue) * 250,
            backgroundColor: item.color,
            borderRadius: "4px 4px 0 0",
            marginBottom: 10,
            display: "flex",
            alignItems: "end",
            justifyContent: "center",
            color: "white",
            fontWeight: "bold",
            fontSize: 16,
            paddingBottom: 10
          }}>
            {item.value}
          </div>
          <span style={{
            fontSize: 14,
            color: "#2c3e50",
            textAlign: "center"
          }}>
            {item.label}
          </span>
        </div>
      ))}
    </div>
  );

  const renderPieChart = () => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: 40
      }}>
        <div style={{
          width: 300,
          height: 300,
          borderRadius: "50%",
          position: "relative",
          overflow: "hidden"
        }}>
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const angle = (item.value / total) * 360;
            const startAngle = currentAngle;
            currentAngle += angle;

            return (
              <div
                key={index}
                style={{
                  position: "absolute",
                  width: "50%",
                  height: "50%",
                  backgroundColor: item.color,
                  transformOrigin: "100% 100%",
                  transform: `rotate(${startAngle}deg) skew(${90 - angle}deg)`
                }}
              />
            );
          })}
        </div>
        
        <div style={{ flex: 1 }}>
          {data.map((item, index) => (
            <div key={index} style={{
              display: "flex",
              alignItems: "center",
              gap: 10,
              marginBottom: 15
            }}>
              <div style={{
                width: 20,
                height: 20,
                backgroundColor: item.color,
                borderRadius: 3
              }} />
              <span style={{ fontSize: 16, color: "#2c3e50" }}>
                {item.label}: {item.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "white",
      padding: 60,
      display: "flex",
      flexDirection: "column",
      alignItems: "center"
    }}>
      <h1 style={{
        fontSize: 36,
        color: "#2c3e50",
        marginBottom: 40,
        textAlign: "center"
      }}>
        {title}
      </h1>
      
      {chartType === "bar" ? renderBarChart() : renderPieChart()}
    </div>
  );
};

export const SalesChart = () => {
  return (
    <Still 
      id="sales-chart" 
      component={DataChart} 
      width={1200} 
      height={800}
      defaultProps={{
        title: "2024年季度销售数据",
        chartType: "bar",
        data: [
          { label: "Q1", value: 120, color: "#3498db" },
          { label: "Q2", value: 150, color: "#2ecc71" },
          { label: "Q3", value: 180, color: "#f39c12" },
          { label: "Q4", value: 200, color: "#e74c3c" }
        ]
      }}
    />
  );
};
```

## 与 Composition 的区别

| 特性 | Still | Composition |
|------|-------|-------------|
| 帧数 | 固定为 1 帧 | 可指定任意帧数 |
| FPS | 不需要指定 | 必须指定 |
| 用途 | 静态图像 | 动态视频 |
| 渲染输出 | 图片格式 (PNG, JPEG) | 视频格式 (MP4, WebM) |

## 最佳实践

1. **图像尺寸**: 根据目标平台选择合适的尺寸
2. **性能优化**: 避免在 Still 中使用动画相关的钩子
3. **响应式设计**: 考虑不同设备的显示需求
4. **资源管理**: 合理使用静态资源和外部图片
5. **模板化**: 创建可重用的 Still 模板

## 常见用例

- 社交媒体图片生成
- 证书和奖状制作
- 产品展示卡片
- 数据可视化图表
- 网站横幅和缩略图

## 相关 API

- [`<Composition>`](./Composition.md) - 视频组合组件
- [`staticFile()`](./staticFile.md) - 静态文件访问
- [`getInputProps()`](./getInputProps.md) - 获取输入属性

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/Still.tsx)
