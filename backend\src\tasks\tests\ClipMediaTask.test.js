/**
 * @功能概述: ClipMediaTask 的测试文件。
 *           该测试会读取真实的视频、SRT和JSON数据文件，
 *           执行媒体剪辑任务，并验证输出结果。
 *           可直接通过 'node ClipMediaTask.test.js' 执行。
 * @注意事项:
 *   - 本测试依赖于实际的视频文件和 FFmpeg 安装。
 *   - 测试会在 'backend/uploads/' 目录下生成剪辑后的视频和字幕文件，请注意清理。
 */

const fs = require('fs');
const path = require('path');
const ClipMediaTask = require('../ClipMediaTask');
const logger = require('../../utils/logger');
const { TASK_STATUS } = require('../../constants/progress');
const { getVideoDimensions } = require('../../utils/videoUtils'); // 调整路径

// 测试配置
const testConfig = {
    videoFileName: 'videoFile-1748227224915-599592991.mp4',
    chineseSrtFileName: 'videoFile-1748227224915-599592991-chinese-subtitle.srt',
    englishSrtFileName: 'videoFile-1748227224915-599592991-english-subtitle.srt',
    jsonDataFileName: 'videoFile-1748227224915-599592991-raw-transcription.json',
    startTime: 0,
    endTime: 60, // 用于主要成功场景测试
};

const projectRootUploadsPath = path.join(__dirname, '..', '..', '..', 'uploads');
const testLogPrefix = '[文件：ClipMediaTask.test.js][媒体剪辑任务测试]';

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        throw new Error(`Assertion Failed: ${message}`);
    }
}

function assertEqual(actual, expected, message) {
    if (actual !== expected) {
        throw new Error(`Assertion Failed: ${message}. Expected '${expected}', but got '${actual}'.`);
    }
}

function assertDefined(value, message) {
    if (typeof value === 'undefined') {
        throw new Error(`Assertion Failed: ${message}. Value was undefined.`);
    }
}

function assertMatch(string, regex, message) {
    if (typeof string !== 'string' || !regex.test(string)) {
        throw new Error(`Assertion Failed: ${message}. String '${string}' did not match regex '${regex}'.`);
    }
}

async function runClipMediaTaskTests() {
    logger.info(`${testLogPrefix} 媒体剪辑任务测试脚本开始执行。`);
    let baseInitialContext;
    let task;
    let originalVideoWidth, originalVideoHeight;

    // --- 上下文准备 (基础) ---
    logger.info(`${testLogPrefix} [上下文准备] 开始准备基础测试上下文。`);
    try {
        const videoPath = path.join(projectRootUploadsPath, testConfig.videoFileName);
        const chineseSrtPath = path.join(projectRootUploadsPath, testConfig.chineseSrtFileName);
        const englishSrtPath = path.join(projectRootUploadsPath, testConfig.englishSrtFileName);
        const jsonDataPath = path.join(projectRootUploadsPath, testConfig.jsonDataFileName);

        assert(fs.existsSync(videoPath), `测试视频文件未找到: ${videoPath}`);
        assert(fs.existsSync(chineseSrtPath), `中文字幕文件未找到: ${chineseSrtPath}`);
        assert(fs.existsSync(englishSrtPath), `英文字幕文件未找到: ${englishSrtPath}`);
        assert(fs.existsSync(jsonDataPath), `JSON数据文件未找到: ${jsonDataPath}`);
        
        const chineseSrtContent = fs.readFileSync(chineseSrtPath, 'utf-8');
        const englishSrtContent = fs.readFileSync(englishSrtPath, 'utf-8');
        const jsonDataString = fs.readFileSync(jsonDataPath, 'utf-8');
        const extractedRawJsonData = JSON.parse(jsonDataString);

        baseInitialContext = {
            uploadedVideoPath: videoPath,
            originalVideoName: testConfig.videoFileName,
            chineseSrtContent,
            englishSrtContent,
            extractedRawJsonData,
        };

        if (!baseInitialContext.extractedRawJsonData.duration) {
            logger.warn(`${testLogPrefix} [上下文准备] 测试用的 extractedRawJsonData 中缺少 duration 字段。将使用硬编码的 150s。`);
            baseInitialContext.extractedRawJsonData.duration = 150; 
        }
        logger.info(`${testLogPrefix} [上下文准备] 基础测试上下文准备完成。视频总时长: ${baseInitialContext.extractedRawJsonData.duration}s`);

        // 获取原始视频尺寸
        logger.info(`${testLogPrefix} [视频尺寸获取] 开始获取原始视频尺寸...`);
        const dimensions = await getVideoDimensions(baseInitialContext.uploadedVideoPath);
        originalVideoWidth = dimensions.width;
        originalVideoHeight = dimensions.height;
        logger.info(`${testLogPrefix} [视频尺寸获取] 成功获取原始视频尺寸: ${originalVideoWidth}x${originalVideoHeight}`);

    } catch (error) {
        logger.error(`${testLogPrefix} [上下文准备或视频尺寸获取] 失败: ${error.message}`);
        throw error; 
    }

    // --- 测试用例 1: 成功剪辑视频和字幕 (正常 endTime, 无视觉裁剪) ---
    logger.info(`${testLogPrefix} [测试用例 1：成功剪辑 - 无视觉裁剪] 开始执行。`);
    const successContextNoCrop = {
        ...baseInitialContext,
        startTime: testConfig.startTime,
        endTime: testConfig.endTime,
        reqId: 'test-success-no-crop-req-' + Date.now(),
    };
    logger.debug(`${testLogPrefix} [测试用例 1] startTime: ${successContextNoCrop.startTime}, endTime: ${successContextNoCrop.endTime}`);
    task = new ClipMediaTask();
    const progressUpdates1 = [];
    const mockProgressCallback1 = (progressData) => progressUpdates1.push(progressData);

    try {
        const result = await task.execute(successContextNoCrop, mockProgressCallback1);
        logger.info(`${testLogPrefix} [测试用例 1：成功剪辑 - 无视觉裁剪] ClipMediaTask 执行完毕。结果: ${JSON.stringify(result, null, 2)}`);

        assertDefined(result, "[成功剪辑 - 无视觉裁剪] 结果对象应已定义");
        assertEqual(task.status, TASK_STATUS.COMPLETED, "[成功剪辑 - 无视觉裁剪] 任务状态应为 COMPLETED");
        
        assertDefined(result.clippedVideoPath, "[成功剪辑 - 无视觉裁剪] 剪辑后的视频路径应已定义");
        assert(fs.existsSync(result.clippedVideoPath), `[成功剪辑 - 无视觉裁剪] 剪辑后的视频文件应存在于: ${result.clippedVideoPath}`);
        logger.info(`${testLogPrefix} [测试用例 1：成功剪辑 - 无视觉裁剪] 剪辑后视频文件存在: ${result.clippedVideoPath}`);
        
        assertDefined(result.clippedOriginalVideoName, "[成功剪辑 - 无视觉裁剪] 剪辑后的原始文件名应已定义");
        assert(result.clippedOriginalVideoName.includes('_clip_'), "[成功剪辑 - 无视觉裁剪] 剪辑后的文件名应包含 '_clip_'");
        assert(!result.clippedOriginalVideoName.includes('_cropped'), "[成功剪辑 - 无视觉裁剪] 剪辑后的文件名不应包含 '_cropped'");
        
        assertDefined(result.clippedChineseSrtContent, "[成功剪辑 - 无视觉裁剪] 剪辑后的中文字幕内容应已定义");
        assertEqual(typeof result.clippedChineseSrtContent, 'string', "[成功剪辑 - 无视觉裁剪] 剪辑后的中文字幕内容应为字符串");
        assertDefined(result.clippedChineseSrtFilePath, "[成功剪辑 - 无视觉裁剪] 剪辑后的中文字幕文件路径应已定义");
        assert(fs.existsSync(result.clippedChineseSrtFilePath), `[成功剪辑 - 无视觉裁剪] 剪辑后的中文字幕文件应存在于: ${result.clippedChineseSrtFilePath}`);
        
        assertDefined(result.clippedEnglishSrtContent, "[成功剪辑 - 无视觉裁剪] 剪辑后的英文字幕内容应已定义");
        assertEqual(typeof result.clippedEnglishSrtContent, 'string', "[成功剪辑 - 无视觉裁剪] 剪辑后的英文字幕内容应为字符串");
        assertDefined(result.clippedEnglishSrtFilePath, "[成功剪辑 - 无视觉裁剪] 剪辑后的英文字幕文件路径应已定义");
        assert(fs.existsSync(result.clippedEnglishSrtFilePath), `[成功剪辑 - 无视觉裁剪] 剪辑后的英文字幕文件应存在于: ${result.clippedEnglishSrtFilePath}`);
        
        assertEqual(result.visualCropApplied, false, "[成功剪辑 - 无视觉裁剪] visualCropApplied 应为 false");
        
        logger.info(`${testLogPrefix} [测试用例 1：成功剪辑 - 无视觉裁剪] 所有断言通过。`);
        
        assert(progressUpdates1.length > 3, "[成功剪辑 - 无视觉裁剪] 应至少有几条进度更新");
        assertEqual(progressUpdates1[0].status, TASK_STATUS.STARTED, "[成功剪辑 - 无视觉裁剪] 第一条进度应为 STARTED");
        assertDefined(progressUpdates1.find(p => p.status === TASK_STATUS.COMPLETED), "[成功剪辑 - 无视觉裁剪] 应包含 COMPLETED 状态的进度更新");

    } catch (error) {
        logger.error(`${testLogPrefix} [测试用例 1：成功剪辑 - 无视觉裁剪][ERROR] ClipMediaTask 执行失败: ${error.message}`);
        if (task && task.status) logger.error(`${testLogPrefix} [测试用例 1][ERROR] 任务状态: ${task.status}`);
        if (task && task.error) logger.error(`${testLogPrefix} [测试用例 1][ERROR] 任务错误详情: ${task.error.message}`);
        throw error; 
    }

    // --- 测试用例 2: startTime 大于 endTime ---
    logger.info(`${testLogPrefix} [测试用例 2：无效时间戳 startTime > endTime] 开始测试。`);
    const invalidTimeContext = {
        ...baseInitialContext,
        startTime: 10,
        endTime: 5,
        reqId: 'test-invalid-time-req-' + Date.now()
    };
    task = new ClipMediaTask();
    try {
        await task.execute(invalidTimeContext, () => {});
        throw new Error("ClipMediaTask 未因 startTime > endTime 而抛出预期错误");
    } catch (error) {
        logger.info(`${testLogPrefix} [测试用例 2：无效时间戳 startTime > endTime] 捕获到预期错误: ${error.message}`);
        assertMatch(error.message, /endTime .* 必须大于 startTime/i, "错误消息应匹配 endTime 大于 startTime 的校验");
        assertEqual(task.status, TASK_STATUS.FAILED, "任务状态应为 FAILED");
    }
    
    // --- 测试用例 3: startTime 大于视频总时长 ---
    logger.info(`${testLogPrefix} [测试用例 3：startTime 超出范围] 开始测试。`);
    const invalidStartTimeContext = {
        ...baseInitialContext,
        startTime: baseInitialContext.extractedRawJsonData.duration + 10,
        endTime: baseInitialContext.extractedRawJsonData.duration + 20,
        reqId: 'test-invalid-startTime-req-' + Date.now()
    };
    task = new ClipMediaTask();
    try {
        await task.execute(invalidStartTimeContext, () => {});
        throw new Error("ClipMediaTask 未因 startTime > duration 而抛出预期错误");
    } catch (error) {
        logger.info(`${testLogPrefix} [测试用例 3：startTime 超出范围] 捕获到预期错误: ${error.message}`);
        assertMatch(error.message, /startTime .* 不能大于或等于视频总时长/i, "错误消息应匹配 startTime 超出范围的校验");
        assertEqual(task.status, TASK_STATUS.FAILED, "任务状态应为 FAILED");
    }

    // --- 测试用例 4: endTime 大于视频总时长 (应自动修正并成功) ---
    logger.info(`${testLogPrefix} [测试用例 4：endTime 超出范围 - 自动修正] 开始测试。`);
    const autoCorrectEndTimeContext = {
        ...baseInitialContext,
        startTime: 0,
        endTime: baseInitialContext.extractedRawJsonData.duration + 20,
        reqId: 'test-auto-correct-endTime-req-' + Date.now()
    };
    task = new ClipMediaTask();
    try {
        const result = await task.execute(autoCorrectEndTimeContext, () => {});
        logger.info(`${testLogPrefix} [测试用例 4：endTime 超出范围 - 自动修正] ClipMediaTask 执行完毕。`);
        assertEqual(task.status, TASK_STATUS.COMPLETED, "[自动修正endTime] 任务状态应为 COMPLETED");
        assertDefined(result.clippedVideoPath, "[自动修正endTime] 剪辑后的视频路径应已定义");
        assert(fs.existsSync(result.clippedVideoPath), `[自动修正endTime] 剪辑后的视频文件应存在于: ${result.clippedVideoPath}`);
        assertDefined(result.clippedChineseSrtFilePath, "[自动修正endTime] 剪辑后的中文字幕文件路径应已定义");
        assert(fs.existsSync(result.clippedChineseSrtFilePath), `[自动修正endTime] 剪辑后的中文字幕文件应存在于: ${result.clippedChineseSrtFilePath}`);
        assertEqual(result.visualCropApplied, false, "[自动修正endTime] visualCropApplied 应为 false");
        assert(result.clippedOriginalVideoName && !result.clippedOriginalVideoName.includes('_cropped'), "[自动修正endTime] 文件名不应包含 '_cropped'");
        logger.info(`${testLogPrefix} [测试用例 4：endTime 超出范围 - 自动修正] 测试通过，任务成功完成并修正了endTime。`);
    } catch (error) {
        logger.error(`${testLogPrefix} [测试用例 4：endTime 超出范围 - 自动修正][ERROR] 测试失败: ${error.message}`);
        if (task && task.status) logger.error(`${testLogPrefix} [测试用例 4][ERROR] 任务状态: ${task.status}`);
        throw error; 
    }

    // --- 测试用例 5: extractedRawJsonData.duration 无效 ---
    logger.info(`${testLogPrefix} [测试用例 5：无效duration] 开始测试。`);
    const invalidDurationContext = {
        ...baseInitialContext,
        startTime: 0,
        endTime: 10,
        extractedRawJsonData: { ...baseInitialContext.extractedRawJsonData, duration: -5 },
        reqId: 'test-invalid-duration-req-' + Date.now()
    };
    task = new ClipMediaTask();
    try {
        await task.execute(invalidDurationContext, () => {});
        throw new Error("ClipMediaTask 未因无效的 extractedRawJsonData.duration 而抛出预期错误");
    } catch (error) {
        logger.info(`${testLogPrefix} [测试用例 5：无效duration] 捕获到预期错误: ${error.message}`);
        assertMatch(
            error.message,
            /原始视频时长 \(extractedRawJsonData.duration\) 无效或缺失。接收到的值: .*/i,
            "错误消息应匹配无效 duration 的校验，并包含接收到的值"
        );
        assertEqual(task.status, TASK_STATUS.FAILED, "任务状态应为 FAILED");
    }

    // --- 测试用例 6: 缺少必需字段 (uploadedVideoPath) ---
    logger.info(`${testLogPrefix} [测试用例 6：缺少uploadedVideoPath] 开始测试。`);
    const missingPathContext = { ...baseInitialContext, uploadedVideoPath: undefined, reqId: 'test-missing-path-req-' + Date.now(), startTime:0, endTime:10};
    delete missingPathContext.uploadedVideoPath;
    task = new ClipMediaTask();
    try {
        await task.execute(missingPathContext, () => {});
        throw new Error("ClipMediaTask 未因缺少 uploadedVideoPath 而抛出预期错误");
    } catch (error) {
        logger.info(`${testLogPrefix} [测试用例 6：缺少uploadedVideoPath] 捕获到预期错误: ${error.message}`);
        assertMatch(error.message, /上下文缺少必需字段 uploadedVideoPath/i, "错误消息应匹配缺少 uploadedVideoPath 的校验");
        assertEqual(task.status, TASK_STATUS.FAILED, "任务状态应为 FAILED");
    }

    // --- 测试用例 7: 成功进行视觉裁剪 ---
    logger.info(`${testLogPrefix} [测试用例 7：合规视觉裁剪] 开始测试。`);
    const validCropParams = {
        cropWidth: Math.floor(originalVideoWidth * 0.8), // 裁剪为原始的80%宽度
        cropHeight: Math.floor(originalVideoHeight * 0.8), // 裁剪为原始的80%高度
        cropXOffset: Math.floor(originalVideoWidth * 0.1), // X偏移10%
        cropYOffset: Math.floor(originalVideoHeight * 0.1), // Y偏移10%
    };
    // 确保裁剪尺寸是偶数，某些编解码器可能要求
    if (validCropParams.cropWidth % 2 !== 0) validCropParams.cropWidth -=1;
    if (validCropParams.cropHeight % 2 !== 0) validCropParams.cropHeight -=1;

    const visualCropContext = {
        ...baseInitialContext,
        startTime: 5, // 使用一个较短的时间段
        endTime: 25,
        reqId: 'test-visual-crop-req-' + Date.now(),
        ...validCropParams
    };
    task = new ClipMediaTask();
    try {
        const result = await task.execute(visualCropContext, () => {});
        logger.info(`${testLogPrefix} [测试用例 7：合规视觉裁剪] ClipMediaTask 执行完毕。`);
        assertEqual(task.status, TASK_STATUS.COMPLETED, "[合规视觉裁剪] 任务状态应为 COMPLETED");
        assertDefined(result.clippedVideoPath, "[合规视觉裁剪] 剪辑后的视频路径应已定义");
        assert(fs.existsSync(result.clippedVideoPath), `[合规视觉裁剪] 剪辑后的视频文件应存在于: ${result.clippedVideoPath}`);
        assert(result.clippedOriginalVideoName.includes('_cropped'), "[合规视觉裁剪] 剪辑后的文件名应包含 '_cropped'");
        assertEqual(result.visualCropApplied, true, "[合规视觉裁剪] visualCropApplied 应为 true");
        logger.info(`${testLogPrefix} [测试用例 7：合规视觉裁剪] 测试通过。输出文件: ${result.clippedVideoPath}`);
        // 可选: 使用 getVideoDimensions 验证裁剪后视频的尺寸
        // const croppedDimensions = await getVideoDimensions(result.clippedVideoPath);
        // assertEqual(croppedDimensions.width, validCropParams.cropWidth, "[合规视觉裁剪] 裁剪后视频宽度应正确");
        // assertEqual(croppedDimensions.height, validCropParams.cropHeight, "[合规视觉裁剪] 裁剪后视频高度应正确");

    } catch (error) {
        logger.error(`${testLogPrefix} [测试用例 7：合规视觉裁剪][ERROR] 测试失败: ${error.message}`);
        if (task && task.status) logger.error(`${testLogPrefix} [测试用例 7][ERROR] 任务状态: ${task.status}`);
        throw error;
    }

    // --- 测试用例 8: 不合规视觉裁剪 (X偏移+宽度 超出原始宽度) ---
    logger.info(`${testLogPrefix} [测试用例 8：不合规视觉裁剪 - X溢出] 开始测试。`);
    const invalidCropXContext = {
        ...baseInitialContext,
        startTime: 0,
        endTime: 10,
        reqId: 'test-invalid-crop-x-req-' + Date.now(),
        cropWidth: Math.floor(originalVideoWidth * 0.6),
        cropHeight: Math.floor(originalVideoHeight * 0.6),
        cropXOffset: Math.floor(originalVideoWidth * 0.5) + 1, // X偏移(50%)+宽度(60%) > 100%
        cropYOffset: 0,
    };
    task = new ClipMediaTask();
    try {
        await task.execute(invalidCropXContext, () => {});
        throw new Error("ClipMediaTask 未因不合规的视觉裁剪参数 (X溢出) 而抛出预期错误");
    } catch (error) {
        logger.info(`${testLogPrefix} [测试用例 8：不合规视觉裁剪 - X溢出] 捕获到预期错误: ${error.message}`);
        assertMatch(error.message, /视觉裁剪X偏移 .* \+ 裁剪宽度 .* 超出原始视频宽度/i, "错误消息应匹配X溢出校验");
        assertEqual(task.status, TASK_STATUS.FAILED, "任务状态应为 FAILED");
    }
    
    // --- 测试用例 9: 不合规视觉裁剪 (cropWidth 为 0) ---
    logger.info(`${testLogPrefix} [测试用例 9：不合规视觉裁剪 - cropWidth为0] 开始测试。`);
    const invalidCropWidthContext = {
        ...baseInitialContext,
        startTime: 0,
        endTime: 10,
        reqId: 'test-invalid-crop-width-req-' + Date.now(),
        cropWidth: 0, 
        cropHeight: Math.floor(originalVideoHeight / 2),
        cropXOffset: 0,
        cropYOffset: 0,
    };
    task = new ClipMediaTask();
    try {
        const result = await task.execute(invalidCropWidthContext, () => {});
        logger.info(`${testLogPrefix} [测试用例 9：不合规视觉裁剪 - cropWidth为0] ClipMediaTask 执行完毕。`);
        assertEqual(task.status, TASK_STATUS.COMPLETED, "[cropWidth为0] 任务状态应为 COMPLETED (应跳过裁剪)");
        assertEqual(result.visualCropApplied, false, "[cropWidth为0] visualCropApplied 应为 false，因为裁剪参数无效");
        assert(result.clippedOriginalVideoName && !result.clippedOriginalVideoName.includes('_cropped'), "[cropWidth为0] 文件名不应包含 '_cropped'");
        logger.info(`${testLogPrefix} [测试用例 9：不合规视觉裁剪 - cropWidth为0] 测试通过，任务跳过了无效的视觉裁剪。`);
    } catch (error) {
         logger.error(`${testLogPrefix} [测试用例 9：不合规视觉裁剪 - cropWidth为0][ERROR] 测试失败: ${error.message}`);
        if (task && task.status) logger.error(`${testLogPrefix} [测试用例 9][ERROR] 任务状态: ${task.status}`);
        throw error;
    }

    // --- 测试用例 10: 部分视觉裁剪参数 (缺少 cropXOffset) ---
    logger.info(`${testLogPrefix} [测试用例 10：部分视觉裁剪参数] 开始测试。`);
    const partialCropParamsContext = {
        ...baseInitialContext,
        startTime: 0,
        endTime: 10,
        reqId: 'test-partial-crop-params-req-' + Date.now(),
        cropWidth: Math.floor(originalVideoWidth / 2),
        cropHeight: Math.floor(originalVideoHeight / 2),
        // cropXOffset: undefined, // 故意缺失
        cropYOffset: 0,
    };
    task = new ClipMediaTask();
    try {
        const result = await task.execute(partialCropParamsContext, () => {});
        logger.info(`${testLogPrefix} [测试用例 10：部分视觉裁剪参数] ClipMediaTask 执行完毕。`);
        assertEqual(task.status, TASK_STATUS.COMPLETED, "[部分裁剪参数] 任务状态应为 COMPLETED (应跳过裁剪)");
        assertEqual(result.visualCropApplied, false, "[部分裁剪参数] visualCropApplied 应为 false");
        assert(result.clippedOriginalVideoName && !result.clippedOriginalVideoName.includes('_cropped'), "[部分裁剪参数] 文件名不应包含 '_cropped'");
        logger.info(`${testLogPrefix} [测试用例 10：部分视觉裁剪参数] 测试通过，任务跳过了视觉裁剪。`);
    } catch (error) {
        logger.error(`${testLogPrefix} [测试用例 10：部分视觉裁剪参数][ERROR] 测试失败: ${error.message}`);
        if (task && task.status) logger.error(`${testLogPrefix} [测试用例 10][ERROR] 任务状态: ${task.status}`);
        throw error;
    }
}

// 主执行逻辑
(async () => {
    try {
        await runClipMediaTaskTests();
        logger.info(`${testLogPrefix} \n✅ ✅ ✅ 所有测试用例通过! ✅ ✅ ✅`);
        process.exit(0);
    } catch (error) {
        logger.error(`${testLogPrefix} \n❌ ❌ ❌ 测试执行过程中发生错误或断言失败: ${error.message} ❌ ❌ ❌`);
        if (error.stack) {
            logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        }
        process.exit(1);
    }
})(); 