# &lt;Video&gt;

## 概述

`<Video>` 组件包装了原生的 [`<video>`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLVideoElement) 元素，用于在组件中包含与 Remotion 时间同步的视频。

**推荐**: 优先使用 [`<OffthreadVideo>`](./OffthreadVideo.md)，它在渲染时更快且支持更多编解码器。

## 语法

```typescript
import { Video, staticFile } from "remotion";

<Video src={staticFile("video.webm")} />
```

## 基础用法

### 1. 本地视频文件

```typescript
import { AbsoluteFill, staticFile, Video } from "remotion";

export const MyComposition = () => {
  return (
    <AbsoluteFill>
      <Video src={staticFile("video.webm")} />
    </AbsoluteFill>
  );
};
```

### 2. 远程视频URL

```typescript
export const MyComposition = () => {
  return (
    <AbsoluteFill>
      <Video src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" />
    </AbsoluteFill>
  );
};
```

## 核心属性

### src
- **类型**: `string`
- **描述**: 要渲染的视频URL，可以是远程URL或使用 `staticFile()` 引用的本地文件

### trimBefore (v4.0.319+)
- **类型**: `number`
- **描述**: 移除视频开始部分的帧数

```typescript
// 假设组合的fps为30
// trimBefore={60} 移除前2秒，trimAfter={120} 移除4秒后的内容
// 视频将播放从00:02:00到00:04:00的范围，总共2秒
<Video 
  src={staticFile("video.webm")} 
  trimBefore={60} 
  trimAfter={120} 
/>
```

### trimAfter (v4.0.319+)
- **类型**: `number`
- **描述**: 移除视频结束部分的帧数

### volume
- **类型**: `number | ((frame: number) => number)`
- **描述**: 控制整个轨道的音量或基于帧的音量变化

```typescript
// 静态音量
<Video volume={0.5} src={staticFile("video.webm")} />

// 动态音量 - 100帧内从0渐变到1
<Video 
  volume={(f) => interpolate(f, [0, 100], [0, 1], {extrapolateLeft: 'clamp'})} 
  src={staticFile("video.webm")} 
/>
```

### playbackRate (v2.2.0+)
- **类型**: `number`
- **默认值**: `1`
- **描述**: 控制视频播放速度

```typescript
// 2倍速播放
<Video playbackRate={2} src={staticFile("video.webm")} />

// 0.5倍速播放（慢动作）
<Video playbackRate={0.5} src={staticFile("video.webm")} />
```

### muted
- **类型**: `boolean`
- **描述**: 静音视频，提高渲染效率

```typescript
<Video 
  muted 
  src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" 
/>
```

### loop (v3.2.29+)
- **类型**: `boolean`
- **描述**: 使视频无限循环

```typescript
<Video 
  loop 
  src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" 
/>
```

## 实际应用场景

### 1. 背景视频

```typescript
import { AbsoluteFill, Video, staticFile } from "remotion";

const BackgroundVideo = () => {
  return (
    <AbsoluteFill>
      <Video 
        src={staticFile("background.mp4")}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover"
        }}
        muted // 背景视频通常静音
        loop // 循环播放
      />
    </AbsoluteFill>
  );
};
```

### 2. 视频剪辑和拼接

```typescript
import { Sequence, Video, staticFile } from "remotion";

const VideoMontage = () => {
  return (
    <>
      {/* 第一个片段: 0-120帧 */}
      <Sequence from={0} durationInFrames={120}>
        <Video 
          src={staticFile("clip1.mp4")}
          trimBefore={30} // 跳过前1秒
          trimAfter={150} // 只播放5秒
        />
      </Sequence>
      
      {/* 第二个片段: 120-240帧 */}
      <Sequence from={120} durationInFrames={120}>
        <Video 
          src={staticFile("clip2.mp4")}
          playbackRate={1.5} // 1.5倍速播放
        />
      </Sequence>
      
      {/* 第三个片段: 240-360帧 */}
      <Sequence from={240} durationInFrames={120}>
        <Video 
          src={staticFile("clip3.mp4")}
          volume={0.3} // 降低音量
        />
      </Sequence>
    </>
  );
};
```

### 3. 动态音量控制

```typescript
import { Video, staticFile, useCurrentFrame, interpolate } from "remotion";

const DynamicVolumeVideo = () => {
  const frame = useCurrentFrame();
  
  // 淡入淡出效果
  const volume = interpolate(
    frame,
    [0, 30, 270, 300], // 前1秒淡入，后1秒淡出
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <Video 
      src={staticFile("narration.mp4")}
      volume={volume}
    />
  );
};
```

### 4. 响应式视频布局

```typescript
import { Video, staticFile, useVideoConfig } from "remotion";

const ResponsiveVideo = () => {
  const { width, height } = useVideoConfig();
  const isVertical = height > width;

  return (
    <Video 
      src={staticFile(isVertical ? "vertical-video.mp4" : "horizontal-video.mp4")}
      style={{
        width: isVertical ? "100%" : "80%",
        height: isVertical ? "60%" : "100%",
        objectFit: "cover"
      }}
    />
  );
};
```

### 5. 多视频画中画

```typescript
import { AbsoluteFill, Video, staticFile } from "remotion";

const PictureInPicture = () => {
  return (
    <AbsoluteFill>
      {/* 主视频 */}
      <Video 
        src={staticFile("main-video.mp4")}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover"
        }}
      />
      
      {/* 画中画视频 */}
      <Video 
        src={staticFile("pip-video.mp4")}
        style={{
          position: "absolute",
          top: 20,
          right: 20,
          width: 300,
          height: 200,
          border: "3px solid white",
          borderRadius: 8
        }}
        volume={0.3} // 降低画中画音量
      />
    </AbsoluteFill>
  );
};
```

### 6. 视频转场效果

```typescript
import { Video, staticFile, useCurrentFrame, interpolate, AbsoluteFill } from "remotion";

const VideoTransition = () => {
  const frame = useCurrentFrame();
  
  // 第一个视频的透明度
  const video1Opacity = interpolate(frame, [0, 150, 180], [1, 1, 0], {
    extrapolateRight: "clamp"
  });
  
  // 第二个视频的透明度
  const video2Opacity = interpolate(frame, [150, 180, 330], [0, 1, 1], {
    extrapolateLeft: "clamp"
  });

  return (
    <AbsoluteFill>
      <Video 
        src={staticFile("video1.mp4")}
        style={{ opacity: video1Opacity }}
      />
      <Video 
        src={staticFile("video2.mp4")}
        style={{ opacity: video2Opacity }}
      />
    </AbsoluteFill>
  );
};
```

### 7. 音频处理和音调调整

```typescript
import { Video, staticFile } from "remotion";

const AudioProcessedVideo = () => {
  return (
    <>
      {/* 原始音调 */}
      <Video 
        src={staticFile("speech.mp4")}
        toneFrequency={1} // 原始音调
      />
      
      {/* 降低音调 */}
      <Video 
        src={staticFile("music.mp4")}
        toneFrequency={0.8} // 降低20%音调
        volume={0.5}
      />
      
      {/* 提高音调 */}
      <Video 
        src={staticFile("effect.mp4")}
        toneFrequency={1.2} // 提高20%音调
        volume={0.3}
      />
    </>
  );
};
```

## 高级配置

### 1. 错误处理

```typescript
import { Video, staticFile } from "remotion";

const ErrorHandledVideo = () => {
  const handleVideoError = (error: Event) => {
    console.error("视频加载失败:", error);
    // 可以设置备用视频或显示错误信息
  };

  return (
    <Video 
      src={staticFile("video.mp4")}
      onError={handleVideoError}
      style={{ width: "100%", height: "100%" }}
    />
  );
};
```

### 2. 自动播放限制处理

```typescript
import { Video, staticFile } from "remotion";

const AutoplayHandledVideo = () => {
  const handleAutoplayError = () => {
    console.log("自动播放被阻止，视频将静音播放");
    // 可以显示用户交互提示
  };

  return (
    <Video 
      src={staticFile("video.mp4")}
      onAutoPlayError={handleAutoplayError}
    />
  );
};
```

### 3. 缓冲状态处理

```typescript
import { Video, staticFile } from "remotion";

const BufferingVideo = () => {
  return (
    <Video 
      src={staticFile("large-video.mp4")}
      pauseWhenBuffering={true} // 缓冲时暂停播放器
      acceptableTimeShiftInSeconds={0.2} // 自定义同步阈值
    />
  );
};
```

### 4. 跨域配置

```typescript
import { Video } from "remotion";

const CrossOriginVideo = () => {
  return (
    <Video 
      src="https://example.com/video.mp4"
      crossOrigin="anonymous" // 或 "use-credentials"
    />
  );
};
```

### 5. Web Audio API 集成

```typescript
import { Video, staticFile } from "remotion";

const WebAudioVideo = () => {
  return (
    <Video 
      src={staticFile("audio-rich-video.mp4")}
      useWebAudioApi={true} // 启用Web Audio API
      volume={1.5} // 可以超过1的音量
    />
  );
};
```

## 性能优化

### 1. 静音优化

```typescript
// 对于只需要视觉效果的视频，添加muted属性
<Video 
  src={staticFile("visual-only.mp4")}
  muted // 避免下载音频轨道，提高渲染效率
/>
```

### 2. 延迟渲染配置

```typescript
<Video 
  src={staticFile("large-video.mp4")}
  delayRenderTimeoutInMilliseconds={10000} // 10秒超时
  delayRenderRetries={2} // 重试2次
/>
```

### 3. 时间轴优化

```typescript
<Video 
  src={staticFile("background-video.mp4")}
  showInTimeline={false} // 在时间轴中隐藏，减少UI复杂度
  name="背景视频" // 添加描述性名称
/>
```

## 与其他组件结合

### 1. 与 Sequence 组合

```typescript
import { Sequence, Video, staticFile } from "remotion";

const SequencedVideos = () => {
  return (
    <>
      <Sequence from={0} durationInFrames={120}>
        <Video src={staticFile("intro.mp4")} />
      </Sequence>
      
      <Sequence from={120} durationInFrames={240}>
        <Video src={staticFile("main.mp4")} />
      </Sequence>
      
      <Sequence from={360} durationInFrames={60}>
        <Video src={staticFile("outro.mp4")} />
      </Sequence>
    </>
  );
};
```

### 2. 与 Audio 组合

```typescript
import { Video, Audio, staticFile } from "remotion";

const VideoWithSeparateAudio = () => {
  return (
    <>
      <Video 
        src={staticFile("video.mp4")}
        muted // 视频静音
      />
      <Audio 
        src={staticFile("soundtrack.mp3")}
        volume={0.8}
      />
    </>
  );
};
```

## 编解码器支持

Remotion 支持的视频格式：
- **H.264** (推荐用于兼容性)
- **H.265/HEVC** (更好的压缩)
- **VP8/VP9** (WebM格式)
- **AV1** (最新标准)

详见：[Remotion支持的视频格式](/docs/miscellaneous/video-formats)

## 相关 API

- [`<OffthreadVideo>`](./OffthreadVideo.md) - 更快的视频组件
- [`<Audio>`](./Audio.md) - 音频组件
- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`staticFile()`](./staticFile.md) - 静态文件引用
- [`interpolate()`](./interpolate.md) - 值插值

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/video/Video.tsx)
