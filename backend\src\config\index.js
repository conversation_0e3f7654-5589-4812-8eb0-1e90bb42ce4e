const dotenv = require('dotenv');
const path = require('path');
const logger = require('../utils/logger'); // 导入logger工具模块

/**
 * @功能概述: 加载环境变量配置并返回。
 * @returns {object} 包含应用程序配置的对象。
 */
const loadConfig = () => {
    const logPrefix = '[文件：index.js][配置加载器][loadConfig]';
    logger.info(`${logPrefix} 开始加载环境变量配置。`);

    // 加载 .env 文件中的环境变量
    logger.info(`${logPrefix} [步骤 1] 尝试加载 .env 文件...`); // 使用 logger.info
    const dotenvResult = dotenv.config({ path: path.resolve(__dirname, '../../.env') });

    // 记录 dotenv.config() 的加载结果
    if (dotenvResult.error) {
        logger.error(`${logPrefix} [步骤 2] dotenv.config() 加载 .env 文件失败: ${dotenvResult.error}`); // 使用 logger.error
    } else if (dotenvResult.parsed) {
        // 记录成功加载的环境变量，但不打印敏感值
        const loadedVars = Object.keys(dotenvResult.parsed);
        logger.info(`${logPrefix} [步骤 2] dotenv.config() 成功加载以下环境变量: ${loadedVars.join(', ')}`); // 使用 logger.info
        // 可以选择记录具体加载的值（非敏感的）或只确认加载成功
        // logger.debug(`${logPrefix} 加载的变量值: ${JSON.stringify(dotenvResult.parsed)}`); // 注意：包含敏感信息！
    } else {
        logger.warn(`${logPrefix} [步骤 2] dotenv.config() 未加载任何环境变量 (文件可能不存在或为空)。`); // 使用 logger.warn
    }

    // 读取 Azure Speech Service、Cloudflare Workers AI 和 Dify 的配置
    const config = {
        // Azure Speech Service 配置
        speechEndpoint: process.env.SPEECH_ENDPOINT,
        speechKey: process.env.SPEECH_KEY,
        apiVersion: process.env.API_VERSION || '2024-11-15',
        defaultLocale: process.env.DEFAULT_LOCALE || 'en-US',
        profanityFilter: process.env.PROFANITY_FILTER || 'Masked',
        requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 300000,

        // Cloudflare Workers AI 配置
        cloudflareApiToken: process.env.CLOUDFLARE_API_TOKEN,
        cloudflareAccountId: process.env.CLOUDFLARE_ACCOUNT_ID,
        cloudflareWhisperModel: process.env.CLOUDFLARE_WHISPER_MODEL || '@cf/openai/whisper',
        cloudflareRequestTimeout: parseInt(process.env.CLOUDFLARE_REQUEST_TIMEOUT) || 300000,
        useCloudflareAi: process.env.USE_CLOUDFLARE_AI === 'true',

        // FFmpeg 路径配置 - 从环境变量或 .env 读取
        ffmpegPath: process.env.FFMPEG_PATH,
        ffprobePath: process.env.FFPROBE_PATH,

        // Dify 配置保持不变
        difyApiEndpoint: process.env.DIFY_API_ENDPOINT,
        difyApiKey: process.env.DIFY_API_KEY,

        // OpenRouter 配置
        openrouter: {
            apiKey: process.env.OPENROUTER_API_KEY,
            chatCompletionsUrl: process.env.OPENROUTER_CHAT_COMPLETIONS_URL || 'https://openrouter.ai/api/v1/chat/completions',
        },

        llm: {

            

            // === 🔧 LLM模型配置 ===

            //默认模型、温度、最大token
            defaultModel: 'google/gemini-2.5-flash-preview-05-20',
            defaultTemperature: 0.3,      // 默认温度参数
            defaultMaxTokens: 20000,      // 默认最大token限制


            // === 📊 智能Token分配策略配置 ===
            // 根据内容长度动态调整token分配的策略参数
            translation: {
                // Token分配算法的倍数系数（基于实际测试数据优化）
                // 📈 数据基础：3,469字符英文 → 1,715-1,729 tokens中文输出
                tokenRatios: {
                    short: 0.8,    // 短字幕（<2K字符）：较保守的token分配
                    medium: 1.0,   // 中等字幕（2K-5K字符）：标准token分配
                    long: 1.2,     // 长字幕（5K-10K字符）：增加token预留
                    extraLong: 1.5 // 超长字幕（>10K字符）：最大token预留
                },
                
                // 最小token保证（确保即使很短的内容也有足够的处理空间）
                minTokens: {
                    short: 4000,    // 短字幕最少4K tokens
                    medium: 8000,   // 中等字幕最少8K tokens  
                    long: 16000,    // 长字幕最少16K tokens
                    extraLong: 24000 // 超长字幕最少24K tokens
                },
                
                // 长度分界点（字符数）
                lengthThresholds: {
                    shortToMedium: 2000,    // 短->中等：2K字符
                    mediumToLong: 5000,     // 中等->长：5K字符
                    longToExtraLong: 10000  // 长->超长：10K字符
                }
            }
        },

        // 基础上传目录配置 - 优先从环境变量读取，始终相对于backend目录
        baseUploadDir: process.env.UPLOAD_DIR ?
            path.resolve(path.join(__dirname, '../..', process.env.UPLOAD_DIR)) :
            path.join(__dirname, '../../uploads'), // 默认值：从 backend/src/config 向上两级到 backend 目录，再拼接 uploads

        // 项目目录结构配置
        projectDirs: {
            projects: 'projects',      // 项目主目录名
            source: 'source',          // 原始文件目录名
            processed: 'processed',    // 处理文件目录名
            generated: 'generated',    // 生成文件目录名
            temp: 'temp'              // 临时文件目录名
        },

        // 向后兼容：保留原有的uploadDir配置
        get uploadDir() {
            return this.baseUploadDir;
        },
        timeouts: {
            server: 300000,    // 5分钟
            sseHeartbeat: 15000 // 15秒
        }
    };

    // 验证必要的环境变量是否存在 (Azure Speech Service)
    if (!config.speechEndpoint || !config.speechKey) {
        const errorMsg = `${logPrefix} 错误：Azure Speech Service 配置 (speechEndpoint 或 speechKey) 缺失。请检查 .env 文件。`;
        logger.error(errorMsg); // 使用 logger.error
        // 抛出错误，因为 Azure Speech Service 是核心功能
        throw new Error(errorMsg);
    }

    // Dify 配置验证（警告级别）
    if (!config.difyApiEndpoint || !config.difyApiKey) {
        logger.warn(`${logPrefix} 警告：Dify 配置 (API 端点或密钥) 缺失。如果不需要Dify功能可忽略此警告。`); // 使用 logger.warn
    }

    // OpenRouter 配置验证与日志记录
    if (!config.openrouter.apiKey) {
        logger.warn(`${logPrefix} [OpenRouter配置][警告] OpenRouter API Key 未在环境变量中配置 (OPENROUTER_API_KEY)。LLM 翻译功能将无法使用。`);
    } else {
        logger.info(`${logPrefix} [OpenRouter配置] OpenRouter API Key 已加载。`); // 确认已加载，不打印密钥本身
    }
    if (!process.env.OPENROUTER_CHAT_COMPLETIONS_URL) {
        logger.info(`${logPrefix} [OpenRouter配置] OpenRouter Chat Completions URL 未在环境变量中配置 (OPENROUTER_CHAT_COMPLETIONS_URL)，将使用默认值: ${config.openrouter.chatCompletionsUrl}`);
    } else {
        logger.info(`${logPrefix} [OpenRouter配置] OpenRouter Chat Completions URL 已配置为: ${config.openrouter.chatCompletionsUrl}`);
    }

    // LLM 默认模型配置日志
    if (!process.env.DEFAULT_CORRECTION_MODEL) {
        logger.info(`${logPrefix} [LLM配置] 默认校正模型未在环境变量 (DEFAULT_CORRECTION_MODEL) 中配置，将使用备选默认值: '${config.llm.defaultCorrectionModel}'`);
    } else {
        logger.info(`${logPrefix} [LLM配置] 默认校正模型已通过环境变量配置为: '${config.llm.defaultCorrectionModel}'`);
    }

    if (!process.env.DEFAULT_TRANSLATION_MODEL) {
        logger.info(`${logPrefix} [LLM配置] 默认翻译模型未在环境变量 (DEFAULT_TRANSLATION_MODEL) 中配置，将使用备选默认值: '${config.llm.defaultTranslationModel}'`);
    } else {
        logger.info(`${logPrefix} [LLM配置] 默认翻译模型已通过环境变量配置为: '${config.llm.defaultTranslationModel}'`);
    }

    // 检查 FFmpeg 路径是否已配置
    if (!config.ffmpegPath || !config.ffprobePath) {
        logger.warn(`${logPrefix}警告：FFmpeg 或 FFprobe 路径未在环境变量或 .env 中配置。`);
        // fluent-ffmpeg 在路径未设置时会尝试自动查找，但通常不可靠
    }

    logger.info(`${logPrefix} 环境变量配置加载完成。`);
    // 注意：为了安全，日志中不要打印敏感配置值

    return config;
};



// 是的，这样修改可以确保配置在整个应用程序生命周期中只被加载一次，实现单例模式。
// 当 `backend/src/config/index.js` 模块首次被任何其他模块（例如 `videoProcessingPipelineService.js` 或任何 Task 类）通过 `require` 导入时，
// `loadConfig()` 函数会立即执行，并将其返回的配置对象缓存起来。
// 之后，无论多少次 `require('../config')`，Node.js 都会直接返回这个已缓存的配置对象，而不会再次执行 `loadConfig()` 函数。
// 因此，`videoProcessingPipelineService.js` 及其内部调用的所有 Task（如 `GetTranscriptionTask.js`, `ConvertToAudioTask.js`, `TranslateSubtitleTask.js`, `TranscriptionCorrectionTask.js`）
// 都会共享同一个已加载的配置实例，避免了重复加载配置。

// 在 Node.js 中，`module.exports` 是一个特殊的对象，用于定义一个模块对外暴露的接口。
// 每个 .js 文件在 Node.js 中都被视为一个独立的模块。
// 这行代码 `module.exports = loadConfig;` 的意思是：
// 将当前模块（`backend/src/config/index.js`）中的 `loadConfig` 函数指定为该模块的导出值。
// 当其他模块（例如 `app.js`）使用 `require('./config')` 或 `require('../config')` 来导入此模块时，
// `require` 函数的返回值将会是 `loadConfig` 这个函数本身，而不是函数的执行结果。
//
// 换句话说，其他模块在导入后，如果想获取配置对象，需要显式调用这个导入得到的函数：
// 例如:
// const getConfigFunction = require('./config'); // getConfigFunction 现在就是 loadConfig 函数
// const actualConfigObject = getConfigFunction(); // 调用函数以获取配置对象
//
// 这种模式允许配置的加载是按需的，或者在特定时机执行。
// 修正：将其修改为立即执行函数，确保配置在模块首次加载时就被初始化并缓存，实现单例模式。
module.exports = loadConfig(); // 立即执行 loadConfig 函数并导出其结果
