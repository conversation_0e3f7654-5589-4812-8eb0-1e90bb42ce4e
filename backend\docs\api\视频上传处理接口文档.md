# 视频上传处理接口文档

## 📋 接口概述

**接口名称**: 视频上传处理接口
**接口描述**: 上传视频文件并自动执行视频转音频、语音转录、字幕优化的完整处理流程
**架构版本**: 新项目目录架构 (2025-07-16)
**开发状态**: ✅ 已完成并测试通过
**最后更新**: 2025-07-16

---

## 🏗️ 新架构特性

### 项目目录结构
```
backend/uploads/projects/{videoIdentifier}/
├── source/              # 原始上传文件
│   └── original.mp4     # 用户上传的原始视频
├── processed/           # 处理产出文件
│   ├── audio.mp3        # 提取的音频文件
│   ├── transcription.json # 转录数据文件
│   ├── english.srt      # 英文字幕文件
│   └── optimized.json   # 优化数据文件
└── generated/           # 生成视频文件
    └── video_v1.mp4     # 生成的短视频
```

### URL访问结构
- **新标准化URL**: `/api/files/projects/{videoId}/{category}/{filename}`
- **向后兼容URL**: `/backend/uploads/...` (继续有效)

---

## 🌐 基本信息

| 项目 | 内容 |
|------|------|
| **请求方法** | `POST` |
| **请求URL** | `http://localhost:8081/api/video/uploadVideo` |
| **请求格式** | `multipart/form-data` |
| **响应格式** | `Server-Sent Events (SSE)` 事件流 |
| **超时时间** | 300秒 (5分钟) |
| **文件大小限制** | 500MB |

---

## 📤 请求参数

### 必需参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `videoFile` | File | ✅ | 视频文件 | `video.mp4` |

### 支持的视频格式

- **推荐格式**: MP4, MOV, AVI
- **支持格式**: MP4, AVI, MOV, MKV, WEBM, FLV, M4V
- **编码要求**: 无特殊要求，系统自动处理

### 文件要求

- **文件大小**: 最大 500MB
- **时长限制**: 建议 30分钟以内 (超长视频处理时间较长)
- **分辨率**: 无限制，系统只提取音频进行处理

---

## 📥 响应格式

### SSE事件流

接口采用 **Server-Sent Events (SSE)** 实时推送处理进度，客户端需要监听以下事件：

#### 1. 连接建立事件
```javascript
event: connection
data: {"type":"connection","connectionId":"video-upload-xxx","message":"SSE连接已建立"}
```

#### 2. 请求接受确认事件
```javascript
event: controller_status
data: {
  "type": "accepted",
  "message": "视频 'example.mp4' 已接收，准备进入处理流水线",
  "videoIdentifier": "videoFile-1752635194070",
  "fileInfo": {
    "originalName": "example.mp4",
    "size": 15728640,
    "mimetype": "video/mp4"
  }
}
```

#### 3. 流水线进度事件
```javascript
event: pipeline_progress
data: {
  "pipelineName": "VideoProcessing-req123",
  "pipelineStatus": "running",
  "currentTaskName": "ConvertToAudioTask",
  "currentTaskStatus": "running",
  "currentTaskDetail": "正在转换视频为音频...",
  "progress": {
    "current": 30,
    "total": 100
  }
}
```

#### 4. 最终完成事件
```javascript
event: pipeline_complete
data: {
  "message": "视频处理流水线已完成",
  "result": {
    "status": "completed",
    "context": {
      "videoIdentifier": "videoFile-1752635194070",
      "videoPlaybackUrl": "http://localhost:8081/api/files/projects/videoFile-1752635194070/source/original.mp4",
      "audioFileUrl": "http://localhost:8081/api/files/projects/videoFile-1752635194070/processed/audio.mp3",
      "transcriptionJsonUrl": "http://localhost:8081/api/files/projects/videoFile-1752635194070/processed/transcription.json",
      "englishSrtUrl": "http://localhost:8081/api/files/projects/videoFile-1752635194070/processed/english.srt",
      "chineseSrtUrl": "http://localhost:8081/api/files/projects/videoFile-1752635194070/processed/chinese.srt"
    }
  }
}
```

#### 5. 心跳事件
```javascript
event: heartbeat
data: {"connectionId":"video-upload-xxx","uptime":15000}
```

#### 6. 错误事件
```javascript
event: controller_status
data: {
  "type": "controller_error",
  "errorDetails": {
    "message": "服务器内部发生严重错误",
    "name": "Error"
  }
}
```

---

## 🔄 处理流程

### 新架构处理流程

#### 步骤1: 文件接收和目录创建
- 接收用户上传的视频文件
- 创建项目目录结构 (`source/`, `processed/`, `generated/`)
- 移动原始文件到 `source/original.{ext}`

#### 步骤2: 流水线任务序列

1. **ConvertToAudioForCloudflareTask** - Cloudflare专用视频转音频
   - 从 `source/original.{ext}` 提取音频
   - 转换为极度压缩的 MP3 格式 (16kHz, 单声道, 32kbps)
   - 专门针对Cloudflare Workers AI优化，文件大小<2MB
   - 保存到 `processed/{videoId}_audio.mp3`

2. **GetTranscriptionTask** - 语音转录
   - 调用语音识别API (支持Azure/OpenAI/Cloudflare)
   - 生成带时间戳的转录文本
   - 保存到 `processed/{videoId}_transcription.json`

3. **SubtitleOptimizationTask** - 字幕优化
   - 合并过短的字幕片段
   - 拆分过长的句子
   - 保存到 `processed/{videoId}_english.srt`

#### 步骤3: URL生成和返回
- 使用PathHelper生成标准化文件访问URL
- 返回新架构的文件链接结构

### 容错机制

- **转录API失败**: 自动降级处理，生成基础时间轴字幕
- **网络超时**: 智能重试机制，最多重试5次
- **文件损坏**: 详细错误提示，指导用户重新上传
- **目录创建失败**: 自动重试和错误恢复

---

## 📁 输出文件

### 新架构文件组织

处理成功后，系统会在项目目录下生成以下文件：

#### source/ 目录
| 文件类型 | 文件名 | 描述 | URL示例 |
|----------|--------|------|---------|
| 原始视频 | `original.{ext}` | 用户上传的原始视频 | `/api/files/projects/{videoId}/source/original.mp4` |

#### processed/ 目录
| 文件类型 | 文件名格式 | 描述 | URL字段 |
|----------|------------|------|---------|
| 音频文件 | `{videoIdentifier}_audio.mp3` | 提取的音频文件 | `audioFileUrl` |
| 转录JSON | `{videoIdentifier}_transcription.json` | 原始转录数据 | `transcriptionJsonUrl` |
| 优化字幕JSON | `{videoIdentifier}_optimized_subtitle.json` | 优化后的字幕数据 | - |
| 简化字幕JSON | `{videoIdentifier}_corrected.json` | 校正后的字幕数据 | - |
| 英文SRT | `{videoIdentifier}_english_subtitle.srt` | 英文字幕文件 | `englishSrtUrl` |
| 中文SRT | `{videoIdentifier}_chinese_subtitle.srt` | 中文字幕文件 | `chineseSrtUrl` |

#### generated/ 目录
| 文件类型 | 文件名格式 | 描述 | 用途 |
|----------|------------|------|------|
| 生成视频 | `video_v{n}.mp4` | 生成的短视频 | 由生成接口产出 |
| ASS字幕 | `subtitles.ass` | ASS格式字幕 | 由生成接口产出 |

---

## 💻 前端调用示例

### JavaScript (原生)

```javascript
// 创建FormData
const formData = new FormData();
formData.append('videoFile', fileInput.files[0]);

// 建立SSE连接
const eventSource = new EventSource('http://localhost:3000/api/video/uploadVideo');

// 监听事件
eventSource.addEventListener('controller_status', (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'accepted') {
        console.log('文件已接收:', data.videoIdentifier);
    }
});

eventSource.addEventListener('pipeline_progress', (event) => {
    const data = JSON.parse(event.data);
    console.log(`进度: ${data.currentTaskName} - ${data.currentTaskDetail}`);
});

eventSource.addEventListener('pipeline_complete', (event) => {
    const data = JSON.parse(event.data);
    console.log('处理完成:', data.result.context);
    eventSource.close();
});

// 发送请求
fetch('http://localhost:8081/api/video/uploadVideo', {
    method: 'POST',
    body: formData
});
```

### Vue.js 示例

```javascript
// 在Vue组件中
async uploadVideo(file) {
    const formData = new FormData();
    formData.append('videoFile', file);
    
    // 建立SSE连接
    this.eventSource = new EventSource('/api/video/uploadVideo');
    
    this.eventSource.addEventListener('pipeline_complete', (event) => {
        const result = JSON.parse(event.data);
        this.videoResult = result.result.context;
        this.eventSource.close();
    });
    
    // 发送上传请求
    await fetch('/api/video/uploadVideo', {
        method: 'POST',
        body: formData
    });
}
```

---

## ⚠️ 错误处理

### 常见错误码

| 错误类型 | 描述 | 解决方案 |
|----------|------|----------|
| `FILE_MISSING` | 未找到上传的视频文件 | 确保表单字段名为 `videoFile` |
| `FILE_TOO_LARGE` | 文件超过500MB限制 | 压缩视频或分段上传 |
| `UNSUPPORTED_FORMAT` | 不支持的视频格式 | 转换为MP4/MOV/AVI格式 |
| `TRANSCRIPTION_FAILED` | 转录服务失败 | 系统自动降级，生成基础字幕 |
| `PIPELINE_TIMEOUT` | 处理超时 | 检查网络连接，重新上传 |

### 错误事件示例

```javascript
{
  "type": "controller_error",
  "errorDetails": {
    "message": "未找到上传的视频文件",
    "code": "FILE_MISSING"
  }
}
```

---

## 🔧 技术特性

### 新架构特性
- **项目目录管理**: 每个视频独立的项目目录结构
- **文件分类存储**: source/processed/generated三级分类
- **标准化URL**: 统一的文件访问路径格式
- **PathHelper工具**: 智能路径管理和URL生成

### 核心功能特性
- **实时进度推送**: SSE事件流实时反馈处理状态
- **智能容错**: 转录失败时自动降级处理
- **多API支持**: 支持Azure、OpenAI、Cloudflare语音识别
- **Cloudflare优化**: 使用ConvertToAudioForCloudflareTask，文件大小减少75%
- **文件管理**: 自动生成唯一文件名，避免冲突
- **路径转换**: 自动将服务器路径转换为Web访问URL
- **心跳保活**: 防止长时间处理时连接断开

### Cloudflare Workers AI 优化
- **极度压缩**: 32kbps比特率 (vs 标准128kbps)
- **文件大小**: 减少约75%，通常<2MB
- **质量保证**: 16kHz采样率确保语音识别质量
- **处理速度**: 更快的上传和API处理
- **成本优化**: 减少带宽和存储成本

### 向后兼容
- **传统URL**: 继续支持 `/backend/uploads/...` 访问方式
- **API兼容**: 现有前端代码无需修改即可使用

---

## 📊 性能指标

- **平均处理时间**: 1-3分钟 (取决于视频长度)
- **并发支持**: 支持多用户同时上传
- **成功率**: >95% (包含降级处理)
- **文件压缩率**: 音频文件约为原视频的10-20%

---

## 🔍 调试信息

### 日志查看
```bash
# 查看实时日志
tail -f backend/logs/app.log

# 过滤上传相关日志
grep "uploadVideoController" backend/logs/app.log
```

### 常用调试命令
```bash
# 检查上传目录权限
ls -la backend/uploads/

# 查看生成的文件
ls -la backend/uploads/output/
```

---

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 视频文件信息 (格式、大小、时长)
2. 错误事件内容
3. 浏览器控制台日志
4. 服务器日志 (如可访问)

**联系方式**: 请通过项目Issue或内部技术群联系开发团队
