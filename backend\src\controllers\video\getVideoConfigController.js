/**
 * @功能概述: 获取视频配置参数 - 专门处理前端获取视频配置的请求
 * @职责范围: 
 *   - 读取后端video-config.json配置文件
 *   - 提取前端需要的配置项（repeatCount, backgroundStyle, subtitleConfig等）
 *   - 返回标准化的前端配置格式
 * 
 * @API接口: GET /api/video/getVideoConfig
 * @请求格式: 无需参数
 * @响应格式: JSON
 * 
 * @架构设计: 单一职责原则 - 只处理视频配置获取相关逻辑
 * @创建时间: 2025-08-10
 */

// === 导入依赖模块 ===
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs');

/**
 * @文件位置: backend/src/controllers/video/getVideoConfigController.js
 * @依赖文件:
 *   - backend/src/utils/logger.js - 日志工具
 *   - backend/src/config/video/video-config.json - 视频配置文件
 */

// 模块级日志前缀
const moduleLogPrefix = '[文件：getVideoConfigController.js][获取视频配置][模块初始化]';
logger.info(`${moduleLogPrefix} 模块已加载。`);

/**
 * @功能概述: 获取视频配置参数的主控制器函数
 * @参数说明: 
 *   - req: Express请求对象
 *   - res: Express响应对象
 * @返回值: JSON响应，包含前端需要的视频配置
 * @错误处理: 统一错误响应格式
 * @执行流程:
 *   1. 读取video-config.json文件
 *   2. 提取前端需要的配置项
 *   3. 转换为前端兼容的格式
 *   4. 返回标准化响应
 */
const getVideoConfig = async (req, res) => {
    const reqId = req.id || Date.now().toString();
    const logPrefix = `[文件：getVideoConfigController.js][获取视频配置][getVideoConfig][ReqID:${reqId}]`;
    
    try {
        logger.info(`${logPrefix} ========== 开始获取视频配置 ==========`);
        
        // 步骤1: 读取video-config.json文件
        logger.info(`${logPrefix}[步骤 1] 开始读取视频配置文件`);
        const videoConfigPath = path.join(__dirname, '../../config/video/video-config.json');
        logger.info(`${logPrefix}[步骤 1] 配置文件路径: ${videoConfigPath}`);
        
        let rawConfig;
        try {
            const configFileContent = fs.readFileSync(videoConfigPath, 'utf8');
            rawConfig = JSON.parse(configFileContent);
            logger.info(`${logPrefix}[步骤 1] 成功读取配置文件，内容长度: ${configFileContent.length} 字符`);
            logger.debug(`${logPrefix}[步骤 1] 原始配置内容: ${JSON.stringify(rawConfig, null, 2)}`);
        } catch (error) {
            logger.error(`${logPrefix}[步骤 1] 读取配置文件失败: ${error.message}`);
            return res.status(500).json({
                success: false,
                error: '配置文件读取失败',
                message: error.message,
                reqId: reqId
            });
        }
        
        // 步骤2: 提取前端需要的配置项
        logger.info(`${logPrefix}[步骤 2] 开始提取前端需要的配置项`);
        
        // 提取基础配置
        const frontendConfig = {
            repeatCount: rawConfig.repeatCount || 3,
            backgroundStyle: rawConfig.backgroundStyle || 'newspaper'
        };
        
        logger.info(`${logPrefix}[步骤 2] 基础配置提取完成:`);
        logger.info(`${logPrefix}  - repeatCount: ${frontendConfig.repeatCount}`);
        logger.info(`${logPrefix}  - backgroundStyle: ${frontendConfig.backgroundStyle}`);
        
        // 步骤3: 处理repeatModes（从subtitleConfig中提取到根级别）
        logger.info(`${logPrefix}[步骤 3] 开始处理repeatModes配置`);
        if (rawConfig.subtitleConfig && rawConfig.subtitleConfig.repeatModes) {
            frontendConfig.repeatModes = rawConfig.subtitleConfig.repeatModes;
            logger.info(`${logPrefix}[步骤 3] 成功提取repeatModes，数量: ${frontendConfig.repeatModes.length}`);
            logger.debug(`${logPrefix}[步骤 3] repeatModes详情: ${JSON.stringify(frontendConfig.repeatModes, null, 2)}`);
        } else {
            // 如果没有配置，使用默认值
            frontendConfig.repeatModes = [
                { name: "blindListen", displayText: "第一遍 盲听" },
                { name: "clozedSubtitle", displayText: "第二遍 单词填空" },
                { name: "bilingualSubtitle", displayText: "第三遍 中英翻译" }
            ];
            logger.warn(`${logPrefix}[步骤 3] 未找到repeatModes配置，使用默认值`);
        }
        
        // 步骤4: 处理subtitleConfig
        logger.info(`${logPrefix}[步骤 4] 开始处理subtitleConfig配置`);
        frontendConfig.subtitleConfig = {};
        
        // 处理videoGuide配置
        if (rawConfig.subtitleConfig && rawConfig.subtitleConfig.videoGuide) {
            const vg = rawConfig.subtitleConfig.videoGuide;
            frontendConfig.subtitleConfig.videoGuide = {
                enabled: vg.enabled !== false,
                title1: vg.title1 || "坚持30天",
                title2: vg.title2 || "听懂国外新闻"
            };
            logger.info(`${logPrefix}[步骤 4] videoGuide配置提取完成:`);
            logger.info(`${logPrefix}  - enabled: ${frontendConfig.subtitleConfig.videoGuide.enabled}`);
            logger.info(`${logPrefix}  - title1: "${frontendConfig.subtitleConfig.videoGuide.title1}"`);
            logger.info(`${logPrefix}  - title2: "${frontendConfig.subtitleConfig.videoGuide.title2}"`);
        } else {
            // 默认videoGuide配置
            frontendConfig.subtitleConfig.videoGuide = {
                enabled: true,
                title1: "坚持30天",
                title2: "听懂国外新闻"
            };
            logger.warn(`${logPrefix}[步骤 4] 未找到videoGuide配置，使用默认值`);
        }
        
        // 处理advertisement配置
        if (rawConfig.subtitleConfig && rawConfig.subtitleConfig.advertisement) {
            const ad = rawConfig.subtitleConfig.advertisement;
            frontendConfig.subtitleConfig.advertisement = {
                enabled: ad.enabled !== false,
                titles: ad.titles || []
            };
            
            // 确保titles格式正确
            if (!Array.isArray(frontendConfig.subtitleConfig.advertisement.titles) || 
                frontendConfig.subtitleConfig.advertisement.titles.length === 0) {
                frontendConfig.subtitleConfig.advertisement.titles = [
                    {
                        line1: "🎯关注水蜜桃英语",
                        line2: "每天2分钟，听力打卡！"
                    }
                ];
                logger.warn(`${logPrefix}[步骤 4] advertisement.titles为空，使用默认值`);
            }
            
            logger.info(`${logPrefix}[步骤 4] advertisement配置提取完成:`);
            logger.info(`${logPrefix}  - enabled: ${frontendConfig.subtitleConfig.advertisement.enabled}`);
            logger.info(`${logPrefix}  - titles数量: ${frontendConfig.subtitleConfig.advertisement.titles.length}`);
            
            // 记录第一个标题作为示例
            if (frontendConfig.subtitleConfig.advertisement.titles.length > 0) {
                const firstTitle = frontendConfig.subtitleConfig.advertisement.titles[0];
                logger.info(`${logPrefix}  - titles[0].line1: "${firstTitle.line1}"`);
                logger.info(`${logPrefix}  - titles[0].line2: "${firstTitle.line2}"`);
            }
        } else {
            // 默认advertisement配置
            frontendConfig.subtitleConfig.advertisement = {
                enabled: true,
                titles: [
                    {
                        line1: "🎯关注水蜜桃英语",
                        line2: "每天2分钟，听力打卡！"
                    }
                ]
            };
            logger.warn(`${logPrefix}[步骤 4] 未找到advertisement配置，使用默认值`);
        }
        
        // 步骤5: 验证配置完整性
        logger.info(`${logPrefix}[步骤 5] 开始验证配置完整性`);
        const configValidation = {
            hasRepeatCount: typeof frontendConfig.repeatCount === 'number',
            hasBackgroundStyle: typeof frontendConfig.backgroundStyle === 'string',
            hasRepeatModes: Array.isArray(frontendConfig.repeatModes) && frontendConfig.repeatModes.length > 0,
            hasVideoGuide: !!frontendConfig.subtitleConfig.videoGuide,
            hasAdvertisement: !!frontendConfig.subtitleConfig.advertisement,
            repeatCountMatchesModesLength: frontendConfig.repeatCount === frontendConfig.repeatModes.length
        };
        
        logger.info(`${logPrefix}[步骤 5] 配置验证结果: ${JSON.stringify(configValidation, null, 2)}`);
        
        const isValid = Object.values(configValidation).every(Boolean);
        if (!isValid) {
            logger.warn(`${logPrefix}[步骤 5] 配置验证发现问题，但继续返回`);
        } else {
            logger.info(`${logPrefix}[步骤 5] 配置验证通过`);
        }
        
        // 步骤6: 返回响应
        logger.info(`${logPrefix}[步骤 6] 准备返回配置响应`);
        logger.debug(`${logPrefix}[步骤 6] 最终前端配置: ${JSON.stringify(frontendConfig, null, 2)}`);
        
        const response = {
            success: true,
            data: frontendConfig,
            metadata: {
                source: 'backend/src/config/video/video-config.json',
                extractedAt: new Date().toISOString(),
                validation: configValidation,
                reqId: reqId
            }
        };
        
        logger.info(`${logPrefix}[步骤 6] 成功返回视频配置，配置项数量: ${Object.keys(frontendConfig).length}`);
        logger.info(`${logPrefix} ========== 获取视频配置完成 ==========`);
        
        res.json(response);
        
    } catch (error) {
        logger.error(`${logPrefix} 获取视频配置时发生未预期错误: ${error.message}`);
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            success: false,
            error: '获取视频配置失败',
            message: error.message,
            reqId: reqId
        });
    }
};

module.exports = getVideoConfig;
logger.info(`${moduleLogPrefix} getVideoConfig 控制器已导出。`);
