/**
 * @功能概述: 视频生成控制器 - 专门处理视频生成请求
 * @职责范围:
 *   - 接收视频生成参数（多片段、裁剪参数、视频配置等）
 *   - 通过标准化SSE推送生成进度
 *   - 调用视频生成流水线执行片段提取和裁剪
 *   - 返回生成结果和处理详情
 *
 * @API接口: POST /api/video/generateVideo
 * @请求格式: JSON (生成参数)
 * @响应格式: SSE事件流
 *
 * @架构设计: 单一职责原则 - 只处理视频生成相关逻辑
 * @创建时间: 2025-06-12
 * @重构说明: 从videoController.js中拆分出来，提高可维护性
 * @更新时间: 2025-06-13 - 支持新的参数格式（多片段、视频配置）
 */

// 导入日志工具
const logger = require('../../utils/logger');

// 导入标准化SSE基础设施
const {
    SSE_EVENT_TYPES,
    CONTROLLER_STATUS,
    PIPELINE_STATUS,
    createSSEEventData,
    createControllerStatusSSE,
    createHeartbeatSSE,
    SSEConnectionManager
} = require('../../constants/progress');

// 导入视频生成流水线服务
const VideoGenerationPipelineService = require('../../pipelines/videoGenerationPipelineService');

// 导入配置
const config = require('../../config');
const fs = require('fs');
const path = require('path');

// 导入路径管理工具
const PathHelper = require('../../utils/pathHelper');

// 模块级日志前缀
const moduleLogPrefix = '[文件：generateVideoController.js][视频生成控制器][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用生成控制器，遵循单一职责原则`);

/**
 * @功能概述: 处理视频生成请求的核心方法
 * @参数说明:
 *   - req: Express请求对象，包含生成参数(req.body)
 *   - res: Express响应对象，用于SSE事件推送
 *
 * @请求参数验证:
 *   - videoIdentifier: 视频标识符（字符串，非空）
 *   - clipSegments: 片段数组（数组，包含startTime和endTime）
 *   - cropData: 裁剪参数对象（包含cropWidth、cropHeight、cropXOffset、cropYOffset）
 *   - videoConfig: 视频配置对象（包含repeatCount和repeatModes）
 *
 * @执行流程:
 *   1. 初始化SSE连接和心跳机制
 *   2. 验证生成参数的完整性和有效性
 *   3. 发送请求接受确认事件
 *   4. 暂时返回参数给前端（用于调试）
 *   5. 发送最终结果事件并清理连接
 *
 * @错误处理:
 *   - 参数无效：发送预检失败事件
 *   - 流水线异常：发送控制器错误事件
 *   - 连接异常：自动清理资源
 *
 * @SSE事件类型:
 *   - ACCEPTED: 请求接受确认
 *   - PIPELINE_PROGRESS: 流水线进度更新
 *   - CONTROLLER_ERROR: 控制器级别错误
 *   - HEARTBEAT: 连接保活心跳
 */
const generateVideo = async (req, res) => {
    // === 步骤1: 初始化请求上下文和日志 ===
    const reqId = req.id || 'unknown_generate_req';
    const connectionId = `video-generate-${reqId}-${Date.now()}`;
    const logPrefix = `[文件：generateVideoController.js][generateVideo][ReqID:${reqId}][ConnID:${connectionId}] `;

    logger.info(`${logPrefix}[步骤 1] 开始处理视频生成请求 (标准化SSE)。`);

    // === 步骤2: 初始化SSE连接管理器和心跳机制 ===
    let sseManager = null;
    let heartbeatInterval = null;

    try {
        // 创建SSE连接管理器实例
        sseManager = new SSEConnectionManager(res, connectionId);

        // 初始化SSE连接（设置响应头并发送连接确认事件）
        sseManager.initialize();
        logger.info(`${logPrefix}[步骤 2.1] SSE连接管理器已初始化。连接ID: ${connectionId}`);

        // 启动心跳机制（每15秒发送一次）
        heartbeatInterval = setInterval(() => {
            if (sseManager && sseManager.isActive) {
                const heartbeat = createHeartbeatSSE({
                    connectionId: connectionId,
                    uptime: Date.now() - sseManager.startTime
                });
                sseManager.sendEvent(heartbeat);
            } else {
                clearInterval(heartbeatInterval);
            }
        }, 15000);

        // 处理客户端主动断开连接的情况
        let clientDisconnected = false;
        req.on('close', () => {
            clientDisconnected = true;
            logger.warn(`${logPrefix}[SSE_CLOSE] 客户端连接已关闭。ReqID: ${reqId}`);
            logger.warn(`${logPrefix}[SSE_CLOSE] 连接关闭时SSE管理器状态: ${sseManager ? (sseManager.isActive ? 'active' : 'inactive') : 'null'}`);

            // 不要立即关闭SSE连接，让流水线完成后再处理
            // 只标记客户端已断开，避免后续发送事件
            if (sseManager && sseManager.isActive) {
                logger.warn(`${logPrefix}[SSE_CLOSE] 客户端断开但保持SSE管理器活跃，等待流水线完成`);
            }
        });

        // === 步骤3: 提取和验证生成参数 ===
        const {
            videoIdentifier,
            clipSegments,
            cropData,
            videoConfig,
            correctedFullText,
            originalVideoPath,
            englishSrtContent,
            chineseSrtContent
        } = req.body;

        logger.info(`${logPrefix}[步骤 3] 接收到生成参数:`);
        logger.info(`${logPrefix}  - 视频标识符: ${videoIdentifier}`);
        logger.info(`${logPrefix}  - 片段数量: ${clipSegments ? clipSegments.length : 0}`);
        logger.info(`${logPrefix}  - 裁剪参数: ${JSON.stringify(cropData)}`);
        logger.info(`${logPrefix}  - 原始视频路径: ${originalVideoPath}`);
        logger.info(`${logPrefix}  - 校正文本长度: ${correctedFullText ? correctedFullText.length : 0}`);
        logger.info(`${logPrefix}  - 英文字幕长度: ${englishSrtContent ? englishSrtContent.length : 0}`);
        logger.info(`${logPrefix}  - 中文字幕长度: ${chineseSrtContent ? chineseSrtContent.length : 0}`);

        // === 🔍 最高细粒度videoConfig日志追踪 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.1] ========== 前端传入videoConfig原始数据分析 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig是否存在: ${!!videoConfig}`);
        logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig类型: ${typeof videoConfig}`);
        logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig完整结构: ${JSON.stringify(videoConfig, null, 2)}`);

        if (videoConfig) {
            logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig根级别键: [${Object.keys(videoConfig).join(', ')}]`);
            logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.repeatCount: ${videoConfig.repeatCount} (类型: ${typeof videoConfig.repeatCount})`);
            logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.backgroundStyle: "${videoConfig.backgroundStyle}" (类型: ${typeof videoConfig.backgroundStyle})`);
            logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.repeatModes存在: ${!!videoConfig.repeatModes}`);
            if (videoConfig.repeatModes) {
                logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.repeatModes长度: ${videoConfig.repeatModes.length}`);
                logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.repeatModes详情: ${JSON.stringify(videoConfig.repeatModes, null, 2)}`);
            }

            logger.info(`${logPrefix}[🔍 TRACE] 前端videoConfig.subtitleConfig存在: ${!!videoConfig.subtitleConfig}`);
            if (videoConfig.subtitleConfig) {
                logger.info(`${logPrefix}[🔍 TRACE] 前端subtitleConfig键: [${Object.keys(videoConfig.subtitleConfig).join(', ')}]`);

                // videoGuide详细分析
                logger.info(`${logPrefix}[🔍 TRACE] 前端subtitleConfig.videoGuide存在: ${!!videoConfig.subtitleConfig.videoGuide}`);
                if (videoConfig.subtitleConfig.videoGuide) {
                    const vg = videoConfig.subtitleConfig.videoGuide;
                    logger.info(`${logPrefix}[🔍 TRACE] 前端videoGuide.enabled: ${vg.enabled} (类型: ${typeof vg.enabled})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 前端videoGuide.title1: "${vg.title1}" (类型: ${typeof vg.title1}, 长度: ${vg.title1?.length || 0})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 前端videoGuide.title2: "${vg.title2}" (类型: ${typeof vg.title2}, 长度: ${vg.title2?.length || 0})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 前端videoGuide完整对象: ${JSON.stringify(vg, null, 2)}`);
                }

                // advertisement详细分析
                logger.info(`${logPrefix}[🔍 TRACE] 前端subtitleConfig.advertisement存在: ${!!videoConfig.subtitleConfig.advertisement}`);
                if (videoConfig.subtitleConfig.advertisement) {
                    const ad = videoConfig.subtitleConfig.advertisement;
                    logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.enabled: ${ad.enabled} (类型: ${typeof ad.enabled})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles存在: ${!!ad.titles}`);
                    if (ad.titles) {
                        logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles类型: ${Array.isArray(ad.titles) ? 'Array' : typeof ad.titles}`);
                        logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles长度: ${ad.titles.length}`);
                        ad.titles.forEach((title, index) => {
                            logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles[${index}]: ${JSON.stringify(title, null, 2)}`);
                            logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles[${index}].line1: "${title.line1}" (长度: ${title.line1?.length || 0})`);
                            logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles[${index}].line2: "${title.line2}" (长度: ${title.line2?.length || 0})`);
                        });
                    }
                    logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement完整对象: ${JSON.stringify(ad, null, 2)}`);
                }
            }
        } else {
            logger.warn(`${logPrefix}[🔍 TRACE] ⚠️ 前端未传递videoConfig或为null/undefined`);
        }
        logger.info(`${logPrefix}[🔍 TRACE] ========== 前端传入videoConfig分析完成 ==========`);

        // === 步骤3.5: 合并videoConfig配置 ===
        logger.info(`${logPrefix}[步骤 3.5] 开始合并videoConfig配置`);

        // 读取默认配置文件
        const videoConfigPath = path.join(__dirname, '../../config/video/video-config.json');
        let defaultVideoConfig = {};
        try {
            const configFileContent = fs.readFileSync(videoConfigPath, 'utf8');
            defaultVideoConfig = JSON.parse(configFileContent);
            logger.info(`${logPrefix}[步骤 3.5] 成功读取默认video-config.json`);

            // === 🔍 默认配置详细分析 ===
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.5.1] ========== 默认videoConfig详细分析 ==========`);
            logger.info(`${logPrefix}[🔍 TRACE] 默认配置文件路径: ${videoConfigPath}`);
            logger.info(`${logPrefix}[🔍 TRACE] 默认配置文件内容长度: ${configFileContent.length} 字符`);
            logger.info(`${logPrefix}[🔍 TRACE] 默认配置根级别键: [${Object.keys(defaultVideoConfig).join(', ')}]`);
            logger.info(`${logPrefix}[🔍 TRACE] 默认配置完整结构: ${JSON.stringify(defaultVideoConfig, null, 2)}`);

            logger.info(`${logPrefix}[🔍 TRACE] 默认配置.repeatCount: ${defaultVideoConfig.repeatCount} (类型: ${typeof defaultVideoConfig.repeatCount})`);
            logger.info(`${logPrefix}[🔍 TRACE] 默认配置.backgroundStyle: "${defaultVideoConfig.backgroundStyle}" (类型: ${typeof defaultVideoConfig.backgroundStyle})`);

            logger.info(`${logPrefix}[🔍 TRACE] 默认配置.subtitleConfig存在: ${!!defaultVideoConfig.subtitleConfig}`);
            if (defaultVideoConfig.subtitleConfig) {
                const sc = defaultVideoConfig.subtitleConfig;
                logger.info(`${logPrefix}[🔍 TRACE] 默认subtitleConfig键: [${Object.keys(sc).join(', ')}]`);

                // 默认videoGuide分析
                logger.info(`${logPrefix}[🔍 TRACE] 默认subtitleConfig.videoGuide存在: ${!!sc.videoGuide}`);
                if (sc.videoGuide) {
                    logger.info(`${logPrefix}[🔍 TRACE] 默认videoGuide.enabled: ${sc.videoGuide.enabled} (类型: ${typeof sc.videoGuide.enabled})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 默认videoGuide.title1: "${sc.videoGuide.title1}" (类型: ${typeof sc.videoGuide.title1}, 长度: ${sc.videoGuide.title1?.length || 0})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 默认videoGuide.title2: "${sc.videoGuide.title2}" (类型: ${typeof sc.videoGuide.title2}, 长度: ${sc.videoGuide.title2?.length || 0})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 默认videoGuide完整对象: ${JSON.stringify(sc.videoGuide, null, 2)}`);
                }

                // 默认advertisement分析
                logger.info(`${logPrefix}[🔍 TRACE] 默认subtitleConfig.advertisement存在: ${!!sc.advertisement}`);
                if (sc.advertisement) {
                    logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement.enabled: ${sc.advertisement.enabled} (类型: ${typeof sc.advertisement.enabled})`);
                    logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement.titles存在: ${!!sc.advertisement.titles}`);
                    if (sc.advertisement.titles) {
                        logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement.titles类型: ${Array.isArray(sc.advertisement.titles) ? 'Array' : typeof sc.advertisement.titles}`);
                        logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement.titles长度: ${sc.advertisement.titles.length}`);
                        sc.advertisement.titles.forEach((title, index) => {
                            logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement.titles[${index}]: ${JSON.stringify(title, null, 2)}`);
                        });
                    }
                    logger.info(`${logPrefix}[🔍 TRACE] 默认advertisement完整对象: ${JSON.stringify(sc.advertisement, null, 2)}`);
                }

                // 默认repeatModes分析
                logger.info(`${logPrefix}[🔍 TRACE] 默认subtitleConfig.repeatModes存在: ${!!sc.repeatModes}`);
                if (sc.repeatModes) {
                    logger.info(`${logPrefix}[🔍 TRACE] 默认repeatModes长度: ${sc.repeatModes.length}`);
                    logger.info(`${logPrefix}[🔍 TRACE] 默认repeatModes详情: ${JSON.stringify(sc.repeatModes, null, 2)}`);
                }
            }
            logger.info(`${logPrefix}[🔍 TRACE] ========== 默认videoConfig分析完成 ==========`);

        } catch (error) {
            logger.error(`${logPrefix}[步骤 3.5] 读取video-config.json失败: ${error.message}`);
            logger.error(`${logPrefix}[🔍 TRACE] 配置文件读取错误详情: ${error.stack}`);
            // 如果读取失败，使用空对象作为默认配置
            defaultVideoConfig = {};
            logger.warn(`${logPrefix}[🔍 TRACE] 使用空对象作为默认配置: ${JSON.stringify(defaultVideoConfig)}`);
        }

        // 合并配置：前端传递的videoConfig覆盖默认配置中的同名字段
        // 确保videoConfig不为undefined
        const safeVideoConfig = videoConfig || {};

        // 深度合并函数 - 完整版本
        const deepMerge = (target, source) => {
            // 如果源对象为null或undefined，返回目标对象
            if (source === null || source === undefined) {
                return target;
            }
            
            // 如果目标对象为null或undefined，返回源对象的深拷贝
            if (target === null || target === undefined) {
                return typeof source === 'object' && !Array.isArray(source) ? 
                    deepMerge({}, source) : source;
            }
            
            const result = { ...target };
            
            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    const sourceValue = source[key];
                    const targetValue = target[key];
                    
                    // 如果源值是对象且不是数组，进行深度合并
                    if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
                        result[key] = deepMerge(targetValue || {}, sourceValue);
                    } else {
                        // 否则直接覆盖（包括数组、基本类型、null、undefined）
                        result[key] = sourceValue;
                    }
                }
            }
            
            return result;
        };

        logger.info(`${logPrefix}[步骤 3.5] 开始深度合并配置`);

        // === 🔍 深度合并前的对比分析 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.5.2] ========== 深度合并前对比分析 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并前 - 默认配置键: [${Object.keys(defaultVideoConfig).join(', ')}]`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并前 - 前端配置键: [${Object.keys(safeVideoConfig).join(', ')}]`);

        // 对比关键字段
        logger.info(`${logPrefix}[🔍 TRACE] 对比repeatCount - 默认: ${defaultVideoConfig.repeatCount}, 前端: ${safeVideoConfig.repeatCount}`);
        logger.info(`${logPrefix}[🔍 TRACE] 对比backgroundStyle - 默认: "${defaultVideoConfig.backgroundStyle}", 前端: "${safeVideoConfig.backgroundStyle}"`);

        // 对比subtitleConfig
        const defaultSC = defaultVideoConfig.subtitleConfig;
        const frontendSC = safeVideoConfig.subtitleConfig;
        logger.info(`${logPrefix}[🔍 TRACE] 对比subtitleConfig存在性 - 默认: ${!!defaultSC}, 前端: ${!!frontendSC}`);

        if (defaultSC && frontendSC) {
            // 对比videoGuide
            logger.info(`${logPrefix}[🔍 TRACE] 对比videoGuide存在性 - 默认: ${!!defaultSC.videoGuide}, 前端: ${!!frontendSC.videoGuide}`);
            if (defaultSC.videoGuide && frontendSC.videoGuide) {
                logger.info(`${logPrefix}[🔍 TRACE] 对比videoGuide.title1 - 默认: "${defaultSC.videoGuide.title1}", 前端: "${frontendSC.videoGuide.title1}"`);
                logger.info(`${logPrefix}[🔍 TRACE] 对比videoGuide.title2 - 默认: "${defaultSC.videoGuide.title2}", 前端: "${frontendSC.videoGuide.title2}"`);
                logger.info(`${logPrefix}[🔍 TRACE] 对比videoGuide.enabled - 默认: ${defaultSC.videoGuide.enabled}, 前端: ${frontendSC.videoGuide.enabled}`);
            }

            // 对比advertisement
            logger.info(`${logPrefix}[🔍 TRACE] 对比advertisement存在性 - 默认: ${!!defaultSC.advertisement}, 前端: ${!!frontendSC.advertisement}`);
            if (defaultSC.advertisement && frontendSC.advertisement) {
                logger.info(`${logPrefix}[🔍 TRACE] 对比advertisement.enabled - 默认: ${defaultSC.advertisement.enabled}, 前端: ${frontendSC.advertisement.enabled}`);
                logger.info(`${logPrefix}[🔍 TRACE] 对比advertisement.titles长度 - 默认: ${defaultSC.advertisement.titles?.length || 0}, 前端: ${frontendSC.advertisement.titles?.length || 0}`);
                if (frontendSC.advertisement.titles && frontendSC.advertisement.titles.length > 0) {
                    logger.info(`${logPrefix}[🔍 TRACE] 前端advertisement.titles[0] - line1: "${frontendSC.advertisement.titles[0].line1}", line2: "${frontendSC.advertisement.titles[0].line2}"`);
                }
            }
        }

        logger.info(`${logPrefix}[🔍 TRACE] 执行深度合并: deepMerge(defaultVideoConfig, safeVideoConfig)`);
        const mergedVideoConfig = deepMerge(defaultVideoConfig, safeVideoConfig);
        logger.info(`${logPrefix}[🔍 TRACE] 深度合并完成`);

        // === 🔍 深度合并后的结果分析 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.5.3] ========== 深度合并后结果分析 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并后配置根级别键: [${Object.keys(mergedVideoConfig).join(', ')}]`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并后完整配置: ${JSON.stringify(mergedVideoConfig, null, 2)}`);

        logger.info(`${logPrefix}[🔍 TRACE] 合并后.repeatCount: ${mergedVideoConfig.repeatCount} (来源: ${mergedVideoConfig.repeatCount === safeVideoConfig.repeatCount ? '前端' : '默认'})`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并后.backgroundStyle: "${mergedVideoConfig.backgroundStyle}" (来源: ${mergedVideoConfig.backgroundStyle === safeVideoConfig.backgroundStyle ? '前端' : '默认'})`);

        if (mergedVideoConfig.subtitleConfig) {
            const mergedSC = mergedVideoConfig.subtitleConfig;
            logger.info(`${logPrefix}[🔍 TRACE] 合并后subtitleConfig键: [${Object.keys(mergedSC).join(', ')}]`);

            if (mergedSC.videoGuide) {
                logger.info(`${logPrefix}[🔍 TRACE] 合并后videoGuide.title1: "${mergedSC.videoGuide.title1}" (来源: ${mergedSC.videoGuide.title1 === frontendSC?.videoGuide?.title1 ? '前端' : '默认'})`);
                logger.info(`${logPrefix}[🔍 TRACE] 合并后videoGuide.title2: "${mergedSC.videoGuide.title2}" (来源: ${mergedSC.videoGuide.title2 === frontendSC?.videoGuide?.title2 ? '前端' : '默认'})`);
                logger.info(`${logPrefix}[🔍 TRACE] 合并后videoGuide.enabled: ${mergedSC.videoGuide.enabled} (来源: ${mergedSC.videoGuide.enabled === frontendSC?.videoGuide?.enabled ? '前端' : '默认'})`);
            }

            if (mergedSC.advertisement) {
                logger.info(`${logPrefix}[🔍 TRACE] 合并后advertisement.enabled: ${mergedSC.advertisement.enabled} (来源: ${mergedSC.advertisement.enabled === frontendSC?.advertisement?.enabled ? '前端' : '默认'})`);
                logger.info(`${logPrefix}[🔍 TRACE] 合并后advertisement.titles长度: ${mergedSC.advertisement.titles?.length || 0}`);
                if (mergedSC.advertisement.titles && mergedSC.advertisement.titles.length > 0) {
                    mergedSC.advertisement.titles.forEach((title, index) => {
                        logger.info(`${logPrefix}[🔍 TRACE] 合并后advertisement.titles[${index}] - line1: "${title.line1}", line2: "${title.line2}"`);
                    });
                }
            }
        }
        logger.info(`${logPrefix}[🔍 TRACE] ========== 深度合并结果分析完成 ==========`);

        // === 重要修复：确保 repeatCount 和 repeatModes 的一致性 ===
        logger.info(`${logPrefix}[步骤 3.6] 开始修复 repeatCount 和 repeatModes 一致性`);

        // === 🔍 一致性修复详细分析 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6.1] ========== 一致性修复前状态分析 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 合并后mergedVideoConfig.repeatCount: ${mergedVideoConfig.repeatCount}`);
        logger.info(`${logPrefix}[🔍 TRACE] 前端safeVideoConfig.repeatCount: ${safeVideoConfig.repeatCount} (类型: ${typeof safeVideoConfig.repeatCount})`);
        logger.info(`${logPrefix}[🔍 TRACE] 前端safeVideoConfig.repeatModes存在: ${!!safeVideoConfig.repeatModes}`);
        if (safeVideoConfig.repeatModes) {
            logger.info(`${logPrefix}[🔍 TRACE] 前端safeVideoConfig.repeatModes长度: ${safeVideoConfig.repeatModes.length}`);
            logger.info(`${logPrefix}[🔍 TRACE] 前端safeVideoConfig.repeatModes详情: ${JSON.stringify(safeVideoConfig.repeatModes, null, 2)}`);
        }
        logger.info(`${logPrefix}[🔍 TRACE] 合并后mergedVideoConfig.subtitleConfig.repeatModes存在: ${!!(mergedVideoConfig.subtitleConfig && mergedVideoConfig.subtitleConfig.repeatModes)}`);
        if (mergedVideoConfig.subtitleConfig && mergedVideoConfig.subtitleConfig.repeatModes) {
            logger.info(`${logPrefix}[🔍 TRACE] 合并后mergedVideoConfig.subtitleConfig.repeatModes长度: ${mergedVideoConfig.subtitleConfig.repeatModes.length}`);
            logger.info(`${logPrefix}[🔍 TRACE] 合并后mergedVideoConfig.subtitleConfig.repeatModes详情: ${JSON.stringify(mergedVideoConfig.subtitleConfig.repeatModes, null, 2)}`);
        }

        // 检查前端传递的 repeatModes 位置（可能在根级别）
        let finalRepeatCount = mergedVideoConfig.repeatCount;
        let finalRepeatModes = null;

        // 优先使用前端传递的配置
        if (safeVideoConfig.repeatCount && typeof safeVideoConfig.repeatCount === 'number') {
            finalRepeatCount = safeVideoConfig.repeatCount;
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 使用前端传递的 repeatCount: ${finalRepeatCount}`);
        } else {
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 使用合并后的 repeatCount: ${finalRepeatCount}`);
        }

        if (safeVideoConfig.repeatModes && Array.isArray(safeVideoConfig.repeatModes)) {
            finalRepeatModes = safeVideoConfig.repeatModes;
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 使用前端传递的 repeatModes，数量: ${finalRepeatModes.length}`);
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 前端repeatModes详情: ${JSON.stringify(finalRepeatModes, null, 2)}`);
        } else if (mergedVideoConfig.subtitleConfig && mergedVideoConfig.subtitleConfig.repeatModes) {
            finalRepeatModes = mergedVideoConfig.subtitleConfig.repeatModes;
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 使用默认配置的 repeatModes，数量: ${finalRepeatModes.length}`);
            logger.info(`${logPrefix}[🔍 TRACE][步骤 3.6] 默认repeatModes详情: ${JSON.stringify(finalRepeatModes, null, 2)}`);
        } else {
            logger.warn(`${logPrefix}[🔍 TRACE][步骤 3.6] ⚠️ 未找到任何repeatModes配置`);
        }
        
        // 确保一致性
        if (finalRepeatModes && finalRepeatCount !== finalRepeatModes.length) {
            logger.warn(`${logPrefix}[步骤 3.6] 检测到不一致: repeatCount=${finalRepeatCount}, repeatModes.length=${finalRepeatModes.length}`);
            
            if (finalRepeatModes.length > finalRepeatCount) {
                // 截断多余的模式
                finalRepeatModes = finalRepeatModes.slice(0, finalRepeatCount);
                logger.info(`${logPrefix}[步骤 3.6] 截断 repeatModes 到 ${finalRepeatCount} 个元素`);
            } else if (finalRepeatModes.length < finalRepeatCount) {
                // 补充缺失的模式（默认为 blindListen）
                while (finalRepeatModes.length < finalRepeatCount) {
                    const index = finalRepeatModes.length;
                    const chineseNumbers = ["一", "二", "三", "四", "五"];
                    const chineseNumber = chineseNumbers[index] || (index + 1);
                    finalRepeatModes.push({
                        name: "blindListen",
                        displayText: `第${chineseNumber}遍 盲听`
                    });
                }
                logger.info(`${logPrefix}[步骤 3.6] 补充 repeatModes 到 ${finalRepeatCount} 个元素`);
            }
        }
        
        // 更新合并后的配置
        mergedVideoConfig.repeatCount = finalRepeatCount;
        if (finalRepeatModes) {
            // 确保 repeatModes 在正确的位置
            if (!mergedVideoConfig.subtitleConfig) {
                mergedVideoConfig.subtitleConfig = {};
            }
            mergedVideoConfig.subtitleConfig.repeatModes = finalRepeatModes;
            
            // 如果前端传递的是根级别的 repeatModes，移除它以避免混淆
            if (mergedVideoConfig.repeatModes) {
                delete mergedVideoConfig.repeatModes;
                logger.info(`${logPrefix}[步骤 3.6] 移除根级别的 repeatModes，使用 subtitleConfig.repeatModes`);
            }
        }
        
        logger.info(`${logPrefix}[步骤 3.6] 一致性修复完成: repeatCount=${mergedVideoConfig.repeatCount}, repeatModes.length=${mergedVideoConfig.subtitleConfig?.repeatModes?.length || 0}`);

        // === 🔍 最终配置详细分析 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.7] ========== 最终配置详细分析 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 最终配置完整结构: ${JSON.stringify(mergedVideoConfig, null, 2)}`);
        logger.info(`${logPrefix}[🔍 TRACE] 最终配置根级别键: [${Object.keys(mergedVideoConfig).join(', ')}]`);
        logger.info(`${logPrefix}[🔍 TRACE] 最终.repeatCount: ${mergedVideoConfig.repeatCount} (类型: ${typeof mergedVideoConfig.repeatCount})`);
        logger.info(`${logPrefix}[🔍 TRACE] 最终.backgroundStyle: "${mergedVideoConfig.backgroundStyle}" (类型: ${typeof mergedVideoConfig.backgroundStyle})`);

        if (mergedVideoConfig.subtitleConfig) {
            const finalSC = mergedVideoConfig.subtitleConfig;
            logger.info(`${logPrefix}[🔍 TRACE] 最终subtitleConfig键: [${Object.keys(finalSC).join(', ')}]`);

            // 最终videoGuide配置
            if (finalSC.videoGuide) {
                logger.info(`${logPrefix}[🔍 TRACE] 最终videoGuide配置:`);
                logger.info(`${logPrefix}[🔍 TRACE]   - enabled: ${finalSC.videoGuide.enabled} (类型: ${typeof finalSC.videoGuide.enabled})`);
                logger.info(`${logPrefix}[🔍 TRACE]   - title1: "${finalSC.videoGuide.title1}" (类型: ${typeof finalSC.videoGuide.title1}, 长度: ${finalSC.videoGuide.title1?.length || 0})`);
                logger.info(`${logPrefix}[🔍 TRACE]   - title2: "${finalSC.videoGuide.title2}" (类型: ${typeof finalSC.videoGuide.title2}, 长度: ${finalSC.videoGuide.title2?.length || 0})`);
                logger.info(`${logPrefix}[🔍 TRACE]   - style存在: ${!!finalSC.videoGuide.style}`);
                if (finalSC.videoGuide.style) {
                    logger.info(`${logPrefix}[🔍 TRACE]   - style详情: ${JSON.stringify(finalSC.videoGuide.style, null, 2)}`);
                }
                logger.info(`${logPrefix}[🔍 TRACE] 最终videoGuide完整对象: ${JSON.stringify(finalSC.videoGuide, null, 2)}`);
            } else {
                logger.warn(`${logPrefix}[🔍 TRACE] ⚠️ 最终配置中videoGuide不存在`);
            }

            // 最终advertisement配置
            if (finalSC.advertisement) {
                logger.info(`${logPrefix}[🔍 TRACE] 最终advertisement配置:`);
                logger.info(`${logPrefix}[🔍 TRACE]   - enabled: ${finalSC.advertisement.enabled} (类型: ${typeof finalSC.advertisement.enabled})`);
                logger.info(`${logPrefix}[🔍 TRACE]   - titles存在: ${!!finalSC.advertisement.titles}`);
                if (finalSC.advertisement.titles) {
                    logger.info(`${logPrefix}[🔍 TRACE]   - titles类型: ${Array.isArray(finalSC.advertisement.titles) ? 'Array' : typeof finalSC.advertisement.titles}`);
                    logger.info(`${logPrefix}[🔍 TRACE]   - titles长度: ${finalSC.advertisement.titles.length}`);
                    finalSC.advertisement.titles.forEach((title, index) => {
                        logger.info(`${logPrefix}[🔍 TRACE]   - titles[${index}]: ${JSON.stringify(title, null, 2)}`);
                        logger.info(`${logPrefix}[🔍 TRACE]   - titles[${index}].line1: "${title.line1}" (长度: ${title.line1?.length || 0})`);
                        logger.info(`${logPrefix}[🔍 TRACE]   - titles[${index}].line2: "${title.line2}" (长度: ${title.line2?.length || 0})`);
                    });
                }
                logger.info(`${logPrefix}[🔍 TRACE]   - style存在: ${!!finalSC.advertisement.style}`);
                logger.info(`${logPrefix}[🔍 TRACE] 最终advertisement完整对象: ${JSON.stringify(finalSC.advertisement, null, 2)}`);
            } else {
                logger.warn(`${logPrefix}[🔍 TRACE] ⚠️ 最终配置中advertisement不存在`);
            }

            // 最终repeatModes配置
            if (finalSC.repeatModes) {
                logger.info(`${logPrefix}[🔍 TRACE] 最终repeatModes配置:`);
                logger.info(`${logPrefix}[🔍 TRACE]   - 长度: ${finalSC.repeatModes.length}`);
                logger.info(`${logPrefix}[🔍 TRACE]   - 详情: ${JSON.stringify(finalSC.repeatModes, null, 2)}`);
            } else {
                logger.warn(`${logPrefix}[🔍 TRACE] ⚠️ 最终配置中repeatModes不存在`);
            }
        } else {
            logger.error(`${logPrefix}[🔍 TRACE] ❌ 最终配置中subtitleConfig不存在`);
        }

        logger.info(`${logPrefix}[🔍 TRACE] ========== 最终配置分析完成 ==========`);

        // === 🔍 配置传递追踪 ===
        logger.info(`${logPrefix}[🔍 TRACE][步骤 3.8] ========== 配置传递到流水线追踪 ==========`);
        logger.info(`${logPrefix}[🔍 TRACE] 即将传递给流水线的videoConfig: ${JSON.stringify(mergedVideoConfig, null, 2)}`);
        logger.info(`${logPrefix}[🔍 TRACE] 传递给流水线的关键配置摘要:`);
        logger.info(`${logPrefix}[🔍 TRACE]   - repeatCount: ${mergedVideoConfig.repeatCount}`);
        logger.info(`${logPrefix}[🔍 TRACE]   - backgroundStyle: "${mergedVideoConfig.backgroundStyle}"`);
        if (mergedVideoConfig.subtitleConfig) {
            if (mergedVideoConfig.subtitleConfig.videoGuide) {
                logger.info(`${logPrefix}[🔍 TRACE]   - videoGuide.title1: "${mergedVideoConfig.subtitleConfig.videoGuide.title1}"`);
                logger.info(`${logPrefix}[🔍 TRACE]   - videoGuide.title2: "${mergedVideoConfig.subtitleConfig.videoGuide.title2}"`);
            }
            if (mergedVideoConfig.subtitleConfig.advertisement && mergedVideoConfig.subtitleConfig.advertisement.titles) {
                logger.info(`${logPrefix}[🔍 TRACE]   - advertisement.titles数量: ${mergedVideoConfig.subtitleConfig.advertisement.titles.length}`);
                if (mergedVideoConfig.subtitleConfig.advertisement.titles.length > 0) {
                    logger.info(`${logPrefix}[🔍 TRACE]   - advertisement.titles[0].line1: "${mergedVideoConfig.subtitleConfig.advertisement.titles[0].line1}"`);
                    logger.info(`${logPrefix}[🔍 TRACE]   - advertisement.titles[0].line2: "${mergedVideoConfig.subtitleConfig.advertisement.titles[0].line2}"`);
                }
            }
        }
        logger.info(`${logPrefix}[🔍 TRACE] ========== 配置传递追踪完成 ==========`);

        // 记录背景风格配置
        logger.info(`${logPrefix}  - 背景风格 backgroundStyle: ${mergedVideoConfig.backgroundStyle || '未设置'}`);

        logger.info(`${logPrefix}  - 完整合并后配置: ${JSON.stringify(mergedVideoConfig, null, 2)}`);

        // === 步骤4: 参数完整性和有效性校验 ===
        const validationErrors = [];

        // 验证videoIdentifier
        if (!videoIdentifier || typeof videoIdentifier !== 'string' || videoIdentifier.trim() === '') {
            validationErrors.push('videoIdentifier必须是非空字符串');
        }

        // 验证clipSegments
        if (!clipSegments || !Array.isArray(clipSegments) || clipSegments.length === 0) {
            validationErrors.push('clipSegments必须是非空数组');
        } else {
            clipSegments.forEach((segment, index) => {
                if (typeof segment.startTime !== 'number' || segment.startTime < 0) {
                    validationErrors.push(`clipSegments[${index}].startTime必须是非负数字`);
                }
                if (typeof segment.endTime !== 'number' || segment.endTime <= segment.startTime) {
                    validationErrors.push(`clipSegments[${index}].endTime必须是大于startTime的数字`);
                }
            });
        }

        // 验证cropData
        if (!cropData || typeof cropData !== 'object') {
            validationErrors.push('cropData必须是对象');
        } else {
            const requiredCropFields = ['cropWidth', 'cropHeight', 'cropXOffset', 'cropYOffset'];
            requiredCropFields.forEach(field => {
                if (typeof cropData[field] !== 'number' || cropData[field] < 0) {
                    validationErrors.push(`cropData.${field}必须是非负数字`);
                }
            });
        }

        // 验证videoConfig
        if (!mergedVideoConfig || typeof mergedVideoConfig !== 'object') {
            validationErrors.push('mergedVideoConfig必须是对象');
        } else {
            if (typeof mergedVideoConfig.repeatCount !== 'number' || mergedVideoConfig.repeatCount < 1) {
                validationErrors.push('videoConfig.repeatCount必须是大于0的数字');
            }
            if (!mergedVideoConfig.subtitleConfig || !Array.isArray(mergedVideoConfig.subtitleConfig.repeatModes) || mergedVideoConfig.subtitleConfig.repeatModes.length === 0) {
                validationErrors.push('videoConfig.subtitleConfig.repeatModes必须是非空数组');
            } else if (mergedVideoConfig.repeatCount !== mergedVideoConfig.subtitleConfig.repeatModes.length) {
                validationErrors.push(`videoConfig.repeatCount(${mergedVideoConfig.repeatCount})与repeatModes数量(${mergedVideoConfig.subtitleConfig.repeatModes.length})不一致`);
            }
            // 验证backgroundStyle
            const validBackgroundStyles = ['newspaper', 'abstract'];
            if (mergedVideoConfig.backgroundStyle && !validBackgroundStyles.includes(mergedVideoConfig.backgroundStyle)) {
                validationErrors.push(`videoConfig.backgroundStyle必须是有效值之一: ${validBackgroundStyles.join(', ')}`);
            }
        }

        if (validationErrors.length > 0) {
            logger.warn(`${logPrefix}[步骤 4][WARN] 参数校验失败: ${validationErrors.join('; ')}`);

            // 使用标准化工厂函数创建预检失败事件
            const preflightFailedEvent = createControllerStatusSSE(
                CONTROLLER_STATUS.FAILED_PREFLIGHT,
                {
                    message: `生成参数校验失败: ${validationErrors.join('; ')}`,
                    errorDetails: {
                        message: 'Invalid generation parameters.',
                        code: 'PARAM_VALIDATION_FAILED',
                        validationErrors: validationErrors
                    }
                }
            );

            sseManager.sendEvent(preflightFailedEvent);
            sseManager.close();
            return;
        }

        // === 步骤5: 发送请求接受确认事件 ===
        const acceptedEvent = createControllerStatusSSE(
            CONTROLLER_STATUS.ACCEPTED,
            {
                message: `视频生成请求已接收，准备处理${clipSegments.length}个片段`,
                reqId: reqId,  // 添加reqId到事件数据中
                videoIdentifier: videoIdentifier,
                generationParams: {
                    segmentCount: clipSegments.length,
                    cropDimensions: `${cropData.cropWidth}x${cropData.cropHeight}`,
                    cropOffset: `${cropData.cropXOffset},${cropData.cropYOffset}`,
                    repeatCount: mergedVideoConfig.repeatCount,
                    repeatModes: mergedVideoConfig.subtitleConfig?.repeatModes ? mergedVideoConfig.subtitleConfig.repeatModes.map(m => m.name).join(', ') : 'N/A'
                }
            }
        );

        sseManager.sendEvent(acceptedEvent);
        logger.info(`${logPrefix}[步骤 5] 已发送请求接受确认事件`);

        // === 步骤6: 定义标准化进度回调函数 ===
        const progressCallbackFromController = (progressData) => {
            try {
                logger.info(`${logPrefix}[PROGRESS_CALLBACK] 收到生成进度: Pipeline '${progressData.pipelineName}', Status '${progressData.pipelineStatus}'`);

                const progressEvent = createSSEEventData(
                    SSE_EVENT_TYPES.PIPELINE_PROGRESS,
                    progressData
                );

                // 确保连接仍然活跃
                if (sseManager && sseManager.isActive) {
                    const sent = sseManager.sendEvent(progressEvent);
                    if (!sent) {
                        logger.warn(`${logPrefix}[PROGRESS_CALLBACK] SSE事件发送失败`);
                    }
                }
            } catch (error) {
                logger.error(`${logPrefix}[PROGRESS_CALLBACK] 进度回调处理异常: ${error.message}`);
            }
        };

        // === 步骤7: 创建视频生成流水线服务 ===
        logger.info(`${logPrefix}[步骤 7] 创建VideoGenerationPipelineService实例。`);
        const generationPipelineService = new VideoGenerationPipelineService(reqId);

        // === 步骤8: 执行视频生成流水线 ===
        logger.info(`${logPrefix}[步骤 8] 调用generationPipelineService.processVideoGeneration。`);

        // 创建或获取生成文件目录
        const generatedDir = PathHelper.getGeneratedDir(videoIdentifier);
        fs.mkdirSync(generatedDir, { recursive: true });
        logger.info(`${logPrefix}[步骤 8.0] 生成文件目录: ${generatedDir}`);

        // 定义视频生成流水线的上下文参数
        const generationContext = {
            reqId,                     // 请求的唯一标识符
            originalVideoPath,         // 原始视频文件路径
            videoIdentifier,           // 视频的唯一标识符
            clipSegments,              // 多个片段信息
            cropData,                  // 裁剪参数
            videoConfig: mergedVideoConfig, // 使用合并后的视频配置参数
            correctedFullText,         // 字幕文本（TranscriptionCorrectionTask必需）
            englishSrtContent,         // 英文字幕内容
            chineseSrtContent,         // 中文字幕内容
            savePath: generatedDir     // 文件保存路径（生成文件保存到generated目录）
        };

        logger.info(`${logPrefix}[步骤 8.1] 执行视频生成流水线核心方法。上下文参数: ${JSON.stringify(generationContext)}`);

        // 调用流水线服务执行视频生成
        const generationResult = await generationPipelineService.processVideoGeneration(
            generationContext,
            progressCallbackFromController
        );

        logger.info(`${logPrefix}[步骤 8.2] generationPipelineService.processVideoGeneration调用完成。最终状态: ${generationResult.status}`);

        // === 步骤9: 处理流水线结果 ===
        if (generationResult.status !== 'completed' && generationResult.error) {
            // 只有在流水线失败时才发送额外的错误事件
            let errorEventDataPayload = {
                pipelineName: `VideoGeneration-${reqId}`,
                pipelineStatus: PIPELINE_STATUS.FAILED,
                timestamp: new Date().toISOString(),
                videoIdentifier: videoIdentifier,
                failedTaskErrorDetails: {
                    message: generationResult.error.message,
                    name: generationResult.error.name,
                }
            };

            const errorEvent = createSSEEventData(
                SSE_EVENT_TYPES.PIPELINE_PROGRESS,
                errorEventDataPayload
            );

            sseManager.sendEvent(errorEvent);
            logger.error(`${logPrefix}[步骤 9] 视频生成流水线执行失败，已发送错误事件。错误: ${generationResult.error.message}`);

            // 关闭SSE连接
            setTimeout(() => {
                if (sseManager && sseManager.isActive) {
                    sseManager.close();
                    logger.info(`${logPrefix}[步骤 9] SSE连接已因错误关闭`);
                }
            }, 1000);
        } else if (generationResult.status === 'completed') {
            logger.info(`${logPrefix}[步骤 9] 视频生成流水线执行成功。准备发送最终标准化结果。`);

            // 详细记录完整的上下文信息，供调试使用
            if (generationResult.context) {
                logger.info(`${logPrefix}[步骤 9] 完整上下文信息:`);
                logger.info(`${logPrefix}  - 处理后视频路径: ${generationResult.context.processedVideoPath || 'N/A'}`);
                logger.info(`${logPrefix}  - 处理后视频文件名: ${generationResult.context.processedVideoFileName || 'N/A'}`);
                logger.info(`${logPrefix}  - 处理后视频标识符: ${generationResult.context.processedVideoIdentifier || 'N/A'}`);
                logger.info(`${logPrefix}  - 最终视频尺寸: ${generationResult.context.finalVideoWidth || 'N/A'}x${generationResult.context.finalVideoHeight || 'N/A'}`);
                logger.info(`${logPrefix}  - 最终视频时长: ${generationResult.context.finalVideoDuration || 'N/A'}秒`);
                logger.info(`${logPrefix}  - 最终视频帧率: ${generationResult.context.finalVideoFrameRate || 'N/A'}fps`);
                logger.info(`${logPrefix}  - 最终视频编码: ${generationResult.context.finalVideoCodec || 'N/A'}`);
                logger.info(`${logPrefix}  - 最终视频文件大小: ${generationResult.context.finalVideoFileSize || 'N/A'} bytes`);
                logger.info(`${logPrefix}  - 处理历史: ${JSON.stringify(generationResult.context.processingHistory || {})}`);
                logger.info(`${logPrefix}  - 处理完成时间: ${generationResult.context.processedAt || 'N/A'}`);
                logger.info(`${logPrefix}  - 处理耗时: ${generationResult.context.processingDuration || 'N/A'}ms`);
            }

            // 发送最终的标准化结果给前端
            const finalResultEvent = createSSEEventData(
                SSE_EVENT_TYPES.PIPELINE_COMPLETE,
                {
                    message: '视频生成流水线已完成',
                    result: generationResult,  // 发送完整的标准化结果
                    timestamp: new Date().toISOString(),
                    reqId: reqId
                }
            );

            // 检查客户端连接状态和SSE管理器状态
            if (clientDisconnected) {
                logger.warn(`${logPrefix}[步骤 9] 客户端已断开连接，跳过发送最终结果`);
            } else if (sseManager && sseManager.isActive) {
                logger.info(`${logPrefix}[步骤 9] SSE连接状态正常，准备发送最终结果事件`);
                const sent = sseManager.sendEvent(finalResultEvent);
                logger.info(`${logPrefix}[步骤 9] 最终结果事件发送${sent ? '成功' : '失败'}`);

                // 使用优雅关闭方法，给最终结果事件足够的传输时间
                if (!clientDisconnected && sseManager && sseManager.isActive) {
                    // 发送流水线完成通知事件
                    const pipelineCompletedEvent = createSSEEventData(
                        SSE_EVENT_TYPES.SYSTEM_STATUS,
                        {
                            type: 'pipeline_completed',
                            connectionId: sseManager.connectionId,
                            message: '视频生成流水线已完成，连接即将关闭'
                        }
                    );
                    sseManager.sendEvent(pipelineCompletedEvent);

                    // 使用优雅关闭，延迟2秒关闭连接
                    sseManager.gracefulClose(2000);
                    logger.info(`${logPrefix}[步骤 9] 已启动SSE连接优雅关闭流程`);
                } else {
                    logger.warn(`${logPrefix}[步骤 9] 客户端已断开或SSE连接已断开`);
                    if (sseManager && sseManager.isActive) {
                        sseManager.close(false); // 不发送关闭事件，直接关闭
                        logger.info(`${logPrefix}[步骤 9] 强制关闭SSE连接`);
                    }
                }
            } else {
                logger.warn(`${logPrefix}[步骤 9] SSE连接已断开，无法发送最终结果`);
                logger.warn(`${logPrefix}[步骤 9] SSE管理器状态: ${sseManager ? (sseManager.isActive ? 'active' : 'inactive') : 'null'}`);
            }
        }

    } catch (error) {
        // === 错误处理：控制器级别异常捕获 ===
        logger.error(`${logPrefix}[CRITICAL_ERROR] 控制器级别捕获到意外错误: ${error.message}`);
        logger.error(`${logPrefix}Stack: ${error.stack}`);

        if (sseManager && sseManager.isActive) {
            try {
                // 使用标准化工厂函数创建控制器错误事件
                const controllerErrorEvent = createControllerStatusSSE(
                    CONTROLLER_STATUS.CONTROLLER_ERROR,
                    {
                        errorDetails: {
                            message: `视频生成服务器内部发生严重错误: ${error.message}`,
                            name: error.name,
                            stackPreview: error.stack ? error.stack.substring(0, 200) + '...' : 'N/A'
                        }
                    }
                );

                sseManager.sendEvent(controllerErrorEvent);
                logger.info(`${logPrefix}[ERROR_RECOVERY] 已发送控制器错误事件`);

            } catch (sseError) {
                logger.error(`${logPrefix}[CRITICAL_ERROR] 发送控制器错误事件失败: ${sseError.message}`);
            }

            sseManager.close();
            logger.info(`${logPrefix}[ERROR_RECOVERY] SSE连接已因错误关闭`);
        }
    } finally {
        // === 资源清理：确保心跳正确关闭 ===
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }
        // 注意：不在finally中关闭SSE连接，让成功路径中的setTimeout来处理
        // 这样可以确保最终结果事件有足够时间发送
    }
};

// 导出控制器方法
module.exports = {
    generateVideo
};