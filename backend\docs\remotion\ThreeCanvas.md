# ThreeCanvas

**来源**: @remotion/three 包  
**类型**: React 组件  
**用途**: 在 Remotion 中创建 3D 场景和动画

## 功能概述

`<ThreeCanvas>` 是 React Three Fiber 的 `<Canvas />` 组件的包装器，专门为 Remotion 设计。它解决了 React Three Fiber 作为自定义渲染器时无法访问外部 React 上下文的问题，使得在 Remotion 中使用 3D 功能变得简单直接。

## 核心特性

- **上下文同步**: 自动同步 Remotion 的 `useCurrentFrame()` 和其他上下文
- **声明式动画**: 使用 Remotion 的时间轴而非 React Three Fiber 的 `useFrame`
- **时间轴兼容**: 支持在 Studio 中前后拖拽和暂停
- **布局修复**: 解决浏览器缩放导致的布局问题

## 基本语法

```tsx
import { ThreeCanvas } from "@remotion/three";
import { useCurrentFrame, useVideoConfig } from "remotion";

const My3DComponent: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();

  return (
    <ThreeCanvas
      width={width}
      height={height}
      camera={{ fov: 75, position: [0, 0, 470] }}
    >
      {/* 3D 内容 */}
    </ThreeCanvas>
  );
};
```

## 必需属性

### width
- **类型**: `number`
- **描述**: 画布宽度（像素）
- **必需**: 是
- **说明**: 用于解决浏览器缩放导致的布局问题

### height
- **类型**: `number`
- **描述**: 画布高度（像素）
- **必需**: 是
- **说明**: 用于解决浏览器缩放导致的布局问题

## 可选属性

### camera
- **类型**: `CameraProps`
- **描述**: 相机配置对象
- **示例**: `{ fov: 75, position: [0, 0, 470] }`

### orthographic
- **类型**: `boolean`
- **描述**: 是否使用正交投影
- **默认值**: `false`

### style
- **类型**: `React.CSSProperties`
- **描述**: CSS 样式对象
- **示例**: `{ backgroundColor: "white" }`

## 实际应用场景

### 1. 旋转变色立方体

```tsx
import { ThreeCanvas } from "@remotion/three";
import { interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const SpinningCube: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();

  return (
    <ThreeCanvas
      orthographic={false}
      width={width}
      height={height}
      style={{ backgroundColor: "white" }}
      camera={{ fov: 75, position: [0, 0, 470] }}
    >
      <ambientLight intensity={0.15} />
      <pointLight args={[undefined, 0.4]} position={[200, 200, 0]} />
      <mesh
        position={[0, 0, 0]}
        rotation={[
          frame * 0.06 * 0.5,
          frame * 0.07 * 0.5,
          frame * 0.08 * 0.5
        ]}
        scale={interpolate(Math.sin(frame / 10), [-1, 1], [0.8, 1.2])}
      >
        <boxGeometry args={[100, 100, 100]} />
        <meshStandardMaterial
          color={[
            Math.sin(frame * 0.12) * 0.5 + 0.5,
            Math.cos(frame * 0.11) * 0.5 + 0.5,
            Math.sin(frame * 0.08) * 0.5 + 0.5,
          ]}
        />
      </mesh>
    </ThreeCanvas>
  );
};
```

### 2. 3D 产品展示

```tsx
const ProductShowcase: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();
  
  const rotationY = interpolate(frame, [0, 300], [0, Math.PI * 2]);
  const cameraZ = interpolate(frame, [0, 100], [500, 300], {
    extrapolateRight: "clamp"
  });

  return (
    <ThreeCanvas
      width={width}
      height={height}
      camera={{ position: [0, 0, cameraZ] }}
    >
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      
      <mesh rotation={[0, rotationY, 0]}>
        <cylinderGeometry args={[50, 50, 100, 32]} />
        <meshStandardMaterial color="#ff6b6b" metalness={0.8} roughness={0.2} />
      </mesh>
    </ThreeCanvas>
  );
};
```

### 3. 粒子动画系统

```tsx
const ParticleSystem: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();
  
  const particles = Array.from({ length: 100 }, (_, i) => {
    const time = frame * 0.02;
    const x = Math.sin(time + i * 0.1) * 200;
    const y = Math.cos(time + i * 0.15) * 150;
    const z = Math.sin(time + i * 0.05) * 100;
    
    return { x, y, z, id: i };
  });

  return (
    <ThreeCanvas
      width={width}
      height={height}
      camera={{ position: [0, 0, 400] }}
    >
      <ambientLight intensity={0.3} />
      {particles.map((particle) => (
        <mesh key={particle.id} position={[particle.x, particle.y, particle.z]}>
          <sphereGeometry args={[2, 8, 8]} />
          <meshBasicMaterial color="#4ecdc4" />
        </mesh>
      ))}
    </ThreeCanvas>
  );
};
```

### 4. 3D 文字动画

```tsx
const Text3D: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();
  
  const scale = spring({
    frame,
    fps: 30,
    config: { damping: 200 }
  });

  return (
    <ThreeCanvas
      width={width}
      height={height}
      camera={{ position: [0, 0, 300] }}
    >
      <ambientLight intensity={0.4} />
      <pointLight position={[100, 100, 100]} />
      
      <group scale={[scale, scale, scale]}>
        <mesh>
          <textGeometry args={["REMOTION", { size: 40, height: 10 }]} />
          <meshStandardMaterial color="#ff4757" />
        </mesh>
      </group>
    </ThreeCanvas>
  );
};
```

### 5. 多层 3D 场景

```tsx
const MultiLayerScene: React.FC = () => {
  const frame = useCurrentFrame();
  const { width, height } = useVideoConfig();
  
  const backgroundRotation = frame * 0.01;
  const foregroundRotation = frame * 0.03;

  return (
    <ThreeCanvas
      width={width}
      height={height}
      camera={{ position: [0, 0, 500] }}
    >
      <ambientLight intensity={0.2} />
      <directionalLight position={[5, 5, 5]} />
      
      {/* 背景层 */}
      <group rotation={[0, backgroundRotation, 0]}>
        <mesh position={[0, 0, -200]}>
          <torusGeometry args={[100, 30, 16, 100]} />
          <meshStandardMaterial color="#3742fa" wireframe />
        </mesh>
      </group>
      
      {/* 前景层 */}
      <group rotation={[foregroundRotation, 0, 0]}>
        <mesh>
          <octahedronGeometry args={[50]} />
          <meshStandardMaterial color="#ff3838" />
        </mesh>
      </group>
    </ThreeCanvas>
  );
};
```

## 重要注意事项

### Sequence 组件兼容性

当在 `<ThreeCanvas>` 内使用 `<Sequence>` 时，必须添加 `layout="none"` 属性：

```tsx
<ThreeCanvas width={width} height={height}>
  <Sequence from={30} durationInFrames={60} layout="none">
    {/* 3D 内容 */}
  </Sequence>
</ThreeCanvas>
```

### 动画最佳实践

1. **使用声明式动画**: 避免使用 React Three Fiber 的 `useFrame`
2. **基于帧的计算**: 使用 `useCurrentFrame()` 进行所有动画计算
3. **性能优化**: 避免在每帧创建新对象，使用 `useMemo` 缓存复杂计算

### 常见错误

- **忘记设置 width/height**: 会导致布局问题
- **使用 useFrame**: 在 Remotion 中不会正常工作
- **Sequence 布局错误**: 需要设置 `layout="none"`

## 性能优化建议

1. **几何体复用**: 对相同的几何体使用 `useMemo`
2. **材质优化**: 避免过于复杂的材质和光照
3. **LOD 系统**: 根据距离调整模型细节
4. **实例化**: 对大量相同对象使用 `InstancedMesh`

## 与其他 Remotion 组件的集成

```tsx
const IntegratedScene: React.FC = () => {
  const { width, height } = useVideoConfig();
  
  return (
    <AbsoluteFill>
      {/* 3D 背景 */}
      <ThreeCanvas width={width} height={height}>
        {/* 3D 内容 */}
      </ThreeCanvas>
      
      {/* 2D 覆盖层 */}
      <AbsoluteFill style={{ pointerEvents: "none" }}>
        <h1 style={{ color: "white", textAlign: "center" }}>
          3D + 2D 组合
        </h1>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

## 参考资源

- **官方文档**: https://www.remotion.dev/docs/three-canvas
- **React Three Fiber**: https://github.com/pmndrs/react-three-fiber
- **源代码**: https://github.com/remotion-dev/remotion/blob/main/packages/three/src/ThreeCanvas.tsx
- **示例项目**: Remotion 仓库的 examples 文件夹
