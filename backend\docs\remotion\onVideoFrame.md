# onVideoFrame

**版本**: v4.0.190+  
**类型**: 回调函数属性  
**组件**: OffthreadVideo  
**用途**: 视频帧级处理和操作

## 功能概述

`onVideoFrame` 是 `<OffthreadVideo>` 组件的回调属性，当从视频中提取帧时被调用。这个功能为视频操作提供了强大的底层控制能力，可以实现绿幕效果、实时滤镜、帧分析等高级视频处理功能。

## 核心特性

- **帧级访问**: 获取视频的每一帧数据
- **实时处理**: 在预览和渲染时都可以处理帧
- **Canvas 兼容**: 接收 `CanvasImageSource` 对象
- **环境适配**: 预览时为 `HTMLVideoElement`，渲染时为 `HTMLImageElement`

## 基本语法

```tsx
import { OffthreadVideo, useVideoConfig } from 'remotion';

const VideoProcessor: React.FC = () => {
  const { width, height } = useVideoConfig();
  
  const handleVideoFrame = (frame: CanvasImageSource) => {
    // 处理视频帧
    console.log('收到新帧:', frame);
  };

  return (
    <OffthreadVideo
      src="video.mp4"
      onVideoFrame={handleVideoFrame}
    />
  );
};
```

## 回调函数参数

### frame
- **类型**: `CanvasImageSource`
- **预览环境**: `HTMLVideoElement`
- **渲染环境**: `HTMLImageElement`
- **描述**: 当前视频帧的图像数据

## 实际应用场景

### 1. 绿幕效果实现

```tsx
import React, { useCallback, useRef } from 'react';
import { AbsoluteFill, OffthreadVideo, useVideoConfig } from 'remotion';

export const GreenscreenEffect: React.FC<{ opacity: number }> = ({ opacity }) => {
  const canvas = useRef<HTMLCanvasElement>(null);
  const { width, height } = useVideoConfig();

  const onVideoFrame = useCallback(
    (frame: CanvasImageSource) => {
      if (!canvas.current) return;
      
      const context = canvas.current.getContext('2d');
      if (!context) return;

      // 绘制原始帧
      context.drawImage(frame, 0, 0, width, height);
      const imageFrame = context.getImageData(0, 0, width, height);
      const { length } = imageFrame.data;

      // 处理绿色像素，使其透明
      for (let i = 0; i < length; i += 4) {
        const red = imageFrame.data[i + 0];
        const green = imageFrame.data[i + 1];
        const blue = imageFrame.data[i + 2];
        
        // 检测绿色区域
        if (green > 100 && red < 100 && blue < 100) {
          imageFrame.data[i + 3] = opacity * 255; // 设置透明度
        }
      }
      
      context.putImageData(imageFrame, 0, 0);
    },
    [height, width, opacity],
  );

  return (
    <AbsoluteFill>
      <AbsoluteFill>
        <OffthreadVideo 
          style={{ opacity: 0 }} 
          onVideoFrame={onVideoFrame} 
          src="greenscreen-video.mp4" 
        />
      </AbsoluteFill>
      <AbsoluteFill>
        <canvas ref={canvas} width={width} height={height} />
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

### 2. 实时颜色滤镜

```tsx
const ColorFilter: React.FC<{ filterType: 'sepia' | 'grayscale' | 'invert' }> = ({ 
  filterType 
}) => {
  const canvas = useRef<HTMLCanvasElement>(null);
  const { width, height } = useVideoConfig();

  const applyFilter = useCallback(
    (frame: CanvasImageSource) => {
      if (!canvas.current) return;
      
      const context = canvas.current.getContext('2d');
      if (!context) return;

      context.drawImage(frame, 0, 0, width, height);
      const imageData = context.getImageData(0, 0, width, height);
      const data = imageData.data;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        switch (filterType) {
          case 'grayscale':
            const gray = 0.299 * r + 0.587 * g + 0.114 * b;
            data[i] = data[i + 1] = data[i + 2] = gray;
            break;
            
          case 'sepia':
            data[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
            data[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
            data[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));
            break;
            
          case 'invert':
            data[i] = 255 - r;
            data[i + 1] = 255 - g;
            data[i + 2] = 255 - b;
            break;
        }
      }

      context.putImageData(imageData, 0, 0);
    },
    [filterType, width, height],
  );

  return (
    <AbsoluteFill>
      <OffthreadVideo 
        src="video.mp4" 
        onVideoFrame={applyFilter}
        style={{ opacity: 0 }}
      />
      <canvas ref={canvas} width={width} height={height} />
    </AbsoluteFill>
  );
};
```

### 3. 运动检测和分析

```tsx
const MotionDetector: React.FC = () => {
  const canvas = useRef<HTMLCanvasElement>(null);
  const previousFrame = useRef<ImageData | null>(null);
  const [motionLevel, setMotionLevel] = useState(0);
  const { width, height } = useVideoConfig();

  const detectMotion = useCallback(
    (frame: CanvasImageSource) => {
      if (!canvas.current) return;
      
      const context = canvas.current.getContext('2d');
      if (!context) return;

      context.drawImage(frame, 0, 0, width, height);
      const currentImageData = context.getImageData(0, 0, width, height);

      if (previousFrame.current) {
        let totalDifference = 0;
        const currentData = currentImageData.data;
        const prevData = previousFrame.current.data;

        for (let i = 0; i < currentData.length; i += 4) {
          const rDiff = Math.abs(currentData[i] - prevData[i]);
          const gDiff = Math.abs(currentData[i + 1] - prevData[i + 1]);
          const bDiff = Math.abs(currentData[i + 2] - prevData[i + 2]);
          totalDifference += (rDiff + gDiff + bDiff) / 3;
        }

        const motionPercentage = (totalDifference / (width * height)) / 255;
        setMotionLevel(motionPercentage);
      }

      previousFrame.current = currentImageData;
    },
    [width, height],
  );

  return (
    <AbsoluteFill>
      <OffthreadVideo 
        src="video.mp4" 
        onVideoFrame={detectMotion}
      />
      <div style={{
        position: 'absolute',
        top: 20,
        left: 20,
        color: 'white',
        fontSize: 24,
        background: 'rgba(0,0,0,0.7)',
        padding: 10,
        borderRadius: 5,
      }}>
        运动强度: {(motionLevel * 100).toFixed(1)}%
      </div>
    </AbsoluteFill>
  );
};
```

### 4. 边缘检测效果

```tsx
const EdgeDetection: React.FC = () => {
  const canvas = useRef<HTMLCanvasElement>(null);
  const { width, height } = useVideoConfig();

  const detectEdges = useCallback(
    (frame: CanvasImageSource) => {
      if (!canvas.current) return;
      
      const context = canvas.current.getContext('2d');
      if (!context) return;

      context.drawImage(frame, 0, 0, width, height);
      const imageData = context.getImageData(0, 0, width, height);
      const data = imageData.data;
      const output = new Uint8ClampedArray(data);

      // Sobel 边缘检测算子
      const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
      const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          let pixelX = 0;
          let pixelY = 0;

          for (let i = -1; i <= 1; i++) {
            for (let j = -1; j <= 1; j++) {
              const idx = ((y + i) * width + (x + j)) * 4;
              const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
              
              pixelX += gray * sobelX[(i + 1) * 3 + (j + 1)];
              pixelY += gray * sobelY[(i + 1) * 3 + (j + 1)];
            }
          }

          const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
          const outputIdx = (y * width + x) * 4;
          
          output[outputIdx] = magnitude;
          output[outputIdx + 1] = magnitude;
          output[outputIdx + 2] = magnitude;
          output[outputIdx + 3] = 255;
        }
      }

      const outputImageData = new ImageData(output, width, height);
      context.putImageData(outputImageData, 0, 0);
    },
    [width, height],
  );

  return (
    <AbsoluteFill>
      <OffthreadVideo 
        src="video.mp4" 
        onVideoFrame={detectEdges}
        style={{ opacity: 0 }}
      />
      <canvas ref={canvas} width={width} height={height} />
    </AbsoluteFill>
  );
};
```

### 5. 多重效果组合

```tsx
const MultiEffectProcessor: React.FC<{
  effects: Array<'blur' | 'brightness' | 'contrast' | 'saturation'>;
  intensity: number;
}> = ({ effects, intensity }) => {
  const canvas = useRef<HTMLCanvasElement>(null);
  const { width, height } = useVideoConfig();

  const applyEffects = useCallback(
    (frame: CanvasImageSource) => {
      if (!canvas.current) return;
      
      const context = canvas.current.getContext('2d');
      if (!context) return;

      // 应用 CSS 滤镜
      let filterString = '';
      
      effects.forEach(effect => {
        switch (effect) {
          case 'blur':
            filterString += `blur(${intensity * 5}px) `;
            break;
          case 'brightness':
            filterString += `brightness(${1 + intensity}) `;
            break;
          case 'contrast':
            filterString += `contrast(${1 + intensity}) `;
            break;
          case 'saturation':
            filterString += `saturate(${1 + intensity}) `;
            break;
        }
      });

      context.filter = filterString;
      context.drawImage(frame, 0, 0, width, height);
      context.filter = 'none'; // 重置滤镜
    },
    [effects, intensity, width, height],
  );

  return (
    <AbsoluteFill>
      <OffthreadVideo 
        src="video.mp4" 
        onVideoFrame={applyEffects}
        style={{ opacity: 0 }}
      />
      <canvas ref={canvas} width={width} height={height} />
    </AbsoluteFill>
  );
};
```

## 重要注意事项

### 环境差异
- **预览时**: `frame` 是 `HTMLVideoElement`
- **渲染时**: `frame` 是 `HTMLImageElement`
- 两种环境下都可以作为 `CanvasImageSource` 使用

### 性能考虑
- 每帧都会调用回调函数，避免重复的复杂计算
- 使用 `useCallback` 优化回调函数
- 考虑使用 Web Workers 处理复杂的图像处理

### 跨域设置
当使用 `onVideoFrame` 时，默认 `crossOrigin` 为 `"anonymous"`：

```tsx
<OffthreadVideo
  src="https://example.com/video.mp4"
  onVideoFrame={handleFrame}
  crossOrigin="anonymous" // 默认设置
/>
```

## 错误处理

```tsx
const SafeVideoProcessor: React.FC = () => {
  const handleVideoFrame = useCallback(
    (frame: CanvasImageSource) => {
      try {
        // 处理帧数据
        processFrame(frame);
      } catch (error) {
        console.error('帧处理错误:', error);
        // 可以选择跳过这一帧或应用默认处理
      }
    },
    [],
  );

  return (
    <OffthreadVideo
      src="video.mp4"
      onVideoFrame={handleVideoFrame}
      onError={(error) => {
        console.error('视频加载错误:', error);
      }}
    />
  );
};
```

## 性能优化建议

1. **避免重复计算**: 缓存不变的计算结果
2. **使用 Web Workers**: 将复杂处理移到后台线程
3. **降低处理频率**: 可以跳过某些帧来提高性能
4. **优化算法**: 使用高效的图像处理算法

## 常见用例

- **绿幕/蓝幕效果**: 背景替换和合成
- **实时滤镜**: 颜色调整、模糊、锐化等
- **运动分析**: 检测和跟踪物体运动
- **内容分析**: 场景检测、人脸识别等
- **艺术效果**: 边缘检测、油画效果等

## 参考资源

- **官方文档**: https://www.remotion.dev/docs/offthreadvideo#onvideoframe
- **视频操作指南**: https://www.remotion.dev/docs/video-manipulation
- **Canvas API**: https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API
- **图像处理算法**: 各种计算机视觉和图像处理技术
