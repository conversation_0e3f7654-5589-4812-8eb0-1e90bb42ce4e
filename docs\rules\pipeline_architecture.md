# 流水线架构实现规则

本规则定义了如何在项目中实现和扩展流水线架构模式。使用 `PipelineBase` 和 `TaskBase` 类作为基础，为复杂的异步处理流程提供一致的结构。

## 流水线架构概述

流水线架构将复杂处理流程分解为一系列独立、有序的任务，具有模块化、数据共享、顺序执行、错误隔离和易于扩展的特点。项目中的基本组件包括：

- 基类 (`backend/src/class/`)：`PipelineBase` 和 `TaskBase`
- 具体流水线 (`backend/src/pipelines/`)：如 `VideoProcessingPipelineService`
- 流水线测试 (`backend/src/pipelines/tests/`)：流水线集成测试
- 具体任务 (`backend/src/tasks/`)：如 `ConvertToAudioTask`、`GetTranscriptionTask`
- 任务测试 (`backend/src/tasks/tests/`)：任务单元测试

## 流水线服务类创建标准

### 1. 文件结构和基础架构

```javascript
/**
 * @功能概述: [流水线服务的整体目标和处理流程]
 *           [此服务编排多个任务的具体描述]
 * 
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 */

const PipelineBase = require('../class/PipelineBase');
const { Task1, Task2, Task3 } = require('../tasks');
const logger = require('../utils/logger');
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');

// 模块级日志前缀 - 统一格式
const moduleLogPrefix = `[文件：[服务类文件名].js][服务类中文名][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录，任务通过 services/ 目录调用外部API`);
```

### 2. 构造函数标准实现

```javascript
class MyPipelineService {
    /**
     * @功能概述: 构造函数。初始化处理流水线并添加所需任务。
     * @param {string} [reqId='unknown_req'] - 请求ID，用于日志追踪。
     */
    constructor(reqId = 'unknown_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：[服务类文件名].js][服务类中文名][ReqID:${this.reqId}]`;

        // 创建流水线实例
        this.processingPipeline = new PipelineBase(`[流水线名称]-${this.reqId}`);

        // 按执行顺序统一添加所有任务
        this.addAllTasks();
        
        // 记录最终任务序列
        logger.info(`${this.logPrefix}[流水线名称] Pipeline 已创建，包含完整任务序列: Task1 → Task2 → Task3`);
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     */
    addAllTasks() {
        // 按执行顺序添加任务
        this.addTask1();
        this.addTask2();
        this.addTask3();
    }
}
```

## 任务添加注释标准

### 详细注释模板

每个任务添加都必须包含以下结构化注释：

```javascript
/**
 * @功能概述: 添加[任务中文名] ([任务类名]) 到流水线。
 *           此任务负责[具体功能描述]。
 *
 * @上下文输入 (context 预期的字段):
 *   - 字段名1: {类型} (必需/可选) 字段用途和来源说明
 *   - 字段名2: {类型} (必需/可选) 字段用途和来源说明
 *   - config.配置项: {类型} (来自配置模块) 配置项说明
 *
 * @执行后上下文状态 ([任务类名] 完成后 context 对象的完整内容):
 *   // === 来自初始输入 ===
 *   - reqId: {string} 请求追踪ID
 *   - 其他初始字段: {类型} 字段说明
 *   
 *   // === 来自 [前序任务名] (前序任务) ===
 *   - 前序任务输出字段1: {类型} 字段说明
 *   - 前序任务输出字段2: {类型} 字段说明
 *   
 *   // === 来自 [当前任务名] (当前任务新增) ===
 *   - 当前任务输出字段1: {类型} 字段说明
 *   - 当前任务输出字段2: {类型} 字段说明
 *   (任务内部会更新自身的 this.status 和 this.result)
 */
addTaskX() {
    this.processingPipeline.addTask(new TaskX());
}
```

### 最终任务注释增强

对于流水线的最后一个任务，需要额外添加完整输出摘要：

```javascript
/**
 * @功能概述: 添加最终处理任务...
 * 
 * @执行后上下文状态 (最终任务完成后 context 对象的完整内容):
 *   // ... 前面的所有状态 ...
 *   
 *   // === 来自 [最终任务名] (第N个任务，当前任务新增) ===
 *   - 最终输出字段1: {类型} 主要输出说明
 *   - 最终输出字段2: {类型} 次要输出说明
 *   
 *   // === 最终流水线输出摘要 ===
 *   // 此时 context 包含完整的处理结果，主要可用数据：
 *   // 1. 主要输出字段 - 用途说明
 *   // 2. 次要输出字段 - 用途说明
 *   // 3. 中间产物字段 - 复用说明
 *   // 4. 其他可用字段 - 用途说明
 */
```

## 流水线结果标准化

### 标准化工具概述

为了统一所有流水线的最终返回格式，简化前端处理逻辑，项目使用 `pipelineResultStandardizer.js` 通用工具对流水线执行结果进行标准化处理。

**设计理念：** 通用化、零配置、自动识别，任何新流水线都可以直接使用，无需修改工具代码。

### 标准化工具位置

**文件路径：** `backend/src/utils/pipelineResultStandardizer.js`

### 导入和使用方式

#### 1. 导入标准化工具

```javascript
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');
```

#### 2. 在流水线服务中调用（超简单）

```javascript
// 在流水线服务的主要处理方法返回前调用
async processMain(initialContext, serviceProgressCallback) {
    // ... 流水线执行逻辑 ...

    // 执行流水线
    const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);

    // 步骤 6: 标准化返回结果（所有参数可选，自动识别）
    const standardizedResult = standardizePipelineResult(result, '[流水线类型]', currentContext.reqId);
    // 或者更简单：const standardizedResult = standardizePipelineResult(result);
    logger.info(`${this.logPrefix}[processMain] 结果已标准化，状态: ${standardizedResult.status}`);

    return standardizedResult;
}
```

### 通用自动识别机制

**核心特点：** 无需预定义流水线类型，自动识别所有字段，零配置使用。

#### 自动文件路径识别
- **识别规则：** 自动提取包含 `'Path'`、`'FilePath'`、`'File'` 的字符串字段
- **示例：** `originalVideoPath`、`audioFilePath`、`assFile` 等自动识别

#### 自动统计信息提取
- **识别规则：** 自动提取包含 `'Duration'`、`'Count'`、`'Size'` 的数值字段
- **示例：** `audioDuration`、`subtitleCount`、`videoFileSize` 等自动识别

#### 自动URL转换
- **转换规则：** 包含 `'uploads'` 的路径自动转换为Web访问URL
- **格式：** `http://localhost:8081/backend/uploads/...`

### 标准化后的返回格式

```javascript
{
    // === 基础信息 ===
    status: 'completed' | 'failed',           // 执行状态
    reqId: 'req-uuid-12345',                  // 请求ID
    pipelineType: 'upload' | 'generation',   // 流水线类型
    timestamp: '2025-06-13T10:44:23.000Z',   // 完成时间

    // === 核心文件路径（服务器路径） ===
    files: {
        originalVideo: '/path/to/original.mp4',
        finalVideo: '/path/to/final.mp4',
        audio: '/path/to/audio.mp3',
        ass: '/path/to/subtitles.ass',
        // ... 其他关键文件
    },

    // === Web访问URL ===
    urls: {
        originalVideo: 'http://localhost:8081/backend/uploads/original.mp4',
        finalVideo: 'http://localhost:8081/backend/uploads/final.mp4',
        audio: 'http://localhost:8081/backend/uploads/audio.mp3',
        // ... 对应的URL
    },

    // === 处理统计 ===
    stats: {
        totalTasks: 9,
        completedTasks: 9,
        audioDuration: 30.35,
        videoDuration: 91.04,
        videoResolution: '1080x1920',
        repeatCount: 3,
        subtitleCount: 8
    },

    // === 原始context（保留兼容性） ===
    originalContext: { /* 完整的原始context */ }
}
```

### 实际应用示例

#### VideoProcessingPipelineService 示例

**文件：** `backend/src/pipelines/videoProcessingPipelineService.js`

```javascript
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');

class VideoProcessingPipelineService {
    async processUploadedVideo(initialContext, serviceProgressCallback) {
        // ... 流水线执行逻辑 ...

        const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);

        // 步骤 6: 标准化返回结果
        const standardizedResult = standardizePipelineResult(result, 'upload', currentContext.reqId);
        logger.info(`${this.logPrefix}[processUploadedVideo] 结果已标准化，状态: ${standardizedResult.status}`);

        return standardizedResult;
    }
}
```

#### VideoGenerationPipelineService 示例

**文件：** `backend/src/pipelines/videoGenerationPipelineService.js`

```javascript
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');

class VideoGenerationPipelineService {
    async processVideoGeneration(initialContext, serviceProgressCallback) {
        // ... 流水线执行逻辑 ...

        const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);

        // 步骤 6: 标准化返回结果
        const standardizedResult = standardizePipelineResult(result, 'generation', currentContext.reqId);
        logger.info(`${this.logPrefix}[processVideoGeneration] 结果已标准化，状态: ${standardizedResult.status}`);

        return standardizedResult;
    }
}
```

### 精简后的核心优势

1. **零配置使用**：新流水线无需修改任何工具代码，导入即用
2. **自动智能识别**：自动识别文件路径、统计信息，无需手动配置
3. **完全通用化**：一套代码适配所有流水线类型，无需switch-case
4. **极简调用方式**：所有参数可选，最简单只需一个参数
5. **向后完全兼容**：现有流水线调用无需任何修改
6. **统一返回格式**：所有流水线返回相同的标准化格式

### 新人开发指导（超简单）

#### 创建新流水线服务时：

1. **导入标准化工具**：
   ```javascript
   const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');
   ```

2. **在主要处理方法返回前调用（三种方式任选）**：
   ```javascript
   // 方式1：完整参数
   const standardizedResult = standardizePipelineResult(result, '流水线名称', reqId);

   // 方式2：简化参数
   const standardizedResult = standardizePipelineResult(result, '流水线名称');

   // 方式3：最简参数（推荐）
   const standardizedResult = standardizePipelineResult(result);

   return standardizedResult;
   ```

3. **完成！无需其他配置**：
   - ✅ 文件路径自动识别（包含Path/FilePath/File的字段）
   - ✅ 统计信息自动提取（包含Duration/Count/Size的数值字段）
   - ✅ URL自动转换（包含uploads的路径）
   - ✅ 原始context完整保留

#### 字段命名建议（自动识别）：

为了更好地利用自动识别功能，建议在context中使用以下命名规范：

- **文件路径字段**：以 `Path`、`FilePath`、`File` 结尾
  - 示例：`originalVideoPath`、`audioFilePath`、`assFile`
- **统计信息字段**：以 `Duration`、`Count`、`Size` 结尾
  - 示例：`audioDuration`、`subtitleCount`、`videoFileSize`

#### 无需扩展：

**重要：** 新流水线无需修改 `pipelineResultStandardizer.js` 文件，工具会自动识别所有符合命名规范的字段。

### 精简前后对比

#### 精简前的问题：
```javascript
// 复杂的switch-case逻辑，每个新流水线都要修改
function extractFilePaths(context, pipelineType) {
    switch (pipelineType) {
        case 'upload':
            files.originalVideo = context.originalVideoPath;
            files.audio = context.audioFilePath;
            // ... 10多行特定配置
            break;
        case 'generation':
            files.finalVideo = context.finalVideoPath;
            // ... 又是10多行特定配置
            break;
        // 新流水线需要在这里添加新的case
    }
}
```

#### 精简后的解决方案：
```javascript
// 通用自动识别，无需修改
function extractAllFilePaths(context) {
    const files = {};
    Object.keys(context).forEach(key => {
        if (key.includes('Path') || key.includes('FilePath') || key.includes('File')) {
            files[key] = context[key];  // 自动识别所有文件字段
        }
    });
    return files;
}
```

#### 开发体验对比：

| 方面 | 精简前 | 精简后 |
|------|--------|--------|
| 新流水线开发 | 需要修改工具代码 | 零配置，直接使用 |
| 代码复杂度 | 复杂switch-case | 简单通用逻辑 |
| 维护成本 | 每个流水线都要维护 | 一次编写，永久使用 |
| 学习成本 | 需要理解复杂规则 | 只需了解命名规范 |
| 扩展性 | 硬编码，难扩展 | 自动识别，易扩展 |

## 流水线执行方法标准

### 主要处理方法实现

```javascript
/**
 * @功能概述: 执行完整的[业务流程]，包括[主要步骤1]和[主要步骤2]。
 * @param {object} initialContext - 初始上下文，必须包含:
 *                                 - 必需字段1: 类型 - 用途说明
 *                                 - 必需字段2: 类型 - 用途说明
 *                                 - (可选) reqId: string - 请求ID，会传递给各任务
 * @param {function} serviceProgressCallback - 服务进度回调函数，用于报告服务执行状态
 * @returns {Promise<object>} Pipeline 执行结果，包含 status, context, tasks。
 *                            成功时，context 中应包含:
 *                              - 输出字段1: 类型 (来自 Task1)
 *                              - 输出字段2: 类型 (来自 Task2)
 *                              - 最终输出: 类型 (来自 TaskN)
 */
async processMain(initialContext, serviceProgressCallback) {
    logger.info(`${this.logPrefix}[processMain] 开始执行[业务流程] ([主要步骤描述])。`);
    
    // 步骤 1: 构建执行上下文
    const currentContext = {
        reqId: this.reqId,
        ...initialContext
    };
    
    // 步骤 2: 参数校验
    if (!currentContext.必需字段1 || !currentContext.必需字段2) {
        const errorMsg = 'processMain 调用失败：initialContext 必须包含 [必需字段列表]。';
        logger.error(`${this.logPrefix}[processMain] ${errorMsg}`);
        
        // 步骤 2.1: 参数校验失败时的回调处理
        if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
            try {
                logger.debug(`${this.logPrefix}[processMain] 参数校验失败，调用服务进度回调。`);
                serviceProgressCallback({
                    serviceName: '[服务类名]',
                    methodName: 'processMain',
                    status: 'failed_setup',
                    error: { message: errorMsg },
                    timestamp: new Date().toISOString()
                });
            } catch (cbError) {
                logger.error(`${this.logPrefix}[processMain] 服务进度回调执行出错: ${cbError.message}`);
            }
        }
        
        return { 
            status: 'failed', 
            error: new Error(errorMsg), 
            context: currentContext, 
            tasks: [] 
        };
    }
    
    // 步骤 3: 记录任务序列
    logger.info(`${this.logPrefix}[processMain][步骤 1] 流水线任务序列确认: Task1 → Task2 → TaskN`);
    
    // 步骤 4: 执行流水线
    logger.debug(`${this.logPrefix}[processMain] 执行内部流水线，并传递回调函数。`);
    const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);
    logger.info(`${this.logPrefix}[processMain] [业务流程]执行完毕。Status: ${result.status}`);
    
    // 步骤 5: 记录关键输出
    if (result.context) {
        const { 关键输出字段1, 关键输出字段2, 最终输出字段 } = result.context;
        logger.debug(`${this.logPrefix}[processMain] 最终上下文关键信息: 字段1=${关键输出字段1}, 字段2=${关键输出字段2}, 最终字段长度=${最终输出字段 ? 最终输出字段.length : '未知'}`);
    }
    
    // 步骤 6: 标准化返回结果
    const standardizedResult = standardizePipelineResult(result, '[流水线类型]', currentContext.reqId);
    logger.info(`${this.logPrefix}[processMain] 结果已标准化，状态: ${standardizedResult.status}`);

    return standardizedResult;
}
```

## 日志记录标准

### 日志前缀分层结构

1. **模块级日志前缀**：
```javascript
const moduleLogPrefix = `[文件：[文件名].js][模块中文名][模块初始化]`;
```

2. **实例级日志前缀**：
```javascript
this.logPrefix = `[文件：[文件名].js][模块中文名][ReqID:${this.reqId}]`;
```

3. **方法级日志前缀**：
```javascript
const methodLogPrefix = `${this.logPrefix}[方法名]`;
```

### 关键记录点

1. **模块加载记录**：
```javascript
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 具体架构验证信息`);
```

2. **流水线创建记录**：
```javascript
logger.info(`${this.logPrefix}[流水线名] Pipeline 已创建，包含完整任务序列: Task1 → Task2 → TaskN`);
```

3. **执行流程记录**：
```javascript
logger.info(`${this.logPrefix}[方法名] 开始执行[业务描述]。`);
logger.info(`${this.logPrefix}[方法名][步骤 X] 步骤描述`);
logger.debug(`${this.logPrefix}[方法名] 详细调试信息`);
logger.info(`${this.logPrefix}[方法名] 执行完毕。Status: ${result.status}`);
```

4. **关键数据记录**：
```javascript
logger.debug(`${this.logPrefix}[方法名] 最终上下文关键信息: 字段1=${值1}, 字段2=${值2}`);
```

## 错误处理和回调机制

### 参数校验错误处理

```javascript
if (!requiredParam) {
    const errorMsg = '方法调用失败：参数校验详情';
    logger.error(`${this.logPrefix}[方法名] ${errorMsg}`);
    
    // 尝试调用服务进度回调
    if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
        try {
            logger.debug(`${this.logPrefix}[方法名] 参数校验失败，调用服务进度回调。`);
            serviceProgressCallback({
                serviceName: '服务类名',
                methodName: '方法名',
                status: 'failed_setup',
                error: { message: errorMsg },
                timestamp: new Date().toISOString()
            });
        } catch (cbError) {
            logger.error(`${this.logPrefix}[方法名] 服务进度回调执行出错: ${cbError.message}`);
        }
    }
    
    return { status: 'failed', error: new Error(errorMsg), context, tasks: [] };
}
```

### 服务进度回调传递

```javascript
// 将服务层回调传递给流水线层
const result = await this.processingPipeline.execute(context, serviceProgressCallback);
```

## 架构验证注释

每个流水线服务文件都必须包含架构验证注释：

```javascript
/**
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 */
```

## 流水线测试架构

### 测试文件组织结构

流水线测试采用简单的单文件组织结构：

```
backend/src/pipelines/tests/
└── [PipelineServiceName].test.js          # 单一测试文件，包含所有测试逻辑
```

### 流水线测试理念

针对流水线服务的特点，特别是LLM任务耗时较长的情况，测试设计遵循以下原则：

1. **分层测试策略**：快速测试（不调用LLM）+ 完整测试（可选的LLM调用）
2. **Mock优先**：默认使用模拟数据，避免昂贵的外部API调用
3. **可选真实调用**：通过环境变量控制是否进行真实LLM测试
4. **关键路径覆盖**：重点测试参数校验、数据传递、错误处理
5. **简单实用**：单文件包含所有测试，易于运行和维护

### 测试文件模板

每个流水线服务的测试文件应遵循以下模板：

```javascript
/**
 * [PipelineServiceName] 测试文件
 * 运行方式：node [PipelineServiceName].test.js
 * 环境变量：
 *   - RUN_LLM_TESTS=true  启用真实LLM调用测试（耗时较长）
 *   - TEST_MODE=fast      仅运行快速测试（默认）
 */

const [PipelineServiceName] = require('../[PipelineServiceName]');
const logger = require('../utils/logger');

// 测试配置
const TEST_CONFIG = {
    runLLMTests: process.env.RUN_LLM_TESTS === 'true',
    testMode: process.env.TEST_MODE || 'fast',
    timeout: process.env.RUN_LLM_TESTS === 'true' ? 300000 : 10000 // 5分钟 vs 10秒
};

// 测试日志前缀
const testLogPrefix = '[文件：[PipelineServiceName].test.js][流水线服务测试][测试执行]';

logger.info(`${testLogPrefix} 🧪 开始测试 [PipelineServiceName]`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} 🤖 LLM测试: ${TEST_CONFIG.runLLMTests ? '启用' : '禁用'}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 测试数据构建器
function buildValidContext(overrides = {}) {
    return {
        reqId: 'test_req_' + Date.now(),
        // 根据实际流水线需求添加必需字段
        必需字段1: '有效值1',
        必需字段2: '有效值2',
        ...overrides
    };
}

function buildInvalidContext() {
    return {
        reqId: 'invalid_test_req'
        // 故意缺少必需字段
    };
}

// Mock数据（用于快速测试）
const mockApiResponse = {
    // 模拟真实API响应结构
    text: "Mock transcription text",
    segments: [
        { id: 1, start: 0, end: 5, text: "Hello" },
        { id: 2, start: 5, end: 10, text: "World" }
    ]
};

// 测试统计
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
};

// 简单测试框架（基于logger）
function test(name, testFn, skipCondition = false) {
    testStats.total++;
    
    if (skipCondition) {
        logger.info(`${testLogPrefix} ⏭️  SKIP: ${name}`);
        testStats.skipped++;
        return;
    }
    
    logger.debug(`${testLogPrefix} ▶️  START: ${name}`);
    
    return Promise.resolve()
        .then(() => testFn())
        .then(() => {
            logger.info(`${testLogPrefix} ✅ PASS: ${name}`);
            testStats.passed++;
        })
        .catch((error) => {
            logger.error(`${testLogPrefix} ❌ FAIL: ${name}`);
            logger.error(`${testLogPrefix}    错误: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix}    堆栈: ${error.stack.split('\n')[1]?.trim()}`);
            }
            testStats.failed++;
        });
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`期望 ${expected}，实际 ${actual}`);
            }
        },
        toEqual: (expected) => {
            if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                throw new Error(`期望 ${JSON.stringify(expected)}，实际 ${JSON.stringify(actual)}`);
            }
        },
        toHaveProperty: (prop) => {
            if (!(prop in actual)) {
                throw new Error(`期望包含属性 ${prop}`);
            }
        },
        toContain: (item) => {
            if (!actual.includes(item)) {
                throw new Error(`期望包含 ${item}`);
            }
        },
        toThrow: async (fn) => {
            let thrown = false;
            try {
                await fn();
            } catch (e) {
                thrown = true;
            }
            if (!thrown) {
                throw new Error('期望抛出异常但没有');
            }
        }
    };
}

// 主要测试套件
async function runTests() {
    const startTime = Date.now();
    
    // 1. 基础功能测试（快速）
    await test('实例化服务应该成功', async () => {
        const service = new @PipelineServiceName;
        expect(service.reqId).toBe('test_req');
        expect(service.processingPipeline).toHaveProperty('execute');
    });

    // 2. 参数校验测试（快速）
    await test('参数校验失败应该返回错误', async () => {
        const service = new @PipelineServiceName;
        const invalidContext = buildInvalidContext();
        
        const result = await service.processMain(invalidContext);
        
        expect(result.status).toBe('failed');
        expect(result.error.message).toContain('必须包含');
    });

    // 3. Mock数据测试（快速）
    await test('使用Mock数据应该成功执行', async () => {
        const service = new @PipelineServiceName;
        const mockContext = buildValidContext({
            // 为LLM任务提供mock数据，避免真实调用
            transcriptionStatus: 'success',
            apiResponse: mockApiResponse,
            // 其他mock字段...
        });
        
        // 监听进度回调
        const progressEvents = [];
        const progressCallback = (data) => progressEvents.push(data);
        
        const result = await service.processMain(mockContext, progressCallback);
        
        expect(result.status).toBe('completed');
        expect(result.context).toHaveProperty('最终输出字段');
        expect(progressEvents.length).toBe(3); // started, running, completed
    }, !TEST_CONFIG.runLLMTests); // 当启用LLM测试时跳过mock测试

    // 4. 错误处理测试（快速）
    await test('中间任务失败应该终止流水线', async () => {
        const service = new @PipelineServiceName;
        const contextWithFailure = buildValidContext({
            // 设置会导致某个任务失败的条件
            someField: 'invalid_value_that_causes_failure'
        });
        
        const result = await service.processMain(contextWithFailure);
        
        expect(result.status).toBe('failed');
        // 验证部分任务完成，后续任务未执行
    });

    // 5. 回调异常隔离测试（快速）
    await test('回调异常不应该影响流水线执行', async () => {
        const service = new @PipelineServiceName;
        const validContext = buildValidContext();
        
        const throwingCallback = () => {
            throw new Error('测试回调异常');
        };
        
        // 应该不抛出异常
        const result = await service.processMain(validContext, throwingCallback);
        expect(result.status).toBe('completed');
    });

    // 6. 真实LLM测试（可选，耗时）
    await test('真实LLM调用完整流程测试', async () => {
        const service = new @PipelineServiceName;
        const realContext = buildValidContext({
            // 使用真实数据，触发LLM调用
            uploadedVideoPath: '/path/to/real/test/video.mp4',
            originalVideoName: 'test_video.mp4'
        });
        
        logger.warn(`${testLogPrefix} ⚠️  正在执行真实LLM调用，这可能需要几分钟...`);
        const startTime = Date.now();
        
        const result = await service.processMain(realContext);
        
        const duration = Date.now() - startTime;
        logger.info(`${testLogPrefix} ⏱️  LLM测试耗时: ${(duration / 1000).toFixed(2)}秒`);
        
        expect(result.status).toBe('completed');
        expect(result.context).toHaveProperty('englishSrtContent');
        expect(typeof result.context.englishSrtContent).toBe('string');
        expect(result.context.englishSrtContent.length).toBe(/* 期望最小长度 */);
    }, !TEST_CONFIG.runLLMTests); // 仅在启用LLM测试时运行

    // 输出测试结果
    const totalTime = Date.now() - startTime;
    logger.info(`${testLogPrefix} 📊 测试结果统计:`);
    logger.info(`${testLogPrefix}    总计: ${testStats.total}`);
    logger.info(`${testLogPrefix}    通过: ${testStats.passed} ✅`);
    logger.info(`${testLogPrefix}    失败: ${testStats.failed} ❌`);
    logger.info(`${testLogPrefix}    跳过: ${testStats.skipped} ⏭️`);
    logger.info(`${testLogPrefix}    耗时: ${(totalTime / 1000).toFixed(2)}秒`);
    
    if (testStats.failed > 0) {
        logger.error(`${testLogPrefix} 💥 测试失败！有 ${testStats.failed} 个用例失败`);
        // 保留关键结果输出到控制台
        console.log(`❌ 测试失败: ${testStats.failed}/${testStats.total} 用例失败`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试通过！`);
        // 保留成功结果输出到控制台
        console.log(`✅ 测试成功: ${testStats.passed}/${testStats.total} 用例通过`);
        process.exit(0);
    }
}

// 错误处理（使用logger）
process.on('uncaughtException', (error) => {
    logger.error(`${testLogPrefix} 💥 未捕获异常: ${error.message}`);
    logger.error(`${testLogPrefix} 异常堆栈: ${error.stack}`);
    console.log(`❌ 测试异常终止: ${error.message}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error(`${testLogPrefix} 💥 未处理的Promise拒绝:`, reason);
    logger.error(`${testLogPrefix} Promise对象:`, promise);
    console.log(`❌ 测试Promise拒绝: ${reason}`);
    process.exit(1);
});

// 超时处理（使用logger）
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 运行测试（使用logger）
runTests().catch((error) => {
    logger.error(`${testLogPrefix} 💥 测试执行失败: ${error.message}`);
    logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    console.log(`❌ 测试执行失败: ${error.message}`);
    process.exit(1);
});
```

### 测试运行方式

#### 快速测试（默认）
```bash
# 运行快速测试，使用Mock数据，避免LLM调用
node VideoProcessingPipelineService.test.js

# 或者明确指定快速模式
TEST_MODE=fast node VideoProcessingPipelineService.test.js
```

#### 完整测试（包含LLM）
```bash
# 运行包含真实LLM调用的完整测试（耗时较长）
RUN_LLM_TESTS=true node VideoProcessingPipelineService.test.js

# 设置更长的超时时间
RUN_LLM_TESTS=true TIMEOUT=600000 node VideoProcessingPipelineService.test.js
```

### 测试最佳实践

1. **开发期间**：主要运行快速测试，确保基本逻辑正确
2. **部署前**：运行完整测试，验证LLM集成的正确性
3. **CI/CD**：配置快速测试作为基础检查，完整测试作为可选步骤
4. **Mock数据维护**：定期更新Mock数据，确保与真实API响应结构一致
5. **错误模拟**：设计特定的输入条件来触发各种错误场景
6. **性能监控**：记录LLM测试的耗时，识别性能问题
7. **日志集成**：
   - **详细调试**：测试详细信息通过logger记录到日志文件，便于问题追踪
   - **关键结果**：仅将最重要的测试结果输出到控制台，保持简洁
   - **统一前缀**：使用标准日志前缀格式，与应用日志保持一致
   - **错误追踪**：异常和失败信息完整记录到日志，包含堆栈信息
   - **日志级别**：合理使用logger.debug/info/warn/error，便于日志筛选

## 完整模板示例

基于 `videoProcessingPipelineService.js` 的标准模板：

```javascript
/**
 * @功能概述: [流水线服务功能描述]
 *           [此服务编排多个任务的具体描述]
 * 
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 */

const PipelineBase = require('../class/PipelineBase');
const { Task1, Task2, Task3 } = require('../tasks');
const logger = require('../utils/logger');
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');

const moduleLogPrefix = `[文件：[文件名].js][服务中文名][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录，任务通过 services/ 目录调用外部API`);

class MyPipelineService {
    constructor(reqId = 'unknown_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：[文件名].js][服务中文名][ReqID:${this.reqId}]`;
        this.processingPipeline = new PipelineBase(`[流水线名]-${this.reqId}`);

        // 统一添加所有任务（包含详细注释）
        this.addTask1();
        this.addTask2();
        this.addTask3();
        
        logger.info(`${this.logPrefix}[流水线名] Pipeline 已创建，包含完整任务序列: Task1 → Task2 → Task3`);
    }

    // 详细的任务添加方法（每个都有完整注释）...

    /**
     * @功能概述: 执行完整的[业务流程]处理
     * @param {object} initialContext - 初始上下文
     * @param {function} serviceProgressCallback - 服务进度回调函数
     * @returns {Promise<object>} 标准化的流水线执行结果
     */
    async processMain(initialContext, serviceProgressCallback) {
        // ... 流水线执行逻辑 ...

        const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);

        // 步骤 6: 标准化返回结果
        const standardizedResult = standardizePipelineResult(result, '[流水线类型]', currentContext.reqId);
        logger.info(`${this.logPrefix}[processMain] 结果已标准化，状态: ${standardizedResult.status}`);

        return standardizedResult;
    }
}

module.exports = MyPipelineService;
```

## 最佳实践总结

基于 `videoProcessingPipelineService.js` 的优秀开发理念：

1. **统一任务管理**：所有任务在构造函数中按顺序添加，每个任务有独立的添加方法和详细注释
2. **上下文状态跟踪**：使用 `@执行后上下文状态` 详细记录每个任务执行后的完整上下文内容
3. **分层日志记录**：模块级、实例级、方法级日志前缀，记录关键执行步骤和数据状态
4. **完整错误处理**：参数校验、回调错误处理、详细错误日志记录
5. **架构验证**：明确各层职责，确保架构分层的正确性
6. **进度透明化**：通过回调机制提供细粒度的执行进度报告
7. **可维护性**：结构化注释、统一命名规范、清晰的数据流跟踪
8. **结果标准化**：使用 `pipelineResultStandardizer.js` 统一所有流水线的返回格式，简化前端处理
9. **测试全面性**：端到端测试、真实数据模拟、错误场景覆盖、性能验证

这些标准确保了流水线服务的一致性、可维护性和可调试性，为项目的持续发展提供了坚实的基础。

## 参考资料

- 任务开发详细标准：@task_development_standard.mdc
- SSE 事件数据结构标准：@sse_event_standard.mdc
- 代码注释标准：@code-commenting-standard.mdc

