# Easing

## 概述

`Easing` 模块实现了常见的缓动函数，可以与 [`interpolate()`](./interpolate.md) API 一起使用，为动画提供自然流畅的过渡效果。

您可以在 [http://easings.net/](http://easings.net/) 找到一些常见缓动函数的可视化演示。

## 语法

```typescript
import { Easing, interpolate } from "remotion";

const value = interpolate(frame, [0, 100], [0, 1], {
  easing: Easing.bezier(0.8, 0.22, 0.96, 0.65)
});
```

## 预定义动画

### back
提供基础动画，对象在向前移动之前会稍微向后移动

### bounce
提供弹跳动画效果

### ease
提供基础惯性交互，类似于对象缓慢加速到目标速度

### elastic
提供基础弹性交互，类似于弹簧来回振荡

## 标准函数

### linear
线性函数，`f(t) = t`，位置与经过时间一对一相关

### quad
二次函数，`f(t) = t * t`，位置等于经过时间的平方

### cubic
三次函数，`f(t) = t * t * t`，位置等于经过时间的立方

### poly(n)
幂函数，位置等于经过时间的第N次幂

## 附加函数

### bezier(x1, y1, x2, y2)
提供三次贝塞尔曲线，等同于CSS过渡的 `transition-timing-function`

### circle
提供圆形函数

### sin
提供正弦函数

### exp
提供指数函数

## 修饰符函数

### in(easing)
向前运行缓动函数

### out(easing)
向后运行缓动函数

### inOut(easing)
使任何缓动函数对称，前半段向前运行，后半段向后运行

## 基础用法

### 1. 基础缓动应用

```typescript
import { Easing, interpolate, useCurrentFrame, AbsoluteFill } from "remotion";

const BasicEasing: React.FC = () => {
  const frame = useCurrentFrame();
  
  const scale = interpolate(frame, [0, 100], [0, 1], {
    easing: Easing.bezier(0.8, 0.22, 0.96, 0.65),
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <AbsoluteFill
      style={{
        transform: `scale(${scale})`,
        backgroundColor: "red"
      }}
    />
  );
};
```

### 2. 步进函数

```typescript
import { Easing, interpolate, useCurrentFrame } from "remotion";

const StepFunctions: React.FC = () => {
  const frame = useCurrentFrame();

  // step0: 对于任何正值返回1
  const step0Value = interpolate(frame, [0, 60], [0, 1], {
    easing: Easing.step0
  });

  // step1: 如果n >= 1则返回1
  const step1Value = interpolate(frame, [0, 60], [0, 1], {
    easing: Easing.step1
  });

  return (
    <div style={{
      display: "flex",
      flexDirection: "column",
      gap: 20,
      padding: 40
    }}>
      <div style={{
        width: `${step0Value * 300}px`,
        height: 40,
        backgroundColor: "#e74c3c"
      }}>
        Step0 函数
      </div>
      <div style={{
        width: `${step1Value * 300}px`,
        height: 40,
        backgroundColor: "#3498db"
      }}>
        Step1 函数
      </div>
    </div>
  );
};
```

## 实际应用场景

### 1. 缓动函数比较工具

```typescript
import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const EasingComparison: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const easingFunctions = [
    { name: "Linear", easing: Easing.linear, color: "#e74c3c" },
    { name: "Ease", easing: Easing.ease, color: "#3498db" },
    { name: "Ease In", easing: Easing.in(Easing.ease), color: "#2ecc71" },
    { name: "Ease Out", easing: Easing.out(Easing.ease), color: "#f39c12" },
    { name: "Ease In Out", easing: Easing.inOut(Easing.ease), color: "#9b59b6" },
    { name: "Bounce", easing: Easing.bounce, color: "#1abc9c" },
    { name: "Elastic", easing: Easing.elastic(1), color: "#e67e22" },
    { name: "Back", easing: Easing.back(1.5), color: "#34495e" }
  ];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#f8f9fa",
      padding: 40
    }}>
      <h2 style={{
        textAlign: "center",
        marginBottom: 40,
        color: "#2c3e50"
      }}>
        缓动函数比较
      </h2>

      {easingFunctions.map((func, index) => {
        const progress = interpolate(
          frame,
          [0, durationInFrames],
          [0, 1],
          {
            easing: func.easing,
            extrapolateLeft: "clamp",
            extrapolateRight: "clamp"
          }
        );

        return (
          <div key={index} style={{
            marginBottom: 30,
            display: "flex",
            alignItems: "center"
          }}>
            <div style={{
              width: 120,
              fontSize: 14,
              fontWeight: "bold",
              color: func.color
            }}>
              {func.name}
            </div>
            
            <div style={{
              flex: 1,
              height: 20,
              backgroundColor: "#ecf0f1",
              borderRadius: 10,
              position: "relative",
              overflow: "hidden"
            }}>
              <div style={{
                width: `${progress * 100}%`,
                height: "100%",
                backgroundColor: func.color,
                borderRadius: 10,
                transition: "width 0.1s ease"
              }} />
            </div>
            
            <div style={{
              width: 60,
              textAlign: "right",
              fontSize: 12,
              color: "#666"
            }}>
              {(progress * 100).toFixed(1)}%
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default EasingComparison;
```

### 2. 贝塞尔曲线动画

```typescript
import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const BezierAnimations: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const bezierCurves = [
    {
      name: "ease-in",
      curve: [0.42, 0, 1, 1],
      color: "#e74c3c"
    },
    {
      name: "ease-out", 
      curve: [0, 0, 0.58, 1],
      color: "#3498db"
    },
    {
      name: "ease-in-out",
      curve: [0.42, 0, 0.58, 1],
      color: "#2ecc71"
    },
    {
      name: "custom",
      curve: [0.68, -0.55, 0.265, 1.55],
      color: "#f39c12"
    }
  ];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#2c3e50",
      padding: 40,
      display: "flex",
      flexDirection: "column",
      alignItems: "center"
    }}>
      <h2 style={{
        color: "white",
        marginBottom: 60,
        textAlign: "center"
      }}>
        贝塞尔曲线动画演示
      </h2>

      <div style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, 1fr)",
        gap: 40,
        width: "100%",
        maxWidth: 800
      }}>
        {bezierCurves.map((curve, index) => {
          const [x1, y1, x2, y2] = curve.curve;
          
          const progress = interpolate(
            frame,
            [0, durationInFrames],
            [0, 1],
            {
              easing: Easing.bezier(x1, y1, x2, y2),
              extrapolateLeft: "clamp",
              extrapolateRight: "clamp"
            }
          );

          const translateX = progress * 200;
          const scale = 0.8 + progress * 0.4;

          return (
            <div key={index} style={{
              backgroundColor: "rgba(255,255,255,0.1)",
              borderRadius: 15,
              padding: 30,
              position: "relative",
              height: 150
            }}>
              <h3 style={{
                color: "white",
                marginBottom: 20,
                fontSize: 18
              }}>
                {curve.name}
              </h3>
              
              <div style={{
                fontSize: 12,
                color: "#bdc3c7",
                marginBottom: 20
              }}>
                cubic-bezier({curve.curve.join(', ')})
              </div>

              <div style={{
                position: "relative",
                height: 60,
                backgroundColor: "rgba(0,0,0,0.2)",
                borderRadius: 8,
                overflow: "hidden"
              }}>
                <div style={{
                  position: "absolute",
                  left: 10,
                  top: "50%",
                  transform: `translate(${translateX}px, -50%) scale(${scale})`,
                  width: 30,
                  height: 30,
                  backgroundColor: curve.color,
                  borderRadius: "50%",
                  boxShadow: `0 0 20px ${curve.color}`
                }} />
              </div>
            </div>
          );
        })}
      </div>

      <div style={{
        marginTop: 40,
        color: "#bdc3c7",
        textAlign: "center",
        fontSize: 14
      }}>
        当前帧: {frame} / {durationInFrames}
      </div>
    </div>
  );
};

export default BezierAnimations;
```

### 3. 弹性和弹跳效果

```typescript
import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const ElasticBounceEffects: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const effects = [
    {
      name: "弹性 (默认)",
      easing: Easing.elastic(1),
      color: "#e74c3c"
    },
    {
      name: "弹性 (高弹性)",
      easing: Easing.elastic(2),
      color: "#3498db"
    },
    {
      name: "弹性 (低弹性)",
      easing: Easing.elastic(0.5),
      color: "#2ecc71"
    },
    {
      name: "弹跳",
      easing: Easing.bounce,
      color: "#f39c12"
    },
    {
      name: "回弹",
      easing: Easing.back(1.5),
      color: "#9b59b6"
    }
  ];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#34495e",
      padding: 40
    }}>
      <h2 style={{
        color: "white",
        textAlign: "center",
        marginBottom: 50
      }}>
        弹性和弹跳效果演示
      </h2>

      {effects.map((effect, index) => {
        const progress = interpolate(
          frame,
          [0, durationInFrames],
          [0, 1],
          {
            easing: effect.easing,
            extrapolateLeft: "clamp",
            extrapolateRight: "clamp"
          }
        );

        const translateX = progress * 400;

        return (
          <div key={index} style={{
            marginBottom: 60,
            position: "relative"
          }}>
            <div style={{
              color: "white",
              fontSize: 16,
              marginBottom: 15,
              fontWeight: "bold"
            }}>
              {effect.name}
            </div>

            {/* 轨道 */}
            <div style={{
              width: 500,
              height: 4,
              backgroundColor: "rgba(255,255,255,0.2)",
              borderRadius: 2,
              position: "relative"
            }}>
              {/* 目标线 */}
              <div style={{
                position: "absolute",
                right: 0,
                top: -10,
                bottom: -10,
                width: 2,
                backgroundColor: "rgba(255,255,255,0.5)"
              }} />
            </div>

            {/* 动画球 */}
            <div style={{
              position: "absolute",
              left: 0,
              top: 25,
              transform: `translateX(${translateX}px)`,
              width: 40,
              height: 40,
              backgroundColor: effect.color,
              borderRadius: "50%",
              boxShadow: `0 0 20px ${effect.color}`,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: 12,
              fontWeight: "bold"
            }}>
              {Math.round(progress * 100)}%
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ElasticBounceEffects;
```

### 4. 组合缓动效果

```typescript
import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const CombinedEasingEffects: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 多阶段动画
  const stageProgress = interpolate(
    frame,
    [0, durationInFrames / 3, durationInFrames * 2/3, durationInFrames],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  // 不同阶段使用不同缓动
  const getEasingForStage = () => {
    if (frame < durationInFrames / 3) {
      return Easing.out(Easing.back(1.5)); // 入场回弹
    } else if (frame < durationInFrames * 2/3) {
      return Easing.linear; // 中间保持
    } else {
      return Easing.in(Easing.elastic(1)); // 出场弹性
    }
  };

  const mainProgress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 1],
    {
      easing: getEasingForStage(),
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp"
    }
  );

  // 旋转动画
  const rotation = interpolate(
    frame,
    [0, durationInFrames],
    [0, 720],
    {
      easing: Easing.inOut(Easing.cubic),
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp"
    }
  );

  // 缩放动画
  const scale = interpolate(
    frame,
    [0, durationInFrames / 4, durationInFrames * 3/4, durationInFrames],
    [0.5, 1.2, 1.2, 0.5],
    {
      easing: Easing.bounce,
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp"
    }
  );

  // 颜色变化
  const hue = interpolate(
    frame,
    [0, durationInFrames],
    [0, 360],
    {
      easing: Easing.linear,
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp"
    }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#1a1a1a",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      position: "relative"
    }}>
      {/* 主动画元素 */}
      <div style={{
        width: 100,
        height: 100,
        backgroundColor: `hsl(${hue}, 70%, 50%)`,
        borderRadius: "20%",
        transform: `
          translateX(${(mainProgress - 0.5) * 400}px)
          rotate(${rotation}deg)
          scale(${scale})
        `,
        boxShadow: `0 0 40px hsl(${hue}, 70%, 50%)`,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "white",
        fontWeight: "bold",
        fontSize: 14
      }}>
        {Math.round(mainProgress * 100)}%
      </div>

      {/* 轨迹指示器 */}
      <div style={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: 800,
        height: 2,
        backgroundColor: "rgba(255,255,255,0.1)",
        borderRadius: 1
      }} />

      {/* 阶段指示器 */}
      <div style={{
        position: "absolute",
        bottom: 40,
        left: "50%",
        transform: "translateX(-50%)",
        display: "flex",
        gap: 20,
        color: "white",
        fontSize: 12
      }}>
        <div style={{
          opacity: frame < durationInFrames / 3 ? 1 : 0.3,
          fontWeight: frame < durationInFrames / 3 ? "bold" : "normal"
        }}>
          入场 (Back Out)
        </div>
        <div style={{
          opacity: frame >= durationInFrames / 3 && frame < durationInFrames * 2/3 ? 1 : 0.3,
          fontWeight: frame >= durationInFrames / 3 && frame < durationInFrames * 2/3 ? "bold" : "normal"
        }}>
          保持 (Linear)
        </div>
        <div style={{
          opacity: frame >= durationInFrames * 2/3 ? 1 : 0.3,
          fontWeight: frame >= durationInFrames * 2/3 ? "bold" : "normal"
        }}>
          出场 (Elastic In)
        </div>
      </div>
    </div>
  );
};

export default CombinedEasingEffects;
```

### 5. 自定义缓动函数

```typescript
import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const CustomEasingFunctions: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 自定义缓动函数
  const customEasings = [
    {
      name: "波浪",
      easing: (t: number) => Math.sin(t * Math.PI * 2) * 0.1 + t,
      color: "#e74c3c"
    },
    {
      name: "阶梯",
      easing: (t: number) => Math.floor(t * 5) / 5,
      color: "#3498db"
    },
    {
      name: "抛物线",
      easing: (t: number) => t * t * (3 - 2 * t),
      color: "#2ecc71"
    },
    {
      name: "指数衰减",
      easing: (t: number) => 1 - Math.exp(-5 * t),
      color: "#f39c12"
    }
  ];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#2c3e50",
      padding: 40
    }}>
      <h2 style={{
        color: "white",
        textAlign: "center",
        marginBottom: 50
      }}>
        自定义缓动函数
      </h2>

      {customEasings.map((custom, index) => {
        const progress = interpolate(
          frame,
          [0, durationInFrames],
          [0, 1],
          {
            easing: custom.easing,
            extrapolateLeft: "clamp",
            extrapolateRight: "clamp"
          }
        );

        return (
          <div key={index} style={{
            marginBottom: 50,
            backgroundColor: "rgba(255,255,255,0.05)",
            borderRadius: 15,
            padding: 25
          }}>
            <div style={{
              color: "white",
              fontSize: 18,
              marginBottom: 20,
              fontWeight: "bold"
            }}>
              {custom.name}
            </div>

            <div style={{
              display: "flex",
              alignItems: "center",
              gap: 20
            }}>
              {/* 可视化图表 */}
              <div style={{
                width: 200,
                height: 100,
                backgroundColor: "rgba(0,0,0,0.3)",
                borderRadius: 8,
                position: "relative",
                border: "1px solid rgba(255,255,255,0.1)"
              }}>
                {/* 绘制曲线 */}
                {Array.from({ length: 50 }, (_, i) => {
                  const t = i / 49;
                  const easedValue = custom.easing(t);
                  return (
                    <div
                      key={i}
                      style={{
                        position: "absolute",
                        left: `${t * 100}%`,
                        bottom: `${Math.max(0, Math.min(1, easedValue)) * 100}%`,
                        width: 2,
                        height: 2,
                        backgroundColor: custom.color,
                        borderRadius: "50%"
                      }}
                    />
                  );
                })}
                
                {/* 当前位置指示器 */}
                <div style={{
                  position: "absolute",
                  left: `${(frame / durationInFrames) * 100}%`,
                  bottom: `${progress * 100}%`,
                  width: 6,
                  height: 6,
                  backgroundColor: "white",
                  borderRadius: "50%",
                  transform: "translate(-50%, 50%)",
                  boxShadow: `0 0 10px ${custom.color}`
                }} />
              </div>

              {/* 动画演示 */}
              <div style={{
                flex: 1,
                height: 60,
                backgroundColor: "rgba(0,0,0,0.3)",
                borderRadius: 8,
                position: "relative",
                overflow: "hidden"
              }}>
                <div style={{
                  position: "absolute",
                  left: 10,
                  top: "50%",
                  transform: `translate(${progress * 300}px, -50%)`,
                  width: 40,
                  height: 40,
                  backgroundColor: custom.color,
                  borderRadius: "50%",
                  boxShadow: `0 0 20px ${custom.color}`
                }} />
              </div>

              {/* 数值显示 */}
              <div style={{
                color: "white",
                fontSize: 14,
                textAlign: "right",
                minWidth: 80
              }}>
                <div>进度: {(progress * 100).toFixed(1)}%</div>
                <div style={{ fontSize: 12, color: "#bdc3c7" }}>
                  帧: {frame}/{durationInFrames}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default CustomEasingFunctions;
```

## API 参考

### 步进函数
- `Easing.step0(n)`: 对任何正值返回1
- `Easing.step1(n)`: n >= 1时返回1

### 标准函数
- `Easing.linear(t)`: 线性函数
- `Easing.ease(t)`: 基础惯性交互
- `Easing.quad(t)`: 二次函数
- `Easing.cubic(t)`: 三次函数
- `Easing.poly(n)`: n次幂函数

### 数学函数
- `Easing.sin(t)`: 正弦函数
- `Easing.circle(t)`: 圆形函数
- `Easing.exp(t)`: 指数函数

### 特殊效果
- `Easing.elastic(bounciness)`: 弹性效果
- `Easing.back(s)`: 回弹效果
- `Easing.bounce(t)`: 弹跳效果
- `Easing.bezier(x1, y1, x2, y2)`: 贝塞尔曲线

### 修饰符
- `Easing.in(easing)`: 向前运行
- `Easing.out(easing)`: 向后运行
- `Easing.inOut(easing)`: 对称运行

## 最佳实践

1. **选择合适的缓动**: 根据动画类型选择最适合的缓动函数
2. **组合使用**: 在不同阶段使用不同的缓动函数
3. **性能考虑**: 复杂的自定义缓动函数可能影响性能
4. **视觉测试**: 使用可视化工具测试缓动效果
5. **用户体验**: 确保缓动符合用户期望和自然感知

## 常见用例

- UI元素进入/退出动画
- 页面转场效果
- 数据可视化动画
- 游戏角色移动
- 品牌动画效果

## 相关 API

- [`interpolate()`](./interpolate.md) - 数值插值
- [`spring()`](./spring.md) - 弹簧动画
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧

## 致谢

Easing API 与 [React Native](https://reactnative.dev/docs/easing) 完全相同，文档也是从那里复制过来的。感谢他们提供了这个优秀的API。

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/easing.ts)
