

## Coze工作流参考示例

### 1. 工作流创建与配置

*   **访问Coze平台**: 进入“工作空间”->“资源库”->右上角“资源”->“新增工作流”。
*   **命名工作流**: 为工作流命名（例如“学习方法规划视频生成”），并填写英文描述。
*   **配置开始节点**:
    *   添加变量：`title`（主题）和 `watermark`（水印）。

### 2. 视频文案生成

*   **节点**: 第一个“大模型”节点。
*   **模型**: DeepSeek V3。
*   **输入**: 引入“开始节点”的 `title` 变量。
*   **系统提示词**:
    *   角色：“经验丰富的学习方法规划大师”。
    *   要求：详细描述文章结构、内容展开、语言风格、论证方式等。
    *   输出格式：标题（18字以内）、关键词、文章内容（600字以内）。
*   **用户提示词**: 引入 `title` 变量。
*   **输出**: 定义三个变量：`title`（标题）、`caption`（关键词）、`content`（文章内容）。

### 3. 分镜描述生成

*   **节点**: 第二个“大模型”节点。
*   **模型**: DeepSeek V3。
*   **输入**: 选择上一个“大模型”节点的 `content` 变量。
*   **系统提示词**:
    *   角色：“视频分镜描述专家”。
    *   要求：生成至少10个、最多50个分镜。
    *   分镜构成：基础几何图形。
    *   图案风格：极简风格。
    *   主体色调：指定。
    *   输出格式：对象数组，包含分镜名称、分镜描述、字幕文案。
*   **用户提示词**: 引入 `content` 变量。
*   **输出**: 变量类型“array object”，包含分镜名称、分镜描述、字幕文案。

### 4. 文案处理与翻译

*   **文案分割**:
    *   **节点**: “代码节点”。
    *   **输入**: “生成分镜”节点的输出。
    *   **操作**: 根据标点符号将文案分割成列表。
    *   **输出**: 变量类型“array string”。
*   **中文转英文**:
    *   **节点**: 第三个“大模型”节点。
    *   **模型**: DeepSeek V3。
    *   **输入**: “文案分割成列表”节点的输出。
    *   **系统提示词**:
        *   角色：“专业的翻译专家”。
        *   要求：将中文文本翻译成英文，提供中英文列表示例。
        *   输出变量类型：“array string”。
    *   **用户提示词**: 引入输入变量。

### 5. 分镜图片与配音生成

*   **分镜图片生成（批处理）**:
    *   **节点**: “批处理”节点，并行运行数量设置为3。
    *   **输入**: “生成分镜”节点的输出。
    *   **内部节点**:
        *   “图像生成”节点（模型：“通用 Pro”，尺寸：512x512）。
            *   输入：描述变量。
            *   正向提示词：引入分镜描述变量，添加风格描述（如“极简主义风格”、“主题颜色黑色”、“背景纯白色”）。
        *   “抠图”节点。
            *   输入：“图像生成”节点的输出（data）。
    *   **输出**: “批处理”节点的输出（data）连接到“结束节点”。
*   **文案配音生成（批处理）**:
    *   **节点**: 第一个“批处理”节点，并行运行数量设置为2。
    *   **输入**: “文案分割成列表”节点的输出。
    *   **内部节点**:
        *   “插件”节点（选择“语音合成”插件）。
            *   输入变量：“生成文案配音”节点的 item。
            *   预设音色：例如“演播小书”。
    *   **输出**: 第一个“批处理”节点的输出（link）连接到“结束节点”。

### 6. 视频草稿制作与元素添加

*   **背景图生成**:
    *   **节点**: “画板”节点，尺寸 1440x1080。
    *   **操作**: 添加图片并拉伸铺满画板。
*   **剪映插件操作**:
    *   **时间线数据**:
        *   节点：“插件”节点（选择“剪映小助手”），选择“从音频列表中获取时间线列表”。
        *   输入：“生成文案配音”节点的输出。
    *   **图片时间线数据**:
        *   节点：“代码节点”。
        *   输入：依次选择“生成分镜”节点的输出、“文案分割成列表”节点的输出、“生成文案的时间线数据”节点的 `timeline`。
        *   操作：编写代码生成图片时间线数据。
        *   输出：变量类型“array object”。
    *   **创建草稿**:
        *   节点：“剪映小助手”节点，选择“创建草稿”，设置高度1920，宽度1080。
    *   **背景图处理**:
        *   节点：“剪映小助手”节点，选择“字符转列表”。
        *   输入：“生成背景图”节点的 `data`。
    *   **图片数据制作**:
        *   节点：“剪映小助手”节点，选择“image in force”（制作图片数据）。
        *   输入：“背景图列表化”节点的 `info` 和“生成文案的时间线数据”节点的 `all timeline`。
    *   **批量添加图片**:
        *   节点：“剪映小助手”节点，选择“批量添加图片”。
        *   输入：“创建草稿”节点的 `draft_id` 和“制作背景图数据”节点的 `enforce`。
    *   **水印处理**:
        *   节点：“剪映小助手”节点，选择“字符转列表”。
        *   输入：“开始节点”的 `watermark`。
    *   **字幕制作（水印）**:
        *   节点：“剪映小助手”节点，选择“caption enforce”（制作字幕数据）。
        *   输入：“水印列表化”节点的 `info` 和“生成文案的时间线数据”节点的 `all timeline`。
        *   设置：循环动画“故障闪动”，动画时长。
    *   **批量添加字幕（水印）**:
        *   节点：“剪映小助手”节点，选择“批量添加字幕”。
        *   输入：“制作水印字幕数据”的 `enforce`、“创建草稿”节点的 `draft_id`。
        *   设置：对齐方式（居中）、字体大小、字间距、文字颜色（白色）、X/Y轴位置。
    *   **字幕制作（中文字幕）**:
        *   节点：“剪映小助手”节点，选择“capabine e false”（制作字幕数据）。
        *   输入：“文案分割成列表”节点的输出和“生成文案的时间线”节点的 timeline。
    *   **批量添加字幕（中文字幕）**:
        *   节点：“剪映小助手”节点，选择“批量添加字幕”。
        *   输入：“制作中文字幕数据”的 `enforce`、“创建草稿”节点的 `draft_id`。
        *   设置：对齐方式（居中）、字体大小（6）、文字颜色（黑色）、X/Y轴位置。
    *   **字幕制作（英文字幕）**:
        *   节点：“剪映小助手”节点，选择“captain info”（制作字幕数据）。
        *   输入：“生成英文文案列表”的 `output` 和“生成文案的时间线”节点的 timeline。
    *   **批量添加字幕（英文字幕）**:
        *   节点：“剪映小助手”节点，选择“批量添加字幕”。
        *   输入：“制作英文字母数据”的 `enforce`、“创建草稿”节点的 `draft_id`。
        *   设置：对齐方式（居中）、字体大小（5）、文字颜色（黑色）、X/Y轴位置。
    *   **字幕制作（标题）**:
        *   节点：“剪映小助手”节点，选择“字符转列表”。
        *   输入：“生成文案”节点的 `title`。
    *   **批量添加字幕（标题）**:
        *   节点：“剪映小助手”节点，选择“camption info”（制作字幕数据）。
        *   输入：“标题列表化”节点的 `enforce` 和“生成文案的时间线”节点的 `out timeline`。
        *   设置：对齐方式（居中）、字体大小（13）、行间距（6）、文字颜色（白色）、X/Y轴位置。
    *   **图片组合动画**:
        *   节点：“制作图片数据”（输入：“生成分镜图片”的 `output` 和“生成图片的时间线数据”的 timeline）。
        *   节点：“代码节点”（输入：“制作图片数据”的 `info`），编写代码随机添加组合动画，输出 string。
        *   节点：“剪映小助手”节点（选择“批量添加图片”），输入：“创建草稿”的 `draft_id` 和“随机为图片添加一个组合动画”的 `info`，设置缩放和位置。
    *   **音频数据制作与批量添加**:
        *   节点：“剪映小助手”节点（选择“audio enforce”），输入：“生成文案配音”的 `output` 和“生成文案的时间线”的 timeline。
        *   节点：“剪映小助手”节点（选择“批量添加音频”），输入：“制作文案配音数据”的 `info` 和“创建草稿”的 `drive id`。

### 6. 工作流连接与试运行

*   **连接节点**: 将所有节点连接到“结束节点”。
*   **结束节点输出**: 在“结束节点”中，添加输出变量 `draft_id`，值为“创建草稿”节点的 `draft_id`。
*   **试运行**:
    *   点击“试运行”。
    *   输入水印和主题（例如“数学”）。
*   **生成最终视频**:
    *   复制试运行输出的草稿 ID。
    *   在 Coze 平台的“剪映小助手”插件中粘贴该 ID。
    *   点击“创建剪映草稿”以生成最终视频。