# cancelRender()

## 概述

`cancelRender()` 函数用于停止所有帧的渲染，并且不会进行任何重试。当遇到无法恢复的错误时，可以使用此函数立即终止渲染过程。

**版本要求**: v3.3.44+

## 语法

```typescript
import { cancelRender } from "remotion";

cancelRender(reason);
```

## 参数

### reason
- **类型**: `string | Error`
- **描述**: 取消渲染的原因
- **建议**: 使用 `Error` 对象以获得更好的堆栈跟踪信息

## 基础用法

### 1. 基础错误处理

```typescript
import React, { useEffect, useState } from "react";
import { cancelRender, continueRender, delayRender } from "remotion";

export const MyComp: React.FC = () => {
  const [handle] = useState(() => delayRender("获取数据中..."));

  useEffect(() => {
    fetch("https://api.example.com/data")
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then((data) => {
        // 数据获取成功，继续渲染
        continueRender(handle);
      })
      .catch((err) => {
        // 数据获取失败，取消渲染
        cancelRender(err);
      });
  }, [handle]);

  return <div>正在加载数据...</div>;
};
```

### 2. 使用字符串原因

```typescript
import { cancelRender, delayRender } from "remotion";
import { useEffect, useState } from "react";

const StringReasonComponent = () => {
  const [handle] = useState(() => delayRender("验证用户权限..."));

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const response = await fetch("/api/user/permissions");
        const permissions = await response.json();
        
        if (!permissions.canCreateVideo) {
          // 使用字符串描述取消原因
          cancelRender("用户没有创建视频的权限");
          return;
        }
        
        continueRender(handle);
      } catch (error) {
        cancelRender(`权限检查失败: ${error}`);
      }
    };

    checkPermissions();
  }, [handle]);

  return <div>检查权限中...</div>;
};
```

## 实际应用场景

### 1. 数据验证失败

```typescript
import { cancelRender, continueRender, delayRender, getInputProps } from "remotion";
import { useEffect, useState } from "react";

interface VideoInputProps {
  title: string;
  duration: number;
  backgroundImage: string;
}

const DataValidationComponent = () => {
  const [handle] = useState(() => delayRender("验证输入数据..."));
  const [validationComplete, setValidationComplete] = useState(false);

  useEffect(() => {
    const validateInputs = async () => {
      try {
        const inputProps = getInputProps() as VideoInputProps;
        
        // 验证标题
        if (!inputProps.title || inputProps.title.trim().length === 0) {
          cancelRender(new Error("视频标题不能为空"));
          return;
        }
        
        if (inputProps.title.length > 100) {
          cancelRender(new Error("视频标题不能超过100个字符"));
          return;
        }
        
        // 验证时长
        if (!inputProps.duration || inputProps.duration <= 0) {
          cancelRender(new Error("视频时长必须大于0"));
          return;
        }
        
        if (inputProps.duration > 3600) {
          cancelRender(new Error("视频时长不能超过1小时"));
          return;
        }
        
        // 验证背景图片
        if (!inputProps.backgroundImage) {
          cancelRender(new Error("必须提供背景图片"));
          return;
        }
        
        // 检查图片是否存在
        const imageExists = await checkImageExists(inputProps.backgroundImage);
        if (!imageExists) {
          cancelRender(new Error(`背景图片不存在: ${inputProps.backgroundImage}`));
          return;
        }
        
        // 所有验证通过
        setValidationComplete(true);
        continueRender(handle);
        
      } catch (error) {
        cancelRender(new Error(`数据验证过程中发生错误: ${error}`));
      }
    };

    validateInputs();
  }, [handle]);

  const checkImageExists = async (imagePath: string): Promise<boolean> => {
    try {
      const response = await fetch(imagePath, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  };

  if (!validationComplete) {
    return <div>验证输入数据中...</div>;
  }

  return <div>数据验证通过，开始渲染...</div>;
};
```

### 2. 外部服务不可用

```typescript
import { cancelRender, continueRender, delayRender } from "remotion";
import { useEffect, useState } from "react";

const ExternalServiceComponent = () => {
  const [handle] = useState(() => delayRender("检查外部服务状态..."));
  const [serviceStatus, setServiceStatus] = useState<'checking' | 'available' | 'unavailable'>('checking');

  useEffect(() => {
    const checkExternalServices = async () => {
      const requiredServices = [
        { name: "图片处理服务", url: "https://image-api.example.com/health" },
        { name: "字体服务", url: "https://fonts-api.example.com/health" },
        { name: "音频处理服务", url: "https://audio-api.example.com/health" }
      ];

      try {
        const healthChecks = await Promise.allSettled(
          requiredServices.map(async (service) => {
            const response = await fetch(service.url, { 
              method: 'GET',
              timeout: 5000 // 5秒超时
            });
            
            if (!response.ok) {
              throw new Error(`${service.name} 返回状态码 ${response.status}`);
            }
            
            return { service: service.name, status: 'ok' };
          })
        );

        const failedServices = healthChecks
          .map((result, index) => ({ result, service: requiredServices[index] }))
          .filter(({ result }) => result.status === 'rejected')
          .map(({ service, result }) => ({
            name: service.name,
            error: result.status === 'rejected' ? result.reason : 'Unknown error'
          }));

        if (failedServices.length > 0) {
          const errorMessage = `以下外部服务不可用:\n${failedServices
            .map(service => `- ${service.name}: ${service.error}`)
            .join('\n')}`;
          
          cancelRender(new Error(errorMessage));
          setServiceStatus('unavailable');
          return;
        }

        // 所有服务都可用
        setServiceStatus('available');
        continueRender(handle);
        
      } catch (error) {
        cancelRender(new Error(`外部服务检查失败: ${error}`));
        setServiceStatus('unavailable');
      }
    };

    checkExternalServices();
  }, [handle]);

  if (serviceStatus === 'checking') {
    return <div>检查外部服务状态中...</div>;
  }

  if (serviceStatus === 'unavailable') {
    return <div>外部服务不可用，渲染已取消</div>;
  }

  return <div>所有外部服务正常，开始渲染...</div>;
};
```

### 3. 资源配额检查

```typescript
import { cancelRender, continueRender, delayRender } from "remotion";
import { useEffect, useState } from "react";

interface ResourceQuota {
  maxVideoLength: number;
  maxFileSize: number;
  remainingCredits: number;
  dailyRenderLimit: number;
  todayRenderCount: number;
}

const ResourceQuotaComponent = () => {
  const [handle] = useState(() => delayRender("检查资源配额..."));
  const [quotaStatus, setQuotaStatus] = useState<'checking' | 'sufficient' | 'exceeded'>('checking');

  useEffect(() => {
    const checkResourceQuota = async () => {
      try {
        const response = await fetch('/api/user/quota');
        
        if (!response.ok) {
          throw new Error(`配额检查失败: HTTP ${response.status}`);
        }
        
        const quota: ResourceQuota = await response.json();
        
        // 检查每日渲染限制
        if (quota.todayRenderCount >= quota.dailyRenderLimit) {
          cancelRender(new Error(
            `已达到每日渲染限制 (${quota.todayRenderCount}/${quota.dailyRenderLimit})`
          ));
          setQuotaStatus('exceeded');
          return;
        }
        
        // 检查剩余积分
        if (quota.remainingCredits <= 0) {
          cancelRender(new Error("账户积分不足，无法进行渲染"));
          setQuotaStatus('exceeded');
          return;
        }
        
        // 检查视频长度限制（假设当前视频为60秒）
        const currentVideoLength = 60;
        if (currentVideoLength > quota.maxVideoLength) {
          cancelRender(new Error(
            `视频长度 (${currentVideoLength}秒) 超过限制 (${quota.maxVideoLength}秒)`
          ));
          setQuotaStatus('exceeded');
          return;
        }
        
        // 检查文件大小限制（假设预估大小为50MB）
        const estimatedFileSize = 50 * 1024 * 1024; // 50MB in bytes
        if (estimatedFileSize > quota.maxFileSize) {
          const maxSizeMB = quota.maxFileSize / (1024 * 1024);
          cancelRender(new Error(
            `预估文件大小超过限制 (最大 ${maxSizeMB}MB)`
          ));
          setQuotaStatus('exceeded');
          return;
        }
        
        // 所有配额检查通过
        setQuotaStatus('sufficient');
        continueRender(handle);
        
      } catch (error) {
        cancelRender(new Error(`资源配额检查失败: ${error}`));
        setQuotaStatus('exceeded');
      }
    };

    checkResourceQuota();
  }, [handle]);

  if (quotaStatus === 'checking') {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <h2>检查资源配额中...</h2>
        <p>验证渲染权限和资源限制</p>
      </div>
    );
  }

  if (quotaStatus === 'exceeded') {
    return (
      <div style={{ 
        color: 'red', 
        textAlign: 'center', 
        padding: 50,
        border: '1px solid red',
        borderRadius: 5
      }}>
        <h2>资源配额不足</h2>
        <p>渲染已取消，请检查账户状态</p>
      </div>
    );
  }

  return (
    <div style={{ textAlign: 'center', padding: 50 }}>
      <h2>资源配额检查通过</h2>
      <p>开始渲染视频...</p>
    </div>
  );
};
```

### 4. 条件性取消渲染

```typescript
import { cancelRender, continueRender, delayRender, getInputProps } from "remotion";
import { useEffect, useState } from "react";

interface ConditionalProps {
  environment: 'development' | 'staging' | 'production';
  enableExperimentalFeatures: boolean;
  userRole: 'admin' | 'user' | 'guest';
}

const ConditionalRenderComponent = () => {
  const [handle] = useState(() => delayRender("检查渲染条件..."));
  const [conditionsMet, setConditionsMet] = useState(false);

  useEffect(() => {
    const checkRenderConditions = () => {
      try {
        const props = getInputProps() as ConditionalProps;
        
        // 在生产环境中禁用实验性功能
        if (props.environment === 'production' && props.enableExperimentalFeatures) {
          cancelRender(new Error(
            "生产环境中不允许使用实验性功能"
          ));
          return;
        }
        
        // 只有管理员可以在生产环境中渲染
        if (props.environment === 'production' && props.userRole !== 'admin') {
          cancelRender(new Error(
            "只有管理员可以在生产环境中进行渲染"
          ));
          return;
        }
        
        // 访客用户不能渲染
        if (props.userRole === 'guest') {
          cancelRender(new Error(
            "访客用户没有渲染权限，请先登录"
          ));
          return;
        }
        
        // 检查当前时间是否在允许的渲染时间窗口内
        const currentHour = new Date().getHours();
        const isMaintenanceTime = currentHour >= 2 && currentHour <= 4; // 凌晨2-4点维护时间
        
        if (isMaintenanceTime && props.environment === 'production') {
          cancelRender(new Error(
            "系统维护时间 (02:00-04:00)，暂停渲染服务"
          ));
          return;
        }
        
        // 所有条件检查通过
        setConditionsMet(true);
        continueRender(handle);
        
      } catch (error) {
        cancelRender(new Error(`条件检查失败: ${error}`));
      }
    };

    checkRenderConditions();
  }, [handle]);

  if (!conditionsMet) {
    return <div>检查渲染条件中...</div>;
  }

  return <div>条件检查通过，开始渲染...</div>;
};
```

### 5. 超时取消

```typescript
import { cancelRender, continueRender, delayRender } from "remotion";
import { useEffect, useState } from "react";

const TimeoutCancelComponent = () => {
  const [handle] = useState(() => delayRender("加载关键资源..."));
  const [status, setStatus] = useState<'loading' | 'success' | 'timeout' | 'error'>('loading');

  useEffect(() => {
    const timeoutDuration = 10000; // 10秒超时
    let timeoutId: NodeJS.Timeout;

    const loadCriticalResources = async () => {
      try {
        // 设置超时取消
        timeoutId = setTimeout(() => {
          setStatus('timeout');
          cancelRender(new Error(
            `关键资源加载超时 (${timeoutDuration / 1000}秒)，渲染已取消`
          ));
        }, timeoutDuration);

        // 模拟加载关键资源
        const resources = await Promise.all([
          loadResource('fonts'),
          loadResource('images'),
          loadResource('audio'),
          loadResource('config')
        ]);

        clearTimeout(timeoutId);
        
        // 检查资源是否完整
        const missingResources = resources
          .map((resource, index) => ({ resource, index }))
          .filter(({ resource }) => !resource.success)
          .map(({ index }) => ['fonts', 'images', 'audio', 'config'][index]);

        if (missingResources.length > 0) {
          cancelRender(new Error(
            `以下关键资源加载失败: ${missingResources.join(', ')}`
          ));
          setStatus('error');
          return;
        }

        setStatus('success');
        continueRender(handle);
        
      } catch (error) {
        clearTimeout(timeoutId);
        setStatus('error');
        cancelRender(new Error(`资源加载过程中发生错误: ${error}`));
      }
    };

    loadCriticalResources();

    // 清理函数
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [handle]);

  const loadResource = async (resourceType: string) => {
    // 模拟资源加载，随机延迟
    const delay = Math.random() * 8000 + 2000; // 2-10秒随机延迟
    
    return new Promise<{ success: boolean; type: string }>((resolve) => {
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90% 成功率
        resolve({ success, type: resourceType });
      }, delay);
    });
  };

  const renderStatus = () => {
    switch (status) {
      case 'loading':
        return (
          <div style={{ textAlign: 'center', padding: 50 }}>
            <h2>加载关键资源中...</h2>
            <p>最多等待 10 秒</p>
          </div>
        );
      
      case 'success':
        return (
          <div style={{ textAlign: 'center', padding: 50 }}>
            <h2>资源加载成功</h2>
            <p>开始渲染视频...</p>
          </div>
        );
      
      case 'timeout':
        return (
          <div style={{ 
            color: 'red', 
            textAlign: 'center', 
            padding: 50,
            border: '1px solid red',
            borderRadius: 5
          }}>
            <h2>加载超时</h2>
            <p>关键资源加载时间过长，渲染已取消</p>
          </div>
        );
      
      case 'error':
        return (
          <div style={{ 
            color: 'red', 
            textAlign: 'center', 
            padding: 50,
            border: '1px solid red',
            borderRadius: 5
          }}>
            <h2>加载失败</h2>
            <p>关键资源加载失败，渲染已取消</p>
          </div>
        );
      
      default:
        return null;
    }
  };

  return renderStatus();
};
```

## 最佳实践

1. **使用 Error 对象**: 优先使用 `Error` 对象而不是字符串，以获得更好的调试信息
2. **提供详细信息**: 在错误消息中包含足够的上下文信息
3. **及时取消**: 一旦确定无法继续渲染，立即调用 `cancelRender()`
4. **避免重复调用**: 确保 `cancelRender()` 只被调用一次
5. **清理资源**: 在取消渲染前清理已分配的资源

## 与其他函数的关系

| 函数 | 用途 | 调用时机 |
|------|------|----------|
| `delayRender()` | 延迟渲染开始 | 需要等待异步操作时 |
| `continueRender()` | 继续被延迟的渲染 | 异步操作成功完成时 |
| `cancelRender()` | 取消渲染 | 遇到无法恢复的错误时 |

## 常见错误

### 1. 在 continueRender 后调用 cancelRender

```typescript
// ❌ 错误：在继续渲染后又取消
useEffect(() => {
  const handle = delayRender();
  
  fetch('/api/data')
    .then(data => {
      continueRender(handle);
      // 错误：已经继续渲染了，不应该再取消
      if (data.someCondition) {
        cancelRender("条件不满足");
      }
    });
}, []);

// ✅ 正确：在继续渲染前检查所有条件
useEffect(() => {
  const handle = delayRender();
  
  fetch('/api/data')
    .then(data => {
      if (data.someCondition) {
        cancelRender("条件不满足");
      } else {
        continueRender(handle);
      }
    });
}, []);
```

### 2. 忘记提供错误信息

```typescript
// ❌ 错误：没有提供错误信息
if (someErrorCondition) {
  cancelRender(); // 缺少参数
}

// ✅ 正确：提供详细的错误信息
if (someErrorCondition) {
  cancelRender(new Error("详细描述错误原因"));
}
```

## 相关 API

- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`continueRender()`](./continueRender.md) - 继续渲染

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/cancel-render.ts)
