/**
 * @文件功能: 流水线结果标准化工具
 * @创建时间: 2025-06-13
 * @作者: AI Assistant
 * @描述: 通用的流水线context格式化工具，简化前端处理逻辑
 */

const logger = require('../utils/logger');

/**
 * @功能概述: 标准化流水线最终context输出格式
 * @参数说明:
 *   - rawResult: {object} 原始流水线执行结果 {status, context, tasks}
 *   - pipelineType: {string} 流水线类型（可选，用于标识）
 *   - reqId: {string} 请求ID（可选）
 * @返回值: {object} 标准化的最终context
 */
function standardizePipelineResult(rawResult, pipelineType = 'unknown', reqId = 'unknown') {
    const logPrefix = '[文件：pipelineResultStandardizer.js][standardizePipelineResult]';

    try {
        // 基础验证
        if (!rawResult || !rawResult.context) {
            throw new Error('rawResult或rawResult.context为空');
        }

        const { status, context, tasks } = rawResult;

        // 构建标准化的最终context - 简单通用格式
        const standardizedContext = {
            // === 基础信息 ===
            status: status,                                    // 'completed' | 'failed'
            reqId: reqId,                                      // 请求ID
            pipelineType: pipelineType,                        // 流水线类型
            timestamp: new Date().toISOString(),               // 完成时间

            // === 通用文件路径和URL ===
            files: extractAllFilePaths(context),
            urls: convertPathsToUrls(extractAllFilePaths(context)),

            // === 通用统计信息 ===
            stats: extractBasicStats(context, tasks),

            // === 原始context（完整保留） ===
            context: context
        };

        logger.info(`${logPrefix} 成功标准化流水线结果，类型: ${pipelineType}, reqId: ${reqId}`);
        return standardizedContext;

    } catch (error) {
        logger.error(`${logPrefix} 标准化失败: ${error.message}`);

        // 失败时返回最小化的标准格式
        return {
            status: 'failed',
            reqId: reqId,
            pipelineType: pipelineType,
            timestamp: new Date().toISOString(),
            error: error.message,
            files: {},
            urls: {},
            stats: {},
            context: rawResult.context || {}
        };
    }
}

/**
 * @功能概述: 通用提取所有文件路径（自动识别）
 * @参数说明:
 *   - context: {object} 原始context
 * @返回值: {object} 所有文件路径对象
 */
function extractAllFilePaths(context) {
    const files = {};

    // 通用提取逻辑：自动识别所有包含路径的字段
    Object.keys(context).forEach(key => {
        const value = context[key];
        if (value && typeof value === 'string' &&
            (key.includes('Path') || key.includes('FilePath') || key.includes('File'))) {
            files[key] = value;
        }
    });

    return files;
}

/**
 * @功能概述: 将服务器路径转换为Web访问URL
 * @参数说明:
 *   - files: {object} 文件路径对象
 * @返回值: {object} URL对象
 */
function convertPathsToUrls(files) {
    const urls = {};
    const baseUrl = 'http://localhost:8081';

    Object.keys(files).forEach(key => {
        const filePath = files[key];
        if (filePath && typeof filePath === 'string') {
            // 简单的路径转换：提取uploads后的部分
            if (filePath.includes('uploads')) {
                const relativePath = filePath.substring(filePath.indexOf('uploads'));
                urls[key] = `${baseUrl}/backend/${relativePath}`;
            }
        }
    });

    return urls;
}

/**
 * @功能概述: 通用提取基础统计信息
 * @参数说明:
 *   - context: {object} 原始context
 *   - tasks: {Array} 任务数组
 * @返回值: {object} 基础统计信息对象
 */
function extractBasicStats(context, tasks) {
    const stats = {
        // 任务统计
        totalTasks: tasks ? tasks.length : 0,
        completedTasks: tasks ? tasks.filter(t => t.status === 'completed').length : 0
    };

    // 自动提取数值型统计信息
    Object.keys(context).forEach(key => {
        const value = context[key];
        if (typeof value === 'number' &&
            (key.includes('Duration') || key.includes('Count') || key.includes('Size'))) {
            stats[key] = value;
        }
    });

    return stats;
}

module.exports = {
    standardizePipelineResult
};

logger.info('[文件：pipelineResultStandardizer.js] 流水线结果标准化工具已加载');
