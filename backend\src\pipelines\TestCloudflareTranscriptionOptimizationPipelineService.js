/**
 * @文件名: TestCloudflareTranscriptionOptimizationPipelineService.js
 * @功能概述: 测试专用的Cloudflare转录优化流水线服务
 * @作者: AI Assistant
 * @创建时间: 2025-07-16
 * @最后修改: 2025-07-17
 *
 * @功能描述:
 *   包含ConvertToAudioForCloudflareTask、GetTranscriptionTaskByCloudflare、SubtitleOptimizationTask、
 *   TranscriptionCorrectionTask、ContentSummarizationTask、TranslateSubtitleTask、SubtitleClozeTask、
 *   BilingualSubtitleMergeTask、GenerateASSTask、GenerateVideoTask十个任务的完整视频处理流水线。
 *   此服务编排了10个核心任务，实现了从视频转音频、语音转录、字幕优化、AI校正、内容总结、
 *   字幕翻译、挖空处理、双语合并、ASS生成到视频合成的完整工作流。
 *
 * ========== 流水线任务序列和数据流转 ==========
 *
 * @任务流程:
 *   1. ConvertToAudioForCloudflareTask - Cloudflare专用视频转音频，提取极度压缩音频文件(<2MB)
 *   2. GetTranscriptionTaskByCloudflare - Cloudflare语音转录，获取ASR原始响应
 *   3. SubtitleOptimizationTask - 字幕优化，优化转录结果
 *   4. TranscriptionCorrectionTask - AI智能校正，使用LLM修正转录错误
 *   5. ContentSummarizationTask - 内容总结，生成摘要和标题
 *   6. TranslateSubtitleTask - 字幕翻译，将英文字幕翻译为中文
 *   7. SubtitleClozeTask - 字幕挖空，生成填空练习版本
 *   8. BilingualSubtitleMergeTask - 双语字幕合并，生成增强双语字幕
 *   9. GenerateASSTask - ASS字幕生成，创建完整的ASS字幕文件
 *   10. GenerateVideoTask - 视频生成，合成最终的9:16短视频
 *
 * ========== 流水线上下文输入输出 ==========
 *
 * @流水线输入 (initialContext):
 *   - videoIdentifier: {string} 视频唯一标识符
 *   - originalVideoPath: {string} 原始视频文件路径
 *   - originalVideoName: {string} 原始视频文件名
 *   - uploadedVideoDirPath: {string} 上传视频目录路径
 *   - savePath: {string} 文件保存路径
 *   - reqId: {string} 请求ID（可选）
 *
 * @流水线输出 (finalContext):
 *   - audioFilePath: {string} 提取的音频文件路径
 *   - apiResponse: {object} Cloudflare ASR原始响应数据
 *   - transcriptionStatus: {string} 转录状态
 *   - optimizedData: {Array} 优化后的字幕segments数组
 *   - simplifiedSubtitleJsonArray: {Array} 简化字幕JSON数组（5字段格式）
 *   - simplifiedSubtitleJsonPath: {string} 简化字幕JSON文件路径
 *   - optimizedEnglishSrtContent: {string} 优化后的英文字幕SRT内容
 *   - correctedSubtitleJsonArray: {Array} AI校正后的字幕JSON数组
 *   - correctedSubtitleJsonPath: {string} AI校正后的字幕JSON文件路径
 *   - correctionStatus: {string} AI校正状态
 *
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 */

const PipelineBase = require('../class/PipelineBase');
const ConvertToAudioForCloudflareTask = require('../tasks/ConvertToAudioForCloudflareTask');
const GetTranscriptionTaskByCloudflare = require('../tasks/GetTranscriptionTaskByCloudflare');
const SubtitleOptimizationTask = require('../tasks/SubtitleOptimizationTask');
const TranscriptionCorrectionTask = require('../tasks/TranscriptionCorrectionTask');
const ContentSummarizationTask = require('../tasks/ContentSummarizationTask');
const TranslateSubtitleTask = require('../tasks/TranslateSubtitleTask');
const SubtitleClozeTask = require('../tasks/SubtitleClozeTask');
const BilingualSubtitleMergeTask = require('../tasks/BilingualSubtitleMergeTask');
const GenerateASSTask = require('../tasks/GenerateASSTask');
const GenerateVideoTask = require('../tasks/GenerateVideoTask');
const logger = require('../utils/logger');
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');
const fs = require('fs').promises;
const path = require('path');

// 模块级日志前缀 - 统一格式
const moduleLogPrefix = `[文件：TestCloudflareTranscriptionOptimizationPipelineService.js][Cloudflare转录优化流水线服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录。`);
logger.info(`${moduleLogPrefix}[功能验证] 包含3个任务的Cloudflare转录优化流水线。`);

/**
 * @类名: TestCloudflareTranscriptionOptimizationPipelineService
 * @继承: 无
 * @功能: Cloudflare转录优化流水线服务类
 */
class TestCloudflareTranscriptionOptimizationPipelineService {
    /**
     * @功能概述: 构造函数 - 初始化Cloudflare转录优化流水线
     * @参数说明:
     *   - reqId: {string} [可选] 请求ID，用于日志追踪，默认为'unknown_test_cloudflare_req'
     * @执行流程:
     *   1. 初始化日志前缀和请求ID
     *   2. 创建PipelineBase实例
     *   3. 按顺序添加所有10个任务到流水线
     *   4. 记录任务序列信息
     */
    constructor(reqId = 'unknown_test_cloudflare_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：TestCloudflareTranscriptionOptimizationPipelineService.js][Cloudflare转录优化流水线服务][ReqID:${this.reqId}]`;

        // 创建流水线实例
        this.processingPipeline = new PipelineBase(`TestCloudflareTranscriptionOptimizationPipeline-${this.reqId}`);

        // 按执行顺序统一添加所有任务
        this.addAllTasks();

        const taskNames = this.processingPipeline.tasks.map(task => task.name).join(' → ');
        logger.info(`${this.logPrefix}[TestCloudflareTranscriptionOptimizationPipeline] Pipeline 已创建，包含任务序列: ${taskNames}`);
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     * @执行顺序:
     *   1. ConvertToAudioForCloudflareTask - Cloudflare专用视频转音频任务（极度压缩）
     *   2. GetTranscriptionTaskByCloudflare - Cloudflare语音转录任务
     *   3. SubtitleOptimizationTask - 字幕优化任务
     *   4. TranscriptionCorrectionTask - AI智能校正任务
     *   5. ContentSummarizationTask - 内容总结任务
     *   6. TranslateSubtitleTask - 字幕翻译任务
     *   7. SubtitleClozeTask - 字幕挖空任务
     *   8. BilingualSubtitleMergeTask - 双语字幕合并任务
     *   9. GenerateASSTask - ASS字幕生成任务
     *   10. GenerateVideoTask - 视频生成任务
     * @数据流转:
     *   每个任务的输出会自动传递给下一个任务作为输入，
     *   形成完整的视频处理流水线：从音频提取到最终视频生成。
     */
    addAllTasks() {
        this.addConvertToAudioForCloudflareTask();
        this.addGetTranscriptionTaskByCloudflare();
        this.addSubtitleOptimizationTask();
        this.addTranscriptionCorrectionTask();
        this.addContentSummarizationTask();
        this.addTranslateSubtitleTask();
        this.addSubtitleClozeTask();
        this.addBilingualSubtitleMergeTask();
        this.addGenerateASSTask();
        this.addGenerateVideoTask();
    }

    /**
     * @功能概述: 添加Cloudflare专用视频转音频任务 (ConvertToAudioForCloudflareTask) 到流水线。
     *           此任务负责从原始视频文件中提取音频，使用极度压缩设置生成<2MB的音频文件供Cloudflare转录使用。
     *
     * @Cloudflare优化特性:
     *   - 比特率: 32kbps (vs 原版128kbps，减少75%)
     *   - 采样率: 16kHz (Whisper推荐)
     *   - 声道: 单声道 (减少50%文件大小)
     *   - 目标文件大小: <2MB (符合Cloudflare Workers AI限制)
     *
     * @上下文输入 (context 预期的字段 for ConvertToAudioForCloudflareTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - originalVideoPath: {string} (必需) 原始视频文件路径
     *   - originalVideoName: {string} (必需) 原始视频文件名
     *   - uploadedVideoDirPath: {string} (必需) 上传视频目录路径
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @执行后上下文状态 (ConvertToAudioForCloudflareTask 完成后 context 对象的完整内容):
     *   // === 来自初始输入 ===
     *   - reqId: {string} 请求追踪ID
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - originalVideoName: {string} 原始视频文件名
     *   - uploadedVideoDirPath: {string} 上传视频目录路径
     *   - savePath: {string} 文件保存路径
     *
     *   // === 来自 ConvertToAudioForCloudflareTask (第1个任务新增) ===
     *   - audioFilePath: {string} 提取的音频文件路径
     *   - audioFilePathInUploads: {string} 音频文件在uploads目录中的路径
     *   - audioFileName: {string} 音频文件名（包含cloudflare标识）
     *   - audioFileSize: {number} 音频文件大小（字节）
     *   - audioFileSizeMB: {string} 音频文件大小（MB）
     *   - cloudflareOptimized: {boolean} true - 标识为Cloudflare优化版本
     *   - finalAudioBitrate: {string} '32k' - 实际使用的比特率
     */
    addConvertToAudioForCloudflareTask() {
        this.processingPipeline.addTask(new ConvertToAudioForCloudflareTask());
    }

    /**
     * @功能概述: 添加Cloudflare语音转录任务 (GetTranscriptionTaskByCloudflare) 到流水线。
     *           此任务负责调用Cloudflare Workers AI语音转录服务，将音频文件转换为文本转录结果。
     *
     * @上下文输入 (context 预期的字段 for GetTranscriptionTaskByCloudflare):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - audioFilePathInUploads: {string} (必需) 音频文件路径，来自ConvertToAudioTask
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @执行后上下文状态 (GetTranscriptionTaskByCloudflare 完成后 context 对象的完整内容):
     *   // === 来自初始输入 ===
     *   - reqId: {string} 请求追踪ID
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - originalVideoName: {string} 原始视频文件名
     *   - uploadedVideoDirPath: {string} 上传视频目录路径
     *   - savePath: {string} 文件保存路径
     *
     *   // === 来自 ConvertToAudioTask (第1个任务) ===
     *   - audioFilePath: {string} 提取的音频文件路径
     *   - audioFilePathInUploads: {string} 音频文件在uploads目录中的路径
     *   - audioDuration: {number} 音频时长（秒）
     *   - audioFormat: {string} 音频格式
     *
     *   // === 来自 GetTranscriptionTaskByCloudflare (第2个任务新增) ===
     *   - apiResponse: {object} Cloudflare ASR原始响应数据（Whisper兼容格式）
     *   - transcriptionStatus: {string} 转录状态
     *   - transcriptionJsonPath: {string} 转录JSON文件路径
     *   - processedAudioPath: {string} 实际处理的音频文件路径
     */
    addGetTranscriptionTaskByCloudflare() {
        this.processingPipeline.addTask(new GetTranscriptionTaskByCloudflare());
    }

    /**
     * @功能概述: 添加字幕优化任务 (SubtitleOptimizationTask) 到流水线。
     *           此任务负责对Cloudflare转录结果进行优化处理，包括合并过短segments、
     *           合并句子片段和智能拆分长句子等操作。
     *
     * @上下文输入 (context 预期的字段 for SubtitleOptimizationTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - apiResponse: {object} (必需) Cloudflare ASR原始响应数据，来自GetTranscriptionTaskByCloudflare
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @执行后上下文状态 (SubtitleOptimizationTask 完成后 context 对象的完整内容):
     *   // === 来自初始输入 ===
     *   - reqId: {string} 请求追踪ID
     *   - videoIdentifier: {string} 视频唯一标识符
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - originalVideoName: {string} 原始视频文件名
     *   - uploadedVideoDirPath: {string} 上传视频目录路径
     *   - savePath: {string} 文件保存路径
     *
     *   // === 来自 ConvertToAudioTask (第1个任务) ===
     *   - audioFilePath: {string} 提取的音频文件路径
     *   - audioFilePathInUploads: {string} 音频文件在uploads目录中的路径
     *   - audioDuration: {number} 音频时长（秒）
     *   - audioFormat: {string} 音频格式
     *
     *   // === 来自 GetTranscriptionTaskByCloudflare (第2个任务) ===
     *   - apiResponse: {object} Cloudflare ASR原始响应数据（Whisper兼容格式）
     *   - transcriptionStatus: {string} 转录状态
     *   - transcriptionJsonPath: {string} 转录JSON文件路径
     *   - processedAudioPath: {string} 实际处理的音频文件路径
     *
     *   // === 来自 SubtitleOptimizationTask (第3个任务，最终任务新增) ===
     *   - optimizedData: {Array} 优化后的字幕segments数组
     *   - optimizedFilePath: {string} 优化后字幕文件路径
     *   - simplifiedSubtitleJsonArray: {Array} 简化字幕JSON数组（5字段格式）
     *   - simplifiedSubtitleJsonPath: {string} 简化字幕JSON文件路径
     *   - optimizedEnglishSrtContent: {string} 优化后的英文字幕SRT内容
     *   - originalSegmentsCount: {number} 原始segments数量
     *   - optimizedSegmentsCount: {number} 优化后segments数量
     *   - optimizationStatus: {string} 优化状态
     *
     *   // === 最终流水线输出摘要 ===
     *   // 此时 context 包含完整的Cloudflare转录优化结果，主要可用数据：
     *   // 1. optimizedData - 优化后的字幕segments数组，可用于后续处理
     *   // 2. simplifiedSubtitleJsonArray - 简化字幕JSON数组，标准5字段格式
     *   // 3. optimizedEnglishSrtContent - 优化后的英文字幕SRT内容，可直接使用
     *   // 4. audioFilePath - 提取的音频文件，可用于视频生成
     *   // 5. transcriptionJsonPath - 转录JSON文件，可用于备份或分析
     */
    addSubtitleOptimizationTask() {
        this.processingPipeline.addTask(new SubtitleOptimizationTask());
    }

    /**
     * @功能概述: 执行完整的Cloudflare转录优化流水线
     * @param {object} initialContext - 初始化上下文
     * @param {string} initialContext.videoIdentifier - 视频标识符
     * @param {string} initialContext.originalVideoPath - 原始视频文件路径
     * @param {string} initialContext.originalVideoName - 原始视频文件名
     * @param {string} initialContext.uploadedVideoDirPath - 上传视频目录路径
     * @param {string} [initialContext.savePath] - 保存路径，默认使用配置
     * @param {string} [initialContext.reqId] - 请求ID
     * @param {function} [serviceProgressCallback] - 进度回调函数
     * @returns {Promise<object>} 标准化的流水线执行结果
     */
    async processCloudflareTranscriptionOptimization(initialContext, serviceProgressCallback) {
        logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 开始执行Cloudflare转录优化流水线。`);

        try {
            // 步骤 1: 验证输入参数
            this.validateInput(initialContext);

            // 步骤 2: 准备执行上下文
            const currentContext = {
                reqId: this.reqId,
                ...initialContext
            };

            logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 输入参数验证通过，开始执行流水线。`);
            logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 任务序列: ConvertToAudioForCloudflareTask → GetTranscriptionTaskByCloudflare → SubtitleOptimizationTask → TranscriptionCorrectionTask → ContentSummarizationTask → TranslateSubtitleTask → SubtitleClozeTask → BilingualSubtitleMergeTask → GenerateASSTask → GenerateVideoTask`);

            // 步骤 2.5: 加载videoConfig配置文件
            logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 开始加载video-config.json配置文件...`);
            const videoConfigPath = path.join(__dirname, '../config/video/video-config.json');
            try {
                const configContent = await fs.readFile(videoConfigPath, 'utf8');
                currentContext.videoConfig = JSON.parse(configContent);
                logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] video-config.json加载成功`);
                logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 配置摘要: ${currentContext.videoConfig.width}x${currentContext.videoConfig.height}, ${currentContext.videoConfig.framerate}fps, 重复${currentContext.videoConfig.repeatCount}次`);
            } catch (error) {
                logger.error(`${this.logPrefix}[processCloudflareTranscriptionOptimization] video-config.json加载失败: ${error.message}`);
                throw new Error(`无法加载video-config.json配置文件: ${error.message}`);
            }

            // 步骤 3: 执行流水线
            const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);

            // 步骤 4: 标准化返回结果
            const standardizedResult = standardizePipelineResult(result, 'cloudflare_transcription_optimization', currentContext.reqId);
            logger.info(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 流水线执行完成，状态: ${standardizedResult.status}`);

            return standardizedResult;

        } catch (error) {
            logger.error(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 流水线执行失败: ${error.message}`);
            logger.error(`${this.logPrefix}[processCloudflareTranscriptionOptimization] 错误堆栈: ${error.stack}`);

            // 返回标准化的错误结果
            const standardizedError = standardizePipelineResult(
                { error: error.message, stack: error.stack },
                'cloudflare_transcription_optimization',
                this.reqId
            );
            return standardizedError;
        }
    }

    /**
     * @功能概述: 添加AI智能校正任务 (TranscriptionCorrectionTask) 到流水线。
     *           此任务负责使用LLM对转录结果进行智能校正，修正语法错误、标点符号等。
     *
     * @上下文输入 (context 预期的字段 for TranscriptionCorrectionTask):
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自SubtitleOptimizationTask的简化字幕JSON数组
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @执行后上下文状态 (TranscriptionCorrectionTask 完成后 context 对象的新增内容):
     *   // === 来自 TranscriptionCorrectionTask (第4个任务新增) ===
     *   - correctedSubtitleJsonArray: {Array} AI校正后的字幕JSON数组（5字段格式）
     *   - correctedSubtitleJsonPath: {string} AI校正后的字幕JSON文件路径
     *   - correctionStatus: {string} AI校正状态 ('success' | 'failed' | 'degraded')
     *   - correctionMethod: {string} 校正方法 ('llm_batch_correction')
     *   - correctionMetrics: {object} 校正指标（修改数量、成功率等）
     *   - llmProcessingDetails: {object} LLM处理详情
     */
    addTranscriptionCorrectionTask() {
        this.processingPipeline.addTask(new TranscriptionCorrectionTask());
    }

    /**
     * @功能概述: 添加内容总结任务 (ContentSummarizationTask) 到流水线。
     *           此任务负责对完整转录文本进行AI总结，生成内容摘要和标题。
     *
     * @功能特性:
     *   - 输入: fullTranscriptText (来自TranscriptionCorrectionTask)
     *   - 处理: 使用LLM进行内容分析和总结
     *   - 输出: 摘要(100字)、标题(10字)、关键话题
     *   - 格式: JSON文件保存
     *
     * @上下文输入 (context 预期的字段 for ContentSummarizationTask):
     *   - fullTranscriptText: {string} (必需) 完整转录文本，来自TranscriptionCorrectionTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @上下文输出 (context 新增的字段 from ContentSummarizationTask):
     *   - transcriptSummary: {string} 内容摘要（100个中文字）
     *   - transcriptTitle: {string} 内容标题（10个中文字）
     *   - summaryJsonPath: {string} 保存的JSON文件路径
     *   - summaryStatus: {string} 总结状态 ('success' | 'failed')
     *   - summaryMetadata: {object} 总结元数据（长度统计、模型信息等）
     *   - dynamicVideoTitle: {string} 动态视频标题（title + " | " + summary）
     */
    addContentSummarizationTask() {
        // 创建一个包装的ContentSummarizationTask，在执行后添加动态标题生成逻辑
        const originalTask = new ContentSummarizationTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 执行原始的ContentSummarizationTask
            const result = await originalExecute(context, progressCallback);

            // 生成动态标题
            if (result.transcriptTitle && result.transcriptSummary) {
                result.dynamicVideoTitle = `${result.transcriptTitle} | ${result.transcriptSummary}`;
                logger.info(`${this.logPrefix}[ContentSummarizationTask] 生成动态标题: "${result.dynamicVideoTitle}"`);
            } else {
                result.dynamicVideoTitle = result.transcriptTitle || '未知标题';
                logger.warn(`${this.logPrefix}[ContentSummarizationTask] 标题或摘要缺失，使用默认标题: "${result.dynamicVideoTitle}"`);
            }

            return result;
        };

        this.processingPipeline.addTask(originalTask);
    }

    /**
     * @功能概述: 添加字幕翻译任务 (TranslateSubtitleTask) 到流水线。
     *           此任务负责将英文字幕翻译为中文，为双语学习提供支持。
     *
     * @上下文输入 (context 预期的字段 for TranslateSubtitleTask):
     *   - correctedSubtitleJsonArray: {Array} (必需) 校正后的英文字幕，来自TranscriptionCorrectionTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @上下文输出 (context 新增的字段 from TranslateSubtitleTask):
     *   - translatedSubtitleJsonArray: {Array} 翻译后的中文字幕JSON数组
     *   - translatedSubtitleJsonPath: {string} 翻译字幕JSON文件路径
     *   - translationStatus: {string} 翻译状态 ('success' | 'failed')
     */
    addTranslateSubtitleTask() {
        this.processingPipeline.addTask(new TranslateSubtitleTask());
    }

    /**
     * @功能概述: 添加字幕挖空任务 (SubtitleClozeTask) 到流水线。
     *           此任务负责创建填空练习版本的字幕，用于语言学习。
     *
     * @上下文输入 (context 预期的字段 for SubtitleClozeTask):
     *   - correctedSubtitleJsonArray: {Array} (必需) 校正后的英文字幕，来自TranscriptionCorrectionTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @上下文输出 (context 新增的字段 from SubtitleClozeTask):
     *   - clozedSubtitleJsonArray: {Array} 挖空后的字幕JSON数组
     *   - clozedSubtitleJsonPath: {string} 挖空字幕JSON文件路径
     *   - clozeStatus: {string} 挖空状态 ('success' | 'failed')
     */
    addSubtitleClozeTask() {
        this.processingPipeline.addTask(new SubtitleClozeTask());
    }

    /**
     * @功能概述: 添加双语字幕合并任务 (BilingualSubtitleMergeTask) 到流水线。
     *           此任务负责将英文、中文和挖空字幕合并为增强双语字幕。
     *
     * @上下文输入 (context 预期的字段 for BilingualSubtitleMergeTask):
     *   - correctedSubtitleJsonArray: {Array} (必需) 校正后的英文字幕，来自TranscriptionCorrectionTask
     *   - translatedSubtitleJsonArray: {Array} (必需) 翻译后的中文字幕，来自TranslateSubtitleTask
     *   - clozedSubtitleJsonArray: {Array} (必需) 挖空后的字幕，来自SubtitleClozeTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 新增的字段 from BilingualSubtitleMergeTask):
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕JSON数组
     *   - enhancedBilingualSubtitleJsonPath: {string} 增强双语字幕JSON文件路径
     *   - bilingualSubtitleMergeTaskStatus: {string} 合并状态 ('success' | 'failed')
     */
    addBilingualSubtitleMergeTask() {
        // 创建一个包装的BilingualSubtitleMergeTask，使用动态标题
        const originalTask = new BilingualSubtitleMergeTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 使用动态标题更新videoIdentifier（如果存在）
            if (context.dynamicVideoTitle) {
                // 清理标题中的特殊字符，用于文件命名
                const cleanTitle = context.dynamicVideoTitle
                    .replace(/[\/\\:*?"<>|]/g, '_')  // 替换文件系统不允许的字符
                    .substring(0, 100);  // 限制长度

                context.dynamicVideoTitleForFile = cleanTitle;
                logger.info(`${this.logPrefix}[BilingualSubtitleMergeTask] 使用动态标题: "${cleanTitle}"`);
            }

            return await originalExecute(context, progressCallback);
        };

        this.processingPipeline.addTask(originalTask);
    }

    /**
     * @功能概述: 添加ASS字幕生成任务 (GenerateASSTask) 到流水线。
     *           此任务负责生成完整的ASS格式字幕文件。
     *
     * @上下文输入 (context 预期的字段 for GenerateASSTask):
     *   - enhancedBilingualSubtitleJsonArray: {Array} (必需) 增强双语字幕，来自BilingualSubtitleMergeTask
     *   - clozedSubtitleJsonArray: {Array} (必需) 挖空字幕，来自SubtitleClozeTask
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioForCloudflareTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 新增的字段 from GenerateASSTask):
     *   - assFilePath: {string} 生成的ASS字幕文件路径
     *   - assContent: {string} 完整的ASS字幕内容
     *   - audioDuration: {number} 音频时长（秒）
     */
    addGenerateASSTask() {
        this.processingPipeline.addTask(new GenerateASSTask());
    }

    /**
     * @功能概述: 添加视频生成任务 (GenerateVideoTask) 到流水线。
     *           此任务负责生成最终的9:16短视频。
     *
     * @上下文输入 (context 预期的字段 for GenerateVideoTask):
     *   - originalVideoPath: {string} (必需) 原始视频文件路径
     *   - assContent: {string} (必需) ASS字幕内容，来自GenerateASSTask
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioForCloudflareTask
     *   - audioDuration: {number} (必需) 音频时长，来自GenerateASSTask
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 新增的字段 from GenerateVideoTask):
     *   - finalVideoPath: {string} 最终生成的视频文件路径
     *   - videoGenerationStats: {object} 视频生成统计信息
     */
    addGenerateVideoTask() {
        // 创建一个包装的GenerateVideoTask，使用动态标题
        const originalTask = new GenerateVideoTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 使用动态标题更新videoIdentifier（如果存在）
            if (context.dynamicVideoTitle) {
                // 清理标题中的特殊字符，用于文件命名
                const cleanTitle = context.dynamicVideoTitle
                    .replace(/[\/\\:*?"<>|]/g, '_')  // 替换文件系统不允许的字符
                    .substring(0, 100);  // 限制长度

                context.dynamicVideoTitleForFile = cleanTitle;
                logger.info(`${this.logPrefix}[GenerateVideoTask] 使用动态标题: "${cleanTitle}"`);
            }

            return await originalExecute(context, progressCallback);
        };

        this.processingPipeline.addTask(originalTask);
    }

    /**
     * @功能概述: 验证输入参数
     * @param {object} context - 输入上下文
     * @throws {Error} 当必需参数缺失时抛出错误
     */
    validateInput(context) {
        const requiredFields = ['videoIdentifier', 'originalVideoPath', 'originalVideoName', 'uploadedVideoDirPath'];

        for (const field of requiredFields) {
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                throw new Error(`缺少必需参数: ${field}`);
            }
        }

        logger.debug(`${this.logPrefix}[validateInput] 输入参数验证通过。`);
    }
}

// 导出TestCloudflareTranscriptionOptimizationPipelineService类
module.exports = TestCloudflareTranscriptionOptimizationPipelineService;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}TestCloudflareTranscriptionOptimizationPipelineService 类已导出。`);
