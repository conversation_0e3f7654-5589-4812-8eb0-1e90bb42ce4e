/**
 * @文件概述: 通用工具函数库 - 纯工具函数集合
 * @重构时间: 2025-06-14
 * @职责范围:
 *   - 时间格式化函数
 *   - 数据验证工具
 *   - 字幕解析工具
 *   - 通用工具函数（防抖、节流等）
 */

/**
 * @功能概述: 解析SRT (SubRip Text) 字幕格式的字符串内容，将其转换为一个包含字幕对象的数组。
 * @param {string} srtContent - 完整的SRT格式字符串。
 * @returns {Array<object>} 一个字幕对象数组，每个对象包含:
 *   - id: {string} 字幕的原始编号。
 *   - start: {number} 字幕开始时间 (秒)。
 *   - end: {number} 字幕结束时间 (秒)。
 *   - text: {string} 字幕文本内容。
 * @日志:
 *   - 当SRT内容无效或为空时，在控制台打印警告。
 *   - 开始解析时，在控制台打印SRT内容长度。
 *   - 解析过程中，如遇到无效时间格式行或不足3行的块，在控制台打印警告。
 *   - 解析完成后，在控制台打印解析出的字幕条数。
 */
const parseSrt = (srtContent) => {
    if (!srtContent || typeof srtContent !== 'string') {
        console.warn('[文件：utils.js][parseSrt] SRT内容无效或为空。');
        return [];
    }
    console.log('[文件：utils.js][parseSrt] 开始解析SRT内容，长度:', srtContent.length);
    const subtitles = [];
    // 统一换行符，然后按空行分割块
    const blocks = srtContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n\n');

    const timeToSeconds = (timeStr) => {
        const parts = timeStr.split(/[:,]/);
        if (parts.length !== 4) return 0;
        return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]) + parseInt(parts[3]) / 1000;
    };

    for (const block of blocks) {
        const lines = block.trim().split('\n');
        if (lines.length >= 3) { // 至少需要编号、时间、文本三行
            const id = lines[0];
            // 修正正则表达式以匹配更严格的时间格式，并允许前后有空格
            const timeMatch = lines[1].match(/^\s*(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})\s*$/);
            if (timeMatch) {
                const start = timeToSeconds(timeMatch[1]);
                const end = timeToSeconds(timeMatch[2]);
                const text = lines.slice(2).join('\n');
                subtitles.push({ id, start, end, text });
            } else {
                 console.warn('[文件：utils.js][parseSrt] 无效的时间格式行:', lines[1], '块内容:', block);
            }
        } else if (block.trim() !== '') { // 如果块不为空但行数不足
            console.warn('[文件：utils.js][parseSrt] 无效的SRT块 (行数不足):', block);
        }
    }
    console.log('[文件：utils.js][parseSrt] SRT解析完成，字幕条数:', subtitles.length);
    return subtitles;
};

/**
 * @功能概述: 将总秒数格式化为 "MM:SS.m" 的显示格式 (例如 "01:23.5")。
 * @param {number | null | undefined} totalSeconds - 需要格式化的总秒数。
 *                                                    如果为 null, undefined, NaN 或负数，则返回 "00:00.0"。
 * @returns {string} 格式化后的时间字符串。
 * @日志: 无直接控制台日志输出。
 */
const formatTimeForDisplay = (totalSeconds) => {
    if (totalSeconds === null || totalSeconds === undefined || isNaN(totalSeconds) || totalSeconds < 0) {
        return '00:00.0';
    }
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    const milliseconds = Math.floor((totalSeconds % 1) * 10); // single digit for milliseconds
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${String(milliseconds)}`;
};

/**
 * @功能概述: 格式化进度百分比，返回带 "%" 符号的整数百分比字符串。
 * @param {number} percentage - 进度百分比 (0-100)。
 * @returns {string} 格式化后的百分比字符串，例如 "75%"。
 * @日志: 无直接控制台日志输出。
 */
const formatProgressText = (percentage) => {
    return `${Math.round(percentage)}%`;
};

// ============================================================================
// 新增工具函数
// ============================================================================

/**
 * @功能概述: 将秒数转换为 HH:MM:SS 格式的时间字符串
 * @参数说明: {number} seconds - 需要转换的秒数
 * @返回值: {string} 格式化后的时间字符串，例如 "01:23:45"
 */
function formatTime(seconds) {
    if (isNaN(seconds) || seconds < 0) return "00:00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}













/**
 * @功能概述: 验证视频生成参数
 * @param {object} params - 待验证的参数对象
 * @returns {{isValid: boolean, message: string}} 验证结果
 */
function validateGenerateParams(params) {
    const logPrefix = '[文件：utils.js][validateGenerateParams]';
    console.log(`${logPrefix} 正在验证生成参数:`, params);

    if (!params.script || params.script.trim() === '') {
        return { isValid: false, message: '视频文案不能为空' };
    }
    if (!params.videoIdentifier || params.videoIdentifier.trim() === '') {
        return { isValid: false, message: '视频ID不能为空' };
    }
    if (!params.voice || params.voice.trim() === '') {
        return { isValid: false, message: '必须选择一个配音' };
    }
    if (!params.videoStyle || params.videoStyle.trim() === '') {
        return { isValid: false, message: '必须选择一个视频风格' };
    }
    // ... 可根据需要添加更多验证规则

    console.log(`${logPrefix} 生成参数验证通过`);
    return { isValid: true, message: '验证通过' };
}





/**
 * @功能概述: 返回当前时间的格式化字符串
 * @返回值: {string} 'HH:MM:SS' 格式的时间字符串
 */
function getCurrentTime() {
    return new Date().toLocaleTimeString('en-US', { hour12: false });
}

/**
 * @功能概述: POST API请求函数（支持Fetch + ReadableStream SSE处理）
 * @参数说明:
 *   - {string} url - 请求地址
 *   - {object} params - 请求参数
 * @返回值: {Promise} API响应Promise
 */
async function postApi(url, params = {}) {
    const logPrefix = '[文件：utils.js][postApi]';

    try {
        console.log(`${logPrefix} 发起POST请求: ${url}`, params);

        // 首先检查是否需要SSE处理（通过URL判断）
        if (url.includes('/generateVideo') || url.includes('/sse')) {
            // 使用Fetch + ReadableStream处理POST SSE响应
            return await handleSSEWithFetchStream(url, params, logPrefix);
        }

        // 普通POST请求
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        const result = await response.json();
        console.log(`${logPrefix} 接口返回信息:`, result);
        return result;

    } catch (error) {
        console.log(`${logPrefix} 请求失败:`, error);
        throw error;
    }
}

/**
 * @功能概述: 使用Fetch + ReadableStream处理POST SSE响应
 * @参数说明:
 *   - {string} url - SSE端点URL
 *   - {object} params - POST请求参数
 *   - {string} logPrefix - 日志前缀
 * @返回值: {Promise} 最终结果Promise
 */
async function handleSSEWithFetchStream(url, params, logPrefix) {
    console.log(`${logPrefix} 使用Fetch + ReadableStream处理POST SSE连接: ${url}`);

    try {
        // 发起POST请求（增加超时控制）
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            console.warn(`${logPrefix} 请求超时，取消连接`);
            controller.abort();
        }, 300000); // 5分钟超时

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(params),
            signal: controller.signal
        });

        // 清除超时定时器
        clearTimeout(timeoutId);

        console.log(`${logPrefix} Fetch响应状态:`, response.status, response.statusText);
        console.log(`${logPrefix} Fetch响应头:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        // 检查是否是SSE响应
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('text/event-stream')) {
            console.warn(`${logPrefix} 响应不是SSE格式，Content-Type: ${contentType}`);
            // 尝试作为普通JSON处理
            const result = await response.json();
            console.log(`${logPrefix} 作为JSON处理的结果:`, result);
            return result;
        }

        // 处理SSE流
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let finalResult = null;
        let buffer = '';
        let currentEventType = null;
        let currentEventData = null;

        console.log(`${logPrefix} 开始读取SSE流数据`);

        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                console.log(`${logPrefix} SSE流读取完成，最终结果状态:`, finalResult ? '已获取' : '未获取');
                break;
            }

            // 解码数据块
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // 按行处理数据
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留最后一个不完整的行

            for (const line of lines) {
                console.log(`${logPrefix} 处理SSE行:`, line);

                if (line.startsWith('event: ')) {
                    currentEventType = line.slice(7);
                    console.log(`${logPrefix} SSE事件类型:`, currentEventType);

                    // 特别关注pipelineComplete事件
                    if (currentEventType === 'pipelineComplete') {
                        console.log(`${logPrefix} 🎯 检测到pipelineComplete事件！`);
                    }
                } else if (line.startsWith('data: ')) {
                    try {
                        const dataStr = line.slice(6); // 移除 'data: ' 前缀

                        // 跳过空数据或心跳
                        if (!dataStr.trim() || dataStr.trim() === '[DONE]') {
                            continue;
                        }

                        currentEventData = JSON.parse(dataStr);
                        console.log(`${logPrefix} 收到SSE数据:`, currentEventData);

                        // 使用SSE事件管理器处理事件
                        if (window.SSEEventManager && currentEventType && currentEventData) {
                            try {
                                // 获取全局的SSE事件管理器实例
                                const sseManager = window.sseEventManagerInstance;
                                if (sseManager) {
                                    sseManager.handleSSEEvent(currentEventType, currentEventData);
                                }
                            } catch (error) {
                                console.error(`${logPrefix} SSE事件管理器处理失败:`, error);
                            }
                        }

                        // 检查是否是最终结果事件
                        console.log(`${logPrefix} 检查最终结果事件: eventType=${currentEventType}, hasResult=${!!currentEventData.result}, pipelineStatus=${currentEventData.pipelineStatus}`);

                        if (currentEventType === 'pipelineComplete' && currentEventData.result) {
                            finalResult = currentEventData.result;
                            console.log(`${logPrefix} 🎉 收到pipelineComplete事件，最终结果:`, finalResult);
                            console.log(`${logPrefix} 🔍 pipelineComplete结果结构分析:`, {
                                hasFiles: !!finalResult.files,
                                hasContext: !!finalResult.context,
                                hasOriginalContext: !!finalResult.originalContext,
                                resultKeys: Object.keys(finalResult),
                                filesKeys: finalResult.files ? Object.keys(finalResult.files) : null,
                                contextKeys: finalResult.context ? Object.keys(finalResult.context) : null
                            });
                            if (finalResult.files) {
                                console.log(`${logPrefix} 📁 files 详细内容:`, JSON.stringify(finalResult.files, null, 2));
                            }
                            if (finalResult.context) {
                                console.log(`${logPrefix} 📄 context 详细内容:`, JSON.stringify(finalResult.context, null, 2));
                            }
                            reader.cancel(); // 取消读取
                            return finalResult;
                        } else if (currentEventData.pipelineStatus === 'completed') {
                            // 检查多种可能的结果字段
                            if (currentEventData.finalResult) {
                                finalResult = currentEventData.finalResult;
                                console.log(`${logPrefix} 🎉 收到completed状态，从finalResult获取最终结果:`, finalResult);
                            } else if (currentEventData.result) {
                                finalResult = currentEventData.result;
                                console.log(`${logPrefix} 🎉 收到completed状态，从result获取最终结果:`, finalResult);
                            } else {
                                // 如果没有明确的结果字段，使用整个事件数据作为结果
                                finalResult = currentEventData;
                                console.log(`${logPrefix} 🎉 收到completed状态，使用完整事件数据作为最终结果:`, finalResult);
                            }

                            console.log(`${logPrefix} 🔍 completed状态结果结构分析:`, {
                                hasFiles: !!finalResult.files,
                                hasContext: !!finalResult.context,
                                hasOriginalContext: !!finalResult.originalContext,
                                resultKeys: Object.keys(finalResult),
                                filesKeys: finalResult.files ? Object.keys(finalResult.files) : null,
                                contextKeys: finalResult.context ? Object.keys(finalResult.context) : null
                            });
                            if (finalResult.files) {
                                console.log(`${logPrefix} 📁 completed files 详细内容:`, JSON.stringify(finalResult.files, null, 2));
                            }
                            if (finalResult.context) {
                                console.log(`${logPrefix} 📄 completed context 详细内容:`, JSON.stringify(finalResult.context, null, 2));
                            }
                            if (finalResult.originalContext) {
                                console.log(`${logPrefix} 📜 completed originalContext 详细内容:`, JSON.stringify(finalResult.originalContext, null, 2));
                            }

                            // 确保结果包含status字段
                            if (finalResult && !finalResult.status) {
                                finalResult.status = 'completed';
                            }

                            reader.cancel(); // 取消读取
                            return finalResult;
                        }

                        // 处理系统状态事件
                        if (currentEventType === 'systemStatus') {
                            if (currentEventData.type === 'connection_closing') {
                                console.log(`${logPrefix} 收到连接关闭通知，但继续等待最终结果`);
                                // 不要在这里结束连接，继续等待可能的最终结果
                            } else if (currentEventData.type === 'pipeline_completed') {
                                console.log(`${logPrefix} 收到流水线完成通知，准备结束连接`);
                                // 流水线完成通知，可以准备结束了
                            }
                        }
                    } catch (e) {
                        console.error(`${logPrefix} 解析SSE数据失败:`, e, '原始数据:', line);
                    }
                } else if (line.startsWith('id: ')) {
                    const eventId = line.slice(4);
                    console.log(`${logPrefix} SSE事件ID:`, eventId);
                } else if (line.startsWith('retry: ')) {
                    const retryTime = line.slice(7);
                    console.log(`${logPrefix} SSE重试时间:`, retryTime);
                } else if (line.trim() === '') {
                    // 空行表示事件结束，重置当前事件状态
                    if (currentEventType && currentEventData) {
                        console.log(`${logPrefix} 完整SSE事件: ${currentEventType}`, currentEventData);
                    }
                    currentEventType = null;
                    currentEventData = null;
                }
            }
        }

        console.log(`${logPrefix} SSE流处理完成，最终结果:`, finalResult);

        // 如果没有获取到最终结果，但有事件数据，尝试构造一个基本结果
        if (!finalResult && currentEventData) {
            console.log(`${logPrefix} 尝试从最后的事件数据构造结果:`, currentEventData);
            finalResult = {
                status: currentEventData.pipelineStatus || 'completed',
                context: currentEventData.context || currentEventData,
                message: '通过SSE事件数据构造的结果'
            };
        }

        return finalResult;

    } catch (error) {
        console.error(`${logPrefix} Fetch + ReadableStream处理失败:`, error);
        throw error;
    }
}



/**
 * @功能概述: 文件上传API请求函数（支持SSE流式响应）
 * @参数说明:
 *   - {string} url - 请求地址
 *   - {FormData} formData - 文件表单数据
 * @返回值: {Promise} API响应Promise
 * @更新说明: 支持SSE流式响应，向前兼容原有JSON响应格式
 */
async function uploadApi(url, formData) {
    const logPrefix = '[文件：utils.js][uploadApi]';

    try {
        console.log(`${logPrefix} 发起文件上传请求: ${url}`);

        // 检查是否是视频上传接口，如果是则使用SSE处理
        if (url.includes('/uploadVideo') || url.includes('/upload')) {
            console.log(`${logPrefix} 检测到视频上传接口，使用SSE流式处理`);
            return await handleUploadSSEWithFetchStream(url, formData, logPrefix);
        }

        // 普通文件上传（向前兼容）
        const response = await fetch(url, {
            method: 'POST',
            body: formData // 不设置Content-Type，让浏览器自动设置
        });

        const result = await response.json();
        console.log(`${logPrefix} 上传接口返回信息:`, result);

        return result;

    } catch (error) {
        console.log(`${logPrefix} 上传请求失败:`, error);
        throw error;
    }
}

/**
 * @功能概述: 使用Fetch + ReadableStream处理文件上传SSE响应
 * @参数说明:
 *   - {string} url - 上传端点URL
 *   - {FormData} formData - 文件表单数据
 *   - {string} logPrefix - 日志前缀
 * @返回值: {Promise} 最终结果Promise
 */
async function handleUploadSSEWithFetchStream(url, formData, logPrefix) {
    console.log(`${logPrefix} 使用Fetch + ReadableStream处理文件上传SSE连接: ${url}`);

    try {
        // 发起文件上传请求（增加超时控制）
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            console.warn(`${logPrefix} 上传请求超时，取消连接`);
            controller.abort();
        }, 600000); // 10分钟超时（文件上传需要更长时间）

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            },
            body: formData, // FormData不需要设置Content-Type
            signal: controller.signal
        });

        // 清除超时定时器
        clearTimeout(timeoutId);

        console.log(`${logPrefix} 上传响应状态:`, response.status, response.statusText);
        console.log(`${logPrefix} 上传响应头:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        // 检查是否是SSE响应
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('text/event-stream')) {
            console.warn(`${logPrefix} 响应不是SSE格式，Content-Type: ${contentType}`);
            // 尝试作为普通JSON处理（向前兼容）
            const result = await response.json();
            console.log(`${logPrefix} 作为JSON处理的上传结果:`, result);
            return result;
        }

        // 处理SSE流
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let finalResult = null;
        let buffer = '';
        let currentEventType = null;
        let currentEventData = null;

        console.log(`${logPrefix} 开始读取上传SSE流数据`);

        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                console.log(`${logPrefix} 上传SSE流读取完成，最终结果状态:`, finalResult ? '已获取' : '未获取');
                break;
            }

            // 解码数据块
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // 按行处理数据
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留最后一个不完整的行

            for (const line of lines) {
                console.log(`${logPrefix} 处理上传SSE行:`, line);

                if (line.startsWith('event: ')) {
                    currentEventType = line.slice(7);
                    console.log(`${logPrefix} 上传SSE事件类型:`, currentEventType);

                    // 特别关注pipelineComplete事件
                    if (currentEventType === 'pipelineComplete') {
                        console.log(`${logPrefix} 🎯 检测到上传pipelineComplete事件！`);
                    }
                } else if (line.startsWith('data: ')) {
                    try {
                        const dataStr = line.slice(6); // 移除 'data: ' 前缀

                        // 跳过空数据或心跳
                        if (!dataStr.trim() || dataStr.trim() === '[DONE]') {
                            continue;
                        }

                        currentEventData = JSON.parse(dataStr);
                        console.log(`${logPrefix} 收到上传SSE数据:`, currentEventData);

                        // 使用SSE事件管理器处理上传事件
                        if (window.SSEEventManager && currentEventType && currentEventData) {
                            try {
                                // 获取全局的SSE事件管理器实例
                                const sseManager = window.sseEventManagerInstance;
                                if (sseManager) {
                                    sseManager.handleSSEEvent(currentEventType, currentEventData);
                                }
                            } catch (error) {
                                console.error(`${logPrefix} 上传SSE事件管理器处理失败:`, error);
                            }
                        }

                        // 检查是否是最终结果事件
                        console.log(`${logPrefix} 检查上传最终结果事件: eventType=${currentEventType}, hasResult=${!!currentEventData.result}, pipelineStatus=${currentEventData.pipelineStatus}`);

                        if (currentEventType === 'pipelineComplete' && currentEventData.result) {
                            finalResult = currentEventData.result;
                            console.log(`${logPrefix} 🎉 收到上传pipelineComplete事件，最终结果:`, finalResult);
                            console.log(`${logPrefix} 🔍 pipelineComplete结果结构分析:`, {
                                hasFiles: !!finalResult.files,
                                hasContext: !!finalResult.context,
                                hasOriginalContext: !!finalResult.originalContext,
                                resultKeys: Object.keys(finalResult),
                                filesKeys: finalResult.files ? Object.keys(finalResult.files) : null,
                                contextKeys: finalResult.context ? Object.keys(finalResult.context) : null
                            });
                            if (finalResult.files) {
                                console.log(`${logPrefix} 📁 files 详细内容:`, JSON.stringify(finalResult.files, null, 2));
                            }
                            if (finalResult.context) {
                                console.log(`${logPrefix} 📄 context 详细内容:`, JSON.stringify(finalResult.context, null, 2));
                            }
                            reader.cancel(); // 取消读取
                            return finalResult;
                        } else if (currentEventData.pipelineStatus === 'completed') {
                            // 检查多种可能的结果字段
                            if (currentEventData.finalResult) {
                                finalResult = currentEventData.finalResult;
                                console.log(`${logPrefix} 🎉 收到上传completed状态，从finalResult获取最终结果:`, finalResult);
                            } else if (currentEventData.result) {
                                finalResult = currentEventData.result;
                                console.log(`${logPrefix} 🎉 收到上传completed状态，从result获取最终结果:`, finalResult);
                            } else {
                                // 如果没有明确的结果字段，使用整个事件数据作为结果
                                finalResult = currentEventData;
                                console.log(`${logPrefix} 🎉 收到上传completed状态，使用完整事件数据作为最终结果:`, finalResult);
                            }

                            console.log(`${logPrefix} 🔍 completed状态结果结构分析:`, {
                                hasFiles: !!finalResult.files,
                                hasContext: !!finalResult.context,
                                hasOriginalContext: !!finalResult.originalContext,
                                resultKeys: Object.keys(finalResult),
                                filesKeys: finalResult.files ? Object.keys(finalResult.files) : null,
                                contextKeys: finalResult.context ? Object.keys(finalResult.context) : null
                            });
                            if (finalResult.files) {
                                console.log(`${logPrefix} 📁 completed files 详细内容:`, JSON.stringify(finalResult.files, null, 2));
                            }
                            if (finalResult.context) {
                                console.log(`${logPrefix} 📄 completed context 详细内容:`, JSON.stringify(finalResult.context, null, 2));
                            }
                            if (finalResult.originalContext) {
                                console.log(`${logPrefix} 📜 completed originalContext 详细内容:`, JSON.stringify(finalResult.originalContext, null, 2));
                            }

                            // 确保结果包含status字段
                            if (finalResult && !finalResult.status) {
                                finalResult.status = 'completed';
                            }

                            reader.cancel(); // 取消读取
                            return finalResult;
                        }

                        // 处理系统状态事件
                        if (currentEventType === 'systemStatus') {
                            if (currentEventData.type === 'connection_closing') {
                                console.log(`${logPrefix} 收到上传连接关闭通知，但继续等待最终结果`);
                            } else if (currentEventData.type === 'pipeline_completed') {
                                console.log(`${logPrefix} 收到上传流水线完成通知，准备结束连接`);
                            }
                        }
                    } catch (e) {
                        console.error(`${logPrefix} 解析上传SSE数据失败:`, e, '原始数据:', line);
                    }
                } else if (line.startsWith('id: ')) {
                    const eventId = line.slice(4);
                    console.log(`${logPrefix} 上传SSE事件ID:`, eventId);
                } else if (line.startsWith('retry: ')) {
                    const retryTime = line.slice(7);
                    console.log(`${logPrefix} 上传SSE重试时间:`, retryTime);
                } else if (line.trim() === '') {
                    // 空行表示事件结束，重置当前事件状态
                    if (currentEventType && currentEventData) {
                        console.log(`${logPrefix} 完整上传SSE事件: ${currentEventType}`, currentEventData);
                    }
                    currentEventType = null;
                    currentEventData = null;
                }
            }
        }

        console.log(`${logPrefix} 上传SSE流处理完成，最终结果:`, finalResult);

        // 如果没有获取到最终结果，但有事件数据，尝试构造一个基本结果
        if (!finalResult && currentEventData) {
            console.log(`${logPrefix} 尝试从最后的上传事件数据构造结果:`, currentEventData);
            finalResult = {
                status: currentEventData.pipelineStatus || 'completed',
                context: currentEventData.context || currentEventData,
                message: '通过上传SSE事件数据构造的结果'
            };
        }

        return finalResult;

    } catch (error) {
        console.error(`${logPrefix} 上传Fetch + ReadableStream处理失败:`, error);
        throw error;
    }
}

/**
 * @功能概述: 获取项目列表
 * @参数说明:
 *   - options: {object} 查询选项
 *     - page: {number} 页码，默认1
 *     - pageSize: {number} 每页数量，默认10
 *     - sortBy: {string} 排序字段，默认uploadTime
 *     - order: {string} 排序方向，默认desc
 * @返回值: {Promise<object>} 包含项目列表和分页信息的响应对象
 * @调用关系: 由项目选择功能调用
 * @日志记录: 记录请求参数、响应状态和错误信息
 */
const fetchProjectList = async (options = {}) => {
    const logPrefix = '[文件：utils.js][fetchProjectList]';

    try {
        // 设置默认参数
        const params = {
            page: options.page || 1,
            pageSize: options.pageSize || 10,
            sortBy: options.sortBy || 'uploadTime',
            order: options.order || 'desc'
        };

        console.log(`${logPrefix} 开始获取项目列表，参数:`, params);

        // 构建查询字符串
        const queryString = new URLSearchParams(params).toString();
        const url = `/api/video/listProjects?${queryString}`;

        console.log(`${logPrefix} 请求URL: ${url}`);

        // 发送GET请求
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log(`${logPrefix} 响应状态: ${response.status}`);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`${logPrefix} 获取项目列表成功，项目数量: ${result.data?.projects?.length || 0}`);

        return result;

    } catch (error) {
        console.error(`${logPrefix} 获取项目列表失败:`, error);
        throw error;
    }
};

/**
 * @功能概述: 获取项目详细信息
 * @参数说明:
 *   - videoIdentifier: {string} 项目标识符，必需参数
 * @返回值: {Promise<object>} 包含项目详情和editorData的响应对象
 * @调用关系: 由项目选择确认功能调用
 * @日志记录: 记录请求参数、响应状态和数据完整性信息
 */
const fetchProjectDetails = async (videoIdentifier) => {
    const logPrefix = '[文件：utils.js][fetchProjectDetails]';

    try {
        if (!videoIdentifier) {
            throw new Error('videoIdentifier参数不能为空');
        }

        console.log(`${logPrefix} 开始获取项目详情，videoIdentifier: ${videoIdentifier}`);

        // 构建请求URL
        const url = `/api/video/projectDetails?videoIdentifier=${encodeURIComponent(videoIdentifier)}`;
        console.log(`${logPrefix} 请求URL: ${url}`);

        // 发送GET请求
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log(`${logPrefix} 响应状态: ${response.status}`);

        if (!response.ok) {
            if (response.status === 404) {
                throw new Error('项目不存在，请检查项目ID是否正确');
            } else if (response.status === 400) {
                throw new Error('请求参数错误');
            } else {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
        }

        const result = await response.json();

        // 验证响应数据结构
        if (result.status === 'success' && result.data?.editorData) {
            const editorData = result.data.editorData;
            const dataIntegrity = result.data.dataIntegrity;

            console.log(`${logPrefix} 项目详情获取成功`);
            console.log(`${logPrefix} editorData字段:`, Object.keys(editorData));
            console.log(`${logPrefix} 数据完整性:`, dataIntegrity);

            // 验证必需字段
            const requiredFields = ['videoIdentifier', 'videoPlaybackUrl', 'originalVideoName'];
            const missingFields = requiredFields.filter(field => !editorData[field]);

            if (missingFields.length > 0) {
                console.warn(`${logPrefix} 警告：缺少必需字段: ${missingFields.join(', ')}`);
            } else {
                console.log(`${logPrefix} 所有必需字段验证通过`);
            }
        } else {
            console.warn(`${logPrefix} 响应数据格式异常:`, result);
        }

        return result;

    } catch (error) {
        console.error(`${logPrefix} 获取项目详情失败:`, error);
        throw error;
    }
};

/**
 * @功能概述: 调用后端API获取指定项目的generated文件列表
 * @param {string} projectId - 项目唯一标识符
 * @param {object} options - 可选参数
 * @param {number} options.limit - 返回数量限制，默认20
 * @param {string} options.sortBy - 排序方式，默认time_desc
 * @returns {Promise<object>} 返回格式: {success: boolean, data?: object, error?: string}
 */
const callListGeneratedFilesAPI = async (projectId, options = {}) => {
    const logPrefix = '[文件：utils.js][callListGeneratedFilesAPI]';

    try {
        console.log(`${logPrefix} 开始获取项目生成文件列表，项目ID: ${projectId}`);

        const { limit = 20, sortBy = 'time_desc' } = options;
        const url = `http://localhost:8081/api/video/listGeneratedFiles/${projectId}?limit=${limit}&sortBy=${sortBy}`;

        console.log(`${logPrefix} 请求URL: ${url}`);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        console.log(`${logPrefix} API响应:`, data);

        if (data.status === 'success') {
            console.log(`${logPrefix} 获取成功，视频数量: ${data.data.totalVideos}, 配对数量: ${data.data.totalPairs}`);
            return { success: true, data: data.data };
        } else {
            console.warn(`${logPrefix} API返回错误: ${data.message}`);
            return { success: false, error: data.message || '获取生成文件列表失败' };
        }

    } catch (error) {
        console.error(`${logPrefix} 请求失败:`, error);
        return { success: false, error: error.message || '网络请求失败' };
    }
};

/**
 * @功能概述: 格式化时间戳为可读格式
 * @param {string} timestamp - ISO时间戳格式 (如: "2025-07-25T23-20-29-805Z")
 * @returns {string} 格式化后的时间字符串
 */
const formatTimestamp = (timestamp) => {
    if (!timestamp) return '未知时间';

    try {
        // 将特殊格式的时间戳转换为标准ISO格式
        // "2025-07-25T23-20-29-805Z" -> "2025-07-25T23:20:29.805Z"
        const standardTimestamp = timestamp
            .replace(/T(\d{2})-(\d{2})-(\d{2})-(\d{3})Z$/, 'T$1:$2:$3.$4Z');

        const date = new Date(standardTimestamp);

        if (isNaN(date.getTime())) {
            console.warn('[文件：utils.js][formatTimestamp] 无效的时间戳格式:', timestamp);
            return timestamp; // 返回原始值
        }

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

    } catch (error) {
        console.error('[文件：utils.js][formatTimestamp] 时间格式化失败:', error);
        return timestamp; // 返回原始值
    }
};



// 将所有工具函数聚合到Utils对象中，并挂载到window上
window.Utils = {
    parseSrt,
    formatTimeForDisplay,
    formatProgressText,
    formatTime,
    validateGenerateParams,
    getCurrentTime,
    postApi,
    uploadApi,
    // 项目管理相关API
    fetchProjectList,
    fetchProjectDetails,
    // 生成文件管理相关API
    callListGeneratedFilesAPI,
    formatTimestamp
};

console.log('[文件：utils.js][模块加载] 所有工具函数已挂载到 window.Utils');
