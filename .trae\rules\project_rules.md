# 项目规则索引

本文档是此代码库中所有活动规则的中心索引。

## 通用与核心 (General & Core)

基础的项目指南和结构。

| 规则文件 | 描述 |
| :------- | :--- |
| [general_principles.mdc](general_principles.mdc) | 项目通用原则与特性指南 |
| [code-commenting-standard.md](code-commenting-standard.md) | 代码注释编写标准与习惯 |
| [file_structure.md](file_structure.md) | 项目文件结构与特定目录说明 |
| [design.md](design.md) | 项目设计规范与架构指导 |

## API与接口规范 (API & Interface Standards)

API创建和接口相关的规范。

| 规则文件 | 描述 |
| :------- | :--- |
| [API_CREATION_GUIDELINES.md](API_CREATION_GUIDELINES.md) | API创建开发指导原则 |
| [llm_api_standard.md](llm_api_standard.md) | LLM API调用标准化规范 - 统一接口与增强功能 |
| [sse_event_standard.md](sse_event_standard.md) | SSE 事件数据结构与实现标准 |

## 架构与开发规范 (Architecture & Development Standards)

代码架构和开发相关的规范。

| 规则文件 | 描述 |
| :------- | :--- |
| [pipeline_architecture.md](pipeline_architecture.md) | 流水线架构实现规范 |
| [task_development_standard.md](task_development_standard.md) | 任务开发标准与进度回调使用规范 |
| [frontend_development_standard.md](frontend_development_standard.md) | 前端开发标准与WordPress兼容性要求 |
| [remotion_architecture_guide.md](remotion_architecture_guide.md) | Remotion架构指导规范 |

## 工具与配置 (Tools & Configuration)

工具配置和增强功能相关的规范。

| 规则文件 | 描述 |
| :------- | :--- |
| [augment_rules.md](augment_rules.md) | 增强工具配置规则 |

## 文件说明

- **总计文件数**: 13个规则文件
- **最后更新**: 2025-08-06
- **文件格式**: Markdown (.md) 和 MDC (.mdc)

## 使用说明

1. 点击文件名可直接访问对应的规则文档
2. 所有规则文件都位于 `.trae/rules/` 目录下
3. 建议按分类查找相关规范
4. 新增规则文件时请及时更新此索引



