# TranslateSubtitleTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **correctedSubtitleJsonArray** (Array): 校正后字幕JSON数组，来自TranscriptionCorrectionTask
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **translationModel** (string): 翻译模型，默认'google/gemini-2.5-flash-lite-preview-06-17'
- **targetLanguage** (string): 目标语言，默认'zh-CN'（简体中文）
- **temperature** (number): 创造性控制，默认0.2
- **maxTokens** (number): 输出限制，默认6000

## 2. 输出上下文参数 (Output Context)

- **translatedSubtitleJsonArray** (Array): 翻译后的字幕JSON数组
- **translatedSubtitleJsonPath** (string): 翻译字幕JSON文件路径
- **translatedChineseSrtContent** (string): 翻译后的中文SRT内容
- **translatedChineseSrtPath** (string): 翻译中文SRT文件路径
- **translationStats** (object): 翻译统计信息
  - **totalSegments** (number): 总片段数
  - **translatedSegments** (number): 翻译片段数
  - **targetLanguage** (string): 目标语言
  - **modelUsed** (string): 使用的翻译模型
- **translationStatus** (string): 翻译状态，成功时为'success'
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 输入格式（correctedSubtitleJsonArray）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "On this Sunday night, Trump's crackdown on protests in Los Angeles.",
    "words": [
      {"text": "On", "start": 0.16, "end": 0.3},
      {"text": "this", "start": 0.3, "end": 0.5}
    ]
  }
]
```

### LLM提示词参数格式
```json
{
  "sourceLanguage": "English",
  "targetLanguage": "Chinese",
  "json_subtitle_array_string": "[{\"id\":\"1\",\"start\":0.16,\"end\":4.32,\"text\":\"On this Sunday night, Trump's crackdown on protests in Los Angeles.\"}]",
  "fullTranscriptionContext": "完整的视频转录文本，用于理解全局语境..."
}
```

### LLM输出格式（翻译后）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "今晚这个周日，特朗普镇压洛杉矶的抗议活动。"
  }
]
```

### 最终输出格式（重新合并words字段）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "今晚这个周日，特朗普镇压洛杉矶的抗议活动。",
    "words": [
      {"text": "今晚", "start": 0.16, "end": 0.3, "original": "On"},
      {"text": "这个", "start": 0.3, "end": 0.5, "original": "this"}
    ]
  }
]
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 翻译字幕JSON文件
- **.srt**: 翻译中文SRT文件

### 文件命名规则
- **翻译字幕**: `{videoIdentifier}_translated_subtitle.json`
- **翻译SRT**: `{videoIdentifier}_chinese.srt`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保中文字符正确保存

## 5. 执行逻辑概述

字幕翻译任务负责将英文字幕翻译为中文，提供双语学习支持。任务首先验证输入数据的完整性，然后使用LLM进行智能翻译，考虑上下文语境和语言习惯。翻译过程保持原有的时间戳信息，确保中英文字幕的时间同步。LLM会根据视频内容的专业领域和语言风格进行适应性翻译，提供自然流畅的中文表达。翻译后的数据包含原文和译文的对应关系，支持词级别的对照学习。任务生成包含翻译信息的JSON文件和标准SRT格式的中文字幕文件，为双语视频制作和语言学习提供完整的翻译支持。
