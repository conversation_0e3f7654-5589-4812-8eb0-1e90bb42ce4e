/**
 * Cloudflare API 标准格式转换测试
 * 验证GetTranscriptionTaskByCloudflare返回标准格式
 */

const GetTranscriptionTaskByCloudflare = require('../GetTranscriptionTaskByCloudflare');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    reqId: 'standard-format-test',
    videoIdentifier: 'test_standard_format',
    audioFilePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3',
    savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
};

async function testStandardFormat() {
    console.log('🔧 开始测试 Cloudflare API 标准格式转换');
    console.log('📊 测试音频文件:', TEST_CONFIG.audioFilePath);
    
    // 检查音频文件是否存在
    if (!fs.existsSync(TEST_CONFIG.audioFilePath)) {
        console.error('❌ 测试音频文件不存在:', TEST_CONFIG.audioFilePath);
        return;
    }
    
    try {
        // 创建任务实例
        const task = new GetTranscriptionTaskByCloudflare();
        
        // 构建上下文
        const context = {
            reqId: TEST_CONFIG.reqId,
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            audioFilePathInUploads: TEST_CONFIG.audioFilePath,
            savePath: TEST_CONFIG.savePath
        };
        
        console.log('🚀 开始调用 Cloudflare Workers AI API...');
        const startTime = Date.now();
        
        // 执行转录任务
        const result = await task.execute(context);
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log('✅ API 调用成功，耗时:', duration, '秒');
        console.log('📊 转录状态:', result.transcriptionStatus);
        
        // 读取生成的转录文件
        const transcriptionPath = path.join(TEST_CONFIG.savePath, `${TEST_CONFIG.videoIdentifier}_transcription.json`);
        if (fs.existsSync(transcriptionPath)) {
            const transcriptionData = JSON.parse(fs.readFileSync(transcriptionPath, 'utf8'));
            
            console.log('\n📋 转录结果分析:');
            console.log('- 语言:', transcriptionData.language);
            console.log('- 时长:', transcriptionData.duration, '秒');
            console.log('- 文本长度:', transcriptionData.text.length, '字符');
            console.log('- segments 数量:', transcriptionData.segments.length);
            
            // 验证标准格式
            console.log('\n🎯 标准格式验证:');
            
            // 检查顶级字段
            const requiredTopFields = ['task', 'language', 'duration', 'text', 'segments'];
            const missingTopFields = requiredTopFields.filter(field => !(field in transcriptionData));
            if (missingTopFields.length === 0) {
                console.log('✅ 顶级字段完整:', requiredTopFields.join(', '));
            } else {
                console.log('❌ 缺少顶级字段:', missingTopFields.join(', '));
            }
            
            // 检查segments格式
            if (transcriptionData.segments && transcriptionData.segments.length > 0) {
                const firstSegment = transcriptionData.segments[0];
                const requiredSegmentFields = ['id', 'seek', 'start', 'end', 'text', 'tokens', 'temperature', 'avg_logprob', 'compression_ratio', 'no_speech_prob', 'words'];
                const missingSegmentFields = requiredSegmentFields.filter(field => !(field in firstSegment));
                
                if (missingSegmentFields.length === 0) {
                    console.log('✅ segment字段完整:', requiredSegmentFields.join(', '));
                } else {
                    console.log('❌ segment缺少字段:', missingSegmentFields.join(', '));
                }
                
                // 检查words格式
                if (firstSegment.words && firstSegment.words.length > 0) {
                    const firstWord = firstSegment.words[0];
                    const requiredWordFields = ['text', 'start', 'end'];
                    const missingWordFields = requiredWordFields.filter(field => !(field in firstWord));
                    const extraWordFields = Object.keys(firstWord).filter(field => !requiredWordFields.includes(field));
                    
                    if (missingWordFields.length === 0) {
                        console.log('✅ words字段完整:', requiredWordFields.join(', '));
                    } else {
                        console.log('❌ words缺少字段:', missingWordFields.join(', '));
                    }
                    
                    if (extraWordFields.length === 0) {
                        console.log('✅ words无多余字段');
                    } else {
                        console.log('⚠️  words有多余字段:', extraWordFields.join(', '));
                    }
                    
                    // 显示第一个word示例
                    console.log('📝 第一个word示例:', JSON.stringify(firstWord, null, 2));
                    
                    // 验证text字段内容
                    if (firstWord.text && typeof firstWord.text === 'string' && firstWord.text.trim().length > 0) {
                        console.log('✅ words.text字段有效内容');
                    } else {
                        console.log('❌ words.text字段无效或为空');
                    }
                } else {
                    console.log('❌ segment.words数组为空');
                }
                
                // 统计words总数
                let totalWords = 0;
                transcriptionData.segments.forEach(segment => {
                    if (segment.words && Array.isArray(segment.words)) {
                        totalWords += segment.words.length;
                    }
                });
                console.log('📊 总计words数量:', totalWords);
                
                // 检查是否有空text的segments
                const emptyTextSegments = transcriptionData.segments.filter(seg => !seg.text || seg.text.trim() === '');
                if (emptyTextSegments.length === 0) {
                    console.log('✅ 所有segments都有有效文本');
                } else {
                    console.log('❌ 发现空文本segments:', emptyTextSegments.length, '个');
                }
                
            } else {
                console.log('❌ segments数组为空');
            }
            
        } else {
            console.error('❌ 转录文件未生成:', transcriptionPath);
        }
        
        console.log('\n🎉 标准格式测试完成！');
        
    } catch (error) {
        console.error('❌ 标准格式测试失败:', error.message);
        console.error('📋 错误详情:', error);
    }
}

// 运行标准格式测试
if (require.main === module) {
    testStandardFormat().catch(console.error);
}

module.exports = { testStandardFormat };
