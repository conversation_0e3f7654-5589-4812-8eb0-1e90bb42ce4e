/**
 * @文件名: sse-event-manager.js
 * @功能概述: SSE事件管理器，统一处理所有SSE事件的解析、分类和状态管理
 * @创建时间: 2025-07-04
 * @遵循规范: .cursor/rules/sse_event_standard.mdc
 */

/**
 * SSE事件管理器类
 * 负责统一管理SSE事件的接收、解析、分类和状态更新
 */
class SSEEventManager {
    constructor() {
        this.logPrefix = '[文件：sse-event-manager.js][SSEEventManager]';
        
        // SSE连接状态
        this.connectionStatus = {
            isConnected: false,
            connectionId: null,
            startTime: null,
            lastHeartbeat: null,
            errorCount: 0
        };
        
        // 流水线状态
        this.pipelineStatus = {
            name: null,
            status: null, // 'running', 'completed', 'failed'
            progress: 0,
            startTime: null,
            endTime: null,
            errorDetails: null
        };
        
        // 当前任务状态
        this.currentTask = {
            name: null,
            status: null,
            detail: null,
            startTime: null,
            errorInfo: null
        };
        
        // 任务历史记录
        this.taskHistory = [];
        
        // 状态更新回调函数
        this.statusUpdateCallbacks = [];
        
        console.log(`${this.logPrefix} SSE事件管理器初始化完成`);
    }
    
    /**
     * 注册状态更新回调函数
     * @param {Function} callback - 状态更新时的回调函数
     */
    registerStatusUpdateCallback(callback) {
        if (typeof callback === 'function') {
            this.statusUpdateCallbacks.push(callback);
            console.log(`${this.logPrefix} 注册状态更新回调函数`);
        }
    }
    
    /**
     * 触发状态更新回调
     */
    triggerStatusUpdate() {
        this.statusUpdateCallbacks.forEach(callback => {
            try {
                callback({
                    connection: this.connectionStatus,
                    pipeline: this.pipelineStatus,
                    currentTask: this.currentTask,
                    taskHistory: this.taskHistory
                });
            } catch (error) {
                console.error(`${this.logPrefix} 状态更新回调执行失败:`, error);
            }
        });
    }
    
    /**
     * 处理SSE事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    handleSSEEvent(eventType, eventData) {
        console.log(`${this.logPrefix} 处理SSE事件: ${eventType}`, eventData);
        
        try {
            switch (eventType) {
                case 'pipelineProgress':
                    this.handlePipelineProgressEvent(eventData);
                    break;
                case 'pipelineComplete':
                    this.handlePipelineCompleteEvent(eventData);
                    break;
                case 'heartbeat':
                    this.handleHeartbeatEvent(eventData);
                    break;
                case 'controllerStatus':
                    this.handleControllerStatusEvent(eventData);
                    break;
                default:
                    console.log(`${this.logPrefix} 未知事件类型: ${eventType}`);
                    break;
            }
            
            // 触发状态更新
            this.triggerStatusUpdate();
            
        } catch (error) {
            console.error(`${this.logPrefix} 处理SSE事件失败:`, error);
            this.connectionStatus.errorCount++;
            this.triggerStatusUpdate();
        }
    }
    
    /**
     * 处理流水线进度事件
     */
    handlePipelineProgressEvent(eventData) {
        console.log(`${this.logPrefix} 处理流水线进度事件`, eventData);
        
        // 更新流水线状态
        if (eventData.pipelineName) {
            this.pipelineStatus.name = eventData.pipelineName;
        }
        
        if (eventData.pipelineStatus) {
            this.pipelineStatus.status = eventData.pipelineStatus;
            
            // 检查是否是失败状态
            if (eventData.pipelineStatus === 'failed') {
                this.pipelineStatus.endTime = new Date().toISOString();
                this.pipelineStatus.errorDetails = eventData.failedTaskErrorDetails || eventData.errorDetails;
            }
        }
        
        // 更新当前任务信息
        if (eventData.currentTaskName) {
            this.currentTask.name = eventData.currentTaskName;
            this.currentTask.status = eventData.currentTaskStatus;
            this.currentTask.detail = eventData.currentTaskDetail;
            
            // 如果是新任务，记录开始时间
            if (this.currentTask.name !== eventData.currentTaskName) {
                this.currentTask.startTime = new Date().toISOString();
            }
        }
        
        // 更新任务历史
        if (eventData.tasksSnapshot && Array.isArray(eventData.tasksSnapshot)) {
            this.updateTaskHistory(eventData.tasksSnapshot);
        }
        
        // 更新进度
        if (eventData.progress !== undefined) {
            this.pipelineStatus.progress = eventData.progress;
        }
    }
    
    /**
     * 处理流水线完成事件
     */
    handlePipelineCompleteEvent(eventData) {
        console.log(`${this.logPrefix} 处理流水线完成事件`, eventData);
        
        this.pipelineStatus.status = 'completed';
        this.pipelineStatus.endTime = new Date().toISOString();
        this.pipelineStatus.progress = 100;
        
        // 清空当前任务
        this.currentTask = {
            name: null,
            status: null,
            detail: null,
            startTime: null,
            errorInfo: null
        };
    }
    
    /**
     * 处理心跳事件
     */
    handleHeartbeatEvent(eventData) {
        this.connectionStatus.lastHeartbeat = new Date().toISOString();
        if (eventData.connectionId) {
            this.connectionStatus.connectionId = eventData.connectionId;
        }
    }
    
    /**
     * 处理控制器状态事件
     */
    handleControllerStatusEvent(eventData) {
        console.log(`${this.logPrefix} 处理控制器状态事件`, eventData);
        
        if (eventData.pipelineStatus === 'ACCEPTED') {
            this.pipelineStatus.name = eventData.message || '视频生成流水线';
            this.pipelineStatus.status = 'running';
            this.pipelineStatus.startTime = new Date().toISOString();
            this.connectionStatus.isConnected = true;
            this.connectionStatus.startTime = new Date().toISOString();
        }
    }
    
    /**
     * 更新任务历史记录
     */
    updateTaskHistory(tasksSnapshot) {
        this.taskHistory = tasksSnapshot.map(task => ({
            name: task.name,
            status: task.status,
            resultPreview: task.resultPreview,
            errorSummary: task.errorSummary,
            timestamp: new Date().toISOString()
        }));
    }
    
    /**
     * 重置所有状态
     */
    reset() {
        console.log(`${this.logPrefix} 重置所有状态`);
        
        this.connectionStatus = {
            isConnected: false,
            connectionId: null,
            startTime: null,
            lastHeartbeat: null,
            errorCount: 0
        };
        
        this.pipelineStatus = {
            name: null,
            status: null,
            progress: 0,
            startTime: null,
            endTime: null,
            errorDetails: null
        };
        
        this.currentTask = {
            name: null,
            status: null,
            detail: null,
            startTime: null,
            errorInfo: null
        };
        
        this.taskHistory = [];
        
        this.triggerStatusUpdate();
    }
    
    /**
     * 获取当前状态摘要
     */
    getStatusSummary() {
        return {
            connection: this.connectionStatus,
            pipeline: this.pipelineStatus,
            currentTask: this.currentTask,
            taskHistory: this.taskHistory,
            isGenerationCompleted: this.pipelineStatus.status === 'completed',
            isGenerationFailed: this.pipelineStatus.status === 'failed',
            isGenerationRunning: this.pipelineStatus.status === 'running'
        };
    }
}

// 导出SSE事件管理器类
window.SSEEventManager = SSEEventManager;

console.log('[文件：sse-event-manager.js] SSE事件管理器模块加载完成');
