# GetTranscriptionTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **audioFilePath** (string): 音频文件完整路径
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **locale** (string): 语言区域设置，默认从配置获取
- **profanityFilterMode** (string): 亵渎内容过滤模式，默认从配置获取
- **timeout** (number): API超时时间（毫秒），默认300000（5分钟）

## 2. 输出上下文参数 (Output Context)

- **transcriptionData** (object): 完整的转录数据对象
- **transcriptionJsonPath** (string): 保存的转录JSON文件路径
- **transcriptionSegments** (Array): 转录片段数组
- **transcriptionStatus** (string): 转录状态，成功时为'success'
- **audioFileIdentifier** (string): 音频文件标识符
- **processingStats** (object): 处理统计信息
  - **totalSegments** (number): 总片段数
  - **totalDuration** (number): 总时长
  - **apiResponseTime** (number): API响应时间
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### Azure API请求格式
```json
{
  "definition": {
    "locales": ["en-US"],
    "profanityFilterMode": "Masked",
    "channels": [0, 1]
  }
}
```

### 转录响应格式
```json
{
  "combinedRecognizedPhrases": [
    {
      "channel": 0,
      "lexical": "hello world",
      "itn": "hello world",
      "maskedITN": "hello world",
      "display": "Hello world."
    }
  ],
  "recognizedPhrases": [
    {
      "recognitionStatus": "Success",
      "channel": 0,
      "offset": "PT0S",
      "duration": "PT2.5S",
      "offsetInTicks": 0,
      "durationInTicks": 25000000,
      "nBest": [
        {
          "confidence": 0.95,
          "lexical": "hello world",
          "itn": "hello world",
          "maskedITN": "hello world",
          "display": "Hello world.",
          "words": [
            {
              "word": "Hello",
              "offset": "PT0S",
              "duration": "PT1S",
              "offsetInTicks": 0,
              "durationInTicks": 10000000,
              "confidence": 0.98
            }
          ]
        }
      ]
    }
  ]
}
```

## 4. 文件操作

### 保存的文件格式
- **.json**: Azure转录结果JSON文件

### 文件命名规则
- **模式**: `{videoIdentifier}_transcription.json`
- **示例**: `video123_transcription.json`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保多语言字符正确保存

## 5. 执行逻辑概述

Azure语音转录任务负责将音频文件转录为文本，使用Azure Speech Service的Fast Transcription API。任务首先验证输入参数和音频文件的存在性，然后构建multipart/form-data请求，包含音频文件和转录配置。通过HTTP POST请求调用Azure API，支持详细的进度报告和超时控制。API返回包含词级别时间戳的详细转录结果，任务将原始响应保存为JSON文件，并提取关键信息到上下文中。整个过程提供完善的错误处理和重试机制，确保转录任务的可靠性和稳定性。
