# continueRender()

## 概述

`continueRender()` 用于解除通过 [`delayRender()`](./delayRender.md) 延迟的渲染。当异步操作完成后，必须调用此函数来通知 Remotion 可以继续渲染过程。

## 语法

```typescript
import { continueRender } from "remotion";

continueRender(handle);
```

## 参数

- **handle**: `DelayRenderHandle` - 由 `delayRender()` 返回的句柄

## 基础用法

```typescript
import { useEffect, useState } from "react";
import { delayRender, continueRender } from "remotion";

const MyComponent = () => {
  const [data, setData] = useState(null);
  const [handle] = useState(() => delayRender("获取数据"));

  useEffect(() => {
    fetch("/api/data")
      .then(res => res.json())
      .then(data => {
        setData(data);
        continueRender(handle); // 解除渲染延迟
      })
      .catch(error => {
        console.error("数据获取失败:", error);
        continueRender(handle); // 即使失败也要继续渲染
      });
  }, [handle]);

  return (
    <div>
      {data ? `数据: ${JSON.stringify(data)}` : "加载中..."}
    </div>
  );
};
```

## 重要原则

### 1. 必须调用
每个 `delayRender()` 调用都必须有对应的 `continueRender()` 调用，否则渲染会超时失败。

### 2. 错误处理
即使异步操作失败，也必须调用 `continueRender()` 以避免渲染超时：

```typescript
useEffect(() => {
  fetch("/api/data")
    .then(data => {
      // 成功处理
      continueRender(handle);
    })
    .catch(error => {
      // 失败也要继续渲染
      console.error(error);
      continueRender(handle);
    });
}, [handle]);
```

### 3. 一对一关系
每个 `delayRender()` 句柄只能调用一次 `continueRender()`：

```typescript
const handle = delayRender("操作");

// ✅ 正确
continueRender(handle);

// ❌ 错误 - 不要重复调用同一个句柄
// continueRender(handle);
```

## 实际应用示例

### 1. 多个异步操作

```typescript
const MultiAsyncComponent = () => {
  const [data1, setData1] = useState(null);
  const [data2, setData2] = useState(null);
  const [handle1] = useState(() => delayRender("获取数据1"));
  const [handle2] = useState(() => delayRender("获取数据2"));

  useEffect(() => {
    // 第一个异步操作
    fetch("/api/data1")
      .then(res => res.json())
      .then(data => {
        setData1(data);
        continueRender(handle1); // 解除第一个延迟
      })
      .catch(() => continueRender(handle1));

    // 第二个异步操作
    fetch("/api/data2")
      .then(res => res.json())
      .then(data => {
        setData2(data);
        continueRender(handle2); // 解除第二个延迟
      })
      .catch(() => continueRender(handle2));
  }, [handle1, handle2]);

  return (
    <div>
      <div>数据1: {data1 ? JSON.stringify(data1) : "加载中..."}</div>
      <div>数据2: {data2 ? JSON.stringify(data2) : "加载中..."}</div>
    </div>
  );
};
```

### 2. 条件性继续渲染

```typescript
const ConditionalContinue = ({ shouldWait }: { shouldWait: boolean }) => {
  const [data, setData] = useState(null);
  const [handle] = useState(() => 
    shouldWait ? delayRender("条件等待") : null
  );

  useEffect(() => {
    if (shouldWait && handle) {
      setTimeout(() => {
        setData("延迟数据");
        continueRender(handle);
      }, 2000);
    }
  }, [shouldWait, handle]);

  return (
    <div>
      {shouldWait ? (
        data ? `延迟数据: ${data}` : "等待中..."
      ) : (
        "无需等待"
      )}
    </div>
  );
};
```

### 3. 清理函数中的处理

```typescript
const CleanupComponent = () => {
  const [handle] = useState(() => delayRender("清理示例"));

  useEffect(() => {
    let isCancelled = false;

    fetch("/api/data")
      .then(res => res.json())
      .then(data => {
        if (!isCancelled) {
          // 只有在组件未卸载时才继续渲染
          continueRender(handle);
        }
      })
      .catch(() => {
        if (!isCancelled) {
          continueRender(handle);
        }
      });

    return () => {
      isCancelled = true;
      // 注意: 如果组件卸载但异步操作未完成，
      // 仍需要调用 continueRender 以避免超时
    };
  }, [handle]);

  return <div>清理示例组件</div>;
};
```

## 与其他API结合使用

### 1. 与 cancelRender 结合

```typescript
import { cancelRender } from "remotion";

const ErrorHandlingComponent = () => {
  const [handle] = useState(() => delayRender("关键操作"));

  useEffect(() => {
    fetch("/api/critical-data")
      .then(res => {
        if (!res.ok) {
          throw new Error(`HTTP错误: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        // 成功时继续渲染
        continueRender(handle);
      })
      .catch(error => {
        // 关键错误时取消整个渲染
        cancelRender(`关键操作失败: ${error.message}`);
        // 注意: cancelRender 会自动处理所有 delayRender 调用
      });
  }, [handle]);

  return <div>关键操作处理中...</div>;
};
```

### 2. 与 useBufferState 结合

```typescript
import { useBufferState } from "remotion";

const BufferedComponent = () => {
  const buffer = useBufferState();
  const [handle] = useState(() => delayRender("缓冲操作"));

  useEffect(() => {
    const delayHandle = buffer.delayPlayback();

    fetch("/api/data")
      .then(res => res.json())
      .then(data => {
        // 解除播放延迟
        delayHandle.unblock();
        // 继续渲染
        continueRender(handle);
      })
      .catch(() => {
        delayHandle.unblock();
        continueRender(handle);
      });

    return () => {
      delayHandle.unblock();
    };
  }, [buffer, handle]);

  return <div>缓冲组件</div>;
};
```

## 最佳实践

### 1. 使用 try-finally 确保调用

```typescript
const SafeComponent = () => {
  const [handle] = useState(() => delayRender("安全操作"));

  useEffect(() => {
    const performOperation = async () => {
      try {
        const data = await fetch("/api/data").then(res => res.json());
        // 处理数据
      } catch (error) {
        console.error("操作失败:", error);
      } finally {
        // 无论成功失败都继续渲染
        continueRender(handle);
      }
    };

    performOperation();
  }, [handle]);

  return <div>安全操作组件</div>;
};
```

### 2. 避免重复调用

```typescript
const AvoidDuplicateComponent = () => {
  const [handle] = useState(() => delayRender("避免重复"));
  const [completed, setCompleted] = useState(false);

  useEffect(() => {
    if (completed) return;

    fetch("/api/data")
      .then(data => {
        if (!completed) {
          setCompleted(true);
          continueRender(handle);
        }
      })
      .catch(() => {
        if (!completed) {
          setCompleted(true);
          continueRender(handle);
        }
      });
  }, [handle, completed]);

  return <div>避免重复调用示例</div>;
};
```

### 3. 超时保护

```typescript
const TimeoutProtectedComponent = () => {
  const [handle] = useState(() => delayRender("超时保护", {
    timeoutInMilliseconds: 10000 // 10秒超时
  }));

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let completed = false;

    const complete = () => {
      if (!completed) {
        completed = true;
        clearTimeout(timeoutId);
        continueRender(handle);
      }
    };

    // 设置备用超时
    timeoutId = setTimeout(() => {
      console.warn("操作超时，强制继续渲染");
      complete();
    }, 8000); // 比 delayRender 超时稍早

    fetch("/api/data")
      .then(data => complete())
      .catch(() => complete());

    return () => {
      clearTimeout(timeoutId);
    };
  }, [handle]);

  return <div>超时保护组件</div>;
};
```

## 调试技巧

### 1. 添加日志

```typescript
const DebuggingComponent = () => {
  const [handle] = useState(() => {
    console.log("创建 delayRender 句柄");
    return delayRender("调试操作");
  });

  useEffect(() => {
    console.log("开始异步操作");
    
    fetch("/api/data")
      .then(data => {
        console.log("异步操作成功，调用 continueRender");
        continueRender(handle);
      })
      .catch(error => {
        console.log("异步操作失败，仍调用 continueRender");
        continueRender(handle);
      });
  }, [handle]);

  return <div>调试组件</div>;
};
```

### 2. 状态跟踪

```typescript
const StateTrackingComponent = () => {
  const [status, setStatus] = useState("初始化");
  const [handle] = useState(() => delayRender("状态跟踪"));

  useEffect(() => {
    setStatus("开始加载");
    
    fetch("/api/data")
      .then(data => {
        setStatus("加载成功");
        continueRender(handle);
      })
      .catch(error => {
        setStatus("加载失败");
        continueRender(handle);
      });
  }, [handle]);

  return <div>状态: {status}</div>;
};
```

## 常见错误

### 1. 忘记调用 continueRender
```typescript
// ❌ 错误 - 忘记调用 continueRender
const BadComponent = () => {
  const [handle] = useState(() => delayRender("忘记继续"));
  
  useEffect(() => {
    fetch("/api/data").then(data => {
      // 忘记调用 continueRender(handle);
    });
  }, [handle]);
  
  return <div>错误示例</div>;
};
```

### 2. 条件性调用导致遗漏
```typescript
// ❌ 错误 - 某些条件下不调用 continueRender
const ConditionalBadComponent = () => {
  const [handle] = useState(() => delayRender("条件错误"));
  
  useEffect(() => {
    fetch("/api/data")
      .then(data => {
        if (data.success) {
          continueRender(handle); // 只在成功时调用
        }
        // 失败时忘记调用 continueRender
      });
  }, [handle]);
  
  return <div>条件错误示例</div>;
};
```

## 相关 API

- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`cancelRender()`](./cancelRender.md) - 取消渲染
- [`useBufferState()`](./useBufferState.md) - 缓冲状态管理

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/delay-render.ts)
