# getRemotionEnvironment()

## 概述

`getRemotionEnvironment()` 函数用于获取当前 Remotion 环境的信息。它返回一个包含环境状态的对象，帮助您根据不同的运行环境调整组件或函数的行为。

**版本要求**: v4.0.25+

## 语法

```typescript
import { getRemotionEnvironment } from "remotion";

const environment = getRemotionEnvironment();
```

## 返回值

返回一个包含以下属性的对象：

### isStudio
- **类型**: `boolean`
- **描述**: 是否在 [Remotion Studio](/docs/cli/studio) 中运行

### isRendering
- **类型**: `boolean`
- **描述**: 是否在渲染过程中运行

### isPlayer
- **类型**: `boolean`
- **描述**: 是否有 [`<Player>`](/docs/player) 组件挂载在当前页面

### isReadOnlyStudio (v4.0.238+)
- **类型**: `boolean`
- **描述**: 是否在[静态部署的 Studio](https://www.remotion.dev/docs/studio/deploy-static) 中，此时无法使用 [`@remotion/studio`](/docs/studio/api) API

## 基础用法

### 1. 基础环境检测

```typescript
import React from 'react';
import { getRemotionEnvironment } from 'remotion';

export const MyComp: React.FC = () => {
  const { isStudio, isPlayer, isRendering, isReadOnlyStudio } = getRemotionEnvironment();

  if (isStudio) {
    return <div>我在 Studio 中运行！</div>;
  }

  if (isPlayer) {
    return <div>我在 Player 中运行！</div>;
  }

  if (isRendering) {
    return <div>我正在渲染中</div>;
  }

  return <div>Hello World!</div>;
};
```

### 2. 条件性样式应用

```typescript
import React from 'react';
import { getRemotionEnvironment } from 'remotion';

const ConditionalStyling = () => {
  const { isStudio, isRendering } = getRemotionEnvironment();

  const baseStyle = {
    width: 200,
    height: 200,
    backgroundColor: '#3498db',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: 18
  };

  const studioStyle = {
    ...baseStyle,
    border: '3px dashed #e74c3c',
    borderRadius: 10
  };

  const renderingStyle = {
    ...baseStyle,
    boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
  };

  const style = isStudio ? studioStyle : isRendering ? renderingStyle : baseStyle;

  return (
    <div style={style}>
      {isStudio && "Studio 模式"}
      {isRendering && "渲染模式"}
      {!isStudio && !isRendering && "默认模式"}
    </div>
  );
};
```

## 实际应用场景

### 1. 数据获取防抖

```typescript
import React, { useEffect, useState } from 'react';
import { getRemotionEnvironment, delayRender, continueRender } from 'remotion';

const DataFetchingComponent = () => {
  const [data, setData] = useState<any>(null);
  const [handle] = useState(() => delayRender("获取数据中..."));
  const { isStudio, isRendering } = getRemotionEnvironment();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // 在 Studio 中使用防抖，避免频繁请求
        if (isStudio) {
          const timeoutId = setTimeout(async () => {
            const response = await fetch('/api/data');
            const result = await response.json();
            setData(result);
            continueRender(handle);
          }, 500); // 500ms 防抖

          return () => clearTimeout(timeoutId);
        } else {
          // 在渲染时立即获取数据
          const response = await fetch('/api/data');
          const result = await response.json();
          setData(result);
          continueRender(handle);
        }
      } catch (error) {
        console.error('数据获取失败:', error);
        continueRender(handle);
      }
    };

    fetchData();
  }, [isStudio, isRendering, handle]);

  if (!data) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        fontSize: 18
      }}>
        {isStudio ? "Studio 中加载数据..." : "渲染中加载数据..."}
      </div>
    );
  }

  return (
    <div style={{ padding: 20 }}>
      <h2>数据已加载</h2>
      <pre>{JSON.stringify(data, null, 2)}</pre>
      <div style={{ marginTop: 10, fontSize: 12, color: '#666' }}>
        环境: {isStudio ? 'Studio' : isRendering ? 'Rendering' : 'Other'}
      </div>
    </div>
  );
};
```

### 2. 开发工具显示

```typescript
import React from 'react';
import { getRemotionEnvironment, useCurrentFrame, useVideoConfig } from 'remotion';

const DevelopmentTools = () => {
  const { isStudio } = getRemotionEnvironment();
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // 只在 Studio 中显示开发工具
  if (!isStudio) {
    return null;
  }

  return (
    <div style={{
      position: 'absolute',
      top: 10,
      right: 10,
      backgroundColor: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: 10,
      borderRadius: 5,
      fontSize: 12,
      fontFamily: 'monospace',
      zIndex: 1000
    }}>
      <div>当前帧: {frame}</div>
      <div>总帧数: {durationInFrames}</div>
      <div>帧率: {fps} FPS</div>
      <div>时间: {(frame / fps).toFixed(2)}s</div>
      <div style={{ marginTop: 5, color: '#4caf50' }}>
        🔧 开发模式
      </div>
    </div>
  );
};

const MyComposition = () => {
  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      {/* 主要内容 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        fontSize: 24
      }}>
        我的视频内容
      </div>
      
      {/* 开发工具 - 只在 Studio 中显示 */}
      <DevelopmentTools />
    </div>
  );
};
```

### 3. 性能优化

```typescript
import React, { useMemo } from 'react';
import { getRemotionEnvironment, useCurrentFrame } from 'remotion';

const PerformanceOptimizedComponent = () => {
  const { isStudio, isRendering } = getRemotionEnvironment();
  const frame = useCurrentFrame();

  // 在 Studio 中使用较低质量以提高预览性能
  // 在渲染时使用高质量
  const quality = useMemo(() => {
    if (isStudio) {
      return 'low'; // Studio 中使用低质量预览
    } else if (isRendering) {
      return 'high'; // 渲染时使用高质量
    }
    return 'medium'; // 其他情况使用中等质量
  }, [isStudio, isRendering]);

  // 根据质量设置生成不同复杂度的内容
  const particleCount = useMemo(() => {
    switch (quality) {
      case 'low': return 50;
      case 'medium': return 200;
      case 'high': return 500;
      default: return 100;
    }
  }, [quality]);

  const particles = useMemo(() => {
    return Array.from({ length: particleCount }, (_, i) => {
      const angle = (i / particleCount) * 360 + frame * 2;
      const radius = 100 + Math.sin(frame * 0.1 + i) * 50;
      const x = Math.cos((angle * Math.PI) / 180) * radius;
      const y = Math.sin((angle * Math.PI) / 180) * radius;

      return (
        <div
          key={i}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: quality === 'high' ? 8 : 4,
            height: quality === 'high' ? 8 : 4,
            borderRadius: '50%',
            backgroundColor: `hsl(${(i * 360) / particleCount}, 70%, 60%)`,
            transform: `translate(calc(-50% + ${x}px), calc(-50% + ${y}px))`,
            opacity: quality === 'low' ? 0.6 : 0.8
          }}
        />
      );
    });
  }, [particleCount, frame, quality]);

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      {particles}
      
      {/* 质量指示器 */}
      <div style={{
        position: 'absolute',
        bottom: 10,
        left: 10,
        backgroundColor: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: 5,
        borderRadius: 3,
        fontSize: 12
      }}>
        质量: {quality} | 粒子数: {particleCount}
      </div>
    </div>
  );
};
```

### 4. 条件性功能启用

```typescript
import React, { useState } from 'react';
import { getRemotionEnvironment } from 'remotion';

const ConditionalFeatures = () => {
  const { isStudio, isPlayer, isReadOnlyStudio } = getRemotionEnvironment();
  const [debugMode, setDebugMode] = useState(false);

  const features = {
    // 只在 Studio 中启用的功能
    studioOnly: isStudio && !isReadOnlyStudio,
    // 只在 Player 中启用的功能
    playerOnly: isPlayer,
    // 在 Studio 和 Player 中都启用的功能
    interactiveMode: isStudio || isPlayer,
    // 只在渲染时启用的功能
    renderingOptimizations: !isStudio && !isPlayer
  };

  return (
    <div style={{ padding: 20 }}>
      <h2>功能状态</h2>
      
      <div style={{ marginBottom: 20 }}>
        <h3>环境检测</h3>
        <ul>
          <li>Studio: {isStudio ? '✅' : '❌'}</li>
          <li>Player: {isPlayer ? '✅' : '❌'}</li>
          <li>Rendering: {!isStudio && !isPlayer ? '✅' : '❌'}</li>
          <li>Read-only Studio: {isReadOnlyStudio ? '✅' : '❌'}</li>
        </ul>
      </div>

      <div style={{ marginBottom: 20 }}>
        <h3>可用功能</h3>
        <ul>
          <li>Studio 专用功能: {features.studioOnly ? '✅' : '❌'}</li>
          <li>Player 专用功能: {features.playerOnly ? '✅' : '❌'}</li>
          <li>交互模式: {features.interactiveMode ? '✅' : '❌'}</li>
          <li>渲染优化: {features.renderingOptimizations ? '✅' : '❌'}</li>
        </ul>
      </div>

      {/* Studio 专用控制 */}
      {features.studioOnly && (
        <div style={{ 
          padding: 10, 
          backgroundColor: '#e3f2fd', 
          borderRadius: 5,
          marginBottom: 10
        }}>
          <h4>Studio 控制面板</h4>
          <button 
            onClick={() => setDebugMode(!debugMode)}
            style={{
              padding: '5px 10px',
              backgroundColor: debugMode ? '#f44336' : '#4caf50',
              color: 'white',
              border: 'none',
              borderRadius: 3,
              cursor: 'pointer'
            }}
          >
            {debugMode ? '关闭' : '开启'} 调试模式
          </button>
        </div>
      )}

      {/* Player 专用控制 */}
      {features.playerOnly && (
        <div style={{ 
          padding: 10, 
          backgroundColor: '#f3e5f5', 
          borderRadius: 5,
          marginBottom: 10
        }}>
          <h4>Player 控制面板</h4>
          <p>Player 专用功能已启用</p>
        </div>
      )}

      {/* 渲染优化信息 */}
      {features.renderingOptimizations && (
        <div style={{ 
          padding: 10, 
          backgroundColor: '#e8f5e8', 
          borderRadius: 5
        }}>
          <h4>渲染优化</h4>
          <p>高质量渲染模式已启用</p>
        </div>
      )}

      {/* 调试信息 */}
      {debugMode && features.studioOnly && (
        <div style={{
          marginTop: 20,
          padding: 10,
          backgroundColor: '#fff3e0',
          borderRadius: 5,
          fontFamily: 'monospace',
          fontSize: 12
        }}>
          <h4>调试信息</h4>
          <pre>{JSON.stringify(getRemotionEnvironment(), null, 2)}</pre>
        </div>
      )}
    </div>
  );
};
```

### 5. 环境特定的错误处理

```typescript
import React, { useEffect, useState } from 'react';
import { getRemotionEnvironment } from 'remotion';

const EnvironmentSpecificErrorHandling = () => {
  const { isStudio, isRendering } = getRemotionEnvironment();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleError = (errorEvent: ErrorEvent) => {
      const errorMessage = errorEvent.message;
      
      if (isStudio) {
        // 在 Studio 中显示详细错误信息
        setError(`Studio 错误: ${errorMessage}\n位置: ${errorEvent.filename}:${errorEvent.lineno}`);
        console.error('Studio 错误详情:', errorEvent);
      } else if (isRendering) {
        // 在渲染时记录错误但不显示
        console.error('渲染错误:', errorMessage);
        setError('渲染过程中发生错误，请检查控制台');
      } else {
        // 其他环境的通用错误处理
        setError(`发生错误: ${errorMessage}`);
      }
    };

    window.addEventListener('error', handleError);
    
    return () => {
      window.removeEventListener('error', handleError);
    };
  }, [isStudio, isRendering]);

  if (error) {
    return (
      <div style={{
        padding: 20,
        backgroundColor: '#ffebee',
        border: '1px solid #f44336',
        borderRadius: 5,
        color: '#c62828'
      }}>
        <h3>错误信息</h3>
        <pre style={{ whiteSpace: 'pre-wrap' }}>{error}</pre>
        <button 
          onClick={() => setError(null)}
          style={{
            marginTop: 10,
            padding: '5px 10px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: 3,
            cursor: 'pointer'
          }}
        >
          清除错误
        </button>
      </div>
    );
  }

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      fontSize: 18
    }}>
      <div>
        <p>环境特定错误处理已启用</p>
        <p style={{ fontSize: 14, color: '#666' }}>
          当前环境: {isStudio ? 'Studio' : isRendering ? 'Rendering' : 'Other'}
        </p>
      </div>
    </div>
  );
};
```

## 最佳实践

1. **性能优化**: 在 Studio 中使用较低质量预览，渲染时使用高质量
2. **调试工具**: 只在 Studio 中显示开发工具和调试信息
3. **数据获取**: 在 Studio 中使用防抖避免频繁请求
4. **错误处理**: 根据环境提供不同级别的错误信息
5. **功能切换**: 根据环境启用或禁用特定功能

## 常见用例

- 开发时显示调试信息
- 渲染时启用性能优化
- Studio 中的数据获取防抖
- 环境特定的错误处理
- 条件性功能启用

## 相关 API

- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置
- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`continueRender()`](./continueRender.md) - 继续渲染

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/get-remotion-environment.ts)
