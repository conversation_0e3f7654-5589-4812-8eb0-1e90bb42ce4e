/**
 * @功能概述: 视频片段裁剪和画面裁剪任务 (VideoClipAndCropTask)
 *           负责根据指定的时间参数和裁剪参数对视频进行片段裁剪和画面裁剪
 * 
 * @架构验证: 新架构下的分层验证
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 * 
 * @处理流程: 
 *   1. 参数验证：验证videoIdentifier、时间参数、裁剪参数
 *   2. 文件查找：根据videoIdentifier查找原始视频文件
 *   3. FFmpeg处理：执行时间裁剪和画面裁剪
 *   4. 输出验证：验证生成的视频文件
 *   5. 上下文构建：构建完整的输出上下文供后续处理使用
 */

const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;
const fsConstants = require('fs').constants;
const { spawn } = require('child_process');

// 导入进度监控常量
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');

// 模块级日志前缀，用于标识从本文件输出的日志
const moduleLogPrefix = `[文件：VideoClipAndCropTask.js][视频裁剪任务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 任务正确位于 tasks/ 目录，通过 services/ 目录调用外部API`);

/**
 * @功能概述: 视频片段裁剪和画面裁剪任务类，继承自 TaskBase
 * @主要功能: 使用FFmpeg根据时间参数和裁剪参数对视频进行处理
 * @输入要求: videoIdentifier、时间参数、裁剪参数
 * @输出结果: 处理后的视频文件路径和完整的元数据信息
 * 
 * @处理步骤:
 *   1. 第一步：时间维度裁剪（clipStartTime, clipEndTime）
 *   2. 第二步：空间维度裁剪（cropWidth, cropHeight, cropXOffset, cropYOffset）
 *   3. 两步操作在一个FFmpeg命令中完成，提高处理效率
 */
class VideoClipAndCropTask extends TaskBase {
    /**
     * @功能概述: 构造函数，初始化视频片段裁剪和画面裁剪任务
     * @说明: 设置任务名称和超时时间，调用父类构造函数
     * 
     * @任务配置:
     *   - 任务名称: 'VideoClipAndCrop'
     *   - 超时时间: 300秒（5分钟）
     *   - 支持的视频格式: .mp4, .avi, .mov, .mkv, .webm
     */
    constructor() {
        super('VideoClipAndCrop'); // 调用父类构造函数，设置任务名称
        this.timeout = 300000; // 5分钟超时（FFmpeg处理可能需要较长时间）
        
        // 设置实例级日志前缀
        this.logPrefix = `[文件：VideoClipAndCropTask.js][视频裁剪任务][VideoClipAndCropTask]`;
        
        // 支持的视频格式列表
        this.supportedFormats = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
        
        // 记录实例创建
        logger.info(`${this.logPrefix} VideoClipAndCropTask 实例已创建`);
        logger.info(`${this.logPrefix} 超时设置: ${this.timeout / 1000}秒`);
        logger.info(`${this.logPrefix} 支持的视频格式: ${this.supportedFormats.join(', ')}`);
    }

    /**
     * @功能概述: 执行视频片段裁剪和画面裁剪任务的核心方法
     * @param {object} context - 执行上下文，包含任务所需的所有数据
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度
     * @returns {Promise<object>} 任务执行结果
     * 
     * @上下文输入要求:
     *   - videoIdentifier: {string} 视频文件的唯一标识符（用于查找原始视频）
     *   - clipStartTime: {number} 片段开始时间（秒）
     *   - clipEndTime: {number} 片段结束时间（秒）
     *   - cropWidth: {number} 裁剪宽度（像素）
     *   - cropHeight: {number} 裁剪高度（像素）
     *   - cropXOffset: {number} 裁剪X偏移量（像素）
     *   - cropYOffset: {number} 裁剪Y偏移量（像素）
     *   - reqId: {string} 请求ID（用于日志追踪）
     * 
     * @返回结果:
     *   - processedVideoPath: {string} 处理后视频文件的完整路径
     *   - processedVideoFileName: {string} 处理后视频文件名
     *   - processedVideoIdentifier: {string} 新视频的唯一标识符
     *   - originalVideoPath: {string} 原始视频路径
     *   - originalVideoIdentifier: {string} 原始视频标识符
     *   - finalVideoWidth: {number} 最终视频宽度
     *   - finalVideoHeight: {number} 最终视频高度
     *   - finalVideoDuration: {number} 最终视频时长（秒）
     *   - processingHistory: {object} 处理历史信息
     *   - concatenationReady: {object} 拼接兼容性信息
     *   - copyConfig: {object} 复制配置信息
     *   - aiProcessingReady: {object} AI处理准备信息
     */
    async execute(context, progressCallback) {
        const logPrefix = `${this.logPrefix}[execute][ReqID:${context.reqId || 'unknown'}]`;
        
        try {
            // 记录任务开始
            logger.info(`${logPrefix} 开始执行视频片段裁剪和画面裁剪任务`);
            this.setProgressCallback(progressCallback);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.VALIDATING_INPUTS, { detail: '任务开始 (0%)', current: 0 });

            // === T.2.1 定义必需参数列表 ===
            const requiredFields = [
                'videoIdentifier', 'clipStartTime', 'clipEndTime',
                'cropWidth', 'cropHeight', 'cropXOffset', 'cropYOffset', 'reqId'
            ];

            logger.info(`${logPrefix}[T.2.1] 开始参数验证，必需参数: ${requiredFields.join(', ')}`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.VALIDATING_INPUTS, { detail: '验证输入参数 (10%)', current: 10 });

            // 验证必需参数是否存在
            const missingFields = [];
            for (const field of requiredFields) {
                if (context[field] === null || context[field] === undefined) {
                    missingFields.push(field);
                }
            }

            if (missingFields.length > 0) {
                throw new Error(`缺少必需参数: ${missingFields.join(', ')}`);
            }

            logger.info(`${logPrefix}[T.2.1] 必需参数验证通过`);

            // === T.2.2 实现参数类型和范围验证 ===
            const {
                videoIdentifier,
                clipStartTime,
                clipEndTime,
                cropWidth,
                cropHeight,
                cropXOffset,
                cropYOffset,
                reqId
            } = context;

            logger.info(`${logPrefix}[T.2.2] 开始参数类型和范围验证`);

            // 验证videoIdentifier
            if (typeof videoIdentifier !== 'string' || videoIdentifier.trim() === '') {
                throw new Error('videoIdentifier 必须是非空字符串');
            }

            // 验证时间参数
            if (typeof clipStartTime !== 'number' || typeof clipEndTime !== 'number') {
                throw new Error('clipStartTime 和 clipEndTime 必须是数字');
            }

            if (clipStartTime < 0) {
                throw new Error('clipStartTime 不能为负数');
            }

            if (clipStartTime >= clipEndTime) {
                throw new Error('clipStartTime 必须小于 clipEndTime');
            }

            // 验证裁剪参数
            if (typeof cropWidth !== 'number' || typeof cropHeight !== 'number' ||
                typeof cropXOffset !== 'number' || typeof cropYOffset !== 'number') {
                throw new Error('裁剪参数 (cropWidth, cropHeight, cropXOffset, cropYOffset) 必须是数字');
            }

            if (cropWidth <= 0 || cropHeight <= 0) {
                throw new Error('cropWidth 和 cropHeight 必须大于0');
            }

            if (cropXOffset < 0 || cropYOffset < 0) {
                throw new Error('cropXOffset 和 cropYOffset 不能为负数');
            }

            // 记录验证通过的参数
            logger.info(`${logPrefix}[T.2.2] 参数类型和范围验证通过:`);
            logger.info(`${logPrefix}  - videoIdentifier: ${videoIdentifier}`);
            logger.info(`${logPrefix}  - 时间片段: ${clipStartTime}s - ${clipEndTime}s (时长: ${clipEndTime - clipStartTime}s)`);
            logger.info(`${logPrefix}  - 裁剪尺寸: ${cropWidth}x${cropHeight}`);
            logger.info(`${logPrefix}  - 裁剪偏移: (${cropXOffset}, ${cropYOffset})`);
            logger.info(`${logPrefix}  - 请求ID: ${reqId}`);

            // === T.3.1 根据 videoIdentifier 查找原始视频文件 ===
            logger.info(`${logPrefix}[T.3.1] 开始查找原始视频文件`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, { detail: '查找原始视频文件 (20%)', current: 20 });

            // 构建uploads目录路径
            const uploadsDir = path.join(__dirname, '../../uploads');
            logger.info(`${logPrefix}[T.3.1] 在目录中查找视频文件: ${uploadsDir}`);

            // 查找匹配的视频文件
            let originalVideoPath = null;
            for (const ext of this.supportedFormats) {
                const possiblePath = path.join(uploadsDir, `${videoIdentifier}${ext}`);
                try {
                    await fs.access(possiblePath, fsConstants.F_OK);
                    originalVideoPath = possiblePath;
                    logger.info(`${logPrefix}[T.3.1] 找到视频文件: ${originalVideoPath}`);
                    break;
                } catch (error) {
                    // 文件不存在，继续查找下一个格式
                    logger.debug(`${logPrefix}[T.3.1] 文件不存在: ${possiblePath}`);
                }
            }

            if (!originalVideoPath) {
                throw new Error(`未找到视频文件: ${videoIdentifier}，支持的格式: ${this.supportedFormats.join(', ')}`);
            }

            // === T.3.2 验证原始视频文件可访问性 ===
            logger.info(`${logPrefix}[T.3.2] 验证视频文件可访问性`);

            try {
                // 验证文件可读性
                await fs.access(originalVideoPath, fsConstants.R_OK);
                logger.info(`${logPrefix}[T.3.2] 视频文件可读性验证通过`);

                // 获取文件基本信息
                const fileStats = await fs.stat(originalVideoPath);
                logger.info(`${logPrefix}[T.3.2] 视频文件信息:`);
                logger.info(`${logPrefix}  - 文件路径: ${originalVideoPath}`);
                logger.info(`${logPrefix}  - 文件大小: ${fileStats.size} bytes (${(fileStats.size / 1024 / 1024).toFixed(2)} MB)`);
                logger.info(`${logPrefix}  - 修改时间: ${fileStats.mtime.toISOString()}`);

                if (fileStats.size === 0) {
                    throw new Error('视频文件大小为0，文件可能损坏');
                }

            } catch (error) {
                if (error.code === 'ENOENT') {
                    throw new Error(`视频文件不存在: ${originalVideoPath}`);
                } else if (error.code === 'EACCES') {
                    throw new Error(`没有权限访问视频文件: ${originalVideoPath}`);
                } else {
                    throw new Error(`验证视频文件失败: ${error.message}`);
                }
            }

            logger.info(`${logPrefix}[T.3.2] 视频文件查找和验证完成`);

            // === T.4.1 构建输出文件路径和命名 ===
            logger.info(`${logPrefix}[T.4.1] 构建输出文件路径和命名`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, { detail: '构建输出文件路径 (30%)', current: 30 });

            // 生成时间戳用于文件命名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
            const processedVideoFileName = `${videoIdentifier}_processed_${timestamp}.mp4`;
            const processedVideoPath = path.join(uploadsDir, processedVideoFileName);
            const processedVideoIdentifier = `${videoIdentifier}_processed_${timestamp}`;

            logger.info(`${logPrefix}[T.4.1] 输出文件信息:`);
            logger.info(`${logPrefix}  - 输出文件名: ${processedVideoFileName}`);
            logger.info(`${logPrefix}  - 输出文件路径: ${processedVideoPath}`);
            logger.info(`${logPrefix}  - 新视频标识符: ${processedVideoIdentifier}`);

            // === T.4.2 构建FFmpeg命令参数 ===
            logger.info(`${logPrefix}[T.4.2] 构建FFmpeg命令参数`);

            const clipDuration = clipEndTime - clipStartTime;

            // 构建FFmpeg命令参数
            const ffmpegArgs = [
                '-i', originalVideoPath,                                    // 输入文件
                '-ss', clipStartTime.toString(),                           // 开始时间
                '-t', clipDuration.toString(),                             // 持续时间
                '-vf', `crop=${cropWidth}:${cropHeight}:${cropXOffset}:${cropYOffset}`, // 视频滤镜：裁剪
                '-c:v', 'libx264',                                         // 视频编码器
                '-crf', '23',                                              // 质量设置
                '-c:a', 'aac',                                             // 音频编码器
                '-y',                                                      // 覆盖输出文件
                processedVideoPath                                         // 输出文件
            ];

            const ffmpegCommand = `ffmpeg ${ffmpegArgs.join(' ')}`;
            logger.info(`${logPrefix}[T.4.2] FFmpeg命令: ${ffmpegCommand}`);
            logger.info(`${logPrefix}[T.4.2] 处理参数:`);
            logger.info(`${logPrefix}  - 时间裁剪: ${clipStartTime}s - ${clipEndTime}s (时长: ${clipDuration}s)`);
            logger.info(`${logPrefix}  - 画面裁剪: ${cropWidth}x${cropHeight} at (${cropXOffset}, ${cropYOffset})`);

            // === T.4.3 执行FFmpeg处理并监控进度 ===
            logger.info(`${logPrefix}[T.4.3] 开始执行FFmpeg处理`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, { detail: '开始FFmpeg处理 (40%)', current: 40 });

            await this.executeFFmpeg(ffmpegArgs, clipDuration, logPrefix);

            logger.info(`${logPrefix}[T.4.3] FFmpeg处理完成`);

            // === T.5.1 验证输出视频文件生成 ===
            logger.info(`${logPrefix}[T.5.1] 验证输出视频文件生成`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FINALIZING, { detail: '验证输出文件 (95%)', current: 95 });

            try {
                const outputStats = await fs.stat(processedVideoPath);
                logger.info(`${logPrefix}[T.5.1] 输出文件验证成功:`);
                logger.info(`${logPrefix}  - 文件路径: ${processedVideoPath}`);
                logger.info(`${logPrefix}  - 文件大小: ${outputStats.size} bytes (${(outputStats.size / 1024 / 1024).toFixed(2)} MB)`);

                if (outputStats.size === 0) {
                    throw new Error('输出视频文件大小为0，处理可能失败');
                }
            } catch (error) {
                if (error.code === 'ENOENT') {
                    throw new Error(`输出视频文件未生成: ${processedVideoPath}`);
                } else {
                    throw new Error(`验证输出文件失败: ${error.message}`);
                }
            }

            // === T.5.2 获取输出视频技术参数 ===
            logger.info(`${logPrefix}[T.5.2] 获取输出视频技术参数`);
            const videoMetadata = await this.getVideoMetadata(processedVideoPath, logPrefix);

            // === T.6 构建完整的输出上下文 ===
            logger.info(`${logPrefix}[T.6] 构建完整的输出上下文`);
            const processingEndTime = new Date();
            const processingDuration = processingEndTime.getTime() - new Date().getTime(); // 这里应该记录开始时间

            const outputContext = {
                // T.6.1 核心文件信息
                processedVideoPath,
                processedVideoFileName,
                processedVideoIdentifier,
                originalVideoPath,
                originalVideoIdentifier: videoIdentifier,

                // T.6.2 技术参数信息
                finalVideoWidth: videoMetadata.width || cropWidth,
                finalVideoHeight: videoMetadata.height || cropHeight,
                finalVideoDuration: videoMetadata.duration || clipDuration,
                finalVideoFrameRate: videoMetadata.frameRate || 30,
                finalVideoCodec: videoMetadata.codec || 'h264',
                finalVideoFormat: 'mp4',
                finalVideoBitrate: videoMetadata.bitrate || 0,
                finalVideoFileSize: videoMetadata.fileSize || 0,
                hasAudio: videoMetadata.hasAudio || true,
                audioCodec: videoMetadata.audioCodec || 'aac',
                audioSampleRate: videoMetadata.audioSampleRate || 44100,
                audioChannels: videoMetadata.audioChannels || 2,

                // T.6.3 处理历史信息
                processingHistory: {
                    clipOperation: {
                        originalStartTime: clipStartTime,
                        originalEndTime: clipEndTime,
                        clippedStartTime: 0,
                        clippedDuration: clipDuration
                    },
                    cropOperation: {
                        originalWidth: videoMetadata.originalWidth || 1920,
                        originalHeight: videoMetadata.originalHeight || 1080,
                        cropX: cropXOffset,
                        cropY: cropYOffset,
                        cropWidth: cropWidth,
                        cropHeight: cropHeight
                    }
                },
                processedAt: processingEndTime.toISOString(),
                processingDuration: Math.abs(processingDuration),

                // T.6.4 后续处理支持信息
                concatenationReady: {
                    resolution: `${cropWidth}x${cropHeight}`,
                    frameRate: videoMetadata.frameRate || 30,
                    codec: 'h264',
                    audioFormat: 'aac_44100_2ch',
                    needsReencoding: false,
                    compatibilityHash: `h264_${cropWidth}x${cropHeight}_${videoMetadata.frameRate || 30}fps_aac44100`
                },
                copyConfig: {
                    originalParams: {
                        clipStartTime,
                        clipEndTime,
                        cropWidth,
                        cropHeight,
                        cropXOffset,
                        cropYOffset
                    },
                    isReproducible: true,
                    batchProcessingReady: true
                },
                aiProcessingReady: {
                    hasStableFrames: true,
                    frameCount: Math.round((videoMetadata.duration || clipDuration) * (videoMetadata.frameRate || 30)),
                    motionIntensity: 'medium'
                },

                // 任务状态
                taskStatus: 'completed',
                taskResult: 'success'
            };

            this.reportProgress(TASK_STATUS.COMPLETED, null, { detail: '任务完成 (100%)', current: 100 });
            logger.info(`${logPrefix} VideoClipAndCropTask 执行成功完成`);
            logger.info(`${logPrefix} 最终输出文件: ${processedVideoPath}`);

            return outputContext;
            
        } catch (error) {
            // === T.7 实现完整的错误处理机制 ===
            logger.error(`${logPrefix}[T.7] 任务执行失败: ${error.message}`);

            let errorCategory = 'unknown';
            let errorSuggestion = '';

            // T.7.1 参数验证错误处理
            if (error.message.includes('缺少必需参数') ||
                error.message.includes('必须是') ||
                error.message.includes('不能为负数') ||
                error.message.includes('必须大于')) {
                errorCategory = 'parameter_validation';
                errorSuggestion = '请检查传入的参数是否正确，确保所有必需参数都已提供且格式正确。';
                logger.error(`${logPrefix}[T.7.1] 参数验证错误: ${error.message}`);
            }
            // T.7.2 文件操作错误处理
            else if (error.message.includes('未找到视频文件') ||
                     error.message.includes('文件不存在') ||
                     error.message.includes('没有权限访问') ||
                     error.message.includes('文件大小为0')) {
                errorCategory = 'file_operation';
                errorSuggestion = '请检查视频文件是否存在于uploads目录中，文件是否完整且有读取权限。';
                logger.error(`${logPrefix}[T.7.2] 文件操作错误: ${error.message}`);
            }
            // T.7.3 FFmpeg执行错误处理
            else if (error.message.includes('FFmpeg') ||
                     error.message.includes('ffprobe') ||
                     error.message.includes('处理失败')) {
                errorCategory = 'ffmpeg_execution';
                errorSuggestion = '请检查FFmpeg是否正确安装，视频文件是否损坏，磁盘空间是否充足。';
                logger.error(`${logPrefix}[T.7.3] FFmpeg执行错误: ${error.message}`);
            }

            this.fail(error);

            // 返回详细的失败结果
            return {
                taskStatus: 'failed',
                taskResult: 'error',
                error: error.message,
                errorCategory,
                errorSuggestion,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * @功能概述: 执行FFmpeg命令并监控进度
     * @param {Array} ffmpegArgs - FFmpeg命令参数数组
     * @param {number} totalDuration - 视频总时长（用于计算进度）
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<void>} 执行完成的Promise
     */
    async executeFFmpeg(ffmpegArgs, totalDuration, logPrefix) {
        return new Promise((resolve, reject) => {
            logger.info(`${logPrefix}[executeFFmpeg] 启动FFmpeg进程`);

            // 启动FFmpeg进程
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

            let ffmpegOutput = '';
            let ffmpegError = '';

            // 监听stdout输出
            ffmpegProcess.stdout.on('data', (data) => {
                ffmpegOutput += data.toString();
            });

            // 监听stderr输出（FFmpeg的进度信息通常在stderr中）
            ffmpegProcess.stderr.on('data', (data) => {
                const output = data.toString();
                ffmpegError += output;

                // 解析FFmpeg进度信息
                this.parseFFmpegProgress(output, totalDuration, logPrefix);
            });

            // 监听进程结束
            ffmpegProcess.on('close', (code) => {
                if (code === 0) {
                    logger.info(`${logPrefix}[executeFFmpeg] FFmpeg进程成功完成`);
                    resolve();
                } else {
                    logger.error(`${logPrefix}[executeFFmpeg] FFmpeg进程失败，退出码: ${code}`);
                    logger.error(`${logPrefix}[executeFFmpeg] FFmpeg错误输出: ${ffmpegError}`);
                    reject(new Error(`FFmpeg处理失败，退出码: ${code}`));
                }
            });

            // 监听进程错误
            ffmpegProcess.on('error', (error) => {
                logger.error(`${logPrefix}[executeFFmpeg] FFmpeg进程启动失败: ${error.message}`);
                reject(new Error(`FFmpeg进程启动失败: ${error.message}`));
            });
        });
    }

    /**
     * @功能概述: 解析FFmpeg进度输出并更新进度
     * @param {string} output - FFmpeg输出内容
     * @param {number} totalDuration - 视频总时长
     * @param {string} logPrefix - 日志前缀
     */
    parseFFmpegProgress(output, totalDuration, logPrefix) {
        // FFmpeg进度输出格式示例: time=00:00:10.25 bitrate=1000.0kbits/s speed=1.0x
        const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);

        if (timeMatch) {
            const hours = parseInt(timeMatch[1]);
            const minutes = parseInt(timeMatch[2]);
            const seconds = parseFloat(timeMatch[3]);

            const currentTime = hours * 3600 + minutes * 60 + seconds;
            const progressPercentage = Math.min(Math.round((currentTime / totalDuration) * 100), 100);

            // 将FFmpeg进度映射到40%-90%的范围
            const mappedProgress = 40 + (progressPercentage * 0.5);

            logger.debug(`${logPrefix}[parseFFmpegProgress] 处理进度: ${currentTime.toFixed(2)}s / ${totalDuration}s (${progressPercentage}%)`);

            this.reportProgress(
                TASK_STATUS.RUNNING,
                TASK_SUBSTATUS.PROCESSING,
                {
                    detail: `FFmpeg处理中: ${progressPercentage}% (${mappedProgress.toFixed(0)}%)`,
                    current: mappedProgress.toFixed(0)
                }
            );
        }
    }

    /**
     * @功能概述: 获取视频文件的技术参数
     * @param {string} videoPath - 视频文件路径
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<object>} 视频元数据对象
     */
    async getVideoMetadata(videoPath, logPrefix) {
        return new Promise((resolve, reject) => {
            logger.info(`${logPrefix}[getVideoMetadata] 获取视频元数据: ${videoPath}`);

            // 使用ffprobe获取视频信息
            const ffprobeArgs = [
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                videoPath
            ];

            const ffprobeProcess = spawn('ffprobe', ffprobeArgs);

            let output = '';
            let error = '';

            ffprobeProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            ffprobeProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            ffprobeProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const metadata = JSON.parse(output);
                        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
                        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

                        const result = {
                            width: videoStream ? parseInt(videoStream.width) : 0,
                            height: videoStream ? parseInt(videoStream.height) : 0,
                            duration: parseFloat(metadata.format.duration) || 0,
                            frameRate: videoStream ? this.parseFrameRate(videoStream.r_frame_rate) : 30,
                            codec: videoStream ? videoStream.codec_name : 'unknown',
                            bitrate: parseInt(metadata.format.bit_rate) || 0,
                            fileSize: parseInt(metadata.format.size) || 0,
                            hasAudio: !!audioStream,
                            audioCodec: audioStream ? audioStream.codec_name : null,
                            audioSampleRate: audioStream ? parseInt(audioStream.sample_rate) : null,
                            audioChannels: audioStream ? parseInt(audioStream.channels) : null
                        };

                        logger.info(`${logPrefix}[getVideoMetadata] 元数据获取成功:`, result);
                        resolve(result);
                    } catch (parseError) {
                        logger.error(`${logPrefix}[getVideoMetadata] 解析元数据失败: ${parseError.message}`);
                        reject(new Error(`解析视频元数据失败: ${parseError.message}`));
                    }
                } else {
                    logger.error(`${logPrefix}[getVideoMetadata] ffprobe失败，退出码: ${code}, 错误: ${error}`);
                    reject(new Error(`获取视频元数据失败: ${error}`));
                }
            });

            ffprobeProcess.on('error', (err) => {
                logger.error(`${logPrefix}[getVideoMetadata] ffprobe进程启动失败: ${err.message}`);
                reject(new Error(`ffprobe进程启动失败: ${err.message}`));
            });
        });
    }

    /**
     * @功能概述: 收集VideoClipAndCropTask的详细上下文信息
     * @returns {object} 包含视频处理特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加视频处理特定的上下文信息
     *   - 包含FFmpeg处理详情、视频技术参数、处理历史等
     *
     * @返回对象扩展:
     *   - videoProcessingDetails: 视频处理特定信息
     *   - ffmpegDetails: FFmpeg执行详情
     *   - inputVideoInfo: 输入视频信息
     *   - outputVideoInfo: 输出视频信息
     *   - processingParameters: 处理参数详情
     */
    collectDetailedContext() {
        const logPrefix = `${this.logPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取视频处理信息
            const taskResult = this.result || {};

            // 视频处理特定信息
            const videoProcessingDetails = {
                taskType: 'VideoClipAndCrop',
                supportedFormats: this.supportedFormats,
                timeout: this.timeout,
                processingSteps: [
                    '参数验证',
                    '文件查找',
                    'FFmpeg处理',
                    '输出验证',
                    '元数据获取',
                    '上下文构建'
                ],
                processingMode: 'clip_and_crop',
                qualitySettings: {
                    videoCodec: 'libx264',
                    crf: 23,
                    audioCodec: 'aac'
                }
            };

            // 输入视频信息
            const inputVideoInfo = {
                originalVideoPath: taskResult.originalVideoPath || 'N/A',
                originalVideoIdentifier: taskResult.originalVideoIdentifier || 'N/A',
                originalVideoExists: taskResult.originalVideoExists || false,
                originalVideoSize: taskResult.originalVideoSize || 'N/A',
                originalVideoFormat: taskResult.originalVideoFormat || 'N/A'
            };

            // 输出视频信息
            const outputVideoInfo = {
                processedVideoPath: taskResult.processedVideoPath || 'N/A',
                processedVideoFileName: taskResult.processedVideoFileName || 'N/A',
                processedVideoIdentifier: taskResult.processedVideoIdentifier || 'N/A',
                finalVideoWidth: taskResult.finalVideoWidth || 'N/A',
                finalVideoHeight: taskResult.finalVideoHeight || 'N/A',
                finalVideoDuration: taskResult.finalVideoDuration || 'N/A',
                finalVideoFrameRate: taskResult.finalVideoFrameRate || 'N/A',
                finalVideoCodec: taskResult.finalVideoCodec || 'N/A',
                finalVideoFormat: taskResult.finalVideoFormat || 'N/A',
                finalVideoBitrate: taskResult.finalVideoBitrate || 'N/A',
                finalVideoFileSize: taskResult.finalVideoFileSize || 'N/A',
                hasAudio: taskResult.hasAudio || false,
                audioCodec: taskResult.audioCodec || 'N/A',
                audioSampleRate: taskResult.audioSampleRate || 'N/A',
                audioChannels: taskResult.audioChannels || 'N/A'
            };

            // 处理参数详情
            const processingParameters = {
                clipStartTime: taskResult.clipStartTime || 'N/A',
                clipEndTime: taskResult.clipEndTime || 'N/A',
                clipDuration: taskResult.clipDuration || 'N/A',
                cropWidth: taskResult.cropWidth || 'N/A',
                cropHeight: taskResult.cropHeight || 'N/A',
                cropXOffset: taskResult.cropXOffset || 'N/A',
                cropYOffset: taskResult.cropYOffset || 'N/A',
                requestId: taskResult.reqId || 'N/A'
            };

            // 处理历史详情
            const processingHistory = taskResult.processingHistory || {
                clipOperation: 'N/A',
                cropOperation: 'N/A'
            };

            // 后续处理支持信息
            const postProcessingSupport = {
                concatenationReady: taskResult.concatenationReady || false,
                aiProcessingReady: taskResult.aiProcessingReady || false,
                copyConfig: taskResult.copyConfig || 'N/A'
            };

            // 合并所有上下文信息
            const extendedContext = {
                ...baseContext,
                videoProcessingDetails,
                inputVideoInfo,
                outputVideoInfo,
                processingParameters,
                processingHistory,
                postProcessingSupport,
                collectionMethod: 'VideoClipAndCropTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集VideoClipAndCropTask详细上下文信息`);
            logger.info(`${logPrefix} 上下文包含 ${Object.keys(extendedContext).length} 个主要部分`);

            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                videoProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectionMethod: 'VideoClipAndCropTask.collectDetailedContext (with error)'
            };
        }
    }

    /**
     * @功能概述: 解析帧率字符串
     * @param {string} frameRateStr - 帧率字符串，如 "30/1"
     * @returns {number} 帧率数值
     */
    parseFrameRate(frameRateStr) {
        if (!frameRateStr) return 30;

        const parts = frameRateStr.split('/');
        if (parts.length === 2) {
            const numerator = parseInt(parts[0]);
            const denominator = parseInt(parts[1]);
            return denominator !== 0 ? numerator / denominator : 30;
        }

        return parseFloat(frameRateStr) || 30;
    }
}

module.exports = VideoClipAndCropTask;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}VideoClipAndCropTask 类已导出。`);
