/**
 * @fileoverview 合成配置转换工具
 * @description 将前端的composition配置转换为VideoCompositionTask期望的格式
 * @version 1.0.0
 * <AUTHOR>
 * @created 2025-01-07
 */

const logger = require('./logger');

// 模块级日志前缀
const moduleLogPrefix = '[文件：compositionConfigConverter.js][配置转换工具]';
logger.info(`${moduleLogPrefix}模块已加载。`);

/**
 * 质量等级映射
 */
const QUALITY_MAPPING = {
    'low': { crf: 28, preset: 'ultrafast', audioBitrate: '96k' },
    'medium': { crf: 23, preset: 'medium', audioBitrate: '128k' },
    'high': { crf: 18, preset: 'slow', audioBitrate: '192k' },
    'ultra': { crf: 15, preset: 'veryslow', audioBitrate: '256k' }
};

/**
 * 宽高比映射
 */
const ASPECT_RATIO_MAPPING = {
    '9:16': { width: 1080, height: 1920 },
    '16:9': { width: 1920, height: 1080 },
    '1:1': { width: 1080, height: 1080 },
    '4:3': { width: 1440, height: 1080 },
    '3:4': { width: 1080, height: 1440 }
};

/**
 * 背景主题颜色映射
 */
const BACKGROUND_THEMES = {
    dark: '#1a1a2e',
    blue: '#16213e',
    purple: '#2d1b69',
    black: '#000000',
    white: '#ffffff'
};

/**
 * 将前端composition配置转换为VideoCompositionTask配置
 * @param {Object} frontendConfig - 前端配置对象
 * @param {Object} options - 额外选项
 * @returns {Object} VideoCompositionTask期望的配置
 */
function convertToVideoCompositionConfig(frontendConfig, options = {}) {
    const logPrefix = `${moduleLogPrefix}[convertToVideoCompositionConfig]`;

    try {
        // 默认配置 - 针对16:9转9:16短视频
        const defaultConfig = {
            // 基础参数
            audioRepeatCount: 3,
            outputWidth: 1080,
            outputHeight: 1920,
            videoCodec: 'libx264',
            audioCodec: 'aac',
            crf: 23,
            preset: 'medium',
            audioBitrate: '128k',
            frameRate: 30,

            // 视频布局配置
            videoLayout: {
                firstSegment: {
                    showVideo: true,           // 第一遍显示视频
                    showSubtitles: false,      // 第一遍不显示字幕
                    backgroundColor: '#000000' // 黑色背景
                },
                laterSegments: {
                    showVideo: false,          // 后续不显示视频
                    showSubtitles: true,       // 后续显示字幕
                    backgroundType: 'gradient', // 渐变背景
                    backgroundStartColor: '#1a1a2e',
                    backgroundEndColor: '#16213e'
                }
            },

            // 字幕样式
            subtitleStyle: {
                fontSize: 36,
                fontName: 'Arial',
                primaryColor: '#FFFFFF',
                secondaryColor: '#FFD700',
                outlineColor: '#000000',
                outlineWidth: 2,
                bold: true,
                alignment: 'center',
                marginBottom: 100
            }
        };

        // 开始转换
        const convertedConfig = { ...defaultConfig };

        // 1. 转换音频重复次数
        if (frontendConfig.audioRepeatCount) {
            convertedConfig.audioRepeatCount = Math.max(2, Math.min(5, frontendConfig.audioRepeatCount));
            logger.debug(`${logPrefix} 音频重复次数: ${convertedConfig.audioRepeatCount}`);
        }

        // 2. 转换输出参数
        if (frontendConfig.outputParams) {
            const outputParams = frontendConfig.outputParams;

            // 尺寸设置（固定9:16）
            if (outputParams.width && outputParams.height) {
                convertedConfig.outputWidth = outputParams.width;
                convertedConfig.outputHeight = outputParams.height;
            }

            // 质量转换
            if (outputParams.quality && QUALITY_MAPPING[outputParams.quality]) {
                const qualitySettings = QUALITY_MAPPING[outputParams.quality];
                convertedConfig.crf = qualitySettings.crf;
                convertedConfig.preset = qualitySettings.preset;
                convertedConfig.audioBitrate = qualitySettings.audioBitrate;
                logger.debug(`${logPrefix} 质量等级 ${outputParams.quality}: CRF=${qualitySettings.crf}`);
            }

            // 帧率设置
            if (outputParams.frameRate) {
                convertedConfig.frameRate = outputParams.frameRate;
            }
        }

        // 3. 转换背景配置
        if (frontendConfig.backgroundConfig) {
            const bgConfig = frontendConfig.backgroundConfig;

            // 视频段背景
            if (bgConfig.videoSegment) {
                convertedConfig.videoLayout.firstSegment.backgroundColor =
                    bgConfig.videoSegment.color || '#000000';
            }

            // 字幕段背景
            if (bgConfig.subtitleSegment) {
                const subtitleBg = bgConfig.subtitleSegment;
                convertedConfig.videoLayout.laterSegments.backgroundType = subtitleBg.type || 'gradient';
                convertedConfig.videoLayout.laterSegments.backgroundStartColor =
                    subtitleBg.startColor || '#1a1a2e';
                convertedConfig.videoLayout.laterSegments.backgroundEndColor =
                    subtitleBg.endColor || '#16213e';
            }
        }

        // 4. 转换字幕样式
        if (frontendConfig.subtitleStyle) {
            const subtitleStyle = frontendConfig.subtitleStyle;
            Object.assign(convertedConfig.subtitleStyle, {
                fontSize: subtitleStyle.fontSize || 36,
                fontName: subtitleStyle.fontName || 'Arial',
                primaryColor: subtitleStyle.primaryColor || '#FFFFFF',
                secondaryColor: subtitleStyle.secondaryColor || '#FFD700',
                outlineColor: subtitleStyle.outlineColor || '#000000',
                outlineWidth: subtitleStyle.outlineWidth || 2,
                bold: subtitleStyle.bold !== undefined ? subtitleStyle.bold : true,
                alignment: subtitleStyle.alignment || 'center',
                marginBottom: subtitleStyle.marginBottom || 100
            });
        }

        // 5. 处理额外选项
        if (options.fastMode) {
            convertedConfig.preset = 'ultrafast';
            convertedConfig.crf = 28;
            logger.debug(`${logPrefix} 启用快速模式`);
        }

        logger.info(`${logPrefix} 9:16短视频配置转换完成`);
        return convertedConfig;

    } catch (error) {
        logger.error(`${logPrefix} 配置转换失败: ${error.message}`);
        throw new Error(`配置转换失败: ${error.message}`);
    }
}

/**
 * 创建硬编码的测试配置 - 针对16:9转9:16短视频
 * @param {string} scenario - 测试场景 ('fast', 'balanced', 'high_quality', 'custom')
 * @returns {Object} VideoCompositionTask配置
 */
function createHardcodedTestConfig(scenario = 'balanced') {
    const logPrefix = `${moduleLogPrefix}[createHardcodedTestConfig]`;

    // 模拟前端配置 - 9:16短视频专用
    const frontendConfigs = {
        // 平衡模式（默认）
        balanced: {
            audioRepeatCount: 3,
            backgroundConfig: {
                videoSegment: {
                    type: 'solid',
                    color: '#000000',
                    opacity: 1.0
                },
                subtitleSegment: {
                    type: 'gradient',
                    startColor: '#1a1a2e',
                    endColor: '#16213e',
                    direction: 'vertical',
                    opacity: 0.9
                }
            },
            subtitleStyle: {
                fontSize: 36,
                fontName: 'Arial',
                primaryColor: '#FFFFFF',
                secondaryColor: '#FFD700',
                outlineColor: '#000000',
                outlineWidth: 2,
                bold: true,
                alignment: 'center',
                marginBottom: 100
            },
            outputParams: {
                width: 1080,
                height: 1920,
                quality: 'balanced',
                frameRate: 30,
                format: 'mp4'
            }
        },

        // 快速模式
        fast: {
            audioRepeatCount: 2,
            backgroundConfig: {
                videoSegment: {
                    type: 'solid',
                    color: '#000000',
                    opacity: 1.0
                },
                subtitleSegment: {
                    type: 'solid',
                    color: '#1a1a2e',
                    opacity: 1.0
                }
            },
            subtitleStyle: {
                fontSize: 32,
                fontName: 'Arial',
                primaryColor: '#FFFFFF',
                secondaryColor: '#00FF7F',
                outlineColor: '#000000',
                outlineWidth: 1,
                bold: false,
                alignment: 'center',
                marginBottom: 80
            },
            outputParams: {
                width: 720,
                height: 1280,
                quality: 'fast',
                frameRate: 24,
                format: 'mp4'
            }
        },

        // 高质量模式
        high_quality: {
            audioRepeatCount: 4,
            backgroundConfig: {
                videoSegment: {
                    type: 'solid',
                    color: '#000000',
                    opacity: 1.0
                },
                subtitleSegment: {
                    type: 'gradient',
                    startColor: '#2d1b69',
                    endColor: '#1a1a2e',
                    direction: 'vertical',
                    opacity: 0.95
                }
            },
            subtitleStyle: {
                fontSize: 40,
                fontName: 'Helvetica',
                primaryColor: '#FFFFFF',
                secondaryColor: '#87CEEB',
                outlineColor: '#191970',
                outlineWidth: 3,
                bold: true,
                alignment: 'center',
                marginBottom: 120
            },
            outputParams: {
                width: 1080,
                height: 1920,
                quality: 'high',
                frameRate: 30,
                format: 'mp4'
            }
        },

        // 自定义模式
        custom: {
            audioRepeatCount: 5,
            backgroundConfig: {
                videoSegment: {
                    type: 'solid',
                    color: '#1a1a1a',
                    opacity: 1.0
                },
                subtitleSegment: {
                    type: 'gradient',
                    startColor: '#ff6b35',
                    endColor: '#f7931e',
                    direction: 'vertical',
                    opacity: 0.8
                }
            },
            subtitleStyle: {
                fontSize: 38,
                fontName: 'Times New Roman',
                primaryColor: '#F8F8FF',
                secondaryColor: '#FFD700',
                outlineColor: '#000000',
                outlineWidth: 2,
                bold: true,
                alignment: 'center',
                marginBottom: 100
            },
            outputParams: {
                width: 1080,
                height: 1920,
                quality: 'ultra',
                frameRate: 30,
                format: 'mp4'
            }
        }
    };

    const frontendConfig = frontendConfigs[scenario] || frontendConfigs.balanced;
    const options = scenario === 'fast' ? { fastMode: true } : {};

    logger.info(`${logPrefix} 创建${scenario}场景的9:16短视频配置`);
    return convertToVideoCompositionConfig(frontendConfig, options);
}

module.exports = {
    convertToVideoCompositionConfig,
    createHardcodedTestConfig,
    QUALITY_MAPPING,
    ASPECT_RATIO_MAPPING,
    BACKGROUND_THEMES
};

logger.info(`${moduleLogPrefix}配置转换工具模块已导出。`);
