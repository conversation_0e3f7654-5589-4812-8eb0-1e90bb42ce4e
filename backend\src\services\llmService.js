/**
 * @功能概述: LLM 服务模块，负责与大语言模型进行交互，处理文本分析、翻译、优化等任务。
 *           目前为初始结构，包含核心方法框架。
 */

// 导入日志工具 logger
const logger = require('../utils/logger'); // logger 工具模块已存在
const axios = require('axios'); // 导入 axios 用于 HTTP 请求
const config = require('../config'); // 修正：移除括号 ()，直接导入已初始化的配置对象

// 新增：导入 JSON 校验与修复工具集
const jsonValidator = require('../utils/jsonValidator');

// 导入并验证 promptTemplates 模块
let promptTemplates;
try {
    promptTemplates = require('../services/promptTemplates');
    if (!promptTemplates || typeof promptTemplates !== 'object') {
        throw new Error(`promptTemplates 模块导入异常: 期望对象，实际获得 ${typeof promptTemplates}`);
    }
    if (typeof promptTemplates.generatePrompt !== 'function') {
        throw new Error(`promptTemplates.generatePrompt 不是函数: 实际类型为 ${typeof promptTemplates.generatePrompt}`);
    }
} catch (importError) {
    const errorMsg = `导入 promptTemplates 模块失败: ${importError.message}`;
    console.error(`[llmService.js][CRITICAL] ${errorMsg}`);
    // 创建一个临时的logger用于记录这个关键错误（如果主logger还没初始化）
    if (logger && typeof logger.error === 'function') {
        logger.error(`[文件：llmService.js][LLM服务][模块初始化][CRITICAL] ${errorMsg}`);
    }
    // 为了避免整个模块加载失败，我们创建一个错误占位符
    promptTemplates = {
        generatePrompt: function() {
            throw new Error(`promptTemplates 模块未正确加载: ${importError.message}`);
        }
    };
}

// 模块级日志前缀
const moduleLogPrefix = `[文件：llmService.js][LLM服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`); // 日志: 模块加载完成

/**
 * @功能概述: 通用的 LLM 调用函数。根据任务类型动态加载提示词模板，并与 LLM API 进行交互。
 * @param {string} taskType - 任务类型 (例如 'CORRECT_TRANSCRIPTION', 'TRANSLATE_SUBTITLE')。
 *                             此类型用于查找对应的提示词模板目录。
 * @param {object} options - 包含 LLM 调用所需参数的对象。
 * @param {object} [options.promptParams={}] - 传递给提示词模板进行插值的参数。
 *                                           例如: { raw_text: "...", language_of_text: "English", systemPromptContent: "You are a helpful assistant." ... }
 *                                           其中 `systemPromptContent` 可用于直接在此处定义系统提示，优先级高于顶层 `options.systemMessageContent`。
 * @param {string} [options.templateName='default'] - 要使用的提示词模板文件的名称（不含.md后缀）。
 * @param {string} [options.modelName] - (可选) 要使用的LLM模型名称。如果未提供，则会尝试从配置中获取默认模型。
 * @param {string} [options.reqId='generic_llm_req'] - (可选) 请求ID，用于日志追踪。
 * @param {string} [options.systemMessageContent] - (可选) 明确指定的系统提示内容。如果提供，且 `options.promptParams.systemPromptContent` 未定义或无效，则会使用此系统提示。
 * @param {number} [options.temperature] - (可选) LLM的temperature参数。
 * @param {number} [options.max_tokens] - (可选) LLM的max_tokens参数。
 * @param {boolean} [options.forceJsonOutput=false] - (可选) 是否强制LLM输出JSON格式。设置为true时，将向API发送response_format参数。
 * @param {number} [options.retryCount=0] - (可选) API调用失败时的重试次数。默认为0，表示不重试（总共尝试1次）。
 * @param {number} [options.retryDelay=1000] - (可选) 每次重试之间的延迟时间（毫秒）。默认为1000ms。
 * @param {boolean} [options.validateJsonOutput=false] - (可选) 是否在LLM响应后校验其输出是否为有效JSON (仅当 forceJsonOutput 为 true 时生效)。默认为 false。
 * @param {number} [options.maxJsonValidationRetries=1] - (可选) 如果 validateJsonOutput 为 true 且JSON无效，允许的修正重试次数。默认为1。
 * @param {string} [options.jsonRetrySystemPrompt] - (可选) 当请求LLM修正无效JSON时使用的系统提示。如果未提供，会使用一个默认的通用提示。
 * @param {Array<object>} [options.history=[]] - (可选) 对话历史记录，将被插入到系统提示（如果存在）和用户主提示之间。
 * @returns {Promise<object>} 包含 LLM 处理结果的对象。
 *                            成功时: { status: 'success', processedText: '...', modelUsed: '...', usage: {...}, originalOptions: {...} }
 *                            失败时: { status: 'error', message: '...', details: '...', originalOptions: {...} }
 * @执行流程:
 *   1.  参数准备、验证与日志记录 (包括确定 reqId, taskType, templateName, promptParams 等)。
 *   2.  获取并渲染用户主提示 (调用 promptTemplates.generatePrompt，得到 userPromptContent)。
 *   3.  确定最终使用的 LLM 模型名称 (综合 options.modelName 和 config 配置)。
 *   4.  构建发送给 LLM API 的 messages 数组:
 *       4.1 确定系统提示内容 (systemMessageForApi)，优先级如下:
 *           - options.promptParams.systemPromptContent (如果有效)
 *           - options.systemMessageContent (即从顶层 options 解构的 systemMessageFromOptions, 如果有效)
 *           - 默认系统提示 ("You are a helpful assistant.")
 *       4.2 调用 createMessages 函数，传入 userPromptContent, options.history, 和 systemMessageForApi。
 *   5.  准备 API 请求的其余参数 (temperature, max_tokens, response_format 等)。
 *   6.  执行 LLM API 调用 (例如 OpenRouter)，包含重试逻辑 (针对网络/服务器错误) 和 JSON 验证/修正逻辑 (如果启用)。
 *   7.  处理 API 响应，提取结果。
 *   8.  返回结构化的成功或失败结果对象。
 */

/**
 * @功能概述: 根据主内容、历史记录和系统消息创建符合LLM API要求的消息数组。
 * @param {string} mainContent - 用户的主要输入或提示。
 * @param {Array<object>} [history=[]] - 对话历史记录，每个对象应包含 'role' 和 'content'。
 * @param {string|null} [systemMessageContent=null] - 系统消息的内容。如果为null、空字符串或仅包含空格，则不包含系统消息。
 * @returns {Array<object>} 构建好的消息数组。
 */
const createMessages = (mainContent, history = [], systemMessageContent = null) => {
    const messages = []; // 初始化为空数组

    // 只有当 systemMessageContent 有效（非null、非undefined、非仅包含空格的字符串）时，才添加 system 角色消息
    if (systemMessageContent && String(systemMessageContent).trim()) {
        messages.push({ role: 'system', content: String(systemMessageContent).trim() });
    }

    history.forEach(h => {
        messages.push({ role: h.role, content: h.content });
    });
    messages.push({ role: 'user', content: mainContent });
    return messages;
};

/**
 * @功能概述: API增强配置合并器 - 智能合并传统配置和新增强配置
 * @param {object} legacyOptions - 传统的options参数
 * @param {object} apiEnhancements - 新的API增强配置
 * @param {string} logPrefix - 日志前缀
 * @returns {object} 合并后的配置对象
 */
function mergeApiConfigurations(legacyOptions, apiEnhancements, logPrefix) {
    const config = {
        // 基础配置（来自现有参数）
        basic: {
            modelName: legacyOptions.modelName,
            temperature: legacyOptions.temperature,
            max_tokens: legacyOptions.max_tokens,
            forceJsonOutput: legacyOptions.forceJsonOutput || false,
            retryCount: legacyOptions.retryCount || 0,
            retryDelay: legacyOptions.retryDelay || 1000,
            validateJsonOutput: legacyOptions.validateJsonOutput || false,
            maxJsonValidationRetries: legacyOptions.maxJsonValidationRetries || 1,
            jsonRetrySystemPrompt: legacyOptions.jsonRetrySystemPrompt || "Your previous JSON output was invalid. Please review the error and the original request, and provide a corrected, valid JSON response. Ensure all strings are properly escaped, objects and arrays are complete, and there are no extraneous characters outside the main JSON structure.",
            messages: null, // 将在后续步骤中设置
            apiKey: null,   // 将从config中获取
            appUrl: null,   // 将从config中获取
            appName: null   // 将从config中获取
        },
        
        // 增强配置（新功能）
        enhanced: {
            structuredOutput: apiEnhancements.structuredOutput || { enabled: false },
            streaming: apiEnhancements.streaming || { enabled: false },
            customHeaders: apiEnhancements.customHeaders || {},
            transforms: apiEnhancements.transforms || [],
            advancedRetry: apiEnhancements.advancedRetry || { 
                exponentialBackoff: false,
                jitter: false,
                maxDelay: 30000
            },
            responseProcessing: apiEnhancements.responseProcessing || {
                customValidator: null,
                customParser: null
            }
        }
    };
    
    // 记录配置合并结果
    if (apiEnhancements && Object.keys(apiEnhancements).length > 0) {
        logger.info(`${logPrefix}[Enhanced API] 检测到API增强配置，启用新功能`);
        
        if (config.enhanced.structuredOutput.enabled) {
            logger.info(`${logPrefix}[Enhanced API] - OpenRouter Structured Outputs: 启用`);
        }
        
        if (config.enhanced.streaming.enabled) {
            logger.info(`${logPrefix}[Enhanced API] - 流式输出: 启用`);
        }
        
        if (config.enhanced.transforms.length > 0) {
            logger.info(`${logPrefix}[Enhanced API] - Message Transforms: ${config.enhanced.transforms.join(', ')}`);
        }
        
        if (Object.keys(config.enhanced.customHeaders).length > 0) {
            logger.info(`${logPrefix}[Enhanced API] - 自定义请求头: ${Object.keys(config.enhanced.customHeaders).join(', ')}`);
        }
    } else {
        logger.debug(`${logPrefix}[Enhanced API] 使用传统模式，无API增强配置`);
    }
    
    return config;
}

/**
 * @功能概述: 构建增强的API请求体
 * @param {object} config - 合并后的配置对象
 * @param {Array} messages - 消息数组
 * @param {string} determinedModelName - 确定的模型名称
 * @param {string} logPrefix - 日志前缀
 * @returns {object} 增强的请求体
 */
function buildEnhancedRequestBody(config, messages, determinedModelName, logPrefix) {
    const body = {
        model: determinedModelName,
        messages: messages
    };
    
    // 添加基础参数
    if (config.basic.temperature !== undefined) {
        body.temperature = config.basic.temperature;
    }
    if (config.basic.max_tokens !== undefined) {
        body.max_tokens = config.basic.max_tokens;
    }
    
    // === 新功能：OpenRouter Structured Outputs ===
    if (config.enhanced.structuredOutput.enabled && config.enhanced.structuredOutput.schema) {
        body.response_format = {
            type: 'json_schema',
            json_schema: config.enhanced.structuredOutput.schema
        };
        logger.info(`${logPrefix}[Enhanced API] 启用 OpenRouter Structured Outputs: ${config.enhanced.structuredOutput.schema.name || 'unnamed'}`);
    } else if (config.basic.forceJsonOutput) {
        // 向前兼容：使用传统的 JSON 输出格式
        body.response_format = { type: "json_object" };
        logger.info(`${logPrefix}[Enhanced API] 使用传统 JSON 输出格式（向前兼容）`);
    }
    
    // === 新功能：Message Transforms ===
    if (config.enhanced.transforms.length > 0) {
        body.transforms = config.enhanced.transforms;
        logger.info(`${logPrefix}[Enhanced API] 启用 Message Transforms: ${config.enhanced.transforms.join(', ')}`);
    }
    
    // === 新功能：流式输出 ===
    if (config.enhanced.streaming.enabled) {
        body.stream = true;
        logger.info(`${logPrefix}[Enhanced API] 启用流式输出`);
    }
    
    return body;
}

/**
 * @功能概述: 构建增强的请求头
 * @param {object} config - 合并后的配置对象
 * @param {string} logPrefix - 日志前缀
 * @returns {object} 增强的请求头
 */
function buildEnhancedHeaders(config, logPrefix) {
    const headers = {
        'Authorization': `Bearer ${config.basic.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': config.basic.appUrl || 'YOUR_APP_URL_OR_DOMAIN',
        'X-Title': config.basic.appName || 'YOUR_APP_NAME'
    };
    
    // 合并自定义请求头
    if (Object.keys(config.enhanced.customHeaders).length > 0) {
        Object.assign(headers, config.enhanced.customHeaders);
        logger.info(`${logPrefix}[Enhanced API] 应用自定义请求头: ${Object.keys(config.enhanced.customHeaders).join(', ')}`);
    }
    
    return headers;
}

/**
 * @功能概述: 增强的重试逻辑执行器
 * @param {Function} apiCallFunction - API调用函数
 * @param {object} config - 配置对象
 * @param {string} logPrefix - 日志前缀
 * @returns {Promise<object>} API响应结果
 */
async function executeEnhancedRetryLogic(apiCallFunction, config, logPrefix) {
    const maxTotalAttempts = config.basic.retryCount + 1;
    let lastError = null;
    let attempt = 1;
    
    while (attempt <= maxTotalAttempts) {
        try {
            logger.info(`${logPrefix}[Enhanced Retry] 尝试 ${attempt}/${maxTotalAttempts} - 调用API`);
            
            const result = await apiCallFunction();
            logger.info(`${logPrefix}[Enhanced Retry] 第 ${attempt} 次尝试成功`);
            return result;
            
        } catch (error) {
            lastError = error;
            logger.warn(`${logPrefix}[Enhanced Retry] 第 ${attempt} 次尝试失败: ${error.message}`);
            
            // 判断是否为不可重试的错误
            let isNonRetryable = false;
            if (error.response) {
                const statusCode = error.response.status;
                if (statusCode >= 400 && statusCode < 500 && statusCode !== 429) {
                    isNonRetryable = true;
                    logger.warn(`${logPrefix}[Enhanced Retry] HTTP ${statusCode} 被视为不可重试错误`);
                }
            }
            
            if (error.message && (
                error.message.toLowerCase().includes("invalid api key") ||
                error.message.toLowerCase().includes("authentication failed")
            )) {
                isNonRetryable = true;
                logger.warn(`${logPrefix}[Enhanced Retry] 认证错误，不可重试`);
            }
            
            // 如果到达最大尝试次数或遇到不可重试错误
            if (attempt >= maxTotalAttempts || isNonRetryable) {
                const reason = isNonRetryable ? "不可重试错误" : "达到最大尝试次数";
                logger.error(`${logPrefix}[Enhanced Retry] 重试失败: ${reason}`);
                break;
            }
            
            // 计算延迟时间
            let delay = config.basic.retryDelay;
            
            if (config.enhanced.advancedRetry.exponentialBackoff) {
                delay = Math.min(
                    config.basic.retryDelay * Math.pow(2, attempt - 1),
                    config.enhanced.advancedRetry.maxDelay
                );
                logger.debug(`${logPrefix}[Enhanced Retry] 使用指数退避: ${delay}ms`);
            }
            
            if (config.enhanced.advancedRetry.jitter) {
                delay += Math.random() * 1000; // 添加最多1秒的随机抖动
                logger.debug(`${logPrefix}[Enhanced Retry] 应用抖动后延迟: ${Math.round(delay)}ms`);
            }
            
            logger.info(`${logPrefix}[Enhanced Retry] 等待 ${Math.round(delay)}ms 后重试`);
            await new Promise(resolve => setTimeout(resolve, delay));
            
            attempt++;
        }
    }
    
    // 如果所有重试都失败了，抛出最后的错误
    throw lastError;
}



async function callLLM(taskType, options = {}) {
    // 步骤 1: 参数准备与日志记录
    const reqId = options.reqId || 'generic_llm_req';
    const logPrefix = `[文件：llmService.js][LLM服务][callLLM][TaskType:${taskType}][ReqID:${reqId}]`;

    logger.info(`${logPrefix}[步骤 1.1] 接收到通用 LLM 调用请求。`);
    logger.debug(`${logPrefix}[步骤 1.2] 传入选项 (options) 预览: ${JSON.stringify(options)}`);

    // 步骤 1.3: 参数类型验证和处理
    if (typeof taskType !== 'string' || !taskType.trim()) {
        const errorMsg = 'taskType 参数必须是非空字符串';
        logger.error(`${logPrefix}[ERROR][步骤 1.3] ${errorMsg}。接收到的 taskType: ${typeof taskType} "${taskType}"`);
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'InvalidTaskTypeError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }

    const {
        promptParams = {},
        templateName: templateNameFromOptions,
        modelName: modelNameFromOptions,
        systemMessageContent: systemMessageFromOptions,
        temperature: temperatureFromOptions,
        max_tokens: maxTokensFromOptions,
        forceJsonOutput = false,
        retryCount = 0,
        retryDelay = 1000,
        validateJsonOutput = false,
        maxJsonValidationRetries = 1,
        jsonRetrySystemPrompt = "Your previous JSON output was invalid. Please review the error and the original request, and provide a corrected, valid JSON response. Ensure all strings are properly escaped, objects and arrays are complete, and there are no extraneous characters outside the main JSON structure.",
        
        // === 新增：API增强配置 ===
        apiEnhancements = {}
    } = options;

    // 步骤 1.4: 智能配置合并
    const enhancedConfig = mergeApiConfigurations(options, apiEnhancements, logPrefix);

    // 步骤 1.5: 确保 templateName 是字符串类型
    let templateName = 'default';
    if (templateNameFromOptions !== undefined) {
        if (typeof templateNameFromOptions === 'string' && templateNameFromOptions.trim()) {
            templateName = templateNameFromOptions.trim();
            logger.debug(`${logPrefix}[步骤 1.5a] 使用options中提供的templateName: "${templateName}"`);
        } else {
            logger.warn(`${logPrefix}[WARN][步骤 1.5b] options.templateName 不是有效字符串 (类型: ${typeof templateNameFromOptions}, 值: ${templateNameFromOptions})，使用默认值 "default"`);
        }
    }

    // 步骤 1.6: 验证 promptParams 类型
    if (typeof promptParams !== 'object' || promptParams === null) {
        const errorMsg = 'promptParams 参数必须是对象类型';
        logger.error(`${logPrefix}[ERROR][步骤 1.6] ${errorMsg}。接收到的 promptParams: ${typeof promptParams}`);
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'InvalidPromptParamsError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }

    // 步骤 2: 获取并渲染提示词模板
    logger.info(`${logPrefix}[步骤 2.1] 开始获取任务类型 '${taskType}' (模板名: '${templateName}') 的提示词。`);
    
    if (typeof promptTemplates !== 'object' || !promptTemplates) {
        const errorMsg = 'promptTemplates 模块未正确加载或导入失败';
        logger.error(`${logPrefix}[ERROR][步骤 2.2] ${errorMsg}。promptTemplates 类型: ${typeof promptTemplates}`);
        // 修改点: throw error
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'PromptTemplatesModuleError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }

    if (typeof promptTemplates.generatePrompt !== 'function') {
        const errorMsg = 'promptTemplates.generatePrompt 不是一个函数';
        logger.error(`${logPrefix}[ERROR][步骤 2.2] ${errorMsg}。generatePrompt 类型: ${typeof promptTemplates.generatePrompt}`);
        // 修改点: throw error
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'PromptGenerateMethodError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }

    let promptResult;
    try {
        logger.debug(`${logPrefix}[步骤 2.3] 调用 promptTemplates.generatePrompt("${taskType}", "${templateName}", promptParams对象)`);
        promptResult = promptTemplates.generatePrompt(taskType, promptParams, templateName);
    } catch (promptError) {
        const errorMsg = `调用 promptTemplates.generatePrompt 时发生错误: ${promptError.message}`;
        logger.error(`${logPrefix}[ERROR][步骤 2.3] ${errorMsg}`);
        // 修改点: throw error, 包装原始错误
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'PromptGenerationCallError';
        // @ts-ignore
        err.originalOptions = options;
        // @ts-ignore
        err.cause = promptError; // 保留原始错误信息
        throw err;
    }

    if (!promptResult || !promptResult.prompt) {
        const errorMsg = `无法为任务类型 '${taskType}' (模板: '${templateName}') 获取有效的提示词。参数: ${JSON.stringify(promptParams)}`;
        logger.error(`${logPrefix}[ERROR][步骤 2.4] ${errorMsg}`);
        // 修改点: throw error
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'PromptGenerationError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }
    let userPromptContent = promptResult.prompt;
    const promptFilePath = promptResult.filePath || '未知来源';
    logger.info(`${logPrefix}[步骤 2.5] 成功获取提示词。来源: ${promptFilePath}`);

    // 步骤 3: 确定要使用的 LLM 模型（增强版）
    let determinedModelName = enhancedConfig.basic.modelName;
    if (!determinedModelName) {
        if (config.llm && config.llm.taskTypes && config.llm.taskTypes[taskType] && config.llm.taskTypes[taskType].defaultModel) {
            determinedModelName = config.llm.taskTypes[taskType].defaultModel;
            logger.info(`${logPrefix}[步骤 3.1a] 未在options中指定模型，使用任务类型 '${taskType}' 配置的默认模型: ${determinedModelName}`);
        } else if (taskType === 'TRANSLATE_SUBTITLE' && config.llm.defaultTranslationModel) {
            determinedModelName = config.llm.defaultTranslationModel;
            logger.info(`${logPrefix}[步骤 3.1b] 使用旧配置的默认翻译模型: ${determinedModelName}`);
        } else if (taskType === 'CORRECT_TRANSCRIPTION' && config.llm.defaultCorrectionModel) {
            determinedModelName = config.llm.defaultCorrectionModel;
            logger.info(`${logPrefix}[步骤 3.1c] 使用旧配置的默认校正模型: ${determinedModelName}`);
        } else if (config.llm.defaultGenericModel) {
            determinedModelName = config.llm.defaultGenericModel;
            logger.info(`${logPrefix}[步骤 3.1d] 使用通用默认模型: ${determinedModelName}`);
        } else {
            determinedModelName = 'google/gemini-pro';
            logger.warn(`${logPrefix}[WARN][步骤 3.1e] 使用硬编码备选模型: ${determinedModelName}`);
        }
    } else {
        logger.info(`${logPrefix}[步骤 3.2] 使用options中直接指定的LLM模型: ${determinedModelName}`);
    }
    logger.info(`${logPrefix}[步骤 3.3] 最终确定的LLM模型: ${determinedModelName}`);

    // 步骤 4: 构建发送给 LLM API 的 messages 数组
    logger.debug(`${logPrefix}[步骤 4.1] 开始构建 LLM API 的 messages 数组。`);
    let systemMessageForApi = 'You are a helpful assistant.';
    if (promptParams && promptParams.systemPromptContent && String(promptParams.systemPromptContent).trim()) {
        systemMessageForApi = String(promptParams.systemPromptContent).trim();
        logger.info(`${logPrefix}[步骤 4.2.1] 使用来自 options.promptParams.systemPromptContent 的系统提示。`);
    } else if (systemMessageFromOptions && String(systemMessageFromOptions).trim()) {
        systemMessageForApi = String(systemMessageFromOptions).trim();
        logger.info(`${logPrefix}[步骤 4.2.2] 使用来自 options.systemMessageContent (顶层options解构出的systemMessageFromOptions) 的系统提示。`);
    } else {
        logger.info(`${logPrefix}[步骤 4.2.3] 未提供特定系统提示，使用默认系统提示: "${systemMessageForApi}"`);
    }
    logger.debug(`${logPrefix}[步骤 4.2.4] 最终确定的系统提示 (将传递给 createMessages): "${systemMessageForApi || '无'}"`);
    const messages = createMessages(userPromptContent, options.history, systemMessageForApi);
    if (messages && messages.length > 0) {
        logger.debug(`${logPrefix}[步骤 4.4] 构建的 messages 数组包含 ${messages.length} 条消息。`);
        logger.debug(`${logPrefix}[步骤 4.4.1] 首条消息 (role: ${messages[0].role}, content preview: ${String(messages[0].content).substring(0, 70)}...)`);
        if (messages.length > 1) {
            logger.debug(`${logPrefix}[步骤 4.4.2] 末条消息 (role: ${messages[messages.length - 1].role}, content preview: ${String(messages[messages.length - 1].content).substring(0, 100)}...)`);
        }
    } else {
        logger.warn(`${logPrefix}[WARN][步骤 4.4] 构建的 messages 数组为空或无效。`);
    }

    // 步骤 5: 准备增强的API参数
    enhancedConfig.basic.temperature = enhancedConfig.basic.temperature;
    enhancedConfig.basic.max_tokens = enhancedConfig.basic.max_tokens;
    
    // 应用配置优先级逻辑
    if (enhancedConfig.basic.temperature === undefined && config.llm && config.llm.taskTypes && config.llm.taskTypes[taskType] && config.llm.taskTypes[taskType].options && config.llm.taskTypes[taskType].options.temperature !== undefined) {
        enhancedConfig.basic.temperature = config.llm.taskTypes[taskType].options.temperature;
        logger.info(`${logPrefix}[步骤 5.1] 使用任务类型 '${taskType}' 配置的默认 temperature: ${enhancedConfig.basic.temperature}`);
    }
    if (enhancedConfig.basic.max_tokens === undefined && config.llm && config.llm.taskTypes && config.llm.taskTypes[taskType] && config.llm.taskTypes[taskType].options && config.llm.taskTypes[taskType].options.max_tokens !== undefined) {
        enhancedConfig.basic.max_tokens = config.llm.taskTypes[taskType].options.max_tokens;
        logger.info(`${logPrefix}[步骤 5.2] 使用任务类型 '${taskType}' 配置的默认 max_tokens: ${enhancedConfig.basic.max_tokens}`);
    }
    if (enhancedConfig.basic.temperature === undefined && config.llm && config.llm.defaultOptions && config.llm.defaultOptions.temperature !== undefined) {
        enhancedConfig.basic.temperature = config.llm.defaultOptions.temperature;
        logger.info(`${logPrefix}[步骤 5.3] 使用全局配置的默认 temperature: ${enhancedConfig.basic.temperature}`);
    }
    if (enhancedConfig.basic.max_tokens === undefined && config.llm && config.llm.defaultOptions && config.llm.defaultOptions.max_tokens !== undefined) {
        enhancedConfig.basic.max_tokens = config.llm.defaultOptions.max_tokens;
        logger.info(`${logPrefix}[步骤 5.4] 使用全局配置的默认 max_tokens: ${enhancedConfig.basic.max_tokens}`);
    }
    
    // 设置API配置信息
    enhancedConfig.basic.apiKey = config.openrouter.apiKey;
    enhancedConfig.basic.appUrl = config.appUrl;
    enhancedConfig.basic.appName = config.appName;
    
    logger.info(`${logPrefix}[步骤 5.5] 最终参数: temperature=${enhancedConfig.basic.temperature === undefined ? 'LLM默认' : enhancedConfig.basic.temperature}, max_tokens=${enhancedConfig.basic.max_tokens === undefined ? 'LLM默认' : enhancedConfig.basic.max_tokens}, 增强功能=${Object.keys(apiEnhancements).length > 0 ? '启用' : '禁用'}`);

    // 步骤 6: 构建增强的API请求
    if (!enhancedConfig.basic.apiKey || !config.openrouter.chatCompletionsUrl) {
        const errorMsg = 'OpenRouter API Key 或 Chat Completions URL 未在配置中定义。请检查配置文件。';
        logger.error(`${logPrefix}[ERROR][步骤 6.1] ${errorMsg}`);
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = 'ConfigurationError';
        // @ts-ignore
        err.originalOptions = options;
        throw err;
    }

    const apiUrl = config.openrouter.chatCompletionsUrl;
    
    // 使用增强的请求体构建器
    const body = buildEnhancedRequestBody(enhancedConfig, messages, determinedModelName, logPrefix);
    
    // 使用增强的请求头构建器
    const headers = buildEnhancedHeaders(enhancedConfig, logPrefix);
    
    // 日志记录
    logger.info(`${logPrefix}[步骤 6.2] 准备调用增强LLM API。Endpoint: ${apiUrl}, Model: ${determinedModelName}`);
    const requestBodyForLog = JSON.stringify({...body, messages: [{role: 'system', content: (messages[0] && messages[0].role === 'system' ? messages[0].content.substring(0,50) + '...' : 'N/A')}, {role:'user', content: (messages[messages.length -1].content.substring(0,100) + '...')}] });
    logger.debug(`${logPrefix}[步骤 6.3] 请求体 (预览): ${requestBodyForLog}`);
    const requestBodySize = JSON.stringify(body).length;
    logger.debug(`${logPrefix}[步骤 6.4] API实际请求体大小: ${requestBodySize} bytes.`);
    if (requestBodySize > (config.llm?.requestSizeWarningThreshold || 50000)) {
        logger.warn(`${logPrefix}[警告][步骤 6.5] API请求体较大 (${requestBodySize} bytes)，可能影响性能或内存。`);
    }
    const timeoutForAxios = config.llm?.apiTimeout || 120000;

    // 步骤 7: 执行增强的API调用（带重试逻辑）
    const apiCallFunction = async () => {
        const startTime = Date.now();
        const response = await axios.post(apiUrl, body, { headers, timeout: timeoutForAxios });
        const duration = Date.now() - startTime;
        logger.info(`${logPrefix}[步骤 7.1] LLM API 调用成功。耗时: ${duration}ms。`);
        return response.data;
    };

    let rawLlmResponseData;
    try {
        if (enhancedConfig.enhanced.advancedRetry.exponentialBackoff || enhancedConfig.enhanced.advancedRetry.jitter) {
            // 使用增强重试逻辑
            rawLlmResponseData = await executeEnhancedRetryLogic(apiCallFunction, enhancedConfig, logPrefix);
        } else {
            // 使用传统重试逻辑（向前兼容）
            const maxTotalAttempts = enhancedConfig.basic.retryCount + 1;
            let lastError = null;
            
            for (let attempt = 1; attempt <= maxTotalAttempts; attempt++) {
                try {
                    logger.info(`${logPrefix}[步骤 7.0] 尝试调用LLM API (传统重试 ${attempt}/${maxTotalAttempts})`);
                    rawLlmResponseData = await apiCallFunction();
                    break;
                } catch (error) {
                    lastError = error;
                    logger.warn(`${logPrefix}[步骤 7.0] API调用尝试 ${attempt} 失败: ${error.message}`);
                    
                    let isNonRetryable = false;
                    if (error.response) {
                        const statusCode = error.response.status;
                        if (statusCode >= 400 && statusCode < 500 && statusCode !== 429) {
                            isNonRetryable = true;
                        }
                    }
                    if (error.message && (error.message.toLowerCase().includes("invalid api key") || error.message.toLowerCase().includes("authentication failed"))) {
                        isNonRetryable = true;
                    }
                    
                    if (attempt >= maxTotalAttempts || isNonRetryable) {
                        break;
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, enhancedConfig.basic.retryDelay));
                }
            }
            
            if (!rawLlmResponseData && lastError) {
                throw lastError;
            }
        }
    } catch (error) {
        logger.error(`${logPrefix}[错误][步骤 7.2] 调用 LLM API 时发生错误: ${error.message}`);
        let errorMessage = `LLM API 调用失败: ${error.message}`;
        let errorType = 'GenericApiError';
        let errorDetails = error.message;

        if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
            errorMessage = `LLM API 调用超时 (>${timeoutForAxios / 1000}s)`;
            errorType = 'TimeoutError';
        } else if (error.response) {
            errorMessage = `LLM API 错误: ${error.response.status}`;
            errorDetails = error.response.data;
            errorType = 'ApiResponseError';
        } else if (error.request) {
            errorMessage = `LLM API 无响应: ${error.message}`;
            errorType = 'NoResponseError';
        } else {
            errorMessage = `LLM API 请求设置错误: ${error.message}`;
            errorType = 'RequestSetupError';
        }
        
        const err = new Error(errorMessage);
        // @ts-ignore
        err.details = errorDetails;
        // @ts-ignore
        err.originalOptions = options;
        // @ts-ignore
        err.errorType = errorType;
        // @ts-ignore
        err.cause = error;
        throw err;
    }

    logger.info(`${logPrefix}[步骤 7.3] API 响应摘要 (来自 rawLlmResponseData):`);
    logger.info(`${logPrefix}   - ID: ${rawLlmResponseData.id || 'N/A'}`);
    logger.info(`${logPrefix}   - 模型: ${rawLlmResponseData.model || 'N/A'}`);
    logger.info(`${logPrefix}   - 提供商: ${rawLlmResponseData.provider || 'N/A'}`);
    logger.info(`${logPrefix}   - 完成原因: ${rawLlmResponseData.choices?.[0]?.finish_reason || 'N/A'}`);
    logger.info(`${logPrefix}   - 响应长度: ${rawLlmResponseData.choices?.[0]?.message?.content?.length || 0} 字符`);
    logger.debug(`${logPrefix}[步骤 7.3.1] 完整API响应 (rawLlmResponseData): ${JSON.stringify(rawLlmResponseData, null, 2)}`);

    let processedText = null;
    if (rawLlmResponseData && rawLlmResponseData.choices && rawLlmResponseData.choices.length > 0 && rawLlmResponseData.choices[0].message && rawLlmResponseData.choices[0].message.content) {
        processedText = rawLlmResponseData.choices[0].message.content;
        logger.info(`${logPrefix}[步骤 7.4.1] 成功从LLM响应中提取原始处理后的文本。`);
    } else {
        const errorMsg = 'LLM API 响应格式不符合预期 (无法提取 choices[0].message.content)。';
        logger.error(`${logPrefix}[错误][步骤 7.4.2] ${errorMsg} 实际响应数据: ${JSON.stringify(rawLlmResponseData)}`);
        // 修改点: throw error
        const err = new Error(errorMsg);
        // @ts-ignore
        err.details = rawLlmResponseData;
        // @ts-ignore
        err.originalOptions = options;
        // @ts-ignore
        err.errorType = 'InvalidResponseFormatError';
        throw err;
    }

    // 步骤 8: 增强的JSON验证和响应处理
    if (enhancedConfig.basic.validateJsonOutput && enhancedConfig.basic.forceJsonOutput) {
        logger.info(`${logPrefix}[步骤 8.1] 启用增强JSON输出校验。将尝试解析LLM响应。`);
        let jsonIsValid = false;
        let lastJsonParseError = null;
        let lastParsingAttemptsLog = null;

        for (let jsonAttempt = 1; jsonAttempt <= enhancedConfig.basic.maxJsonValidationRetries + 1; jsonAttempt++) {
            logger.info(`${logPrefix}[步骤 8.2] JSON校验尝试 ${jsonAttempt}/${enhancedConfig.basic.maxJsonValidationRetries + 1}.`);
            
            let validationResult;
            if (enhancedConfig.enhanced.responseProcessing.customValidator) {
                // 使用自定义验证器
                try {
                    validationResult = enhancedConfig.enhanced.responseProcessing.customValidator(processedText);
                    logger.info(`${logPrefix}[Enhanced API] 使用自定义JSON验证器`);
                } catch (customValidatorError) {
                    logger.warn(`${logPrefix}[Enhanced API] 自定义验证器失败，回退到默认验证器: ${customValidatorError.message}`);
                    validationResult = jsonValidator.extractAndParseJson(processedText, `${logPrefix}[jsonValidator]`);
                }
            } else {
                // 使用默认验证器
                validationResult = jsonValidator.extractAndParseJson(processedText, `${logPrefix}[jsonValidator]`);
            }
            
            if (validationResult.success) {
                logger.info(`${logPrefix}[步骤 8.2.1] JSON校验成功。`);
                processedText = JSON.stringify(validationResult.data);
                jsonIsValid = true;
                break;
            } else {
                logger.warn(`${logPrefix}[警告][步骤 8.2.2] JSON校验失败 (尝试 ${jsonAttempt})。错误: ${validationResult.error}`);
                lastJsonParseError = validationResult.error;
                lastParsingAttemptsLog = validationResult.parsingAttemptsLog;
                logger.debug(`${logPrefix}[步骤 8.2.3] JSON解析尝试日志: ${JSON.stringify(lastParsingAttemptsLog, null, 2)}`);

                if (jsonAttempt > enhancedConfig.basic.maxJsonValidationRetries) {
                    logger.error(`${logPrefix}[错误][步骤 8.2.4] 已达到JSON修正的最大尝试次数 (${enhancedConfig.basic.maxJsonValidationRetries})，但JSON仍无效。`);
                    break;
                }

                logger.info(`${logPrefix}[步骤 8.3] 准备请求LLM修正无效的JSON输出 (修正尝试 ${jsonAttempt}/${enhancedConfig.basic.maxJsonValidationRetries})。`);
                const originalUserPromptSummary = userPromptContent.length > 1000 ? userPromptContent.substring(0, 1000) + "... (truncated)" : userPromptContent;
                const retryUserPrompt = `The previous attempt to generate JSON resulted in an error. \nError details: ${lastJsonParseError}\nOriginal (or previous) invalid JSON output (may be truncated):\n\`\`\`json\n${processedText.substring(0, 2000)}...\n\`\`\`\nPlease analyze this error and the invalid output. Your task is to provide a corrected, valid JSON response based on the original request intent (summarized below) and the structure you were trying to produce. Ensure your entire response is a single, valid JSON object or array, with no extraneous text before or after it.\n\nSummary of original request intent:\n${originalUserPromptSummary}\n\nPlease provide only the corrected JSON.`;
                const retryMessages = [{ role: "system", content: enhancedConfig.basic.jsonRetrySystemPrompt }, { role: "user", content: retryUserPrompt }];
                const retryBody = { ...body, messages: retryMessages };
                const systemPromptPreview = enhancedConfig.basic.jsonRetrySystemPrompt ? enhancedConfig.basic.jsonRetrySystemPrompt.substring(0,50) + '...' : 'Default JSON retry prompt...';
                logger.debug(`${logPrefix}[步骤 8.3.1] JSON修正请求体 (预览): ${JSON.stringify({...retryBody, messages: [{role:'system', content: systemPromptPreview}, {role:'user', content: 'Correction prompt...'}] })}`);

                try {
                    logger.info(`${logPrefix}[步骤 8.3.2] 调用LLM进行JSON修正 (修正尝试 ${jsonAttempt})。`);
                    const retryStartTime = Date.now();
                    const correctionResponse = await axios.post(apiUrl, retryBody, { headers, timeout: timeoutForAxios });
                    const retryDuration = Date.now() - retryStartTime;
                    logger.info(`${logPrefix}[步骤 8.3.3] LLM JSON修正调用成功。耗时: ${retryDuration}ms。`);
                    if (correctionResponse.data && correctionResponse.data.choices && correctionResponse.data.choices.length > 0 && correctionResponse.data.choices[0].message && correctionResponse.data.choices[0].message.content) {
                        processedText = correctionResponse.data.choices[0].message.content;
                        logger.info(`${logPrefix}[步骤 8.3.4] 获取到修正后的LLM响应，将进行下一次JSON校验。新文本长度: ${processedText.length}`);
                        if (correctionResponse.data.usage) {
                            logger.info(`${logPrefix}[步骤 8.3.5] JSON修正调用Token使用: Prompt ${correctionResponse.data.usage.prompt_tokens}, Completion ${correctionResponse.data.usage.completion_tokens}`);
                        }
                    } else {
                        logger.warn(`${logPrefix}[警告][步骤 8.3.6] LLM JSON修正调用的响应格式不符合预期，无法提取修正内容。`);
                    }
                } catch (correctionApiError) {
                    logger.error(`${logPrefix}[错误][步骤 8.3.7] 调用LLM进行JSON修正时发生API错误: ${correctionApiError.message}。中止JSON修正尝试。`);
                    lastJsonParseError = `API error during JSON correction attempt: ${correctionApiError.message}`;
                    break; 
                }
            }
        }

        if (!jsonIsValid) {
            const errorMsg = `LLM未能生成有效的JSON输出，即使在 ${enhancedConfig.basic.maxJsonValidationRetries} 次修正尝试之后。`;
            logger.error(`${logPrefix}[错误][步骤 8.4] ${errorMsg} 最后一次解析错误: ${lastJsonParseError}`);
            const err = new Error(errorMsg);
            // @ts-ignore
            err.details = {
                lastAttemptedText: processedText,
                lastParseError: lastJsonParseError,
                parsingAttemptsLog: lastParsingAttemptsLog,
                originalLlmResponse: rawLlmResponseData
            };
            // @ts-ignore
            err.originalOptions = options;
            // @ts-ignore
            err.errorType = 'InvalidJsonResponseError';
            throw err;
        }
    }
    
    // 步骤 9: 应用自定义响应处理器（如果有）
    if (enhancedConfig.enhanced.responseProcessing.customParser) {
        try {
            logger.info(`${logPrefix}[Enhanced API] 应用自定义响应处理器`);
            processedText = enhancedConfig.enhanced.responseProcessing.customParser(processedText, rawLlmResponseData);
        } catch (customParserError) {
            logger.warn(`${logPrefix}[Enhanced API] 自定义响应处理器失败，使用原始响应: ${customParserError.message}`);
        }
    }

    // 步骤 10: 记录最终统计信息
    if (rawLlmResponseData.usage) {
        logger.info(`${logPrefix}[步骤 10.1 - 最终成功] Token使用统计: 输入${rawLlmResponseData.usage.prompt_tokens}, 输出${rawLlmResponseData.usage.completion_tokens}, 总计${rawLlmResponseData.usage.total_tokens}`);
        if (rawLlmResponseData.usage.completion_tokens >= (enhancedConfig.basic.max_tokens || 4000) * 0.9) {
            logger.warn(`${logPrefix}[警告 - 最终成功] 输出token接近限制 (已使用: ${rawLlmResponseData.usage.completion_tokens}, 限制: ${enhancedConfig.basic.max_tokens || 4000})，可能被截断。`);
        }
    }

    // 步骤 11: 构建增强的返回结果
    const result = {
        status: 'success',
        processedText: processedText,
        modelUsed: rawLlmResponseData.model || determinedModelName,
        usage: rawLlmResponseData.usage,
        originalOptions: options
    };
    
    // 添加增强功能的元数据（如果启用了增强功能）
    if (Object.keys(apiEnhancements).length > 0) {
        result.enhancedFeatures = {
            structuredOutputUsed: enhancedConfig.enhanced.structuredOutput.enabled,
            streamingUsed: enhancedConfig.enhanced.streaming.enabled,
            customHeadersUsed: Object.keys(enhancedConfig.enhanced.customHeaders).length > 0,
            transformsUsed: enhancedConfig.enhanced.transforms.length > 0,
            advancedRetryUsed: enhancedConfig.enhanced.advancedRetry.exponentialBackoff || enhancedConfig.enhanced.advancedRetry.jitter,
            customProcessingUsed: enhancedConfig.enhanced.responseProcessing.customValidator !== null || enhancedConfig.enhanced.responseProcessing.customParser !== null
        };
        logger.info(`${logPrefix}[Enhanced API] 返回增强功能元数据: ${JSON.stringify(result.enhancedFeatures)}`);
    }
    
    return result;
}








/**
 * @功能概述: 优化字幕数据，例如通过LLM进行润色、风格调整或长度控制。
 * @param {Array<object>} rawJsonData - 原始的简化字幕JSON数组，每个对象包含id, start, end, text。
 * @param {object} [optimizationParams={}] - 优化参数对象。
 * @param {string} [optimizationParams.modelName=config.llm.defaultCorrectionModel] - 用于优化的LLM模型名称。
 * @param {string} [optimizationParams.customPromptTemplate] - (可选) 用户提供的自定义提示词模板名称。
 * @param {object} [optimizationParams.promptParams={}] - (可选) 传递给提示词模板的额外参数。
 * @param {string} [reqId='N/A'] - (可选) 请求ID，用于日志追踪。
 * @returns {Promise<object>} 包含优化后字幕数据或错误信息的对象。
 *                          成功: { status: 'success', optimizedSubtitles: [...] }
 *                          失败: { status: 'error', message: '...', details: ... }
 */
async function optimizeSubtitles(rawJsonData, optimizationParams = {}, reqId = 'N/A') {
    const logPrefix = `[文件：llmService.js][LLM服务][optimizeSubtitles][ReqID:${reqId}]`;
    logger.info(`${logPrefix} 开始字幕优化任务。`); // 日志: 开始字幕优化任务

    if (!Array.isArray(rawJsonData) || rawJsonData.length === 0) {
        logger.error(`${logPrefix}[错误] 输入的原始字幕数据 (rawJsonData) 必须是非空数组。`); // 日志: 记录输入数据错误
        return { status: 'error', message: '输入的原始字幕数据必须是非空数组。' };
    }

    // 示例：将原始字幕数据转换为字符串，准备发送给LLM
    // 实际场景中，提示词工程会更复杂
    const subtitlesString = JSON.stringify(rawJsonData.map(item => item.text).join('\n'), null, 2);
    
    const llmOptions = {
        modelName: optimizationParams.modelName || config.llm.defaultCorrectionModel || config.openrouter.defaultModel, // 从优化参数或配置中获取模型
        promptParams: {
            subtitles_to_optimize: subtitlesString,
            optimization_goal: "润色文本，使其更自然流畅，同时保持原意。", // 示例优化目标
            ...(optimizationParams.promptParams || {})
        },
        templateName: optimizationParams.customPromptTemplate || 'OPTIMIZE_SUBTITLES_DEFAULT', // 需要在 promptTemplates.js 中定义此模板
        reqId: reqId,
        forceJsonOutput: optimizationParams.forceJsonOutput !== undefined ? optimizationParams.forceJsonOutput : true, // 默认为true，期望LLM返回JSON
        temperature: optimizationParams.temperature || 0.5,
        max_tokens: optimizationParams.max_tokens || 2000,
    };

    logger.info(`${logPrefix} 准备调用LLM进行字幕优化。模型: ${llmOptions.modelName}, 模板: ${llmOptions.templateName}`); // 日志: 准备调用LLM进行优化

    try {
        const response = await callLLM('OPTIMIZE_SUBTITLES', llmOptions); // OPTIMIZE_SUBTITLES 是一个新的 taskType

        if (response.status === 'success' && response.processedText) {
            logger.info(`${logPrefix} LLM优化调用成功。`); // 日志: LLM优化调用成功
            try {
                const optimizedResult = JSON.parse(response.processedText); // 假设LLM返回的是优化后的JSON数组字符串
                if (!Array.isArray(optimizedResult) || optimizedResult.length !== rawJsonData.length) {
                    logger.error(`${logPrefix}[错误] LLM返回的优化后字幕数量与原始数量不匹配或格式不正确。`); // 日志: 记录优化后字幕数量不匹配错误
                    return { status: 'error', message: 'LLM返回的优化后字幕数量与原始数量不匹配或格式不正确。', details: response.processedText };
                }
                // 此处可能需要将 optimizedResult 中的文本重新映射回 rawJsonData 的结构
                const finalOptimizedSubtitles = rawJsonData.map((item, index) => ({
                    ...item,
                    text: optimizedResult[index]?.text || item.text, // 假设LLM返回的对象数组，或者简单文本数组
                }));
                logger.info(`${logPrefix} 字幕优化成功完成。`); // 日志: 字幕优化成功完成
                return { status: 'success', optimizedSubtitles: finalOptimizedSubtitles, llmUsage: response.usage };
            } catch (parseError) {
                logger.error(`${logPrefix}[错误] 解析LLM优化响应失败: ${parseError.message}。响应文本: ${response.processedText.substring(0, 200)}...`); // 日志: 记录解析LLM优化响应失败
                return { status: 'error', message: `解析LLM优化响应失败: ${parseError.message}`, details: response.processedText };
            }
        } else {
            logger.error(`${logPrefix}[错误] LLM优化调用失败: ${response.message}`); // 日志: LLM优化调用失败
            return { status: 'error', message: `LLM优化调用失败: ${response.message}`, details: response.details, llmUsage: response.usage };
        }
    } catch (error) {
        logger.error(`${logPrefix}[致命错误] 调用 callLLM 进行字幕优化时发生意外错误: ${error.message}`); // 日志: 记录调用callLLM进行字幕优化时发生意外错误
        return { status: 'error', message: `调用 callLLM 进行字幕优化时发生意外错误: ${error.message}`, details: error.stack };
    }
}


module.exports = {
    callLLM,
    // translateTextViaOpenRouter, // 保持注释或移除，因为它已被废弃
    optimizeSubtitles,
};


// 记录信息级别日志，表示 LLM 服务模块的方法已成功导出
logger.info(`${moduleLogPrefix}LLM 服务方法已导出。`); // 日志: LLM服务方法已导出 