/**
 * @文件概述: 视频编辑器模块 - 重构后的视频编辑相关功能
 * @重构时间: 2025-06-12
 * @职责范围: 
 *   - 视频播放控制
 *   - 编辑模式切换（片段截取、画面裁剪、生成）
 *   - 裁剪功能（Cropper.js集成）
 *   - 视频生成流程
 *   - 字幕显示和时间同步
 */

// ============================================================================
// 1. 视频编辑器状态管理
// ============================================================================

/**
 * @功能概述: 创建视频编辑器专用状态
 * @返回值: {object} 包含视频编辑器状态的对象
 */
function createVideoEditorState() {
    const { ref, computed } = Vue;
    
    // 编辑器模式状态
    const editorMode = ref('segment-clipping'); // 'segment-clipping', 'frame-cropping', 'generation'
    
    // 视频播放器状态
    const videoEditorPlayer = ref(null);
    const videoCurrentTime = ref(0);
    const videoDurationRef = ref(0);
    const playbackSpeed = ref(1); // 播放速度：1 = 正常速度，0.5 = 50%速度

    // 画面裁剪模式自动预览相关状态
    const previewTimer = ref(null); // 预览计时器
    const previewIndex = ref(0); // 当前预览的时间点索引
    const previewTimePoints = ref([]); // 所有片段的开始和结束时间点数组
    const canvasUpdateTimer = ref(null); // Canvas实时更新计时器
    const imageCollection = ref([]); // 时间点图像集合
    const canvasImageTimer = ref(null); // Canvas图像切换计时器
    
    // 新增：图像收集和裁剪流程控制状态
    const isCollectingImages = ref(false); // 标识正在收集图像的过程中
    const isImageCollectionReady = ref(false); // 标识图像是否已收集完成并开始切换
    
    // 多段片段选择状态
    const clipSegments = ref([]); // 存储多个片段的数组，每个元素格式：{startTime: number, endTime: number}
    const currentSegmentIndex = ref(0); // 当前正在编辑的片段索引
    const isSelectingStart = ref(true); // 当前是否在选择开始时间（true: 选择开始时间, false: 选择结束时间）

    // 兼容性：为了保持现有代码的兼容性，提供当前片段的开始和结束时间
    const clipStartTime = computed(() => {
        const currentSegment = clipSegments.value[currentSegmentIndex.value];
        return currentSegment ? currentSegment.startTime : null;
    });
    const clipEndTime = computed(() => {
        const currentSegment = clipSegments.value[currentSegmentIndex.value];
        return currentSegment ? currentSegment.endTime : null;
    });
    


    
    // 裁剪相关状态
    const cropDataForBackend = ref({
        isCroppingActive: false,
        cropWidth: null,
        cropHeight: null,
        cropXOffset: null,
        cropYOffset: null
    });
    
    const confirmedCropParams = ref({
        isConfirmed: false,
        x: null,
        y: null,
        width: null,
        height: null
    });
    
    // Cropper.js实例
    const cropperInstance = ref(null);
    
    // 字幕相关状态
    const englishSubtitles = ref([]);
    // 移除：const chineseSubtitles = ref([]);
    
    // 当前显示的字幕（简化为只有英文）
    const currentSubtitles = ref('');

    return {
        // 编辑器模式
        editorMode,

        // 视频播放器
        videoEditorPlayer,
        videoCurrentTime,
        videoDurationRef,
        playbackSpeed,

        // 画面裁剪模式预览
        previewTimer,
        previewIndex,
        previewTimePoints,
        canvasUpdateTimer,
        imageCollection,
        canvasImageTimer,

        // 多段片段选择
        clipSegments,
        currentSegmentIndex,
        isSelectingStart,

        // 兼容性：当前片段的开始和结束时间
        clipStartTime,
        clipEndTime,

        // 裁剪功能
        cropDataForBackend,
        confirmedCropParams,
        cropperInstance,

        // 字幕显示
        currentSubtitles,
        englishSubtitles,

        // 新增：图像收集和裁剪流程控制状态
        isCollectingImages,
        isImageCollectionReady
    };
}

// ============================================================================
// 2. 视频编辑器计算属性
// ============================================================================

/**
 * @功能概述: 创建视频编辑器计算属性
 * @参数说明: {object} state - 视频编辑器状态对象
 * @返回值: {object} 包含计算属性的对象
 */
function createVideoEditorComputed(state) {
    const { computed } = Vue;
    
    /**
     * @功能概述: 视频进度百分比
     */
    const videoProgressPercentage = computed(() => {
        return state.videoDurationRef.value > 0
            ? (state.videoCurrentTime.value / state.videoDurationRef.value) * 100
            : 0;
    });
    
    /**
     * @功能概述: 多段片段操作按钮标签
     */
    const segmentActionButtonLabel = computed(() => {
        const segmentCount = state.clipSegments.value.length;
        const currentIndex = state.currentSegmentIndex.value;

        if (segmentCount === 0 || state.isSelectingStart.value) {
            return `设置片段${currentIndex + 1}开始时间`;
        } else {
            const currentSegment = state.clipSegments.value[currentIndex];
            if (currentSegment && currentSegment.startTime !== null && currentSegment.endTime === null) {
                return `设置片段${currentIndex + 1}结束时间`;
            }
        }
        return `设置片段${currentIndex + 1}开始时间`;
    });

    /**
     * @功能概述: 播放速度按钮标签
     */
    const playbackSpeedLabel = computed(() => {
        return state.playbackSpeed.value === 1 ? '1x' : '0.5x';
    });
    
    /**
     * @功能概述: 下一步按钮标题提示
     */
    const nextStepButtonTitle = computed(() => {
        if (state.editorMode.value === 'segment-clipping') {
            if (state.clipStartTime.value === null || state.clipEndTime.value === null) {
                return '请先完成片段选择';
            }
            return '进入画面裁剪模式';
        }
        if (state.editorMode.value === 'frame-cropping') {
            if (!state.confirmedCropParams.value.isConfirmed) {
                return '请先确认裁剪参数';
            }
            return '进入生成确认模式';
        }
        return '';
    });
    
    /**
     * @功能概述: 多段片段选择样式数组
     */
    const selectedSegmentStyles = computed(() => {
        const styles = [];
        if (state.videoDurationRef.value > 0) {
            state.clipSegments.value.forEach((segment, index) => {
                if (segment.startTime !== null && segment.endTime !== null) {
                    const startPercent = (segment.startTime / state.videoDurationRef.value) * 100;
                    const endPercent = (segment.endTime / state.videoDurationRef.value) * 100;
                    const width = endPercent - startPercent;

                    styles.push({
                        position: 'absolute',
                        left: startPercent + '%',
                        width: width + '%',
                        height: '100%',
                        backgroundColor: `rgba(${64 + index * 30}, ${158 - index * 20}, 255, 0.3)`,
                        border: `1px solid rgb(${64 + index * 30}, ${158 - index * 20}, 255)`,
                        borderRadius: '3px',
                        zIndex: 1
                    });
                }
            });
        }
        return styles;
    });

    /**
     * @功能概述: 兼容性：当前片段样式（保持原有接口）
     */
    const selectedSegmentStyle = computed(() => {
        const styles = selectedSegmentStyles.value;
        return styles.length > 0 ? styles[0] : { width: '0%' };
    });
    
    /**
     * @功能概述: 所有片段的开始标记位置数组
     */
    const allStartMarkers = computed(() => {
        const logPrefix = '[video-editor.js][allStartMarkers]';
        const markers = [];

        console.log(`${logPrefix} 计算所有开始标记，片段数量: ${state.clipSegments.value.length}`);
        console.log(`${logPrefix} 视频时长: ${state.videoDurationRef.value}`);

        if (state.videoDurationRef.value > 0) {
            state.clipSegments.value.forEach((segment, index) => {
                console.log(`${logPrefix} 片段${index + 1}:`, segment);
                if (segment.startTime !== null) {
                    const percent = (segment.startTime / state.videoDurationRef.value) * 100;
                    markers.push({
                        percent: percent,
                        segmentIndex: index,
                        time: segment.startTime
                    });
                    console.log(`${logPrefix} 片段${index + 1}开始标记: ${percent}%`);
                }
            });
        }

        console.log(`${logPrefix} 返回开始标记数组:`, markers);
        return markers;
    });

    /**
     * @功能概述: 所有片段的结束标记位置数组
     */
    const allEndMarkers = computed(() => {
        const logPrefix = '[video-editor.js][allEndMarkers]';
        const markers = [];

        console.log(`${logPrefix} 计算所有结束标记，片段数量: ${state.clipSegments.value.length}`);

        if (state.videoDurationRef.value > 0) {
            state.clipSegments.value.forEach((segment, index) => {
                console.log(`${logPrefix} 片段${index + 1}:`, segment);
                if (segment.endTime !== null) {
                    const percent = (segment.endTime / state.videoDurationRef.value) * 100;
                    markers.push({
                        percent: percent,
                        segmentIndex: index,
                        time: segment.endTime
                    });
                    console.log(`${logPrefix} 片段${index + 1}结束标记: ${percent}%`);
                }
            });
        }

        console.log(`${logPrefix} 返回结束标记数组:`, markers);
        return markers;
    });

    /**
     * @功能概述: 兼容性：当前片段的开始标记位置
     */
    const startMarkerLeftPercent = computed(() => {
        const markers = allStartMarkers.value;
        return markers.length > 0 ? markers[0].percent : -1;
    });

    /**
     * @功能概述: 兼容性：当前片段的结束标记位置
     */
    const endMarkerLeftPercent = computed(() => {
        const markers = allEndMarkers.value;
        return markers.length > 0 ? markers[0].percent : -1;
    });
    
    return {
        videoProgressPercentage,
        segmentActionButtonLabel,
        playbackSpeedLabel,
        nextStepButtonTitle,
        selectedSegmentStyles,
        selectedSegmentStyle,
        allStartMarkers,
        allEndMarkers,
        startMarkerLeftPercent,
        endMarkerLeftPercent
    };
}

// ============================================================================
// 3. 视频播放控制模块
// ============================================================================

/**
 * @功能概述: 创建视频播放控制功能
 * @参数说明: {object} state - 视频编辑器状态对象
 * @返回值: {object} 包含播放控制方法的对象
 */
function createVideoPlaybackFunctions(state) {
    const logPrefix = '[video-editor.js][createVideoPlaybackFunctions]';
    const { videoCurrentTime, videoDurationRef, playbackSpeed } = state;
    let videoEditorPlayerRef = null;

    const setVideoPlayerRef = (ref) => {
        videoEditorPlayerRef = ref;
    };

    /**
     * @功能概述: 处理视频时间更新事件
     * @调用关系: 由视频元素的timeupdate事件调用
     * @状态影响: 更新videoCurrentTime状态和字幕显示
     */
    const handleTimeUpdate = () => {
        const logPrefix = '[文件：video-editor.js][handleTimeUpdate]';

        if (videoEditorPlayerRef && videoEditorPlayerRef.value) {
            const currentTime = videoEditorPlayerRef.value.currentTime;
            videoCurrentTime.value = currentTime;

            // 每5秒输出一次时间更新日志，避免过多日志
            if (Math.floor(currentTime) % 5 === 0 && Math.floor(currentTime * 10) % 10 === 0) {
                console.log(`${logPrefix} 视频时间更新: ${currentTime}秒`);
            }

            // 更新字幕显示，传递state参数
            updateSubtitleDisplay(currentTime, state);
        } else {
            console.warn(`${logPrefix} 视频播放器元素未找到`);
        }
    };
    
    /**
     * @功能概述: 视频加载完成事件处理
     * @调用关系: 由视频元素的loadedmetadata事件调用
     * @状态影响: 更新videoDurationRef状态
     */
    const onVideoLoaded = () => {
        const logPrefix = '[文件：video-editor.js][onVideoLoaded]';
        if (videoEditorPlayerRef) {
            videoDurationRef.value = videoEditorPlayerRef.value.duration;
            console.log(`${logPrefix} 视频加载完成，时长: ${videoDurationRef.value}秒`);
        }
    };
    
    /**
     * @功能概述: 视频时间跳转
     * @参数说明: {number} seconds - 跳转的秒数（正数前进，负数后退）
     */
    const seekVideo = (seconds) => {
        if (videoEditorPlayerRef) {
            const newTime = Math.max(0, Math.min(
                videoEditorPlayerRef.value.currentTime + seconds,
                videoDurationRef.value
            ));
            videoEditorPlayerRef.value.currentTime = newTime;
        }
    };
    
    /**
     * @功能概述: 向前跳转
     * @参数说明: {number} seconds - 前进的秒数
     */
    const seekForward = (seconds) => {
        seekVideo(seconds);
    };
    
    /**
     * @功能概述: 向后跳转
     * @参数说明: {number} seconds - 后退的秒数
     */
    const seekBackward = (seconds) => {
        seekVideo(-seconds);
    };
    
    /**
     * @功能概述: 跳转到片段开始时间
     */
    const jumpToStartTime = () => {
        if (state.clipStartTime.value !== null && videoEditorPlayerRef) {
            videoEditorPlayerRef.value.currentTime = state.clipStartTime.value;
        }
    };
    
    /**
     * @功能概述: 跳转到片段结束时间
     */
    const jumpToEndTime = () => {
        if (state.clipEndTime.value !== null && videoEditorPlayerRef) {
            videoEditorPlayerRef.value.currentTime = state.clipEndTime.value;
        }
    };
    
    return {
        setVideoPlayerRef,
        handleTimeUpdate,
        onVideoLoaded,
        seekVideo,
        seekForward,
        seekBackward,
        jumpToStartTime,
        jumpToEndTime
    };
}

// ============================================================================
// 4. 片段选择模块
// ============================================================================

/**
 * @功能概述: 创建片段选择功能
 * @参数说明: {object} state - 视频编辑器状态对象
 * @返回值: {object} 包含片段选择方法的对象
 */
function createSegmentFunctions(state) {
    const logPrefix = '[video-editor.js][createSegmentFunctions]';
    const { clipSegments, currentSegmentIndex, isSelectingStart, videoDurationRef } = state;
    let videoEditorPlayerRef = null;

    const setVideoPlayerRef = (ref) => {
        videoEditorPlayerRef = ref;
    };

    /**
     * @功能概述: 检查某个时间点是否与现有片段重叠
     * @参数: time - 要检查的时间点
     * @参数: excludeIndex - 排除检查的片段索引（当前正在编辑的片段）
     * @返回: 如果重叠返回重叠的片段信息，否则返回null
     */
    const checkTimeOverlap = (time, excludeIndex) => {
        const logPrefix = '[文件：video-editor.js][checkTimeOverlap]';
        console.log(`${logPrefix} 检查时间${time}是否与已有片段重叠，排除索引: ${excludeIndex}`);

        for (let i = 0; i < state.clipSegments.value.length; i++) {
            if (i === excludeIndex) continue; // 跳过当前正在编辑的片段

            const segment = state.clipSegments.value[i];
            if (segment.startTime !== null && segment.endTime !== null) {
                console.log(`${logPrefix} 检查片段${i + 1}: [${segment.startTime}, ${segment.endTime}]`);
                if (time >= segment.startTime && time <= segment.endTime) {
                    console.log(`${logPrefix} ❌ 时间${time}与片段${i + 1}重叠`);
                    return { index: i, segment: segment };
                }
            }
        }
        console.log(`${logPrefix} ✅ 时间${time}无重叠`);
        return null;
    };

    /**
     * @功能概述: 检查新片段是否与已有片段重叠
     * @参数: startTime - 新片段开始时间
     * @参数: endTime - 新片段结束时间
     * @参数: excludeIndex - 排除检查的片段索引
     * @返回: 如果重叠返回重叠的片段信息，否则返回null
     */
    const checkSegmentOverlap = (startTime, endTime, excludeIndex) => {
        const logPrefix = '[文件：video-editor.js][checkSegmentOverlap]';
        console.log(`${logPrefix} 检查新片段[${startTime}, ${endTime}]是否与已有片段重叠`);

        for (let i = 0; i < state.clipSegments.value.length; i++) {
            if (i === excludeIndex) continue;

            const segment = state.clipSegments.value[i];
            if (segment.startTime !== null && segment.endTime !== null) {
                console.log(`${logPrefix} 检查与片段${i + 1}[${segment.startTime}, ${segment.endTime}]的重叠`);

                // 检查是否有重叠：新片段的开始时间在已有片段内，或新片段的结束时间在已有片段内，或新片段完全包含已有片段
                const hasOverlap = (
                    (startTime >= segment.startTime && startTime <= segment.endTime) ||  // 新片段开始时间在已有片段内
                    (endTime >= segment.startTime && endTime <= segment.endTime) ||      // 新片段结束时间在已有片段内
                    (startTime <= segment.startTime && endTime >= segment.endTime)       // 新片段完全包含已有片段
                );

                if (hasOverlap) {
                    console.log(`${logPrefix} ❌ 新片段与片段${i + 1}重叠`);
                    return { index: i, segment: segment };
                }
            }
        }
        console.log(`${logPrefix} ✅ 新片段无重叠`);
        return null;
    };

    /**
     * @功能概述: 处理多段片段选择操作（带重叠检测）
     * @调用关系: 由片段选择按钮调用
     * @状态影响: 更新clipSegments状态
     */
    const handleSegmentAction = () => {
        const logPrefix = '[文件：video-editor.js][handleSegmentAction]';
        const currentTime = state.videoCurrentTime.value;

        console.log(`${logPrefix} ========== 开始处理片段选择 ==========`);
        console.log(`${logPrefix} 当前视频时间: ${currentTime}秒`);
        console.log(`${logPrefix} 当前片段索引: ${state.currentSegmentIndex.value}`);
        console.log(`${logPrefix} 是否选择开始时间: ${state.isSelectingStart.value}`);
        console.log(`${logPrefix} 当前片段数组:`, JSON.stringify(state.clipSegments.value, null, 2));

        // 确保当前片段索引有效
        if (state.currentSegmentIndex.value >= state.clipSegments.value.length) {
            console.log(`${logPrefix} 需要创建新片段，当前索引: ${state.currentSegmentIndex.value}, 数组长度: ${state.clipSegments.value.length}`);
            // 创建新片段
            state.clipSegments.value.push({ startTime: null, endTime: null });
            console.log(`${logPrefix} 新片段已创建，更新后数组:`, JSON.stringify(state.clipSegments.value, null, 2));
        }

        const currentSegment = state.clipSegments.value[state.currentSegmentIndex.value];
        console.log(`${logPrefix} 当前操作的片段:`, JSON.stringify(currentSegment, null, 2));

        if (state.isSelectingStart.value || currentSegment.startTime === null) {
            // 设置开始时间 - 检查重叠
            console.log(`${logPrefix} 设置开始时间分支`);

            const overlap = checkTimeOverlap(currentTime, state.currentSegmentIndex.value);
            if (overlap) {
                console.warn(`${logPrefix} ❌ 开始时间${currentTime}与片段${overlap.index + 1}重叠`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.warning(`开始时间不能位于已有片段${overlap.index + 1}内（${overlap.segment.startTime}秒-${overlap.segment.endTime}秒）`);
                } else {
                    alert(`开始时间不能位于已有片段${overlap.index + 1}内`);
                }
                return;
            }

            currentSegment.startTime = currentTime;
            state.isSelectingStart.value = false;
            console.log(`${logPrefix} ✅ 设置片段${state.currentSegmentIndex.value + 1}开始时间: ${currentTime}秒`);
            console.log(`${logPrefix} 更新后isSelectingStart: ${state.isSelectingStart.value}`);
            console.log(`${logPrefix} 更新后片段:`, JSON.stringify(currentSegment, null, 2));

        } else if (currentSegment.endTime === null) {
            // 设置结束时间 - 检查基本验证和重叠
            console.log(`${logPrefix} 设置结束时间分支`);

            if (currentTime <= currentSegment.startTime) {
                console.warn(`${logPrefix} ❌ 结束时间${currentTime}不能早于或等于开始时间${currentSegment.startTime}`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.warning('结束时间必须晚于开始时间');
                } else {
                    alert('结束时间必须晚于开始时间');
                }
                return;
            }

            const timeOverlap = checkTimeOverlap(currentTime, state.currentSegmentIndex.value);
            if (timeOverlap) {
                console.warn(`${logPrefix} ❌ 结束时间${currentTime}与片段${timeOverlap.index + 1}重叠`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.warning(`结束时间不能位于已有片段${timeOverlap.index + 1}内（${timeOverlap.segment.startTime}秒-${timeOverlap.segment.endTime}秒）`);
                } else {
                    alert(`结束时间不能位于已有片段${timeOverlap.index + 1}内`);
                }
                return;
            }

            const segmentOverlap = checkSegmentOverlap(currentSegment.startTime, currentTime, state.currentSegmentIndex.value);
            if (segmentOverlap) {
                console.warn(`${logPrefix} ❌ 新片段[${currentSegment.startTime}, ${currentTime}]与片段${segmentOverlap.index + 1}重叠`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.warning(`新片段与已有片段${segmentOverlap.index + 1}重叠，请重新设置`);
                } else {
                    alert(`新片段与已有片段${segmentOverlap.index + 1}重叠，请重新设置`);
                }
                // 重置当前片段
                currentSegment.startTime = null;
                currentSegment.endTime = null;
                state.isSelectingStart.value = true;
                console.log(`${logPrefix} 重置当前片段，重新开始选择`);
                return;
            }

            currentSegment.endTime = currentTime;
            console.log(`${logPrefix} ✅ 设置片段${state.currentSegmentIndex.value + 1}结束时间: ${currentTime}秒`);
            console.log(`${logPrefix} 完成的片段:`, JSON.stringify(currentSegment, null, 2));

            // 片段完成，准备下一个片段
            const oldIndex = state.currentSegmentIndex.value;
            state.currentSegmentIndex.value++;
            state.isSelectingStart.value = true;
            console.log(`${logPrefix} 片段完成，索引从 ${oldIndex} 更新到 ${state.currentSegmentIndex.value}`);
            console.log(`${logPrefix} isSelectingStart重置为: ${state.isSelectingStart.value}`);

            // 使用正确的Element Plus消息API
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.success(`片段${oldIndex + 1}已完成，可以继续选择下一个片段`);
            } else {
                console.log(`${logPrefix} ✅ 片段${oldIndex + 1}已完成`);
            }
        }

        console.log(`${logPrefix} ========== 处理完成，最终状态 ==========`);
        console.log(`${logPrefix} 最终片段索引: ${state.currentSegmentIndex.value}`);
        console.log(`${logPrefix} 最终isSelectingStart: ${state.isSelectingStart.value}`);
        console.log(`${logPrefix} 最终片段数组:`, JSON.stringify(state.clipSegments.value, null, 2));
    };

    /**
     * @功能概述: 删除最后一个完整片段
     * @调用关系: 由重置按钮调用
     * @状态影响: 删除最后一个完整的片段
     */
    const resetSegmentSelection = () => {
        const logPrefix = '[文件：video-editor.js][resetSegmentSelection]';
        console.log(`${logPrefix} 开始精细化删除操作`);
        console.log(`${logPrefix} 当前片段数组:`, JSON.stringify(state.clipSegments.value, null, 2));

        // 检查数组是否为空
        if (state.clipSegments.value.length === 0) {
            console.log(`${logPrefix} 片段数组为空，无任何操作`);
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.info('没有片段可删除');
            }
            return;
        }

        // 获取最后一个片段
        const lastIndex = state.clipSegments.value.length - 1;
        const lastSegment = state.clipSegments.value[lastIndex];
        console.log(`${logPrefix} 最后一个片段 (索引${lastIndex}):`, JSON.stringify(lastSegment, null, 2));

        // 精细化删除逻辑
        if (lastSegment.endTime !== null) {
            // 情况1: endTime 有值，删除 endTime
            const deletedEndTime = lastSegment.endTime;
            lastSegment.endTime = null;
            
            console.log(`${logPrefix} 已删除片段${lastIndex + 1}的结束时间: ${deletedEndTime}秒`);
            
            // 调整选择状态：回到选择结束时间状态
            state.currentSegmentIndex.value = lastIndex;
            state.isSelectingStart.value = false; // 现在需要选择结束时间
            
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.success(`已删除片段${lastIndex + 1}的结束时间（${deletedEndTime}秒）`);
            }
            
        } else if (lastSegment.startTime !== null) {
            // 情况2: endTime 为 null，但 startTime 有值，删除整个片段（因为它不完整）
            const deletedStartTime = lastSegment.startTime;
            state.clipSegments.value.splice(lastIndex, 1);
            
            console.log(`${logPrefix} 已删除不完整的片段${lastIndex + 1}（开始时间: ${deletedStartTime}秒）`);
            
            // --- 核心修复：开始 ---
            // 之前错误的逻辑是聚焦到上一个已完成的片段，导致UI卡死。
            // 正确的逻辑是，删除一个不完整的片段后，应该准备好创建下一个新片段。

            // 将当前编辑索引指向下一个新片段的位置（即当前数组的长度）
            state.currentSegmentIndex.value = state.clipSegments.value.length;
            // 明确设置操作模式为"选择开始时间"
            state.isSelectingStart.value = true;
            
            console.log(`${logPrefix} 状态已重置，准备创建新的片段（索引: ${state.currentSegmentIndex.value}），操作模式: '选择开始时间'`);
            // --- 核心修复：结束 ---
            
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.success(`已删除片段${lastIndex + 1}的开始时间`);
            }
            
        } else {
            // 情况3: startTime 和 endTime 都为 null
            if (state.clipSegments.value.length === 1) {
                // 如果只有一个全空元素，保留它（这是初始状态）
                console.log(`${logPrefix} 最后一个片段为初始空片段，保留不删除`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.info('无法删除初始空片段');
                }
            } else {
                // 如果有多个元素且最后一个为全空，删除它
                state.clipSegments.value.splice(lastIndex, 1);
                console.log(`${logPrefix} 已删除空片段${lastIndex + 1}`);
                
                // 调整索引到新的最后一个片段
                state.currentSegmentIndex.value = Math.max(0, state.clipSegments.value.length - 1);
                
                // 检查新的最后一个片段的状态，决定选择状态
                const newLastSegment = state.clipSegments.value[state.currentSegmentIndex.value];
                if (newLastSegment.startTime !== null && newLastSegment.endTime === null) {
                    state.isSelectingStart.value = false; // 需要选择结束时间
                } else {
                    state.isSelectingStart.value = true; // 需要选择开始时间
                }
                
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.success(`已删除空片段${lastIndex + 1}`);
                }
            }
        }

        console.log(`${logPrefix} 删除后片段数组:`, JSON.stringify(state.clipSegments.value, null, 2));
        console.log(`${logPrefix} 调整后当前索引: ${state.currentSegmentIndex.value}`);
        console.log(`${logPrefix} 调整后选择状态: ${state.isSelectingStart.value ? '选择开始时间' : '选择结束时间'}`);
    };

    /**
     * @功能概述: 切换播放速度
     * @调用关系: 由播放速度按钮调用
     * @状态影响: 在1x和0.5x之间切换播放速度
     */
    const togglePlaybackSpeed = () => {
        const logPrefix = '[文件：video-editor.js][togglePlaybackSpeed]';

        if (state.playbackSpeed.value === 1) {
            // 切换到0.5x速度
            state.playbackSpeed.value = 0.5;
            console.log(`${logPrefix} 切换到0.5x播放速度`);
        } else {
            // 切换到1x速度
            state.playbackSpeed.value = 1;
            console.log(`${logPrefix} 切换到1x播放速度`);
        }

        // 应用播放速度到视频元素 - 多种方式查找
        let videoElement = state.videoEditorPlayer.value;

        // 如果state中没有，尝试从全局引用获取
        if (!videoElement && window.videoEditorPlayerGlobal) {
            videoElement = window.videoEditorPlayerGlobal;
            console.log(`${logPrefix} 从全局引用获取视频元素`);
        }

        // 如果还是没有，尝试直接从DOM查找
        if (!videoElement) {
            videoElement = document.querySelector('video[ref="videoEditorPlayer"]') || document.querySelector('video');
            console.log(`${logPrefix} 从DOM查找视频元素`);
        }

        if (videoElement) {
            videoElement.playbackRate = state.playbackSpeed.value;
            console.log(`${logPrefix} 视频播放速度已设置为: ${state.playbackSpeed.value}x`);

            // 使用正确的Element Plus消息API
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.info(`播放速度已设置为 ${state.playbackSpeed.value}x`);
            } else {
                console.log(`${logPrefix} ✅ 播放速度已设置为 ${state.playbackSpeed.value}x`);
            }
        } else {
            console.warn(`${logPrefix} ❌ 视频播放器未初始化`);
            console.error(`${logPrefix} 调试信息:`, {
                stateVideoPlayer: !!state.videoEditorPlayer.value,
                globalVideoPlayer: !!window.videoEditorPlayerGlobal,
                domVideoElements: document.querySelectorAll('video').length
            });
        }
    };

    /**
     * @功能概述: 生成预览时间点数组
     * @调用关系: 由startSegmentPreview调用
     * @状态影响: 更新previewTimePoints数组
     */
    const generatePreviewTimePoints = () => {
        const logPrefix = '[文件：video-editor.js][generatePreviewTimePoints]';
        const timePoints = [];

        console.log(`${logPrefix} 开始生成预览时间点`);
        console.log(`${logPrefix} 当前片段数组:`, JSON.stringify(state.clipSegments.value, null, 2));

        state.clipSegments.value.forEach((segment, index) => {
            if (segment.startTime !== null && segment.endTime !== null) {
                timePoints.push({
                    time: segment.startTime,
                    type: 'start',
                    segmentIndex: index,
                    description: `片段${index + 1}开始`
                });
                timePoints.push({
                    time: segment.endTime,
                    type: 'end',
                    segmentIndex: index,
                    description: `片段${index + 1}结束`
                });
                console.log(`${logPrefix} 添加片段${index + 1}: 开始${segment.startTime}秒, 结束${segment.endTime}秒`);
            }
        });

        // 按时间排序
        timePoints.sort((a, b) => a.time - b.time);

        state.previewTimePoints.value = timePoints;
        console.log(`${logPrefix} 生成的预览时间点:`, timePoints);

        return timePoints;
    };

    /**
     * @功能概述: 开始片段预览
     * @调用关系: 进入frame-cropping模式时调用
     * @状态影响: 启动预览计时器
     */
    const startSegmentPreview = () => {
        const logPrefix = '[文件：video-editor.js][startSegmentPreview]';
        console.log(`${logPrefix} 开始启动片段预览`);

        // 清除现有计时器
        stopSegmentPreview();

        // 生成预览时间点
        const timePoints = generatePreviewTimePoints();

        if (timePoints.length === 0) {
            console.log(`${logPrefix} 没有完整片段，无法启动预览`);
            return;
        }

        // 重置预览索引
        state.previewIndex.value = 0;

        // 立即跳转到第一个时间点
        if (state.videoEditorPlayer.value && timePoints[0]) {
            state.videoEditorPlayer.value.currentTime = timePoints[0].time;
            console.log(`${logPrefix} 立即跳转到第一个时间点: ${timePoints[0].time}秒 (${timePoints[0].description})`);
        }

        // 启动计时器，每0.5秒切换一次
        state.previewTimer.value = setInterval(() => {
            const currentTimePoint = timePoints[state.previewIndex.value];

            if (state.videoEditorPlayer.value && currentTimePoint) {
                state.videoEditorPlayer.value.currentTime = currentTimePoint.time;
                console.log(`${logPrefix} 预览切换到: ${currentTimePoint.time}秒 (${currentTimePoint.description})`);

                // 移动到下一个时间点
                state.previewIndex.value = (state.previewIndex.value + 1) % timePoints.length;
            }
        }, 500); // 每0.5秒切换一次

        console.log(`${logPrefix} ✅ 片段预览已启动，计时器ID:`, state.previewTimer.value);
    };

    /**
     * @功能概述: 停止片段预览
     * @调用关系: 离开frame-cropping模式时调用
     * @状态影响: 清除预览计时器
     */
    const stopSegmentPreview = () => {
        const logPrefix = '[文件：video-editor.js][stopSegmentPreview]';

        if (state.previewTimer.value) {
            console.log(`${logPrefix} 清除预览计时器，ID:`, state.previewTimer.value);
            clearInterval(state.previewTimer.value);
            state.previewTimer.value = null;
            state.previewIndex.value = 0;
            state.previewTimePoints.value = [];
            console.log(`${logPrefix} ✅ 片段预览已停止`);
        } else {
            console.log(`${logPrefix} 没有活跃的预览计时器`);
        }
    };

    /**
     * @功能概述: 收集时间点图像
     * @调用关系: 进入frame-cropping模式时调用
     * @状态影响: 更新imageCollection数组
     */
    const collectTimePointImages = async () => {
        const logPrefix = '[文件：video-editor.js][collectTimePointImages]';
        console.log(`${logPrefix} 开始收集时间点图像`);

        // 生成预览时间点
        const timePoints = generatePreviewTimePoints();

        if (timePoints.length === 0) {
            console.log(`${logPrefix} 没有完整片段，无法收集图像`);
            return;
        }

        // 获取视频元素 - 多种方式查找
        let videoElement = state.videoEditorPlayer.value;

        // 如果state中没有，尝试从全局引用获取
        if (!videoElement && window.videoEditorPlayerGlobal) {
            videoElement = window.videoEditorPlayerGlobal;
            console.log(`${logPrefix} 从全局引用获取视频元素`);
        }

        // 如果还是没有，尝试直接从DOM查找
        if (!videoElement) {
            videoElement = document.querySelector('video[ref="videoEditorPlayer"]') || document.querySelector('video');
            console.log(`${logPrefix} 从DOM查找视频元素`);
        }

        if (!videoElement) {
            console.error(`${logPrefix} 视频元素不存在`);
            console.error(`${logPrefix} 调试信息:`, {
                stateVideoPlayer: !!state.videoEditorPlayer.value,
                globalVideoPlayer: !!window.videoEditorPlayerGlobal,
                domVideoElements: document.querySelectorAll('video').length
            });
            return;
        }

        console.log(`${logPrefix} 找到视频元素:`, {
            src: videoElement.src,
            videoWidth: videoElement.videoWidth,
            videoHeight: videoElement.videoHeight,
            readyState: videoElement.readyState
        });

        console.log(`${logPrefix} 开始收集${timePoints.length}个时间点的图像`);
        state.imageCollection.value = [];

        // 保存原始时间
        const originalTime = videoElement.currentTime;

        for (let i = 0; i < timePoints.length; i++) {
            const timePoint = timePoints[i];
            console.log(`${logPrefix} 收集第${i + 1}个图像: ${timePoint.time}秒 (${timePoint.description})`);

            try {
                // 跳转到指定时间
                videoElement.currentTime = timePoint.time;

                // 等待视频跳转完成
                await new Promise((resolve) => {
                    const onSeeked = () => {
                        videoElement.removeEventListener('seeked', onSeeked);
                        resolve();
                    };
                    videoElement.addEventListener('seeked', onSeeked);

                    // 超时保护
                    setTimeout(resolve, 1000);
                });

                // 创建临时Canvas捕获当前帧
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = videoElement.videoWidth || 1920;
                tempCanvas.height = videoElement.videoHeight || 1080;
                tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);

                // 保存图像数据
                const imageData = tempCanvas.toDataURL('image/jpeg', 0.9);
                state.imageCollection.value.push({
                    time: timePoint.time,
                    description: timePoint.description,
                    imageData: imageData,
                    segmentIndex: timePoint.segmentIndex,
                    type: timePoint.type
                });

                console.log(`${logPrefix} ✅ 第${i + 1}个图像收集完成`);

            } catch (error) {
                console.error(`${logPrefix} 收集第${i + 1}个图像失败:`, error);
            }
        }

        // 恢复原始时间
        videoElement.currentTime = originalTime;

        console.log(`${logPrefix} ✅ 图像收集完成，共${state.imageCollection.value.length}张图像`);
        console.log(`${logPrefix} 图像集合:`, state.imageCollection.value.map(img => ({
            time: img.time,
            description: img.description
        })));
    };

    /**
     * @功能概述: 启动Canvas图像切换
     * @调用关系: 启用画面裁剪时调用
     * @状态影响: 启动Canvas图像切换计时器
     */
    const startCanvasImageSwitching = (canvas) => {
        const logPrefix = '[文件：video-editor.js][startCanvasImageSwitching]';
        console.log(`${logPrefix} 启动Canvas图像切换`);

        // 清除现有计时器
        stopCanvasImageSwitching();

        if (state.imageCollection.value.length === 0) {
            console.warn(`${logPrefix} 图像集合为空，无法启动切换`);
            return;
        }

        let currentIndex = 0;
        const ctx = canvas.getContext('2d');

        const switchImage = () => {
            const currentImage = state.imageCollection.value[currentIndex];

            if (currentImage) {
                const img = new Image();
                img.onload = () => {
                    // 清除Canvas并绘制新图像
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                    console.log(`${logPrefix} 切换到图像: ${currentImage.time}秒 (${currentImage.description})`);
                };
                img.src = currentImage.imageData;

                // 移动到下一张图像
                currentIndex = (currentIndex + 1) % state.imageCollection.value.length;
            }
        };

        // 立即显示第一张图像
        switchImage();

        // 启动定时切换，每0.5秒切换一次
        state.canvasImageTimer.value = setInterval(switchImage, 500);

        console.log(`${logPrefix} ✅ Canvas图像切换已启动，每0.5秒切换一次`);
    };

    /**
     * @功能概述: 停止Canvas图像切换
     * @调用关系: 关闭画面裁剪时调用
     * @状态影响: 清除Canvas图像切换计时器
     */
    const stopCanvasImageSwitching = () => {
        const logPrefix = '[文件：video-editor.js][stopCanvasImageSwitching]';

        if (state.canvasImageTimer.value) {
            console.log(`${logPrefix} 清除Canvas图像切换计时器`);
            clearInterval(state.canvasImageTimer.value);
            state.canvasImageTimer.value = null;
            console.log(`${logPrefix} ✅ Canvas图像切换已停止`);
        } else {
            console.log(`${logPrefix} 没有活跃的Canvas图像切换计时器`);
        }
    };

    /**
     * @功能概述: 启动Canvas实时更新（保持Cropper选择框可见）
     * @调用关系: 在画面裁剪模式下启用裁剪器时调用
     * @状态影响: 启动Canvas更新机制
     */
    const startCanvasRealTimeUpdate = (state) => {
        const logPrefix = '[文件：video-editor.js][startCanvasRealTimeUpdate]';
        console.log(`${logPrefix} 启动Canvas实时更新（保持裁剪框可见）`);

        // 清除现有的Canvas更新
        stopCanvasRealTimeUpdate();

        const videoElement = state.videoEditorPlayer.value;
        const canvas = document.getElementById('cropperCanvas');

        if (!videoElement || !canvas) {
            console.warn(`${logPrefix} 视频元素或Canvas不存在，无法启动实时更新`);
            return;
        }

        const updateCanvasFrame = () => {
            if (canvas && videoElement && state.cropperInstance.value) {
                try {
                    // 使用Cropper的replace方法更新图像，这样可以保持选择框
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');

                    // 获取视频的实际尺寸
                    tempCanvas.width = videoElement.videoWidth || videoElement.clientWidth;
                    tempCanvas.height = videoElement.videoHeight || videoElement.clientHeight;

                    // 将当前视频帧绘制到临时Canvas
                    tempCtx.drawImage(videoElement, 0, 0, tempCanvas.width, tempCanvas.height);

                    // 使用Cropper的replace方法更新图像，保持选择框
                    const imageDataURL = tempCanvas.toDataURL('image/jpeg', 0.8);
                    state.cropperInstance.value.replace(imageDataURL);

                    console.log(`${logPrefix} Cropper图像已更新，时间: ${videoElement.currentTime}秒`);

                } catch (error) {
                    console.warn(`${logPrefix} Cropper更新失败:`, error);
                }
            }
        };

        // 监听视频时间更新事件
        state.canvasUpdateTimer.value = updateCanvasFrame;
        videoElement.addEventListener('timeupdate', updateCanvasFrame);

        // 立即更新一次
        updateCanvasFrame();

        console.log(`${logPrefix} ✅ Cropper实时更新已启动，监听timeupdate事件`);
    };

    /**
     * @功能概述: 停止Canvas实时更新
     * @调用关系: 关闭裁剪器或离开画面裁剪模式时调用
     * @状态影响: 移除Canvas更新事件监听
     */
    const stopCanvasRealTimeUpdate = () => {
        const logPrefix = '[文件：video-editor.js][stopCanvasRealTimeUpdate]';

        if (state.canvasUpdateTimer.value && state.videoEditorPlayer.value) {
            console.log(`${logPrefix} 移除Canvas更新事件监听`);
            state.videoEditorPlayer.value.removeEventListener('timeupdate', state.canvasUpdateTimer.value);
            state.canvasUpdateTimer.value = null;
            console.log(`${logPrefix} ✅ Canvas实时更新已停止`);
        } else {
            console.log(`${logPrefix} 没有活跃的Canvas更新监听`);
        }
    };

    return {
        setVideoPlayerRef,
        checkTimeOverlap,
        checkSegmentOverlap,
        handleSegmentAction,
        resetSegmentSelection,
        togglePlaybackSpeed,
        generatePreviewTimePoints,
        startSegmentPreview,
        stopSegmentPreview,
        collectTimePointImages,
        startCanvasImageSwitching,
        stopCanvasImageSwitching,
        startCanvasRealTimeUpdate,
        stopCanvasRealTimeUpdate
    };
}

// ============================================================================
// 5. 编辑器模式切换模块
// ============================================================================

/**
 * @功能概述: 创建编辑器模式切换相关功能
 * @参数说明: {object} state - 视频编辑器状态对象
 * @参数说明: {object} segmentFunctions - 包含片段处理方法的对象
 * @返回值: {object} 包含模式切换方法的对象
 */
function createModeSwitchFunctions(state, segmentFunctions) {
    const logPrefix = '[video-editor.js][createModeSwitchFunctions]';
    const { editorMode, cropperInstance, clipSegments, confirmedCropParams, imageCollection, cropDataForBackend } = state;
    let videoEditorPlayerRef = null;

    const setVideoPlayerRef = (ref) => {
        videoEditorPlayerRef = ref;
    };

    /**
     * @功能概述: 导航到片段截取模式
     * @调用关系: 由导航按钮调用
     * @状态影响: 只更新editorMode状态
     */
    const navigateToSegmentClipping = () => {
        const logPrefix = '[文件：video-editor.js][navigateToSegmentClipping]';
        console.log(`${logPrefix} 导航到片段截取模式`);

        // 如果当前在裁剪模式，清理所有相关UI状态
        if (state.editorMode.value === 'frame-cropping') {
            console.log(`${logPrefix} 清理frame-cropping模式的UI状态`);

            // 清理Cropper.js实例
            if (state.cropDataForBackend.value.isCroppingActive && state.cropperInstance.value) {
                console.log(`${logPrefix} 清理活跃的裁剪器UI`);
                state.cropperInstance.value.destroy();
                state.cropperInstance.value = null;
                state.cropDataForBackend.value.isCroppingActive = false;
            }

            // 清理Cropper.js的Canvas
            const cropperCanvas = document.getElementById('cropperCanvas');
            if (cropperCanvas) {
                console.log(`${logPrefix} 移除cropperCanvas`);
                cropperCanvas.remove();
            }

            // 清理frame-cropping模式的Canvas
            const frameCroppingCanvas = document.getElementById('frameCroppingCanvas');
            if (frameCroppingCanvas) {
                console.log(`${logPrefix} 移除frameCroppingCanvas`);
                frameCroppingCanvas.remove();
            }

            // 停止Canvas图像切换
            segmentFunctions.stopCanvasImageSwitching();

            // 重置图像收集状态
            state.isCollectingImages.value = false;
            state.isImageCollectionReady.value = false;

            // 确保视频元素可见
            if (state.videoEditorPlayer.value) {
                state.videoEditorPlayer.value.style.display = 'block';
            }
        }

        // 只切换模式，不控制视频状态
        state.editorMode.value = 'segment-clipping';
    };

    /**
     * @功能概述: 导航到画面裁剪模式
     * @调用关系: 由导航按钮调用
     * @状态影响: 切换到 'frame-cropping' 模式，暂停视频，开始图像收集流程（不自动启动裁剪）
     */
    const navigateToFrameCropping = async () => {
        const logPrefix = '[文件：video-editor.js][navigateToFrameCropping]';
        console.log(`${logPrefix} 导航到画面裁剪模式`);

        // 停止任何正在进行的预览
        segmentFunctions.stopSegmentPreview();

        // 切换模式
        editorMode.value = 'frame-cropping';
        
        // 关键修改：不自动启动裁剪功能
        cropDataForBackend.value.isCroppingActive = false;

        if (videoEditorPlayerRef && videoEditorPlayerRef.value) {
            videoEditorPlayerRef.value.pause();
        }

        // 重置图像收集状态
        state.isCollectingImages.value = false;
        state.isImageCollectionReady.value = false;

        console.log(`${logPrefix} 开始图像收集流程`);
        
        // 每次都重新收集图像，确保基于最新的片段状态
        try {
            // 设置收集状态
            state.isCollectingImages.value = true;
            
            // 清空现有图像集合，强制重新收集
            imageCollection.value = [];
            
            console.log(`${logPrefix} 开始收集图像，确保基于最新片段状态`);
            await segmentFunctions.collectTimePointImages();
            
            console.log(`${logPrefix} 图像收集完成，开始Canvas渲染和图像切换`);
            
            // 创建Canvas并开始图像切换
            await initializeImageSwitching(state);
            
            // 设置图像准备就绪状态
            state.isImageCollectionReady.value = true;
            
            console.log(`${logPrefix} ✅ 图像切换已启动，"启用画面裁剪"按钮现在可用`);
            
        } catch (error) {
            console.error(`${logPrefix} 图像收集或切换启动失败:`, error);
        } finally {
            // 无论成功失败，都标记收集过程结束
            state.isCollectingImages.value = false;
        }
    };

    /**
     * @功能概述: 导航到参数设置模式
     * @调用关系: 由导航按钮调用
     * @状态影响: 只更新editorMode状态
     */
    const navigateToParameterSettings = () => {
        const logPrefix = '[文件：video-editor.js][navigateToParameterSettings]';
        console.log(`${logPrefix} 导航到参数设置模式`);

        // 如果当前在frame-cropping模式，清理相关UI状态
        if (state.editorMode.value === 'frame-cropping') {
            console.log(`${logPrefix} 清理frame-cropping模式的UI状态`);

            // 清理Cropper.js实例
            if (state.cropDataForBackend.value.isCroppingActive && state.cropperInstance.value) {
                console.log(`${logPrefix} 清理活跃的裁剪器UI`);
                state.cropperInstance.value.destroy();
                state.cropperInstance.value = null;
                state.cropDataForBackend.value.isCroppingActive = false;
            }

            // 清理Cropper.js的Canvas
            const cropperCanvas = document.getElementById('cropperCanvas');
            if (cropperCanvas) {
                console.log(`${logPrefix} 移除cropperCanvas`);
                cropperCanvas.remove();
            }

            // 清理frame-cropping模式的Canvas
            const frameCroppingCanvas = document.getElementById('frameCroppingCanvas');
            if (frameCroppingCanvas) {
                console.log(`${logPrefix} 移除frameCroppingCanvas`);
                frameCroppingCanvas.remove();
            }

            // 停止Canvas图像切换
            segmentFunctions.stopCanvasImageSwitching();

            // 重置图像收集状态
            state.isCollectingImages.value = false;
            state.isImageCollectionReady.value = false;

            // 确保视频元素可见
            if (state.videoEditorPlayer.value) {
                state.videoEditorPlayer.value.style.display = 'block';
            }
        }

        state.editorMode.value = 'parameter-settings';
    };

    /**
     * @功能概述: 导航到生成模式
     * @调用关系: 由导航按钮调用
     * @状态影响: 只更新editorMode状态
     */
    const navigateToGeneration = () => {
        const logPrefix = '[文件：video-editor.js][navigateToGeneration]';
        console.log(`${logPrefix} 导航到生成模式`);

        // 如果当前在frame-cropping模式，清理相关UI状态
        if (state.editorMode.value === 'frame-cropping') {
            console.log(`${logPrefix} 清理frame-cropping模式的UI状态`);

            // 清理Cropper.js实例
            if (state.cropDataForBackend.value.isCroppingActive && state.cropperInstance.value) {
                console.log(`${logPrefix} 清理活跃的裁剪器UI`);
                state.cropperInstance.value.destroy();
                state.cropperInstance.value = null;
                state.cropDataForBackend.value.isCroppingActive = false;
            }

            // 清理Cropper.js的Canvas
            const cropperCanvas = document.getElementById('cropperCanvas');
            if (cropperCanvas) {
                console.log(`${logPrefix} 移除cropperCanvas`);
                cropperCanvas.remove();
            }

            // 清理frame-cropping模式的Canvas
            const frameCroppingCanvas = document.getElementById('frameCroppingCanvas');
            if (frameCroppingCanvas) {
                console.log(`${logPrefix} 移除frameCroppingCanvas`);
                frameCroppingCanvas.remove();
            }

            // 停止Canvas图像切换
            segmentFunctions.stopCanvasImageSwitching();

            // 重置图像收集状态
            state.isCollectingImages.value = false;
            state.isImageCollectionReady.value = false;

            // 确保视频元素可见
            if (state.videoEditorPlayer.value) {
                state.videoEditorPlayer.value.style.display = 'block';
            }
        }

        state.editorMode.value = 'generation';
    };

    /**
     * @功能概述: 初始化图像切换（创建Canvas并启动图像轮播）
     * @调用关系: 由 navigateToFrameCropping 调用
     * @状态影响: 创建Canvas元素并启动图像切换
     */
    const initializeImageSwitching = async (state) => {
        const logPrefix = '[文件：video-editor.js][initializeImageSwitching]';
        console.log(`${logPrefix} 开始初始化Canvas和图像切换`);

        try {
            // 获取视频元素
            let videoElement = state.videoEditorPlayer.value;
            if (!videoElement && window.videoEditorPlayerGlobal) {
                videoElement = window.videoEditorPlayerGlobal;
            }
            if (!videoElement) {
                videoElement = document.querySelector('video[ref="videoEditorPlayer"]') || document.querySelector('video');
            }

            if (!videoElement) {
                throw new Error('找不到视频元素');
            }

            // 创建或获取canvas元素
            let canvas = document.getElementById('cropperCanvas');
            if (!canvas) {
                console.log(`${logPrefix} 创建新的canvas元素`);
                canvas = document.createElement('canvas');
                canvas.id = 'cropperCanvas';
                canvas.style.position = 'absolute';
                canvas.style.top = '0px';
                canvas.style.left = '0px';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.zIndex = '9999';
                canvas.style.pointerEvents = 'auto';
                canvas.style.display = 'block';

                // 将canvas插入到视频容器中
                const videoContainer = videoElement.parentElement;
                videoContainer.appendChild(canvas);
            }

            // 设置canvas尺寸与视频相同
            canvas.width = videoElement.videoWidth || 1920;
            canvas.height = videoElement.videoHeight || 1080;
            console.log(`${logPrefix} Canvas尺寸设置为: ${canvas.width}x${canvas.height}`);

            // 启动Canvas图像切换
            if (state.imageCollection.value.length > 0) {
                startCanvasImageSwitchingInternal(canvas, state);
                console.log(`${logPrefix} ✅ Canvas图像切换已启动`);
            } else {
                throw new Error('图像集合为空，无法启动图像切换');
            }

        } catch (error) {
            console.error(`${logPrefix} ❌ 初始化图像切换失败:`, error);
            throw error;
        }
    };

    return {
        setVideoPlayerRef,
        navigateToSegmentClipping,
        navigateToFrameCropping,
        navigateToParameterSettings,
        navigateToGeneration,
        initializeImageSwitching
    };
}

// ============================================================================
// 6. 裁剪功能模块
// ============================================================================

/**
 * @功能概述: 创建裁剪相关功能
 * @参数说明: {object} state - 视频编辑器状态对象
 * @参数说明: {object} segmentFunctions - 包含片段处理方法的对象
 * @返回值: {object} 包含裁剪方法的对象
 */
function createCroppingFunctions(state, segmentFunctions) {
    const logPrefix = '[video-editor.js][createCroppingFunctions]';
    const { editorMode, cropperInstance, cropDataForBackend, confirmedCropParams, imageCollection } = state;
    let videoEditorPlayerRef = null;

    const setVideoPlayerRef = (ref) => {
        videoEditorPlayerRef = ref;
    };

    /**
     * @功能概述: 切换裁剪状态
     * @调用关系: 由"启用/关闭画面裁剪"按钮调用
     * @状态影响: 更新cropDataForBackend状态，初始化或销毁Cropper实例
     */
    const toggleCropping = async () => {
        const logPrefix = '[文件：video-editor.js][toggleCropping]';

        try {
            if (editorMode.value !== 'frame-cropping') {
                console.warn(`${logPrefix} 仅在画面裁剪模式下可用`);
                return;
            }

            // 检查图像收集是否准备完成
            if (!state.isImageCollectionReady.value) {
                console.warn(`${logPrefix} 图像切换尚未准备完成，无法启动裁剪`);
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.warning('请等待图像加载完成后再启用裁剪');
                }
                return;
            }

            const isActive = !cropDataForBackend.value.isCroppingActive;
            cropDataForBackend.value.isCroppingActive = isActive;

            if (isActive) {
                console.log(`${logPrefix} 启用画面裁剪`);

                if (videoEditorPlayerRef && videoEditorPlayerRef.value) {
                    videoEditorPlayerRef.value.pause();
                }

                // 图像已经在切换，直接初始化Cropper
                console.log(`${logPrefix} 图像切换已准备完成，直接初始化Cropper`);
                initializeCropper(state);

            } else {
                console.log(`${logPrefix} 关闭画面裁剪`);
                destroyCropper(state);
            }
        } catch (error) {
            console.error(`${logPrefix} 裁剪切换失败:`, error);
        }
    };

    /**
     * @功能概述: 记录当前的裁剪数据
     * @调用关系: 由"确认裁剪"按钮调用
     * @状态影响: 更新confirmedCropParams状态
     */
    const logCropData = () => {
        const logPrefix = '[文件：video-editor.js][logCropData]';

        if (state.cropperInstance.value) {
            console.log(`${logPrefix} 使用Cropper.js获取数据`);
            const cropData = state.cropperInstance.value.getData();
            console.log(`${logPrefix} 确认裁剪参数:`, cropData);

            // 更新确认的裁剪参数
            state.confirmedCropParams.value = {
                isConfirmed: true,
                x: Math.round(cropData.x),
                y: Math.round(cropData.y),
                width: Math.round(cropData.width),
                height: Math.round(cropData.height)
            };

            // 更新后端数据
            state.cropDataForBackend.value = {
                ...state.cropDataForBackend.value,
                cropWidth: Math.round(cropData.width),
                cropHeight: Math.round(cropData.height),
                cropXOffset: Math.round(cropData.x),
                cropYOffset: Math.round(cropData.y)
            };

            // 使用正确的Element Plus消息API
            if (window.ElementPlus && window.ElementPlus.ElMessage) {
                window.ElementPlus.ElMessage.success('裁剪参数已确认');
            } else {
                console.log(`${logPrefix} ✅ 裁剪参数已确认`);
                alert('裁剪参数已确认');
            }

            // 自动关闭裁剪模式
            console.log(`${logPrefix} 自动关闭裁剪模式`);
            destroyCropper(state);
            state.cropDataForBackend.value.isCroppingActive = false;
        }
    };

    return {
        setVideoPlayerRef,
        toggleCropping,
        logCropData
    };
}

/**
 * @功能概述: 初始化Cropper.js实例
 * @参数说明: {object} state - 视频编辑器状态对象
 */
function initializeCropper(state) {
    const logPrefix = '[文件：video-editor.js][initializeCropper]';
    console.log(`${logPrefix} 开始初始化Cropper - 视频帧到Canvas的裁剪逻辑`);

    try {
        // 检查Cropper.js是否可用
        console.log(`${logPrefix} 检查Cropper.js可用性:`, typeof Cropper);
        if (typeof Cropper === 'undefined') {
            console.error(`${logPrefix} Cropper.js未加载`);
            return;
        }

        // 获取视频元素 - 多种方式查找
        console.log(`${logPrefix} 查找视频元素...`);
        let videoElement = state.videoEditorPlayer.value;

        // 如果state中没有，尝试从全局引用获取
        if (!videoElement && window.videoEditorPlayerGlobal) {
            videoElement = window.videoEditorPlayerGlobal;
            console.log(`${logPrefix} 从全局引用获取视频元素`);
        }

        // 如果还是没有，尝试直接从DOM查找
        if (!videoElement) {
            videoElement = document.querySelector('video[ref="videoEditorPlayer"]') || document.querySelector('video');
            console.log(`${logPrefix} 从DOM查找视频元素`);
        }

        console.log(`${logPrefix} 视频元素:`, videoElement);
        console.log(`${logPrefix} 视频元素详情:`, {
            exists: !!videoElement,
            src: videoElement?.src || '未设置',
            videoWidth: videoElement?.videoWidth || 0,
            videoHeight: videoElement?.videoHeight || 0,
            readyState: videoElement?.readyState || 0
        });

        if (!videoElement) {
            console.error(`${logPrefix} 找不到视频元素`);
            console.error(`${logPrefix} 调试信息:`, {
                stateVideoPlayer: !!state.videoEditorPlayer.value,
                globalVideoPlayer: !!window.videoEditorPlayerGlobal,
                domVideoElements: document.querySelectorAll('video').length
            });
            return;
        }

        // 视频控制已在toggleCropping中处理，这里只处理Cropper初始化

        // 创建或获取canvas元素
        let canvas = document.getElementById('cropperCanvas');
        if (!canvas) {
            console.log(`${logPrefix} 创建新的canvas元素`);
            canvas = document.createElement('canvas');
            canvas.id = 'cropperCanvas';
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = 'calc(100% - 40px)'; // 为视频控制条留出空间
            canvas.style.zIndex = '5'; // 降低z-index，不阻挡控制条
            canvas.style.pointerEvents = 'auto'; // 确保可以交互

            // 将canvas插入到视频容器中
            const videoContainer = videoElement.parentElement;
            videoContainer.appendChild(canvas);
        } else {
            console.log(`${logPrefix} 使用现有的canvas元素`);
        }

        // 设置canvas尺寸与视频相同
        canvas.width = videoElement.videoWidth || 1920;
        canvas.height = videoElement.videoHeight || 1080;
        console.log(`${logPrefix} Canvas尺寸设置为: ${canvas.width}x${canvas.height}`);

        // 将视频当前帧绘制到canvas上
        const ctx = canvas.getContext('2d');
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        console.log(`${logPrefix} 视频当前帧已绘制到canvas`);

        // 在画面裁剪模式下，使用Canvas图像切换
        if (state.editorMode.value === 'frame-cropping') {
            console.log(`${logPrefix} 画面裁剪模式：使用Canvas图像切换`);

            // 设置Canvas的高z-index，确保在视频之上
            canvas.style.zIndex = '9999';
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = '100%';

            console.log(`${logPrefix} Canvas z-index设置为9999，确保在视频之上`);

            // 启动Canvas图像切换
            console.log(`${logPrefix} 准备启动Canvas图像切换，图像集合长度:`, state.imageCollection.value.length);
            if (state.imageCollection.value.length > 0) {
                // 直接调用Canvas图像切换函数
                startCanvasImageSwitchingInternal(canvas, state);
            } else {
                console.warn(`${logPrefix} 图像集合为空，无法启动Canvas图像切换`);
            }
        }

        // 非画面裁剪模式，使用原来的Canvas方式
        canvas.style.display = 'block';
        console.log(`${logPrefix} Canvas显示，视频保持可见和可控制`);

        // 在canvas上初始化Cropper
        console.log(`${logPrefix} 在canvas上创建Cropper实例...`);
        state.cropperInstance.value = new Cropper(canvas, {
            aspectRatio: 16 / 9, // 固定16:9比例
            viewMode: 1, // 限制裁剪框在画布内
            autoCropArea: 0.8, // 初始裁剪区域占80%
            responsive: true,
            restore: false,
            guides: true, // 显示网格线
            center: true, // 显示中心指示器
            highlight: false,
            cropBoxMovable: true, // 允许移动裁剪框
            cropBoxResizable: true, // 允许调整裁剪框大小
            toggleDragModeOnDblclick: false,
            background: false, // 不显示网格背景
            modal: true, // 显示模态遮罩
            minCropBoxWidth: 160, // 最小宽度（16:9比例下的最小值）
            minCropBoxHeight: 90, // 最小高度（16:9比例下的最小值）
            ready: function() {
                console.log(`${logPrefix} ✅ Cropper已准备就绪，固定16:9比例，用户可以调整裁剪区域`);
            }
        });

        console.log(`${logPrefix} ✅ Canvas模式Cropper实例初始化成功:`, state.cropperInstance.value);

    } catch (error) {
        console.error(`${logPrefix} ❌ Cropper初始化失败:`, error);
        console.error(`${logPrefix} 错误堆栈:`, error.stack);
    }
}

/**
 * @功能概述: 内部Canvas图像切换函数
 * @参数说明: {HTMLElement} canvas - Canvas元素
 * @参数说明: {object} state - 视频编辑器状态对象
 */
function startCanvasImageSwitchingInternal(canvas, state) {
    const logPrefix = '[文件：video-editor.js][startCanvasImageSwitchingInternal]';
    console.log(`${logPrefix} 启动Canvas图像切换`);

    // 清除现有计时器
    if (state.canvasImageTimer.value) {
        clearInterval(state.canvasImageTimer.value);
        state.canvasImageTimer.value = null;
    }

    if (state.imageCollection.value.length === 0) {
        console.warn(`${logPrefix} 图像集合为空，无法启动切换`);
        return;
    }

    let currentIndex = 0;
    const ctx = canvas.getContext('2d');

    const switchImage = () => {
        const currentImage = state.imageCollection.value[currentIndex];

        if (currentImage) {
            const img = new Image();
            img.onload = () => {
                // 清除Canvas并绘制新图像
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // 减少日志输出频率，避免控制台刷屏
                // console.log(`${logPrefix} 切换到图像: ${currentImage.time}秒 (${currentImage.description})`);
            };
            img.src = currentImage.imageData;

            // 移动到下一张图像
            currentIndex = (currentIndex + 1) % state.imageCollection.value.length;
        }
    };

    // 立即显示第一张图像
    switchImage();

    // 启动定时切换，每0.5秒切换一次
    state.canvasImageTimer.value = setInterval(switchImage, 500);

    console.log(`${logPrefix} ✅ Canvas图像切换已启动，每0.5秒切换一次`);
}

/**
 * @功能概述: 创建自定义裁剪框
 * @参数说明: {object} state - 视频编辑器状态对象
 * @参数说明: {HTMLElement} videoElement - 视频元素
 */
function createCustomCropBox(state, videoElement) {
    const logPrefix = '[文件：video-editor.js][createCustomCropBox]';
    console.log(`${logPrefix} 创建自定义裁剪框`);

    try {
        // 移除现有的裁剪框
        const existingCropBox = document.getElementById('customCropBox');
        if (existingCropBox) {
            existingCropBox.remove();
        }

        // 获取视频容器
        const videoContainer = videoElement.parentElement;
        const videoRect = videoElement.getBoundingClientRect();
        const containerRect = videoContainer.getBoundingClientRect();

        // 创建裁剪框容器
        const cropBox = document.createElement('div');
        cropBox.id = 'customCropBox';
        cropBox.style.position = 'absolute';
        cropBox.style.top = '0';
        cropBox.style.left = '0';
        cropBox.style.width = '100%';
        cropBox.style.height = '100%';
        cropBox.style.pointerEvents = 'none';
        cropBox.style.zIndex = '10';

        // 创建裁剪选择框 - 使用更明显的样式
        const cropSelection = document.createElement('div');
        cropSelection.id = 'cropSelection';
        cropSelection.style.position = 'absolute';
        cropSelection.style.border = '4px solid red'; // 使用红色粗边框，更容易看见
        cropSelection.style.backgroundColor = 'rgba(255, 0, 0, 0.2)'; // 红色半透明背景
        cropSelection.style.cursor = 'move';
        cropSelection.style.pointerEvents = 'auto';
        cropSelection.style.boxSizing = 'border-box';

        // 设置初始尺寸和位置（16:9比例，占视频50%，更容易看见）
        const videoWidth = videoRect.width;
        const videoHeight = videoRect.height;
        const cropWidth = videoWidth * 0.5; // 改为50%，更容易看见
        const cropHeight = cropWidth * (9 / 16); // 保持16:9比例
        const cropLeft = (videoWidth - cropWidth) / 2;
        const cropTop = (videoHeight - cropHeight) / 2;

        cropSelection.style.width = cropWidth + 'px';
        cropSelection.style.height = cropHeight + 'px';
        cropSelection.style.left = cropLeft + 'px';
        cropSelection.style.top = cropTop + 'px';

        console.log(`${logPrefix} 裁剪框尺寸和位置: ${cropWidth}x${cropHeight} at (${cropLeft}, ${cropTop})`);

        // 添加网格线 - 使用更明显的白色线条
        const gridLines = document.createElement('div');
        gridLines.innerHTML = `
            <div style="position: absolute; top: 33.33%; left: 0; right: 0; height: 2px; background: white;"></div>
            <div style="position: absolute; top: 66.66%; left: 0; right: 0; height: 2px; background: white;"></div>
            <div style="position: absolute; left: 33.33%; top: 0; bottom: 0; width: 2px; background: white;"></div>
            <div style="position: absolute; left: 66.66%; top: 0; bottom: 0; width: 2px; background: white;"></div>
        `;
        cropSelection.appendChild(gridLines);

        // 添加控制点 - 使用更大更明显的样式
        const corners = ['nw', 'ne', 'sw', 'se'];
        corners.forEach(corner => {
            const handle = document.createElement('div');
            handle.className = `crop-handle crop-handle-${corner}`;
            handle.style.position = 'absolute';
            handle.style.width = '20px'; // 更大的控制点
            handle.style.height = '20px';
            handle.style.backgroundColor = 'yellow'; // 黄色更明显
            handle.style.border = '3px solid black'; // 黑色边框
            handle.style.borderRadius = '50%'; // 圆形
            handle.style.cursor = corner.includes('n') ? (corner.includes('w') ? 'nw-resize' : 'ne-resize') : (corner.includes('w') ? 'sw-resize' : 'se-resize');

            // 设置控制点位置
            if (corner.includes('n')) handle.style.top = '-10px';
            if (corner.includes('s')) handle.style.bottom = '-10px';
            if (corner.includes('w')) handle.style.left = '-10px';
            if (corner.includes('e')) handle.style.right = '-10px';

            cropSelection.appendChild(handle);
        });

        // 添加拖拽功能
        addDragFunctionality(cropSelection, videoElement, state);

        // 将裁剪框添加到容器
        cropBox.appendChild(cropSelection);
        videoContainer.appendChild(cropBox);

        console.log(`${logPrefix} 裁剪框已添加到DOM`);
        console.log(`${logPrefix} 视频容器:`, videoContainer);
        console.log(`${logPrefix} 裁剪框容器:`, cropBox);
        console.log(`${logPrefix} 裁剪选择框:`, cropSelection);
        console.log(`${logPrefix} 裁剪框样式:`, {
            position: cropSelection.style.position,
            border: cropSelection.style.border,
            backgroundColor: cropSelection.style.backgroundColor,
            width: cropSelection.style.width,
            height: cropSelection.style.height,
            left: cropSelection.style.left,
            top: cropSelection.style.top,
            zIndex: cropBox.style.zIndex
        });

        // 保存裁剪框引用
        state.cropperInstance.value = {
            element: cropSelection,
            getCropData: () => {
                const rect = cropSelection.getBoundingClientRect();
                const videoRect = videoElement.getBoundingClientRect();
                return {
                    x: rect.left - videoRect.left,
                    y: rect.top - videoRect.top,
                    width: rect.width,
                    height: rect.height
                };
            },
            destroy: () => {
                if (cropBox.parentElement) {
                    cropBox.remove();
                }
            }
        };

        console.log(`${logPrefix} ✅ 自定义裁剪框创建完成`);

        // 验证裁剪框是否在DOM中
        setTimeout(() => {
            const checkBox = document.getElementById('customCropBox');
            const checkSelection = document.getElementById('cropSelection');
            console.log(`${logPrefix} 验证DOM - customCropBox存在:`, !!checkBox);
            console.log(`${logPrefix} 验证DOM - cropSelection存在:`, !!checkSelection);
            if (checkSelection) {
                const computedStyle = window.getComputedStyle(checkSelection);
                console.log(`${logPrefix} 计算样式:`, {
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    opacity: computedStyle.opacity,
                    zIndex: computedStyle.zIndex
                });
            }
        }, 100);

    } catch (error) {
        console.error(`${logPrefix} ❌ 自定义裁剪框创建失败:`, error);
    }
}

/**
 * @功能概述: 为裁剪框添加拖拽功能
 */
function addDragFunctionality(cropSelection, videoElement, state) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    cropSelection.addEventListener('mousedown', (e) => {
        if (e.target.classList.contains('crop-handle')) return; // 忽略控制点

        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        startLeft = parseInt(cropSelection.style.left);
        startTop = parseInt(cropSelection.style.top);

        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        const newLeft = startLeft + deltaX;
        const newTop = startTop + deltaY;

        // 限制在视频边界内
        const videoRect = videoElement.getBoundingClientRect();
        const cropRect = cropSelection.getBoundingClientRect();
        const maxLeft = videoRect.width - cropRect.width;
        const maxTop = videoRect.height - cropRect.height;

        cropSelection.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
        cropSelection.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
    });
}

/**
 * @功能概述: 销毁Cropper.js实例
 * @参数说明: {object} state - 视频编辑器状态对象
 */
function destroyCropper(state) {
    const logPrefix = '[文件：video-editor.js][destroyCropper]';
    console.log(`${logPrefix} 开始销毁Cropper并恢复视频显示`);

    try {
        if (state.cropperInstance.value) {
            console.log(`${logPrefix} 销毁Cropper实例`);
            state.cropperInstance.value.destroy();
            state.cropperInstance.value = null;
            console.log(`${logPrefix} ✅ Cropper实例已销毁`);
        }

        // 在画面裁剪模式下，Cropper直接在视频上，无需额外清理
        if (state.editorMode.value === 'frame-cropping') {
            console.log(`${logPrefix} 在画面裁剪模式下，Cropper已销毁，无需额外清理`);
        } else {
            // 非画面裁剪模式下，移除Canvas
            const canvas = document.getElementById('cropperCanvas');
            if (canvas) {
                console.log(`${logPrefix} 移除canvas元素`);
                canvas.remove();
                console.log(`${logPrefix} ✅ Canvas已移除`);
            }

            // 停止Canvas实时更新
            if (segmentFunctions && segmentFunctions.stopCanvasRealTimeUpdate) {
                segmentFunctions.stopCanvasRealTimeUpdate();
            }
        }

        // 视频一直保持显示，无需恢复
        console.log(`${logPrefix} ✅ 视频一直保持可见和可控制状态`);

        // 只设置裁剪为非活跃状态，保持其他裁剪数据不变
        state.cropDataForBackend.value.isCroppingActive = false;
        console.log(`${logPrefix} ✅ 裁剪状态设置为非活跃，保持裁剪数据不变`);

    } catch (error) {
        console.error(`${logPrefix} ❌ Cropper销毁失败:`, error);
        console.error(`${logPrefix} 错误堆栈:`, error.stack);
    }
}

// ============================================================================
// 7. 字幕显示模块
// ============================================================================

/**
 * @功能概述: 更新字幕显示
 * @参数说明: {number} currentTime - 当前播放时间
 * @调用关系: 由handleTimeUpdate调用
 * @状态影响: 更新currentSubtitles状态
 */
function updateSubtitleDisplay(currentTime, state) {
    const logPrefix = '[文件：video-editor.js][updateSubtitleDisplay]';

    // 减少调试日志输出，只在必要时输出
    if (!state) {
        console.error(`${logPrefix} ❌ state对象未定义`);
        return;
    }

    if (!state.currentSubtitles) {
        console.error(`${logPrefix} ❌ currentSubtitles状态未定义`);
        return;
    }

    try {
        let currentEnglish = '';
        
        // 添加调试信息
        const subtitlesCount = state.englishSubtitles && state.englishSubtitles.value ? state.englishSubtitles.value.length : 0;
        
        // 每10秒输出一次调试信息，避免控制台刷屏
        if (Math.floor(currentTime) % 10 === 0 && Math.floor(currentTime * 10) % 10 === 0) {
            console.log(`${logPrefix} 调试信息 - 当前时间: ${currentTime}秒, 字幕总数: ${subtitlesCount}`);
            if (subtitlesCount > 0) {
                console.log(`${logPrefix} 字幕数组示例:`, state.englishSubtitles.value.slice(0, 2));
            }
        }
        
        if (state.englishSubtitles && state.englishSubtitles.value && Array.isArray(state.englishSubtitles.value)) {
            const englishSubtitle = state.englishSubtitles.value.find(subtitle => {
                return currentTime >= subtitle.start && currentTime <= subtitle.end;
            });

            if (englishSubtitle) {
                currentEnglish = englishSubtitle.text;
                // 当找到字幕时输出调试信息
                if (state.currentSubtitles.value !== currentEnglish) {
                    console.log(`${logPrefix} 找到字幕: "${currentEnglish}" (时间: ${currentTime}秒, 字幕时间: ${englishSubtitle.start}-${englishSubtitle.end})`);
                }
            }
        } else {
            // 如果字幕数组不存在或为空，输出调试信息
            if (subtitlesCount === 0 && Math.floor(currentTime) % 10 === 0 && Math.floor(currentTime * 10) % 10 === 0) {
                console.warn(`${logPrefix} 字幕数组为空或未定义`);
            }
        }

        // 更新字幕显示状态
        state.currentSubtitles.value = currentEnglish;

    } catch (error) {
        console.error(`${logPrefix} ❌ 字幕更新失败:`, error);
        console.error(`${logPrefix} 错误堆栈:`, error.stack);
    }
}

// 将所有创建函数和状态导出到全局
window.VideoEditor = {
    createVideoEditorState,
    createVideoEditorComputed,
    createVideoPlaybackFunctions,
    createSegmentFunctions,
    createModeSwitchFunctions,
    createCroppingFunctions,
    updateSubtitleDisplay,  // 添加字幕更新函数
    initialize: () => {
         // 可以在这里添加一些初始化逻辑
        console.log('[文件：video-editor.js] VideoEditor模块已初始化。');
    }
};

console.log('[文件：video-editor.js][模块加载] VideoEditor模块已挂载到 window.VideoEditor');
