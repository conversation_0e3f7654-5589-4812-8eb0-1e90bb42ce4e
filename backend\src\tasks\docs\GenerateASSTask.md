# GenerateASSTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **audioFilePath** (string): 音频文件路径，来自ConvertToAudioTask
- **clozedSubtitleJsonArray** (Array): 挖空字幕数组，来自SubtitleClozeTask
  - 结构: `[{id, start, end, text, words}, ...]`
- **enhancedBilingualSubtitleJsonArray** (Array): 增强双语字幕数组，来自BilingualSubtitleMergeTask
  - 结构: `[{id, start, end, text_english, text_chinese, words_explanation}, ...]`
- **savePath** (string): 文件保存路径

### 可选参数
- **videoConfig** (object): 视频配置对象，包含样式和布局设置
- **repeatCount** (number): 重复播放次数，默认从配置获取

## 2. 输出上下文参数 (Output Context)

- **assFilePath** (string): 生成的ASS字幕文件完整路径
- **assContent** (string): 完整的ASS字幕内容（供后续任务直接使用，无需读取文件）
- **assContentLength** (number): ASS内容长度（字符数）
- **videoConfig** (object): 完整的视频配置对象
- **audioDuration** (number): 音频时长（秒）
- **processedSubtitles** (object): 处理统计信息
  - **clozedCount** (number): 填空字幕条数
  - **bilingualCount** (number): 双语字幕条数
  - **videoTitleCount** (number): 视频标题字幕条数
  - **repeatModeGuideCount** (number): 单元引导字幕条数

## 3. 重要数据格式

### ASS文件结构
```
[Script Info]
Title: Generated ASS Subtitle
PlayResX: 1080
PlayResY: 1920

[V4+ Styles]
Style: VideoTitle,Arial,48,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Dialogue: 0,0:00:00.00,0:00:05.00,VideoTitle,,0,0,0,,视频标题内容
Dialogue: 1,0:00:05.00,0:00:10.00,BilingualEnglish,,0,0,0,,Hello world
Dialogue: 2,0:00:05.00,0:00:10.00,BilingualChinese,,0,0,0,,你好世界
```

### 关键词翻译格式
```
{\1c&H00FFFF00}keyword{\fs30} [翻译]{\r}
```

### 双语字幕数据格式
```json
{
  "id": 1,
  "start": 0.0,
  "end": 5.0,
  "text_english": "Hello world",
  "text_chinese": "你好世界",
  "words_explanation": {
    "world": "世界，全球"
  }
}
```

## 4. 文件操作

### 保存的文件格式
- **.ass**: ASS格式字幕文件（Advanced SubStation Alpha）

### 文件命名规则
- **模式**: `{videoIdentifier}_extended_ass.ass`
- **示例**: `video123_extended_ass.ass`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 使用UTF-8编码保存，确保中文字符正确显示
- 同时将ASS内容保存到context中供后续任务使用

## 5. 执行逻辑概述

ASS字幕文件生成任务负责创建完整的ASS格式字幕文件，包含视频标题、填空字幕、双语字幕和单元引导字幕等多种类型。任务首先验证输入参数并加载视频配置，然后使用CSS到ASS映射工具转换样式定义。接下来获取音频时长信息，为字幕时间轴提供基础数据。核心处理包括关键词翻译功能，支持words_explanation对象的处理，生成带有翻译注释的特效标签。最后生成完整的ASS文件内容，包含脚本信息、样式定义和事件列表，并同时保存到文件和上下文中，为后续的视频合成任务提供字幕数据。
