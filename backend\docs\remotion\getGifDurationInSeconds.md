# getGifDurationInSeconds()

## 概述

`getGifDurationInSeconds()` 函数用于获取 GIF 动画的持续时间（以秒为单位）。这是 `@remotion/gif` 包的一部分。

**版本要求**: v3.2.22+

**注意**: 远程 GIF 需要支持 [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)。

## 语法

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";

const duration = await getGifDurationInSeconds(src);
```

## 参数

### src
- **类型**: `string`
- **描述**: 指向 GIF 资源的字符串路径或 URL

## 返回值

- **类型**: `Promise<number>`
- **描述**: GIF 的持续时间（秒），不考虑是否循环播放

## 基础用法

### 1. 本地 GIF 文件

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile } from "remotion";
import gif from "./cat.gif";

const MyComp: React.FC = () => {
  const getDuration = useCallback(async () => {
    // 导入的 GIF 文件
    const importedDuration = await getGifDurationInSeconds(gif); // 127.452
    console.log(`导入的 GIF 时长: ${importedDuration} 秒`);
    
    // public 文件夹中的 GIF
    const publicDuration = await getGifDurationInSeconds(staticFile('giphy.gif')); // 2.10
    console.log(`公共 GIF 时长: ${publicDuration} 秒`);
  }, []);

  useEffect(() => {
    getDuration();
  }, []);

  return null;
};
```

### 2. 远程 GIF 文件

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";

const RemoteGifDuration = () => {
  const [duration, setDuration] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  const checkRemoteGif = async () => {
    setLoading(true);
    try {
      const remoteDuration = await getGifDurationInSeconds(
        'https://media.giphy.com/media/xT0GqH01ZyKwd3aT3G/giphy.gif'
      ); // 3.23
      setDuration(remoteDuration);
    } catch (error) {
      console.error("获取远程 GIF 时长失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkRemoteGif();
  }, []);

  return (
    <div>
      {loading ? (
        <p>正在获取 GIF 时长...</p>
      ) : duration ? (
        <p>GIF 时长: {duration.toFixed(2)} 秒</p>
      ) : (
        <p>无法获取 GIF 时长</p>
      )}
    </div>
  );
};
```

## 实际应用场景

### 1. 动态组合时长计算

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile } from "remotion";

const DynamicComposition = () => {
  const [totalDuration, setTotalDuration] = useState(0);
  const [gifDurations, setGifDurations] = useState<number[]>([]);

  const calculateTotalDuration = async () => {
    const gifFiles = [
      staticFile("intro.gif"),
      staticFile("main-content.gif"),
      staticFile("outro.gif")
    ];

    const durations = await Promise.all(
      gifFiles.map(file => getGifDurationInSeconds(file))
    );

    setGifDurations(durations);
    setTotalDuration(durations.reduce((sum, duration) => sum + duration, 0));
  };

  useEffect(() => {
    calculateTotalDuration();
  }, []);

  return (
    <div>
      <h3>GIF 序列时长分析</h3>
      {gifDurations.map((duration, index) => (
        <p key={index}>
          GIF {index + 1}: {duration.toFixed(2)} 秒
        </p>
      ))}
      <p><strong>总时长: {totalDuration.toFixed(2)} 秒</strong></p>
    </div>
  );
};
```

### 2. 自适应视频时长

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile, useVideoConfig } from "remotion";

const AdaptiveVideoLength = () => {
  const { fps } = useVideoConfig();
  const [recommendedFrames, setRecommendedFrames] = useState(0);

  const calculateOptimalLength = async () => {
    const mainGifDuration = await getGifDurationInSeconds(staticFile("main-animation.gif"));
    
    // 建议视频长度为 GIF 时长的整数倍，确保完整循环
    const cycles = Math.ceil(mainGifDuration * fps / 60) || 1; // 至少1个循环
    const optimalFrames = Math.ceil(mainGifDuration * fps * cycles);
    
    setRecommendedFrames(optimalFrames);
  };

  useEffect(() => {
    calculateOptimalLength();
  }, [fps]);

  return (
    <div>
      <p>建议视频长度: {recommendedFrames} 帧</p>
      <p>约 {(recommendedFrames / fps).toFixed(2)} 秒</p>
    </div>
  );
};
```

### 3. GIF 同步控制

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { Gif } from "@remotion/gif";
import { staticFile, useCurrentFrame, useVideoConfig } from "remotion";

const SynchronizedGifs = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const [gifDuration, setGifDuration] = useState<number | null>(null);

  useEffect(() => {
    const loadGifDuration = async () => {
      const duration = await getGifDurationInSeconds(staticFile("sync-animation.gif"));
      setGifDuration(duration);
    };
    loadGifDuration();
  }, []);

  // 计算当前应该显示的 GIF 循环
  const currentTime = frame / fps;
  const cycleNumber = gifDuration ? Math.floor(currentTime / gifDuration) : 0;
  const isEvenCycle = cycleNumber % 2 === 0;

  return (
    <div style={{ display: "flex", gap: 20 }}>
      {/* 正常播放的 GIF */}
      <Gif
        src={staticFile("sync-animation.gif")}
        width={200}
        height={200}
        fit="contain"
        playbackRate={1}
      />
      
      {/* 反向播放的 GIF (通过调整播放速度模拟) */}
      <Gif
        src={staticFile("sync-animation.gif")}
        width={200}
        height={200}
        fit="contain"
        playbackRate={isEvenCycle ? 1 : -1} // 注意: 负值可能不被支持
        style={{
          transform: isEvenCycle ? "none" : "scaleX(-1)" // 水平翻转模拟反向
        }}
      />
    </div>
  );
};
```

### 4. 性能优化的预加载

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

const OptimizedGifLoader = () => {
  const [gifInfo, setGifInfo] = useState<{
    duration: number;
    preloaded: boolean;
  } | null>(null);

  const optimizedLoad = async () => {
    const gifSrc = staticFile("large-animation.gif");
    
    // 首先获取时长信息
    const duration = await getGifDurationInSeconds(gifSrc);
    
    // 如果 GIF 时长合理，则预加载
    if (duration > 0 && duration < 10) { // 只预加载10秒以内的GIF
      const { waitUntilDone, free } = preloadGif(gifSrc);
      
      setGifInfo({ duration, preloaded: false });
      
      await waitUntilDone();
      setGifInfo({ duration, preloaded: true });
      
      // 在组件卸载时清理
      return () => free();
    } else {
      setGifInfo({ duration, preloaded: false });
    }
  };

  useEffect(() => {
    optimizedLoad();
  }, []);

  return (
    <div>
      {gifInfo ? (
        <div>
          <p>GIF 时长: {gifInfo.duration.toFixed(2)} 秒</p>
          <p>预加载状态: {gifInfo.preloaded ? "已完成" : "未预加载"}</p>
          {gifInfo.preloaded && (
            <p style={{ color: "green" }}>✅ GIF 已准备就绪，播放将更流畅</p>
          )}
        </div>
      ) : (
        <p>正在分析 GIF...</p>
      )}
    </div>
  );
};
```

### 5. 批量 GIF 分析

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile } from "remotion";

interface GifAnalysis {
  filename: string;
  duration: number;
  status: "success" | "error";
  error?: string;
}

const BatchGifAnalyzer = () => {
  const [analyses, setAnalyses] = useState<GifAnalysis[]>([]);
  const [loading, setLoading] = useState(false);

  const analyzeGifs = async () => {
    setLoading(true);
    const gifFiles = [
      "animation1.gif",
      "animation2.gif", 
      "animation3.gif",
      "background.gif",
      "effects.gif"
    ];

    const results: GifAnalysis[] = [];

    for (const filename of gifFiles) {
      try {
        const duration = await getGifDurationInSeconds(staticFile(filename));
        results.push({
          filename,
          duration,
          status: "success"
        });
      } catch (error) {
        results.push({
          filename,
          duration: 0,
          status: "error",
          error: error instanceof Error ? error.message : "未知错误"
        });
      }
    }

    setAnalyses(results);
    setLoading(false);
  };

  const totalDuration = analyses
    .filter(a => a.status === "success")
    .reduce((sum, a) => sum + a.duration, 0);

  return (
    <div>
      <button onClick={analyzeGifs} disabled={loading}>
        {loading ? "分析中..." : "分析所有 GIF"}
      </button>
      
      {analyses.length > 0 && (
        <div style={{ marginTop: 20 }}>
          <h3>GIF 分析结果</h3>
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead>
              <tr>
                <th style={{ border: "1px solid #ccc", padding: 8 }}>文件名</th>
                <th style={{ border: "1px solid #ccc", padding: 8 }}>时长(秒)</th>
                <th style={{ border: "1px solid #ccc", padding: 8 }}>状态</th>
              </tr>
            </thead>
            <tbody>
              {analyses.map((analysis, index) => (
                <tr key={index}>
                  <td style={{ border: "1px solid #ccc", padding: 8 }}>
                    {analysis.filename}
                  </td>
                  <td style={{ border: "1px solid #ccc", padding: 8 }}>
                    {analysis.status === "success" ? analysis.duration.toFixed(2) : "-"}
                  </td>
                  <td style={{ border: "1px solid #ccc", padding: 8 }}>
                    {analysis.status === "success" ? (
                      <span style={{ color: "green" }}>✅ 成功</span>
                    ) : (
                      <span style={{ color: "red" }}>❌ {analysis.error}</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <div style={{ marginTop: 10 }}>
            <strong>总时长: {totalDuration.toFixed(2)} 秒</strong>
          </div>
        </div>
      )}
    </div>
  );
};
```

### 6. 智能循环计算

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile, useVideoConfig } from "remotion";

const SmartLoopCalculator = () => {
  const { durationInFrames, fps } = useVideoConfig();
  const [loopInfo, setLoopInfo] = useState<{
    gifDuration: number;
    videoDuration: number;
    completeCycles: number;
    remainingTime: number;
    recommendation: string;
  } | null>(null);

  const calculateLoops = async () => {
    const gifDuration = await getGifDurationInSeconds(staticFile("loop-animation.gif"));
    const videoDuration = durationInFrames / fps;
    
    const completeCycles = Math.floor(videoDuration / gifDuration);
    const remainingTime = videoDuration % gifDuration;
    
    let recommendation = "";
    if (remainingTime < gifDuration * 0.1) {
      recommendation = "视频时长与 GIF 循环完美匹配";
    } else if (remainingTime > gifDuration * 0.9) {
      recommendation = "建议延长视频时长以完成最后一个循环";
    } else {
      recommendation = "建议调整视频时长以获得更好的循环效果";
    }

    setLoopInfo({
      gifDuration,
      videoDuration,
      completeCycles,
      remainingTime,
      recommendation
    });
  };

  useEffect(() => {
    calculateLoops();
  }, [durationInFrames, fps]);

  return (
    <div>
      {loopInfo && (
        <div>
          <h3>循环分析</h3>
          <p>GIF 时长: {loopInfo.gifDuration.toFixed(2)} 秒</p>
          <p>视频时长: {loopInfo.videoDuration.toFixed(2)} 秒</p>
          <p>完整循环次数: {loopInfo.completeCycles}</p>
          <p>剩余时间: {loopInfo.remainingTime.toFixed(2)} 秒</p>
          <p style={{ 
            fontWeight: "bold",
            color: loopInfo.remainingTime < loopInfo.gifDuration * 0.1 ? "green" : "orange"
          }}>
            建议: {loopInfo.recommendation}
          </p>
        </div>
      )}
    </div>
  );
};
```

## 错误处理

```typescript
import { getGifDurationInSeconds } from "@remotion/gif";

const ErrorHandledDuration = () => {
  const [result, setResult] = useState<{
    duration?: number;
    error?: string;
  }>({});

  const safeDurationCheck = async (src: string) => {
    try {
      const duration = await getGifDurationInSeconds(src);
      setResult({ duration });
    } catch (error) {
      console.error("获取 GIF 时长失败:", error);
      setResult({ 
        error: error instanceof Error ? error.message : "未知错误" 
      });
    }
  };

  return (
    <div>
      <button onClick={() => safeDurationCheck(staticFile("test.gif"))}>
        检查 GIF 时长
      </button>
      
      {result.duration && (
        <p>时长: {result.duration.toFixed(2)} 秒</p>
      )}
      
      {result.error && (
        <p style={{ color: "red" }}>错误: {result.error}</p>
      )}
    </div>
  );
};
```

## 最佳实践

1. **错误处理**: 总是使用 try-catch 包装异步调用
2. **性能考虑**: 避免重复调用相同 GIF 的时长检查
3. **缓存结果**: 对于静态 GIF，可以缓存时长结果
4. **CORS 配置**: 确保远程 GIF 支持跨域访问
5. **超时处理**: 对于网络 GIF，考虑添加超时机制

## 相关 API

- [`<Gif>`](./Gif.md) - GIF 显示组件
- [`preloadGif()`](./preloadGif.md) - GIF 预加载
- [`staticFile()`](./staticFile.md) - 静态文件引用

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/gif/src/get-gif-duration-in-seconds.ts)
