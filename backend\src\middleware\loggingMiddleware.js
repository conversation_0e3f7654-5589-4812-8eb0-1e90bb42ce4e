/**
 * @功能概述: 日志记录中间件，用于记录API请求和响应的详细信息
 *           特别针对视频生成API的完整日志记录
 */

const logger = require('../utils/logger');

/**
 * @功能概述: 创建日志记录中间件
 * @参数说明: 
 *   - logLevel: 日志级别 ('info', 'debug', 'warn', 'error')
 *   - includeBody: 是否记录请求体内容
 *   - includeHeaders: 是否记录请求头信息
 * @返回值: Express中间件函数
 */
function createLoggingMiddleware(options = {}) {
    const {
        logLevel = 'info',
        includeBody = true,
        includeHeaders = false,
        maxBodyLength = 1000
    } = options;

    return (req, res, next) => {
        const startTime = Date.now();
        const reqId = req.id || 'unknown';
        const logPrefix = `[文件：loggingMiddleware.js][日志中间件][ReqID:${reqId}]`;

        // 记录请求开始
        logger[logLevel](`${logPrefix}[REQUEST_START] ${req.method} ${req.originalUrl}`);
        
        // 记录请求头（如果启用）
        if (includeHeaders) {
            logger.debug(`${logPrefix}[REQUEST_HEADERS] ${JSON.stringify(req.headers, null, 2)}`);
        }

        // 记录请求体（如果启用且存在）
        if (includeBody && req.body && Object.keys(req.body).length > 0) {
            const bodyStr = JSON.stringify(req.body, null, 2);
            const truncatedBody = bodyStr.length > maxBodyLength 
                ? bodyStr.substring(0, maxBodyLength) + '...[truncated]'
                : bodyStr;
            logger[logLevel](`${logPrefix}[REQUEST_BODY] ${truncatedBody}`);
        }

        // 记录查询参数（如果存在）
        if (req.query && Object.keys(req.query).length > 0) {
            logger[logLevel](`${logPrefix}[REQUEST_QUERY] ${JSON.stringify(req.query)}`);
        }

        // 记录文件上传信息（如果存在）
        if (req.file) {
            logger[logLevel](`${logPrefix}[REQUEST_FILE] ${JSON.stringify({
                fieldname: req.file.fieldname,
                originalname: req.file.originalname,
                mimetype: req.file.mimetype,
                size: req.file.size,
                filename: req.file.filename
            })}`);
        }

        // 拦截响应结束事件
        const originalEnd = res.end;
        res.end = function(chunk, encoding) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            // 记录响应信息
            logger[logLevel](`${logPrefix}[RESPONSE_END] Status: ${res.statusCode}, Duration: ${duration}ms`);

            // 如果是错误响应，记录更多信息
            if (res.statusCode >= 400) {
                logger.warn(`${logPrefix}[RESPONSE_ERROR] Status: ${res.statusCode}, Duration: ${duration}ms`);
            }

            // 调用原始的end方法
            originalEnd.call(this, chunk, encoding);
        };

        // 继续到下一个中间件
        next();
    };
}

/**
 * @功能概述: 专门用于视频生成API的日志中间件
 * @说明: 包含详细的请求体记录，适用于调试和监控
 */
const videoGenerationLoggingMiddleware = createLoggingMiddleware({
    logLevel: 'info',
    includeBody: true,
    includeHeaders: false,
    maxBodyLength: 2000 // 视频生成API可能有较大的请求体
});

/**
 * @功能概述: 通用API日志中间件
 * @说明: 基础日志记录，适用于大多数API
 */
const generalLoggingMiddleware = createLoggingMiddleware({
    logLevel: 'info',
    includeBody: false,
    includeHeaders: false
});

/**
 * @功能概述: 调试模式日志中间件
 * @说明: 包含所有详细信息，用于开发和调试
 */
const debugLoggingMiddleware = createLoggingMiddleware({
    logLevel: 'debug',
    includeBody: true,
    includeHeaders: true,
    maxBodyLength: 5000
});

module.exports = {
    createLoggingMiddleware,
    videoGenerationLoggingMiddleware,
    generalLoggingMiddleware,
    debugLoggingMiddleware
};
