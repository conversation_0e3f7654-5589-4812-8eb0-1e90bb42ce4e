/**
 * @功能概述: 视频上传控制器 - 专门处理视频文件上传请求
 * @职责范围: 
 *   - 接收multer处理后的视频文件
 *   - 通过标准化SSE推送处理进度
 *   - 调用视频处理流水线执行转换
 *   - 返回处理结果和文件URL
 * 
 * @API接口: POST /api/video/upload
 * @请求格式: multipart/form-data (视频文件)
 * @响应格式: SSE事件流
 * 
 * @架构设计: 单一职责原则 - 只处理视频上传相关逻辑
 * @创建时间: 2025-06-12
 * @重构说明: 从videoController.js中拆分出来，提高可维护性
 */

// 导入日志工具
const logger = require('../../utils/logger');
const path = require('path');

// 导入标准化SSE基础设施
const {
    SSE_EVENT_TYPES,
    CONTROLLER_STATUS,
    PIPELINE_STATUS,
    createSSEEventData,
    createControllerStatusSSE,
    createHeartbeatSSE,
    SSEConnectionManager
} = require('../../constants/progress');

// 导入视频处理流水线服务
const VideoProcessingPipelineService = require('../../pipelines/videoProcessingPipelineService');

// 导入路径管理工具
const PathHelper = require('../../utils/pathHelper');

// 导入文件名处理工具
const { sanitizeFileName } = require('../../utils/fileNameUtils');

// 模块级日志前缀
const moduleLogPrefix = '[文件：uploadVideoController.js][视频上传控制器][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用上传控制器，遵循单一职责原则`);

/**
 * @功能概述: 处理视频文件上传请求的核心方法
 * @参数说明:
 *   - req: Express请求对象，包含multer处理后的文件信息(req.file)
 *   - res: Express响应对象，用于SSE事件推送
 * 
 * @执行流程:
 *   1. 初始化SSE连接和心跳机制
 *   2. 验证上传文件的有效性
 *   3. 提取视频标识符和路径信息
 *   4. 发送请求接受确认事件
 *   5. 创建并执行视频处理流水线
 *   6. 处理流水线结果并转换为Web URL
 *   7. 发送最终结果事件并清理连接
 * 
 * @错误处理:
 *   - 文件缺失：发送预检失败事件
 *   - 流水线异常：发送控制器错误事件
 *   - 连接异常：自动清理资源
 * 
 * @SSE事件类型:
 *   - ACCEPTED: 请求接受确认
 *   - PIPELINE_PROGRESS: 流水线进度更新
 *   - CONTROLLER_ERROR: 控制器级别错误
 *   - HEARTBEAT: 连接保活心跳
 */
const uploadVideo = async (req, res) => {
    // === 步骤1: 初始化请求上下文和日志 ===
    const reqId = req.id || 'unknown_upload_req';
    const connectionId = `video-upload-${reqId}-${Date.now()}`;
    const logPrefix = `[文件：uploadVideoController.js][uploadVideo][ReqID:${reqId}][ConnID:${connectionId}] `;
    
    logger.info(`${logPrefix}[步骤 1] 开始处理视频上传请求 (标准化SSE)。`);

    // === 步骤2: 初始化SSE连接管理器和心跳机制 ===
    let sseManager = null;
    let videoIdentifier = 'unknown-video';
    let heartbeatInterval = null;

    try {
        // 创建SSE连接管理器实例
        sseManager = new SSEConnectionManager(res, connectionId);
        
        // 初始化SSE连接（设置响应头并发送连接确认事件）
        sseManager.initialize();
        logger.info(`${logPrefix}[步骤 2.1] SSE连接管理器已初始化。连接ID: ${connectionId}`);

        // 启动心跳机制（每15秒发送一次）
        // 心跳的作用：
        // 1. 维持连接活跃，防止代理服务器关闭空闲连接
        // 2. 检测客户端存活状态，及时清理死连接
        // 3. 避免TCP/HTTP超时，确保长连接稳定性
        heartbeatInterval = setInterval(() => {
            if (sseManager && sseManager.isActive) {
                const heartbeat = createHeartbeatSSE({
                    connectionId: connectionId,
                    uptime: Date.now() - sseManager.startTime
                });
                sseManager.sendEvent(heartbeat);
            } else {
                // SSE管理器不再活跃时停止心跳
                clearInterval(heartbeatInterval);
            }
        }, 15000);

        // 处理客户端主动断开连接的情况
        let clientDisconnected = false;
        req.on('close', () => {
            clientDisconnected = true;
            logger.warn(`${logPrefix}[SSE_CLOSE] 客户端连接已关闭。ReqID: ${reqId}`);
            logger.warn(`${logPrefix}[SSE_CLOSE] 连接关闭时SSE管理器状态: ${sseManager ? (sseManager.isActive ? 'active' : 'inactive') : 'null'}`);

            // 不要立即关闭SSE连接，让流水线完成后再处理
            // 只标记客户端已断开，避免后续发送事件
            if (sseManager && sseManager.isActive) {
                logger.warn(`${logPrefix}[SSE_CLOSE] 客户端断开但保持SSE管理器活跃，等待流水线完成`);
            }
        });

        // === 步骤3: 验证上传文件 ===
        if (!req.file) {
            logger.warn(`${logPrefix}[步骤 3][WARN] 未找到上传文件。`);
            
            // 使用标准化工厂函数创建预检失败事件
            const preflightFailedEvent = createControllerStatusSSE(
                CONTROLLER_STATUS.FAILED_PREFLIGHT,
                {
                    message: '未找到上传的视频文件',
                    errorDetails: {
                        message: 'No video file uploaded.',
                        code: 'FILE_MISSING'
                    }
                }
            );
            
            sseManager.sendEvent(preflightFailedEvent);
            sseManager.close();
            return;
        }
        
        // === 步骤4: 提取文件路径和视频标识符 ===
        // req.file.path 是multer生成的完整文件路径（包含文件名）
        const uploadedVideoFullPath = req.file.path;

        logger.info(`${logPrefix}[步骤 4.1] 接收到文件信息:`);
        logger.info(`${logPrefix}  - 完整路径: ${uploadedVideoFullPath}`);
        logger.info(`${logPrefix}  - 原始文件名: ${req.file.originalname}`);
        logger.info(`${logPrefix}  - 服务器文件名: ${req.file.filename}`);

        // === 步骤5: 提取视频标识符 ===
        // 从multer生成的文件名中提取标识符（去除扩展名）
        if (req.file.filename) {
            videoIdentifier = path.parse(req.file.filename).name;
            logger.info(`${logPrefix}[步骤 5.1] 从multer文件名提取的videoIdentifier: ${videoIdentifier}`);
        } else {
            // 备用方案：生成临时标识符
            videoIdentifier = `videoFile-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
            logger.warn(`${logPrefix}[步骤 5.2][WARN] req.file.filename未定义，已生成临时videoIdentifier: ${videoIdentifier}`);
        }

        // === 步骤6: 创建项目目录结构 ===
        logger.info(`${logPrefix}[步骤 6] 开始创建项目目录结构`);
        logger.info(`${logPrefix}[步骤 6.1] 项目标识符: ${videoIdentifier}`);

        const { sourceDir, processedDir, generatedDir } = PathHelper.createProjectDirs(videoIdentifier);

        logger.info(`${logPrefix}[步骤 6.2] 项目目录结构创建完成:`);
        logger.info(`${logPrefix}  - 原始文件目录: ${sourceDir}`);
        logger.info(`${logPrefix}  - 处理文件目录: ${processedDir}`);
        logger.info(`${logPrefix}  - 生成文件目录: ${generatedDir}`);

        // === 步骤7: 移动原始文件到source目录 ===
        logger.info(`${logPrefix}[步骤 7] 开始移动原始文件到source目录`);

        // 步骤7.1: 处理原始文件名
        logger.info(`${logPrefix}[步骤 7.1] 开始处理原始文件名`);
        const originalName = req.file.originalname;
        logger.info(`${logPrefix}[步骤 7.1.1] 用户上传的原始文件名: ${originalName}`);

        // 使用文件名清理工具处理文件名
        const sanitizeResult = sanitizeFileName(originalName, {
            maxLength: 100,
            preserveExtension: true,
            fallbackName: 'original'
        });

        logger.info(`${logPrefix}[步骤 7.1.2] 文件名处理结果:`);
        logger.info(`${logPrefix}  - 处理成功: ${sanitizeResult.success}`);
        logger.info(`${logPrefix}  - 原始文件名: ${sanitizeResult.originalName}`);
        logger.info(`${logPrefix}  - 清理后文件名: ${sanitizeResult.sanitizedName}`);
        logger.info(`${logPrefix}  - 是否修改: ${sanitizeResult.modified}`);
        logger.info(`${logPrefix}  - 修改原因: ${sanitizeResult.reason}`);

        // 确定最终使用的文件名
        let finalFileName;
        if (sanitizeResult.success) {
            finalFileName = sanitizeResult.sanitizedName;
            logger.info(`${logPrefix}[步骤 7.1.3] 使用清理后的文件名: ${finalFileName}`);
        } else {
            // 回退到原有的命名方式
            finalFileName = 'original' + path.extname(originalName);
            logger.warn(`${logPrefix}[步骤 7.1.3][WARN] 文件名处理失败，回退到系统命名: ${finalFileName}`);
            logger.warn(`${logPrefix}[步骤 7.1.3][WARN] 失败原因: ${sanitizeResult.reason}`);
        }

        const finalVideoPath = path.join(sourceDir, finalFileName);

        logger.info(`${logPrefix}[步骤 7.2] 源文件路径: ${uploadedVideoFullPath}`);
        logger.info(`${logPrefix}[步骤 7.3] 目标文件路径: ${finalVideoPath}`);
        logger.info(`${logPrefix}[步骤 7.4] 最终文件名: ${finalFileName}`);

        try {
            require('fs').renameSync(uploadedVideoFullPath, finalVideoPath);
            logger.info(`${logPrefix}[步骤 7.4] ✅ 原始文件移动成功: ${finalVideoPath}`);

            // 验证文件是否真的存在
            const fileExists = require('fs').existsSync(finalVideoPath);
            logger.info(`${logPrefix}[步骤 7.5] 文件存在性验证: ${fileExists}`);

            if (!fileExists) {
                throw new Error('文件移动后验证失败，目标文件不存在');
            }
        } catch (error) {
            logger.error(`${logPrefix}[步骤 7][ERROR] 移动文件失败: ${error.message}`);
            logger.error(`${logPrefix}[步骤 7][ERROR] 错误详情: ${error.stack}`);
            throw new Error(`移动原始文件失败: ${error.message}`);
        }

        // 设置流水线保存路径为processed目录
        const savePath = processedDir;
        logger.info(`${logPrefix}[步骤 8] 流水线配置:`);
        logger.info(`${logPrefix}[步骤 8.1] 保存路径设置为: ${savePath}`);
        logger.info(`${logPrefix}[步骤 8.2] 原始视频路径: ${finalVideoPath}`);
        logger.info(`${logPrefix}[步骤 8.3] 新架构模式: 启用`);

        // === 步骤6: 发送请求接受确认事件 ===
        const acceptedEvent = createControllerStatusSSE(
            CONTROLLER_STATUS.ACCEPTED,
            {
                message: `视频 '${req.file.originalname}' 已接收，准备进入处理流水线`,
                videoIdentifier: videoIdentifier,
                fileInfo: {
                    originalName: req.file.originalname,
                    size: req.file.size,
                    mimetype: req.file.mimetype
                }
            }
        );
        
        sseManager.sendEvent(acceptedEvent);
        logger.info(`${logPrefix}[步骤 6] 已发送请求接受确认事件`);

        // === 步骤7: 创建视频处理流水线服务 ===
        logger.info(`${logPrefix}[步骤 7] 创建VideoProcessingPipelineService实例。`);
        const pipelineService = new VideoProcessingPipelineService(reqId);

        // === 步骤8: 定义标准化进度回调函数 ===
        const progressCallbackFromController = (progressData) => {
            try {
                logger.info(`${logPrefix}[PROGRESS_CALLBACK] 收到流水线进度: Pipeline '${progressData.pipelineName}', Status '${progressData.pipelineStatus}'`);
                
                const progressEvent = createSSEEventData(
                    SSE_EVENT_TYPES.PIPELINE_PROGRESS,
                    progressData
                );
                
                // 确保连接仍然活跃
                if (sseManager && sseManager.isActive) {
                    const sent = sseManager.sendEvent(progressEvent);
                    if (!sent) {
                        logger.warn(`${logPrefix}[PROGRESS_CALLBACK] SSE事件发送失败`);
                    }
                }
            } catch (error) {
                logger.error(`${logPrefix}[PROGRESS_CALLBACK] 进度回调处理异常: ${error.message}`);
            }
        };

        // === 步骤9: 执行视频处理流水线 ===
        logger.info(`${logPrefix}[步骤 9] 开始执行视频处理流水线`);

        // 定义视频处理流水线的上下文参数
        const context = {
            reqId,                          // 请求的唯一标识符
            videoIdentifier,                // 视频的唯一标识符
            uploadedVideoDirPath: sourceDir, // 原始文件存储目录路径
            savePath,                       // 流水线后续产出文件的保存路径（processed目录）
            originalVideoName: req.file.originalname,  // 用户上传的原始文件名
            finalVideoName: finalFileName,  // 最终保存的文件名
            originalVideoPath: finalVideoPath,   // 移动后的原始视频文件路径
            fileNameProcessing: {           // 文件名处理信息
                success: sanitizeResult.success,
                modified: sanitizeResult.modified,
                reason: sanitizeResult.reason
            }
        };

        logger.info(`${logPrefix}[步骤 9.1] 流水线上下文参数详情:`);
        logger.info(`${logPrefix}  - reqId: ${context.reqId}`);
        logger.info(`${logPrefix}  - videoIdentifier: ${context.videoIdentifier}`);
        logger.info(`${logPrefix}  - uploadedVideoDirPath: ${context.uploadedVideoDirPath}`);
        logger.info(`${logPrefix}  - savePath: ${context.savePath}`);
        logger.info(`${logPrefix}  - originalVideoName: ${context.originalVideoName}`);
        logger.info(`${logPrefix}  - finalVideoName: ${context.finalVideoName}`);
        logger.info(`${logPrefix}  - originalVideoPath: ${context.originalVideoPath}`);
        logger.info(`${logPrefix}  - fileNameProcessing: ${JSON.stringify(context.fileNameProcessing)}`);

        logger.info(`${logPrefix}[步骤 9.2] 开始调用流水线服务...`);

        // 调用流水线服务执行视频处理
        const pipelineResult = await pipelineService.processUploadedVideo(
            context,
            progressCallbackFromController
        );

        logger.info(`${logPrefix}[步骤 9.3] ✅ 流水线执行完成！`);
        logger.info(`${logPrefix}[步骤 9.4] 流水线最终状态: ${pipelineResult.status}`);
        logger.info(`${logPrefix}[步骤 9.5] 流水线任务数量: ${pipelineResult.tasks ? pipelineResult.tasks.length : 0}`);

        // === 步骤10: 处理流水线结果 ===
        logger.info(`${logPrefix}[步骤 10] 开始处理流水线结果`);
        logger.info(`${logPrefix}[步骤 10.1] 流水线状态: ${pipelineResult.status}`);

        if (pipelineResult.status !== 'completed' && pipelineResult.error) {
            logger.error(`${logPrefix}[步骤 10.2] ❌ 流水线执行失败`);
            logger.error(`${logPrefix}[步骤 10.3] 错误信息: ${pipelineResult.error.message}`);
            logger.error(`${logPrefix}[步骤 10.4] 错误类型: ${pipelineResult.error.name}`);

            // 只有在流水线失败时才发送额外的错误事件
            let errorEventDataPayload = {
                pipelineName: `VideoProcessing-${reqId}`,
                pipelineStatus: PIPELINE_STATUS.FAILED,
                timestamp: new Date().toISOString(),
                videoIdentifier: videoIdentifier,
                failedTaskErrorDetails: {
                    message: pipelineResult.error.message,
                    name: pipelineResult.error.name,
                }
            };

            const errorEvent = createSSEEventData(
                SSE_EVENT_TYPES.PIPELINE_PROGRESS,
                errorEventDataPayload
            );

            sseManager.sendEvent(errorEvent);
            logger.error(`${logPrefix}[步骤 10.5] 已发送错误事件到前端`);

            // 关闭SSE连接
            setTimeout(() => {
                if (sseManager && sseManager.isActive) {
                    sseManager.close();
                    logger.info(`${logPrefix}[步骤 10.6] SSE连接已因错误关闭`);
                }
            }, 1000);
        } else if (pipelineResult.status === 'completed') {
            logger.info(`${logPrefix}[步骤 10.2] ✅ 流水线执行成功！`);
            logger.info(`${logPrefix}[步骤 10.3] 开始处理成功结果`);

            // 详细记录完整的上下文信息，供调试使用
            if (pipelineResult.context) {
                logger.info(`${logPrefix}[步骤 10.4] 流水线输出上下文信息:`);
                logger.info(`${logPrefix}  - 视频标识符: ${pipelineResult.context.videoIdentifier || 'N/A'}`);
                logger.info(`${logPrefix}  - 原始视频路径: ${pipelineResult.context.originalVideoPath || 'N/A'}`);
                logger.info(`${logPrefix}  - 音频文件路径: ${pipelineResult.context.audioFilePathInUploads || 'N/A'}`);
                logger.info(`${logPrefix}  - 转录JSON路径: ${pipelineResult.context.transcriptionJsonPath || 'N/A'}`);
                logger.info(`${logPrefix}  - 英文字幕路径: ${pipelineResult.context.englishSrtPath || 'N/A'}`);
                logger.info(`${logPrefix}  - 中文字幕路径: ${pipelineResult.context.chineseSrtPath || 'N/A'}`);
                logger.info(`${logPrefix}  - 优化字幕路径: ${pipelineResult.context.optimizedSrtContent || 'N/A'}`);
                logger.info(`${logPrefix}  - 处理完成时间: ${pipelineResult.context.processedAt || 'N/A'}`);
                logger.info(`${logPrefix}  - 处理耗时: ${pipelineResult.context.processingDuration || 'N/A'}ms`);
            }

            // === 步骤10.5: 使用新架构生成文件URLs ===
            logger.info(`${logPrefix}[步骤 10.5] 开始生成新架构的文件URLs`);

            const contextWithUrls = { ...(pipelineResult.context || {}) };
            const appBaseUrl = process.env.APP_BASE_URL || 'http://localhost:3000';

            // 构建文件信息对象
            const files = {
                source: {
                    'original.mp4': 'original' + path.extname(req.file.originalname)
                },
                processed: {}
            };

            // 添加处理文件信息
            if (pipelineResult.context.audioFilePathInUploads) {
                const audioFileName = path.basename(pipelineResult.context.audioFilePathInUploads);
                files.processed.audio = audioFileName;
                logger.info(`${logPrefix}[步骤 10.5.1] 音频文件: ${audioFileName}`);
            }

            if (pipelineResult.context.transcriptionJsonPath) {
                const transcriptionFileName = path.basename(pipelineResult.context.transcriptionJsonPath);
                files.processed.transcription = transcriptionFileName;
                logger.info(`${logPrefix}[步骤 10.5.2] 转录文件: ${transcriptionFileName}`);
            }

            if (pipelineResult.context.englishSrtPath) {
                const englishSrtFileName = path.basename(pipelineResult.context.englishSrtPath);
                files.processed.englishSrt = englishSrtFileName;
                logger.info(`${logPrefix}[步骤 10.5.3] 英文字幕: ${englishSrtFileName}`);
            }

            if (pipelineResult.context.chineseSrtPath) {
                const chineseSrtFileName = path.basename(pipelineResult.context.chineseSrtPath);
                files.processed.chineseSrt = chineseSrtFileName;
                logger.info(`${logPrefix}[步骤 10.5.4] 中文字幕: ${chineseSrtFileName}`);
            }

            // 使用PathHelper生成标准化URLs
            const fileUrls = PathHelper.generateFileUrls(videoIdentifier, files);
            logger.info(`${logPrefix}[步骤 10.5.5] 生成的文件URLs:`);
            logger.info(`${logPrefix}  - 原始视频: ${fileUrls.source['original.mp4'] || 'N/A'}`);
            logger.info(`${logPrefix}  - 音频文件: ${fileUrls.processed.audio || 'N/A'}`);
            logger.info(`${logPrefix}  - 转录文件: ${fileUrls.processed.transcription || 'N/A'}`);
            logger.info(`${logPrefix}  - 英文字幕: ${fileUrls.processed.englishSrt || 'N/A'}`);
            logger.info(`${logPrefix}  - 中文字幕: ${fileUrls.processed.chineseSrt || 'N/A'}`);

            // 更新上下文中的URL字段
            contextWithUrls.videoPlaybackUrl = fileUrls.source['original.mp4'];
            contextWithUrls.audioFileUrl = fileUrls.processed.audio;
            contextWithUrls.transcriptionJsonUrl = fileUrls.processed.transcription;
            contextWithUrls.englishSrtUrl = fileUrls.processed.englishSrt;
            contextWithUrls.chineseSrtUrl = fileUrls.processed.chineseSrt;

            // === 步骤10.6: 准备最终结果数据 ===
            logger.info(`${logPrefix}[步骤 10.6] 新架构URL生成完成，准备发送最终结果`);

            // === 步骤11: 发送最终的标准化结果给前端 ===
            const finalResultEvent = createSSEEventData(
                SSE_EVENT_TYPES.PIPELINE_COMPLETE,
                {
                    message: '视频处理流水线已完成',
                    result: pipelineResult,  // 发送完整的标准化结果
                    timestamp: new Date().toISOString(),
                    reqId: reqId
                }
            );

            // 检查客户端连接状态和SSE管理器状态
            if (clientDisconnected) {
                logger.warn(`${logPrefix}[步骤 11] 客户端已断开连接，跳过发送最终结果`);
            } else if (sseManager && sseManager.isActive) {
                logger.info(`${logPrefix}[步骤 11] SSE连接状态正常，准备发送最终结果事件`);
                const sent = sseManager.sendEvent(finalResultEvent);
                logger.info(`${logPrefix}[步骤 11] 最终结果事件发送${sent ? '成功' : '失败'}`);

                // 使用优雅关闭方法，给最终结果事件足够的传输时间
                if (!clientDisconnected && sseManager && sseManager.isActive) {
                    // 发送流水线完成通知事件
                    const pipelineCompletedEvent = createSSEEventData(
                        SSE_EVENT_TYPES.SYSTEM_STATUS,
                        {
                            type: 'pipeline_completed',
                            connectionId: sseManager.connectionId,
                            message: '视频处理流水线已完成，连接即将关闭'
                        }
                    );
                    sseManager.sendEvent(pipelineCompletedEvent);

                    // 使用优雅关闭，延迟2秒关闭连接
                    sseManager.gracefulClose(2000);
                    logger.info(`${logPrefix}[步骤 11] 已启动SSE连接优雅关闭流程`);
                } else {
                    logger.warn(`${logPrefix}[步骤 11] 客户端已断开或SSE连接已断开`);
                    if (sseManager && sseManager.isActive) {
                        sseManager.close(false); // 不发送关闭事件，直接关闭
                        logger.info(`${logPrefix}[步骤 11] 强制关闭SSE连接`);
                    }
                }
            } else {
                logger.warn(`${logPrefix}[步骤 11] SSE连接已断开，无法发送最终结果`);
                logger.warn(`${logPrefix}[步骤 11] SSE管理器状态: ${sseManager ? (sseManager.isActive ? 'active' : 'inactive') : 'null'}`);
            }
        }

    } catch (error) {
        // === 错误处理：控制器级别异常捕获 ===
        logger.error(`${logPrefix}[CRITICAL_ERROR] 控制器级别捕获到意外错误: ${error.message}`);
        logger.error(`${logPrefix}Stack: ${error.stack}`);
        
        if (sseManager && sseManager.isActive) {
            try {
                // 使用标准化工厂函数创建控制器错误事件
                const controllerErrorEvent = createControllerStatusSSE(
                    CONTROLLER_STATUS.CONTROLLER_ERROR,
                    {
                        errorDetails: {
                            message: `服务器内部发生严重错误: ${error.message}`,
                            name: error.name,
                            stackPreview: error.stack ? error.stack.substring(0, 200) + '...' : 'N/A'
                        },
                        videoIdentifier: videoIdentifier
                    }
                );
                
                sseManager.sendEvent(controllerErrorEvent);
                logger.info(`${logPrefix}[ERROR_RECOVERY] 已发送控制器错误事件`);
                
            } catch (sseError) {
                logger.error(`${logPrefix}[CRITICAL_ERROR] 发送控制器错误事件失败: ${sseError.message}`);
            }
            
            sseManager.close();
            logger.info(`${logPrefix}[ERROR_RECOVERY] SSE连接已因错误关闭`);
        }
    } finally {
        // === 资源清理：确保心跳正确关闭 ===
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }
        // 注意：不在finally中关闭SSE连接，让成功路径中的gracefulClose来处理
        // 这样可以确保最终结果事件有足够时间发送
    }
};

// 导出控制器方法
module.exports = {
    uploadVideo
};
