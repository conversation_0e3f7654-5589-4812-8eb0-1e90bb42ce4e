/**
 * @功能概述: Remotion配置文件，用于启动Remotion Studio和渲染设置
 * @配置类型: Remotion开发环境配置
 * @使用场景: 开发时预览和调试视频组件
 */

import { Config } from '@remotion/cli/config';

// 设置 Remotion 项目的入口文件
// 这个文件通常会导入并注册你的根组件 (Root.tsx)
Config.setEntryPoint('src/remotion/index.ts');

// 设置公共资源目录 (public directory)
// 这是 staticFile() 函数查找静态资源（如图片、音频）的根目录
Config.setPublicDir('src/remotion/public');

// 设置渲染并发数，避免资源占用过多
Config.setConcurrency(1);
