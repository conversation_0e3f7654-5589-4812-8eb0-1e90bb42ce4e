/**
 * @功能概述: Remotion视频生成流水线服务，使用Remotion技术完全替代AssVideoGenerationPipelineService
 * @任务序列: AudioProcessingTask → RemotionVideoRenderTask
 * @预期时长: 根据视频长度和复杂度，通常2-10分钟
 * @进度回调: SSE事件推送任务进度和状态
 */

const PipelineBase = require('../class/PipelineBase');
const AudioProcessingTask = require('../tasks/AudioProcessingTask');
const RemotionVideoRenderTask = require('../tasks/RemotionVideoRenderTask');
const VideoConfigManager = require('../config/converters/VideoConfigManager');
const VideoConfigConverter = require('../config/converters/VideoConfigConverter');
const logger = require('../utils/logger');
const path = require('path');

class RemotionVideoGenerationPipelineService extends PipelineBase {
    constructor(reqId) {
        super('RemotionVideoGenerationPipelineService', '基于Remotion的视频生成流水线');
        this.reqId = reqId;
        this.description = '使用Remotion技术生成高质量视频，支持复杂动画和布局效果';
        
        // 初始化任务
        this.audioProcessingTask = new AudioProcessingTask();
        this.remotionVideoRenderTask = new RemotionVideoRenderTask();
        
        // 注册任务到流水线
        this.addTask(this.audioProcessingTask);
        this.addTask(this.remotionVideoRenderTask);
    }

    /**
     * @功能概述: 执行Remotion视频生成流水线
     * @param {Object} inputContext - 输入上下文参数
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Object} 流水线执行结果
     * @执行流程:
     *   1. 验证输入参数和配置
     *   2. 加载和转换视频配置
     *   3. 执行音频处理任务
     *   4. 执行Remotion视频渲染任务
     *   5. 返回标准化结果
     */
    async execute(inputContext, progressCallback) {
        const logPrefix = `[文件：RemotionVideoGenerationPipelineService.js][Remotion视频生成流水线][execute][ReqID:${this.reqId}] `;
        
        logger.info(`${logPrefix}[步骤 1] 开始Remotion视频生成流水线`);
        this.status = 'running';
        this.startTime = Date.now();
        
        // 报告流水线开始
        if (progressCallback) progressCallback({
            pipelineName: this.name,
            status: 'pipeline_started',
            detail: '开始Remotion视频生成流水线',
            timestamp: new Date().toISOString()
        });

        try {
            // 步骤 1: 验证输入参数
            this._validateInputContext(inputContext, logPrefix);
            
            // 步骤 2: 准备执行上下文
            const context = await this._prepareExecutionContext(inputContext, logPrefix);
            logger.info(`${logPrefix}[步骤 2] 执行上下文准备完成`);

            // 步骤 3: 执行音频处理任务
            if (progressCallback) progressCallback({
                pipelineName: this.name,
                status: 'task_starting',
                taskName: this.audioProcessingTask.name,
                detail: '开始音频处理任务'
            });
            
            const audioResult = await this.audioProcessingTask.execute(context, progressCallback);
            
            // 更新上下文
            Object.assign(context, audioResult);
            logger.info(`${logPrefix}[步骤 3] 音频处理任务完成`);

            // 步骤 4: 执行Remotion视频渲染任务
            if (progressCallback) progressCallback({
                pipelineName: this.name,
                status: 'task_starting',
                taskName: this.remotionVideoRenderTask.name,
                detail: '开始Remotion视频渲染任务'
            });
            
            const renderResult = await this.remotionVideoRenderTask.execute(context, progressCallback);
            
            // 更新上下文
            Object.assign(context, renderResult);
            logger.info(`${logPrefix}[步骤 4] Remotion视频渲染任务完成`);

            // 步骤 5: 构建流水线结果
            this.status = 'completed';
            this.endTime = Date.now();
            this.executionTime = this.endTime - this.startTime;
            
            const pipelineResult = this._buildPipelineResult(context, logPrefix);
            this.result = pipelineResult;
            
            // 报告流水线完成
            if (progressCallback) progressCallback({
                pipelineName: this.name,
                status: 'pipeline_completed',
                result: pipelineResult,
                detail: 'Remotion视频生成流水线完成',
                executionTime: this.executionTime,
                timestamp: new Date().toISOString()
            });
            
            logger.info(`${logPrefix}[SUCCESS] Remotion视频生成流水线完成 - 耗时: ${this.executionTime}ms`);
            return pipelineResult;
            
        } catch (error) {
            logger.error(`${logPrefix}[ERROR] Remotion视频生成流水线失败: ${error.message}`);
            this.status = 'failed';
            this.error = error;
            this.endTime = Date.now();
            this.executionTime = this.endTime - this.startTime;
            
            // 报告流水线失败
            if (progressCallback) progressCallback({
                pipelineName: this.name,
                status: 'pipeline_failed',
                error: { message: error.message, name: error.name },
                detail: `Remotion视频生成流水线失败: ${error.message}`,
                executionTime: this.executionTime,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }

    /**
     * @功能概述: 验证输入上下文参数
     * @param {Object} inputContext - 输入上下文
     * @param {string} logPrefix - 日志前缀
     */
    _validateInputContext(inputContext, logPrefix) {
        logger.debug(`${logPrefix}[步骤 1.1] 开始验证输入参数`);
        
        const requiredFields = [
            'videoIdentifier',
            'audioFilePath', 
            'savePath'
        ];
        
        for (const field of requiredFields) {
            if (!inputContext[field]) {
                throw new Error(`缺少必需的输入参数: ${field}`);
            }
        }
        
        // 验证可选但重要的字段
        if (!inputContext.clozedSubtitleJsonArray && !inputContext.enhancedBilingualSubtitleJsonArray) {
            logger.warn(`${logPrefix}[步骤 1.1][WARN] 未提供字幕数据，将生成无字幕视频`);
        }
        
        logger.debug(`${logPrefix}[步骤 1.1] 输入参数验证通过`);
    }

    /**
     * @功能概述: 准备执行上下文
     * @param {Object} inputContext - 输入上下文
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<Object>} 执行上下文
     */
    async _prepareExecutionContext(inputContext, logPrefix) {
        logger.debug(`${logPrefix}[步骤 2.1] 开始准备执行上下文`);
        
        try {
            // 加载和转换视频配置
            let videoConfig;
            if (inputContext.videoConfig) {
                // 使用提供的配置
                if (typeof inputContext.videoConfig === 'string') {
                    // 配置文件路径
                    videoConfig = await VideoConfigManager.loadConfig(inputContext.videoConfig, { renderEngine: 'remotion' });
                    logger.debug(`${logPrefix}[步骤 2.1] 从文件加载配置: ${inputContext.videoConfig}`);
                } else {
                    // 配置对象，需要检测版本并转换
                    const version = VideoConfigManager.detectConfigVersion(inputContext.videoConfig);
                    logger.debug(`${logPrefix}[步骤 2.1] 检测到配置版本: ${version}`);

                    if (version === "1.0" || version === "legacy") {
                        // 老配置转换为通用配置
                        videoConfig = VideoConfigConverter.convertLegacyToUniversal(inputContext.videoConfig);
                        logger.info(`${logPrefix}[步骤 2.1] 老配置已转换为通用配置格式`);
                    } else {
                        // 已经是通用配置格式
                        videoConfig = inputContext.videoConfig;
                        logger.debug(`${logPrefix}[步骤 2.1] 使用通用配置格式`);
                    }

                    // 验证配置
                    VideoConfigManager.validateConfig(videoConfig);
                    logger.debug(`${logPrefix}[步骤 2.1] 配置验证通过`);
                }
            } else {
                // 使用默认配置
                videoConfig = await VideoConfigManager.getDefaultConfig('educational_subtitle', 'remotion');
                logger.debug(`${logPrefix}[步骤 2.1] 使用默认配置`);
            }
            
            // 构建执行上下文
            const context = {
                // 基础信息
                reqId: this.reqId,
                videoIdentifier: inputContext.videoIdentifier,
                
                // 输入文件
                audioFilePath: inputContext.audioFilePath,
                originalVideoPath: inputContext.originalVideoPath,
                
                // 字幕数据
                clozedSubtitleJsonArray: inputContext.clozedSubtitleJsonArray || [],
                enhancedBilingualSubtitleJsonArray: inputContext.enhancedBilingualSubtitleJsonArray || [],
                
                // 配置信息
                videoConfig,
                repeatCount: videoConfig.content.audio.repeat || 3,
                fps: videoConfig.output.fps || 30,
                
                // 输出路径
                savePath: inputContext.savePath,
                
                // 其他参数
                ...inputContext
            };
            
            logger.debug(`${logPrefix}[步骤 2.1] 执行上下文准备完成`);
            return context;
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 2.1][ERROR] 执行上下文准备失败: ${error.message}`);
            throw new Error(`执行上下文准备失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 构建流水线结果
     * @param {Object} context - 执行上下文
     * @param {string} logPrefix - 日志前缀
     * @returns {Object} 标准化的流水线结果
     */
    _buildPipelineResult(context, logPrefix) {
        logger.debug(`${logPrefix}[步骤 5.1] 开始构建流水线结果`);
        
        try {
            const result = {
                // 基础信息
                pipelineName: this.name,
                videoIdentifier: context.videoIdentifier,
                reqId: this.reqId,
                
                // 输出文件
                finalVideoPath: context.finalVideoPath,
                processedAudioPath: context.processedAudioPath,
                
                // 统计信息
                videoGenerationStats: {
                    totalExecutionTime: this.executionTime,
                    audioProcessingTime: this.audioProcessingTask.executionTime || 0,
                    renderingTime: this.remotionVideoRenderTask.executionTime || 0,
                    
                    // 音频信息
                    originalAudioDuration: context.originalDuration,
                    totalAudioDuration: context.totalDuration,
                    audioRepeatCount: context.repeatCount,
                    
                    // 视频信息
                    videoDurationInFrames: context.durationInFrames,
                    videoFps: context.fps,
                    renderEngine: 'remotion',
                    
                    // 渲染统计
                    renderStats: context.renderStats || {}
                },
                
                // 配置信息
                videoConfig: context.videoConfig,
                remotionConfig: context.remotionConfig,
                
                // 任务结果
                taskResults: {
                    audioProcessing: this.audioProcessingTask.result,
                    remotionRender: this.remotionVideoRenderTask.result
                },
                
                // 时间戳
                startTime: new Date(this.startTime).toISOString(),
                endTime: new Date(this.endTime).toISOString(),
                
                // 成功标识
                success: true
            };
            
            logger.debug(`${logPrefix}[步骤 5.1] 流水线结果构建完成`);
            return result;
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 5.1][ERROR] 流水线结果构建失败: ${error.message}`);
            throw new Error(`流水线结果构建失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 获取流水线状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            pipelineName: this.name,
            status: this.status,
            reqId: this.reqId,
            startTime: this.startTime,
            endTime: this.endTime,
            executionTime: this.executionTime,
            tasks: [
                {
                    name: this.audioProcessingTask.name,
                    status: this.audioProcessingTask.status,
                    result: this.audioProcessingTask.result,
                    error: this.audioProcessingTask.error
                },
                {
                    name: this.remotionVideoRenderTask.name,
                    status: this.remotionVideoRenderTask.status,
                    result: this.remotionVideoRenderTask.result,
                    error: this.remotionVideoRenderTask.error
                }
            ],
            result: this.result,
            error: this.error
        };
    }

    /**
     * @功能概述: 清理流水线资源
     */
    async cleanup() {
        const logPrefix = `[RemotionVideoGenerationPipelineService][cleanup][ReqID:${this.reqId}]`;
        
        try {
            logger.debug(`${logPrefix} 开始清理流水线资源`);
            
            // 清理各个任务的资源
            if (this.audioProcessingTask) {
                await this.audioProcessingTask.cleanup();
            }
            
            if (this.remotionVideoRenderTask) {
                await this.remotionVideoRenderTask.cleanup();
            }
            
            logger.debug(`${logPrefix} 流水线资源清理完成`);
            
        } catch (error) {
            logger.error(`${logPrefix} 流水线资源清理失败: ${error.message}`);
            // 清理失败不应该影响主流程，只记录错误
        }
    }
}

module.exports = RemotionVideoGenerationPipelineService;
