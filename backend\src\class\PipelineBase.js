/**
 * @功能概述: 管理和执行一系列有序任务的处理流程 (PipelineBase)。
 *           负责维护任务队列、共享的上下文数据，并按顺序调用每个任务。
 *           集成了标准化的进度监控机制，支持细粒度的流水线和任务进度追踪。
 * 
 * @说明: 这是从原 llmPipeline.js 中拆分出来的 LLMPipeline 类，现更名为 PipelineBase。
 *        作为流水线架构的核心基类，为所有具体流水线提供统一的执行框架。
 * 
 * @架构位置: 流水线层基类，位于任务层之上，为控制器层提供流程编排能力
 * @进度监控: 支持流水线级别和任务级别的双重进度追踪，提供标准化的SSE数据格式
 * 
 * @依赖关系:
 *   - TaskBase: 任务基类，所有添加到流水线的任务都应继承此类
 *   - progress.js: 进度监控常量和工厂函数
 *   - logger: 日志记录工具
 */

// 导入日志工具，用于记录流水线执行过程中的关键信息
const logger = require('../utils/logger'); // 路径相对于 backend/src/class/

// 导入任务基类，确保添加到流水线的任务符合标准接口
const TaskBase = require('./TaskBase'); // 导入 TaskBase 基类

// 导入进度监控相关常量和工厂函数，用于标准化进度数据
const { 
  TASK_STATUS, 
  TASK_SUBSTATUS, 
  PIPELINE_STATUS,
  createProgressData, 
  createLLMProgressData,
  createPipelineProgressData 
} = require('../constants/progress');

// 模块级日志前缀，用于标识从本文件输出的日志
const moduleLogPrefix = `[文件：PipelineBase.js][流程基类][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);

/**
 * @功能概述: 管理和执行一系列有序任务的处理流程 (Pipeline)。
 *           它负责维护任务队列、共享的上下文数据，并按顺序调用每个任务。
 *           集成标准化进度监控，支持实时进度报告和SSE事件推送。
 * 
 * @property {string} name - 流程的名称，用于标识和日志记录
 * @property {string} pipelineId - 流水线的唯一标识符，格式：{name}-{timestamp}-{randomString}
 * @property {Array<TaskBase>} tasks - 按顺序存储的任务实例的数组，流程将按照这个数组的顺序执行任务
 * @property {object} context - 流程执行期间的共享上下文数据，任务之间通过这个对象传递和共享信息
 * @property {string} status - 整个流程的当前执行状态，来自 PIPELINE_STATUS 枚举
 * @property {object} customTaskHandlers - 用于存储已注册的自定义任务处理器函数
 * @property {Array} progressHistory - 流水线级别的进度历史记录数组
 * @property {number|null} startTime - 流水线开始执行的时间戳
 * @property {number|null} endTime - 流水线结束执行的时间戳
 * 
 * @example
 * // 创建流水线并添加任务
 * const pipeline = new PipelineBase('VideoProcessing');
 * pipeline.addTask(new ConvertToAudioTask());
 * pipeline.addTask(new TranscriptionTask());
 * 
 * // 执行流水线
 * const result = await pipeline.execute(
 *   { videoPath: '/path/to/video.mp4' },
 *   (progressData) => console.log('进度:', progressData)
 * );
 */
class PipelineBase {
    /**
     * @功能概述: PipelineBase 类的构造函数，用于创建一个流程实例。
     *           初始化所有必要的属性，包括进度追踪和状态管理。
     * 
     * @param {string} name - 流程的名称，应该具有描述性，便于日志识别和调试
     * 
     * @throws {Error} 当名称无效时抛出错误（null、undefined、空字符串或非字符串类型）
     * 
     * @说明: 构造函数会自动生成唯一的流水线ID，初始化所有状态属性，
     *        设置初始状态为 PENDING，并创建空的进度历史记录数组。
     * 
     * @初始化内容:
     *   - 生成唯一流水线ID（包含时间戳和随机字符串）
     *   - 设置初始状态为 PENDING
     *   - 初始化任务数组、上下文对象、自定义处理器对象
     *   - 初始化时间戳和进度历史记录
     * 
     * @example
     * const pipeline = new PipelineBase('VideoProcessing');
     * // 流水线ID示例: VideoProcessing-1640995200000-abc123def
     */
    constructor(name) {
        // 参数验证：确保名称是有效的非空字符串
        if (!name || typeof name !== 'string' || name.trim() === '') {
            const errorMsg = '创建流程时必须提供一个有效的非空字符串名称。';
            logger.error(`${moduleLogPrefix}[PipelineBase][constructor] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        // 基础标识信息
        this.name = name; // 流程的名称，用于标识和日志记录
        this.pipelineId = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; // 生成唯一流水线ID

        // 任务管理
        this.tasks = []; // 按顺序存储的任务实例的数组
        this.customTaskHandlers = {}; // 用于存储已注册的自定义任务处理器函数

        // 数据共享
        this.context = {}; // 流程执行期间的共享上下文数据

        // 状态管理
        this.status = PIPELINE_STATUS.PENDING; // 整个流程的当前执行状态，初始为等待状态

        // 进度追踪
        this.progressHistory = []; // 流水线级别的进度历史记录数组
        this.startTime = null; // 流水线开始执行的时间戳
        this.endTime = null; // 流水线结束执行的时间戳
        
        // 记录流水线实例创建日志
        logger.info(`${moduleLogPrefix}[PipelineBase:${this.name}][constructor] 流程实例已创建，ID: ${this.pipelineId}`);

        // 初始化任务时间戳
        this.tasks.forEach(task => {
            if (!task.taskId) {
                task.taskId = `${task.name}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
            }
        });
    }

    /**
     * @功能概述: 向流程的任务队列中添加一个任务。
     *           支持添加 TaskBase 实例或符合特定规范的函数（函数将被自动包装成 TaskBase）。
     * 
     * @param {TaskBase|function} taskOrHandler - 要添加的任务，可以是：
     *                                           - TaskBase 的实例：直接添加到任务队列
     *                                           - 函数：将被包装成 TaskBase 实例后添加
     * @param {string} [taskName] - 当 taskOrHandler 是函数时的必需参数，作为任务的名称
     * 
     * @returns {PipelineBase} 返回当前流程实例 (this)，支持链式调用
     * 
     * @throws {Error} 在以下情况下抛出错误：
     *                 - taskOrHandler 类型无效（既不是 TaskBase 实例也不是函数）
     *                 - 添加函数任务时未提供有效的 taskName
     * 
     * @说明: 
     *   - TaskBase 实例会直接添加到任务数组
     *   - 函数会被包装成 TaskBase 实例，其 execute 方法指向原函数
     *   - 支持链式调用，可以连续添加多个任务
     * 
     * @执行流程:
     *   1. 检查 taskOrHandler 的类型
     *   2. 如果是 TaskBase 实例，直接添加到任务数组
     *   3. 如果是函数，验证 taskName 参数，创建包装的 TaskBase 实例
     *   4. 记录添加操作的日志
     *   5. 返回当前实例支持链式调用
     * 
     * @example
     * // 添加 TaskBase 实例
     * pipeline.addTask(new ConvertToAudioTask());
     * 
     * // 添加函数任务
     * pipeline.addTask(async (context) => {
     *   return { processedData: 'some result' };
     * }, 'CustomProcessing');
     * 
     * // 链式调用
     * pipeline
     *   .addTask(new Task1())
     *   .addTask(new Task2())
     *   .addTask(customFunction, 'CustomTask');
     */
    addTask(taskOrHandler, taskName) {
        // 定义日志前缀，包含模块名、流程名和方法名，方便追踪日志来源
        const logPrefix = `${moduleLogPrefix}[PipelineBase:${this.name}][addTask]`;

        // 检查要添加的任务是 TaskBase 的实例还是一个函数
        if (taskOrHandler instanceof TaskBase) {
            // 如果是 TaskBase 实例，直接将其添加到任务数组中
            this.tasks.push(taskOrHandler);
            // 记录日志，表明已添加一个 TaskBase 实例任务
            logger.info(`${logPrefix} 任务 '${taskOrHandler.name}' (TaskBase实例) 已添加到位置 ${this.tasks.length}`);
        } else if (typeof taskOrHandler === 'function') {
            // 如果是函数，检查是否提供了有效的任务名称
            if (!taskName || typeof taskName !== 'string' || taskName.trim() === '') {
                // 如果未提供有效名称，记录错误并抛出异常
                logger.error(`${logPrefix} 添加自定义任务函数时必须提供一个有效的任务名。`);
                throw new Error('Task name must be provided for function-based tasks.');
            }
            // 创建一个新的 TaskBase 实例来包装这个函数
            const functionalTask = new TaskBase(taskName);
            // 将传入的函数赋值给新 TaskBase 实例的 execute 方法
            functionalTask.execute = async (context, progressCallback) => taskOrHandler(context, progressCallback);
            // 将包装后的 TaskBase 实例添加到任务数组中
            this.tasks.push(functionalTask);
            // 记录日志，表明已添加一个函数任务并已包装
            logger.info(`${logPrefix} 自定义任务函数 '${taskName}' 已添加并包装为TaskBase，位置 ${this.tasks.length}`);
        } else {
            // 如果既不是 TaskBase 实例也不是函数，记录错误并抛出异常
            logger.error(`${logPrefix} 尝试添加无效的任务类型。任务必须是 TaskBase 实例或函数。接收到类型: ${typeof taskOrHandler}`);
            throw new Error('Invalid task type. Must be a TaskBase instance or a function.');
        }
        
        // 返回当前 Pipeline 实例，支持链式调用
        return this;
    }

    /**
     * @功能概述: 注册一个自定义任务处理器函数，用于后续的动态任务创建。
     *           注册的处理器可以在运行时被引用和使用。
     * 
     * @param {string} handlerName - 自定义任务处理器的唯一名称，用于后续查找和引用
     * @param {function} handlerFunction - 实际的任务处理函数，应该是一个异步函数
     * 
     * @returns {void}
     * 
     * @throws {Error} 在以下情况下抛出错误：
     *                 - handlerName 无效（null、undefined、空字符串或非字符串类型）
     *                 - handlerFunction 不是一个函数
     * 
     * @说明: 
     *   - 处理器名称必须是唯一的，重复注册会覆盖之前的处理器
     *   - 处理器函数应该遵循任务执行的标准接口
     *   - 注册的处理器存储在 customTaskHandlers 对象中
     * 
     * @执行流程:
     *   1. 验证处理器名称的有效性
     *   2. 验证处理器函数的类型
     *   3. 将处理器存储到 customTaskHandlers 对象中
     *   4. 记录注册成功的日志
     * 
     * @example
     * // 注册自定义处理器
     * pipeline.registerCustomTaskHandler('dataProcessor', async (context) => {
     *   const processedData = await processData(context.rawData);
     *   return { processedData };
     * });
     * 
     * // 后续可以通过名称引用
     * const handler = pipeline.customTaskHandlers['dataProcessor'];
     */
    registerCustomTaskHandler(handlerName, handlerFunction) {
        const logPrefix = `${moduleLogPrefix}[PipelineBase:${this.name}][registerCustomTaskHandler]`;
        
        // 验证处理器名称的有效性
        if (!handlerName || typeof handlerName !== 'string' || handlerName.trim() === '') {
            logger.error(`${logPrefix} 注册自定义任务处理器时，名称 '${handlerName}' 无效。`);
            throw new Error('Custom task handler name must be a non-empty string.');
        }
        
        // 验证处理器函数的类型
        if (typeof handlerFunction !== 'function') {
            logger.error(`${logPrefix} 自定义任务处理器 '${handlerName}' 必须是一个函数。接收到的类型: ${typeof handlerFunction}`);
            throw new Error('Handler must be a function.');
        }
        
        // 将处理器存储到 customTaskHandlers 对象中
        this.customTaskHandlers[handlerName] = handlerFunction;
        
        // 记录注册成功的日志
        logger.info(`${logPrefix} 自定义任务处理器 '${handlerName}' 已成功注册。`);
    }

    /**
     * @功能概述: 按顺序执行流程中定义的所有任务。这是驱动整个流程运行的核心方法。
     *           集成了标准化的进度监控和错误处理机制，支持实时进度报告。
     * 
     * @param {object} [initialContext={}] - 流程启动时的初始上下文数据，包含：
     *                                      - 任何任务需要的初始输入数据
     *                                      - 配置参数和选项
     *                                      - 请求ID等追踪信息
     * @param {function} [pipelineProgressCallback] - 用于报告流水线及各任务进度的回调函数
     *                                               回调函数接收标准化的进度数据对象
     * 
     * @returns {Promise<object>} 返回一个包含流程执行结果的对象：
     *                           - status: 流程最终状态 ('completed' 或 'failed')
     *                           - context: 包含所有任务输出的最终上下文对象
     *                           - tasks: 所有任务实例的数组，包含各自的状态和结果
     * 
     * @throws {Error} 当任务执行失败时，错误会被捕获并记录，但不会向上抛出
     * 
     * @说明: 
     *   - 任务按照添加顺序依次执行
     *   - 每个任务的输出会自动合并到共享上下文中
     *   - 任务失败会导致整个流程终止
     *   - 支持细粒度的进度报告和SSE事件推送
     *   - 所有进度回调异常都会被安全捕获，不影响主流程
     *   - 使用标准化的 createPipelineProgressData 工厂函数创建进度数据
     * 
     * @执行流程:
     *   1. 初始化流程状态和上下文
     *   2. 记录流程开始并发送开始事件
     *   3. 依次执行每个任务：
     *      a. 设置任务进度回调处理器
     *      b. 调用任务的 execute 方法
     *      c. 将任务结果合并到上下文
     *      d. 处理任务执行异常
     *   4. 更新流程最终状态
     *   5. 发送完成或失败事件
     *   6. 返回执行结果
     * 
     * @进度回调数据结构: 使用 createPipelineProgressData 工厂函数创建，包含：
     *   - pipelineName: 流水线名称
     *   - pipelineId: 流水线唯一标识
     *   - pipelineStatus: 流水线当前状态
     *   - tasksSnapshot: 所有任务的状态快照数组
     *   - progress: 整体进度信息（当前/总计/百分比）
     *   - timestamp: 时间戳
     *   - currentTaskName: 当前执行任务名称（如果有）
     *   - currentTaskStatus: 当前任务状态（如果有）
     *   - currentTaskDetail: 当前任务详情（如果有）
     *   - failedTaskName: 失败任务名称（仅失败时）
     *   - finalContextPreview: 最终上下文预览（仅完成时）
     * 
     * @example
     * // 基本使用
     * const result = await pipeline.execute(
     *   { videoPath: '/path/to/video.mp4' }
     * );
     * 
     * // 带进度回调
     * const result = await pipeline.execute(
     *   { videoPath: '/path/to/video.mp4' },
     *   (progressData) => {
     *     console.log(`流水线 ${progressData.pipelineName} 状态: ${progressData.pipelineStatus}`);
     *     console.log(`整体进度: ${progressData.progress.percentage}%`);
     *     if (progressData.currentTaskName) {
     *       console.log(`当前任务: ${progressData.currentTaskName} - ${progressData.currentTaskDetail}`);
     *     }
     *   }
     * );
     */
    async execute(initialContext = {}, pipelineProgressCallback) {
        const logPrefix = `${moduleLogPrefix}[PipelineBase:${this.name}][execute]`;
        
        // 步骤 1: 初始化流程状态和上下文
        this.context = { ...initialContext }; // 复制初始上下文，避免修改原对象
        this.status = PIPELINE_STATUS.RUNNING; // 设置流程状态为运行中
        this.startTime = Date.now(); // 记录流程开始时间

        // 在 execute 方法的开始，初始化上下文后添加
        // 确保所有任务都有唯一ID
        this.tasks.forEach(task => {
            if (!task.taskId) {
                task.taskId = `${task.name}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
            }
        });

        /**
         * @功能概述: 安全地调用流水线进度回调函数的辅助函数
         *           使用标准化的 createPipelineProgressData 工厂函数创建进度数据
         * @param {object} progressDetails - 特定于此次回调的进度详情
         * 
         * @说明: 
         *   - 使用 createPipelineProgressData 工厂函数创建标准化进度数据
         *   - 自动包含流水线基础信息和任务快照
         *   - 安全捕获回调异常，避免影响主流程
         *   - 记录详细的调试日志
         */
        const safeCallPipelineProgressCallback = (progressDetails) => {
            if (pipelineProgressCallback && typeof pipelineProgressCallback === 'function') {
                try {
                    // 使用标准化工厂函数创建流水线进度数据
                    const pipelineProgressData = createPipelineProgressData({
                        pipelineName: this.name, // 流水线名称
                        pipelineId: this.pipelineId, // 流水线唯一标识
                        pipelineStatus: progressDetails.pipelineStatusOverride || this.status, // 流水线状态，支持临时覆盖
                        tasks: this.tasks, // 所有任务数组，工厂函数会自动生成快照
                        ...progressDetails // 合并特定于此次回调的细节
                    });
                    
                    // 记录即将发送给回调函数的数据预览，用于调试
                    logger.debug(`${logPrefix} 调用 pipelineProgressCallback。数据预览: pipelineStatus=${pipelineProgressData.pipelineStatus}, progress=${pipelineProgressData.progress.percentage}%, currentTask=${progressDetails.currentTaskName || 'N/A'}`);
                    
                    // 调用进度回调函数，传递标准化的进度数据
                    pipelineProgressCallback(pipelineProgressData);

                    // 记录进度历史
                    this.progressHistory.push({
                        timestamp: new Date().toISOString(),
                        status: pipelineProgressData.pipelineStatus,
                        currentTask: progressDetails.currentTaskName || null,
                        snapshot: pipelineProgressData.tasksSnapshot
                    });

                    // 限制历史记录数量，避免内存过度使用
                    if (this.progressHistory.length > 100) {
                        this.progressHistory = this.progressHistory.slice(-50); // 保留最近50条记录
                    }
                } catch (cbError) {
                    // 捕获回调函数执行异常，记录错误但不影响主流程
                    logger.error(`${logPrefix} pipelineProgressCallback 自身执行出错: ${cbError.message}`);
                }
            }
        };

        // 步骤 2: 记录流程开始并发送开始事件
        logger.info(`${logPrefix}[步骤 1/6] 初始化流程上下文`);
        logger.info(`${logPrefix}[步骤 2/6] 注册 ${this.tasks.length} 个任务`);
        logger.info(`${logPrefix} 开始执行流程。初始上下文keys: ${Object.keys(this.context).join(', ')}`);
        logger.info(`${logPrefix} 流程包含 ${this.tasks.length} 个任务: ${this.tasks.map(t => t.name).join(' → ')}`);
        
        // 发送流程开始事件，使用 pipelineStatusOverride 临时覆盖状态为 'started'
        safeCallPipelineProgressCallback({ 
            pipelineStatusOverride: PIPELINE_STATUS.RUNNING,
            currentTaskName: null,
            currentTaskStatus: null,
            currentTaskDetail: '流水线已启动，准备执行任务'
        });

        // 步骤 3: 依次执行每个任务
        logger.info(`${logPrefix} 开始按序执行 ${this.tasks.length} 个任务`);
        for (let i = 0; i < this.tasks.length; i++) {
            const task = this.tasks[i]; // 获取当前要执行的任务
            const taskLogPrefix = `${moduleLogPrefix}[PipelineBase:${this.name}][Task:${task.name}]`;
            
            // 记录任务开始执行的日志
            logger.info(`${taskLogPrefix} 准备执行任务 #${i + 1}/${this.tasks.length}。`);
            
            /**
             * @功能概述: 任务进度处理器，用于接收和转发任务的进度更新
             * @param {object} taskProgress - 任务报告的进度数据
             * 
             * @说明: 
             *   - 接收任务级别的进度更新
             *   - 转换为流水线级别的进度数据
             *   - 通过 safeCallPipelineProgressCallback 向上层报告
             *   - 使用标准化工厂函数确保数据结构一致性
             */
            const taskProgressHandler = (taskProgress) => {
                // 记录任务进度更新的调试日志
                logger.debug(`${logPrefix} 任务 '${task.name}' 报告进度: status=${taskProgress.status}, detail=${taskProgress.detail || 'N/A'}`);
                
                // 将任务进度转换为流水线进度并向上报告
                safeCallPipelineProgressCallback({
                    currentTaskName: task.name, // 明确是哪个任务触发的进度更新
                    currentTaskStatus: taskProgress.status, // 当前任务的状态
                    currentTaskDetail: taskProgress.detail, // 当前任务的详细描述
                    // 如果任务报告了错误，提取错误信息
                    currentTaskError: taskProgress.error ? 
                        { message: taskProgress.error.message, name: taskProgress.error.name } : undefined,
                    // 如果任务报告了结果，提供结果预览
                    currentTaskResultPreview: taskProgress.result ? 
                        (JSON.stringify(taskProgress.result).substring(0, 70) + '...') : undefined,
                    // 包含任务ID以便前端追踪
                    currentTaskId: task.taskId
                });
            };

            try {
                // 步骤 3a: 执行任务
                // task.status 和 task.result/error 会在 task.execute 内部更新并通过 taskProgressHandler 回调
                const taskExecutionPromise = task.execute(this.context, taskProgressHandler);

                // 添加超时控制 (默认 5 分钟)
                const timeoutDuration = task.timeout || 300000; // 任务实例可以覆盖默认超时
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => {
                        const timeoutError = new Error(`任务执行超时: ${task.name} 在 ${timeoutDuration / 1000} 秒内未完成`);
                        timeoutError.name = 'TaskTimeoutError';
                        logger.error(`${taskLogPrefix}[ERROR][TIMEOUT] ${timeoutError.message}`);

                        // 增强的进程清理机制
                        try {
                            // 1. 尝试任务自定义的取消方法
                            if (typeof task.cancel === 'function') {
                                task.cancel();
                                logger.warn(`${taskLogPrefix}[TIMEOUT] 调用任务自定义取消方法`);
                            }

                            // 2. 特殊处理VideoCompositionTask的FFmpeg进程
                            if (task.name === 'VideoCompositionTask' && typeof task.forceCleanupFFmpegProcess === 'function') {
                                task.forceCleanupFFmpegProcess('任务超时');
                                logger.warn(`${taskLogPrefix}[TIMEOUT] 强制清理VideoCompositionTask的FFmpeg进程`);
                            }

                            // 3. 通用进程清理（如果任务有currentProcess属性）
                            if (task.currentProcess && !task.currentProcess.killed) {
                                logger.warn(`${taskLogPrefix}[TIMEOUT] 强制终止任务进程 PID: ${task.currentProcess.pid}`);
                                if (process.platform === 'win32') {
                                    require('child_process').exec(`taskkill /F /PID ${task.currentProcess.pid}`);
                                } else {
                                    task.currentProcess.kill('SIGKILL');
                                }
                            }

                        } catch (cleanupError) {
                            logger.error(`${taskLogPrefix}[TIMEOUT] 进程清理失败: ${cleanupError.message}`);
                        }

                        reject(timeoutError);
                    }, timeoutDuration)
                );

                // 使用 Promise.race 竞争任务执行和超时
                const result = await Promise.race([taskExecutionPromise, timeoutPromise]);

                logger.debug(`${logPrefix}[execute] 任务执行 Promise 已解析。`);

                // 步骤 3b: 处理任务执行结果
                // 此处的 result 是 task.execute 的最终返回值，通常与 task.result 一致
                // 如果结果是超时错误，这里会被catch块捕获，不会到达这里
                if (typeof result === 'object' && result !== null) {
                    // 如果结果是对象，将其合并到共享上下文中
                    this.context = { ...this.context, ...result };
                    logger.info(`${taskLogPrefix} 任务 '${task.name}' 返回结果已合并到上下文。结果keys: ${Object.keys(result).join(', ')}`);
                } else {
                    // 如果结果不是对象或为null，记录但不合并
                    logger.info(`${taskLogPrefix} 任务 '${task.name}' 返回结果非对象或为null，未合并新数据。返回值类型: ${typeof result}`);
                }
                
                // 记录任务完成后的上下文状态
                logger.debug(`${taskLogPrefix} 任务 '${task.name}' 完成后，当前上下文keys: ${Object.keys(this.context).join(', ')}`);
                
                // 发送任务完成的流水线级进度更新
                safeCallPipelineProgressCallback({
                    currentTaskName: task.name,
                    currentTaskStatus: TASK_STATUS.COMPLETED,
                    currentTaskDetail: `任务 '${task.name}' 已完成`,
                    currentTaskId: task.taskId
                });
                
            } catch (error) {
                // 步骤 3c: 处理任务执行异常
                // task.status 应该已经是 'failed' (由任务内部回调设置) - 确保这一点或在此处设置
                if (task.status !== TASK_STATUS.FAILED) {
                    task.fail(error); // 如果任务内部没有标记失败，在此处调用fail方法
                }
                
                // 更新整个流水线的状态为失败
                this.status = PIPELINE_STATUS.FAILED;
                this.endTime = Date.now(); // 记录流程结束时间
                
                // 记录任务失败的详细错误信息
                logger.error(`${taskLogPrefix} 任务 '${task.name}' 执行失败 (PipelineBase捕获): ${error.message}`);
                logger.error(`${taskLogPrefix} 错误堆栈 (PipelineBase捕获): ${error.stack}`);
                
                // 将错误信息添加到上下文中，供后续分析使用
                this.context.pipelineError = {
                    failedTaskName: task.name, // 失败的任务名称
                    failedTaskId: task.taskId, // 失败任务的ID
                    message: error.message, // 错误消息
                    name: error.name, // 错误类型名称
                    stack: error.stack // 错误堆栈
                };
                
                // 发送流程失败事件，使用标准化工厂函数
                safeCallPipelineProgressCallback({
                    pipelineStatusOverride: PIPELINE_STATUS.FAILED, // 明确覆盖流水线状态
                    failedTaskName: task.name, // 失败任务的名称
                    failedTaskId: task.taskId, // 失败任务的ID
                    failedTaskErrorDetails: {
                        message: error.message, 
                        name: error.name,
                        stackPreview: error.stack ? error.stack.substring(0,500)+'...' : 'N/A' // 堆栈预览
                    },
                    currentTaskName: task.name, // 标记当前是哪个任务失败
                    currentTaskStatus: TASK_STATUS.FAILED, // 当前任务的状态为失败
                    currentTaskDetail: `任务 '${task.name}' 执行失败` // 详细描述
                });
                
                // 记录流程终止的警告日志
                logger.warn(`${logPrefix} 流程因任务 '${task.name}' 失败而终止。总执行时间: ${this.endTime - this.startTime}ms`);
                
                // 任务失败导致整个流水线终止，跳出循环
                break;
            }
        }
        
        // 步骤 4: 检查流水线状态，只有当状态不是失败时才标记为完成
        if (this.status !== PIPELINE_STATUS.FAILED) {
            // 所有任务成功完成，更新流程状态
            this.status = PIPELINE_STATUS.COMPLETED;
            this.endTime = Date.now(); // 记录流程结束时间
            
            // 步骤 5: 记录成功完成并发送完成事件
            const totalExecutionTime = this.endTime - this.startTime;
            logger.info(`${logPrefix} 流程所有任务执行完毕。最终状态: ${this.status}，总执行时间: ${totalExecutionTime}ms`);
            
            // 发送流程完成事件，使用标准化工厂函数
            safeCallPipelineProgressCallback({
                finalContextPreview: JSON.stringify(this.context), // 完整的最终上下文，不截断
                finalMessage: `流水线成功完成，共执行 ${this.tasks.length} 个任务，耗时 ${totalExecutionTime}ms`
            });
        } else {
            // 流水线已经标记为失败，记录失败完成
            const totalExecutionTime = this.endTime - this.startTime;
            logger.error(`${logPrefix} 流程执行失败。最终状态: ${this.status}，总执行时间: ${totalExecutionTime}ms`);
            
            // 发送流程失败的最终事件
            safeCallPipelineProgressCallback({
                pipelineStatusOverride: PIPELINE_STATUS.FAILED,
                finalContextPreview: JSON.stringify(this.context),
                finalMessage: `流水线执行失败，失败任务: ${this.context.pipelineError?.failedTaskName || '未知'}，耗时 ${totalExecutionTime}ms`
            });
        }
        
        // 步骤 6: 返回结果（保持原格式，标准化在服务层处理）
        return { status: this.status, context: this.context, tasks: this.tasks };
    }
}

// 导出 PipelineBase 类供其他模块使用
module.exports = PipelineBase;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}PipelineBase 类已导出。`); 