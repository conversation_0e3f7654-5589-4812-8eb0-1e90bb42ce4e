/**
 * @文件功能: 文件下载控制器
 * @创建日期: 2025-06-14
 * @作者: AI Assistant
 * @功能概述: 提供安全的文件下载服务，支持视频和音频文件下载
 */

const path = require('path');
const fs = require('fs');
const logger = require('../../utils/logger');

/**
 * @功能概述: 处理文件下载请求
 * @参数说明:
 *   - req.query.file: 要下载的文件路径
 * @返回值: 文件流或错误响应
 * @安全措施: 路径验证、文件存在性检查、MIME类型设置
 */
async function downloadFile(req, res) {
    const logPrefix = '[文件：downloadController.js][downloadFile]';
    const reqId = req.headers['x-request-id'] || 'unknown';
    
    try {
        logger.info(`${logPrefix}[ReqID:${reqId}] 收到文件下载请求`);
        
        // 获取文件路径参数
        const filePath = req.query.file;
        if (!filePath) {
            logger.warn(`${logPrefix}[ReqID:${reqId}] 缺少文件路径参数`);
            return res.status(400).json({
                error: '缺少文件路径参数',
                message: '请提供要下载的文件路径'
            });
        }
        
        logger.info(`${logPrefix}[ReqID:${reqId}] 请求下载文件: ${filePath}`);
        
        // 安全检查：防止路径遍历攻击
        const normalizedPath = path.normalize(filePath);
        if (normalizedPath.includes('..') || normalizedPath.startsWith('/')) {
            logger.warn(`${logPrefix}[ReqID:${reqId}] 检测到不安全的文件路径: ${normalizedPath}`);
            return res.status(403).json({
                error: '不安全的文件路径',
                message: '文件路径包含不允许的字符'
            });
        }
        
        // 构建完整的文件路径
        const fullPath = path.resolve(normalizedPath);
        logger.info(`${logPrefix}[ReqID:${reqId}] 解析后的完整路径: ${fullPath}`);
        
        // 检查文件是否存在
        if (!fs.existsSync(fullPath)) {
            logger.warn(`${logPrefix}[ReqID:${reqId}] 文件不存在: ${fullPath}`);
            return res.status(404).json({
                error: '文件不存在',
                message: '请求的文件未找到'
            });
        }
        
        // 获取文件信息
        const stats = fs.statSync(fullPath);
        if (!stats.isFile()) {
            logger.warn(`${logPrefix}[ReqID:${reqId}] 路径不是文件: ${fullPath}`);
            return res.status(400).json({
                error: '路径错误',
                message: '指定的路径不是文件'
            });
        }
        
        // 获取文件扩展名和MIME类型
        const ext = path.extname(fullPath).toLowerCase();
        const fileName = path.basename(fullPath);
        
        let mimeType = 'application/octet-stream'; // 默认MIME类型
        switch (ext) {
            case '.mp4':
                mimeType = 'video/mp4';
                break;
            case '.avi':
                mimeType = 'video/x-msvideo';
                break;
            case '.mov':
                mimeType = 'video/quicktime';
                break;
            case '.mp3':
                mimeType = 'audio/mpeg';
                break;
            case '.wav':
                mimeType = 'audio/wav';
                break;
            case '.m4a':
                mimeType = 'audio/mp4';
                break;
            default:
                logger.warn(`${logPrefix}[ReqID:${reqId}] 未知文件类型: ${ext}`);
        }
        
        logger.info(`${logPrefix}[ReqID:${reqId}] 文件信息: 名称=${fileName}, 大小=${stats.size}字节, MIME=${mimeType}`);
        
        // 设置响应头
        res.setHeader('Content-Type', mimeType);
        res.setHeader('Content-Length', stats.size);
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
        res.setHeader('Cache-Control', 'no-cache');
        
        // 创建文件流并发送
        const fileStream = fs.createReadStream(fullPath);
        
        fileStream.on('error', (error) => {
            logger.error(`${logPrefix}[ReqID:${reqId}] 文件流读取错误:`, error);
            if (!res.headersSent) {
                res.status(500).json({
                    error: '文件读取失败',
                    message: '服务器无法读取文件'
                });
            }
        });
        
        fileStream.on('end', () => {
            logger.info(`${logPrefix}[ReqID:${reqId}] 文件下载完成: ${fileName}`);
        });
        
        // 管道文件流到响应
        fileStream.pipe(res);
        
    } catch (error) {
        logger.error(`${logPrefix}[ReqID:${reqId}] 下载处理失败:`, error);
        
        if (!res.headersSent) {
            res.status(500).json({
                error: '下载失败',
                message: '服务器内部错误',
                details: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
}

/**
 * @功能概述: 获取文件信息（不下载）
 * @参数说明:
 *   - req.query.file: 要查询的文件路径
 * @返回值: 文件信息JSON
 */
async function getFileInfo(req, res) {
    const logPrefix = '[文件：downloadController.js][getFileInfo]';
    const reqId = req.headers['x-request-id'] || 'unknown';
    
    try {
        logger.info(`${logPrefix}[ReqID:${reqId}] 收到文件信息查询请求`);
        
        const filePath = req.query.file;
        if (!filePath) {
            return res.status(400).json({
                error: '缺少文件路径参数'
            });
        }
        
        // 安全检查
        const normalizedPath = path.normalize(filePath);
        if (normalizedPath.includes('..') || normalizedPath.startsWith('/')) {
            return res.status(403).json({
                error: '不安全的文件路径'
            });
        }
        
        const fullPath = path.resolve(normalizedPath);
        
        if (!fs.existsSync(fullPath)) {
            return res.status(404).json({
                error: '文件不存在'
            });
        }
        
        const stats = fs.statSync(fullPath);
        if (!stats.isFile()) {
            return res.status(400).json({
                error: '路径不是文件'
            });
        }
        
        const ext = path.extname(fullPath).toLowerCase();
        const fileName = path.basename(fullPath);
        
        const fileInfo = {
            name: fileName,
            path: filePath,
            size: stats.size,
            extension: ext,
            created: stats.birthtime,
            modified: stats.mtime,
            isDownloadable: ['.mp4', '.avi', '.mov', '.mp3', '.wav', '.m4a'].includes(ext)
        };
        
        logger.info(`${logPrefix}[ReqID:${reqId}] 返回文件信息: ${fileName}`);
        res.json(fileInfo);
        
    } catch (error) {
        logger.error(`${logPrefix}[ReqID:${reqId}] 文件信息查询失败:`, error);
        res.status(500).json({
            error: '查询失败',
            message: '服务器内部错误'
        });
    }
}

module.exports = {
    downloadFile,
    getFileInfo
};
