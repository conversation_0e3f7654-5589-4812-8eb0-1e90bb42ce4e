# &lt;Series&gt;

## 概述

`<Series>` 组件用于轻松地将应该按顺序播放的场景拼接在一起。这是一个高阶组件，可以包含多个 `<Series.Sequence>` 实例。

**版本要求**: v2.3.1+

## 语法

```typescript
import { Series } from "remotion";

<Series>
  <Series.Sequence durationInFrames={40}>
    <Component1 />
  </Series.Sequence>
  <Series.Sequence durationInFrames={20}>
    <Component2 />
  </Series.Sequence>
</Series>
```

## 基础用法

### 1. 基础序列播放

```typescript
import { Series } from "remotion";

const Square = ({ color }: { color: string }) => (
  <div style={{
    width: 100,
    height: 100,
    backgroundColor: color,
    margin: "auto"
  }} />
);

export const Example: React.FC = () => {
  return (
    <Series>
      <Series.Sequence durationInFrames={40}>
        <Square color={"#3498db"} />
      </Series.Sequence>
      <Series.Sequence durationInFrames={20}>
        <Square color={"#5ff332"} />
      </Series.Sequence>
      <Series.Sequence durationInFrames={70}>
        <Square color={"#fdc321"} />
      </Series.Sequence>
    </Series>
  );
};
```

### 2. 文本序列展示

```typescript
import { Series } from "remotion";

const TextSlide = ({ text, color }: { text: string; color: string }) => (
  <div style={{
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    fontSize: 48,
    color,
    fontWeight: "bold"
  }}>
    {text}
  </div>
);

const TextSequence = () => {
  return (
    <Series>
      <Series.Sequence durationInFrames={60}>
        <TextSlide text="第一段" color="#e74c3c" />
      </Series.Sequence>
      <Series.Sequence durationInFrames={60}>
        <TextSlide text="第二段" color="#2ecc71" />
      </Series.Sequence>
      <Series.Sequence durationInFrames={60}>
        <TextSlide text="第三段" color="#3498db" />
      </Series.Sequence>
    </Series>
  );
};
```

## 核心属性

### Series.Sequence 属性

#### durationInFrames (必需)
- **类型**: `number | Infinity`
- **描述**: 序列应该显示的帧数
- **注意**: 只有最后一个 `<Series.Sequence>` 可以使用 `Infinity`，其他必须是正整数

#### offset (可选)
- **类型**: `number`
- **描述**: 调整序列开始时间的偏移量
  - **正数**: 延迟序列开始，创建空白间隔
  - **负数**: 提前开始序列，与前一个序列重叠

#### layout (可选)
- **类型**: `"absolute-fill" | "none"`
- **默认值**: `"absolute-fill"`
- **描述**: 布局模式
  - `"absolute-fill"`: 绝对定位，序列会重叠
  - `"none"`: 不应用布局，需要自行处理

#### style (v3.3.4+)
- **类型**: `React.CSSProperties`
- **描述**: 应用到容器的 CSS 样式
- **注意**: 当 `layout="none"` 时不可用

#### className (v3.3.45+)
- **类型**: `string`
- **描述**: 应用到容器的类名
- **注意**: 当 `layout="none"` 时不可用

#### premountFor (v4.0.140+)
- **类型**: `number`
- **描述**: 预挂载序列的帧数

#### ref (v3.3.4+)
- **类型**: `React.Ref<HTMLDivElement>`
- **描述**: React ref 引用

## 实际应用场景

### 1. 故事板序列

```typescript
import { Series } from "remotion";
import { Img, staticFile } from "remotion";

const StoryboardSequence = () => {
  const scenes = [
    { image: "scene1.jpg", duration: 90, title: "开场" },
    { image: "scene2.jpg", duration: 120, title: "介绍" },
    { image: "scene3.jpg", duration: 150, title: "主要内容" },
    { image: "scene4.jpg", duration: 60, title: "结尾" }
  ];

  return (
    <Series>
      {scenes.map((scene, index) => (
        <Series.Sequence key={index} durationInFrames={scene.duration}>
          <div style={{ position: "relative", width: "100%", height: "100%" }}>
            <Img 
              src={staticFile(scene.image)} 
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
            <div style={{
              position: "absolute",
              bottom: 20,
              left: 20,
              backgroundColor: "rgba(0,0,0,0.7)",
              color: "white",
              padding: "10px 20px",
              borderRadius: 5,
              fontSize: 24
            }}>
              {scene.title}
            </div>
          </div>
        </Series.Sequence>
      ))}
    </Series>
  );
};
```

### 2. 带偏移的重叠效果

```typescript
import { Series } from "remotion";
import { interpolate, useCurrentFrame } from "remotion";

const FadeTransition = ({ children, color }: { children: React.ReactNode; color: string }) => {
  const frame = useCurrentFrame();
  const opacity = interpolate(frame, [0, 15, 45, 60], [0, 1, 1, 0], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      opacity,
      backgroundColor: color,
      width: "100%",
      height: "100%",
      display: "flex",
      justifyContent: "center",
      alignItems: "center"
    }}>
      {children}
    </div>
  );
};

const OverlappingSequence = () => {
  return (
    <Series>
      <Series.Sequence durationInFrames={60}>
        <FadeTransition color="#e74c3c">
          <h1>第一个场景</h1>
        </FadeTransition>
      </Series.Sequence>
      
      {/* 负偏移创建重叠效果 */}
      <Series.Sequence durationInFrames={60} offset={-15}>
        <FadeTransition color="#2ecc71">
          <h1>第二个场景</h1>
        </FadeTransition>
      </Series.Sequence>
      
      <Series.Sequence durationInFrames={60} offset={-15}>
        <FadeTransition color="#3498db">
          <h1>第三个场景</h1>
        </FadeTransition>
      </Series.Sequence>
    </Series>
  );
};
```

### 3. 动态序列生成

```typescript
import { Series } from "remotion";
import { getInputProps } from "remotion";

interface SeriesConfig {
  scenes: Array<{
    component: string;
    duration: number;
    props: any;
  }>;
}

const DynamicSeries = () => {
  const { scenes } = getInputProps() as SeriesConfig;

  const renderComponent = (componentName: string, props: any) => {
    switch (componentName) {
      case "TextSlide":
        return <TextSlide {...props} />;
      case "ImageSlide":
        return <ImageSlide {...props} />;
      case "VideoSlide":
        return <VideoSlide {...props} />;
      default:
        return <div>未知组件: {componentName}</div>;
    }
  };

  return (
    <Series>
      {scenes.map((scene, index) => (
        <Series.Sequence 
          key={index} 
          durationInFrames={scene.duration}
        >
          {renderComponent(scene.component, scene.props)}
        </Series.Sequence>
      ))}
    </Series>
  );
};
```

### 4. 带间隔的序列

```typescript
import { Series } from "remotion";

const SpacedSequence = () => {
  const content = [
    { text: "内容 1", color: "#e74c3c" },
    { text: "内容 2", color: "#2ecc71" },
    { text: "内容 3", color: "#3498db" }
  ];

  return (
    <Series>
      {content.map((item, index) => (
        <React.Fragment key={index}>
          {/* 内容序列 */}
          <Series.Sequence durationInFrames={45}>
            <div style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
              backgroundColor: item.color,
              color: "white",
              fontSize: 48
            }}>
              {item.text}
            </div>
          </Series.Sequence>
          
          {/* 间隔序列（除了最后一个） */}
          {index < content.length - 1 && (
            <Series.Sequence durationInFrames={15}>
              <div style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                backgroundColor: "#34495e",
                color: "white",
                fontSize: 24
              }}>
                ...
              </div>
            </Series.Sequence>
          )}
        </React.Fragment>
      ))}
    </Series>
  );
};
```

### 5. 自定义布局序列

```typescript
import { Series } from "remotion";

const CustomLayoutSeries = () => {
  return (
    <div style={{ display: "flex", flexDirection: "column", height: "100%" }}>
      {/* 顶部固定内容 */}
      <div style={{
        height: 100,
        backgroundColor: "#2c3e50",
        color: "white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontSize: 24
      }}>
        固定标题
      </div>
      
      {/* 主要内容区域 */}
      <div style={{ flex: 1, position: "relative" }}>
        <Series>
          <Series.Sequence durationInFrames={60} layout="none">
            <div style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "#e74c3c",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: 36
            }}>
              场景 1
            </div>
          </Series.Sequence>
          
          <Series.Sequence durationInFrames={60} layout="none">
            <div style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "#2ecc71",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: 36
            }}>
              场景 2
            </div>
          </Series.Sequence>
        </Series>
      </div>
      
      {/* 底部固定内容 */}
      <div style={{
        height: 80,
        backgroundColor: "#34495e",
        color: "white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontSize: 18
      }}>
        固定底部信息
      </div>
    </div>
  );
};
```

### 6. 带引用的序列控制

```typescript
import { Series } from "remotion";
import { useRef, useEffect } from "react";

const RefControlledSeries = () => {
  const firstRef = useRef<HTMLDivElement>(null);
  const secondRef = useRef<HTMLDivElement>(null);
  const thirdRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 可以通过 ref 访问和控制序列元素
    if (firstRef.current) {
      console.log("第一个序列元素:", firstRef.current);
    }
  }, []);

  return (
    <Series>
      <Series.Sequence 
        durationInFrames={40} 
        ref={firstRef}
        style={{ border: "2px solid red" }}
      >
        <div style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          backgroundColor: "#3498db",
          color: "white"
        }}>
          第一个序列
        </div>
      </Series.Sequence>
      
      <Series.Sequence 
        durationInFrames={40} 
        ref={secondRef}
        className="second-sequence"
      >
        <div style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          backgroundColor: "#e74c3c",
          color: "white"
        }}>
          第二个序列
        </div>
      </Series.Sequence>
      
      <Series.Sequence 
        durationInFrames={40} 
        ref={thirdRef}
      >
        <div style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          backgroundColor: "#2ecc71",
          color: "white"
        }}>
          第三个序列
        </div>
      </Series.Sequence>
    </Series>
  );
};
```

### 7. 预挂载优化序列

```typescript
import { Series } from "remotion";
import { useState, useEffect } from "react";

const HeavyComponent = ({ data }: { data: string }) => {
  const [processedData, setProcessedData] = useState<string>("");

  useEffect(() => {
    // 模拟重型数据处理
    const timer = setTimeout(() => {
      setProcessedData(`处理后的 ${data}`);
    }, 1000);

    return () => clearTimeout(timer);
  }, [data]);

  return (
    <div style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%",
      fontSize: 24
    }}>
      {processedData || "加载中..."}
    </div>
  );
};

const PremountedSeries = () => {
  return (
    <Series>
      <Series.Sequence durationInFrames={60}>
        <div>轻量级内容 1</div>
      </Series.Sequence>
      
      {/* 预挂载30帧，提前准备重型组件 */}
      <Series.Sequence 
        durationInFrames={60} 
        premountFor={30}
      >
        <HeavyComponent data="重型数据 1" />
      </Series.Sequence>
      
      <Series.Sequence 
        durationInFrames={60} 
        premountFor={30}
      >
        <HeavyComponent data="重型数据 2" />
      </Series.Sequence>
    </Series>
  );
};
```

## 高级用法

### 1. 嵌套 Series

```typescript
import { Series } from "remotion";

const NestedSeries = () => {
  return (
    <Series>
      {/* 第一部分：介绍 */}
      <Series.Sequence durationInFrames={120}>
        <Series>
          <Series.Sequence durationInFrames={40}>
            <div>介绍标题</div>
          </Series.Sequence>
          <Series.Sequence durationInFrames={80}>
            <div>介绍内容</div>
          </Series.Sequence>
        </Series>
      </Series.Sequence>
      
      {/* 第二部分：主要内容 */}
      <Series.Sequence durationInFrames={180}>
        <Series>
          <Series.Sequence durationInFrames={60}>
            <div>主要内容 1</div>
          </Series.Sequence>
          <Series.Sequence durationInFrames={60}>
            <div>主要内容 2</div>
          </Series.Sequence>
          <Series.Sequence durationInFrames={60}>
            <div>主要内容 3</div>
          </Series.Sequence>
        </Series>
      </Series.Sequence>
    </Series>
  );
};
```

### 2. 条件性序列

```typescript
import { Series } from "remotion";
import { getInputProps } from "remotion";

interface ConditionalProps {
  includeIntro: boolean;
  includeOutro: boolean;
  mainContentDuration: number;
}

const ConditionalSeries = () => {
  const { includeIntro, includeOutro, mainContentDuration } = getInputProps() as ConditionalProps;

  return (
    <Series>
      {includeIntro && (
        <Series.Sequence durationInFrames={60}>
          <div>介绍序列</div>
        </Series.Sequence>
      )}
      
      <Series.Sequence durationInFrames={mainContentDuration}>
        <div>主要内容</div>
      </Series.Sequence>
      
      {includeOutro && (
        <Series.Sequence durationInFrames={60}>
          <div>结尾序列</div>
        </Series.Sequence>
      )}
    </Series>
  );
};
```

## 与 Sequence 的区别

| 特性 | `<Series>` | `<Sequence>` |
|------|------------|--------------|
| 用途 | 顺序播放多个场景 | 在特定时间显示内容 |
| 时间控制 | 自动计算时间 | 手动指定 `from` |
| 嵌套 | 只能包含 `Series.Sequence` | 可以包含任何内容 |
| 重叠 | 通过 `offset` 控制 | 通过 `from` 和 `durationInFrames` |
| 适用场景 | 线性故事叙述 | 复杂时间编排 |

## 最佳实践

1. **合理使用偏移**: 使用负偏移创建平滑过渡效果
2. **预挂载优化**: 对重型组件使用 `premountFor` 提前准备
3. **布局选择**: 根据需求选择合适的 `layout` 模式
4. **引用管理**: 合理使用 ref 进行序列控制
5. **条件渲染**: 根据输入参数动态生成序列

## 相关 API

- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`<AbsoluteFill>`](./AbsoluteFill.md) - 绝对填充布局
- [`getInputProps()`](./getInputProps.md) - 获取输入属性
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/series/index.tsx)
