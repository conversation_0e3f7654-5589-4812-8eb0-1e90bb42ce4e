/**
 * @文件名: fileNameUtils.js
 * @功能概述: 文件名处理工具函数
 * @创建时间: 2025-07-17
 * @作者: Augment Agent
 * @描述: 
 *   此工具模块负责处理文件名的清理和安全化，确保文件名在不同操作系统中都能正常使用。
 *   支持中文字符，移除不安全字符，限制文件名长度，提供错误处理和回退机制。
 */

const path = require('path');
const logger = require('./logger');

// 模块日志前缀
const moduleLogPrefix = '[文件：fileNameUtils.js]';

/**
 * @功能概述: 清理文件名，移除不安全字符并确保跨平台兼容性
 * @参数说明:
 *   - filename: {string} 原始文件名，必需参数
 *   - options: {object} 可选配置参数
 *     - maxLength: {number} 最大文件名长度，默认100
 *     - preserveExtension: {boolean} 是否保留扩展名，默认true
 *     - fallbackName: {string} 清理失败时的回退名称，默认'file'
 * @返回值: {object} 包含清理结果的对象
 *   - success: {boolean} 是否清理成功
 *   - originalName: {string} 原始文件名
 *   - sanitizedName: {string} 清理后的文件名
 *   - modified: {boolean} 文件名是否被修改
 *   - reason: {string} 修改原因（如果有修改）
 * @错误处理: 清理失败时返回回退文件名，确保不会抛出异常
 * @日志记录: 记录清理过程、修改原因和结果
 */
const sanitizeFileName = (filename, options = {}) => {
    const logPrefix = `${moduleLogPrefix}[sanitizeFileName]`;
    
    try {
        // 参数验证
        if (!filename || typeof filename !== 'string') {
            logger.warn(`${logPrefix} 无效的文件名参数: ${filename}`);
            return {
                success: false,
                originalName: filename,
                sanitizedName: options.fallbackName || 'file',
                modified: true,
                reason: '无效的文件名参数'
            };
        }
        
        // 设置默认选项
        const config = {
            maxLength: options.maxLength || 100,
            preserveExtension: options.preserveExtension !== false,
            fallbackName: options.fallbackName || 'file',
            ...options
        };
        
        logger.debug(`${logPrefix} 开始清理文件名: ${filename}`);
        
        // 提取文件扩展名
        const ext = config.preserveExtension ? path.extname(filename) : '';
        const nameWithoutExt = config.preserveExtension ? path.basename(filename, ext) : filename;
        
        // 记录原始信息
        const originalName = filename;
        let sanitizedName = nameWithoutExt;
        let modified = false;
        const modifications = [];
        
        // 步骤1: 移除或替换不安全字符
        // Windows不允许的字符: < > : " | ? * \ /
        // 其他需要处理的字符: 控制字符、连续空格等
        const unsafeChars = /[<>:"|?*\\/\x00-\x1f\x7f]/g;
        if (unsafeChars.test(sanitizedName)) {
            sanitizedName = sanitizedName.replace(unsafeChars, '');
            modified = true;
            modifications.push('移除不安全字符');
        }
        
        // 步骤2: 处理连续空格和首尾空格
        const originalSpaces = sanitizedName;
        sanitizedName = sanitizedName.replace(/\s+/g, ' ').trim();
        if (sanitizedName !== originalSpaces) {
            modified = true;
            modifications.push('规范化空格');
        }
        
        // 步骤3: 移除Windows保留名称
        const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i;
        if (reservedNames.test(sanitizedName)) {
            sanitizedName = `${sanitizedName}_file`;
            modified = true;
            modifications.push('避免Windows保留名称');
        }
        
        // 步骤4: 处理以点开头或结尾的文件名
        if (sanitizedName.startsWith('.') || sanitizedName.endsWith('.')) {
            sanitizedName = sanitizedName.replace(/^\.+|\.+$/g, '');
            if (!sanitizedName) {
                sanitizedName = config.fallbackName;
            }
            modified = true;
            modifications.push('移除首尾点号');
        }
        
        // 步骤5: 检查文件名长度限制
        const maxNameLength = config.maxLength - ext.length;
        if (sanitizedName.length > maxNameLength) {
            // 智能截断：优先保留前面的内容，但尝试在合适的位置截断
            const truncatePoint = findGoodTruncatePoint(sanitizedName, maxNameLength);
            sanitizedName = sanitizedName.substring(0, truncatePoint);
            modified = true;
            modifications.push(`截断长度至${maxNameLength}字符`);
        }
        
        // 步骤6: 确保文件名不为空
        if (!sanitizedName) {
            sanitizedName = config.fallbackName;
            modified = true;
            modifications.push('使用回退名称');
        }
        
        // 步骤7: 重新组合文件名和扩展名
        const finalName = sanitizedName + ext;
        
        // 构建结果对象
        const result = {
            success: true,
            originalName: originalName,
            sanitizedName: finalName,
            modified: modified,
            reason: modified ? modifications.join(', ') : '无需修改'
        };
        
        // 记录处理结果
        if (modified) {
            logger.info(`${logPrefix} 文件名已修改: "${originalName}" → "${finalName}" (${result.reason})`);
        } else {
            logger.debug(`${logPrefix} 文件名无需修改: "${originalName}"`);
        }
        
        return result;
        
    } catch (error) {
        logger.error(`${logPrefix} 文件名清理失败: ${error.message}`);
        
        // 返回回退结果
        const fallbackExt = path.extname(filename || '');
        const fallbackName = (options.fallbackName || 'file') + fallbackExt;
        
        return {
            success: false,
            originalName: filename,
            sanitizedName: fallbackName,
            modified: true,
            reason: `清理失败: ${error.message}`
        };
    }
};

/**
 * @功能概述: 寻找合适的截断点，避免在字符中间截断
 * @参数说明:
 *   - text: {string} 要截断的文本
 *   - maxLength: {number} 最大长度
 * @返回值: {number} 截断位置
 * @说明: 优先在空格、标点符号等位置截断，避免破坏单词或字符
 */
const findGoodTruncatePoint = (text, maxLength) => {
    if (text.length <= maxLength) {
        return text.length;
    }
    
    // 在最大长度范围内寻找合适的截断点
    const searchRange = Math.max(1, maxLength - 10); // 在最后10个字符内寻找
    
    for (let i = maxLength; i >= searchRange; i--) {
        const char = text[i];
        // 在空格、标点符号处截断
        if (/[\s\-_.,;!?()[\]{}]/.test(char)) {
            return i;
        }
    }
    
    // 如果找不到合适的截断点，直接在最大长度处截断
    return maxLength;
};

/**
 * @功能概述: 验证文件名是否安全
 * @参数说明:
 *   - filename: {string} 要验证的文件名
 * @返回值: {object} 验证结果
 *   - isValid: {boolean} 文件名是否有效
 *   - issues: {array} 发现的问题列表
 * @用途: 在保存文件前验证文件名的安全性
 */
const validateFileName = (filename) => {
    const logPrefix = `${moduleLogPrefix}[validateFileName]`;
    
    const issues = [];
    
    if (!filename || typeof filename !== 'string') {
        issues.push('文件名为空或类型无效');
    } else {
        // 检查不安全字符
        if (/[<>:"|?*\\/\x00-\x1f\x7f]/.test(filename)) {
            issues.push('包含不安全字符');
        }
        
        // 检查Windows保留名称
        const nameWithoutExt = path.basename(filename, path.extname(filename));
        if (/^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i.test(nameWithoutExt)) {
            issues.push('使用了Windows保留名称');
        }
        
        // 检查长度
        if (filename.length > 255) {
            issues.push('文件名过长');
        }
        
        // 检查首尾字符
        if (filename.startsWith('.') || filename.endsWith('.')) {
            issues.push('文件名以点号开头或结尾');
        }
    }
    
    const result = {
        isValid: issues.length === 0,
        issues: issues
    };
    
    logger.debug(`${logPrefix} 验证结果: ${filename} - ${result.isValid ? '有效' : '无效'} (${issues.join(', ')})`);
    
    return result;
};

/**
 * @功能概述: 生成安全的回退文件名
 * @参数说明:
 *   - originalName: {string} 原始文件名
 *   - timestamp: {number} 时间戳，可选
 * @返回值: {string} 安全的回退文件名
 * @用途: 当文件名清理完全失败时使用
 */
const generateFallbackName = (originalName, timestamp) => {
    const ext = path.extname(originalName || '');
    const ts = timestamp || Date.now();
    return `file_${ts}${ext}`;
};

// 导出工具函数
module.exports = {
    sanitizeFileName,
    validateFileName,
    generateFallbackName,
    findGoodTruncatePoint
};
