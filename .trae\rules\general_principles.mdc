---
type: "manual"
description: "globs:"
---
# Cursor Rules

以下规则用于指导 AI 在此项目中的行为，帮助正确定位文件、理解代码上下文并进行安全编辑：

1. 文件搜索与读取
   - 使用 `file_search`、`list_dir`、`codebase_search` 或 `grep_search` 定位所需文件或代码片段。
   - 在修改前，应使用 `read_file` 或相关工具查看目标文件的内容，确保上下文完整。

2. 代码编辑
   - 仅使用 `edit_file` 工具进行任何文件修改或创建新文件。
   - 引用或展示文件内容时，始终使用 ```start:end:filepath``` 的格式。
   - 编辑时，不要直接输出完整文件内容到对话中，必须通过工具调用实现。

3. 回复格式
   - 所有回复使用中文。
   - 文件、目录、函数名等标识符使用反引号包裹，例如 `user.php`。

4. 日志与调试
   - 在示例代码中，使用适当的日志函数添加注释性日志，帮助调试和复现问题。

5. 工具使用约定
   - 不要调用未授权或不存在的工具。
   - 优先使用语义搜索工具定位需要的代码，然后再进行细化操作。

6. 遵循对话指令

   - 严格遵循对话中提供的所有额外指南或指令。 