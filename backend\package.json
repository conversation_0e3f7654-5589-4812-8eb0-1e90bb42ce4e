{"name": "wordpress-api-service", "version": "1.0.0", "description": "Express API Service for WordPress", "main": "app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "remotion": "remotion studio src/remotion/index.ts", "remotion:render": "remotion render src/remotion/index.ts"}, "keywords": ["wordpress", "api", "express"], "author": "", "license": "ISC", "dependencies": {"@remotion/cli": "4.0.331", "@remotion/eslint-plugin": "4.0.331", "@remotion/renderer": "4.0.331", "axios": "^1.9.0", "canvas": "^3.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^4.17.1", "fluent-ffmpeg": "2.1.2", "media-captions": "^0.0.18", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "multer": "^1.4.5-lts.2", "mysql2": "^2.3.3", "node-fetch": "^3.3.2", "playwright": "^1.54.1", "puppeteer": "^24.14.0", "remotion": "4.0.331", "subtitle": "^4.2.2-alpha.0", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^19.1.9", "ajv": "^8.17.1", "form-data": "^4.0.3", "jest": "^30.0.5", "nodemon": "^3.1.10", "typescript": "^5.9.2"}}