/**
 * @功能概述: 双语字幕数据合并与增强任务，将英文字幕、中文字幕和挖空字幕合并，并通过LLM生成词汇解释。
 *           此任务结合数据处理和LLM交互，生成包含词汇解释的增强双语字幕格式。
 * @输入依赖: context.videoIdentifier (string, 必需) - 视频标识符，用于文件命名和日志追踪。
 *           context.simplifiedSubtitleJsonArray (Array, 必需) - 来自TranscriptionCorrectionTask的英文字幕JSON数组，包含words字段。
 *           context.translatedSubtitleJsonArray (Array, 必需) - 来自TranslateSubtitleTask的中文字幕JSON数组，包含words字段。
 *           context.clozedSubtitleJsonArray (Array, 必需) - 来自SubtitleClozeTask的挖空字幕JSON数组，包含words字段。
 *           context.savePath (string, 必需) - 文件保存路径。
 *           context.correctedFullText (string, 可选) - 完整上下文文本。
 * @输出结果: context.enhancedBilingualSubtitleJsonArray (Array) - 增强后的双语字幕JSON数组。
 *           context.enhancedBilingualSubtitleJsonPath (string) - 保存增强双语字幕JSON文件的路径。
 * @数据格式: 输入格式：[{id, start, end, text, words}, ...]
 *           输出格式：[{id, start, end, text_english, text_chinese, text_clozed, words_explanation, cloze_words}, ...]
 * @处理逻辑: 1. 验证输入数据的完整性和一致性
 *           2. 合并三种字幕数据为统一格式
 *           3. 通过LLM生成词汇解释（遵循LLM API标准）
 *           4. 智能校验和修复LLM输出
 *           5. 保存增强后的JSON文件
 * @错误处理: 数据不匹配时进行智能修复，LLM输出异常时重试，确保输出数据的完整性
 * @性能特点: 结合数据处理和LLM交互，生成高质量的学习材料，使用增强API配置提升稳定性
 */

const TaskBase = require('../class/TaskBase');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const logger = require('../utils/logger');
const llmService = require('../services/llmService');
const jsonValidator = require('../utils/jsonValidator');
const fileSaver = require('../utils/fileSaver');
const fs = require('fs').promises;
const path = require('path');

/**
 * @class BilingualSubtitleMergeTask
 * @extends TaskBase
 * @description 双语字幕合并与增强任务类，负责将英文、中文和挖空字幕合并，并通过LLM生成词汇解释
 */
class BilingualSubtitleMergeTask extends TaskBase {
    /**
     * @constructor
     * @description 创建双语字幕合并与增强任务实例
     */
    constructor() {
        super('BilingualSubtitleMergeTask');

        // 任务模块日志前缀
        this.taskModuleLogPrefix = '[文件：BilingualSubtitleMergeTask.js][双语字幕合并增强任务][模块初始化]';
        logger.info(`${this.taskModuleLogPrefix}模块已加载。`);
    }

    /**
     * @功能概述: 执行双语字幕合并与增强任务的核心逻辑。
     * @param {object} context - 上下文对象，期望包含:
     *                           - videoIdentifier: {string} 视频标识符 (必需)。
     *                           - simplifiedSubtitleJsonArray: {Array} 英文字幕JSON数组，包含words字段 (必需)。
     *                           - translatedSubtitleJsonArray: {Array} 中文字幕JSON数组，包含words字段 (必需)。
     *                           - clozedSubtitleJsonArray: {Array} 挖空字幕JSON数组，包含words字段 (必需)。
     *                           - savePath: {string} 文件保存路径 (必需)。
     *                           - correctedFullText: {string} 完整上下文文本 (可选)。
     *                           - reqId: {string} 请求ID (可选)。
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度。
     * @returns {Promise<object>} 包含增强双语字幕JSON数组和文件路径的对象。
     * @throws {Error} 如果参数校验失败、数据不匹配、LLM调用失败或文件保存失败时，则抛出错误。
     * @执行流程:
     *   1. 初始化日志和进度报告。
     *   2. 验证必需的输入参数。
     *   3. 验证输入数据的完整性和一致性。
     *   4. 执行数据合并操作。
     *   5. 通过LLM生成词汇解释（使用增强API配置）。
     *   6. 智能校验和修复LLM输出。
     *   7. 保存增强后的双语字幕JSON文件。
     *   8. 标记任务完成并返回结果。
     */
    async execute(context, progressCallback) {
        // 步骤 1: 从上下文中解构出核心参数
        const {
            videoIdentifier,
            simplifiedSubtitleJsonArray,
            translatedSubtitleJsonArray,
            clozedSubtitleJsonArray,
            savePath,
            correctedFullText,
            reqId
        } = context;

        // 步骤 2: 定义任务执行所必需的字段列表
        const requiredFields = [
            'videoIdentifier',
            'simplifiedSubtitleJsonArray',
            'translatedSubtitleJsonArray',
            'clozedSubtitleJsonArray',
            'savePath'
        ];

        // 步骤 3: 构建执行日志前缀
        const execLogPrefix = `[文件：BilingualSubtitleMergeTask.js][双语字幕合并增强任务][${videoIdentifier || 'unknown_video'}][REQ:${reqId || 'unknown'}]`;

        // 步骤 4: 验证必需的上下文参数
        this.validateRequiredFields(context, requiredFields, execLogPrefix);

        // 步骤 5: 设置进度回调并开始任务
        this.setProgressCallback(progressCallback);
        this.start();

        let enhancedBilingualSubtitleJsonArray = null;
        let enhancedBilingualSubtitleJsonPath = null;

        try {
            // 记录上下文参数验证通过的日志
            logger.debug(`${execLogPrefix}[参数验证] 上下文参数验证通过，英文字幕条目数：${simplifiedSubtitleJsonArray.length}，中文字幕条目数：${translatedSubtitleJsonArray.length}，挖空字幕条目数：${clozedSubtitleJsonArray.length}`);

            // 阶段1: 任务初始化
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: `开始双语字幕合并增强任务 [${videoIdentifier}]`,
                current: 10,
                total: 100
            });
            logger.info(`${execLogPrefix}[阶段1/6] 任务初始化完成`);

            // 阶段2: 数据验证和预处理
            logger.info(`${execLogPrefix}[阶段2/6] 开始数据验证和预处理`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `验证输入数据的完整性和一致性`,
                current: 20,
                total: 100
            });

            // 验证输入数据（更新的数据格式）
            this.validateInputData(simplifiedSubtitleJsonArray, translatedSubtitleJsonArray, clozedSubtitleJsonArray, execLogPrefix);
            logger.info(`${execLogPrefix}[阶段2/6] 数据验证完成`);

            // 阶段3: 执行数据合并
            logger.info(`${execLogPrefix}[阶段3/6] 开始执行三种字幕数据合并`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `合并英文、中文和挖空字幕数据`,
                current: 35,
                total: 100
            });

            const mergedSubtitleJsonArray = this.mergeSubtitleData(
                simplifiedSubtitleJsonArray,
                translatedSubtitleJsonArray,
                clozedSubtitleJsonArray,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[阶段3/6] 数据合并完成，生成 ${mergedSubtitleJsonArray.length} 个合并字幕条目`);

            // 阶段4: 通过LLM生成词汇解释（使用标准化API）
            logger.info(`${execLogPrefix}[阶段4/6] 开始通过LLM生成词汇解释`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `调用LLM生成词汇解释`,
                current: 50,
                total: 100
            });

            enhancedBilingualSubtitleJsonArray = await this.enhanceWithLLM(
                mergedSubtitleJsonArray,
                correctedFullText,
                videoIdentifier,
                reqId,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[阶段4/6] LLM增强完成，生成 ${enhancedBilingualSubtitleJsonArray.length} 个增强字幕条目`);

            // 阶段5: 智能校验和修复
            logger.info(`${execLogPrefix}[阶段5/6] 开始智能校验和修复`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `校验和修复LLM输出`,
                current: 70,
                total: 100
            });

            // 这里可以添加额外的校验逻辑，目前LLM调用中已包含校验
            logger.info(`${execLogPrefix}[阶段5/6] 校验和修复完成`);

            // 阶段6: 保存增强双语字幕JSON文件
            logger.info(`${execLogPrefix}[阶段6/6] 开始保存增强双语字幕JSON文件`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING, {
                detail: `保存增强双语字幕JSON文件`,
                current: 85,
                total: 100
            });

            enhancedBilingualSubtitleJsonPath = await this.saveEnhancedBilingualSubtitleJson(
                enhancedBilingualSubtitleJsonArray,
                context.dynamicVideoTitleForFile || videoIdentifier,
                savePath,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[阶段6/6] 增强双语字幕JSON文件已保存到: ${enhancedBilingualSubtitleJsonPath}`);

            // 准备最终返回结果
            const result = {
                enhancedBilingualSubtitleJsonArray: enhancedBilingualSubtitleJsonArray,
                enhancedBilingualSubtitleJsonPath: enhancedBilingualSubtitleJsonPath,
                bilingualSubtitleMergeTaskStatus: 'success'
            };

            // 标记任务完成
            this.reportProgress(TASK_STATUS.COMPLETED, null, {
                detail: `双语字幕合并增强任务完成`,
                current: 100,
                total: 100
            });

            this.result = result;
            this.complete(result);
            logger.info(`${execLogPrefix}[execute] BilingualSubtitleMergeTask 成功完成，总耗时：${this.getElapsedTime()}ms`);
            return result;

        } catch (error) {
            // 捕获任务执行过程中发生的任何错误
            logger.error(`${execLogPrefix}[ERROR] 任务执行失败，错误信息：${error.message}，堆栈：${error.stack}`);
            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出错误
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (context[field] === undefined || context[field] === null) {
                const errorMsg = `执行失败：上下文缺少必需字段 "${field}"`;
                logger.error(`${execLogPrefix}[字段校验] ${errorMsg}`);
                this.fail(new Error(errorMsg));
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix}[字段校验] 所有必需字段 (${requiredFields.join(', ')}) 验证通过。`);
    }

    /**
     * @功能概述: 验证输入数据的完整性和一致性（更新的数据格式）
     * @param {Array} englishSubtitles - 英文字幕数组，包含words字段
     * @param {Array} chineseSubtitles - 中文字幕数组，包含words字段
     * @param {Array} clozedSubtitles - 挖空字幕数组，包含words字段
     * @param {string} execLogPrefix - 日志前缀
     * @throws {Error} 如果数据验证失败
     */
    validateInputData(englishSubtitles, chineseSubtitles, clozedSubtitles, execLogPrefix) {
        // 验证数组类型
        if (!Array.isArray(englishSubtitles)) {
            throw new Error(`英文字幕数据必须是数组类型，当前类型：${typeof englishSubtitles}`);
        }
        if (!Array.isArray(chineseSubtitles)) {
            throw new Error(`中文字幕数据必须是数组类型，当前类型：${typeof chineseSubtitles}`);
        }
        if (!Array.isArray(clozedSubtitles)) {
            throw new Error(`挖空字幕数据必须是数组类型，当前类型：${typeof clozedSubtitles}`);
        }

        // 验证数组长度
        if (englishSubtitles.length === 0) {
            throw new Error('英文字幕数组不能为空');
        }
        if (chineseSubtitles.length === 0) {
            throw new Error('中文字幕数组不能为空');
        }
        if (clozedSubtitles.length === 0) {
            throw new Error('挖空字幕数组不能为空');
        }

        // 验证数组长度一致性（允许轻微差异，但应该基本一致）
        const maxLength = Math.max(englishSubtitles.length, chineseSubtitles.length, clozedSubtitles.length);
        const minLength = Math.min(englishSubtitles.length, chineseSubtitles.length, clozedSubtitles.length);
        const lengthDifference = maxLength - minLength;
        
        if (lengthDifference > 2) {
            logger.warn(`${execLogPrefix}[数据验证] 字幕数组长度差异较大 - 英文:${englishSubtitles.length}, 中文:${chineseSubtitles.length}, 挖空:${clozedSubtitles.length}`);
        }

        // 验证增强字幕结构（包含words字段）
        this.validateEnhancedSubtitleStructure(englishSubtitles, '英文字幕', execLogPrefix);
        this.validateEnhancedSubtitleStructure(chineseSubtitles, '中文字幕', execLogPrefix);
        
        // 验证挖空字幕结构（words字段包含挖空词汇）
        this.validateClozedSubtitleStructure(clozedSubtitles, '挖空字幕', execLogPrefix);

        logger.info(`${execLogPrefix}[数据验证] 输入数据验证通过 - 英文:${englishSubtitles.length}, 中文:${chineseSubtitles.length}, 挖空:${clozedSubtitles.length}`);
    }

    /**
     * @功能概述: 验证增强字幕条目的数据结构（包含words字段）
     * @param {Array} subtitles - 字幕数组
     * @param {string} type - 字幕类型（用于日志）
     * @param {string} execLogPrefix - 日志前缀
     * @throws {Error} 如果结构验证失败
     */
    validateEnhancedSubtitleStructure(subtitles, type, execLogPrefix) {
        const requiredFields = ['id', 'start', 'end', 'text', 'words'];
        
        for (let i = 0; i < Math.min(subtitles.length, 5); i++) { // 只验证前5个条目以提高性能
            const subtitle = subtitles[i];
            
            if (!subtitle || typeof subtitle !== 'object') {
                throw new Error(`${type}第${i + 1}个条目必须是对象类型`);
            }

            for (const field of requiredFields) {
                if (!(field in subtitle)) {
                    throw new Error(`${type}第${i + 1}个条目缺少必需字段：${field}`);
                }
            }

            // 验证时间字段类型
            if (typeof subtitle.start !== 'number' || typeof subtitle.end !== 'number') {
                throw new Error(`${type}第${i + 1}个条目的start和end字段必须是数字类型`);
            }

            // 验证时间逻辑
            if (subtitle.start >= subtitle.end) {
                throw new Error(`${type}第${i + 1}个条目的开始时间必须小于结束时间`);
            }

            // 验证words字段类型
            if (!Array.isArray(subtitle.words)) {
                throw new Error(`${type}第${i + 1}个条目的words字段必须是数组类型`);
            }
        }

        logger.debug(`${execLogPrefix}[结构验证] ${type}数据结构验证通过`);
    }

    /**
     * @功能概述: 验证挖空字幕条目的数据结构
     * @param {Array} subtitles - 挖空字幕数组
     * @param {string} type - 字幕类型（用于日志）
     * @param {string} execLogPrefix - 日志前缀
     * @throws {Error} 如果结构验证失败
     */
    validateClozedSubtitleStructure(subtitles, type, execLogPrefix) {
        const requiredFields = ['id', 'start', 'end', 'text', 'words'];

        for (let i = 0; i < Math.min(subtitles.length, 5); i++) { // 只验证前5个条目以提高性能
            const subtitle = subtitles[i];

            if (!subtitle || typeof subtitle !== 'object') {
                throw new Error(`${type}第${i + 1}个条目必须是对象类型`);
            }

            for (const field of requiredFields) {
                if (!(field in subtitle)) {
                    throw new Error(`${type}第${i + 1}个条目缺少必需字段：${field}`);
                }
            }

            // 验证时间字段类型
            if (typeof subtitle.start !== 'number' || typeof subtitle.end !== 'number') {
                throw new Error(`${type}第${i + 1}个条目的start和end字段必须是数字类型`);
            }

            // 验证时间逻辑
            if (subtitle.start >= subtitle.end) {
                throw new Error(`${type}第${i + 1}个条目的开始时间必须小于结束时间`);
            }

            // 验证words字段类型（挖空字幕的words是简单字符串数组）
            if (!Array.isArray(subtitle.words)) {
                throw new Error(`${type}第${i + 1}个条目的words字段必须是数组类型`);
            }
        }

        logger.debug(`${execLogPrefix}[结构验证] ${type}数据结构验证通过`);
    }

    /**
     * @功能概述: 合并三种字幕数据为符合提示词模板要求的格式
     * @param {Array} englishSubtitles - 英文字幕数组（simplifiedSubtitleJsonArray）
     * @param {Array} chineseSubtitles - 中文字幕数组（translatedSubtitleJsonArray）
     * @param {Array} clozedSubtitles - 挖空字幕数组（clozedSubtitleJsonArray）
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Array} 合并后的字幕数组，包含提示词模板所需的7个字段
     */
    mergeSubtitleData(englishSubtitles, chineseSubtitles, clozedSubtitles, execLogPrefix) {
        const mergedSubtitles = [];
        const maxLength = Math.max(englishSubtitles.length, chineseSubtitles.length, clozedSubtitles.length);

        logger.info(`${execLogPrefix}[数据合并] 开始合并三种字幕数据，最大长度：${maxLength}`);
        logger.debug(`${execLogPrefix}[数据合并] 数据源长度 - 英文:${englishSubtitles.length}, 中文:${chineseSubtitles.length}, 挖空:${clozedSubtitles.length}`);

        for (let i = 0; i < maxLength; i++) {
            const englishItem = englishSubtitles[i];
            const chineseItem = chineseSubtitles[i];
            const clozedItem = clozedSubtitles[i];

            // 按照提示词模板要求构建合并条目
            if (englishItem) {
                const mergedItem = {
                    // id, start, end 来自 simplifiedSubtitleJsonArray
                    id: englishItem.id || `${i + 1}`,
                    start: englishItem.start || 0,
                    end: englishItem.end || 0,
                    
                    // text_english 来自 simplifiedSubtitleJsonArray 的 text 字段
                    text_english: englishItem.text || '',
                    
                    // text_chinese 来自 translatedSubtitleJsonArray 的 text 字段
                    text_chinese: chineseItem?.text || '',
                    
                    // text_clozed 来自 clozedSubtitleJsonArray 的 text 字段
                    text_clozed: clozedItem?.text || '',
                    
                    // words 来自 clozedSubtitleJsonArray 的 words 字段（数组）
                    words: clozedItem?.words || []
                };

                mergedSubtitles.push(mergedItem);
            } else if (chineseItem || clozedItem) {
                // 如果英文字幕缺失但其他字幕存在，创建占位条目
                const baseItem = chineseItem || clozedItem;
                const mergedItem = {
                    id: baseItem.id || `${i + 1}`,
                    start: baseItem.start || 0,
                    end: baseItem.end || 0,
                    text_english: '',
                    text_chinese: chineseItem?.text || '',
                    text_clozed: clozedItem?.text || '',
                    words: clozedItem?.words || []
                };

                mergedSubtitles.push(mergedItem);
            }
        }

        logger.info(`${execLogPrefix}[数据合并] 合并完成，生成 ${mergedSubtitles.length} 个合并条目`);
        
        // 记录第一个合并条目示例用于验证
        if (mergedSubtitles.length > 0) {
            logger.debug(`${execLogPrefix}[数据合并] 第一个合并条目示例：${JSON.stringify(mergedSubtitles[0], null, 2)}`);
            
            // 验证关键字段
            const firstItem = mergedSubtitles[0];
            logger.debug(`${execLogPrefix}[数据合并] 字段验证 - id:${firstItem.id}, text_english:${firstItem.text_english?.substring(0, 50)}..., text_chinese:${firstItem.text_chinese?.substring(0, 30)}..., words:${JSON.stringify(firstItem.words)}`);
            
            // 特别检查words字段
            console.log(`\n🔍 [调试] 第一个合并条目的详细信息:`);
            console.log(`   id: ${firstItem.id}`);
            console.log(`   text_english: ${firstItem.text_english}`);
            console.log(`   text_chinese: ${firstItem.text_chinese}`);
            console.log(`   text_clozed: ${firstItem.text_clozed}`);
            console.log(`   words: ${JSON.stringify(firstItem.words)}`);
            console.log(`   words数量: ${firstItem.words?.length || 0}`);
        }

        return mergedSubtitles;
    }

    /**
     * @功能概述: 通过LLM增强字幕数据，生成词汇解释（遵循LLM API标准）
     * @param {Array} mergedSubtitles - 合并后的字幕数组
     * @param {string} correctedFullText - 完整上下文文本
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} reqId - 请求ID
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<Array>} 增强后的字幕数组
     */
    async enhanceWithLLM(mergedSubtitles, correctedFullText, videoIdentifier, reqId, execLogPrefix) {
        try {
            logger.info(`${execLogPrefix}[LLM增强] 开始通过LLM生成词汇解释`);

            // === 遵循LLM API标准化规范 ===
            
            // 第一步：确定任务类型
            const taskType = 'BILINGUAL_SUBTITLE_ENHANCE';
            
            // 第二步：构建标准化 options 对象
            const standardOptions = {
                // === 必需参数 ===
                promptParams: {
                    merged_subtitle_json: JSON.stringify(mergedSubtitles),
                    overall_video_context: correctedFullText || '无完整上下文文本',
                    systemPromptContent: '你是一位专业的英语学习材料制作专家，擅长为英语学习者生成准确、实用的词汇解释。严格遵守JSON格式要求，确保输出内容符合JSON格式。'
                },
                reqId: reqId || videoIdentifier,

                // === 基础配置 ===
                templateName: 'default',
                //modelName: 'google/gemini-2.5-flash-preview-05-20',
                modelName: 'google/gemini-2.5-flash-lite-preview-06-17',
                temperature: 0.2, // 降低温度，提高一致性
                max_tokens: 20000,

                // === JSON 输出控制 ===
                forceJsonOutput: true,
                validateJsonOutput: true,
                maxJsonValidationRetries: 3, // 增加重试次数

                // === 重试策略 ===
                retryCount: 3,
                retryDelay: 1000
            };

            // 第三步：添加增强功能配置（推荐）
            const enhancedOptions = {
                ...standardOptions,
                
                // === API 增强配置 ===
                apiEnhancements: {
                    // 强制 JSON Schema 输出格式 - 按照OpenRouter标准
                    structuredOutput: {
                        enabled: true,
                        // 使用OpenRouter标准的response_format格式
                        response_format: {
                            type: "json_schema",
                            json_schema: {
                                name: "bilingual_subtitle_enhancement_response",
                                strict: true, // OpenRouter推荐使用strict模式
                                schema: {
                                    type: "array",
                                    items: {
                                        type: "object",
                                        properties: {
                                            id: { 
                                                type: "string",
                                                description: "字幕条目的唯一标识符"
                                            },
                                            start: { 
                                                type: "number",
                                                description: "字幕开始时间（秒）"
                                            },
                                            end: { 
                                                type: "number",
                                                description: "字幕结束时间（秒）"
                                            },
                                            text_english: { 
                                                type: "string",
                                                description: "英文字幕文本"
                                            },
                                            text_chinese: { 
                                                type: "string",
                                                description: "中文字幕文本"
                                            },
                                            words_explanation: {
                                                type: "object",
                                                description: "为挖空词汇提供中文解释，格式为键值对：英文词汇->中文解释",
                                                additionalProperties: {
                                                    type: "string"
                                                }
                                            }
                                        },
                                        required: ["id", "start", "end", "text_english", "text_chinese", "words_explanation"],
                                        additionalProperties: false
                                    }
                                }
                            }
                        }
                    },
                    
                    // 高级重试策略
                    advancedRetry: {
                        exponentialBackoff: true,
                        jitter: true,
                        maxDelay: 30000
                    },
                    
                    // 自定义请求头
                    customHeaders: {
                        'X-Task-Type': 'BilingualSubtitleEnhancement',
                        'X-Processing-Mode': 'Enhanced',
                        'X-Schema-Version': '1.0'
                    }
                }
            };

            logger.debug(`${execLogPrefix}[LLM增强] 标准化LLM调用参数准备完成，输入字幕条目数：${mergedSubtitles.length}`);

            // 第四步：实现 LLM 进度监控
            this.reportLLMProgress('preparing', '准备LLM请求', {
                current: 20,
                total: 100,
                inputSize: mergedSubtitles.length
            });

            this.reportLLMProgress('sending', '发送LLM请求', {
                current: 40,
                total: 100,
                modelName: enhancedOptions.modelName
            });

            // 调用标准化LLM服务
            const llmResult = await llmService.callLLM(taskType, enhancedOptions);

            this.reportLLMProgress('receiving', '接收LLM响应', {
                current: 80,
                total: 100,
                responseSize: llmResult.processedText ? llmResult.processedText.length : 0
            });

            // 第五步：标准化错误处理
            if (!llmResult || llmResult.status !== 'success') {
                const errorCategory = this.categorizeError(new Error(llmResult ? llmResult.message : '未知错误'));
                logger.error(`${execLogPrefix}[${errorCategory}] LLM调用失败：${llmResult ? llmResult.message : '未知错误'}`);
                throw new Error(`LLM调用失败：${llmResult ? llmResult.message : '未知错误'}`);
            }

            logger.info(`${execLogPrefix}[LLM增强] LLM调用成功，开始解析和校验结果`);

            // 第六步：响应处理和验证
            const enhancedSubtitles = this.processLLMResponse(llmResult, execLogPrefix);

            // 智能校验和修复
            const validatedSubtitles = this.validateAndFixLLMOutput(
                enhancedSubtitles,
                mergedSubtitles,
                execLogPrefix
            );

            this.reportLLMProgress('parsing', '解析LLM响应完成', {
                current: 100,
                total: 100
            });

            logger.info(`${execLogPrefix}[LLM增强] LLM增强完成，生成 ${validatedSubtitles.length} 个增强字幕条目`);
            
            // 记录增强功能使用情况
            if (llmResult.enhancedFeatures) {
                logger.info(`${execLogPrefix}[增强功能] ${JSON.stringify(llmResult.enhancedFeatures)}`);
            }
            
            return validatedSubtitles;

        } catch (error) {
            logger.error(`${execLogPrefix}[LLM增强] LLM增强过程中出错: ${error.message}`);
            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: LLM进度报告辅助方法
     * @param {string} stage - 当前阶段
     * @param {string} detail - 详细信息
     * @param {object} progress - 进度信息
     */
    reportLLMProgress(stage, detail, progress) {
        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: `LLM处理: ${detail}`,
            current: progress.current,
            total: progress.total,
            stage: stage,
            ...progress
        });
    }

    /**
     * @功能概述: 错误分类辅助方法
     * @param {Error} error - 错误对象
     * @returns {string} 错误类别
     */
    categorizeError(error) {
        if (error.errorType) return error.errorType;
        if (error.message.includes('timeout')) return 'TIMEOUT_ERROR';
        if (error.message.includes('JSON')) return 'JSON_VALIDATION_ERROR';
        if (error.message.includes('API')) return 'API_ERROR';
        return 'UNKNOWN_ERROR';
    }

    /**
     * @功能概述: 处理LLM响应
     * @param {object} response - LLM响应对象
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Array} 解析后的数据
     */
    processLLMResponse(response, execLogPrefix) {
        // 解析 JSON 响应
        let parsedData;
        try {
            parsedData = JSON.parse(response.processedText);
        } catch (parseError) {
            throw new Error(`LLM响应JSON解析失败: ${parseError.message}`);
        }

        // 任务特定的验证逻辑
        if (!Array.isArray(parsedData)) {
            throw new Error(`LLM返回的内容不是有效的JSON数组`);
        }

        logger.debug(`${execLogPrefix}[LLM响应处理] 成功解析LLM返回的JSON数组，包含 ${parsedData.length} 个条目`);
        return parsedData;
    }

    /**
     * @功能概述: 智能校验和修复LLM输出（更新的数据格式）
     * @param {Array} llmOutput - LLM返回的数组
     * @param {Array} originalData - 原始合并数据（包含7个字段）
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Array} 校验和修复后的数组，只包含6个标准字段（id, start, end, text_english, text_chinese, words_explanation）
     */
    validateAndFixLLMOutput(llmOutput, originalData, execLogPrefix) {
        try {
            logger.debug(`${execLogPrefix}[智能校验] 开始校验LLM输出，LLM条目数：${llmOutput.length}，原始条目数：${originalData.length}`);

            // 基础数量检查
            if (llmOutput.length !== originalData.length) {
                logger.warn(`${execLogPrefix}[智能校验] 数组长度不匹配，LLM：${llmOutput.length}，原始：${originalData.length}`);
            }

            const validatedSubtitles = [];
            const requiredFields = ['id', 'start', 'end', 'text_english', 'text_chinese', 'words_explanation'];

            for (let i = 0; i < originalData.length; i++) {
                const originalItem = originalData[i];
                const llmItem = llmOutput[i];

                // 如果LLM条目不存在或无效，使用原始数据构建
                if (!llmItem || typeof llmItem !== 'object') {
                    logger.warn(`${execLogPrefix}[智能校验] 第${i + 1}个LLM条目无效，使用原始数据构建`);
                    validatedSubtitles.push(this.buildFallbackItem(originalItem));
                    continue;
                }

                // 检查必需字段
                const missingFields = requiredFields.filter(field => !(field in llmItem));
                if (missingFields.length > 0) {
                    logger.warn(`${execLogPrefix}[智能校验] 第${i + 1}个LLM条目缺少字段：${missingFields.join(', ')}，使用原始数据修复`);
                    validatedSubtitles.push(this.buildFixedItem(originalItem, llmItem));
                    continue;
                }

                // 验证words_explanation字段类型
                if (typeof llmItem.words_explanation !== 'object' || llmItem.words_explanation === null) {
                    logger.warn(`${execLogPrefix}[智能校验] 第${i + 1}个LLM条目的words_explanation字段类型错误，使用空对象修复`);
                    llmItem.words_explanation = {};
                }

                // 构建修复后的条目（只包含6个标准字段）
                const validatedItem = {
                    id: originalItem.id,
                    start: originalItem.start,
                    end: originalItem.end,
                    text_english: originalItem.text_english,
                    text_chinese: originalItem.text_chinese,
                    words_explanation: llmItem.words_explanation
                };

                validatedSubtitles.push(validatedItem);
            }

            logger.info(`${execLogPrefix}[智能校验] 校验完成，生成 ${validatedSubtitles.length} 个有效条目，每个条目包含6个标准字段`);
            return validatedSubtitles;

        } catch (error) {
            logger.error(`${execLogPrefix}[智能校验] 校验过程中出错: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 构建回退条目（当LLM条目完全无效时）
     * @param {object} originalItem - 原始条目
     * @returns {object} 回退条目，只包含6个标准字段
     */
    buildFallbackItem(originalItem) {
        return {
            id: originalItem.id,
            start: originalItem.start,
            end: originalItem.end,
            text_english: originalItem.text_english,
            text_chinese: originalItem.text_chinese,
            words_explanation: {}
        };
    }

    /**
     * @功能概述: 构建修复条目（当LLM条目部分有效时）
     * @param {object} originalItem - 原始条目
     * @param {object} llmItem - LLM条目
     * @returns {object} 修复条目，只包含6个标准字段
     */
    buildFixedItem(originalItem, llmItem) {
        return {
            id: originalItem.id,
            start: originalItem.start,
            end: originalItem.end,
            text_english: originalItem.text_english,
            text_chinese: originalItem.text_chinese,
            words_explanation: (llmItem.words_explanation && typeof llmItem.words_explanation === 'object')
                ? llmItem.words_explanation
                : {}
        };
    }

    /**
     * @功能概述: 保存增强双语字幕JSON文件
     * @param {Array} enhancedSubtitles - 增强双语字幕数组
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} savePath - 保存路径
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveEnhancedBilingualSubtitleJson(enhancedSubtitles, videoIdentifier, savePath, execLogPrefix) {
        try {
            // 生成时间戳确保文件名唯一性
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            // 构建文件名（使用时间戳避免覆盖）
            const fileName = `${videoIdentifier}_enhanced_bilingual_subtitle_${timestamp}.json`;

            // 使用fileSaver保存文件
            const filePath = fileSaver.saveDataToFile(
                JSON.stringify(enhancedSubtitles, null, 2),
                fileName,
                savePath,
                `${execLogPrefix}[保存增强双语字幕JSON]`
            );

            if (!filePath) {
                throw new Error(`保存增强双语字幕JSON文件失败: ${fileName}`);
            }

            logger.info(`${execLogPrefix}[文件保存] 增强双语字幕JSON文件已保存：${filePath}`);
            return filePath;

        } catch (error) {
            logger.error(`${execLogPrefix}[文件保存] 保存增强双语字幕JSON文件失败：${error.message}`);
            throw new Error(`保存增强双语字幕JSON文件失败：${error.message}`);
        }
    }
}

module.exports = BilingualSubtitleMergeTask;

// 任务模块日志前缀
const taskModuleLogPrefix = '[文件：BilingualSubtitleMergeTask.js][双语字幕合并增强任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}BilingualSubtitleMergeTask 类已导出。`);
