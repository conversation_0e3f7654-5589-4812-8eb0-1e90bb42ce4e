/**
 * @功能概述: 背景组件，纯渲染组件，所有样式和资源通过props传入
 * @组件类型: Remotion背景组件（数据驱动）
 * @使用场景: 教育类视频的背景层
 */

import React from 'react';
import { AbsoluteFill, staticFile } from 'remotion';

/**
 * @功能概述: 背景组件（纯渲染器）
 * @param {Object} props - 组件属性
 * @param {Object} props.config - 背景配置对象（来自template）
 * @returns {JSX.Element} 背景组件
 */
const Background = ({ config = {} }) => {
    const { type, source, pattern, overlay, fallback } = config;

    // 根据配置类型渲染不同背景
    let backgroundStyle = {};

    if (type === 'image' && source) {
        // 图片背景
        backgroundStyle = {
            backgroundColor: fallback?.color,
            backgroundImage: `url(${staticFile(source)})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
        };
    } else if (type === 'pattern' && pattern) {
        // 图案背景（如报纸纹理）
        backgroundStyle = {
            backgroundColor: fallback?.color,
            backgroundImage: pattern.svgData ? `url("${pattern.svgData}")` : undefined,
            backgroundSize: pattern.size || '100px 100px',
            backgroundPosition: pattern.position || 'center',
            backgroundRepeat: pattern.repeat || 'repeat'
        };
    } else {
        // 纯色背景
        backgroundStyle = {
            backgroundColor: fallback?.color || '#1a1a2e'
        };
    }

    return (
        <AbsoluteFill style={backgroundStyle}>
            {/* 遮罩层 */}
            {overlay?.enabled && (
                <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: overlay.color,
                    opacity: overlay.opacity
                }} />
            )}
        </AbsoluteFill>
    );
};

export default Background;