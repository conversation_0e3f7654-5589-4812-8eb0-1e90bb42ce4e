# 项目管理接口文档

## 📋 接口概述

**功能模块**: 项目管理和视频选择
**接口分类**: video
**开发状态**: ✅ 已完成并测试通过
**最后更新**: 2025-07-26
**架构版本**: 新项目目录架构
**新增功能**: 项目删除API、显示所有项目、Generated文件查询、视频标注管理

---

## 🌐 基本信息

| 项目 | 内容 |
|------|------|
| **API前缀** | `/api/video` |
| **请求格式** | `application/json` |
| **响应格式** | `JSON` |
| **服务器地址** | `http://localhost:8081` |

---

## 📚 接口列表

| 序号 | 接口名称 | 方法 | 路径 | 功能描述 | 状态 |
|------|----------|------|------|----------|------|
| 1 | 获取项目列表 | GET | `/listProjects` | 获取已上传项目列表，支持分页排序 | 🔄 已更新 |
| 2 | 获取项目详情 | GET | `/projectDetails` | 获取项目详细信息和editorData | ✅ 已完成 |
| 3 | 删除项目 | DELETE | `/deleteProject` | 安全删除项目及所有文件 | 🆕 新增 |
| 4 | 查询Generated文件 | GET | `/listGeneratedFiles/:projectId` | 查询项目生成的视频和字幕文件 | 🆕 新增 |
| 5 | 更新视频标注 | PUT | `/updateVideoTags/:projectId` | 更新视频发布状态和标注 | 🆕 新增 |
| 6 | 获取视频标注 | GET | `/getVideoTags/:projectId` | 获取视频标注信息 | 🆕 新增 |
| 7 | 重命名项目文件 | PUT | `/renameProject` | 修改项目原始视频文件名 | 🆕 新增 |

### 1. 获取项目列表 🔄 **已更新**

#### 基本信息
- **接口路径**: `GET /api/video/listProjects`
- **功能描述**: 获取已上传项目列表，支持分页和排序，**现在显示所有项目（包括无视频文件的项目）**
- **控制器**: `listProjectsController.js`
- **更新内容**:
  - ✅ 新增 `hasOriginalVideo` 字段标识项目是否包含视频文件
  - ✅ 显示所有项目目录，不再过滤无视频文件的项目
  - ✅ 无视频文件的项目显示为"无视频文件"，便于管理和删除

#### 请求参数
| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `page` | number | 否 | 1 | 页码，从1开始 |
| `pageSize` | number | 否 | 10 | 每页数量，最大50 |
| `sortBy` | string | 否 | uploadTime | 排序字段 |
| `order` | string | 否 | desc | 排序方向：asc/desc |

#### 请求示例
```javascript
// 获取第一页项目列表
GET /api/video/listProjects

// 获取第二页，每页20个，按上传时间升序
GET /api/video/listProjects?page=2&pageSize=20&sortBy=uploadTime&order=asc
```

#### 响应格式
```javascript
{
  "status": "success",
  "message": "项目列表获取成功",
  "data": {
    "projects": [
      {
        "videoIdentifier": "videoFile-1752647744179-850825533",
        "originalVideoName": "测试视频.mp4",
        "uploadTime": "2025-07-16T06:35:44.000Z",
        "videoPlaybackUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/source/original.mp4",
        "hasProcessedData": true,
        "projectStatus": "completed", // completed/processed/uploaded/unknown
        "fileSize": "2.1MB",
        "lastModified": "2025-07-16T06:35:44.000Z",
        "hasOriginalVideo": true // 🆕 新增字段：标识是否包含视频文件
      },
      {
        "videoIdentifier": "videoFile-1752712798935-836368957",
        "originalVideoName": "无视频文件", // 🆕 无视频文件的项目显示
        "uploadTime": "2025-07-17T02:15:44.000Z",
        "videoPlaybackUrl": null, // 🆕 无视频文件时为null
        "hasProcessedData": false,
        "projectStatus": "uploaded",
        "fileSize": "0 B", // 🆕 无视频文件时为0 B
        "lastModified": "2025-07-17T02:15:44.000Z",
        "hasOriginalVideo": false // 🆕 明确标识无视频文件
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "pageSize": 10,
      "totalPages": 2
    }
  }
}
```

#### 项目状态说明
| 状态 | 说明 |
|------|------|
| `completed` | 已完成生成，有generated文件 |
| `processed` | 已处理，有processed文件 |
| `uploaded` | 仅上传，只有source文件 |
| `unknown` | 状态未知或异常 |

---

### 2. 获取项目详情

#### 基本信息
- **接口路径**: `GET /api/video/projectDetails`
- **功能描述**: 获取项目详细信息，包括完整的editorData格式数据
- **控制器**: `projectDetailsController.js`

#### 请求参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `videoIdentifier` | string | 是 | 项目标识符 |

#### 请求示例
```javascript
GET /api/video/projectDetails?videoIdentifier=videoFile-1752647744179-850825533
```

#### 响应格式
```javascript
{
  "status": "success",
  "message": "项目详情获取成功",
  "data": {
    "videoIdentifier": "videoFile-1752647744179-850825533",
    "editorData": {
      // 与前端uploadVideo函数中的editorData格式完全一致
      "videoPlaybackUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/source/original.mp4",
      "videoIdentifier": "videoFile-1752647744179-850825533",
      "originalVideoPath": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/source/original.mp4",
      "originalVideoName": "测试视频.mp4",
      "englishSrtContent": "1\n00:00:00,000 --> 00:00:03,000\nHello world\n\n2\n00:00:03,000 --> 00:00:06,000\nThis is a test\n",
      "audioFileUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/processed/audio.mp3",
      "transcriptionJsonUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/processed/transcription.json",
      "englishSrtUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/processed/english.srt",
      "chineseSrtUrl": "http://localhost:8081/api/files/projects/videoFile-1752647744179-850825533/processed/chinese.srt"
    },
    "projectInfo": {
      "uploadTime": "2025-07-16T06:35:44.000Z",
      "originalVideoName": "测试视频.mp4",
      "lastModified": "2025-07-16T06:35:44.000Z",
      "fileSize": "2.1MB",
      "hasProcessedFiles": true,
      "hasGeneratedFiles": false
    },
    "dataIntegrity": {
      "isComplete": true,
      "missingFields": [],
      "hasEnglishSubtitle": true,
      "hasAudioFile": true,
      "hasTranscription": true
    }
  }
}
```

---

### 3. 删除项目 🆕 **新增接口**

#### 基本信息
- **接口路径**: `DELETE /api/video/deleteProject`
- **功能描述**: 安全删除指定项目及其所有相关文件和目录
- **控制器**: `deleteProjectController.js`
- **安全特性**:
  - ✅ 路径遍历攻击防护
  - ✅ 参数格式验证
  - ✅ 完整的错误处理
  - ✅ 详细的操作日志记录

#### 请求参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `videoIdentifier` | string | 是 | 要删除的项目标识符（通过请求体传递） |

#### 请求示例
```javascript
// 删除指定项目
DELETE /api/video/deleteProject
Content-Type: application/json

{
  "videoIdentifier": "videoFile-1752647744179-850825533"
}
```

#### 响应格式

##### 成功响应
```javascript
{
  "status": "success",
  "message": "项目删除成功",
  "data": {
    "deletedProject": "videoFile-1752647744179-850825533",
    "deletionInfo": {
      "projectDir": "/path/to/project/directory",
      "deletedFiles": 15,
      "deletedSize": "2.1 MB",
      "deletionTime": "2025-07-17T09:15:30.123Z"
    }
  }
}
```

##### 错误响应示例

**项目不存在 (404)**
```javascript
{
  "status": "error",
  "message": "项目不存在",
  "errorCode": "PROJECT_NOT_FOUND",
  "details": {
    "videoIdentifier": "invalid-project-id",
    "suggestion": "请检查项目标识符是否正确"
  }
}
```

**参数错误 (400)**
```javascript
{
  "status": "error",
  "message": "缺少必需参数：videoIdentifier",
  "errorCode": "MISSING_PARAMETER",
  "details": {
    "requiredParameter": "videoIdentifier",
    "suggestion": "请在请求体中提供videoIdentifier参数"
  }
}
```

**安全验证失败 (400)**
```javascript
{
  "status": "error",
  "message": "无效的项目标识符：包含非法字符",
  "errorCode": "INVALID_PARAMETER",
  "details": {
    "parameter": "videoIdentifier",
    "reason": "项目标识符不能包含路径分隔符或相对路径字符",
    "suggestion": "请使用有效的项目标识符"
  }
}
```

#### 安全机制
1. **路径验证**: 防止 `../` 等路径遍历攻击
2. **字符检查**: 禁止包含 `/`、`\`、`..` 等危险字符
3. **存在性验证**: 确保项目存在后再执行删除
4. **完整性验证**: 删除后验证目录是否完全移除
5. **操作审计**: 记录删除操作的详细信息

#### 删除范围
删除操作将完全移除以下内容：
- 项目根目录及其所有子目录
- `source/` 目录中的原始视频文件
- `processed/` 目录中的处理文件（音频、字幕、转录等）
- `generated/` 目录中的生成文件（最终视频等）
- 所有相关的临时文件和缓存

---

## 🚨 错误处理

### 常见错误响应

#### 1. 参数错误
```javascript
{
  "status": "error",
  "message": "缺少必需参数: videoIdentifier",
  "errorCode": "MISSING_PARAMETER",
  "details": {
    "parameter": "videoIdentifier",
    "suggestion": "请提供有效的项目标识符"
  }
}
```

#### 2. 项目不存在
```javascript
{
  "status": "error",
  "message": "项目不存在",
  "errorCode": "PROJECT_NOT_FOUND",
  "details": {
    "videoIdentifier": "invalid-id",
    "suggestion": "请检查项目ID是否正确"
  }
}
```

#### 3. 服务器错误
```javascript
{
  "status": "error",
  "message": "获取项目列表失败",
  "errorCode": "LIST_PROJECTS_FAILED",
  "details": {
    "error": "具体错误信息",
    "suggestion": "请检查服务器状态或联系管理员"
  }
}
```

### HTTP状态码

| 状态码 | 说明 | 适用接口 |
|--------|------|----------|
| 200 | 请求成功 | 所有接口 |
| 400 | 请求参数错误或安全验证失败 | 所有接口，特别是删除接口的安全检查 |
| 404 | 项目不存在 | projectDetails, deleteProject |
| 500 | 服务器内部错误 | 所有接口 |

#### 删除接口特殊状态码说明
- **400**: 包含路径遍历攻击防护、参数格式验证失败
- **403**: 权限不足（如果后续添加权限控制）
- **404**: 要删除的项目不存在
- **500**: 删除操作失败、文件系统错误

---

## 🔧 前端集成

### JavaScript调用示例

#### 1. 获取项目列表
```javascript
// 使用fetch API
const response = await fetch('/api/video/listProjects?page=1&pageSize=10');
const result = await response.json();

if (result.status === 'success') {
    const projects = result.data.projects;
    const pagination = result.data.pagination;
    console.log('项目列表:', projects);
    console.log('分页信息:', pagination);
}
```

#### 2. 获取项目详情
```javascript
// 获取项目详情并直接用于编辑器
const videoIdentifier = 'videoFile-1752647744179-850825533';
const response = await fetch(`/api/video/projectDetails?videoIdentifier=${videoIdentifier}`);
const result = await response.json();

if (result.status === 'success') {
    // 直接使用editorData，格式与uploadVideo函数中的完全一致
    const editorData = result.data.editorData;
    
    // 赋值给Vue状态
    state.editorData.value = editorData;
    
    // 切换到编辑器状态
    state.activeSection.value = 'video-editor';
    
    // 触发编辑器初始化
    const initEvent = new CustomEvent('initializeVideoEditor', {
        detail: { editorData, timestamp: Date.now() }
    });
    window.dispatchEvent(initEvent);
}
```

#### 3. 删除项目 🆕
```javascript
// 删除项目（带确认对话框）
const deleteProject = async (project) => {
    try {
        // 确认删除操作
        const confirmResult = await ElementPlus.ElMessageBox.confirm(
            `确定要删除项目 "${project.originalVideoName}" 吗？\n\n此操作将永久删除项目的所有文件，无法恢复。`,
            '确认删除',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        if (confirmResult !== 'confirm') {
            return; // 用户取消
        }

        // 发送删除请求
        const response = await fetch('/api/video/deleteProject', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                videoIdentifier: project.videoIdentifier
            })
        });

        const result = await response.json();

        if (response.ok && result.status === 'success') {
            ElMessage.success(`✅ 项目 "${project.originalVideoName}" 删除成功`);

            // 如果删除的是当前选中的项目，清除选择
            if (state.selectedProject &&
                state.selectedProject.videoIdentifier === project.videoIdentifier) {
                clearProjectSelection();
            }

            // 重新加载项目列表
            await loadProjectList();
        } else {
            ElMessage.error(`❌ 删除失败: ${result.message || '未知错误'}`);
        }

    } catch (error) {
        if (error.message === 'cancel') {
            return; // 用户取消
        }
        ElMessage.error(`❌ 删除项目失败: ${error.message}`);
    }
};
```

---

## 🎯 使用场景

### 1. "选择已有视频"功能流程 🔄 **已优化**
1. **获取项目列表**: 调用 `listProjects` 接口展示**所有项目**（包括无视频文件的项目）
2. **智能选择控制**: 前端根据 `hasOriginalVideo` 字段控制选择按钮状态
   - 有视频文件的项目：显示"选择"按钮，可点击
   - 无视频文件的项目：显示"无视频"按钮，禁用状态
3. **获取项目详情**: 调用 `projectDetails` 接口获取完整数据
4. **进入编辑器**: 使用返回的 `editorData` 直接进入编辑模式

### 2. 项目管理功能 🔄 **已增强**
- **完整项目视图**: 查看所有项目，包括上传失败或处理中断的项目
- **状态监控**: 了解项目处理状态和视频文件存在性
- **数据恢复**: 重新编辑之前处理过的项目
- **🆕 项目清理**: 删除不需要的项目，释放存储空间
- **🆕 批量管理**: 可以删除无视频文件的"垃圾"项目

### 3. 项目删除功能流程 🆕 **新增**
1. **项目识别**: 在项目列表中识别要删除的项目
2. **删除确认**: 弹出确认对话框，防止误删
3. **安全删除**: 调用 `deleteProject` 接口安全删除项目
4. **状态更新**: 删除成功后自动刷新项目列表
5. **选择清理**: 如果删除的是当前选中项目，自动清除选择状态

---

## 📊 性能特性

- **分页支持**: 避免大量数据加载
- **缓存机制**: 减少重复文件扫描
- **并发处理**: 支持多个项目并行处理
- **错误恢复**: 完善的错误处理和重试机制
- **🆕 智能过滤**: 前端根据项目状态智能控制操作按钮
- **🆕 安全删除**: 递归删除机制，确保完整清理
- **🆕 操作审计**: 详细记录删除操作，便于问题排查

---

## 🔍 技术实现

### 目录扫描机制 🔄 **已优化**
- 扫描 `backend/uploads/projects/` 目录
- **🆕 显示所有项目**: 不再过滤无视频文件的项目目录
- **🆕 状态标识**: 通过 `hasOriginalVideo` 字段标识项目状态
- 判断处理状态（检查processed/generated文件）

### 数据转换
- 收集项目文件信息
- 生成标准化URL
- 转换为前端editorData格式
- 保持与uploadVideo函数的数据格式一致

### 架构兼容
- 使用PathHelper统一路径管理
- 支持新的项目目录架构
- 保持向后兼容性

---

## 📝 开发说明

### 控制器设计
- **单一职责**: 每个控制器专注一个功能
- **错误处理**: 完善的异常捕获和错误响应
- **日志记录**: 详细的操作日志和调试信息

### 数据格式
- **标准化**: 统一的响应格式和错误码
- **兼容性**: 与现有前端代码完全兼容
- **扩展性**: 支持未来功能扩展

### 测试验证
- **功能测试**: 验证接口基本功能
- **边界测试**: 测试各种边界条件
- **错误测试**: 验证错误处理机制
- **🆕 安全测试**: 验证路径遍历攻击防护
- **🆕 集成测试**: 验证前端与后端的完整交互流程

---

## 🔄 更新日志

### 2025-07-26 更新 🆕

#### 新增功能

##### 1. Generated文件查询API 🆕
- **接口路径**: `GET /api/video/listGeneratedFiles/:projectId`
- **功能**: 查询项目generated目录中的视频和字幕文件
- **特性**: 智能配对、文件信息提取、多种排序方式
- **测试状态**: ✅ 全面测试通过

##### 2. 视频标注管理API 🆕
- **接口路径**:
  - `PUT /api/video/updateVideoTags/:projectId` - 更新标注
  - `GET /api/video/getVideoTags/:projectId` - 获取标注
- **功能**: 管理视频发布状态和标注信息
- **特性**: 批量操作、状态管理、前端集成
- **测试状态**: ✅ 全面测试通过

#### 控制器架构优化
- **废弃文件清理**: 删除了废弃的 `videoController.js` 文件
- **架构验证**: 确认所有控制器都在 `videoRoutes.js` 中正确引用
- **文档同步**: 更新API文档确保与代码实现一致

### 2025-07-17 更新

#### 新增功能

##### 1. 项目删除API 🆕
- **接口路径**: `DELETE /api/video/deleteProject`
- **功能**: 安全删除项目及其所有文件
- **安全特性**: 路径遍历攻击防护、参数验证、操作审计
- **测试状态**: ✅ 全面测试通过

##### 2. 显示所有项目功能 🔄
- **更新接口**: `GET /api/video/listProjects`
- **新增字段**: `hasOriginalVideo` (boolean)
- **功能增强**: 现在显示所有项目目录，包括无视频文件的项目
- **用户体验**: 便于项目管理和清理

### 接口字段更新

#### listProjects 接口新增字段
```javascript
{
  "hasOriginalVideo": true/false, // 🆕 标识项目是否包含视频文件
  "originalVideoName": "视频名称" | "无视频文件", // 🔄 无视频时显示特殊标识
  "videoPlaybackUrl": "URL" | null, // 🔄 无视频时为null
  "fileSize": "大小" | "0 B" // 🔄 无视频时为0 B
}
```

### 前端集成更新

#### 智能按钮控制
- **选择按钮**: 根据 `hasOriginalVideo` 字段控制启用/禁用
- **删除按钮**: 所有项目都可删除，包含确认对话框
- **状态显示**: 清晰区分有视频和无视频项目

#### 用户体验优化
- **完整项目视图**: 显示所有项目，便于管理
- **安全删除**: 防误删机制，操作确认
- **状态同步**: 删除后自动更新列表和选择状态

### 安全性增强

#### 删除接口安全机制
1. **输入验证**: 严格的参数格式检查
2. **路径防护**: 防止 `../` 等路径遍历攻击
3. **存在性验证**: 确保项目存在后再删除
4. **完整性验证**: 删除后验证操作结果
5. **操作审计**: 详细的删除日志记录

### 测试覆盖

#### 新增测试用例
- ✅ 显示所有项目功能测试
- ✅ 删除存在项目测试
- ✅ 删除不存在项目测试 (404)
- ✅ 无效参数测试 (400)
- ✅ 路径遍历攻击防护测试
- ✅ 前端集成测试
- ✅ 项目数量验证测试

#### 测试结果
```
🎯 测试总结:
- 删除存在项目: ✅ 通过
- 删除不存在项目: ✅ 通过 (正确返回404)
- 无效参数测试: ✅ 4/4 通过
- 前端集成测试: ✅ 3/3 通过
- 安全性测试: ✅ 路径遍历攻击防护有效
```

### 向后兼容性

#### API兼容性
- ✅ 现有字段保持不变
- ✅ 新增字段不影响现有功能
- ✅ 响应格式完全兼容

#### 数据兼容性
- ✅ 支持现有项目数据结构
- ✅ 新旧项目混合显示
- ✅ 现有编辑功能正常工作

### 部署说明

#### 生产环境部署
1. **代码更新**: 更新后端控制器和前端代码
2. **功能验证**: 确认所有接口正常工作
3. **安全检查**: 验证删除功能的安全机制
4. **用户培训**: 告知用户新的项目管理功能

#### 监控建议
- 监控删除操作频率和成功率
- 记录安全验证失败的尝试
- 跟踪项目列表性能指标
- 收集用户对新功能的反馈

---

### 4. 查询Generated文件 🆕 **新增**

#### 基本信息
- **接口路径**: `GET /api/video/listGeneratedFiles/:projectId`
- **功能描述**: 查询指定项目的generated目录文件，识别短视频和双语字幕文件，建立配对关系
- **控制器**: `listGeneratedFilesController.js`
- **开发时间**: 2025-07-26
- **测试状态**: ✅ 已完成测试

#### 请求参数
| 参数名 | 类型 | 位置 | 必需 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| `projectId` | string | 路径参数 | 是 | - | 项目唯一标识符 |
| `limit` | number | 查询参数 | 否 | 20 | 返回数量限制，最大100 |
| `sortBy` | string | 查询参数 | 否 | time_desc | 排序方式 |

#### 排序选项
| 值 | 说明 |
|-----|------|
| `time_desc` | 按时间降序（最新优先） |
| `time_asc` | 按时间升序（最旧优先） |
| `size_desc` | 按文件大小降序（最大优先） |
| `size_asc` | 按文件大小升序（最小优先） |

#### 请求示例
```javascript
// 获取项目的generated文件
GET /api/video/listGeneratedFiles/videoFile-1752828818174-496225719

// 带参数的请求
GET /api/video/listGeneratedFiles/videoFile-1752828818174-496225719?limit=5&sortBy=size_desc
```

#### 响应格式
```json
{
  "status": "success",
  "message": "查询成功",
  "data": {
    "projectId": "videoFile-1752828818174-496225719",
    "generatedPath": "/path/to/generated",
    "totalVideos": 2,
    "totalSubtitles": 2,
    "totalPairs": 2,
    "pairs": [
      {
        "videoIdentifier": "苹果隐私诉讼和解",
        "video": {
          "filename": "苹果隐私诉讼和解_extended_video_2025-07-25T23-20-29-805Z.mp4",
          "path": "/full/path/to/video.mp4",
          "size": "4.37 MB",
          "duration": "1分8秒",
          "timestamp": "2025-07-25T23-20-29-805Z",
          "type": "extended_video",
          "modifiedTime": "2025-07-25T23:20:56.262Z"
        },
        "subtitle": {
          "filename": "苹果隐私诉讼和解_enhanced_bilingual_subtitle_2025-07-25T23-19-51-868Z.json",
          "path": "/full/path/to/subtitle.json",
          "size": "2 KB",
          "entryCount": 5,
          "timestamp": "2025-07-25T23-19-51-868Z",
          "type": "bilingual_subtitle",
          "hasContent": true,
          "modifiedTime": "2025-07-25T23:19:52.000Z"
        },
        "isPaired": true
      }
    ],
    "unpairedVideos": [],
    "unpairedSubtitles": [],
    "metadata": {
      "limit": 20,
      "sortBy": "time_desc",
      "hasMore": false,
      "scannedAt": "2025-07-26T07:57:33.000Z"
    }
  }
}
```

#### 错误响应
```json
// 项目不存在
{
  "status": "error",
  "message": "项目不存在",
  "error": "Project directory not found"
}

// 参数格式错误
{
  "status": "error",
  "message": "项目ID格式不正确",
  "error": "Invalid projectId format"
}
```

#### 文件识别规律
基于Task源码的精确识别：

**视频文件（GenerateVideoTask生成）**:
- 命名格式: `${videoIdentifier}_extended_video_${timestamp}.mp4`
- 示例: `苹果隐私诉讼和解_extended_video_2025-07-25T23-20-29-805Z.mp4`

**字幕文件（BilingualSubtitleMergeTask生成）**:
- 命名格式: `${videoIdentifier}_enhanced_bilingual_subtitle_${timestamp}.json`
- 示例: `苹果隐私诉讼和解_enhanced_bilingual_subtitle_2025-07-25T23-19-51-868Z.json`

**时间戳格式**:
- 格式: `YYYY-MM-DDTHH-mm-ss-sssZ`
- 来源: `new Date().toISOString().replace(/[:.]/g, '-')`

#### 配对逻辑
- 基于 `videoIdentifier` 精确匹配视频和字幕文件
- 支持一对一配对关系
- 自动识别未配对的文件

#### 安全特性
- **路径遍历防护**: 验证projectId格式，仅允许字母数字下划线横线
- **目录限制**: 仅能访问uploads/projects目录下的文件
- **存在性验证**: 验证项目目录是否存在

#### 性能特点
- **并发处理**: 异步并发提取文件信息
- **智能识别**: 基于Task源码的精确文件类型识别
- **分页支持**: 支持limit参数控制返回数量
- **多种排序**: 支持按时间、大小等多种排序方式

#### 使用场景
1. **前端管理面板**: 显示项目已制作的短视频列表
2. **文件选择**: 支持用户勾选视频和字幕文件
3. **批量操作**: 为后续的批量下载、删除等操作提供数据
4. **状态展示**: 显示视频-字幕配对状态

#### 测试验证
- ✅ 正常项目查询测试通过
- ✅ 不存在项目错误处理测试通过
- ✅ 参数验证测试通过
- ✅ 文件识别和配对逻辑测试通过
- ✅ 文件信息提取（大小、时长、条目数）测试通过
- ✅ 排序和分页功能测试通过

---

### 5. 视频标注管理 🆕 **新增**

#### 基本信息
- **接口路径**:
  - `PUT /api/video/updateVideoTags/:projectId` - 更新视频标注
  - `GET /api/video/getVideoTags/:projectId` - 获取视频标注
- **功能描述**: 管理项目中视频文件的发布状态和标注信息
- **控制器**: `videoTagsController.js`
- **开发时间**: 2025-07-26
- **测试状态**: ✅ 已完成测试

#### 5.1 更新视频标注

##### 请求参数
| 参数名 | 类型 | 位置 | 必需 | 说明 |
|--------|------|------|------|------|
| `projectId` | string | 路径参数 | 是 | 项目唯一标识符 |
| `videos` | object | 请求体 | 是 | 视频标注数据对象 |

##### 请求体格式
```json
{
  "videos": {
    "video_v1.mp4": {
      "status": "published",
      "tags": ["已发布", "热门"],
      "publishTime": "2025-07-26T08:00:00.000Z"
    },
    "video_v2.mp4": {
      "status": "draft",
      "tags": ["草稿"],
      "notes": "待审核"
    }
  }
}
```

##### 请求示例
```javascript
// 批量更新视频发布状态
PUT /api/video/updateVideoTags/videoFile-1752828818174-496225719
Content-Type: application/json

{
  "videos": {
    "苹果隐私诉讼和解_extended_video_2025-07-25T23-20-29-805Z.mp4": {
      "status": "published"
    },
    "加拿大召回含塑料海盐_extended_video_2025-07-26T05-08-29-556Z.mp4": {
      "status": "draft"
    }
  }
}
```

##### 成功响应
```json
{
  "success": true,
  "message": "视频标注更新成功",
  "data": {
    "projectId": "videoFile-1752828818174-496225719",
    "updatedVideos": 2,
    "status": {
      "苹果隐私诉讼和解_extended_video_2025-07-25T23-20-29-805Z.mp4": {
        "status": "published"
      },
      "加拿大召回含塑料海盐_extended_video_2025-07-26T05-08-29-556Z.mp4": {
        "status": "draft"
      }
    },
    "updatedAt": "2025-07-26T08:15:30.123Z"
  }
}
```

#### 5.2 获取视频标注

##### 请求参数
| 参数名 | 类型 | 位置 | 必需 | 说明 |
|--------|------|------|------|------|
| `projectId` | string | 路径参数 | 是 | 项目唯一标识符 |

##### 请求示例
```javascript
// 获取项目的视频标注信息
GET /api/video/getVideoTags/videoFile-1752828818174-496225719
```

##### 成功响应
```json
{
  "success": true,
  "message": "获取视频标注成功",
  "data": {
    "projectId": "videoFile-1752828818174-496225719",
    "tagsFilePath": "/path/to/generated/tags.json",
    "status": {
      "苹果隐私诉讼和解_extended_video_2025-07-25T23-20-29-805Z.mp4": {
        "status": "published",
        "tags": ["已发布"],
        "publishTime": "2025-07-26T08:00:00.000Z"
      },
      "加拿大召回含塑料海盐_extended_video_2025-07-26T05-08-29-556Z.mp4": {
        "status": "draft",
        "tags": ["草稿"],
        "notes": "待审核"
      }
    },
    "totalVideos": 2,
    "publishedCount": 1,
    "draftCount": 1,
    "lastUpdated": "2025-07-26T08:15:30.123Z"
  }
}
```

#### 错误响应

##### 项目不存在 (404)
```json
{
  "success": false,
  "message": "项目不存在: videoFile-invalid-id"
}
```

##### 参数错误 (400)
```json
{
  "success": false,
  "message": "视频状态数据格式错误"
}
```

##### Generated目录不存在 (404)
```json
{
  "success": false,
  "message": "项目的generated目录不存在: videoFile-1752828818174-496225719"
}
```

#### 数据存储机制

##### tags.json文件结构
```json
{
  "video_filename.mp4": {
    "status": "published|draft",
    "tags": ["标签1", "标签2"],
    "publishTime": "2025-07-26T08:00:00.000Z",
    "notes": "备注信息",
    "customFields": {
      "platform": "youtube",
      "category": "education"
    }
  }
}
```

##### 存储位置
- **文件路径**: `{projectPath}/generated/tags.json`
- **编码格式**: UTF-8
- **格式化**: 2空格缩进的JSON格式
- **权限**: 读写权限，自动创建

#### 状态值定义
| 状态值 | 说明 | 前端显示 |
|--------|------|----------|
| `published` | 已发布 | ✅ 已发布 |
| `draft` | 草稿 | 📝 草稿 |
| `archived` | 已归档 | 📦 已归档 |
| `deleted` | 已删除 | 🗑️ 已删除 |

#### 安全特性
- **路径验证**: 防止路径遍历攻击
- **项目验证**: 确保项目和generated目录存在
- **数据验证**: 验证请求体格式和数据类型
- **原子操作**: 文件写入操作具有原子性

#### 使用场景
1. **发布状态管理**: 标记视频的发布状态
2. **批量操作**: 批量更新多个视频的状态
3. **状态查询**: 获取项目中所有视频的标注信息
4. **前端展示**: 在视频列表中显示发布状态标签
5. **工作流管理**: 支持视频制作到发布的完整工作流

#### 前端集成示例

##### 更新视频状态
```javascript
// 标注选中视频为已发布
const markAsPublished = async (projectId, selectedVideos) => {
    const videos = {};
    selectedVideos.forEach(filename => {
        videos[filename] = { status: 'published' };
    });

    const response = await fetch(`/api/video/updateVideoTags/${projectId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ videos })
    });

    const result = await response.json();
    if (result.success) {
        ElMessage.success(`✅ 成功标注 ${selectedVideos.length} 个视频为已发布`);
    }
};
```

##### 获取并显示状态
```javascript
// 获取视频状态并更新UI
const loadVideoStatus = async (projectId) => {
    const response = await fetch(`/api/video/getVideoTags/${projectId}`);
    const result = await response.json();

    if (result.success) {
        // 更新状态映射
        const statusMap = new Map();
        Object.entries(result.data.status).forEach(([filename, data]) => {
            statusMap.set(filename, data.status || 'draft');
        });
        return statusMap;
    }
    return new Map();
};
```

#### 测试验证
- ✅ 更新视频标注功能测试通过
- ✅ 获取视频标注功能测试通过
- ✅ 项目不存在错误处理测试通过
- ✅ 参数验证测试通过
- ✅ 文件创建和更新测试通过
- ✅ 批量操作测试通过
- ✅ 前端集成测试通过

---

### 7. 重命名项目文件 🆕 **新增**

#### 基本信息
- **接口路径**: `PUT /api/video/renameProject`
- **功能描述**: 修改项目source目录下的原始视频文件名，不修改扩展名，同时更新项目元数据
- **控制器**: `renameProjectController.js`
- **开发时间**: 2025-07-27
- **测试状态**: ✅ 已完成测试

#### 请求参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `videoIdentifier` | string | 是 | 项目唯一标识符 |
| `newFileName` | string | 是 | 新的文件名（不包含扩展名） |

#### 请求示例
```bash
curl -X PUT http://localhost:8081/api/video/renameProject \
  -H "Content-Type: application/json" \
  -d '{
    "videoIdentifier": "videoFile-1753532535488-38735471",
    "newFileName": "My New Video Name"
  }'
```

#### 成功响应
```json
{
  "status": "success",
  "message": "项目文件重命名成功",
  "data": {
    "videoIdentifier": "videoFile-1753532535488-38735471",
    "oldFileName": "Original Video Name",
    "newFileName": "My New Video Name",
    "fullFileName": "My New Video Name.mp4",
    "filePath": "/path/to/project/source/My New Video Name.mp4",
    "changed": true,
    "timestamp": "2025-07-27T02:57:27.123Z"
  }
}
```

#### 错误响应

**1. 缺少必需参数**
```json
{
  "status": "error",
  "message": "缺少必需参数: videoIdentifier",
  "errorCode": "MISSING_PARAMETER",
  "details": {
    "parameter": "videoIdentifier",
    "suggestion": "请提供有效的项目标识符"
  }
}
```

**2. 文件名包含非法字符**
```json
{
  "status": "error",
  "message": "文件名包含非法字符",
  "errorCode": "INVALID_FILENAME",
  "details": {
    "originalName": "Invalid<>Name",
    "suggestion": "文件名不能包含以下字符: < > : \" / \\ | ? *"
  }
}
```

**3. 项目不存在**
```json
{
  "status": "error",
  "message": "项目不存在",
  "errorCode": "PROJECT_NOT_FOUND",
  "details": {
    "videoIdentifier": "invalid-project-id",
    "projectDir": "/path/to/project"
  }
}
```

**4. 新文件名已存在**
```json
{
  "status": "error",
  "message": "新文件名已存在",
  "errorCode": "FILE_NAME_EXISTS",
  "details": {
    "videoIdentifier": "videoFile-1753532535488-38735471",
    "newFileName": "Existing File.mp4",
    "existingPath": "/path/to/existing/file.mp4"
  }
}
```

#### 功能特性
- ✅ **文件名验证**: 自动清理非法字符，确保文件名安全
- ✅ **扩展名保持**: 只修改文件名，保持原有扩展名不变
- ✅ **冲突检查**: 检查新文件名是否已存在，避免覆盖
- ✅ **元数据更新**: 自动更新项目metadata.json中的文件名信息
- ✅ **原子操作**: 使用文件系统的原子重命名操作，确保数据安全
- ✅ **详细日志**: 完整的操作日志记录，便于问题追踪

#### 安全性
- **路径验证**: 严格验证项目路径，防止路径遍历攻击
- **文件名过滤**: 自动过滤危险字符，确保文件系统安全
- **权限检查**: 验证文件操作权限，防止未授权访问
- **错误隔离**: 完善的错误处理，避免敏感信息泄露

#### 测试用例
- ✅ 正常重命名功能测试通过
- ✅ 文件名验证测试通过
- ✅ 项目不存在错误处理测试通过
- ✅ 文件冲突检查测试通过
- ✅ 元数据更新测试通过
- ✅ 非法字符过滤测试通过
