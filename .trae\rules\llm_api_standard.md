---
type: "manual"
description: "globs:"
---
# LLM API 调用标准化规范

**统一的 LLM 调用接口规范，确保所有 Task 中的 LLM 调用保持一致性、可扩展性和稳定性。**

## **核心调用模式**

所有 LLM 调用必须通过 [llmService.js](mdc:backend/src/services/llmService.js) 的统一接口：

```javascript
const llmService = require('../services/llmService');

// ✅ 标准调用格式
const result = await llmService.callLLM(taskType, options);
```

## **步骤化实现指南**

### **第一步：确定任务类型**
```javascript
// 使用标准命名格式：大写字母 + 下划线
const TASK_TYPES = {
    'CORRECT_TRANSCRIPTION',    // 转录校正
    'TRANSLATE_SUBTITLE',       // 字幕翻译  
    'OPTIMIZE_SUBTITLES',       // 字幕优化
    'GENERATE_CONTENT',         // 内容生成
    'ANALYZE_TEXT'             // 文本分析
};
```

### **第二步：构建标准化 options 对象**
```javascript
const standardOptions = {
    // === 必需参数 ===
    promptParams: {              // 提示词插值参数
        input_data: "...",       // 输入数据
        processing_goal: "...",  // 处理目标
        systemPromptContent: "You are a helpful assistant"
    },
    reqId: context.reqId,        // 请求追踪ID

    // === 基础配置 ===
    templateName: 'default',     // 提示词模板
    modelName: 'google/gemini-2.5-flash-preview-05-20',
    temperature: 0.3,            // 创造性控制
    max_tokens: 20000,           // 输出限制

    // === JSON 输出控制 ===
    forceJsonOutput: true,
    validateJsonOutput: true,
    maxJsonValidationRetries: 2,

    // === 重试策略 ===
    retryCount: 3,
    retryDelay: 1000
};
```

### **第三步：添加增强功能配置（推荐）**
```javascript
const enhancedOptions = {
    ...standardOptions,
    
    // === API 增强配置 ===
    apiEnhancements: {
        // 强制 JSON Schema 输出格式 - 严格按照OpenRouter标准
        structuredOutput: {
            enabled: true,
            // ⚠️ 重要：必须使用OpenRouter标准的response_format格式
            response_format: {
                type: "json_schema",  // 固定值，OpenRouter要求
                json_schema: {
                    name: "task_response_schema",  // Schema名称
                    strict: true,  // 推荐使用strict模式确保输出格式
                    schema: {
                        type: "object",  // 或 "array"，根据需要
                        properties: {
                            // 定义具体的输出结构
                            // 示例：
                            // id: { type: "string" },
                            // content: { type: "string" },
                            // metadata: { 
                            //     type: "object",
                            //     properties: {
                            //         key: { type: "string" }
                            //     },
                            //     required: ["key"],
                            //     additionalProperties: false
                            // }
                        },
                        required: ['required_fields'],
                        additionalProperties: false  // 严格模式，不允许额外属性
                    }
                }
            }
        },
        
        // 高级重试策略
        advancedRetry: {
            exponentialBackoff: true,
            jitter: true,
            maxDelay: 30000
        },
        
        // 自定义请求头
        customHeaders: {
            'X-Task-Type': 'YourTaskType',
            'X-Processing-Mode': 'Enhanced'
        }
    }
};
```

### **第四步：实现 LLM 进度监控**
```javascript
// 在 Task 类中使用标准化进度报告
async callLLMForProcessing(inputData, execLogPrefix) {
    // 1. 准备阶段
    this.reportLLMProgress('preparing', '准备LLM请求', {
        current: 20,
        total: 100,
        inputSize: inputData.length
    });

    // 2. 发送阶段
    this.reportLLMProgress('sending', '发送LLM请求', {
        current: 40,
        total: 100,
        modelName: options.modelName
    });

    // 3. 等待阶段
    this.reportLLMProgress('waiting', '等待LLM处理', {
        current: 60,
        total: 100
    });

    // 4. 接收阶段
    this.reportLLMProgress('receiving', '接收LLM响应', {
        current: 80,
        total: 100,
        responseSize: response.length
    });

    // 5. 解析阶段
    this.reportLLMProgress('parsing', '解析LLM响应', {
        current: 100,
        total: 100
    });
}
```

### **第五步：标准化错误处理**
```javascript
async executeLLMTask(context, progressCallback) {
    const execLogPrefix = `${this.instanceLogPrefix}[LLM调用][REQ:${context.reqId}]`;
    
    try {
        const result = await llmService.callLLM(taskType, options);
        
        // 验证响应
        if (result.status !== 'success') {
            throw new Error(`LLM调用失败: ${result.message}`);
        }
        
        return this.processLLMResponse(result, execLogPrefix);
        
    } catch (error) {
        // 错误分类和日志记录
        const errorCategory = this.categorizeError(error);
        logger.error(`${execLogPrefix}[${errorCategory}] ${error.message}`);
        
        // 使用 TaskBase 标准化错误处理
        this.fail(error);
        throw error;
    }
}

// ✅ 错误分类辅助方法
categorizeError(error) {
    if (error.errorType) return error.errorType;
    if (error.message.includes('timeout')) return 'TIMEOUT_ERROR';
    if (error.message.includes('JSON')) return 'JSON_VALIDATION_ERROR';
    if (error.message.includes('API')) return 'API_ERROR';
    return 'UNKNOWN_ERROR';
}
```

### **第六步：响应处理和验证**
```javascript
processLLMResponse(response, execLogPrefix) {
    // 解析 JSON 响应
    let parsedData;
    try {
        parsedData = JSON.parse(response.processedText);
    } catch (parseError) {
        throw new Error(`LLM响应JSON解析失败: ${parseError.message}`);
    }
    
    // 任务特定的验证逻辑
    this.validateLLMOutput(parsedData, execLogPrefix);
    
    // 记录增强功能使用情况
    if (response.enhancedFeatures) {
        logger.info(`${execLogPrefix}[增强功能] ${JSON.stringify(response.enhancedFeatures)}`);
    }
    
    return parsedData;
}
```

## **OpenRouter JSON Schema 标准化规范**

### **关键格式要求**

根据 [OpenRouter 官方文档](mdc:https:/openrouter.ai/docs/features/structured-outputs)，JSON Schema 必须严格遵循以下格式：

```javascript
// ✅ 正确的OpenRouter格式
apiEnhancements: {
    structuredOutput: {
        enabled: true,
        response_format: {                    // 必须使用response_format
            type: "json_schema",              // 固定值
            json_schema: {                    // 嵌套结构
                name: "your_schema_name",     // Schema名称
                strict: true,                 // 推荐使用strict模式
                schema: {
                    // 实际的JSON Schema定义
                    type: "object",
                    properties: { ... },
                    required: [...],
                    additionalProperties: false
                }
            }
        }
    }
}

// ❌ 错误的自定义格式（会导致LLM输出异常）
apiEnhancements: {
    structuredOutput: {
        enabled: true,
        schema: {                            // 错误：直接使用schema
            name: "your_schema_name",
            strict: true,
            schema: { ... }
        }
    }
}
```

### **常见Schema模式**

#### **数组输出格式**
```javascript
response_format: {
    type: "json_schema",
    json_schema: {
        name: "array_response",
        strict: true,
        schema: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    id: { type: "string" },
                    content: { type: "string" },
                    metadata: {
                        type: "object",
                        properties: {
                            key1: { type: "string" },
                            key2: { type: "number" }
                        },
                        required: ["key1"],
                        additionalProperties: false
                    }
                },
                required: ["id", "content"],
                additionalProperties: false
            }
        }
    }
}
```

#### **对象输出格式**
```javascript
response_format: {
    type: "json_schema",
    json_schema: {
        name: "object_response",
        strict: true,
        schema: {
            type: "object",
            properties: {
                status: { type: "string" },
                data: {
                    type: "object",
                    properties: {
                        processed_items: {
                            type: "array",
                            items: { type: "string" }
                        }
                    },
                    required: ["processed_items"],
                    additionalProperties: false
                }
            },
            required: ["status", "data"],
            additionalProperties: false
        }
    }
}
```

### **Schema设计最佳实践**

1. **使用strict模式**：`strict: true` 确保输出严格符合Schema
2. **明确required字段**：列出所有必需的属性
3. **禁用额外属性**：`additionalProperties: false` 防止意外字段
4. **合理的嵌套深度**：避免过深的嵌套结构
5. **清晰的命名**：使用描述性的Schema和属性名称

### **故障排除指南**

#### **常见问题**
1. **输出字段为空对象 `{}`**
   - 原因：Schema过于严苛或格式错误
   - 解决：检查`response_format`格式，确保使用OpenRouter标准

2. **LLM忽略Schema约束**
   - 原因：未使用OpenRouter标准格式
   - 解决：确保使用`response_format` → `json_schema`嵌套结构

3. **Schema验证失败**
   - 原因：`strict: true`时Schema定义不完整
   - 解决：确保所有属性都有明确的类型定义

#### **调试步骤**
1. **禁用Schema测试**：临时设置`enabled: false`验证LLM基础功能
2. **简化Schema**：从最简单的结构开始，逐步增加复杂性
3. **检查日志**：查看`structuredOutputUsed: true`确认Schema被应用
4. **验证格式**：确保使用OpenRouter标准的嵌套结构

## **配置优先级规则**

1. **options 参数** > **apiEnhancements 配置** > **config 默认值**
2. **任务特定配置** > **全局配置**
3. **显式指定** > **自动推断**
4. **OpenRouter标准格式** > **自定义格式**（强制要求）

## **向前兼容保证**

- ✅ **现有代码无需修改**：传统调用方式完全兼容
- ✅ **渐进式增强**：新功能通过 `apiEnhancements` 可选启用
- ✅ **智能回退**：增强功能失败时自动使用传统模式
- ✅ **OpenRouter兼容**：严格遵循OpenRouter API标准

## **最佳实践要点**

### **OpenRouter Schema配置**
```javascript
// ✅ DO: 使用OpenRouter标准格式
const schemaConfig = {
    structuredOutput: {
        enabled: true,
        response_format: {
            type: "json_schema",
            json_schema: {
                name: "descriptive_schema_name",
                strict: true,
                schema: {
                    type: "array", // 或 "object"
                    // ... 详细Schema定义
                }
            }
        }
    }
};

// ❌ DON'T: 使用自定义Schema格式
const wrongConfig = {
    structuredOutput: {
        enabled: true,
        schema: {  // 错误：直接使用schema
            name: "...",
            schema: { ... }
        }
    }
};
```

### **参数验证**
```javascript
// ✅ DO: 调用前验证必需参数
const required = ['promptParams', 'reqId'];
const missing = required.filter(key => !options[key]);
if (missing.length > 0) {
    throw new Error(`缺少必需参数: ${missing.join(', ')}`);
}

// ✅ DO: 验证OpenRouter Schema格式
if (options.apiEnhancements?.structuredOutput?.enabled) {
    const format = options.apiEnhancements.structuredOutput.response_format;
    if (!format || format.type !== "json_schema" || !format.json_schema) {
        throw new Error('OpenRouter Schema格式错误：必须使用response_format.json_schema格式');
    }
}
```

### **日志记录标准**
```javascript
// ✅ DO: 使用标准化日志格式
logger.info(`${execLogPrefix}[LLM调用开始] 任务:${taskType}, 模型:${modelName}`);
logger.debug(`${execLogPrefix}[Token使用] 输入:${usage.prompt_tokens}, 输出:${usage.completion_tokens}`);
logger.info(`${execLogPrefix}[Schema状态] OpenRouter结构化输出: ${structuredOutputUsed ? '启用' : '禁用'}`);
```

## **反例警告**

```javascript
// ❌ DON'T: 直接调用 axios 或其他 HTTP 客户端
const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {...});

// ❌ DON'T: 硬编码提示词
const prompt = "Please correct this transcription: " + rawText;

// ❌ DON'T: 忽略错误处理
const result = await llmService.callLLM(taskType, options); // 没有 try-catch

// ❌ DON'T: 跳过进度报告
// 应该使用 this.reportLLMProgress() 进行状态更新

// ❌ DON'T: 使用错误的Schema格式
const wrongSchema = {
    structuredOutput: {
        enabled: true,
        schema: {  // 错误：应该使用response_format
            name: "wrong_format",
            schema: { ... }
        }
    }
};

// ❌ DON'T: 忽略OpenRouter标准
const customFormat = {
    structuredOutput: {
        enabled: true,
        customFormat: { ... }  // 错误：OpenRouter不支持自定义格式
    }
};
```

## **性能和监控**

- **Token 使用监控**：记录每次调用的 token 消耗
- **响应时间追踪**：监控 API 调用延迟
- **错误率统计**：跟踪失败率和错误类型
- **增强功能效果**：评估新功能对成功率的影响
- **Schema应用监控**：确认`structuredOutputUsed: true`状态
- **OpenRouter兼容性**：监控API调用是否使用正确的格式

