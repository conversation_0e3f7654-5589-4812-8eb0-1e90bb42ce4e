# delayRender() 和 continueRender()

## 概述

`delayRender()` 用于告知 Remotion 某一帧不应立即渲染，而应等待异步任务完成。这对于在渲染前调用 API 获取数据非常有用。

`delayRender()` 返回一个句柄，完成异步任务后，应调用 `continueRender(handle)` 通知 Remotion 可以继续渲染。

## 语法

```typescript
import { delayRender, continueRender } from "remotion";

// 延迟渲染
const handle = delayRender("可选的标签");

// 继续渲染
continueRender(handle);
```

## 基础示例

### 1. API 数据获取

```typescript
import { useCallback, useEffect, useState } from "react";
import { continueRender, delayRender } from "remotion";

export const MyVideo = () => {
  const [data, setData] = useState(null);
  const [handle] = useState(() => delayRender("获取API数据"));

  const fetchData = useCallback(async () => {
    try {
      const response = await fetch("http://example.com/api");
      const json = await response.json();
      setData(json);
      continueRender(handle);
    } catch (error) {
      console.error("数据获取失败:", error);
      continueRender(handle); // 即使失败也要继续渲染
    }
  }, [handle]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div>
      {data ? (
        <div>视频包含来自API的数据！{JSON.stringify(data)}</div>
      ) : (
        <div>加载中...</div>
      )}
    </div>
  );
};
```

### 2. 图片预加载

```typescript
import { useEffect, useState } from "react";
import { delayRender, continueRender } from "remotion";

const ImagePreloader = ({ src }: { src: string }) => {
  const [loaded, setLoaded] = useState(false);
  const [handle] = useState(() => delayRender("预加载图片"));

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setLoaded(true);
      continueRender(handle);
    };
    img.onerror = () => {
      console.error("图片加载失败");
      continueRender(handle); // 即使失败也继续
    };
    img.src = src;
  }, [src, handle]);

  return loaded ? (
    <img src={src} alt="预加载的图片" />
  ) : (
    <div>图片加载中...</div>
  );
};
```

## 高级配置

### 1. 添加标签（v2.6.13+）

```typescript
const handle = delayRender("从API获取数据...");
```

如果超时，标签会在错误消息中显示：
```
Uncaught Error: A delayRender() "从API获取数据..." was called but not cleared after 28000ms.
```

### 2. 重试机制（v4.0.140+）

```typescript
const handle = delayRender("加载资源...", {
  retries: 1, // 默认: 0
});
```

### 3. 自定义超时（v4.0.140+）

```typescript
const handle = delayRender("加载资源...", {
  timeoutInMilliseconds: 7000, // 7秒超时
});
```

## 实际应用场景

### 1. 字体加载

```typescript
const FontLoader = ({ fontFamily }: { fontFamily: string }) => {
  const [fontLoaded, setFontLoaded] = useState(false);
  const [handle] = useState(() => delayRender("加载字体"));

  useEffect(() => {
    const loadFont = async () => {
      try {
        await document.fonts.load(`16px ${fontFamily}`);
        setFontLoaded(true);
        continueRender(handle);
      } catch (error) {
        console.error("字体加载失败:", error);
        continueRender(handle);
      }
    };

    loadFont();
  }, [fontFamily, handle]);

  return (
    <div style={{ fontFamily: fontLoaded ? fontFamily : 'Arial' }}>
      {fontLoaded ? "自定义字体已加载" : "使用默认字体"}
    </div>
  );
};
```

### 2. 多个异步操作

```typescript
const MultiAsyncComponent = () => {
  const [data1, setData1] = useState(null);
  const [data2, setData2] = useState(null);
  const [handle1] = useState(() => delayRender("获取数据1"));
  const [handle2] = useState(() => delayRender("获取数据2"));

  useEffect(() => {
    // 第一个API调用
    fetch("/api/data1")
      .then(res => res.json())
      .then(data => {
        setData1(data);
        continueRender(handle1);
      })
      .catch(() => continueRender(handle1));

    // 第二个API调用
    fetch("/api/data2")
      .then(res => res.json())
      .then(data => {
        setData2(data);
        continueRender(handle2);
      })
      .catch(() => continueRender(handle2));
  }, [handle1, handle2]);

  return (
    <div>
      <div>数据1: {data1 ? JSON.stringify(data1) : "加载中..."}</div>
      <div>数据2: {data2 ? JSON.stringify(data2) : "加载中..."}</div>
    </div>
  );
};
```

### 3. 条件性延迟渲染

```typescript
const ConditionalDelayRender = ({ shouldFetchData }: { shouldFetchData: boolean }) => {
  const [data, setData] = useState(null);
  const [handle] = useState(() => 
    shouldFetchData ? delayRender("条件性数据获取") : null
  );

  useEffect(() => {
    if (shouldFetchData && handle) {
      fetch("/api/conditional-data")
        .then(res => res.json())
        .then(data => {
          setData(data);
          continueRender(handle);
        })
        .catch(() => continueRender(handle));
    }
  }, [shouldFetchData, handle]);

  return (
    <div>
      {shouldFetchData ? (
        data ? `获取的数据: ${JSON.stringify(data)}` : "加载中..."
      ) : (
        "无需获取数据"
      )}
    </div>
  );
};
```

### 4. 错误处理和取消渲染

```typescript
import { cancelRender } from "remotion";

const ErrorHandlingComponent = () => {
  const [handle] = useState(() => delayRender("获取关键数据"));

  useEffect(() => {
    fetch("/api/critical-data")
      .then(res => {
        if (!res.ok) {
          throw new Error(`HTTP错误! 状态: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        // 数据获取成功
        continueRender(handle);
      })
      .catch(error => {
        // 关键数据获取失败，取消整个渲染
        cancelRender(`关键数据获取失败: ${error.message}`);
      });
  }, [handle]);

  return <div>等待关键数据...</div>;
};
```

### 5. 缓存优化

```typescript
const CachedDataComponent = () => {
  const [data, setData] = useState(null);
  const [handle] = useState(() => delayRender("获取缓存数据"));

  useEffect(() => {
    const cacheKey = "api-data-cache";
    
    // 检查localStorage缓存
    const cachedData = localStorage.getItem(cacheKey);
    if (cachedData) {
      setData(JSON.parse(cachedData));
      continueRender(handle);
      return;
    }

    // 如果没有缓存，则获取数据
    fetch("/api/data")
      .then(res => res.json())
      .then(data => {
        // 缓存数据
        localStorage.setItem(cacheKey, JSON.stringify(data));
        setData(data);
        continueRender(handle);
      })
      .catch(() => continueRender(handle));
  }, [handle]);

  return (
    <div>
      {data ? `缓存数据: ${JSON.stringify(data)}` : "加载中..."}
    </div>
  );
};
```

## 与 useBufferState 结合使用

```typescript
import { useBufferState } from "remotion";

const BufferedDataComponent = () => {
  const buffer = useBufferState();
  const [handle] = useState(() => delayRender("获取数据"));
  const [data, setData] = useState(null);

  useEffect(() => {
    const delayHandle = buffer.delayPlayback();
    
    fetch("/api/data")
      .then(res => res.json())
      .then(data => {
        setData(data);
        delayHandle.unblock(); // 解除播放延迟
        continueRender(handle); // 继续渲染
      })
      .catch(() => {
        delayHandle.unblock();
        continueRender(handle);
      });

    return () => {
      delayHandle.unblock();
    };
  }, [buffer, handle]);

  return (
    <div>
      {data ? `数据: ${JSON.stringify(data)}` : "缓冲中..."}
    </div>
  );
};
```

## 自定义Hook

```typescript
import { useEffect, useState } from "react";
import { delayRender, continueRender } from "remotion";

// 自定义数据获取Hook
export const useDelayedFetch = (url: string, label?: string) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [handle] = useState(() => delayRender(label || `获取 ${url}`));

  useEffect(() => {
    fetch(url)
      .then(res => res.json())
      .then(data => {
        setData(data);
        setLoading(false);
        continueRender(handle);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
        continueRender(handle);
      });
  }, [url, handle]);

  return { data, loading, error };
};

// 使用自定义Hook
const MyComponent = () => {
  const { data, loading, error } = useDelayedFetch("/api/data", "获取组件数据");

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;
  
  return <div>数据: {JSON.stringify(data)}</div>;
};
```

## 最佳实践

### 1. 封装在组件内部

```typescript
// ✅ 正确做法
const MyComponent = () => {
  const [handle] = useState(() => delayRender("组件内部延迟"));
  
  useEffect(() => {
    // 异步操作
    continueRender(handle);
  }, [handle]);
  
  return null;
};

// ❌ 错误做法 - 不要在组件外部调用
const handle = delayRender(); // 这会阻塞所有组合的渲染
```

### 2. 使用useState避免重复调用

```typescript
// ✅ 正确做法
const [handle] = useState(() => delayRender("避免重复"));

// ❌ 错误做法 - 每次重渲染都会创建新的延迟
const handle = delayRender("重复调用");
```

### 3. 总是调用continueRender

```typescript
useEffect(() => {
  fetch("/api/data")
    .then(data => {
      // 处理数据
      continueRender(handle);
    })
    .catch(error => {
      // 即使出错也要继续渲染
      console.error(error);
      continueRender(handle);
    });
}, [handle]);
```

## 超时处理

默认超时时间为30秒，可以通过以下方式自定义：

### 1. 全局超时设置

```bash
npx remotion render --timeout=60000
```

### 2. 单个delayRender超时

```typescript
const handle = delayRender("长时间操作", {
  timeoutInMilliseconds: 60000, // 60秒
});
```

## 注意事项

1. **必须调用continueRender**: 每个delayRender都必须有对应的continueRender
2. **超时限制**: 默认30秒超时，超时会导致渲染失败
3. **组件封装**: 将delayRender调用放在组件内部，避免阻塞其他组合
4. **错误处理**: 即使异步操作失败，也要调用continueRender
5. **性能考虑**: 使用缓存避免重复的API调用

## 相关 API

- [`continueRender()`](./continueRender.md) - 继续渲染
- [`cancelRender()`](./cancelRender.md) - 取消渲染
- [`useBufferState()`](./useBufferState.md) - 缓冲状态管理
- [`<Img>`](./Img.md) - 图片组件（支持delayRender配置）
- [`<Video>`](./Video.md) - 视频组件（支持delayRender配置）

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/delay-render.ts)
