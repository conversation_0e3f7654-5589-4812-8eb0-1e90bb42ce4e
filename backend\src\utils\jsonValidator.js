/**
 * @功能概述: JSON校验与修复工具集。
 * @模块说明: 此模块提供了对可能包含非标准字符或格式问题的JSON字符串进行解析、校验和修复的功能。
 *           主要用于处理从LLM（大语言模型）获取的JSON响应，这些响应有时可能不完全符合严格的JSON格式。
 */

const logger = require('./logger'); // 假设logger.js与此文件在同一目录

// 模块级日志前缀
const moduleLogPrefix = '[文件：jsonValidator.js][JSON校验与修复工具][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);





function extractAndParseJson(rawString, logPrefix = '[jsonValidator][extractAndParseJson]') {
    // 定义一个数组，用于记录JSON解析过程中的各个尝试阶段和结果，便于调试和追踪。
    const parsingAttemptsLog = [];
    // 初始化当前处理的字符串，开始时为原始输入。
    let currentString = rawString;

    // 记录开始提取和解析JSON的调试信息，包括原始输入的长度。
    logger.debug(`${logPrefix}[步骤1] 开始提取和解析JSON。原始输入长度: ${rawString?.length || 0}`);
    // 将原始输入的信息记录到解析尝试日志中。
    parsingAttemptsLog.push({ stage: '原始输入', length: rawString?.length || 0, preview: rawString?.substring(0, 100) });

    // 检查输入是否为有效的字符串且不为空白。
    if (typeof currentString !== 'string' || !currentString.trim()) {
        // 如果输入无效，记录错误日志。
        logger.error(`${logPrefix}[错误] 输入字符串为空或非字符串类型。`);
        // 将输入验证失败的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({ stage: '输入验证', success: false, error: '输入为空或非字符串' });
        // 返回失败结果，包含错误信息。
        return {
            success: false,
            data: null,
            error: 'Input string is empty or not a string.',
            cleanedString: currentString, // 返回当前（无效）字符串
            parsingAttemptsLog // 返回完整的解析尝试日志
        };
    }

    // 步骤 1.1: 清理Markdown代码块 (```json ... ``` 或 ``` ... ```)
    // 定义正则表达式，用于匹配以```json或```开头和结尾的Markdown代码块。
    const markdownFenceRegex = /^```(?:json)?\s*([\s\S]*?)\s*```$/;
    // 尝试匹配当前字符串中的Markdown代码块。
    const markdownMatch = currentString.match(markdownFenceRegex);
    // 如果匹配成功且捕获组1（即代码块内容）存在。
    if (markdownMatch && markdownMatch[1]) {
        // 存储清理前的原始字符串长度，用于日志记录。
        const originalBeforeMarkdownClean = currentString;
        // 提取代码块内容并去除首尾空白。
        currentString = markdownMatch[1].trim();
        // 记录移除了Markdown代码块标记的调试信息。
        logger.debug(`${logPrefix}[步骤1.1] 移除了Markdown代码块标记。清理后长度: ${currentString.length}`);
        // 将Markdown清理成功的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({
            stage: 'Markdown清理',
            removed: true,
            originalLength: originalBeforeMarkdownClean.length,
            newLength: currentString.length,
            preview: currentString.substring(0, 100)
        });
    } else {
        // 如果没有匹配到Markdown代码块，记录未执行清理的信息。
        parsingAttemptsLog.push({ stage: 'Markdown清理', removed: false, preview: currentString.substring(0, 100) });
    }

    // 步骤 1.2: 尝试定位JSON主体 (寻找第一个'{'或'['和最后一个'}'或']')
    // 这是一个基础的定位，可能不适用于所有复杂情况，但能处理一些LLM在JSON前后添加解释性文本的场景。
    // 初始化JSON起始和结束索引。
    let jsonStart = -1;
    let jsonEnd = -1;

    // 查找第一个开大括号 '{' 的索引。
    const firstBrace = currentString.indexOf('{');
    // 查找第一个开方括号 '[' 的索引。
    const firstBracket = currentString.indexOf('[');

    // 判断JSON主体是以对象（{}）还是数组（[]）开始。
    // 如果找到了开大括号，并且开方括号不存在或开大括号在开方括号之前。
    if (firstBrace !== -1 && (firstBracket === -1 || firstBrace < firstBracket)) {
        // 设定JSON起始为开大括号的索引。
        jsonStart = firstBrace;
        // 设定JSON结束为最后一个闭大括号 '}' 的索引。
        jsonEnd = currentString.lastIndexOf('}');
    } else if (firstBracket !== -1) { // 如果找到了开方括号。
        // 设定JSON起始为开方括号的索引。
        jsonStart = firstBracket;
        // 设定JSON结束为最后一个闭方括号 ']' 的索引。
        jsonEnd = currentString.lastIndexOf(']');
    }

    // 如果成功定位到JSON的起始和结束边界，并且结束索引在起始索引之后。
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        // 存储提取前的原始字符串，用于日志记录。
        const originalBeforeBodyExtraction = currentString;
        // 提取JSON主体部分（包含起始和结束括号/方括号）。
        currentString = currentString.substring(jsonStart, jsonEnd + 1);
        // 记录成功提取JSON主体的调试信息。
        logger.debug(`${logPrefix}[步骤1.2] 尝试提取JSON主体。提取后长度: ${currentString.length}`);
        // 将JSON主体提取成功的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({
            stage: 'JSON主体提取',
            extracted: true,
            originalLength: originalBeforeBodyExtraction.length,
            newLength: currentString.length,
            preview: currentString.substring(0, 100)
        });
    } else {
        // 如果未能找到清晰的JSON边界或字符串本身就是JSON主体，记录未执行提取的信息。
        logger.debug(`${logPrefix}[步骤1.2] 未执行JSON主体提取（未找到清晰的JSON边界或已是主体）。`);
        // 将JSON主体提取未执行的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({ stage: 'JSON主体提取', extracted: false, preview: currentString.substring(0, 100) });
    }

    // 新增步骤 1.2.1: 移除JavaScript风格的注释
    const originalBeforeCommentRemoval = currentString;
    try {
        // 移除单行注释和多行注释
        currentString = currentString.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');
        if (currentString.length < originalBeforeCommentRemoval.length) {
            logger.debug(`${logPrefix}[步骤1.2.1] 移除了JavaScript风格的注释。清理后长度: ${currentString.length}`);
            parsingAttemptsLog.push({
                stage: '注释移除',
                removed: true,
                originalLength: originalBeforeCommentRemoval.length,
                newLength: currentString.length,
                preview: currentString.substring(0, 100)
            });
        } else {
            parsingAttemptsLog.push({ stage: '注释移除', removed: false, preview: currentString.substring(0, 100) });
        }
    } catch (commentError) {
        logger.warn(`${logPrefix}[警告][步骤1.2.1] 移除注释时发生错误: ${commentError.message}`);
        parsingAttemptsLog.push({ stage: '注释移除', removed: false, error: commentError.message, preview: currentString.substring(0, 100) });
    }

    // 新增步骤 1.2.2: 移除末尾逗号
    const originalBeforeTrailingCommaRemoval = currentString;
    try {
        // 移除对象和数组中末尾的逗号
        currentString = currentString.replace(/,(?=\s*([}\]]))/g, '');
        if (currentString.length < originalBeforeTrailingCommaRemoval.length) {
            logger.debug(`${logPrefix}[步骤1.2.2] 移除了末尾逗号。清理后长度: ${currentString.length}`);
            parsingAttemptsLog.push({
                stage: '末尾逗号移除',
                removed: true,
                originalLength: originalBeforeTrailingCommaRemoval.length,
                newLength: currentString.length,
                preview: currentString.substring(0, 100)
            });
        } else {
            parsingAttemptsLog.push({ stage: '末尾逗号移除', removed: false, preview: currentString.substring(0, 100) });
        }
    } catch (commaError) {
        logger.warn(`${logPrefix}[警告][步骤1.2.2] 移除末尾逗号时发生错误: ${commaError.message}`);
        parsingAttemptsLog.push({ stage: '末尾逗号移除', removed: false, error: commaError.message, preview: currentString.substring(0, 100) });
    }
    
    // 步骤 1.3: 执行JSON.parse()
    // 尝试解析清理和提取后的字符串为JSON对象。
    try {
        const jsonData = JSON.parse(currentString);
        // 如果解析成功，记录信息日志。
        logger.info(`${logPrefix}[步骤1.3] JSON成功解析。`);
        // 将JSON解析成功的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({ stage: 'JSON解析', success: true, dataPreview: JSON.stringify(jsonData)?.substring(0,100) });
        // 返回成功结果，包含解析后的数据、清理后的字符串和完整的日志。
        return {
            success: true,
            data: jsonData,
            error: null,
            cleanedString: currentString, // 这是最终尝试解析的字符串
            parsingAttemptsLog
        };
    } catch (e) {
        // 如果解析失败，记录错误日志，包括错误消息。
        logger.error(`${logPrefix}[错误][步骤1.3] JSON解析失败: ${e.message}`);
        // 记录解析失败字符串的前200字符，便于调试。
        logger.debug(`${logPrefix} 解析失败的字符串 (前200字符): "${currentString.substring(0, 200)}"`);
        // 记录解析失败字符串的后200字符，便于调试。
        logger.debug(`${logPrefix} 解析失败的字符串 (后200字符): "${currentString.substring(Math.max(0, currentString.length - 200))}"`);
        // 将JSON解析失败的信息记录到解析尝试日志中。
        parsingAttemptsLog.push({ stage: 'JSON解析', success: false, error: e.message, attemptedStringPreview: currentString.substring(0,200) });
        // 返回失败结果，包含错误信息、清理后的字符串和完整的日志。
        return {
            success: false,
            data: null,
            error: e.message,
            cleanedString: currentString,
            parsingAttemptsLog
        };
    }
}



/**
 * @功能概述: 校验传入的JSON对象或数组是否符合预期的结构。
 * @param {object|Array} jsonData - 已经通过 JSON.parse() 解析后的JavaScript对象或数组。
 * @param {object} expectedSchema - [待定义] 一个描述预期JSON结构的对象（例如，包含必需字段、类型等）。
 * @param {string} [logPrefix='[jsonValidator][validateJsonStructure]'] - 日志前缀。
 * @returns {object} 包含校验结果的对象，例如 { success: true } 或 { success: false, error: "...", details: [...] }。
 */
function validateJsonStructure(jsonData, expectedSchema, logPrefix = '[jsonValidator][validateJsonStructure]') {
    logger.info(`${logPrefix} 开始校验JSON数据结构。`);
    // 暂存校验错误信息
    const errors = [];

    // 1. 首先检查 jsonData 的顶层类型是否符合 expectedSchema 的预期
    //    例如，expectedSchema 可能指定期望的是 'object' 或 'array'
    if (expectedSchema.type) {
        const actualType = Array.isArray(jsonData) ? 'array' : typeof jsonData;
        if (actualType !== expectedSchema.type) {
            const errorMsg = `顶层数据类型不匹配。期望: ${expectedSchema.type}, 实际: ${actualType}`;
            logger.warn(`${logPrefix}[校验失败] ${errorMsg}`);
            errors.push({ path: '$', message: errorMsg });
            // 如果顶层类型不匹配，后续的属性或元素校验可能没有意义，可以考虑提前返回
            return { success: false, error: '顶层数据类型不匹配。', details: errors };
        }
    }
    logger.debug(`${logPrefix} 顶层数据类型校验通过 (类型: ${typeof jsonData})。`);

    // 2. 如果期望的是对象，则校验必需的属性及其类型
    if (expectedSchema.type === 'object' && expectedSchema.properties) {
        for (const propName in expectedSchema.properties) {
            const propSchema = expectedSchema.properties[propName];
            const propValue = jsonData[propName];

            // 2a. 检查必需属性是否存在
            if (propSchema.required && !(propName in jsonData)) {
                const errorMsg = `对象缺少必需属性: "${propName}"`;
                logger.warn(`${logPrefix}[校验失败] ${errorMsg}`);
                errors.push({ path: `$.${propName}`, message: errorMsg });
                continue; // 继续检查其他属性
            }

            // 如果属性存在，再检查类型
            if (propName in jsonData) {
                const actualPropType = Array.isArray(propValue) ? 'array' : typeof propValue;
                if (propSchema.type && actualPropType !== propSchema.type) {
                    const errorMsg = `属性 "${propName}" 类型不匹配。期望: ${propSchema.type}, 实际: ${actualPropType}`;
                    logger.warn(`${logPrefix}[校验失败] ${errorMsg}`);
                    errors.push({ path: `$.${propName}`, message: errorMsg });
                } else {
                     logger.debug(`${logPrefix} 属性 "${propName}" 类型校验通过 (类型: ${actualPropType})。`);
                }

                // 2b. 如果属性是嵌套对象或数组，可以递归调用 validateJsonStructure
                if (propSchema.type === 'object' && propSchema.properties && typeof propValue === 'object' && propValue !== null) {
                    // 递归校验嵌套对象
                    // const nestedResult = validateJsonStructure(propValue, propSchema, `${logPrefix}[${propName}]`);
                    // if (!nestedResult.success) errors.push(...nestedResult.details);
                } else if (propSchema.type === 'array' && propSchema.items && Array.isArray(propValue)) {
                    // 校验数组中的每个元素
                    // for (let i = 0; i < propValue.length; i++) {
                    //     const itemResult = validateJsonStructure(propValue[i], propSchema.items, `${logPrefix}[${propName}][${i}]`);
                    //     if (!itemResult.success) errors.push(...itemResult.details);
                    // }
                }
            }
        }
    }

    // 3. 如果期望的是数组，则校验数组元素
    else if (expectedSchema.type === 'array' && expectedSchema.items) {
        if (!Array.isArray(jsonData)) {
            // 这在顶层类型检查时应该已经捕获，但以防万一
            const errorMsg = `期望数组但收到非数组类型。`;
            logger.warn(`${logPrefix}[校验失败] ${errorMsg}`);
            errors.push({ path: '$', message: errorMsg });
            return { success: false, error: '期望数组但收到非数组类型。', details: errors };
        }

        if (expectedSchema.minItems !== undefined && jsonData.length < expectedSchema.minItems) {
            const errorMsg = `数组长度 (${jsonData.length}) 小于期望的最小长度 (${expectedSchema.minItems})。`;
            logger.warn(`${logPrefix}[校验失败] ${errorMsg}`);
            errors.push({ path: '$', message: errorMsg });
        }
         logger.debug(`${logPrefix} 数组长度校验通过 (长度: ${jsonData.length})。`);

        // 校验数组中的每个元素是否符合 itemSchema
        // for (let i = 0; i < jsonData.length; i++) {
        //     const itemResult = validateJsonStructure(jsonData[i], expectedSchema.items, `${logPrefix}[${i}]`);
        //     if (!itemResult.success) {
        //         errors.push(...itemResult.details.map(detail => ({ ...detail, path: `$[${i}]${detail.path.substring(1)}` })));
        //     }
        // }
    }

    if (errors.length > 0) {
        logger.warn(`${logPrefix} JSON结构校验失败。发现 ${errors.length} 个问题。`);
        return { success: false, error: 'JSON结构校验失败。', details: errors };
    }

    logger.info(`${logPrefix} JSON数据结构校验成功。`);
    return { success: true };
}

module.exports = {
    extractAndParseJson,
    validateJsonStructure
    // 后续会添加 attemptJsonRepair, processLlmJsonResponse 等函数
};

logger.info(`${moduleLogPrefix}jsonValidator.js 工具模块已初步加载并导出 extractAndParseJson 函数。`); 