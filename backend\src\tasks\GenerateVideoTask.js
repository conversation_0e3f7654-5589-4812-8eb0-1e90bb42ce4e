/**
 * @文件名: GenerateVideoTask.js
 * @功能概述: 视频生成任务 - 完全照抄generate-video.test.js的逻辑
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 *
 * @功能描述:
 *   基于generate-video.test.js的逻辑，生成完整的9:16短视频。
 *   包含音频重复拼接、进度条生成、背景视频生成、ASS字幕烧录等所有功能。
 *   使用Canvas生成进度条和背景视频，通过FFmpeg进行视频合成。
 *
 * @依赖模块:
 *   - TaskBase: 任务基类
 *   - generateProgressBarVideoWithCanvas: Canvas进度条生成工具
 *   - generateBackgroundVideoWithCanvas: Canvas背景视频生成工具
 *   - spawn: FFmpeg进程管理
 *
 * @上下文输入 (Context Input):
 *   必需字段:
 *   - videoIdentifier: {string} 视频唯一标识符 (来自流水线)
 *   - originalVideoPath: {string} 原始视频文件路径 (来自videoController.js)
 *   - audioDuration: {number} 音频时长（秒） (来自GenerateASSTask)
 *   - videoConfig: {Object} 视频配置对象 (来自GenerateASSTask)
 *   - assContent: {string} ASS字幕内容 (来自GenerateASSTask)
 *   - audioFilePath: {string} 音频文件路径 (来自ConvertToAudioTask)
 *   - savePath: {string} 文件保存路径
 *
 * @上下文输出 (Context Output):
 *   新增字段:
 *   - extendedAudioPath: {string} 重复拼接后的音频文件路径
 *   - progressBarVideoPath: {string} 进度条视频文件路径
 *   - backgroundVideoPath: {string} 背景视频文件路径
 *   - finalVideoPath: {string} 最终生成的视频文件路径
 *   - videoGenerationStats: {Object} 视频生成统计信息
 *
 * @核心功能:
 *   1. 音频重复拼接处理 (repeatAudioConcat)
 *   2. Canvas进度条视频生成 (generateProgressBarVideo)
 *   3. Canvas背景视频生成 (generateBackgroundVideo)
 *   4. ASS字幕文件写入和验证 (writeAndValidateAssSubtitle)
 *   5. 9:16视频合成 (generateVideo)
 *
 * @技术特点:
 *   - 完全照抄generate-video.test.js的核心逻辑
 *   - 支持Canvas生成的动态进度条和背景视频
 *   - 智能音频重复拼接和时长计算
 *   - 专业的FFmpeg视频合成流程
 *   - 完善的错误处理和日志记录
 */

const TaskBase = require('../class/TaskBase');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

// 引入Canvas进度条生成工具函数
const { generateProgressBarVideoWithCanvas } = require('../utils/progressBarVideoGenerator');

// 引入Canvas背景视频生成工具函数
const { generateBackgroundVideoWithCanvas } = require('../utils/backgroundVideoGenerator');

/**
 * @类名: GenerateVideoTask
 * @继承: TaskBase
 * @功能: 生成9:16短视频的任务类 - 照抄generate-video.test.js逻辑
 */
class GenerateVideoTask extends TaskBase {
    constructor() {
        super('GenerateVideoTask');
        this.instanceLogPrefix = `[${this.taskName}][${this.instanceId}]`;

        // 背景图片路径定义
        this.NEWSPAPER_BACKGROUND_PATH = path.join(__dirname, '../assets/newspaper_9_16.png');
        this.ABSTRACT_BACKGROUND_PATH = path.join(__dirname, '../assets/abstract_9_16.png');

        // 注意：不再从文件读取配置，而是从context中获取videoConfig对象
    }



    /**
     * @功能概述: 执行视频生成任务 - 完全照抄generate-video.test.js的main函数逻辑
     * @参数说明:
     *   - context: {object} 任务上下文，包含以下必需字段:
     *                       - videoIdentifier: {string} 视频唯一标识符 (必需)
     *                       - originalVideoPath: {string} 原始视频文件路径 (必需)
     *                       - audioDuration: {number} 音频时长（秒） (必需)
     *                       - assContent: {string} ASS字幕内容 (必需)
     *                       - audioFilePath: {string} 音频文件路径 (必需)
     *                       - savePath: {string} 文件保存路径 (必需)
     *   注意: videoConfig将从video-config.json文件读取，不再从上下文获取
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度
     * @returns {Promise<object>} 包含视频文件路径和生成统计信息的对象
     * @throws {Error} 如果参数校验失败、文件生成失败或视频合成失败时，则抛出错误
     * @执行流程: 完全照抄generate-video.test.js的main函数逻辑
     */
    async execute(context, progressCallback) {
        const execLogPrefix = `${this.instanceLogPrefix}[execute]`;

        try {
            // 步骤 1: 初始化和进度报告
            logger.info(`${execLogPrefix} ========== 开始视频生成任务 ==========`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '初始化视频生成任务',
                current: 0,
                total: 100
            });

            // 步骤 2: 验证必需的输入参数
            logger.info(`${execLogPrefix}[步骤 1] 验证输入参数...`);
            const requiredFields = [
                'videoIdentifier',
                'originalVideoPath',
                'audioDuration',
                'assContent',
                'audioFilePath',
                'savePath',
                'videoConfig'
            ];
            this.validateRequiredFields(context, requiredFields, execLogPrefix);

            const {
                videoIdentifier,
                originalVideoPath,
                audioDuration,
                assContent,
                audioFilePath,
                savePath,
                videoConfig
            } = context;

            // 步骤 2.1: 使用context中的配置对象（不再从文件读取）
            logger.info(`${execLogPrefix}[步骤 1.1] 使用context中的videoConfig配置对象...`);
            logger.info(`${execLogPrefix} 配置对象获取成功`);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '参数验证完成',
                current: 10,
                total: 100
            });

            // 步骤 3: 计算总音频时长
            logger.info(`${execLogPrefix}[步骤 2] 计算总音频时长...`);
            const totalAudioDuration = audioDuration * videoConfig.repeatCount;
            logger.info(`${execLogPrefix} 总音频时长: ${totalAudioDuration.toFixed(2)} 秒 (${audioDuration.toFixed(2)} × ${videoConfig.repeatCount})`);

            // 步骤 4: 执行音频重复拼接
            logger.info(`${execLogPrefix}[步骤 3] 执行音频重复拼接...`);
            const extendedAudioPath = await this.repeatAudioConcat(audioFilePath, videoConfig, videoIdentifier, savePath);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '音频重复拼接完成',
                current: 30,
                total: 100
            });

            // 步骤 6: 生成进度条视频
            logger.info(`${execLogPrefix}[步骤 5] 生成进度条视频...`);
            const originalAudioDuration = audioDuration; // 原音频时长
            const progressBarVideoPath = await this.generateProgressBarVideo(originalAudioDuration, videoConfig, videoIdentifier, savePath);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '进度条视频生成完成',
                current: 50,
                total: 100
            });

            // 步骤 7: 重复进度条视频
            logger.info(`${execLogPrefix}[步骤 6] 重复进度条视频...`);
            const repeatedProgressBarVideoPath = await this.repeatProgressBarVideo(progressBarVideoPath, totalAudioDuration, videoConfig, videoIdentifier, savePath);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '进度条视频重复完成',
                current: 60,
                total: 100
            });

            // 步骤 8: 生成背景视频（报纸+80%黑色遮罩）
            logger.info(`${execLogPrefix}[步骤 7] 生成背景视频...`);
            const backgroundVideoPath = await this.generateBackgroundVideo(totalAudioDuration, videoConfig, videoIdentifier, savePath);

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '背景视频生成完成',
                current: 70,
                total: 100
            });

            // 步骤 9: 生成9:16视频（含原视频烧录、进度条和ASS字幕）
            logger.info(`${execLogPrefix}[步骤 8] 生成9:16视频...`);
            const finalVideoPath = await this.generateVideo(
                totalAudioDuration,
                extendedAudioPath,
                repeatedProgressBarVideoPath,
                backgroundVideoPath,
                assContent,
                originalVideoPath,
                videoConfig,
                context.dynamicVideoTitleForFile || videoIdentifier,
                savePath
            );

            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '9:16视频生成完成',
                current: 95,
                total: 100
            });

            // 步骤 10: 构建任务结果（只保留最终视频文件，临时文件已删除）
            const taskResult = {
                // 最终视频文件信息
                finalVideoPath: finalVideoPath,

                // 视频配置（从配置文件读取）
                videoConfig: videoConfig,

                // 视频生成统计
                videoGenerationStats: {
                    originalAudioDuration: originalAudioDuration,
                    totalAudioDuration: totalAudioDuration,
                    repeatCount: videoConfig.repeatCount,
                    videoResolution: `${videoConfig.width}x${videoConfig.height}`,
                    framerate: videoConfig.framerate
                },

                // 任务信息
                videoIdentifier: videoIdentifier,
                savePath: savePath,
                taskStatus: 'completed',
                taskResult: 'success'
            };

            this.reportProgress(TASK_STATUS.COMPLETED, TASK_SUBSTATUS.COMPLETED, {
                detail: '视频生成任务完成',
                current: 100,
                total: 100
            });

            logger.info(`${execLogPrefix} ========== 视频生成任务完成 ==========`);
            logger.info(`${execLogPrefix} ✅ 最终视频文件: ${finalVideoPath}`);
            logger.info(`${execLogPrefix} ✅ 视频分辨率: ${videoConfig.width}x${videoConfig.height}`);
            logger.info(`${execLogPrefix} ✅ 视频时长: ${totalAudioDuration.toFixed(2)} 秒`);
            logger.info(`${execLogPrefix} ✅ 音频重复次数: ${videoConfig.repeatCount}`);

            this.complete(taskResult);
            return taskResult;

        } catch (error) {
            this.fail(error);
            logger.error(`${execLogPrefix} 视频生成任务执行失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出错误
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (!context[field]) {
                const errorMsg = `执行失败：上下文缺少必需字段 ${field}`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。`);
    }



    /**
     * @功能概述: 使用FFmpeg将原音频重复拼接指定次数，生成扩展音频文件 - 完全照抄generate-video.test.js的repeatAudioConcat函数
     * @参数说明:
     *   - audioFilePath: {string} 原音频文件路径
     *   - videoConfig: {Object} 视频配置对象
     *   - videoIdentifier: {string} 视频标识符
     *   - savePath: {string} 保存路径
     * @返回值: {Promise<string>} 生成的输出音频文件完整路径
     * @错误处理:
     *   - FFmpeg进程启动失败时抛出进程错误
     *   - 转换超时（5分钟）时强制终止并抛出超时错误
     *   - 输出文件验证失败时抛出文件错误
     * @执行流程:
     *   1. 构建输出文件完整路径
     *   2. 生成FFmpeg音频拼接命令参数
     *   3. 启动FFmpeg子进程并监听输出
     *   4. 解析进度信息并实时报告
     *   5. 验证输出文件并返回路径
     * @技术参数:
     *   - 音频编码器: libmp3lame (MP3编码)
     *   - 拼接方式: concat filter (无缝连接)
     *   - 输出格式: MP3
     */
    async repeatAudioConcat(audioFilePath, videoConfig, videoIdentifier, savePath) {
        const functionName = 'repeatAudioConcat';

        return new Promise((resolve, reject) => {
            try {
                logger.info(`[${functionName}] 开始音频重复拼接处理...`);

                // 步骤 1: 构建输出文件完整路径
                const outputAudioFilename = `${videoIdentifier}_extended_audio.mp3`;
                const outputPath = path.join(savePath, outputAudioFilename);
                logger.info(`[${functionName}] 输出文件路径: ${outputPath}`);

                // 步骤 2: 构建FFmpeg命令参数
                // 使用concat filter进行音频无缝拼接
                const inputArgs = [];
                const filterInputs = [];

                // 为每次重复添加输入参数
                for (let i = 0; i < videoConfig.repeatCount; i++) {
                    inputArgs.push('-i', audioFilePath);
                    filterInputs.push(`[${i}:a]`);
                }

                const concatFilter = `${filterInputs.join('')}concat=n=${videoConfig.repeatCount}:v=0:a=1[outa]`;

                const ffmpegArgs = [
                    ...inputArgs,                               // 多个输入文件
                    '-filter_complex', concatFilter,            // 音频拼接滤镜
                    '-map', '[outa]',                          // 映射输出音频流
                    '-c:a', 'libmp3lame',                      // MP3编码器
                    '-b:a', '192k',                            // 音频比特率
                    '-y',                                      // 覆盖输出文件
                    outputPath                                 // 输出路径
                ];

                logger.info(`[${functionName}] FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`);
                logger.info(`[${functionName}] 音频重复次数: ${videoConfig.repeatCount}`);

                // 步骤 3: 启动FFmpeg进程
                const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

                let ffmpegOutput = '';
                let ffmpegError = '';

                // 步骤 4: 监听stdout输出（标准输出）
                ffmpegProcess.stdout.on('data', (data) => {
                    ffmpegOutput += data.toString();
                });

                // 步骤 5: 监听stderr输出（FFmpeg的主要输出和进度信息）
                ffmpegProcess.stderr.on('data', (data) => {
                    const output = data.toString();
                    ffmpegError += output;

                    // 步骤 5.1: 解析时间进度信息
                    if (output.includes('time=')) {
                        const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                        if (timeMatch) {
                            const timeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
                            logger.info(`[${functionName}] FFmpeg进度: ${timeStr}`);
                        }
                    }
                });

                // 步骤 6: 监听进程退出事件
                ffmpegProcess.on('close', async (code) => {
                    if (code === 0) {
                        logger.info(`[${functionName}] FFmpeg执行成功`);

                        try {
                            // 步骤 6.1: 验证输出文件是否存在
                            await fs.access(outputPath);

                            // 步骤 6.2: 获取输出文件统计信息
                            const stats = await fs.stat(outputPath);
                            const outputSizeMB = (stats.size / 1024 / 1024).toFixed(2);

                            logger.info(`[${functionName}] 输出文件生成成功: ${outputPath}`);
                            logger.info(`[${functionName}] 输出文件大小: ${outputSizeMB}MB`);

                            resolve(outputPath); // 返回输出文件路径

                        } catch (error) {
                            logger.error(`[${functionName}] 输出文件验证失败: ${error.message}`);
                            reject(new Error(`输出文件不存在: ${outputPath}`));
                        }
                    } else {
                        const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                        logger.error(`[${functionName}] ${errorMsg}`);
                        logger.error(`[${functionName}] FFmpeg错误输出: ${ffmpegError.slice(-500)}`);
                        reject(new Error(errorMsg));
                    }
                });

                // 步骤 7: 监听进程启动错误
                ffmpegProcess.on('error', (error) => {
                    const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                    logger.error(`[${functionName}] ${errorMsg}`);
                    reject(new Error(errorMsg));
                });

                // 步骤 8: 设置超时保护机制（5分钟）
                setTimeout(() => {
                    if (!ffmpegProcess.killed) {
                        logger.warn(`[${functionName}] FFmpeg执行超时，强制终止进程`);
                        ffmpegProcess.kill('SIGTERM'); // 优雅终止

                        // 5秒后强制杀死进程
                        setTimeout(() => {
                            if (!ffmpegProcess.killed) {
                                ffmpegProcess.kill('SIGKILL'); // 强制杀死
                            }
                        }, 5000);

                        reject(new Error('FFmpeg执行超时'));
                    }
                }, 300000); // 5分钟超时

            } catch (error) {
                logger.error(`[${functionName}] 音频重复拼接失败: ${error.message}`);
                reject(error);
            }
        });
    }



    /**
     * @功能概述: 生成动态进度条视频，通过Canvas生成并组合多个视频元素 - 完全照抄generate-video.test.js的generateProgressBarVideo函数
     * @参数说明:
     *   - originalAudioDuration: 音频原始时长（秒），用于控制进度条动画时长和最终视频时长
     *   - videoConfig: {Object} 视频配置对象
     *   - videoIdentifier: {string} 视频标识符
     *   - savePath: {string} 保存路径
     * @返回值: Promise<string> - 解析为生成的进度条视频文件绝对路径
     * @进度条配置:
     *   - 宽度: 从videoConfig继承主视频宽度
     *   - 高度: 使用videoConfig.progressBar.height配置项
     *   - 背景色: 使用videoConfig.progressBar.backgroundColor配置项
     *   - 前景色: 使用videoConfig.progressBar.foregroundColor配置项
     *   - 帧率: 与主视频保持一致(videoConfig.framerate)
     * @实现细节:
     *   - 使用时间戳生成唯一文件名，避免文件冲突
     *   - 输出路径拼接使用系统统一存储路径(savePath)
     *   - 依赖generateProgressBarVideoWithCanvas实现核心动画逻辑
     *   - 完整处理链包含成功/错误日志记录和异常传播
     */
    async generateProgressBarVideo(originalAudioDuration, videoConfig, videoIdentifier, savePath) {
        const functionName = 'generateProgressBarVideo';

        logger.info(`[${functionName}] 开始使用Canvas生成进度条视频...`);
        logger.info(`[${functionName}] 原音频时长: ${originalAudioDuration.toFixed(2)} 秒`);

        // 构建进度条视频文件路径
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const progressBarFilename = `${videoIdentifier}_progress_bar_${timestamp}.mp4`;
        const progressBarPath = path.join(savePath, progressBarFilename);

        try {
            // 使用Canvas生成进度条视频
            const result = await generateProgressBarVideoWithCanvas({
                duration: originalAudioDuration,
                width: videoConfig.width,
                height: videoConfig.progressBar.height,
                backgroundColor: videoConfig.progressBar.backgroundColor,
                foregroundColor: videoConfig.progressBar.foregroundColor,
                framerate: videoConfig.framerate,
                outputPath: progressBarPath
            });

            logger.info(`[${functionName}] Canvas进度条视频生成成功: ${result}`);
            return result;

        } catch (error) {
            logger.error(`[${functionName}] Canvas进度条视频生成失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 重复进度条视频，使其与重复后的音频时长匹配 - 完全照抄generate-video.test.js的repeatProgressBarVideo函数
     * @参数说明:
     *   - progressBarPath: 单次进度条视频路径
     *   - totalDuration: 重复后的总时长
     *   - videoConfig: {Object} 视频配置对象
     *   - videoIdentifier: {string} 视频标识符
     *   - savePath: {string} 保存路径
     * @返回值: Promise<string> - 成功时返回重复后的进度条视频路径
     */
    async repeatProgressBarVideo(progressBarPath, totalDuration, videoConfig, videoIdentifier, savePath) {
        const functionName = 'repeatProgressBarVideo';

        logger.info(`[${functionName}] 开始重复进度条视频...`);
        logger.info(`[${functionName}] 重复次数: ${videoConfig.repeatCount}, 总时长: ${totalDuration.toFixed(2)} 秒`);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const repeatedProgressBarFilename = `${videoIdentifier}_progress_bar_repeated_${timestamp}.mp4`;
        const repeatedProgressBarPath = path.join(savePath, repeatedProgressBarFilename);

        // 检查并删除可能存在的同名文件
        try {
            await fs.access(repeatedProgressBarPath);
            await fs.unlink(repeatedProgressBarPath);
            logger.debug(`[${functionName}] 已删除存在的同名重复进度条文件: ${repeatedProgressBarPath}`);
        } catch (error) {
            logger.debug(`[${functionName}] 重复进度条文件不存在，可以直接创建: ${repeatedProgressBarPath}`);
        }

        return new Promise((resolve, reject) => {
            try {
                // 构建重复进度条的输入列表
                const inputArgs = [];
                const concatInputs = [];

                for (let i = 0; i < videoConfig.repeatCount; i++) {
                    inputArgs.push('-i', progressBarPath);
                    concatInputs.push(`[${i}]`);
                }

                const repeatArgs = [
                    ...inputArgs,
                    '-filter_complex', `${concatInputs.join('')}concat=n=${videoConfig.repeatCount}:v=1:a=0[repeated_progress]`,
                    '-map', '[repeated_progress]',
                    '-c:v', videoConfig.codec,
                    '-preset', videoConfig.preset,
                    '-crf', videoConfig.crf.toString(),
                    '-r', videoConfig.framerate.toString(),
                    '-y',
                    repeatedProgressBarPath
                ];

                logger.info(`[${functionName}] 重复进度条FFmpeg命令: ffmpeg ${repeatArgs.join(' ')}`);

                const ffmpegProcess = spawn('ffmpeg', repeatArgs);

                let ffmpegError = '';

                ffmpegProcess.stderr.on('data', (data) => {
                    ffmpegError += data.toString();
                });

                ffmpegProcess.on('close', async (code) => {
                    if (code === 0) {
                        try {
                            await fs.access(repeatedProgressBarPath);
                            const stats = await fs.stat(repeatedProgressBarPath);
                            const sizeMB = (stats.size / 1024 / 1024).toFixed(2);

                            logger.info(`[${functionName}] 重复进度条视频生成成功: ${repeatedProgressBarPath}`);
                            logger.info(`[${functionName}] 重复进度条视频大小: ${sizeMB}MB`);

                            resolve(repeatedProgressBarPath);
                        } catch (error) {
                            reject(new Error(`重复进度条视频不存在: ${repeatedProgressBarPath}`));
                        }
                    } else {
                        const errorMsg = `重复进度条视频生成失败，退出码: ${code}`;
                        logger.error(`[${functionName}] ${errorMsg}`);
                        logger.error(`[${functionName}] FFmpeg错误输出: ${ffmpegError.slice(-500)}`);
                        reject(new Error(errorMsg));
                    }
                });

                ffmpegProcess.on('error', (error) => {
                    const errorMsg = `重复进度条FFmpeg进程启动失败: ${error.message}`;
                    logger.error(`[${functionName}] ${errorMsg}`);
                    reject(new Error(errorMsg));
                });

            } catch (error) {
                logger.error(`[${functionName}] 重复进度条视频失败: ${error.message}`);
                reject(error);
            }
        });
    }

    /**
     * @功能概述: 生成带报纸背景+80%黑色遮罩的背景视频 - 完全照抄generate-video.test.js的generateBackgroundVideo函数
     * @参数说明:
     *   - totalDuration: 总视频时长（秒）= getAudioDuration * videoConfig.repeatCount
     *   - videoConfig: {Object} 视频配置对象
     *   - videoIdentifier: {string} 视频标识符
     *   - savePath: {string} 保存路径
     * @返回值: Promise<string> - 生成的背景视频文件路径
     */
    async generateBackgroundVideo(totalDuration, videoConfig, videoIdentifier, savePath) {
        const functionName = 'generateBackgroundVideo';

        logger.info(`[${functionName}] 开始使用Canvas生成背景视频...`);
        logger.info(`[${functionName}] 总视频时长: ${totalDuration.toFixed(2)} 秒`);

        // 构建背景视频文件路径
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backgroundFilename = `${videoIdentifier}_background_${timestamp}.mp4`;
        const backgroundPath = path.join(savePath, backgroundFilename);

        try {
            // 容错性配置验证和默认值处理
            let backgroundStyle = videoConfig.backgroundStyle;
            if (!backgroundStyle || (backgroundStyle !== 'newspaper' && backgroundStyle !== 'abstract')) {
                logger.warn(`[${functionName}] backgroundStyle配置无效 (${backgroundStyle})，使用默认值: newspaper`);
                backgroundStyle = 'newspaper';
            }
            logger.info(`[${functionName}] 使用背景风格: ${backgroundStyle}`);

            // 根据配置选择背景图片路径
            let backgroundImagePath;
            if (backgroundStyle === 'abstract') {
                backgroundImagePath = this.ABSTRACT_BACKGROUND_PATH;
            } else {
                backgroundImagePath = this.NEWSPAPER_BACKGROUND_PATH;
            }

            // 检查选定的背景图片是否存在，如果不存在则使用纯色背景
            try {
                await fs.access(backgroundImagePath);
                logger.info(`[${functionName}] 使用${backgroundStyle}背景图片: ${backgroundImagePath}`);
            } catch (error) {
                logger.warn(`[${functionName}] ${backgroundStyle}背景图片不存在，使用纯色背景: ${error.message}`);
                backgroundImagePath = null; // 使用纯色背景
            }

            // 使用Canvas生成背景视频
            const result = await generateBackgroundVideoWithCanvas({
                duration: totalDuration,
                width: videoConfig.width,
                height: videoConfig.height,
                backgroundImagePath: backgroundImagePath,
                framerate: videoConfig.framerate,
                outputPath: backgroundPath
            });

            logger.info(`[${functionName}] Canvas背景视频生成成功: ${result}`);
            return result;

        } catch (error) {
            logger.error(`[${functionName}] Canvas背景视频生成失败: ${error.message}`);
            throw error;
        }
    }



    /**
     * @功能概述: 生成9:16比例的视频，将原始视频烧录到纯白背景上，叠加进度条视频和ASS字幕，时长与音频文件匹配 - 完全照抄generate-video.test.js的generateVideo函数
     * @参数说明:
     *   - totalAudioDuration: {number} 总音频时长（秒），用于确定视频时长
     *   - extendedAudioPath: {string} 扩展音频文件路径，用于合成最终视频
     *   - repeatedProgressBarVideoPath: {string} 重复进度条视频路径，用于叠加到主视频上
     *   - backgroundVideoPath: {string} 背景视频路径，Canvas生成的报纸背景
     *   - assContent: {string} ASS字幕内容，直接用于烧录字幕
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - videoConfig: {Object} 视频配置对象
     *   - videoIdentifier: {string} 视频标识符
     *   - savePath: {string} 保存路径
     * @返回值: {Promise<string>} 生成的输出视频文件完整路径
     * @技术方案: 使用 -filter_complex_script 将复杂的滤镜写入文件，并使用subtitles滤镜烧录ASS字幕。
     */
    async generateVideo(totalAudioDuration, extendedAudioPath, repeatedProgressBarVideoPath, backgroundVideoPath, assContent, originalVideoPath, videoConfig, videoIdentifier, savePath) {
        const functionName = 'generateVideo';

        logger.info(`[${functionName}] 开始生成9:16视频...`);
        logger.info(`[${functionName}] 视频时长: ${totalAudioDuration.toFixed(2)} 秒`);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // 生成短随机ID确保唯一性（避免长标题导致文件名过长）
        const randomId = Math.random().toString(36).substring(2, 8);

        // 最终视频文件仍使用videoIdentifier（用户需要识别）
        const uniqueVideoFilename = `${videoIdentifier}_extended_video_${timestamp}.mp4`;
        const outputVideoPath = path.join(savePath, uniqueVideoFilename);

        // 临时文件使用简短命名（避免Windows文件名长度限制）
        const filterScriptPath = path.join(savePath, `filter_script_${timestamp}_${randomId}.txt`);
        const assSubtitlePath = path.join(savePath, `temp_ass_${timestamp}_${randomId}.ass`);

        logger.info(`[${functionName}] 输出视频路径: ${outputVideoPath}`);
        logger.info(`[${functionName}] 临时滤镜脚本路径: ${filterScriptPath}`);
        logger.info(`[${functionName}] 临时ASS字幕路径: ${assSubtitlePath}`);

        // 步骤 1: 定义最纯净、最正确的滤镜链，无需任何转义
        const maxOriginalVideoDuration = totalAudioDuration / videoConfig.repeatCount;
        const textAreaWidth = videoConfig.width;
        const textAreaHeight = Math.round(videoConfig.width * 9 / 16);
        const progressBarWidth = videoConfig.width;
        const progressBarX = (videoConfig.width - progressBarWidth) / 2;
        const originalVideoHeight = Math.round(videoConfig.width * 9 / 16);
        const originalVideoY = (videoConfig.height - originalVideoHeight) / 2;
        const progressBarY = originalVideoY + originalVideoHeight;

        const filterChain = [
            // 步骤1: 把原视频缩放到合适大小
            `[1:v]scale=${videoConfig.width}:-1[scaled_video]`,

            // 步骤2: 把缩放后的原视频叠加到Canvas背景视频上
            `[0:v][scaled_video]overlay=(W-w)/2:(H-h)/2:enable='between(t,0,${maxOriginalVideoDuration})'[with_video]`,

            // 步骤3: 把Canvas生成的进度条视频叠加上去
            `[with_video][3:v]overlay=${progressBarX}:${progressBarY}[with_progress]`,

            // 步骤4: 叠加灰色文本区背景
            `[with_progress][2:v]overlay=(W-w)/2:(H-h)/2:enable='gte(t,${maxOriginalVideoDuration})'[with_text_area]`,

            // 步骤5: 烧录ASS字幕到视频上
            `[with_text_area]subtitles='${assSubtitlePath.replace(/\\/g, '\\\\').replace(/:/g, '\\:')}'[final_video]`
        ];

        const filterScriptContent = filterChain.join(';');

        return new Promise(async (resolve, reject) => {
            try {
                // 步骤 2: 将ASS内容和滤镜指令写入临时文件
                await fs.writeFile(assSubtitlePath, assContent, 'utf8');
                logger.info(`[${functionName}] 成功创建临时ASS字幕文件: ${assSubtitlePath}`);

                await fs.writeFile(filterScriptPath, filterScriptContent);
                logger.info(`[${functionName}] 成功创建滤镜脚本文件: ${filterScriptPath}`);

                // 步骤 3: 构建FFmpeg命令，使用 -filter_complex_script
                const ffmpegArgs = [
                    // 输入0: Canvas生成的背景视频（报纸+80%黑色遮罩）
                    '-i', backgroundVideoPath,
                    // 输入1: 原视频
                    '-t', maxOriginalVideoDuration.toString(), '-i', originalVideoPath,
                    // 输入2: 文本区
                    '-f', 'lavfi', '-i', `color=${videoConfig.textArea.backgroundColor}:size=${textAreaWidth}x${textAreaHeight}:rate=${videoConfig.framerate}:duration=${totalAudioDuration}`,
                    // 输入3: 进度条视频
                    '-i', repeatedProgressBarVideoPath,
                    // 输入4: 音频
                    '-i', extendedAudioPath,
                    // 核心：从文件读取复杂滤镜
                    '-filter_complex_script', filterScriptPath,
                    // 映射输出
                    '-map', '[final_video]', '-map', '4:a',
                    // 编码设置
                    '-c:v', videoConfig.codec, '-preset', videoConfig.preset, '-crf', videoConfig.crf.toString(), '-r', videoConfig.framerate.toString(),
                    '-c:a', 'aac', '-b:a', '192k',
                    '-y', outputVideoPath
                ];

                logger.info(`[${functionName}] FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`);
                const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

                let ffmpegError = '';
                ffmpegProcess.stderr.on('data', (data) => {
                    const output = data.toString();
                    ffmpegError += output;
                    if (output.includes('time=')) {
                        const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                        if (timeMatch) {
                            logger.info(`[${functionName}] FFmpeg进度: ${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`);
                        }
                    }
                });

                ffmpegProcess.on('close', async (code) => {
                    // 步骤 4: 无论成功或失败，都清理临时文件
                    try {
                        await fs.unlink(filterScriptPath);
                        logger.info(`[${functionName}] 已删除临时滤镜脚本: ${filterScriptPath}`);
                    } catch (e) {
                        logger.warn(`[${functionName}] 删除临时滤镜脚本失败: ${e.message}`);
                    }

                    try {
                        await fs.unlink(assSubtitlePath);
                        logger.info(`[${functionName}] 已删除临时ASS字幕文件: ${assSubtitlePath}`);
                    } catch (e) {
                        logger.warn(`[${functionName}] 删除临时ASS字幕文件失败: ${e.message}`);
                    }

                    if (code === 0) {
                        logger.info(`[${functionName}] FFmpeg执行成功`);

                        // 步骤 5: 清理所有临时文件（背景视频、进度条视频、扩展音频）
                        try {
                            await fs.unlink(backgroundVideoPath);
                            logger.info(`[${functionName}] 已删除临时背景视频: ${backgroundVideoPath}`);
                        } catch (e) {
                            logger.warn(`[${functionName}] 删除临时背景视频失败: ${e.message}`);
                        }

                        try {
                            await fs.unlink(repeatedProgressBarVideoPath);
                            logger.info(`[${functionName}] 已删除临时进度条视频: ${repeatedProgressBarVideoPath}`);
                        } catch (e) {
                            logger.warn(`[${functionName}] 删除临时进度条视频失败: ${e.message}`);
                        }

                        try {
                            await fs.unlink(extendedAudioPath);
                            logger.info(`[${functionName}] 已删除临时扩展音频: ${extendedAudioPath}`);
                        } catch (e) {
                            logger.warn(`[${functionName}] 删除临时扩展音频失败: ${e.message}`);
                        }

                        resolve(outputVideoPath);
                    } else {
                        const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                        logger.error(`[${functionName}] ${errorMsg}`);
                        logger.error(`[${functionName}] FFmpeg错误输出: ${ffmpegError.slice(-1000)}`);
                        reject(new Error(errorMsg));
                    }
                });

                ffmpegProcess.on('error', (error) => {
                    const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                    logger.error(`[${functionName}] ${errorMsg}`);
                    reject(new Error(errorMsg));
                });

            } catch (error) {
                logger.error(`[${functionName}] 生成视频失败: ${error.message}`);
                // 确保如果文件创建失败，也尝试删除临时文件
                try { await fs.unlink(filterScriptPath); } catch (e) {}
                try { await fs.unlink(assSubtitlePath); } catch (e) {}
                reject(error);
            }
        });
    }
}

module.exports = GenerateVideoTask;
