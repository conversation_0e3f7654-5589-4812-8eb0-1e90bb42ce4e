# .env 文件

# 云服务器这样配置CentOS 环境的 FFmpeg 路径
#FFMPEG_PATH=/usr/bin/ffmpeg   # <-- 替换为您 which ffmpeg 得到的实际路径
#FFPROBE_PATH=/usr/bin/ffprobe # <-- 替换为您 which ffprobe 得到的实际路径

# 本地 Windows 环境的 FFmpeg 路径
FFMPEG_PATH=
FFPROBE_PATH=

# Azure OpenAI Service Configuration for Whisper
# Azure OpenAI 服务配置（用于 Whisper）
# 你的 Azure OpenAI 资源终结点
AZURE_OPENAI_ENDPOINT=
# 你的 Azure OpenAI 资源密钥
AZURE_OPENAI_KEY=
# 你在 Azure AI Foundry 门户中为 Whisper 模型部署的自定义名称
WHISPER_DEPLOYMENT_NAME=


# 文件上传目录
UPLOAD_DIR= 


# openrouter
OPENROUTER_API_KEY=
OPENROUTER_CHAT_COMPLETIONS_URL="https://openrouter.ai/api/v1/chat/completions"
# (注意: 请确认这是当前正确的 OpenRouter API v1 基础 URL，如果不是，请使用官方文档提供的)


# Azure AI Services 多服务资源配置
SPEECH_KEY=
SPEECH_ENDPOINT=

# Azure AI Services API配置
API_VERSION=
DEFAULT_LOCALE=
PROFANITY_FILTER=

#  Azure AI Services 可选：超时设置
REQUEST_TIMEOUT=


# Cloudflare Workers AI Configuration
# Cloudflare Workers AI 服务配置（用于替代 Azure Speech Service）
# 你的 Cloudflare API Token（具有 Workers AI 权限）
CLOUDFLARE_API_TOKEN=
# 你的 Cloudflare Account ID
CLOUDFLARE_ACCOUNT_ID=
# Whisper 模型名称（推荐使用）
CLOUDFLARE_WHISPER_MODEL=@cf/openai/whisper
# 备选 Whisper 模型（更快但可能精度稍低）
# CLOUDFLARE_WHISPER_MODEL=@cf/openai/whisper-large-v3-turbo
# 英语专用轻量模型
# CLOUDFLARE_WHISPER_MODEL=@cf/openai/whisper-tiny-en

# Cloudflare Workers AI 可选配置
# API 超时设置（毫秒）
CLOUDFLARE_REQUEST_TIMEOUT=300000
# 是否启用 Cloudflare Workers AI（true/false）
USE_CLOUDFLARE_AI=false
