/**
 * SubtitleClozeTask 真实LLM测试 - 仅第9个测试用例
 * 专门用于验证真实LLM调用和生成文件
 */

const fs = require('fs');
const path = require('path');
const SubtitleClozeTask = require('../SubtitleClozeTask');
const { TASK_STATUS } = require('../../constants/progress');
const logger = require('../../utils/logger');

// 测试日志前缀
const testLogPrefix = '[文件：SubtitleClozeTask-RealLLM-Only.test.js][字幕挖空任务真实LLM测试]';

// 简单断言函数
function assert(condition, message) {
    if (!condition) {
        throw new Error(`断言失败: ${message}`);
    }
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        throw new Error(`断言失败: ${message}. 期望: ${expected}, 实际: ${actual}`);
    }
}

function assertIncludes(text, substring, message) {
    if (!text.includes(substring)) {
        throw new Error(`断言失败: ${message}. 文本中未找到: "${substring}"`);
    }
}

// 运行单个测试用例的辅助函数
async function runSingleTest(testName, testFunction) {
    logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
    try {
        await testFunction();
        logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
        logger.info('');
        return true;
    } catch (error) {
        logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
        logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
        logger.error('');
        return false;
    }
}

// --- 测试数据准备 (使用真实数据) ---
const testSavePath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\test-data';
const testVideoIdentifier = 'test_234_real_llm';

// 创建测试输出目录
if (!fs.existsSync(testSavePath)) {
    fs.mkdirSync(testSavePath, { recursive: true });
}

// 从真实文件读取简化字幕JSON数组
const realDataFilePath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\videoFile-1749090956973-678162462_corrected.json';
const realSimplifiedSubtitleJsonArray = JSON.parse(fs.readFileSync(realDataFilePath, 'utf8'));

// 硬编码真实的完整文本（从指定文件读取）
const realCorrectedFullText = "Good afternoon, there is another wildfire evacuation alert tonight. The County of Northern Lights is telling residents of Hawk Hills, which is near Twin Lakes Provincial Park to be ready to evacuate at any time. Firefighters across northern Alberta have been trying to contain several out-of-control wildfires, and it has been an uphill battle with one community losing dozens of structures. Residents in the Inglewood neighborhood say they were woken up to a scene that felt straight out of a movie, a potentially impaired driver wreaking havoc, damaging multiple vehicles. Jasmine King explains. The homicide unit has been called in to investigate after a man was found dead in a downtown construction has hit another key route in the core. 107th Avenue has lane closures heading east and west. As Lisa McGregor reports, there are now roadblocks on every main route downtown in 48 hours. The Oilers will host Game 1 of the Stanley Cup Final, looking for some revenge against the Florida Panthers, who beat the team in seven last season. Our Slap Cornick joins me now in studio. Canada's premieres pitched projects to the Prime Minister today, including Danielle Smith, who's pushing for an oil pipeline. Morgan Black reports.";

// 主测试函数
async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 SubtitleClozeTask 真实LLM测试 ==========`);
    
    let passedTests = 0;
    let totalTests = 0;

    // 测试用例：成功执行挖空处理 (真实LLM)
    totalTests++;
    const testPassed = await runSingleTest('成功执行挖空处理 (真实LLM)', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            videoIdentifier: testVideoIdentifier,
            simplifiedSubtitleJsonArray: realSimplifiedSubtitleJsonArray,
            correctedFullText: realCorrectedFullText,
            savePath: testSavePath
        };
        const progressLogs = [];

        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 测试 '成功执行挖空处理' 将使用真实LLM。`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 输入数据详情:`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - videoIdentifier: ${context.videoIdentifier}`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - simplifiedSubtitleJsonArray长度: ${context.simplifiedSubtitleJsonArray.length}`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - correctedFullText长度: ${context.correctedFullText.length}字符`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - savePath: ${context.savePath}`);
        
        // 打印输入字幕数据
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 输入字幕数据:`);
        context.simplifiedSubtitleJsonArray.forEach((item, index) => {
            logger.info(`${testLogPrefix}[REAL_LLM_TEST] 字幕${index + 1}: [${item.start}s-${item.end}s] "${item.text}"`);
        });

        const result = await task.execute(context, (data) => {
            progressLogs.push(data);
            // 最高细粒度的进度日志
            logger.info(`${testLogPrefix}[PROGRESS_DETAIL] ${JSON.stringify(data, null, 2)}`);
        });

        // 验证基本结果
        assert(result, '任务执行应返回结果');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        assert(progressLogs.length > 0, '应记录进度回调');
        assert(progressLogs.some(log => log.status === 'running'), '进度回调应包含至少一个主要处理状态');
        assert(progressLogs.every(log => log.taskName === 'SubtitleClozeTask'), '进度回调应包含正确的任务名称');

        // 验证返回结果结构
        assert(Array.isArray(result.clozedSubtitleJsonArray), '结果中应包含挖空后的字幕JSON数组');
        assert(result.clozedSubtitleJsonPath, '结果中应包含挖空后字幕JSON文件路径');
        assert(result.clozedEnglishSrtContent, '结果中应包含挖空后的SRT内容');
        assert(result.clozedEnglishSrtPath, '结果中应包含挖空后SRT文件路径');
        assertEquals(result.subtitleClozeTaskStatus, 'success', '任务状态应为success');

        // 验证文件存在
        assert(fs.existsSync(result.clozedSubtitleJsonPath), `挖空后字幕JSON文件应存在: ${result.clozedSubtitleJsonPath}`);
        assert(fs.existsSync(result.clozedEnglishSrtPath), `挖空后SRT文件应存在: ${result.clozedEnglishSrtPath}`);

        // 验证文件内容
        const savedJsonContent = fs.readFileSync(result.clozedSubtitleJsonPath, 'utf8');
        const savedSrtContent = fs.readFileSync(result.clozedEnglishSrtPath, 'utf8');
        assert(savedJsonContent.length > 0, '保存的JSON文件内容不应为空');
        assert(savedSrtContent.length > 0, '保存的SRT文件内容不应为空');

        // 验证挖空效果
        assertIncludes(result.clozedEnglishSrtContent, '()', 'LLM输出的SRT内容应包含挖空标记 "()"');

        // 验证JSON数组结构
        const parsedJson = JSON.parse(savedJsonContent);
        assert(Array.isArray(parsedJson), '保存的JSON应为数组');
        assertEquals(parsedJson.length, realSimplifiedSubtitleJsonArray.length, '挖空后数组长度应与原始一致');

        // 验证每个条目的结构（与TranslateSubtitleTask保持一致）
        parsedJson.forEach((item, index) => {
            assert(item.id, `条目 ${index + 1} 应有id字段`);
            assert(item.start !== undefined, `条目 ${index + 1} 应有start字段`);
            assert(item.end !== undefined, `条目 ${index + 1} 应有end字段`);
            assert(item.text, `条目 ${index + 1} 应有text字段`);
        });

        // 最高细粒度的LLM响应日志输出
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] ========== 完整LLM响应分析 ==========`);
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 原始字幕数据:`);
        realSimplifiedSubtitleJsonArray.forEach((item, index) => {
            logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 原始${index + 1}: "${item.text}"`);
        });
        
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] LLM挖空后数据:`);
        parsedJson.forEach((item, index) => {
            logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 挖空${index + 1}: "${item.text}"`);
            
            // 分析挖空变化
            const originalText = realSimplifiedSubtitleJsonArray[index].text;
            const clozedText = item.text;
            const hasChanges = originalText !== clozedText;
            const parenthesesCount = (clozedText.match(/\(\)/g) || []).length;
            
            logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 变化分析${index + 1}: 是否挖空=${hasChanges}, 挖空数量=${parenthesesCount}`);
            if (hasChanges) {
                logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 对比${index + 1}:`);
                logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL]   原文: "${originalText}"`);
                logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL]   挖空: "${clozedText}"`);
            }
        });

        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 完整SRT内容:\n${result.clozedEnglishSrtContent}`);
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] 完整JSON内容:\n${savedJsonContent}`);
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] JSON文件保存在: ${result.clozedSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] SRT文件保存在: ${result.clozedEnglishSrtPath}`);
        logger.info(`${testLogPrefix}[REAL_LLM_RESPONSE_FULL] ========== LLM响应分析结束 ==========`);
    });

    if (testPassed) passedTests++;

    // 测试结果汇总
    logger.info(`${testLogPrefix} ========== SubtitleClozeTask 真实LLM测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${totalTests}`);
    logger.info(`${testLogPrefix} 通过: ${passedTests}`);
    logger.info(`${testLogPrefix} 失败: ${totalTests - passedTests}`);

    if (passedTests === totalTests) {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0);
    } else {
        logger.error(`${testLogPrefix} ❌ 有测试用例失败!`);
        process.exit(1);
    }
}

// 运行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试执行出错: ${error.message}`);
    logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    process.exit(1);
});
