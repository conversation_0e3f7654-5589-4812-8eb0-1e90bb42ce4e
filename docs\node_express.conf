server {
    listen 80;
    listen 443 ssl http2;
    listen 443 quic; # 如果您确认 QUIC/HTTP3 已配置并需要
    server_name n8n.shuimitao.online;
    client_max_body_size 1024m;

    # SSL 相关配置 (从您当前配置中保留)
    ssl_certificate    /www/server/panel/vhost/cert/express/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/express/privkey.pem;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+CHACHA20-draft:EECDH+AES128:RSA+AES128:EECDH+AES256:RSA+AES256:EECDH+3DES:RSA+3DES:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    add_header Strict-Transport-Security "max-age=31536000";
    add_header Alt-Svc 'quic=":443"; h3=":443"; h3-29=":443"; h3-27=":443";h3-25=":443"; h3-T050=":443"; h3-Q050=":443";h3-Q049=":443";h3-Q048=":443"; h3-Q046=":443"; h3-Q043=":443"'; # 如果 QUIC/HTTP3 已配置并需要
    error_page 497  https://$host$request_uri; # HTTP 到 HTTPS 跳转

    # 伪静态规则 (如果前端不需要特别的伪静态，可以注释掉或移除这个 include)
    # 如果后端 API 需要伪静态，应考虑将其规则放在 location /api/ 内部或专门为 API 设计
    # include /www/server/panel/vhost/rewrite/node_express.conf;

    # 禁止访问的文件或目录 (保留)
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.svn|\.project|LICENSE|README.md|package.json|package-lock.json|\.env) {
        return 404;
    }

    # SSL 证书验证目录 (保留)
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除 (这个 include 通常由宝塔管理)
    include /www/server/panel/vhost/nginx/well-known/express.conf;
    # location /.well-known/ { # 这个 include 可能已经处理了 .well-known
    #     root  /www/wwwroot/express; # 确保此路径正确或由 include 处理
    # }

    #禁止在证书验证目录放入敏感文件 (保留)
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    # ---- START: 前后端分离的核心配置 (已修改为后端处理根路径) ----
    # 1. 将根路径 / 的请求反向代理到后端 Express 应用
    location / {
        proxy_pass http://127.0.0.1:3000;       # 将请求代理到后端
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade"; # 支持 WebSocket

        # 根据需要调整超时设置
        proxy_connect_timeout 30s;
        proxy_read_timeout 60s; 
        proxy_send_timeout 30s;
    }

    # 2. 反向代理后端 API 请求 (保持不变)
    location /api/ {                             # 假设所有后端 API 都有 /api/ 前缀
        proxy_pass http://127.0.0.1:3000;       # 将 3000 替换为您的后端实际运行端口
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade"; # 支持 WebSocket

        # 根据需要调整超时设置
        proxy_connect_timeout 30s;
        proxy_read_timeout 60s; # 86400s (24小时) 可能太长，除非有特殊需求
        proxy_send_timeout 30s;
    }
    # ---- END: 前后端分离的核心配置 ----

    # Nginx 缓存清理 (如果使用 Nginx 缓存，保留)
    location ~ /purge(/.*) {
        proxy_cache_purge cache_one $host$request_uri$is_args$args;
    }

    # 访问和错误日志 (保留)
    access_log  /www/wwwlogs/n8n.shuimitao.online.log;
    error_log  /www/wwwlogs/n8n.shuimitao.online.error.log;
}