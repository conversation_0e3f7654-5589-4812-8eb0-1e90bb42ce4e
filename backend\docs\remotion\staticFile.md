# staticFile()

## 概述

`staticFile()` 将 `public/` 文件夹中的文件转换为URL，然后可以将其加载到项目中。这是访问静态资源的推荐方式。

**版本要求**: v2.5.7+

## 语法

```typescript
import { staticFile } from "remotion";

const fileUrl = staticFile("/path/to/file.ext");
```

## 参数

- **path**: `string` - 相对于 `public/` 文件夹的文件路径

## 返回值

- **类型**: `string`
- **描述**: 可用于加载资源的URL字符串

## 基础用法

### 1. 项目结构设置

```
my-video/
├─ node_modules/
├─ public/
│  ├─ images/
│  │  ├─ logo.png
│  │  ├─ background.jpg
│  ├─ audio/
│  │  ├─ music.mp3
│  │  ├─ sound-effect.wav
│  ├─ videos/
│  │  ├─ intro.mp4
│  ├─ fonts/
│  │  ├─ custom-font.woff2
│  ├─ data/
│  │  ├─ config.json
├─ src/
│  ├─ Root.tsx
│  ├─ index.ts
├─ package.json
```

**重要**: `public/` 文件夹应始终与包含 `remotion` 依赖的 `package.json` 位于同一文件夹中。

### 2. 基础文件引用

```typescript
import { staticFile, Img, Video, Audio } from "remotion";

const MyComponent = () => {
  // 图片资源
  const logoUrl = staticFile("/images/logo.png");
  const backgroundUrl = staticFile("/images/background.jpg");
  
  // 音频资源
  const musicUrl = staticFile("/audio/music.mp3");
  
  // 视频资源
  const introUrl = staticFile("/videos/intro.mp4");

  return (
    <div>
      <Img src={logoUrl} alt="Logo" />
      <Video src={introUrl} />
      <Audio src={musicUrl} />
    </div>
  );
};
```

## 实际应用场景

### 1. 图片和媒体资源

```typescript
import { staticFile, Img, Video, Audio, AbsoluteFill } from "remotion";

const MediaShowcase = () => {
  return (
    <AbsoluteFill>
      {/* 背景图片 */}
      <Img 
        src={staticFile("/images/background.jpg")} 
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover"
        }}
      />
      
      {/* 前景视频 */}
      <Video 
        src={staticFile("/videos/overlay.mp4")}
        style={{
          position: "absolute",
          top: 50,
          left: 50,
          width: 400,
          height: 300
        }}
      />
      
      {/* 背景音乐 */}
      <Audio 
        src={staticFile("/audio/background-music.mp3")}
        volume={0.3}
      />
      
      {/* Logo */}
      <Img 
        src={staticFile("/images/logo.png")}
        style={{
          position: "absolute",
          bottom: 20,
          right: 20,
          width: 100,
          height: 50
        }}
      />
    </AbsoluteFill>
  );
};
```

### 2. 字体加载

```typescript
import { staticFile } from "remotion";
import { useEffect, useState } from "react";

const CustomFontComponent = () => {
  const [fontLoaded, setFontLoaded] = useState(false);

  useEffect(() => {
    const loadFont = async () => {
      const fontUrl = staticFile("/fonts/custom-font.woff2");
      
      const font = new FontFace("CustomFont", `url(${fontUrl})`);
      await font.load();
      document.fonts.add(font);
      
      setFontLoaded(true);
    };

    loadFont();
  }, []);

  return (
    <div style={{
      fontFamily: fontLoaded ? "CustomFont" : "Arial",
      fontSize: 48,
      textAlign: "center"
    }}>
      {fontLoaded ? "自定义字体已加载" : "加载中..."}
    </div>
  );
};
```

### 3. JSON数据加载

```typescript
import { staticFile } from "remotion";
import { useEffect, useState } from "react";
import { delayRender, continueRender } from "remotion";

interface ConfigData {
  title: string;
  colors: {
    primary: string;
    secondary: string;
  };
  animations: {
    duration: number;
    easing: string;
  };
}

const DataDrivenComponent = () => {
  const [config, setConfig] = useState<ConfigData | null>(null);
  const [handle] = useState(() => delayRender("加载配置数据"));

  useEffect(() => {
    const loadConfig = async () => {
      try {
        const configUrl = staticFile("/data/config.json");
        const response = await fetch(configUrl);
        const data = await response.json();
        setConfig(data);
        continueRender(handle);
      } catch (error) {
        console.error("配置加载失败:", error);
        continueRender(handle);
      }
    };

    loadConfig();
  }, [handle]);

  if (!config) {
    return <div>加载配置中...</div>;
  }

  return (
    <div style={{ 
      backgroundColor: config.colors.primary,
      color: config.colors.secondary,
      padding: 20
    }}>
      <h1>{config.title}</h1>
      <p>动画时长: {config.animations.duration}ms</p>
    </div>
  );
};
```

### 4. 动态资源选择

```typescript
import { staticFile, Img } from "remotion";
import { useCurrentFrame, interpolate } from "remotion";

const DynamicImageSequence = () => {
  const frame = useCurrentFrame();
  
  // 根据帧数选择不同的图片
  const imageIndex = Math.floor(interpolate(frame, [0, 300], [1, 10], {
    extrapolateRight: "clamp"
  }));
  
  const imageUrl = staticFile(`/images/sequence/frame-${imageIndex.toString().padStart(3, '0')}.png`);

  return (
    <Img 
      src={imageUrl} 
      style={{
        width: "100%",
        height: "100%",
        objectFit: "cover"
      }}
    />
  );
};
```

### 5. 多语言资源

```typescript
import { staticFile, Audio } from "remotion";
import { getInputProps } from "remotion";

interface LocalizedProps {
  language: "zh" | "en" | "es" | "fr";
}

const LocalizedAudio = () => {
  const { language } = getInputProps() as LocalizedProps;
  
  // 根据语言选择不同的音频文件
  const audioUrl = staticFile(`/audio/narration-${language}.mp3`);
  
  return <Audio src={audioUrl} />;
};
```

### 6. 条件性资源加载

```typescript
import { staticFile, Video } from "remotion";

interface ConditionalMediaProps {
  quality: "low" | "medium" | "high";
  includeIntro: boolean;
}

const ConditionalMedia = ({ quality, includeIntro }: ConditionalMediaProps) => {
  // 根据质量设置选择不同的视频文件
  const getVideoUrl = (quality: string) => {
    switch (quality) {
      case "low": return staticFile("/videos/content-720p.mp4");
      case "medium": return staticFile("/videos/content-1080p.mp4");
      case "high": return staticFile("/videos/content-4k.mp4");
      default: return staticFile("/videos/content-1080p.mp4");
    }
  };

  return (
    <>
      {includeIntro && (
        <Video src={staticFile("/videos/intro.mp4")} />
      )}
      <Video src={getVideoUrl(quality)} />
    </>
  );
};
```

### 7. 资源预加载

```typescript
import { staticFile } from "remotion";
import { useEffect } from "react";

const PreloadAssets = () => {
  useEffect(() => {
    // 预加载关键资源
    const preloadAssets = [
      staticFile("/images/hero-background.jpg"),
      staticFile("/videos/main-content.mp4"),
      staticFile("/audio/soundtrack.mp3")
    ];

    preloadAssets.forEach(url => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = url;
      
      if (url.includes(".mp4")) {
        link.as = "video";
      } else if (url.includes(".mp3")) {
        link.as = "audio";
      } else if (url.includes(".jpg") || url.includes(".png")) {
        link.as = "image";
      }
      
      document.head.appendChild(link);
    });
  }, []);

  return null; // 这是一个预加载组件，不渲染任何内容
};
```

## URI不安全字符处理

### v4.0.0+ 自动编码

```typescript
// v4.0.0+ 自动处理特殊字符
const imageUrl = staticFile("/images/my-image#portrait.png");
// 输出: "/static-32e8nd/my-image%23portrait.png"

const videoUrl = staticFile("/videos/video with spaces & symbols.mp4");
// 输出: "/static-32e8nd/video%20with%20spaces%20%26%20symbols.mp4"
```

### 迁移注意事项

```typescript
// v4.0.0 之前需要手动编码
// ❌ 不要在 v4.0.0+ 中这样做
const oldWay = staticFile(encodeURIComponent("/images/special#chars.png"));

// ✅ v4.0.0+ 正确做法
const newWay = staticFile("/images/special#chars.png");
```

## 与其他API结合使用

### 1. 与 getStaticFiles 结合

```typescript
import { staticFile, getStaticFiles } from "remotion";

const DynamicAssetLoader = () => {
  const availableFiles = getStaticFiles();
  
  // 过滤出图片文件
  const imageFiles = availableFiles.filter(file => 
    file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg')
  );

  return (
    <div>
      {imageFiles.map(file => (
        <img 
          key={file}
          src={staticFile(file)} 
          alt={file}
          style={{ width: 100, height: 100, margin: 5 }}
        />
      ))}
    </div>
  );
};
```

### 2. 与 watchStaticFile 结合

```typescript
import { staticFile, watchStaticFile } from "remotion";
import { useEffect, useState } from "react";

const WatchedAsset = () => {
  const [content, setContent] = useState("");
  const configFile = "/data/dynamic-config.json";

  useEffect(() => {
    // 监听文件变化
    const unwatch = watchStaticFile(configFile, async () => {
      const url = staticFile(configFile);
      const response = await fetch(url);
      const data = await response.text();
      setContent(data);
    });

    return unwatch;
  }, []);

  return <pre>{content}</pre>;
};
```

## 最佳实践

### 1. 文件组织

```typescript
// 推荐的文件组织结构
const AssetManager = {
  images: {
    logo: () => staticFile("/images/branding/logo.png"),
    backgrounds: {
      main: () => staticFile("/images/backgrounds/main.jpg"),
      secondary: () => staticFile("/images/backgrounds/secondary.jpg")
    }
  },
  audio: {
    music: () => staticFile("/audio/music/background.mp3"),
    effects: {
      transition: () => staticFile("/audio/effects/transition.wav")
    }
  },
  videos: {
    intro: () => staticFile("/videos/intro.mp4"),
    outro: () => staticFile("/videos/outro.mp4")
  }
};

// 使用
const MyComponent = () => {
  return (
    <div>
      <img src={AssetManager.images.logo()} alt="Logo" />
      <audio src={AssetManager.audio.music()} />
    </div>
  );
};
```

### 2. 类型安全的资源管理

```typescript
type AssetPath = 
  | "/images/logo.png"
  | "/images/background.jpg"
  | "/audio/music.mp3"
  | "/videos/intro.mp4";

const typedStaticFile = (path: AssetPath): string => {
  return staticFile(path);
};

// 使用时会有类型提示和检查
const logoUrl = typedStaticFile("/images/logo.png"); // ✅
// const invalidUrl = typedStaticFile("/invalid/path.png"); // ❌ 类型错误
```

### 3. 环境相关资源

```typescript
const getEnvironmentAsset = (basePath: string) => {
  const env = process.env.NODE_ENV;
  const suffix = env === "development" ? "-dev" : "";
  return staticFile(`${basePath}${suffix}.png`);
};

// 开发环境使用 logo-dev.png，生产环境使用 logo.png
const logoUrl = getEnvironmentAsset("/images/logo");
```

## 常见问题

### 1. 为什么不能直接使用字符串？

```typescript
// ❌ 不推荐 - 可能在部署到子目录时出现问题
<img src="/my-image.png" />

// ✅ 推荐 - 框架无关，支持子目录部署
<Img src={staticFile("/my-image.png")} />
```

### 2. 文件路径大小写敏感

```typescript
// 确保文件路径与实际文件名大小写完全匹配
const correctUrl = staticFile("/images/Logo.PNG"); // 如果文件名是 Logo.PNG
// const wrongUrl = staticFile("/images/logo.png"); // 如果实际文件名不匹配会出错
```

### 3. 相对路径处理

```typescript
// ✅ 正确 - 以 / 开头的绝对路径
const imageUrl = staticFile("/images/photo.jpg");

// ❌ 错误 - 不要使用相对路径
// const imageUrl = staticFile("images/photo.jpg");
```

## 相关 API

- [`getStaticFiles()`](./getStaticFiles.md) - 获取所有静态文件列表
- [`watchStaticFile()`](./watchStaticFile.md) - 监听静态文件变化
- [`<Img>`](./Img.md) - 图片组件
- [`<Video>`](./Video.md) - 视频组件
- [`<Audio>`](./Audio.md) - 音频组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/static-file.ts)
