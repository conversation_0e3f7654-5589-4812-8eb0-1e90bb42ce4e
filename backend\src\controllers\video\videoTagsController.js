/**
 * @功能概述: 视频标注控制器 - 处理视频标注相关的API请求
 * @创建时间: 2025-07-26
 * @架构说明: 基于现有项目架构，在generated目录中管理tags.json文件
 */

const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');

// 获取uploads目录路径
const uploadsDir = path.join(__dirname, '../../../uploads');

/**
 * @功能概述: 更新视频发布状态
 * @API路径: PUT /api/video/updateVideoTags/:projectId
 * @请求参数:
 *   - projectId: 项目ID（路径参数）
 *   - videos: 视频状态数据（请求体），格式：{"video_v1.mp4": {"status": "published"}}
 * @响应格式: 成功返回更新后的状态数据，失败返回错误信息
 */
const updateVideoTags = async (req, res) => {
    const functionLogPrefix = '[文件：videoTagsController.js][视频标注控制器][updateVideoTags]';
    
    try {
        const { projectId } = req.params;
        const { videos } = req.body;
        
        logger.info(`${functionLogPrefix} 开始更新视频发布状态，项目ID: ${projectId}`);
        logger.debug(`${functionLogPrefix} 接收到的状态数据: ${JSON.stringify(videos, null, 2)}`);
        
        // 步骤1: 参数验证
        if (!projectId) {
            const errorMsg = '项目ID不能为空';
            logger.warn(`${functionLogPrefix} 参数验证失败: ${errorMsg}`);
            return res.status(400).json({
                success: false,
                message: errorMsg
            });
        }
        
        if (!videos || typeof videos !== 'object') {
            const errorMsg = '视频状态数据格式错误';
            logger.warn(`${functionLogPrefix} 参数验证失败: ${errorMsg}`);
            return res.status(400).json({
                success: false,
                message: errorMsg
            });
        }
        
        // 步骤2: 构建项目路径
        const projectPath = path.join(uploadsDir, 'projects', projectId);
        const generatedPath = path.join(projectPath, 'generated');
        const tagsFilePath = path.join(generatedPath, 'tags.json');
        
        logger.debug(`${functionLogPrefix} 项目路径: ${projectPath}`);
        logger.debug(`${functionLogPrefix} generated路径: ${generatedPath}`);
        logger.debug(`${functionLogPrefix} tags.json路径: ${tagsFilePath}`);
        
        // 步骤3: 验证项目是否存在
        if (!fs.existsSync(projectPath)) {
            const errorMsg = `项目不存在: ${projectId}`;
            logger.warn(`${functionLogPrefix} 项目验证失败: ${errorMsg}`);
            return res.status(404).json({
                success: false,
                message: errorMsg
            });
        }
        
        // 步骤4: 验证generated目录是否存在
        if (!fs.existsSync(generatedPath)) {
            const errorMsg = `项目的generated目录不存在: ${projectId}`;
            logger.warn(`${functionLogPrefix} generated目录验证失败: ${errorMsg}`);
            return res.status(404).json({
                success: false,
                message: errorMsg
            });
        }
        
        // 步骤5: 读取现有的tags.json（如果存在）
        let existingStatus = {};
        if (fs.existsSync(tagsFilePath)) {
            try {
                const existingContent = fs.readFileSync(tagsFilePath, 'utf8');
                existingStatus = JSON.parse(existingContent);
                logger.debug(`${functionLogPrefix} 读取到现有状态数据: ${JSON.stringify(existingStatus, null, 2)}`);
            } catch (parseError) {
                logger.warn(`${functionLogPrefix} 解析现有tags.json失败，将创建新文件: ${parseError.message}`);
                existingStatus = {};
            }
        } else {
            logger.info(`${functionLogPrefix} tags.json文件不存在，将创建新文件`);
        }
        
        // 步骤6: 合并状态数据
        const updatedStatus = { ...existingStatus, ...videos };
        logger.debug(`${functionLogPrefix} 合并后的状态数据: ${JSON.stringify(updatedStatus, null, 2)}`);

        // 步骤7: 写入tags.json文件
        fs.writeFileSync(tagsFilePath, JSON.stringify(updatedStatus, null, 2), 'utf8');
        logger.info(`${functionLogPrefix} 状态数据写入成功: ${tagsFilePath}`);
        
        // 步骤8: 返回成功响应
        res.json({
            success: true,
            message: '视频发布状态更新成功',
            data: {
                projectId,
                updatedVideos: Object.keys(videos),
                totalVideos: Object.keys(updatedStatus).length,
                status: updatedStatus
            }
        });

        logger.info(`${functionLogPrefix} 视频发布状态更新完成，更新了${Object.keys(videos).length}个视频`);
        
    } catch (error) {
        logger.error(`${functionLogPrefix} 更新视频标注失败: ${error.message}`);
        logger.error(`${functionLogPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            success: false,
            message: '服务器内部错误',
            error: error.message
        });
    }
};

/**
 * @功能概述: 获取视频发布状态
 * @API路径: GET /api/video/getVideoTags/:projectId
 * @请求参数:
 *   - projectId: 项目ID（路径参数）
 * @响应格式: 成功返回状态数据，失败返回错误信息
 */
const getVideoTags = async (req, res) => {
    const functionLogPrefix = '[文件：videoTagsController.js][视频标注控制器][getVideoTags]';
    
    try {
        const { projectId } = req.params;
        
        logger.info(`${functionLogPrefix} 开始获取视频发布状态，项目ID: ${projectId}`);
        
        // 步骤1: 参数验证
        if (!projectId) {
            const errorMsg = '项目ID不能为空';
            logger.warn(`${functionLogPrefix} 参数验证失败: ${errorMsg}`);
            return res.status(400).json({
                success: false,
                message: errorMsg
            });
        }
        
        // 步骤2: 构建文件路径
        const projectPath = path.join(uploadsDir, 'projects', projectId);
        const generatedPath = path.join(projectPath, 'generated');
        const tagsFilePath = path.join(generatedPath, 'tags.json');
        
        // 步骤3: 验证项目是否存在
        if (!fs.existsSync(projectPath)) {
            const errorMsg = `项目不存在: ${projectId}`;
            logger.warn(`${functionLogPrefix} 项目验证失败: ${errorMsg}`);
            return res.status(404).json({
                success: false,
                message: errorMsg
            });
        }
        
        // 步骤4: 读取tags.json
        let status = {};
        if (fs.existsSync(tagsFilePath)) {
            try {
                const content = fs.readFileSync(tagsFilePath, 'utf8');
                status = JSON.parse(content);
                logger.debug(`${functionLogPrefix} 读取到状态数据: ${JSON.stringify(status, null, 2)}`);
            } catch (parseError) {
                logger.warn(`${functionLogPrefix} 解析tags.json失败: ${parseError.message}`);
                status = {};
            }
        } else {
            logger.info(`${functionLogPrefix} tags.json文件不存在，返回空数据`);
        }
        
        // 步骤5: 返回成功响应
        res.json({
            success: true,
            message: '获取视频发布状态成功',
            data: {
                projectId,
                totalVideos: Object.keys(status).length,
                status
            }
        });

        logger.info(`${functionLogPrefix} 视频发布状态获取完成，共${Object.keys(status).length}个视频`);
        
    } catch (error) {
        logger.error(`${functionLogPrefix} 获取视频标注失败: ${error.message}`);
        logger.error(`${functionLogPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            success: false,
            message: '服务器内部错误',
            error: error.message
        });
    }
};

module.exports = {
    updateVideoTags,
    getVideoTags
};
