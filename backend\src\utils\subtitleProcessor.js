const logger = require('./logger'); // 假设 logger.js 在同级或可访问路径

/**
 * @功能概述: 从原始API响应数据中提取用于字幕处理的关键信息。
 * @param {object} apiResponse - 完整的API响应对象 (例如，从 res.json 读取的内容)。
 * @returns {object|null} 包含提取数据的对象 { fullText, language, duration, segments }，如果输入无效则返回 null。
 * 
 * @执行流程:
 *   1.  校验输入参数 apiResponse 是否有效。
 *   2.  尝试从 apiResponse.data 中提取 fullText。
 *   3.  尝试从 apiResponse.data.apiResponseData 中提取 language, duration, 和 segments。
 *   4.  如果任何关键数据缺失，记录警告并返回 null。
 *   5.  返回包含所有提取数据的对象。
 */
function extractRawJsonData(apiResponse) {
    const logPrefix = '[文件：subtitleProcessor.js][字幕处理器][extractRawJsonData] ';
    logger.info(`${logPrefix}[步骤 1] 开始提取原始JSON数据。`);

    if (!apiResponse || typeof apiResponse !== 'object') {
        logger.warn(`${logPrefix}[步骤 1.1][WARN] 输入的 apiResponse 无效或非对象。`);
        return null;
    }

    if (!apiResponse.data || typeof apiResponse.data !== 'object') {
        logger.warn(`${logPrefix}[步骤 1.2][WARN] apiResponse.data 无效或非对象。`);
        return null;
    }
    // 旧的提取方式 (不正确，因为 apiResponse.data 直接是 { apiResponseData: { ... } } 结构)
    // const fullText = apiResponse.data.text; 

    // 首先获取包含原始 API 数据的对象
    const apiResponseData = apiResponse.data.apiResponseData; 
    if (!apiResponseData || typeof apiResponseData !== 'object') {
        // 日志：记录 apiResponse.data.apiResponseData 无效的情况
        logger.warn(`${logPrefix}[步骤 1.4][WARN] apiResponse.data.apiResponseData 无效或非对象。`);
        return null;
    }
    
    // 从 apiResponseData (即原始API响应体) 中提取 text 字段
    const fullText = apiResponseData.text; 

    if (typeof fullText === 'undefined') {
        // 日志：记录 fullText (从 apiResponseData.text) 未定义的情况
        logger.warn(`${logPrefix}[步骤 1.3][WARN] 从 apiResponseData.text 提取的 fullText 未定义。`);
        // fullText 可以是空字符串，所以 undefined 才是有问题的。如果允许空，则不应返回null。
        // 此处行为保持不变，若 fullText 为 undefined, 提取的数据中 fullText 也会是 undefined (后续会处理为 '')
    }
    // 日志：记录提取到的 fullText 的预览
    logger.debug(`${logPrefix}[步骤 2] 提取到的 fullText (源自 apiResponseData.text, 前100字符): ${(fullText || '').substring(0, 100)}`);


    const language = apiResponseData.language;
    const duration = apiResponseData.duration;
    const segments = apiResponseData.segments;

    if (typeof language === 'undefined') {
        logger.warn(`${logPrefix}[步骤 3.1][WARN] language 未定义。`);
        return null; // 假设 language 是必需的
    }
    if (typeof duration === 'undefined') {
        logger.warn(`${logPrefix}[步骤 3.2][WARN] duration 未定义。`);
        return null; // 假设 duration 是必需的
    }
    if (!Array.isArray(segments)) {
        logger.warn(`${logPrefix}[步骤 3.3][WARN] segments 不是数组或未定义。`);
        return null;
    }

    logger.info(`${logPrefix}[步骤 4] 成功提取关键数据。Language: ${language}, Duration: ${duration}, Segments count: ${segments.length}`);
    
    const extractedData = {
        fullText: fullText || '', // 确保 fullText 至少是空字符串
        language,
        duration,
        segments
    };
    logger.debug(`${logPrefix}[步骤 5] 返回的提取数据 (部分segments): ${JSON.stringify({ ...extractedData, segments: segments.slice(0, 2) })}`);
    return extractedData;
}

/**
 * @功能概述: 将总秒数格式化为SRT字幕的时间戳格式 (HH:MM:SS,mmm)。
 * @param {number} totalSeconds - 需要格式化的总秒数。
 * @returns {string} SRT格式的时间戳字符串。
 * 
 * @执行流程:
 *   1.  计算小时、分钟、秒和毫秒。
 *   2.  对每个时间单位进行补零操作，确保格式正确 (HH, MM, SS, mmm)。
 *   3.  组装并返回格式化的时间字符串。
 */
function formatSrtTime(totalSeconds) {
    const logPrefix = '[文件：subtitleProcessor.js][字幕处理器][formatSrtTime] ';
    if (typeof totalSeconds !== 'number' || isNaN(totalSeconds) || totalSeconds < 0) {
        logger.warn(`${logPrefix}[WARN] 输入的 totalSeconds 无效: ${totalSeconds}，将返回默认时间 00:00:00,000。`);
        return '00:00:00,000';
    }

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);
    const milliseconds = Math.round((totalSeconds - Math.floor(totalSeconds)) * 1000);

    const pad = (num, size = 2) => num.toString().padStart(size, '0');

    const formattedTime = `${pad(hours)}:${pad(minutes)}:${pad(seconds)},${pad(milliseconds, 3)}`;
    // logger.debug(`${logPrefix} 输入秒数 ${totalSeconds} 转换为 ${formattedTime}`); // 可选：如果需要频繁调试此函数，可以取消注释
    return formattedTime;
}

/**
 * @功能概述: 根据提取的 segments 数据生成SRT字幕格式的字符串。
 * @param {Array<object>} segments - 包含字幕片段信息的数组，每个片段应有 id, start, end, text 属性。
 * @returns {string} SRT格式的字幕字符串。
 * 
 * @执行流程:
 *   1.  校验 segments 输入是否为有效数组。
 *   2.  遍历 segments 数组。
 *   3.  为每个 segment 构建SRT条目：
 *       a.  计算SRT序号 (segment.id + 1)。
 *       b.  使用 formatSrtTime 格式化 segment.start 和 segment.end 时间。
 *       c.  获取 segment.text 并去除首尾空格。
 *       d.  按SRT格式组装序号、时间戳和文本。
 *   4.  将所有SRT条目连接成一个完整的字符串并返回。
 */
function generateSrt(segments) {
    // 定义日志前缀，用于标识此函数内的日志
    const logPrefix = '[文件：subtitleProcessor.js][字幕处理器][generateSrt] ';
    // 记录开始生成SRT字幕的日志，包含输入的segments数量
    logger.info(`${logPrefix}[步骤 1] 开始生成SRT字幕内容。Segments 数量: ${segments ? segments.length : '无效'}`);

    // 校验输入的segments是否为有效的非空数组
    if (!Array.isArray(segments) || segments.length === 0) {
        // 如果segments无效或为空，记录警告日志
        logger.warn(`${logPrefix}[步骤 1.1][WARN] 输入的 segments 无效或为空数组。将返回空SRT字符串。`);
        // 返回空字符串，表示无法生成SRT
        return '';
    }

    // 初始化用于存储生成的SRT内容的字符串
    let srtContent = '';
    // 使用try-catch块捕获生成过程中的潜在错误
    try {
        // 遍历segments数组，处理每一个字幕片段
        for (let i = 0; i < segments.length; i++) {
            // 获取当前处理的字幕片段
            const segment = segments[i];
            // 校验当前segment对象及其必需属性（id, start, end, text）是否存在且类型正确
            if (!segment || typeof segment !== 'object' ||
                typeof segment.id === 'undefined' ||
                typeof segment.start === 'undefined' ||
                typeof segment.end === 'undefined' ||
                typeof segment.text === 'undefined') {
                // 如果segment数据不完整或无效，记录警告日志并跳过当前片段
                logger.warn(`${logPrefix}[步骤 2.1][WARN] Segment 索引 ${i} 数据不完整或无效，已跳过: ${JSON.stringify(segment)}`);
                continue; // 跳到下一个循环迭代
            }

            // 计算SRT字幕条目的序号 (SRT序号从1开始，而segment.id可能从0开始)
            const srtIndex = segment.id + 1;
            // 格式化片段的开始时间为SRT时间戳格式
            const startTime = formatSrtTime(segment.start);
            // 格式化片段的结束时间为SRT时间戳格式
            const endTime = formatSrtTime(segment.end);
            // 获取片段文本并去除首尾空格
            const text = segment.text.trim();

            // 确保处理后的文本不为空
            if (!text) {
                // 如果文本为空，记录信息日志并跳过此字幕条目
                logger.info(`${logPrefix}[步骤 2.2] Segment 索引 ${i} (ID: ${segment.id}) 文本为空，已跳过。`);
                continue; // 跳到下一个循环迭代
            }

            // 按照SRT格式构建当前字幕条目，并追加到srtContent
            srtContent += `${srtIndex}\n`; // 添加序号和换行符
            srtContent += `${startTime} --> ${endTime}\n`; // 添加时间戳行和换行符
            srtContent += `${text}\n\n`; // 添加文本行和两个换行符 (SRT条目之间需要空行)
        }
        // 记录SRT字幕内容生成成功的日志，包含总长度
        logger.info(`${logPrefix}[步骤 3] SRT字幕内容生成成功。总长度: ${srtContent.length}`);
        // logger.debug(`${logPrefix} 生成的SRT内容 (前500字符):\n${srtContent.substring(0, 500)}`); // 可选：调试时打印部分生成的SRT内容
    } catch (error) {
        // 如果在生成过程中发生任何错误，捕获并记录错误日志
        logger.error(`${logPrefix}[步骤 E1][ERROR] 生成SRT内容时发生错误: ${error.message}`);
        // 记录错误的堆栈信息以便调试
        logger.error(`${logPrefix} Stack: ${error.stack}`);
        // 发生错误时返回空字符串
        return '';
    }

    // 返回最终生成的SRT格式字符串
    return srtContent;
}

/**
 * @功能概述: 从简化的字幕JSON数组中生成完整的文本内容。
 * @param {Array<object>} simplifiedSubtitleJsonArray - 包含字幕片段信息的数组，每个片段应有 text 属性。
 * @returns {string} 拼接后的完整文本内容。
 *
 * @执行流程:
 *   1.  校验输入数组是否有效。
 *   2.  遍历数组，提取每个对象的 text 属性。
 *   3.  将所有 text 属性拼接成一个字符串，每个片段文本后添加空格。
 *   4.  返回拼接后的字符串。
 */
function generateCorrectedFullText(simplifiedSubtitleJsonArray) {
    const logPrefix = '[文件：subtitleProcessor.js][字幕处理器][generateCorrectedFullText] ';
    logger.info(`${logPrefix}[步骤 1] 开始生成完整文本内容。条目数: ${simplifiedSubtitleJsonArray ? simplifiedSubtitleJsonArray.length : '无效'}`);

    if (!Array.isArray(simplifiedSubtitleJsonArray)) {
        logger.warn(`${logPrefix}[步骤 1.1][WARN] 输入的 simplifiedSubtitleJsonArray 无效或非数组。将返回空字符串。`);
        return '';
    }

    let fullText = '';
    simplifiedSubtitleJsonArray.forEach(segment => {
        if (segment && typeof segment.text === 'string') {
            fullText += segment.text + ' '; // 每个片段后加空格
        }
    });

    // 移除末尾可能多余的空格并返回
    const trimmedText = fullText.trim();
    logger.info(`${logPrefix}[步骤 2] 完整文本内容生成成功。长度: ${trimmedText.length}`);
    return trimmedText;
}

// 导出这些工具函数供其他模块使用。
// 这些函数在视频处理流水线中用于处理和生成字幕内容。
// 它们负责从原始API响应中提取数据(extractRawJsonData)、格式化时间戳(formatSrtTime)和生成SRT字幕内容(generateSrt)。
module.exports = {
    extractRawJsonData,
    formatSrtTime,
    generateSrt,
    generateCorrectedFullText
};