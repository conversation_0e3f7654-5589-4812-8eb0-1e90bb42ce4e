/**
 * CSS样式到ASS字幕映射器
 * 使用media-captions库将video-config.json中的CSS样式正确映射到ASS字幕格式
 */

const fs = require('fs').promises;
const path = require('path');

/**
 * @功能概述: CSS样式到ASS字幕映射器类
 * @技术实现:
 *   1. 读取video-config.json配置文件
 *   2. 解析CSS样式配置
 *   3. 将CSS样式映射为ASS样式格式
 *   4. 生成符合ASS规范的样式定义
 */
class CSSToASSMapper {
    constructor(configPath, configObject = null) {
        this.configPath = configPath;
        this.config = configObject; // 如果提供了配置对象，直接使用
        this.assStyles = new Map();
    }

    /**
     * @功能概述: 加载video-config.json配置文件或返回已有的配置对象
     * @返回值: Promise<Object> - 配置对象
     */
    async loadConfig() {
        const functionName = 'loadConfig';

        try {
            // 如果构造函数中已经提供了配置对象，直接返回
            if (this.config) {
                console.log(`[${functionName}] 使用已提供的配置对象`);
                return this.config;
            }

            // 否则从文件加载配置
            console.log(`[${functionName}] 开始加载配置文件: ${this.configPath}`);

            const configContent = await fs.readFile(this.configPath, 'utf8');
            this.config = JSON.parse(configContent);

            console.log(`[${functionName}] 配置文件加载成功`);
            return this.config;

        } catch (error) {
            console.error(`[${functionName}] 加载配置文件失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 将CSS颜色值转换为ASS颜色格式
     * @参数说明:
     *   - cssColor: CSS颜色值 (如: "#FFFFFF", "rgba(255,255,255,0.8)")
     * @返回值: string - ASS颜色格式 (如: "&H00FFFFFF")
     * @技术实现:
     *   - 基于media-captions库的专业实现
     *   - 使用ABGR格式（Alpha-Blue-Green-Red）
     *   - 支持命名颜色、十六进制、RGB、RGBA等格式
     *   - 完全兼容SSA/ASS规范
     */
    convertColorToASS(cssColor) {
        if (!cssColor) return '&H00FFFFFF'; // 默认白色

        // 处理命名颜色
        const namedColors = {
            'white': '#FFFFFF', 'black': '#000000', 'red': '#FF0000', 'green': '#008000',
            'blue': '#0000FF', 'yellow': '#FFFF00', 'cyan': '#00FFFF', 'magenta': '#FF00FF',
            'silver': '#C0C0C0', 'gray': '#808080', 'maroon': '#800000', 'olive': '#808000',
            'lime': '#00FF00', 'aqua': '#00FFFF', 'teal': '#008080', 'navy': '#000080',
            'fuchsia': '#FF00FF', 'purple': '#800080'
        };

        if (namedColors[cssColor.toLowerCase()]) {
            cssColor = namedColors[cssColor.toLowerCase()];
        }

        // 处理十六进制颜色 #FFFFFF 或 #FFF
        if (cssColor.startsWith('#')) {
            const hex = cssColor.substring(1);
            let r, g, b;

            if (hex.length === 3) {
                // 短格式 #FFF → #FFFFFF
                r = parseInt(hex[0] + hex[0], 16);
                g = parseInt(hex[1] + hex[1], 16);
                b = parseInt(hex[2] + hex[2], 16);
            } else if (hex.length === 6) {
                // 长格式 #FFFFFF
                r = parseInt(hex.substring(0, 2), 16);
                g = parseInt(hex.substring(2, 4), 16);
                b = parseInt(hex.substring(4, 6), 16);
            } else {
                return '&H00FFFFFF'; // 无效格式，返回默认白色
            }

            return this.rgbaToASSColor(r, g, b, 1.0);
        }

        // 处理rgba颜色
        if (cssColor.startsWith('rgba(')) {
            const match = cssColor.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/);
            if (match) {
                const r = parseInt(match[1]);
                const g = parseInt(match[2]);
                const b = parseInt(match[3]);
                const alpha = parseFloat(match[4]);
                return this.rgbaToASSColor(r, g, b, alpha);
            }
        }

        // 处理rgb颜色
        if (cssColor.startsWith('rgb(')) {
            const match = cssColor.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
            if (match) {
                const r = parseInt(match[1]);
                const g = parseInt(match[2]);
                const b = parseInt(match[3]);
                return this.rgbaToASSColor(r, g, b, 1.0);
            }
        }

        // 处理hsl颜色
        if (cssColor.startsWith('hsl(')) {
            const match = cssColor.match(/hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/);
            if (match) {
                const h = parseInt(match[1]);
                const s = parseInt(match[2]) / 100;
                const l = parseInt(match[3]) / 100;
                const [r, g, b] = this.hslToRgb(h, s, l);
                return this.rgbaToASSColor(r, g, b, 1.0);
            }
        }

        // 默认返回白色
        return '&H00FFFFFF';
    }

    /**
     * @功能概述: RGBA值转换为ASS颜色格式
     * @参数说明:
     *   - r, g, b: RGB颜色分量 (0-255)
     *   - alpha: 透明度 (0.0-1.0)
     * @返回值: ASS颜色格式字符串
     * @技术实现:
     *   - 基于media-captions库的parseColor函数逆向实现
     *   - 使用ABGR格式（Alpha-Blue-Green-Red）
     *   - Alpha值需要反转（0xFF表示透明，0x00表示不透明）
     */
    rgbaToASSColor(r, g, b, alpha) {
        // 确保RGB值在0-255范围内
        r = Math.max(0, Math.min(255, Math.round(r)));
        g = Math.max(0, Math.min(255, Math.round(g)));
        b = Math.max(0, Math.min(255, Math.round(b)));

        // 确保alpha值在0-1范围内
        alpha = Math.max(0, Math.min(1, alpha));

        // ASS中Alpha值是反转的：0x00=不透明，0xFF=透明
        const a = Math.round((1 - alpha) * 255);

        // 构建ABGR格式的32位整数
        const abgr = (a << 24) | (b << 16) | (g << 8) | r;

        // 转换为十六进制字符串，确保8位
        const hex = (abgr >>> 0).toString(16).padStart(8, '0').toUpperCase();

        return `&H${hex}`;
    }

    /**
     * @功能概述: HSL颜色转换为RGB
     * @参数说明:
     *   - h: 色相 (0-360)
     *   - s: 饱和度 (0-1)
     *   - l: 亮度 (0-1)
     * @返回值: [r, g, b] RGB数组
     */
    hslToRgb(h, s, l) {
        h = h / 360;
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        if (s === 0) {
            const gray = Math.round(l * 255);
            return [gray, gray, gray];
        }

        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        const r = Math.round(hue2rgb(p, q, h + 1/3) * 255);
        const g = Math.round(hue2rgb(p, q, h) * 255);
        const b = Math.round(hue2rgb(p, q, h - 1/3) * 255);

        return [r, g, b];
    }

    /**
     * @功能概述: 将CSS字体大小转换为ASS格式
     * @参数说明:
     *   - cssFontSize: CSS字体大小 (如: "50px", "2em", "120%", "large")
     * @返回值: number - ASS字体大小
     * @技术实现:
     *   - 支持px、em、rem、%、pt等单位
     *   - 支持命名大小（small、medium、large等）
     *   - 基于16px作为1em的基准
     *   - 自动缩放以适应视频分辨率
     */
    convertFontSizeToASS(cssFontSize) {
        if (!cssFontSize) return 50; // 默认字体大小

        // 处理数字类型
        if (typeof cssFontSize === 'number') {
            return Math.round(cssFontSize);
        }

        const sizeStr = cssFontSize.toString().toLowerCase().trim();

        // 处理命名大小
        const namedSizes = {
            'xx-small': 9, 'x-small': 10, 'small': 13, 'medium': 16,
            'large': 18, 'x-large': 24, 'xx-large': 32,
            'smaller': 13, 'larger': 18
        };

        if (namedSizes[sizeStr]) {
            return Math.round(namedSizes[sizeStr] * 3.125); // 转换为ASS尺寸 (50px = 16px * 3.125)
        }

        // 处理像素单位
        if (sizeStr.endsWith('px')) {
            const px = parseFloat(sizeStr.replace('px', ''));
            return Math.round(px);
        }

        // 处理em单位 (基于16px)
        if (sizeStr.endsWith('em')) {
            const em = parseFloat(sizeStr.replace('em', ''));
            return Math.round(em * 50); // 1em = 50px in ASS context
        }

        // 处理rem单位 (基于16px)
        if (sizeStr.endsWith('rem')) {
            const rem = parseFloat(sizeStr.replace('rem', ''));
            return Math.round(rem * 50); // 1rem = 50px in ASS context
        }

        // 处理百分比 (基于50px)
        if (sizeStr.endsWith('%')) {
            const percent = parseFloat(sizeStr.replace('%', ''));
            return Math.round(50 * (percent / 100));
        }

        // 处理pt单位 (1pt = 1.333px)
        if (sizeStr.endsWith('pt')) {
            const pt = parseFloat(sizeStr.replace('pt', ''));
            return Math.round(pt * 1.333);
        }

        // 处理pc单位 (1pc = 16px)
        if (sizeStr.endsWith('pc')) {
            const pc = parseFloat(sizeStr.replace('pc', ''));
            return Math.round(pc * 16);
        }

        // 尝试解析纯数字
        const numValue = parseFloat(sizeStr);
        if (!isNaN(numValue)) {
            return Math.round(numValue);
        }

        return 50; // 默认字体大小
    }

    /**
     * @功能概述: 将CSS字体粗细转换为ASS格式
     * @参数说明:
     *   - cssFontWeight: CSS字体粗细 (如: "bold", "normal", "600", "lighter")
     * @返回值: number - ASS字体粗细 (-1=bold, 0=normal)
     * @技术实现:
     *   - 支持命名值（normal、bold、bolder、lighter）
     *   - 支持数值（100-900）
     *   - 600及以上视为粗体
     *   - 基于media-captions库的实现
     */
    convertFontWeightToASS(cssFontWeight) {
        if (!cssFontWeight) return 0;

        const weightStr = cssFontWeight.toString().toLowerCase().trim();

        // 处理命名值
        if (weightStr === 'bold' || weightStr === 'bolder') {
            return -1; // ASS中-1表示粗体
        }

        if (weightStr === 'normal' || weightStr === 'lighter') {
            return 0; // ASS中0表示正常
        }

        // 处理数值
        const numWeight = parseInt(weightStr);
        if (!isNaN(numWeight)) {
            // CSS字体粗细：100-300=thin, 400=normal, 500=medium, 600-900=bold
            return numWeight >= 600 ? -1 : 0;
        }

        return 0; // 默认正常
    }

    /**
     * @功能概述: 将CSS字体样式转换为ASS格式
     * @参数说明:
     *   - cssFontStyle: CSS字体样式 (如: "italic", "normal", "oblique")
     * @返回值: number - ASS字体样式 (-1=italic, 0=normal)
     * @技术实现:
     *   - 支持italic、oblique、normal
     *   - oblique也视为斜体
     *   - 基于ASS规范的斜体标记
     */
    convertFontStyleToASS(cssFontStyle) {
        if (!cssFontStyle) return 0;

        const styleStr = cssFontStyle.toString().toLowerCase().trim();
        return (styleStr === 'italic' || styleStr === 'oblique') ? -1 : 0;
    }

    /**
     * @功能概述: 将CSS文本对齐转换为ASS对齐格式
     * @参数说明:
     *   - cssTextAlign: CSS文本对齐 (如: "center", "left", "right", "justify")
     * @返回值: number - ASS对齐格式
     * @技术实现:
     *   - 1=左下, 2=中下, 3=右下
     *   - 5=左中, 6=中中, 7=右中
     *   - 9=左上, 10=中上, 11=右上
     *   - 基于ASS的9宫格对齐系统
     */
    convertTextAlignToASS(cssTextAlign, verticalAlign = 'middle') {
        if (!cssTextAlign) cssTextAlign = 'center';
        if (!verticalAlign) verticalAlign = 'middle';

        const alignStr = cssTextAlign.toString().toLowerCase().trim();
        const vAlignStr = verticalAlign.toString().toLowerCase().trim();

        // 确定水平对齐
        let horizontal = 2; // 默认居中
        switch (alignStr) {
            case 'left': case 'start': horizontal = 1; break;
            case 'center': horizontal = 2; break;
            case 'right': case 'end': horizontal = 3; break;
            case 'justify': horizontal = 2; break; // justify视为居中
        }

        // 确定垂直对齐
        let vertical = 2; // 默认中间
        switch (vAlignStr) {
            case 'top': case 'text-top': vertical = 3; break;
            case 'middle': case 'center': vertical = 2; break;
            case 'bottom': case 'text-bottom': vertical = 1; break;
            case 'baseline': vertical = 1; break;
        }

        // ASS对齐值计算：(vertical-1)*3 + horizontal
        // 1=左下, 2=中下, 3=右下, 5=左中, 6=中中, 7=右中, 9=左上, 10=中上, 11=右上
        return (vertical - 1) * 3 + horizontal;
    }

    /**
     * @功能概述: 将CSS字符间距转换为ASS格式
     * @参数说明:
     *   - cssLetterSpacing: CSS字符间距 (如: "2px", "0.1em", "normal")
     * @返回值: number - ASS字符间距
     */
    convertLetterSpacingToASS(cssLetterSpacing) {
        if (!cssLetterSpacing || cssLetterSpacing === 'normal') return 0;

        const spacingStr = cssLetterSpacing.toString().toLowerCase().trim();

        if (spacingStr.endsWith('px')) {
            return parseFloat(spacingStr.replace('px', ''));
        }

        if (spacingStr.endsWith('em')) {
            const em = parseFloat(spacingStr.replace('em', ''));
            return Math.round(em * 16); // 1em ≈ 16px spacing
        }

        if (spacingStr.endsWith('rem')) {
            const rem = parseFloat(spacingStr.replace('rem', ''));
            return Math.round(rem * 16);
        }

        const numValue = parseFloat(spacingStr);
        if (!isNaN(numValue)) {
            return Math.round(numValue);
        }

        return 0;
    }

    /**
     * @功能概述: 将CSS文本装饰转换为ASS格式
     * @参数说明:
     *   - cssTextDecoration: CSS文本装饰 (如: "underline", "line-through", "none")
     * @返回值: {underline: number, strikeOut: number}
     */
    convertTextDecorationToASS(cssTextDecoration) {
        if (!cssTextDecoration || cssTextDecoration === 'none') {
            return { underline: 0, strikeOut: 0 };
        }

        const decorationStr = cssTextDecoration.toString().toLowerCase();

        return {
            underline: decorationStr.includes('underline') ? -1 : 0,
            strikeOut: decorationStr.includes('line-through') ? -1 : 0
        };
    }

    /**
     * @功能概述: 将CSS字体族转换为ASS字体名称
     * @参数说明:
     *   - cssFontFamily: CSS字体族 (如: "Arial, sans-serif", "'Times New Roman', serif")
     * @返回值: string - ASS字体名称
     */
    convertFontFamilyToASS(cssFontFamily) {
        if (!cssFontFamily) return 'Arial';

        const familyStr = cssFontFamily.toString().trim();

        // 移除引号并分割字体列表
        const fonts = familyStr.split(',').map(font =>
            font.trim().replace(/['"]/g, '')
        );

        // 字体映射表
        const fontMap = {
            'serif': 'Times New Roman',
            'sans-serif': 'Arial',
            'monospace': 'Courier New',
            'cursive': 'Comic Sans MS',
            'fantasy': 'Impact'
        };

        // 查找第一个可用字体
        for (const font of fonts) {
            const mappedFont = fontMap[font.toLowerCase()];
            if (mappedFont) return mappedFont;

            // 如果不是通用字体族，直接返回
            if (!['serif', 'sans-serif', 'monospace', 'cursive', 'fantasy'].includes(font.toLowerCase())) {
                return font;
            }
        }

        return 'Arial'; // 默认字体
    }

    /**
     * @功能概述: 处理CSS边框转换为ASS轮廓
     * @参数说明:
     *   - cssBorder: CSS边框 (如: "2px solid #000000")
     * @返回值: {outline: number, outlineColor: string}
     */
    convertBorderToASS(cssBorder) {
        if (!cssBorder || cssBorder === 'none') {
            return { outline: 0, outlineColor: '&H00000000' };
        }

        // 解析CSS边框：width style color
        const borderMatch = cssBorder.match(/(\d+(?:\.\d+)?)px\s+(solid|dashed|dotted)\s+(.+)/);
        if (borderMatch) {
            const width = parseFloat(borderMatch[1]);
            const style = borderMatch[2]; // solid, dashed, dotted
            const color = borderMatch[3].trim();

            // ASS只支持实线边框，忽略样式
            const outline = Math.max(1, Math.round(width));
            const outlineColor = this.convertColorToASS(color);

            return { outline, outlineColor };
        }

        return { outline: 2, outlineColor: '&H00000000' };
    }

    /**
     * @功能概述: 处理CSS文本阴影转换为ASS轮廓和阴影
     * @参数说明:
     *   - cssTextShadow: CSS文本阴影 (如: "2px 2px 4px rgba(0,0,0,0.5)")
     * @返回值: {outline: number, shadow: number, outlineColor: string}
     */
    convertTextShadowToASS(cssTextShadow) {
        if (!cssTextShadow || cssTextShadow === 'none') {
            return { outline: 0, shadow: 0, outlineColor: '&H00000000' };
        }

        // 解析文本阴影：offset-x offset-y blur-radius color
        const shadowMatch = cssTextShadow.match(/(-?\d+(?:\.\d+)?)px\s+(-?\d+(?:\.\d+)?)px\s+(-?\d+(?:\.\d+)?)px\s+(.+)/);

        if (shadowMatch) {
            const offsetX = Math.abs(parseFloat(shadowMatch[1]));
            const offsetY = Math.abs(parseFloat(shadowMatch[2]));
            const blurRadius = parseFloat(shadowMatch[3]);
            const shadowColor = shadowMatch[4].trim();

            // ASS中outline表示轮廓粗细，shadow表示阴影偏移
            const outline = Math.max(1, Math.round(blurRadius / 2));
            const shadow = Math.max(1, Math.round(Math.max(offsetX, offsetY)));
            const outlineColor = this.convertColorToASS(shadowColor);

            return { outline, shadow, outlineColor };
        }

        return { outline: 2, shadow: 2, outlineColor: '&H00000000' };
    }

    /**
     * @功能概述: 处理CSS变换转换为ASS缩放和旋转
     * @参数说明:
     *   - cssTransform: CSS变换 (如: "scale(1.2) rotate(15deg)")
     * @返回值: {scaleX: number, scaleY: number, angle: number}
     */
    convertTransformToASS(cssTransform) {
        if (!cssTransform || cssTransform === 'none') {
            return { scaleX: 100, scaleY: 100, angle: 0 };
        }

        let scaleX = 100, scaleY = 100, angle = 0;

        // 解析scale变换
        const scaleMatch = cssTransform.match(/scale\(([^)]+)\)/);
        if (scaleMatch) {
            const scaleValues = scaleMatch[1].split(',').map(v => parseFloat(v.trim()));
            if (scaleValues.length === 1) {
                scaleX = scaleY = Math.round(scaleValues[0] * 100);
            } else if (scaleValues.length === 2) {
                scaleX = Math.round(scaleValues[0] * 100);
                scaleY = Math.round(scaleValues[1] * 100);
            }
        }

        // 解析scaleX变换
        const scaleXMatch = cssTransform.match(/scaleX\(([^)]+)\)/);
        if (scaleXMatch) {
            scaleX = Math.round(parseFloat(scaleXMatch[1]) * 100);
        }

        // 解析scaleY变换
        const scaleYMatch = cssTransform.match(/scaleY\(([^)]+)\)/);
        if (scaleYMatch) {
            scaleY = Math.round(parseFloat(scaleYMatch[1]) * 100);
        }

        // 解析rotate变换
        const rotateMatch = cssTransform.match(/rotate\(([^)]+)\)/);
        if (rotateMatch) {
            const rotateValue = rotateMatch[1];
            if (rotateValue.endsWith('deg')) {
                angle = parseFloat(rotateValue.replace('deg', ''));
            } else if (rotateValue.endsWith('rad')) {
                angle = parseFloat(rotateValue.replace('rad', '')) * (180 / Math.PI);
            }
        }

        return { scaleX, scaleY, angle };
    }

    /**
     * @功能概述: 处理CSS换行规则转换为ASS格式
     * @参数说明:
     *   - cssWhiteSpace: CSS空白处理 (如: "nowrap", "pre", "pre-wrap")
     *   - cssWordWrap: CSS单词换行 (如: "break-word", "normal")
     *   - cssOverflowWrap: CSS溢出换行 (如: "break-word", "anywhere")
     *   - textWrapping: 文本换行配置对象
     * @返回值: {wrapStyle: number, lineBreak: string}
     * @技术实现:
     *   - ASS WrapStyle: 0=智能换行, 1=行尾换行, 2=无换行, 3=智能换行(更宽)
     *   - 根据CSS属性组合确定最佳换行策略
     *   - 支持textWrapping配置强制换行
     */
    convertTextWrapToASS(cssWhiteSpace, cssWordWrap, cssOverflowWrap, textWrapping) {
        // 检查textWrapping配置，如果启用强制换行
        if (textWrapping?.enabled || textWrapping?.forceWrap) {
            console.log(`[convertTextWrapToASS] 检测到textWrapping配置，强制启用换行`);
            console.log(`[convertTextWrapToASS] textWrapping:`, textWrapping);

            // 根据wordBreak设置换行模式
            if (textWrapping.wordBreak === 'break-all') {
                return { wrapStyle: 0, lineBreak: '\\N' }; // 智能换行，强制断行
            }

            return { wrapStyle: 0, lineBreak: '\\N' }; // 默认智能换行
        }

        // 处理nowrap：强制不换行
        if (cssWhiteSpace === 'nowrap') {
            return { wrapStyle: 2, lineBreak: '' }; // 无换行
        }

        // 处理pre系列：保持原有换行
        if (cssWhiteSpace === 'pre') {
            return { wrapStyle: 1, lineBreak: '\\N' }; // 保持换行，不自动换行
        }

        if (cssWhiteSpace === 'pre-line') {
            return { wrapStyle: 1, lineBreak: '\\N' }; // 保持换行，允许自动换行
        }

        if (cssWhiteSpace === 'pre-wrap') {
            return { wrapStyle: 0, lineBreak: '\\N' }; // 保持换行，智能换行
        }

        // 处理break-word：强制换行
        if (cssWordWrap === 'break-word' || cssOverflowWrap === 'break-word' || cssOverflowWrap === 'anywhere') {
            return { wrapStyle: 0, lineBreak: '\\N' }; // 智能换行，允许单词内断行
        }

        // 处理normal：标准换行
        if (cssWordWrap === 'normal' || cssWhiteSpace === 'normal') {
            return { wrapStyle: 0, lineBreak: '\\N' }; // 智能换行
        }

        // 默认智能换行
        return { wrapStyle: 0, lineBreak: '\\N' };
    }

    /**
     * @功能概述: 处理CSS边距转换为ASS边距
     * @参数说明:
     *   - cssMargin: CSS边距值 (如: "10px", "1em", "5%")
     * @返回值: number - ASS边距值
     * @技术实现:
     *   - 支持px、em、rem、%等单位
     *   - 自动转换为ASS像素值
     */
    convertMarginToASS(cssMargin) {
        if (!cssMargin || cssMargin === '0' || cssMargin === 'auto') return 10; // 默认边距

        const marginStr = cssMargin.toString().toLowerCase().trim();

        // 处理像素单位
        if (marginStr.endsWith('px')) {
            const px = parseFloat(marginStr.replace('px', ''));
            return Math.max(0, Math.round(px));
        }

        // 处理em单位 (基于16px)
        if (marginStr.endsWith('em')) {
            const em = parseFloat(marginStr.replace('em', ''));
            return Math.max(0, Math.round(em * 16));
        }

        // 处理rem单位 (基于16px)
        if (marginStr.endsWith('rem')) {
            const rem = parseFloat(marginStr.replace('rem', ''));
            return Math.max(0, Math.round(rem * 16));
        }

        // 处理百分比 (基于视频宽度1080px)
        if (marginStr.endsWith('%')) {
            const percent = parseFloat(marginStr.replace('%', ''));
            return Math.max(0, Math.round(1080 * (percent / 100)));
        }

        // 处理pt单位 (1pt = 1.333px)
        if (marginStr.endsWith('pt')) {
            const pt = parseFloat(marginStr.replace('pt', ''));
            return Math.max(0, Math.round(pt * 1.333));
        }

        // 处理vw单位 (基于视频宽度1080px)
        if (marginStr.endsWith('vw')) {
            const vw = parseFloat(marginStr.replace('vw', ''));
            return Math.max(0, Math.round(1080 * (vw / 100)));
        }

        // 处理vh单位 (基于视频高度1920px)
        if (marginStr.endsWith('vh')) {
            const vh = parseFloat(marginStr.replace('vh', ''));
            return Math.max(0, Math.round(1920 * (vh / 100)));
        }

        // 尝试解析纯数字
        const numValue = parseFloat(marginStr);
        if (!isNaN(numValue)) {
            return Math.max(0, Math.round(numValue));
        }

        return 10; // 默认边距
    }

    /**
     * @功能概述: 处理CSS padding转换为ASS边距
     * @参数说明:
     *   - cssPadding: CSS内边距值
     * @返回值: number - ASS边距值
     */
    convertPaddingToASS(cssPadding) {
        // padding在ASS中也映射为边距
        return this.convertMarginToASS(cssPadding);
    }

    /**
     * @功能概述: 过滤和清理CSS样式配置
     * @参数说明:
     *   - styleConfig: 原始样式配置对象
     * @返回值: object - 清理后的样式配置
     * @技术实现:
     *   - 移除不支持的布局属性
     *   - 静默忽略无法映射的CSS属性
     *   - 保留核心可映射属性
     */
    filterSupportedCSSProperties(styleConfig) {
        if (!styleConfig || typeof styleConfig !== 'object') {
            return {};
        }

        // 支持的核心CSS属性列表
        const supportedProperties = [
            'fontSize', 'fontFamily', 'fontWeight', 'fontStyle',
            'color', 'backgroundColor', 'textShadow', 'textDecoration',
            'textAlign', 'letterSpacing', 'marginLeft', 'marginRight',
            'marginTop', 'marginBottom', 'paddingLeft', 'paddingRight',
            'paddingTop', 'paddingBottom', 'transform'
        ];

        // 不支持的属性列表（静默忽略）
        const unsupportedProperties = [
            'position', 'top', 'left', 'right', 'bottom',
            'lineHeight', 'maxWidth', 'minWidth', 'width', 'height',
            'whiteSpace', 'wordWrap', 'overflowWrap', 'wordBreak',
            'verticalAlign', 'display', 'float', 'clear',
            'zIndex', 'opacity', 'visibility'
        ];

        const filteredConfig = {};

        // 只保留支持的属性
        for (const [key, value] of Object.entries(styleConfig)) {
            if (supportedProperties.includes(key)) {
                filteredConfig[key] = value;
            } else if (unsupportedProperties.includes(key)) {
                // 静默忽略不支持的属性
                console.log(`[filterSupportedCSSProperties] 忽略不支持的CSS属性: ${key}`);
            } else {
                // 未知属性也保留，可能是自定义属性
                filteredConfig[key] = value;
            }
        }

        return filteredConfig;
    }

    /**
     * @功能概述: 生成ASS样式定义
     * @参数说明:
     *   - styleName: 样式名称
     *   - styleConfig: 样式配置对象
     * @返回值: string - ASS样式定义行
     * @技术实现:
     *   - 过滤不支持的CSS属性
     *   - 精准映射到ASS格式
     *   - 基于media-captions库的专业实现
     */
    generateASSStyle(styleName, styleConfig) {
        // 过滤不支持的CSS属性，静默忽略布局属性
        const filteredConfig = this.filterSupportedCSSProperties(styleConfig);

        // 基础字体属性
        const fontName = this.convertFontFamilyToASS(filteredConfig.fontFamily);
        const fontSize = this.convertFontSizeToASS(filteredConfig.fontSize);
        const primaryColor = this.convertColorToASS(filteredConfig.color);
        const secondaryColor = this.convertColorToASS(filteredConfig.secondaryColor || '#0000FF');
        const backColor = this.convertColorToASS(filteredConfig.backgroundColor || 'rgba(0,0,0,0.8)');

        // 字体样式
        const bold = this.convertFontWeightToASS(filteredConfig.fontWeight);
        const italic = this.convertFontStyleToASS(filteredConfig.fontStyle);

        // 文本装饰
        const decoration = this.convertTextDecorationToASS(filteredConfig.textDecoration);
        const underline = decoration.underline;
        const strikeOut = decoration.strikeOut;

        // 变换和缩放
        const transform = this.convertTransformToASS(filteredConfig.transform);
        const scaleX = transform.scaleX;
        const scaleY = transform.scaleY;
        const angle = transform.angle;

        // 字符间距
        const spacing = this.convertLetterSpacingToASS(filteredConfig.letterSpacing);

        // 阴影和轮廓
        const shadow = this.convertTextShadowToASS(filteredConfig.textShadow);
        const outline = shadow.outline;
        const shadowDepth = shadow.shadow;
        const outlineColor = shadow.outlineColor;

        // 对齐方式
        const alignment = this.convertTextAlignToASS(filteredConfig.textAlign, filteredConfig.verticalAlign);

        // 边距设置 - 正确映射CSS边距到ASS边距
        let marginL = 10, marginR = 10, marginV = 10; // 默认值

        // 优先从intelligentLayout.boundaries读取边距（向后兼容）
        if (styleConfig.intelligentLayout?.boundaries) {
            const boundaries = styleConfig.intelligentLayout.boundaries;
            marginL = this.convertMarginToASS(boundaries.leftMargin || boundaries.left);
            marginR = this.convertMarginToASS(boundaries.rightMargin || boundaries.right);
            marginV = this.convertMarginToASS(boundaries.bottomMargin || boundaries.topMargin || boundaries.top || boundaries.bottom);
        }

        // 从过滤后的CSS标准属性读取
        if (filteredConfig.marginLeft || filteredConfig.paddingLeft) {
            marginL = this.convertMarginToASS(filteredConfig.marginLeft || filteredConfig.paddingLeft);
        }
        if (filteredConfig.marginRight || filteredConfig.paddingRight) {
            marginR = this.convertMarginToASS(filteredConfig.marginRight || filteredConfig.paddingRight);
        }

        // 垂直边距：优先使用marginBottom，其次marginTop，最后padding
        if (filteredConfig.marginBottom) {
            marginV = this.convertMarginToASS(filteredConfig.marginBottom);
        } else if (filteredConfig.marginTop) {
            marginV = this.convertMarginToASS(filteredConfig.marginTop);
        } else if (filteredConfig.paddingBottom) {
            marginV = this.convertMarginToASS(filteredConfig.paddingBottom);
        } else if (filteredConfig.paddingTop) {
            marginV = this.convertMarginToASS(filteredConfig.paddingTop);
        } else if (filteredConfig.margin) {
            // 处理简写margin属性
            marginV = this.convertMarginToASS(filteredConfig.margin);
        } else if (filteredConfig.padding) {
            // 处理简写padding属性
            marginV = this.convertMarginToASS(filteredConfig.padding);
        }

        // 其他属性
        const borderStyle = 1; // 默认边框样式
        const encoding = 1; // 默认编码

        return `Style: ${styleName},${fontName},${fontSize},${primaryColor},${secondaryColor},${outlineColor},${backColor},${bold},${italic},${underline},${strikeOut},${scaleX},${scaleY},${spacing},${angle},${borderStyle},${outline},${shadowDepth},${alignment},${marginL},${marginR},${marginV},${encoding}`;
    }

    /**
     * @功能概述: 映射所有样式配置到ASS格式 - 添加emoji专用样式支持
     * @返回值: Map - ASS样式映射表
     */
    async mapAllStyles() {
        const functionName = 'mapAllStyles';
        
        try {
            console.log(`[${functionName}] 开始映射所有样式...`);
            
            // 确保配置已加载
            if (!this.config) {
                await this.loadConfig();
            }
            
            const subtitleConfig = this.config.subtitleConfig;
            
            // 清空现有样式
            this.assStyles.clear();
            
            // 映射视频标题样式
            if (subtitleConfig.videoGuide?.style) {
                const assStyle = this.generateASSStyle('VideoTitle', subtitleConfig.videoGuide.style);
                this.assStyles.set('VideoTitle', assStyle);
                console.log(`[${functionName}] ✅ VideoTitle样式映射完成`);
            }

            // 映射广告样式
            if (subtitleConfig.advertisement?.style) {
                const assStyle = this.generateASSStyle('Advertisement', subtitleConfig.advertisement.style);
                this.assStyles.set('Advertisement', assStyle);
                console.log(`[${functionName}] ✅ Advertisement样式映射完成`);
            }

            // 添加专门的emoji样式（基于advertisement样式但优化字体）
            if (subtitleConfig.advertisement?.style) {
                const emojiStyleConfig = {
                    ...subtitleConfig.advertisement.style,
                    fontFamily: 'Segoe UI Emoji', // 专用emoji字体
                    fontSize: parseInt(subtitleConfig.advertisement.style.fontSize.replace('px', '')) + 'px' // 保持大小一致
                };
                const emojiAssStyle = this.generateASSStyle('EmojiStyle', emojiStyleConfig);
                this.assStyles.set('EmojiStyle', emojiAssStyle);
                console.log(`[${functionName}] ✅ EmojiStyle样式映射完成 - 专用emoji字体支持`);
            }

            // 映射填空字幕样式
            if (subtitleConfig.clozedTextStyle) {
                const assStyle = this.generateASSStyle('ClozedText', subtitleConfig.clozedTextStyle);
                this.assStyles.set('ClozedText', assStyle);
                console.log(`[${functionName}] ✅ ClozedText样式映射完成`);
            }

            // 映射双语字幕英文样式
            if (subtitleConfig.bilingualTextStyle?.englishStyle) {
                const assStyle = this.generateASSStyle('BilingualEnglish', subtitleConfig.bilingualTextStyle.englishStyle);
                this.assStyles.set('BilingualEnglish', assStyle);
                console.log(`[${functionName}] ✅ BilingualEnglish样式映射完成`);
            }

            // 映射双语字幕中文样式
            if (subtitleConfig.bilingualTextStyle?.chineseStyle) {
                const assStyle = this.generateASSStyle('BilingualChinese', subtitleConfig.bilingualTextStyle.chineseStyle);
                this.assStyles.set('BilingualChinese', assStyle);
                console.log(`[${functionName}] ✅ BilingualChinese样式映射完成`);
            }

            // 映射单元引导字幕样式 (修复配置路径)
            if (subtitleConfig.repeatModeStyle) {
                const assStyle = this.generateASSStyle('RepeatModeGuide', subtitleConfig.repeatModeStyle);
                this.assStyles.set('RepeatModeGuide', assStyle);
                console.log(`[${functionName}] ✅ RepeatModeGuide样式映射完成`);
            }

            console.log(`[${functionName}] 所有样式映射完成，共${this.assStyles.size}个样式`);
            return this.assStyles;
            
        } catch (error) {
            console.error(`[${functionName}] 样式映射失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 生成完整的ASS样式头部
     * @返回值: string - ASS样式头部内容
     */
    generateASSStylesHeader() {
        let header = '[V4+ Styles]\n';
        header += 'Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n';
        
        for (const [styleName, styleDefinition] of this.assStyles) {
            header += styleDefinition + '\n';
        }
        
        return header;
    }

    /**
     * @功能概述: 处理文本内容的ASS格式化
     * @参数说明:
     *   - text: 原始文本内容
     *   - styleConfig: 样式配置
     * @返回值: string - 格式化后的ASS文本
     * @技术实现:
     *   - 处理换行符转换
     *   - 处理特殊字符转义
     *   - 应用内联样式标记
     */
    formatTextForASS(text, styleConfig = {}) {
        if (!text) return '';

        let formattedText = text.toString();

        // 处理换行符
        const wrapConfig = this.convertTextWrapToASS(styleConfig.whiteSpace, styleConfig.wordWrap);
        if (wrapConfig.wrapStyle === 2) {
            // 无换行模式：将所有换行符转为空格
            formattedText = formattedText.replace(/\n/g, ' ');
        } else {
            // 换行模式：转换为ASS换行符
            formattedText = formattedText.replace(/\n/g, wrapConfig.lineBreak);
        }

        // 转义ASS特殊字符
        formattedText = formattedText
            .replace(/\{/g, '\\{')  // 转义左大括号
            .replace(/\}/g, '\\}')  // 转义右大括号
            .replace(/\\/g, '\\\\') // 转义反斜杠
            .replace(/\r/g, '');    // 移除回车符

        // 处理HTML实体
        const htmlEntities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&apos;': "'", '&nbsp;': ' ', '&copy;': '©', '&reg;': '®'
        };

        for (const [entity, char] of Object.entries(htmlEntities)) {
            formattedText = formattedText.replace(new RegExp(entity, 'g'), char);
        }

        return formattedText;
    }

    /**
     * @功能概述: 验证ASS样式定义的正确性
     * @参数说明:
     *   - styleDefinition: ASS样式定义字符串
     * @返回值: {valid: boolean, errors: string[]}
     */
    validateASSStyle(styleDefinition) {
        const errors = [];

        if (!styleDefinition || typeof styleDefinition !== 'string') {
            errors.push('样式定义不能为空');
            return { valid: false, errors };
        }

        // 检查基本格式
        if (!styleDefinition.startsWith('Style: ')) {
            errors.push('样式定义必须以"Style: "开头');
        }

        // 分割样式参数
        const parts = styleDefinition.split(',');
        if (parts.length !== 23) {
            errors.push(`样式参数数量错误，期望23个，实际${parts.length}个`);
        }

        // 验证样式名称
        const styleName = parts[0]?.replace('Style: ', '').trim();
        if (!styleName || styleName.length === 0) {
            errors.push('样式名称不能为空');
        }

        // 验证字体大小
        const fontSize = parseInt(parts[2]);
        if (isNaN(fontSize) || fontSize <= 0) {
            errors.push('字体大小必须是正整数');
        }

        // 验证颜色格式
        const colorFields = [3, 4, 5, 6]; // PrimaryColour, SecondaryColour, OutlineColour, BackColour
        for (const index of colorFields) {
            const color = parts[index];
            if (color && !color.match(/^&H[0-9A-F]{8}$/i)) {
                errors.push(`第${index + 1}个参数颜色格式错误: ${color}`);
            }
        }

        // 验证数值范围
        const numericFields = [
            { index: 7, name: 'Bold', min: -1, max: 1 },
            { index: 8, name: 'Italic', min: -1, max: 1 },
            { index: 9, name: 'Underline', min: -1, max: 1 },
            { index: 10, name: 'StrikeOut', min: -1, max: 1 },
            { index: 11, name: 'ScaleX', min: 1, max: 1000 },
            { index: 12, name: 'ScaleY', min: 1, max: 1000 },
            { index: 18, name: 'Alignment', min: 1, max: 11 }
        ];

        for (const field of numericFields) {
            const value = parseInt(parts[field.index]);
            if (isNaN(value) || value < field.min || value > field.max) {
                errors.push(`${field.name}值超出范围[${field.min}, ${field.max}]: ${value}`);
            }
        }

        return { valid: errors.length === 0, errors };
    }

    /**
     * @功能概述: 生成样式使用统计报告
     * @返回值: object - 样式统计信息
     */
    generateStyleReport() {
        const report = {
            totalStyles: this.assStyles.size,
            styles: {},
            summary: {
                fonts: new Set(),
                colors: new Set(),
                sizes: new Set()
            }
        };

        for (const [styleName, styleDefinition] of this.assStyles) {
            const parts = styleDefinition.split(',');
            const styleInfo = {
                name: styleName,
                font: parts[1],
                size: parseInt(parts[2]),
                primaryColor: parts[3],
                valid: this.validateASSStyle(styleDefinition).valid
            };

            report.styles[styleName] = styleInfo;
            report.summary.fonts.add(styleInfo.font);
            report.summary.colors.add(styleInfo.primaryColor);
            report.summary.sizes.add(styleInfo.size);
        }

        // 转换Set为Array
        report.summary.fonts = Array.from(report.summary.fonts);
        report.summary.colors = Array.from(report.summary.colors);
        report.summary.sizes = Array.from(report.summary.sizes).sort((a, b) => a - b);

        return report;
    }

    /**
     * @功能概述: 将映射结果保存到文件
     * @参数说明:
     *   - outputPath: 输出文件路径
     * @返回值: Promise<void>
     */
    async saveToFile(outputPath) {
        const functionName = 'saveToFile';

        try {
            console.log(`[${functionName}] 开始保存ASS样式到文件: ${outputPath}`);

            const assContent = this.generateASSStylesHeader();

            // 验证生成的内容
            let validStyles = 0;
            let invalidStyles = 0;

            for (const [styleName, styleDefinition] of this.assStyles) {
                const validation = this.validateASSStyle(styleDefinition);
                if (validation.valid) {
                    validStyles++;
                } else {
                    invalidStyles++;
                    console.warn(`[${functionName}] 样式${styleName}验证失败:`, validation.errors);
                }
            }

            await fs.writeFile(outputPath, assContent, 'utf8');

            console.log(`[${functionName}] ASS样式文件保存成功`);
            console.log(`[${functionName}] 文件大小: ${(assContent.length / 1024).toFixed(2)} KB`);
            console.log(`[${functionName}] 有效样式: ${validStyles}个，无效样式: ${invalidStyles}个`);

            // 生成样式报告
            const report = this.generateStyleReport();
            console.log(`[${functionName}] 样式统计:`, {
                总数: report.totalStyles,
                字体: report.summary.fonts,
                颜色数: report.summary.colors.length,
                字号范围: `${Math.min(...report.summary.sizes)}-${Math.max(...report.summary.sizes)}px`
            });

        } catch (error) {
            console.error(`[${functionName}] 保存文件失败: ${error.message}`);
            throw error;
        }
    }
}

module.exports = { CSSToASSMapper };
