/**
 * @功能概述: TranscriptionCorrectionTask 任务类的单元测试
 * @说明: 测试转录校正任务的各种执行场景，包括LLM校正和降级处理
 * @架构位置: 测试层，验证TranscriptionCorrectionTask的功能正确性
 * @测试覆盖: 正常流程、参数验证、错误处理、LLM交互、文件保存等
 */

// 导入测试所需的核心模块
const fs = require('fs');
const path = require('path');
const TranscriptionCorrectionTask = require('../TranscriptionCorrectionTask');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const logger = require('../../utils/logger');

// 测试日志前缀，用于标识测试输出
const testLogPrefix = '[测试：TranscriptionCorrectionTask.test.js]';

// 记录测试文件加载
logger.info(`${testLogPrefix} 测试文件已加载。`);

/**
 * @功能概述: 断言函数，用于验证条件是否为真
 * @param {boolean} condition - 要验证的条件
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当条件为假时抛出错误
 */
function assert(condition, message) {
    if (!condition) {
        const fullMessage = `断言失败: ${message}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

/**
 * @功能概述: 相等断言函数，验证两个值是否相等
 * @param {any} actual - 实际值
 * @param {any} expected - 期望值
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当值不相等时抛出错误
 */
function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: "${expected}", 实际: "${actual}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: "${actual}")`);
}

/**
 * @功能概述: 包含断言函数，验证字符串或数组是否包含指定子串
 * @param {string|Array} arrayOrString - 要检查的字符串或数组
 * @param {string} substring - 要查找的子串
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当不包含指定子串时抛出错误
 */
function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

/**
 * @功能概述: 创建测试用的API响应数据
 * @returns {object} 模拟的Azure API响应数据
 */
function createTestApiResponse() {
    return {
        text: "Hello world. This is a test transcription.",
        language: "en",
        segments: [
            {
                id: 0,
                start: 0.0,
                end: 2.5,
                text: "Hello world."
            },
            {
                id: 1,
                start: 2.5,
                end: 5.0,
                text: "This is a test transcription."
            }
        ]
    };
}

/**
 * @功能概述: 清理测试文件
 * @param {string} filePath - 要删除的文件路径
 */
function cleanupTestFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logger.debug(`${testLogPrefix} 清理测试文件: ${filePath}`);
        }
    } catch (error) {
        logger.warn(`${testLogPrefix} 清理测试文件失败: ${error.message}`);
    }
}

/**
 * @功能概述: 主测试执行函数
 */
async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 TranscriptionCorrectionTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('0. 真实数据测试 - 使用SubtitleOptimizationTask输出', async () => {
        // 硬编码的简化字幕JSON数据（来自test_0613_optimized_by_SubtitleOptimizationTask.json）
        const hardcodedSimplifiedSubtitleJsonArray = [
          {
            "id": "1",
            "start": 0.08,
            "end": 3.44,
            "text": "Good afternoon. There is another wildfire evacuation alert tonight.",
            "words": [
              {"text": "Good", "start": 0.08, "end": 0.2},
              {"text": "afternoon.", "start": 0.2, "end": 0.8},
              {"text": "There", "start": 0.8, "end": 1},
              {"text": "is", "start": 1, "end": 1.12},
              {"text": "another", "start": 1.12, "end": 1.52},
              {"text": "wildfire", "start": 1.52, "end": 2.04},
              {"text": "evacuation", "start": 2.04, "end": 2.72},
              {"text": "alert", "start": 2.72, "end": 3},
              {"text": "tonight.", "start": 3, "end": 3.44}
            ]
          },
          {
            "id": "2",
            "start": 3.44,
            "end": 6.88,
            "text": "The County of Northern Lights telling residents of Hawk Hills,",
            "words": [
              {"text": "The", "start": 3.44, "end": 3.56},
              {"text": "County", "start": 3.56, "end": 3.92},
              {"text": "of", "start": 3.92, "end": 4.04},
              {"text": "Northern", "start": 4.04, "end": 4.48},
              {"text": "Lights", "start": 4.48, "end": 4.88},
              {"text": "telling", "start": 4.88, "end": 5.28},
              {"text": "residents", "start": 5.28, "end": 5.84},
              {"text": "of", "start": 5.84, "end": 5.96},
              {"text": "Hawk", "start": 5.96, "end": 6.32},
              {"text": "Hills,", "start": 6.32, "end": 6.88}
            ]
          },
          {
            "id": "3",
            "start": 6.88,
            "end": 8.92,
            "text": "which is near Twin Lakes Provincial Park,",
            "words": [
              {"text": "which", "start": 6.88, "end": 7.12},
              {"text": "is", "start": 7.12, "end": 7.24},
              {"text": "near", "start": 7.24, "end": 7.48},
              {"text": "Twin", "start": 7.48, "end": 7.76},
              {"text": "Lakes", "start": 7.76, "end": 8.08},
              {"text": "Provincial", "start": 8.08, "end": 8.64},
              {"text": "Park,", "start": 8.64, "end": 8.92}
            ]
          },
          {
            "id": "4",
            "start": 8.92,
            "end": 11.56,
            "text": "to be ready to evacuate at any time.",
            "words": [
              {"text": "to", "start": 8.92, "end": 9.04},
              {"text": "be", "start": 9.04, "end": 9.16},
              {"text": "ready", "start": 9.16, "end": 9.48},
              {"text": "to", "start": 9.48, "end": 9.6},
              {"text": "evacuate", "start": 9.6, "end": 10.16},
              {"text": "at", "start": 10.16, "end": 10.28},
              {"text": "any", "start": 10.28, "end": 10.52},
              {"text": "time.", "start": 10.52, "end": 11.56}
            ]
          },
          {
            "id": "5",
            "start": 11.56,
            "end": 13.56,
            "text": "Firefighters across northern Alberta have been",
            "words": [
              {"text": "Firefighters", "start": 11.56, "end": 12.32},
              {"text": "across", "start": 12.32, "end": 12.72},
              {"text": "northern", "start": 12.72, "end": 13.12},
              {"text": "Alberta", "start": 13.12, "end": 13.56},
              {"text": "have", "start": 13.56, "end": 13.8},
              {"text": "been", "start": 13.8, "end": 14}
            ]
          },
          {
            "id": "6",
            "start": 13.56,
            "end": 16.6,
            "text": "trying to contain several out-of-control wildfires,",
            "words": [
              {"text": "trying", "start": 14, "end": 14.4},
              {"text": "to", "start": 14.4, "end": 14.52},
              {"text": "contain", "start": 14.52, "end": 15},
              {"text": "several", "start": 15, "end": 15.48},
              {"text": "out-of-control", "start": 15.48, "end": 16.24},
              {"text": "wildfires,", "start": 16.24, "end": 16.6}
            ]
          },
          {
            "id": "7",
            "start": 16.6,
            "end": 18.36,
            "text": "and it has been an uphill battle",
            "words": [
              {"text": "and", "start": 16.6, "end": 16.76},
              {"text": "it", "start": 16.76, "end": 16.88},
              {"text": "has", "start": 16.88, "end": 17.08},
              {"text": "been", "start": 17.08, "end": 17.32},
              {"text": "an", "start": 17.32, "end": 17.44},
              {"text": "uphill", "start": 17.44, "end": 17.84},
              {"text": "battle", "start": 17.84, "end": 18.36}
            ]
          },
          {
            "id": "8",
            "start": 18.36,
            "end": 21.24,
            "text": "with one community losing dozens of structures.",
            "words": [
              {"text": "with", "start": 18.36, "end": 18.56},
              {"text": "one", "start": 18.56, "end": 18.76},
              {"text": "community", "start": 18.76, "end": 19.36},
              {"text": "losing", "start": 19.36, "end": 19.8},
              {"text": "dozens", "start": 19.8, "end": 20.24},
              {"text": "of", "start": 20.24, "end": 20.36},
              {"text": "structures.", "start": 20.36, "end": 21.24}
            ]
          }
        ];

        const task = new TranscriptionCorrectionTask('RealDataTest');
        const context = {
            reqId: 'test_correction_real_data',
            videoIdentifier: 'test_0613_corrected',
            simplifiedSubtitleJsonArray: hardcodedSimplifiedSubtitleJsonArray,
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input'
        };
        const progressLogs = [];

        logger.info(`${testLogPrefix} 开始真实数据测试，输入segments数量: ${hardcodedSimplifiedSubtitleJsonArray.length}`);

        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        // 验证结果
        assert(result, '任务执行应返回结果');
        assert(result.simplifiedSubtitleJsonArray, '结果应包含校正后的简化字幕数组');
        assert(Array.isArray(result.simplifiedSubtitleJsonArray), '校正后的简化字幕应为数组');
        assertEquals(result.correctionStatus, 'success', '校正状态应为成功');
        assertEquals(task.status, 'completed', '任务状态应为完成');

        logger.info(`${testLogPrefix} 校正效果: ${hardcodedSimplifiedSubtitleJsonArray.length} -> ${result.simplifiedSubtitleJsonArray.length} segments`);
        logger.info(`${testLogPrefix} 生成文件路径: ${result.simplifiedSubtitleJsonPath}`);

        // 验证进度回调
        const hasStarted = progressLogs.some(p => p.status === 'started');
        const hasCompleted = progressLogs.some(p => p.status === 'completed');
        assert(hasStarted, '应记录任务开始状态');
        assert(hasCompleted, '应记录任务完成状态');
    });

    await runSingleTest('1. 任务实例化', async () => {
        const task = new TranscriptionCorrectionTask();
        assert(task instanceof TranscriptionCorrectionTask, '任务应为 TranscriptionCorrectionTask 的实例');
        assertEquals(task.name, 'TranscriptionCorrectionTask', '任务名称应为 TranscriptionCorrectionTask');
        assertEquals(task.status, 'pending', '任务初始状态应为 PENDING');
        assert(task.taskId.includes('TranscriptionCorrectionTask'), '任务ID应包含任务名称');
    });

    await runSingleTest('2. 缺少必需字段 - reqId', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = {
            videoIdentifier: 'test-video',
            apiResponse: createTestApiResponse(),
            savePath: '/test/save',
            correctedFullText: 'Test corrected text'
        }; // 缺少 reqId
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'reqId', '错误消息应指明缺少reqId字段');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('3. 缺少必需字段 - videoIdentifier', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = {
            reqId: 'test-req-id',
            apiResponse: createTestApiResponse(),
            savePath: '/test/save',
            correctedFullText: 'Test corrected text'
        }; // 缺少 videoIdentifier
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'videoIdentifier', '错误消息应指明缺少videoIdentifier字段');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('4. 缺少必需字段 - apiResponse', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = {
            reqId: 'test-req-id',
            videoIdentifier: 'test-video',
            savePath: '/test/save',
            correctedFullText: 'Test corrected text'
        }; // 缺少 apiResponse
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明缺少simplifiedSubtitleJsonArray字段');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('5. 缺少必需字段 - savePath', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = {
            reqId: 'test-req-id',
            videoIdentifier: 'test-video',
            apiResponse: createTestApiResponse(),
            correctedFullText: 'Test corrected text'
        }; // 缺少 savePath
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明缺少simplifiedSubtitleJsonArray字段');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('6. 缺少必需字段 - correctedFullText', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = {
            reqId: 'test-req-id',
            videoIdentifier: 'test-video',
            apiResponse: createTestApiResponse(),
            savePath: '/test/save'
        }; // 缺少 correctedFullText
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明缺少simplifiedSubtitleJsonArray字段');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('7. 无效的apiResponse - 缺少segments', async () => {
        const task = new TranscriptionCorrectionTask();
        const invalidApiResponse = {
            text: "Test text",
            language: "en"
            // 缺少 segments 数组
        };
        const context = {
            reqId: 'test-invalid-api',
            videoIdentifier: 'test-video',
            apiResponse: invalidApiResponse,
            savePath: __dirname,
            correctedFullText: 'Test corrected text'
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明simplifiedSubtitleJsonArray相关问题');
            assertEquals(task.status, 'failed', '任务状态应为 FAILED');
        }
    });

    await runSingleTest('8. 进度回调功能', async () => {
        const task = new TranscriptionCorrectionTask();
        const progressLogs = [];
        
        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));
        
        // 测试进度报告
        task.reportProgress('running', 'processing', {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });
        
        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'TranscriptionCorrectionTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, 'running', '进度回调应包含正确的状态');
    });

    await runSingleTest('9. LLM进度报告功能', async () => {
        const task = new TranscriptionCorrectionTask();
        const progressLogs = [];

        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试LLM专用进度报告
        task.reportLLMProgress('preparing', '准备LLM校正请求', {
            current: 30,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录LLM进度回调');
        assertEquals(progressLogs[0].taskName, 'TranscriptionCorrectionTask', 'LLM进度回调应包含正确的任务名称');
        assert(progressLogs[0].technicalDetail, 'LLM进度回调应包含技术详情');
    });

    await runSingleTest('10. 任务状态管理', async () => {
        const task = new TranscriptionCorrectionTask();
        
        // 测试初始状态
        assertEquals(task.status, 'pending', '初始状态应为PENDING');
        
        // 测试开始状态
        task.start();
        assertEquals(task.status, 'started', '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');
        
        // 测试完成状态
        const testResult = { correctionStatus: 'success' };
        task.complete(testResult);
        assertEquals(task.status, 'completed', '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');
        
        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('11. 错误处理和失败状态', async () => {
        const task = new TranscriptionCorrectionTask();
        const testError = new Error('测试错误');

        // 测试失败状态
        task.fail(testError);
        assertEquals(task.status, 'failed', '失败后状态应为FAILED');
        assertEquals(task.error, testError, '应保存错误对象');
        assert(task.endTime, '失败时应记录结束时间');
    });

    await runSingleTest('12. collectDetailedContext 方法', async () => {
        const task = new TranscriptionCorrectionTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
        assert(context.transcriptionProcessingDetails, '上下文应包含transcriptionProcessingDetails');
        assert(context.llmDetails, '上下文应包含llmDetails');
        assertEquals(context.collectionMethod, 'TranscriptionCorrectionTask.collectDetailedContext',
                    '收集方法应正确标识');
    });

    await runSingleTest('13. validateSegmentsArray 方法', async () => {
        const task = new TranscriptionCorrectionTask();
        const execLogPrefix = '[测试]';

        // 测试有效的segments数组（5字段结构）
        const validSegments = [
            { id: "1", text: 'Hello world', start: 0.0, end: 2.0, words: [{ text: "Hello", start: 0.0, end: 1.0 }, { text: "world", start: 1.0, end: 2.0 }] },
            { id: "2", text: 'Test segment', start: 2.0, end: 4.0, words: [{ text: "Test", start: 2.0, end: 3.0 }, { text: "segment", start: 3.0, end: 4.0 }] }
        ];

        // 应该不抛出错误
        try {
            task.validateSegmentsArray(validSegments, execLogPrefix);
            assert(true, 'validateSegmentsArray应接受有效的segments数组');
        } catch (error) {
            throw new Error(`validateSegmentsArray不应对有效数组抛出错误: ${error.message}`);
        }

        // 测试无效的segments数组（缺少words字段）
        const invalidSegments = [
            { id: "1", text: 'Valid segment', start: 0.0, end: 2.0, words: [{ text: "Valid", start: 0.0, end: 1.0 }, { text: "segment", start: 1.0, end: 2.0 }] },
            { id: "2", text: 'Invalid segment', start: 2.0, end: 4.0 } // 缺少words字段
        ];

        try {
            task.validateSegmentsArray(invalidSegments, execLogPrefix);
            throw new Error('validateSegmentsArray应对无效数组抛出错误');
        } catch (error) {
            assertIncludes(error.message, '5字段', '错误消息应指明5字段结构相关问题');
        }
    });

    await runSingleTest('14. 数据验证方法测试', async () => {
        const task = new TranscriptionCorrectionTask();
        const execLogPrefix = '[测试]';

        // 测试5字段结构的数据验证
        const testSegments = [
            { id: "1", text: 'Hello', start: 0.0, end: 1.0, words: [{ text: "Hello", start: 0.0, end: 1.0 }] },
            { id: "2", text: 'world.', start: 1.0, end: 2.0, words: [{ text: "world.", start: 1.0, end: 2.0 }] }
        ];

        // 使用已有的validateSegmentsArray方法进行测试
        try {
            task.validateSegmentsArray(testSegments, execLogPrefix);
            assert(true, '5字段结构验证应通过');
        } catch (error) {
            throw new Error(`5字段结构验证失败: ${error.message}`);
        }

        // 验证数据结构
        assert(Array.isArray(testSegments), '测试数据应为数组');
        assert(testSegments.length > 0, '测试数据数组不应为空');
        assert(testSegments[0].id, '测试数据项应包含id字段');
        assert(testSegments[0].text, '测试数据项应包含text字段');
        assert(typeof testSegments[0].start === 'number', '测试数据项的start应为数字');
        assert(typeof testSegments[0].end === 'number', '测试数据项的end应为数字');
        assert(Array.isArray(testSegments[0].words), '测试数据项应包含words数组');
    });

    await runSingleTest('15. formatTime 方法', async () => {
        const task = new TranscriptionCorrectionTask();

        // 测试正常时间格式化
        const result1 = task.formatTime(65.5); // 1分5.5秒
        assertEquals(result1, '00:01:05,500', '应正确格式化时间');

        // 测试0秒
        const result2 = task.formatTime(0);
        assertEquals(result2, '00:00:00,000', '应正确格式化0秒');

        // 测试无效输入
        const result3 = task.formatTime(-1);
        assertEquals(result3, '00:00:00,000', '应对无效输入返回默认值');
    });

    await runSingleTest('16. generateEnglishSRT 方法', async () => {
        const task = new TranscriptionCorrectionTask();
        const execLogPrefix = '[测试]';

        const testSimplifiedJson = [
            { id: '1', start: 0.0, end: 2.0, text: 'Hello world.' },
            { id: '2', start: 2.0, end: 4.0, text: 'This is a test.' }
        ];

        const srtContent = task.generateEnglishSRT(testSimplifiedJson, execLogPrefix);

        assert(typeof srtContent === 'string', 'SRT内容应为字符串');
        assert(srtContent.length > 0, 'SRT内容不应为空');
        assertIncludes(srtContent, '1', 'SRT应包含序号');
        assertIncludes(srtContent, 'Hello world.', 'SRT应包含文本内容');
        assertIncludes(srtContent, '-->', 'SRT应包含时间分隔符');
    });

    await runSingleTest('17. saveSimplifiedSubtitleJson 方法', async () => {
        const task = new TranscriptionCorrectionTask();
        const testData = [
            { id: '1', start: 0.0, end: 2.0, text: 'Test subtitle' }
        ];
        const videoIdentifier = 'test-video-123';
        const execLogPrefix = '[测试]';
        const savePath = __dirname;

        try {
            const savedPath = await task.saveSimplifiedSubtitleJson(testData, videoIdentifier, execLogPrefix, savePath);

            assert(savedPath, 'saveSimplifiedSubtitleJson应返回保存路径');
            assertIncludes(savedPath, `${videoIdentifier}_corrected.json`, '保存路径应包含正确的文件名');
            assert(fs.existsSync(savedPath), '保存的文件应存在');

            // 验证文件内容
            const savedContent = JSON.parse(fs.readFileSync(savedPath, 'utf8'));
            assert(Array.isArray(savedContent), '保存的内容应为数组');
            assertEquals(savedContent[0].text, testData[0].text, '保存的内容应正确');

            // 清理测试文件
            cleanupTestFile(savedPath);

        } catch (error) {
            // 如果保存失败，确保清理可能创建的文件
            const expectedPath = path.join(savePath, `${videoIdentifier}_corrected.json`);
            cleanupTestFile(expectedPath);
            throw error;
        }
    });

    await runSingleTest('18. saveEnglishSRT 方法', async () => {
        const task = new TranscriptionCorrectionTask();
        const testSrtContent = '1\r\n00:00:00,000 --> 00:00:02,000\r\nTest subtitle\r\n\r\n';
        const videoIdentifier = 'test-video-123';
        const execLogPrefix = '[测试]';
        const savePath = __dirname;

        try {
            const savedPath = await task.saveEnglishSRT(testSrtContent, videoIdentifier, execLogPrefix, savePath);

            assert(savedPath, 'saveEnglishSRT应返回保存路径');
            assertIncludes(savedPath, `${videoIdentifier}_corrected_english_subtitle.srt`, '保存路径应包含正确的文件名');
            assert(fs.existsSync(savedPath), '保存的文件应存在');

            // 验证文件内容
            const savedContent = fs.readFileSync(savedPath, 'utf8');
            assertEquals(savedContent, testSrtContent, '保存的内容应正确');

            // 清理测试文件
            cleanupTestFile(savedPath);

        } catch (error) {
            // 如果保存失败，确保清理可能创建的文件
            const expectedPath = path.join(savePath, `${videoIdentifier}_corrected_english_subtitle.srt`);
            cleanupTestFile(expectedPath);
            throw error;
        }
    });

    await runSingleTest('19. 真实LLM交互测试 - 字幕校正', async () => {
        const task = new TranscriptionCorrectionTask();
        const progressLogs = [];
        
        // 从真实的字幕文件中读取完整数据
        const simplifiedSubtitleJsonPath = path.join(__dirname, '../../../uploads/input/test_video_b1ogtn_simplified_subtitle.json');
        let simplifiedSubtitleJsonArray;
        
        try {
            const fileContent = fs.readFileSync(simplifiedSubtitleJsonPath, 'utf8');
            simplifiedSubtitleJsonArray = JSON.parse(fileContent);
            logger.info(`${testLogPrefix} 成功读取真实字幕数据，共 ${simplifiedSubtitleJsonArray.length} 个片段`);
        } catch (error) {
            throw new Error(`无法读取字幕数据文件: ${simplifiedSubtitleJsonPath} - ${error.message}`);
        }

        // 从真实数据中提取完整文本
        const fullText = simplifiedSubtitleJsonArray.map(segment => segment.text).join(' ');
        
        // 构建模拟的 apiResponse
        const mockApiResponse = {
            text: fullText,
            language: "en",
            segments: simplifiedSubtitleJsonArray
        };

        const context = {
            reqId: 'test-llm-interaction-req-001',
            videoIdentifier: 'test_video_b1ogtn_real_llm',
            apiResponse: mockApiResponse,
            simplifiedSubtitleJsonArray: simplifiedSubtitleJsonArray,
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output',
            correctedFullText: fullText
        };

        try {
            // 设置进度回调
            task.setProgressCallback((data) => {
                progressLogs.push(data);
                logger.info(`${testLogPrefix}[LLM测试进度] ${data.status} - ${data.substatus} - ${data.detail || 'N/A'}`);
            });

            // 执行真实的LLM交互测试
            logger.info(`${testLogPrefix} 开始真实LLM交互测试...`);
            const result = await task.execute(context, (data) => progressLogs.push(data));

            // 验证结果
            assert(result, '任务执行应返回结果');
            assertEquals(task.status, 'completed', '任务状态应为完成');
            assert(result.correctionStatus, '结果应包含校正状态');
            
            if (result.correctionStatus === 'success') {
                assert(result.simplifiedSubtitleJsonArray, '结果应包含校正后的简化字幕数组');
                assert(Array.isArray(result.simplifiedSubtitleJsonArray), '校正后的简化字幕数组应为数组');
                assert(result.simplifiedSubtitleJsonArray.length > 0, '校正后的简化字幕数组不应为空');
                
                // 验证5字段结构
                const firstSegment = result.simplifiedSubtitleJsonArray[0];
                assert(firstSegment.id, '片段应包含id字段');
                assert(typeof firstSegment.start === 'number', '片段应包含start字段');
                assert(typeof firstSegment.end === 'number', '片段应包含end字段');
                assert(firstSegment.text, '片段应包含text字段');
                assert(Array.isArray(firstSegment.words), '片段应包含words数组');
                
                logger.info(`${testLogPrefix} LLM校正成功，共处理 ${result.simplifiedSubtitleJsonArray.length} 个片段`);
                logger.info(`${testLogPrefix} 首个片段校正结果: ${firstSegment.text}`);
            } else {
                logger.warn(`${testLogPrefix} LLM校正状态: ${result.correctionStatus}`);
            }

            // 验证进度日志
            assert(progressLogs.length > 0, '应记录进度日志');
            logger.info(`${testLogPrefix} 共记录 ${progressLogs.length} 条进度日志`);

        } catch (error) {
            logger.error(`${testLogPrefix} LLM交互测试失败: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
            }
            throw error;
        }
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== TranscriptionCorrectionTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1); // 以错误码退出，方便CI/CD集成
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0); // 成功退出
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
