{"mcpServers": {"Figma-Context-MCP Docs": {"url": "https://gitmcp.io/GLips/Figma-Context-MCP"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Figma AI Bridge": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--st<PERSON>"], "env": {"FIGMA_API_KEY": "*********************************************"}}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "C:/Users/<USER>/cursor_interact_mcp/interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "sk-or-v1-a978dceb653e81712499b71996e2a43085f664f3f589a03fc222f9a1c684dd22E"}}, "mcp-user-insights": {"command": "node", "args": ["C:/Users/<USER>/Desktop/codebase/mcp-user-insights/dist/cli.js"], "env": {"MCP_API_KEY": "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", "MCP_API_BASE_URL": "https://openrouter.ai/api", "MCP_DEFAULT_MODEL": "deepseek/deepseek-r1-0528:free", "MCP_WEB_PORT": "5050", "MCP_DIALOG_TIMEOUT": "60000"}}}}