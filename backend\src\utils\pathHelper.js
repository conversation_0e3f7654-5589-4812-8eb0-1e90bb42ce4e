/**
 * @功能概述: 项目文件路径管理工具类
 * @作用: 统一管理项目目录结构，提供标准化的路径生成方法
 * @创建时间: 2025-07-16
 * @架构设计: 支持按项目ID分组的文件目录架构
 */

const path = require('path');
const fs = require('fs');
const config = require('../config');
const logger = require('./logger');

// 模块级日志前缀
const moduleLogPrefix = '[文件：pathHelper.js][路径管理工具][模块初始化]';
logger.info(`${moduleLogPrefix} PathHelper工具类已加载`);

/**
 * @功能概述: 项目路径管理工具类
 * @设计原则: 
 *   - 统一路径生成逻辑
 *   - 支持项目级目录组织
 *   - 自动创建必要目录
 *   - 生成标准化URL
 */
class PathHelper {
    /**
     * @功能概述: 获取projects根目录路径
     * @returns {string} projects目录的完整路径
     * @示例: getProjectsDir() → 'backend/uploads/projects'
     * @用途: 扫描所有项目目录
     */
    static getProjectsDir() {
        const projectsDir = path.join(config.baseUploadDir, config.projectDirs.projects);
        logger.debug(`${moduleLogPrefix}[getProjectsDir] projects目录: ${projectsDir}`);
        return projectsDir;
    }

    /**
     * @功能概述: 获取项目根目录路径
     * @param {string} videoIdentifier - 视频标识符
     * @returns {string} 项目根目录的完整路径
     * @示例: getProjectDir('video-123') → 'backend/uploads/projects/video-123'
     */
    static getProjectDir(videoIdentifier) {
        const projectDir = path.join(
            config.baseUploadDir,
            config.projectDirs.projects,
            videoIdentifier
        );
        logger.debug(`${moduleLogPrefix}[getProjectDir] 项目目录: ${projectDir}`);
        return projectDir;
    }
    
    /**
     * @功能概述: 获取原始文件目录路径
     * @param {string} videoIdentifier - 视频标识符
     * @returns {string} 原始文件目录的完整路径
     * @用途: 存放用户上传的原始视频文件
     */
    static getSourceDir(videoIdentifier) {
        const sourceDir = path.join(
            this.getProjectDir(videoIdentifier), 
            config.projectDirs.source
        );
        logger.debug(`${moduleLogPrefix}[getSourceDir] 原始文件目录: ${sourceDir}`);
        return sourceDir;
    }
    
    /**
     * @功能概述: 获取处理文件目录路径
     * @param {string} videoIdentifier - 视频标识符
     * @returns {string} 处理文件目录的完整路径
     * @用途: 存放上传流水线产出的文件（音频、转录、字幕等）
     */
    static getProcessedDir(videoIdentifier) {
        const processedDir = path.join(
            this.getProjectDir(videoIdentifier), 
            config.projectDirs.processed
        );
        logger.debug(`${moduleLogPrefix}[getProcessedDir] 处理文件目录: ${processedDir}`);
        return processedDir;
    }
    
    /**
     * @功能概述: 获取生成文件目录路径
     * @param {string} videoIdentifier - 视频标识符
     * @returns {string} 生成文件目录的完整路径
     * @用途: 存放生成流水线产出的文件（短视频、ASS字幕等）
     */
    static getGeneratedDir(videoIdentifier) {
        const generatedDir = path.join(
            this.getProjectDir(videoIdentifier), 
            config.projectDirs.generated
        );
        logger.debug(`${moduleLogPrefix}[getGeneratedDir] 生成文件目录: ${generatedDir}`);
        return generatedDir;
    }
    
    /**
     * @功能概述: 创建完整的项目目录结构
     * @param {string} videoIdentifier - 视频标识符
     * @returns {object} 包含所有目录路径的对象
     * @说明: 自动创建所有必要的子目录，确保目录结构完整
     */
    static createProjectDirs(videoIdentifier) {
        const logPrefix = `${moduleLogPrefix}[createProjectDirs][${videoIdentifier}]`;
        
        try {
            const sourceDir = this.getSourceDir(videoIdentifier);
            const processedDir = this.getProcessedDir(videoIdentifier);
            const generatedDir = this.getGeneratedDir(videoIdentifier);
            
            // 创建所有必要目录
            fs.mkdirSync(sourceDir, { recursive: true });
            fs.mkdirSync(processedDir, { recursive: true });
            fs.mkdirSync(generatedDir, { recursive: true });
            
            logger.info(`${logPrefix} 项目目录结构创建成功:`);
            logger.info(`${logPrefix}   - 原始文件: ${sourceDir}`);
            logger.info(`${logPrefix}   - 处理文件: ${processedDir}`);
            logger.info(`${logPrefix}   - 生成文件: ${generatedDir}`);
            
            return { 
                sourceDir, 
                processedDir, 
                generatedDir,
                projectDir: this.getProjectDir(videoIdentifier)
            };
        } catch (error) {
            logger.error(`${logPrefix} 创建项目目录失败: ${error.message}`);
            throw new Error(`创建项目目录失败: ${error.message}`);
        }
    }
    
    /**
     * @功能概述: 生成文件访问URL
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} category - 文件分类 (source/processed/generated)
     * @param {string} filename - 文件名
     * @returns {string} 完整的文件访问URL
     * @示例: generateFileUrl('video-123', 'processed', 'audio.mp3') 
     *        → 'http://localhost:3000/api/files/projects/video-123/processed/audio.mp3'
     */
    static generateFileUrl(videoIdentifier, category, filename) {
        const baseUrl = process.env.BASE_URL || 'http://localhost:8081';
        const fileUrl = `${baseUrl}/api/files/projects/${videoIdentifier}/${category}/${filename}`;
        logger.debug(`${moduleLogPrefix}[generateFileUrl] 生成URL: ${fileUrl}`);
        return fileUrl;
    }
    
    /**
     * @功能概述: 生成标准化的文件URL集合
     * @param {string} videoIdentifier - 视频标识符
     * @param {object} files - 文件信息对象
     * @returns {object} 包含所有文件URL的标准化对象
     * @用途: 为前端提供统一的文件访问链接结构
     */
    static generateFileUrls(videoIdentifier, files = {}) {
        const logPrefix = `${moduleLogPrefix}[generateFileUrls][${videoIdentifier}]`;
        
        const urls = {
            source: {},
            processed: {},
            generated: {}
        };
        
        // 生成source文件URLs
        if (files.source) {
            Object.keys(files.source).forEach(key => {
                urls.source[key] = this.generateFileUrl(videoIdentifier, 'source', files.source[key]);
            });
        }
        
        // 生成processed文件URLs
        if (files.processed) {
            Object.keys(files.processed).forEach(key => {
                urls.processed[key] = this.generateFileUrl(videoIdentifier, 'processed', files.processed[key]);
            });
        }
        
        // 生成generated文件URLs
        if (files.generated) {
            Object.keys(files.generated).forEach(key => {
                urls.generated[key] = this.generateFileUrl(videoIdentifier, 'generated', files.generated[key]);
            });
        }
        
        logger.debug(`${logPrefix} 生成文件URLs完成，包含 ${Object.keys(urls.source).length + Object.keys(urls.processed).length + Object.keys(urls.generated).length} 个文件`);
        
        return urls;
    }
    
    /**
     * @功能概述: 检查项目目录是否存在
     * @param {string} videoIdentifier - 视频标识符
     * @returns {boolean} 项目目录是否存在
     */
    static projectExists(videoIdentifier) {
        const projectDir = this.getProjectDir(videoIdentifier);
        const exists = fs.existsSync(projectDir);
        logger.debug(`${moduleLogPrefix}[projectExists] 项目 ${videoIdentifier} 存在: ${exists}`);
        return exists;
    }
    
    /**
     * @功能概述: 获取临时文件目录
     * @param {string} sessionId - 会话标识符
     * @returns {string} 临时文件目录路径
     */
    static getTempDir(sessionId) {
        const tempDir = path.join(
            config.baseUploadDir,
            config.projectDirs.temp,
            sessionId
        );
        logger.debug(`${moduleLogPrefix}[getTempDir] 临时目录: ${tempDir}`);
        return tempDir;
    }
}

// 导出PathHelper类
module.exports = PathHelper;
