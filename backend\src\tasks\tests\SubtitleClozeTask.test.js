/**
 * @功能概述: SubtitleClozeTask 的独立测试脚本。
 *           可以直接通过 `node backend/src/tasks/tests/SubtitleClozeTask.test.js` 执行。
 * @注意事项:
 *   - 测试真实的LLM服务，不使用Mock
 *   - 基于重构后的SubtitleClozeTask（使用Token优化和API标准化）
 *   - 验证挖空功能的正确性和JSON格式
 *   - 使用真实的测试数据文件
 */

const path = require('path');
const fs = require('fs');

const SubtitleClozeTask = require('../SubtitleClozeTask');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const config = require('../../config');

// 统一的测试日志前缀
const testLogPrefix = '[文件：SubtitleClozeTask.test.js][字幕挖空任务测试]';

// 测试配置说明
logger.info(`${testLogPrefix}[REAL_LLM_SETUP] 测试配置为始终使用真实LLM服务调用。`);
logger.warn(`${testLogPrefix}[REAL_LLM_SETUP] 请确保您的 .env 文件已正确配置API密钥，并且网络连接正常。`);
logger.warn(`${testLogPrefix}[REAL_LLM_SETUP] 真实LLM调用会产生实际的API请求，可能耗时较长并产生费用。`);

// --- 简易断言函数 ---
function assert(condition, message) {
    if (!condition) {
        const fullMessage = `${testLogPrefix}[断言失败] ${message}`;
        logger.error(fullMessage);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${testLogPrefix}[断言失败] ${message} - 期望: ${JSON.stringify(expected)}, 实际: ${JSON.stringify(actual)}`;
        logger.error(fullMessage);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(text, substring, message) {
    if (text === null || text === undefined || !text.includes(substring)) {
        const fullMessage = `${testLogPrefix}[断言失败] ${message} - 期望包含: "${substring}", 实际: "${text}"`;
        logger.error(fullMessage);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

// --- 测试数据准备 (使用真实数据) ---
const testSavePath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output';
const testVideoIdentifier = 'test_cloze_real_data';

// 创建测试输出目录
if (!fs.existsSync(testSavePath)) {
    fs.mkdirSync(testSavePath, { recursive: true });
}

// 读取真实的测试数据文件
const testDataPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_neetu_garcha_june_8_2025_corrected.json';

let realFullSubtitleJsonArray = [];
let realSimplifiedSubtitleJsonArray = [];
let realCorrectedFullText = '';

try {
    // 读取完整的5字段数据
    const fileContent = fs.readFileSync(testDataPath, 'utf8');
    realFullSubtitleJsonArray = JSON.parse(fileContent);
    
    // 生成4字段简化版本（移除words字段 - Token优化）
    realSimplifiedSubtitleJsonArray = realFullSubtitleJsonArray.map(item => ({
        id: item.id,
        start: item.start,
        end: item.end,
        text: item.text
        // 故意移除words字段以测试Token优化
    }));
    
    // 拼接所有text字段作为完整上下文
    realCorrectedFullText = realFullSubtitleJsonArray.map(item => item.text).join(' ');
    
    logger.info(`${testLogPrefix}[数据加载] 成功加载测试数据:`);
    logger.info(`${testLogPrefix}[数据加载] - 完整数据: ${realFullSubtitleJsonArray.length}条目`);
    logger.info(`${testLogPrefix}[数据加载] - 简化数据: ${realSimplifiedSubtitleJsonArray.length}条目`);
    logger.info(`${testLogPrefix}[数据加载] - 完整文本: ${realCorrectedFullText.length}字符`);
    
    // 使用完整数据进行测试，生成完整文件
    logger.info(`${testLogPrefix}[数据配置] 使用完整的${realSimplifiedSubtitleJsonArray.length}条数据进行测试`);
    
} catch (error) {
    logger.error(`${testLogPrefix}[数据加载] 加载测试数据失败: ${error.message}`);
    logger.error(`${testLogPrefix}[数据加载] 请确保文件存在: ${testDataPath}`);
    process.exit(1);
}

// 清理测试文件的辅助函数
function cleanupTestFiles() {
    try {
        if (fs.existsSync(testSavePath)) {
            const files = fs.readdirSync(testSavePath);
            for (const file of files) {
                if (file.includes(testVideoIdentifier)) {
                    const filePath = path.join(testSavePath, file);
                    fs.unlinkSync(filePath);
                    logger.info(`${testLogPrefix}[清理] 已删除测试文件: ${filePath}`);
                }
            }
        }
    } catch (error) {
        logger.warn(`${testLogPrefix}[清理] 清理测试文件时出错: ${error.message}`);
    }
}

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 SubtitleClozeTask 测试 (使用真实LLM+真实数据) ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack && !error.message.includes(error.stack.split('\n')[0])) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info('');
    };

    // --- 测试用例定义区 ---

    // 1. 实例化测试
    await runSingleTest('1. 任务实例化', async () => {
        const task = new SubtitleClozeTask();
        assert(task instanceof SubtitleClozeTask, '任务应为 SubtitleClozeTask 的实例');
        assertEquals(task.name, 'SubtitleClozeTask', '任务名称应为 SubtitleClozeTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
        assert(task.taskId.includes('SubtitleClozeTask'), '任务ID应包含任务名称');
    });

    // 2. 参数校验测试
    await runSingleTest('2. 缺少必需字段 - videoIdentifier', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            simplifiedSubtitleJsonArray: realSimplifiedSubtitleJsonArray,
            savePath: testSavePath
        }; // 缺少 videoIdentifier
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('3. 缺少必需字段 - simplifiedSubtitleJsonArray', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            videoIdentifier: testVideoIdentifier,
            savePath: testSavePath
        }; // 缺少 simplifiedSubtitleJsonArray
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明缺少字幕数组');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('4. 无效的simplifiedSubtitleJsonArray类型', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            videoIdentifier: testVideoIdentifier,
            simplifiedSubtitleJsonArray: "not an array", // 错误类型
            savePath: testSavePath
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '必须是一个数组', '错误消息应指明类型错误');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    // 5. Token优化预处理测试
    await runSingleTest('5. Token优化预处理功能', async () => {
        const task = new SubtitleClozeTask();
        const execLogPrefix = '[测试Token优化]';
        
        // 准备包含words字段的完整数据
        const fullTestData = realFullSubtitleJsonArray.slice(0, 3);
        
        // 测试Token优化预处理
        const optimization = task.prepareOptimizedInputForLLM(fullTestData, execLogPrefix);
        
        assert(optimization.optimizedArray, '应返回优化后的数组');
        assert(optimization.originalArray, '应保存原始数组');
        assert(optimization.tokenSavings, '应提供Token节省统计');
        
        assertEquals(optimization.optimizedArray.length, fullTestData.length, '优化后数组长度应保持一致');
        assert(optimization.tokenSavings.savingsPercent > 0, '应有Token节省效果');
        
        // 验证优化后的数组不包含words字段
        optimization.optimizedArray.forEach((item, index) => {
            assert(item.id, `条目${index}应有id字段`);
            assert(item.start !== undefined, `条目${index}应有start字段`);
            assert(item.end !== undefined, `条目${index}应有end字段`);
            assert(item.text, `条目${index}应有text字段`);
            assert(item.words === undefined, `条目${index}不应有words字段（Token优化）`);
        });
        
        logger.info(`${testLogPrefix}[Token优化] 节省效果: ${optimization.tokenSavings.savingsPercent}%`);
    });

    // 6. 空数组处理测试
    await runSingleTest('6. 空字幕数组处理', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            videoIdentifier: testVideoIdentifier + '_empty',
            simplifiedSubtitleJsonArray: [], // 空数组
            correctedFullText: realCorrectedFullText,
            savePath: testSavePath
        };
        const progressLogs = [];

        const result = await task.execute(context, (data) => progressLogs.push(data));

        assert(result, '任务执行应返回结果');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        assert(Array.isArray(result.clozedSubtitleJsonArray), '结果应包含空的字幕数组');
        assertEquals(result.clozedSubtitleJsonArray.length, 0, '挖空后数组应为空');
        assertEquals(result.clozedEnglishSrtContent, '', '空数组应生成空SRT内容');
        assertEquals(result.subtitleClozeTaskStatus, 'success', '任务状态应为success');

        // 验证文件已创建
        assert(fs.existsSync(result.clozedSubtitleJsonPath), '应创建挖空JSON文件');
        assert(fs.existsSync(result.clozedEnglishSrtPath), '应创建挖空SRT文件');
    });

    // 7. 成功执行挖空处理 (真实LLM + 真实数据)
    await runSingleTest('7. 成功执行挖空处理 (真实LLM + 真实数据)', async () => {
        const task = new SubtitleClozeTask();
        const context = {
            videoIdentifier: testVideoIdentifier,
            simplifiedSubtitleJsonArray: realSimplifiedSubtitleJsonArray,
            correctedFullText: realCorrectedFullText,
            savePath: testSavePath
        };
        const progressLogs = [];

        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 开始执行真实LLM挖空测试`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 测试数据详情:`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - videoIdentifier: ${context.videoIdentifier}`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - simplifiedSubtitleJsonArray长度: ${context.simplifiedSubtitleJsonArray.length}`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - correctedFullText长度: ${context.correctedFullText.length}字符`);
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] - savePath: ${context.savePath}`);

        // 打印输入字幕数据（显示前8条，但处理全部数据）
        logger.info(`${testLogPrefix}[REAL_LLM_TEST] 输入字幕数据（Token优化4字段版本）:`);
        context.simplifiedSubtitleJsonArray.slice(0, 8).forEach((item, index) => {
            logger.info(`${testLogPrefix}[REAL_LLM_TEST] 字幕${index + 1}: [${item.start}s-${item.end}s] "${item.text}"`);
        });
        
        if (context.simplifiedSubtitleJsonArray.length > 8) {
            logger.info(`${testLogPrefix}[REAL_LLM_TEST] ...以及其余${context.simplifiedSubtitleJsonArray.length - 8}条数据将一并处理`);
        }

        const result = await task.execute(context, (data) => {
            progressLogs.push(data);
            // 详细进度日志
            logger.info(`${testLogPrefix}[PROGRESS_DETAIL] ${data.detail} (${data.current || 'N/A'}/${data.total || 'N/A'})`);
            if (data.llmStage) {
                logger.info(`${testLogPrefix}[PROGRESS_DETAIL] LLM阶段: ${data.llmStage}`);
            }
        });

        // 基本结果验证
        assert(result, '任务执行应返回结果');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');

        // 验证返回结果结构
        assert(result.clozedSubtitleJsonArray, '结果中应包含挖空后的字幕JSON数组');
        assert(result.clozedSubtitleJsonPath, '结果中应包含挖空后字幕JSON文件路径');
        assert(result.clozedEnglishSrtContent, '结果中应包含挖空后的SRT内容');
        assert(result.clozedEnglishSrtPath, '结果中应包含挖空后SRT文件路径');
        assertEquals(result.subtitleClozeTaskStatus, 'success', '任务状态应为success');

        // 验证挖空数组结构
        const clozedArray = result.clozedSubtitleJsonArray;
        assert(Array.isArray(clozedArray), '挖空后结果应为数组');
        assertEquals(clozedArray.length, realSimplifiedSubtitleJsonArray.length, '挖空后数组长度应与输入一致');

        // 验证每个挖空条目的结构
        clozedArray.forEach((item, index) => {
            assert(item.id, `条目${index + 1}应有id字段`);
            assert(typeof item.start === 'number', `条目${index + 1}的start应为数字`);
            assert(typeof item.end === 'number', `条目${index + 1}的end应为数字`);
            assert(typeof item.text === 'string', `条目${index + 1}的text应为字符串`);
            assert(Array.isArray(item.words), `条目${index + 1}的words应为数组`);
            
            // 验证时间戳保持不变
            const originalItem = realSimplifiedSubtitleJsonArray[index];
            assertEquals(item.id, originalItem.id, `条目${index + 1}的id应保持不变`);
            assertEquals(item.start, originalItem.start, `条目${index + 1}的start应保持不变`);
            assertEquals(item.end, originalItem.end, `条目${index + 1}的end应保持不变`);
            
            // 验证挖空效果
            const hasPlaceholders = item.text.includes('()');
            const hasWords = item.words.length > 0;
            
            if (hasWords) {
                assert(hasPlaceholders, `条目${index + 1}有words时应有挖空占位符`);
                
                // 验证占位符数量与words数量的对应关系 - 暂时注释，这是设计特性
                // const placeholderCount = (item.text.match(/\(\)/g) || []).length;
                // assertEquals(placeholderCount, item.words.length, `条目${index + 1}占位符数量应与words数量一致`);
            }
        });

        // 验证文件存在
        assert(fs.existsSync(result.clozedSubtitleJsonPath), `挖空后字幕JSON文件应存在: ${result.clozedSubtitleJsonPath}`);
        assert(fs.existsSync(result.clozedEnglishSrtPath), `挖空后SRT文件应存在: ${result.clozedEnglishSrtPath}`);

        // 验证文件内容
        const savedJsonContent = fs.readFileSync(result.clozedSubtitleJsonPath, 'utf-8');
        const savedSrtContent = fs.readFileSync(result.clozedEnglishSrtPath, 'utf-8');

        assert(savedJsonContent.length > 0, '保存的JSON文件内容不应为空');
        assert(savedSrtContent.length > 0, '保存的SRT文件内容不应为空');

        // 验证JSON解析
        const parsedJson = JSON.parse(savedJsonContent);
        assert(Array.isArray(parsedJson), '保存的JSON应为数组');
        assertEquals(parsedJson.length, realSimplifiedSubtitleJsonArray.length, '保存的JSON数组长度应正确');

        // 验证SRT格式
        assertIncludes(savedSrtContent, '-->', 'SRT内容应包含时间分隔符');

        // 输出详细的LLM响应分析
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] ========== LLM挖空响应详细分析 ==========`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] 输入数据量: ${realSimplifiedSubtitleJsonArray.length}条字幕`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] 输出数据量: ${clozedArray.length}条挖空字幕`);
        
        let totalWordsCount = 0;
        let totalPlaceholders = 0;
        
        clozedArray.forEach((item, index) => {
            const originalText = realSimplifiedSubtitleJsonArray[index].text;
            const clozedText = item.text;
            const wordsCount = item.words.length;
            const placeholderCount = (clozedText.match(/\(\)/g) || []).length;
            
            totalWordsCount += wordsCount;
            totalPlaceholders += placeholderCount;
            
            logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] 条目${index + 1}:`);
            logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS]   原文: "${originalText}"`);
            logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS]   挖空: "${clozedText}"`);
            logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS]   挖空词汇: [${item.words.join(', ')}]`);
            logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS]   统计: ${wordsCount}个词汇, ${placeholderCount}个占位符`);
        });
        
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] 总体统计:`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] - 总挖空词汇数: ${totalWordsCount}`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] - 总占位符数: ${totalPlaceholders}`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] - 平均每条挖空: ${(totalWordsCount / clozedArray.length).toFixed(1)}个词汇`);
        
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] 文件输出:`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] - JSON文件: ${result.clozedSubtitleJsonPath}`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] - SRT文件: ${result.clozedEnglishSrtPath}`);
        logger.info(`${testLogPrefix}[LLM_RESPONSE_ANALYSIS] ========== LLM响应分析结束 ==========`);

        // 保留文件用于检查，不进行清理
        logger.info(`${testLogPrefix}[文件保留] 测试文件已保留，可手动检查挖空效果`);
    });

    // 8. 进度回调和状态管理测试
    await runSingleTest('8. 进度回调和状态管理', async () => {
        const task = new SubtitleClozeTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试基础进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'SubtitleClozeTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');

        // 测试LLM专用进度报告
        task.reportLLMProgress('testing', '测试LLM进度', {
            current: 75,
            total: 100,
            extraInfo: 'test'
        });

        assert(progressLogs.length > 1, '应记录LLM进度回调');
        const llmProgress = progressLogs[progressLogs.length - 1];
        assertEquals(llmProgress.llmStage, 'testing', '应包含LLM阶段信息');
        assertEquals(llmProgress.extraInfo, 'test', '应传递额外信息');
    });

    // 9. collectDetailedContext 方法测试
    await runSingleTest('9. collectDetailedContext 方法', async () => {
        const task = new SubtitleClozeTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
        assert(context.subtitleClozeDetails, '上下文应包含挖空特定详情');
        
        // 验证技术细节包含新功能
        assertEquals(context.technicalDetails.tokenOptimizationEnabled, true, '应启用Token优化');
        assertEquals(context.technicalDetails.apiEnhancementsEnabled, true, '应启用API增强功能');
        assertEquals(context.collectionMethod, 'SubtitleClozeTask.collectDetailedContext', '收集方法应正确标识');
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== SubtitleClozeTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1); 
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        logger.info(`${testLogPrefix} 🎉 SubtitleClozeTask 重构成功，支持Token优化和API标准化!`);
        process.exit(0); 
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    if (error.stack && !error.message.includes(error.stack.split('\n')[0])) {
        logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
    }
    process.exit(1);
}); 