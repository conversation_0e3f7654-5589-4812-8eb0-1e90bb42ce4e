/**
 * @功能概述: 视频生成流水线服务，负责构建和执行视频生成相关的 Pipeline。
 *           此服务编排ClipMediaTask任务，实现视频画面裁剪、多片段剪辑和自动合并。
 *           基于PipelineBase架构，集成标准化进度监控机制，支持细粒度的流水线和任务进度追踪。
 *
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 *
 * @进度监控: 使用标准化的进度监控机制，支持实时SSE事件推送
 * @数据流向: TaskBase → PipelineBase → VideoGenerationPipelineService → Controller → Frontend
 */

const PipelineBase = require('../class/PipelineBase');
const ClipMediaTask = require('../tasks/ClipMediaTask');
const ConvertToAudioForCloudflareTask = require('../tasks/ConvertToAudioForCloudflareTask');
const GetTranscriptionTaskByCloudflare = require('../tasks/GetTranscriptionTaskByCloudflare');
const SubtitleOptimizationTask = require('../tasks/SubtitleOptimizationTask');
const { TranscriptionCorrectionTask, TranslateSubtitleTask } = require('../tasks');
const ContentSummarizationTask = require('../tasks/ContentSummarizationTask');
const SubtitleClozeTask = require('../tasks/SubtitleClozeTask');
const BilingualSubtitleMergeTask = require('../tasks/BilingualSubtitleMergeTask');
const GenerateASSTask = require('../tasks/GenerateASSTask');
const GenerateVideoTask = require('../tasks/GenerateVideoTask');
const logger = require('../utils/logger');
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');
const fs = require('fs').promises;
const path = require('path');

// 导入标准化进度监控常量和工厂函数
const {
    PIPELINE_STATUS,
    TASK_STATUS,
    createPipelineProgressData,
    validateProgressData
} = require('../constants/progress');

// 模块级日志前缀 - 统一格式
const moduleLogPrefix = `[文件：videoGenerationPipelineService.js][视频生成服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录，任务通过 services/ 目录调用外部API`);

/**
 * @功能概述: 视频生成流水线服务类，负责编排和执行视频处理任务
 * @架构说明: 基于PipelineBase架构，包含ClipMediaTask任务
 * @进度监控: 通过SSE向前端发送流水线状态更新
 */
class VideoGenerationPipelineService {
    /**
     * @功能概述: 构造函数。初始化视频生成流水线并添加所需任务。
     * @param {string} [reqId='unknown_req'] - 请求ID，用于日志追踪。
     */
    constructor(reqId = 'unknown_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：videoGenerationPipelineService.js][视频生成服务][ReqID:${this.reqId}]`;

        // 创建流水线实例
        this.processingPipeline = new PipelineBase(`VideoGeneration-${this.reqId}`);

        // 按执行顺序统一添加所有任务
        this.addAllTasks();

        // 记录最终任务序列
        const taskNames = this.processingPipeline.tasks.map(task => task.constructor.name).join(' → ');
        logger.info(`${this.logPrefix}[视频生成流水线] Pipeline 已创建，包含11个任务的完整序列: ${taskNames}`);
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     * @任务序列: 11个任务的完整视频生成流水线
     */
    addAllTasks() {
        // 按执行顺序添加任务（11个任务序列）
        this.addClipMediaTask();                        // 1. 媒体剪辑任务
        this.addConvertToAudioForCloudflareTask();      // 2. Cloudflare音频转换任务
        this.addGetTranscriptionTaskByCloudflare();     // 3. Cloudflare语音转录任务
        this.addSubtitleOptimizationTask();             // 4. 字幕优化任务
        this.addTranscriptionCorrectionTask();          // 5. 转录校正任务
        this.addContentSummarizationTask();             // 6. 内容总结任务
        this.addTranslateSubtitleTask();                // 7. 字幕翻译任务
        this.addSubtitleClozeTask();                    // 8. 字幕挖空任务
        this.addBilingualSubtitleMergeTask();           // 9. 双语字幕合并增强任务
        this.addGenerateASSTask();                      // 10. ASS字幕文件生成任务
        this.addGenerateVideoTask();                    // 11. 最终视频生成任务
    }

    /**
     * @功能概述: 添加媒体剪辑任务 (ClipMediaTask) 到流水线。
     *           此任务负责对原始视频进行画面裁剪、多片段剪辑和自动合并处理。
     *
     * @上下文输入 (context 预期的字段):
     *   - reqId: {string} (必需) 请求追踪ID
     *   - originalVideoPath: {string} (必需) 原始视频文件路径
     *   - cropData: {object} (必需) 裁剪参数对象，包含cropWidth、cropHeight、cropXOffset、cropYOffset
     *   - clipSegments: {Array} (必需) 片段数组，每个包含startTime和endTime
     *   - savePath: {string} (必需) 输出文件保存目录
     *
     * @执行后上下文状态 (ClipMediaTask 完成后 context 对象的完整内容):
     *   // === 来自初始输入 ===
     *   - reqId: {string} 请求追踪ID
     *   - originalVideoPath: {string} 原始视频文件路径
     *   - cropData: {object} 裁剪参数对象
     *   - clipSegments: {Array} 片段数组
     *   - savePath: {string} 输出文件保存目录
     *   - videoIdentifier: {string} 视频文件标识符
     *   - videoConfig: {object} 视频配置对象
     *   - correctedFullText: {string} 校正后的完整文本
     *   - englishSrtContent: {string} 英文字幕内容
     *   - chineseSrtContent: {string} 中文字幕内容
     *
     *   // === 来自 ClipMediaTask (第1个任务，当前任务新增) ===
     *   - processedVideoPath: {string} 最终处理后的视频文件路径
     *   - processedVideoFileName: {string} 最终处理后的视频文件名
     *   (任务内部会更新自身的 this.status 和 this.result)
     *
     *   // === 最终流水线输出摘要 ===
     *   // 此时 context 包含完整的处理结果，主要可用数据：
     *   // 1. processedVideoPath - 最终合并后的视频文件路径，可用于下载或播放
     *   // 2. processedVideoFileName - 最终视频文件名，可用于前端显示
     *   // 3. originalVideoPath - 原始视频路径，可用于对比或备份
     *   // 4. cropData/clipSegments - 处理参数，可用于记录或重现处理过程
     */
    addClipMediaTask() {
        this.processingPipeline.addTask(new ClipMediaTask());
    }

    /**
     * @功能概述: 添加Cloudflare专用视频转音频任务 (ConvertToAudioForCloudflareTask) 到流水线。
     *           此任务负责从处理后的视频文件中提取音频，生成极度压缩的MP3音频文件供Cloudflare转录使用。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - originalVideoPath: {string} (必需) 处理后的视频文件路径（来自ClipMediaTask）
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @执行后上下文状态 (ConvertToAudioForCloudflareTask 完成后 context 新增的字段):
     *   - audioFilePath: {string} 提取的音频文件路径
     *   - audioFilePathInUploads: {string} 音频文件在uploads目录中的路径
     *   - audioFileName: {string} 音频文件名
     *   - audioFileSize: {number} 音频文件大小（字节）
     *   - cloudflareOptimized: {boolean} 标识为Cloudflare优化版本
     */
    addConvertToAudioForCloudflareTask() {
        this.processingPipeline.addTask(new ConvertToAudioForCloudflareTask());
    }

    /**
     * @功能概述: 添加Cloudflare语音转录任务 (GetTranscriptionTaskByCloudflare) 到流水线。
     *           此任务负责调用Cloudflare Workers AI Whisper API，将音频文件转换为文本转录结果。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - audioFilePathInUploads: {string} (必需) 音频文件路径，来自ConvertToAudioForCloudflareTask
     *   - savePath: {string} (必需) 文件保存路径
     *   - cloudflareOptimized: {boolean} (可选) 标识是否为Cloudflare优化版本
     *
     * @执行后上下文状态 (GetTranscriptionTaskByCloudflare 完成后 context 新增的字段):
     *   - apiResponse: {object} Cloudflare Workers AI原始响应数据
     *   - transcriptionStatus: {string} 转录状态
     *   - transcriptionText: {string} 转录文本
     *   - transcriptionJsonPath: {string} 转录JSON文件路径
     *   - segments: {Array} 转录段落数组
     */
    addGetTranscriptionTaskByCloudflare() {
        this.processingPipeline.addTask(new GetTranscriptionTaskByCloudflare());
    }

    /**
     * @功能概述: 添加字幕优化任务 (SubtitleOptimizationTask) 到流水线。
     *           此任务负责优化转录后的字幕质量，包括合并过短segments、拆分过长segments、语义边界优化等。
     *
     * @上下文输入 (context 预期的字段):
     *   - reqId: {string} (必需) 请求追踪ID
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - apiResponse: {object} (必需) 来自GetTranscriptionTask的转录数据
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @执行后上下文状态 (SubtitleOptimizationTask 完成后 context 新增的字段):
     *   - optimizedData: {Array} 优化后的segments数据
     *   - optimizedFilePath: {string} 优化后字幕文件路径
     *   - simplifiedSubtitleJsonArray: {Array} 简化字幕JSON数组（5字段：id, start, end, text, words）
     *   - simplifiedSubtitleJsonPath: {string} 简化字幕JSON文件路径
     *   - optimizedEnglishSrtContent: {string} 优化后的英文字幕SRT内容
     *   - optimizationStatus: {string} 优化状态
     */
    addSubtitleOptimizationTask() {
        this.processingPipeline.addTask(new SubtitleOptimizationTask());
    }

    /**
     * @功能概述: 添加转录校正任务 (TranscriptionCorrectionTask) 到流水线。
     *           此任务负责对语音转写任务的输出进行校正处理，
     *           包括调用LLM服务优化转录文本，并最终生成SRT格式的英文字幕。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - apiResponse: {object} (必需) 语音转写服务返回的原始JSON响应数据，来自GetTranscriptionTask
     *   - transcriptionStatus: {string} (必需) 转录状态，来自GetTranscriptionTask
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @执行后上下文状态 (TranscriptionCorrectionTask 完成后 context 新增的字段):
     *   - simplifiedSubtitleJsonArray: {Array} 英文字幕JSON数组
     *   - simplifiedSubtitleJsonPath: {string} 英文字幕JSON文件路径
     *   - englishSrtPath: {string} 英文SRT文件路径
     *   - correctedFullText: {string} 校正后的完整文本
     */
    addTranscriptionCorrectionTask() {
        this.processingPipeline.addTask(new TranscriptionCorrectionTask());
    }

    /**
     * @功能概述: 添加内容总结任务 (ContentSummarizationTask) 到流水线。
     *           此任务负责对完整转录文本进行AI总结，生成内容摘要和标题。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - fullTranscriptText: {string} (必需) 完整转录文本，来自TranscriptionCorrectionTask
     *   - savePath: {string} (必需) 文件保存路径
     *   - reqId: {string} (必需) 请求追踪ID
     *
     * @执行后上下文状态 (ContentSummarizationTask 完成后 context 新增的字段):
     *   - transcriptSummary: {string} 内容摘要（100个中文字）
     *   - transcriptTitle: {string} 内容标题（10个中文字）
     *   - summaryJsonPath: {string} 保存的JSON文件路径
     *   - summaryStatus: {string} 总结状态
     *   - summaryMetadata: {object} 总结元数据
     *   - dynamicVideoTitle: {string} 动态视频标题（title + " | " + summary）
     */
    addContentSummarizationTask() {
        // 创建一个包装的ContentSummarizationTask，在执行后添加动态标题生成逻辑
        const originalTask = new ContentSummarizationTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 执行原始的ContentSummarizationTask
            const result = await originalExecute(context, progressCallback);

            // 生成动态标题
            if (result.transcriptTitle && result.transcriptSummary) {
                result.dynamicVideoTitle = `${result.transcriptTitle} | ${result.transcriptSummary}`;
                logger.info(`${this.logPrefix}[ContentSummarizationTask] 生成动态标题: "${result.dynamicVideoTitle}"`);
            } else {
                result.dynamicVideoTitle = result.transcriptTitle || '未知标题';
                logger.warn(`${this.logPrefix}[ContentSummarizationTask] 标题或摘要缺失，使用默认标题: "${result.dynamicVideoTitle}"`);
            }

            // 将动态标题添加到context中，供后续Task使用
            context.dynamicVideoTitle = result.dynamicVideoTitle;
            logger.info(`${this.logPrefix}[ContentSummarizationTask] 动态标题已添加到context: "${context.dynamicVideoTitle}"`);

            return result;
        };

        this.processingPipeline.addTask(originalTask);
    }

    /**
     * @功能概述: 添加字幕翻译任务 (TranslateSubtitleTask) 到流水线。
     *           此任务负责将英文字幕翻译为中文字幕，生成双语字幕对照。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     *
     * @执行后上下文状态 (TranslateSubtitleTask 完成后 context 新增的字段):
     *   - translatedSubtitleJsonArray: {Array} 翻译后的中文字幕JSON数组
     *   - translatedSubtitleJsonPath: {string} 中文字幕JSON文件路径
     *   - chineseSrtPath: {string} 中文SRT文件路径
     */
    addTranslateSubtitleTask() {
        this.processingPipeline.addTask(new TranslateSubtitleTask());
    }

    /**
     * @功能概述: 添加字幕挖空任务 (SubtitleClozeTask) 到流水线。
     *           此任务负责对英文字幕进行智能挖空处理，生成填空练习格式的字幕。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     *
     * @执行后上下文状态 (SubtitleClozeTask 完成后 context 新增的字段):
     *   - clozedSubtitleJsonArray: {Array} 挖空字幕JSON数组
     *   - clozedSubtitleJsonPath: {string} 挖空字幕JSON文件路径
     *   - clozedSubtitleSrtPath: {string} 挖空字幕SRT文件路径
     */
    addSubtitleClozeTask() {
        this.processingPipeline.addTask(new SubtitleClozeTask());
    }

    /**
     * @功能概述: 添加双语字幕合并增强任务 (BilingualSubtitleMergeTask) 到流水线。
     *           此任务负责将英文、中文和挖空字幕合并，并通过LLM生成词汇解释。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - translatedSubtitleJsonArray: {Array} (必需) 来自TranslateSubtitleTask的中文字幕JSON数组
     *   - clozedSubtitleJsonArray: {Array} (必需) 来自SubtitleClozeTask的挖空字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     *   - dynamicVideoTitle: {string} (可选) 动态视频标题，用于文件命名
     *
     * @执行后上下文状态 (BilingualSubtitleMergeTask 完成后 context 新增的字段):
     *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕JSON数组
     *   - enhancedBilingualSubtitleJsonPath: {string} 增强双语字幕JSON文件路径
     *   - words_explanation: {object} 词汇解释对象
     *   - dynamicVideoTitleForFile: {string} 处理后的动态标题（用于文件命名）
     */
    addBilingualSubtitleMergeTask() {
        // 创建一个包装的BilingualSubtitleMergeTask，使用动态标题
        const originalTask = new BilingualSubtitleMergeTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 使用动态标题更新videoIdentifier（如果存在）
            if (context.dynamicVideoTitle) {
                // 清理标题中的特殊字符，用于文件命名
                const cleanTitle = context.dynamicVideoTitle
                    .replace(/[\/\\:*?"<>|]/g, '_')  // 替换文件系统不允许的字符
                    .substring(0, 100);  // 限制长度

                context.dynamicVideoTitleForFile = cleanTitle;
                logger.info(`${this.logPrefix}[BilingualSubtitleMergeTask] 使用动态标题: "${cleanTitle}"`);
            }

            return await originalExecute(context, progressCallback);
        };

        this.processingPipeline.addTask(originalTask);
    }

    /**
     * @功能概述: 添加ASS字幕生成任务 (GenerateASSTask) 到流水线。
     *           此任务负责生成ASS格式字幕文件，包含视频标题、填空字幕、双语字幕等所有类型。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioTask
     *   - clozedSubtitleJsonArray: {Array} (必需) 挖空字幕数组，来自SubtitleClozeTask
     *   - enhancedBilingualSubtitleJsonArray: {Array} (必需) 增强双语字幕数组，来自BilingualSubtitleMergeTask
     *   - savePath: {string} (必需) 文件保存路径
     *   - videoConfig: {object} (必需) 视频配置对象
     *
     * @执行后上下文状态 (GenerateASSTask 完成后 context 新增的字段):
     *   - assFilePath: {string} 生成的ASS字幕文件完整路径
     *   - assContent: {string} 完整的ASS字幕内容
     *   - assContentLength: {number} ASS内容长度（字符数）
     *   - audioDuration: {number} 音频时长（秒）
     *   - processedSubtitles: {Object} 处理统计信息
     */
    addGenerateASSTask() {
        this.processingPipeline.addTask(new GenerateASSTask());
    }

    /**
     * @功能概述: 添加视频生成任务 (GenerateVideoTask) 到流水线。
     *           此任务负责生成完整的9:16短视频，包含音频重复拼接、进度条生成、背景视频生成、ASS字幕烧录等功能。
     *
     * @上下文输入 (context 预期的字段):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - originalVideoPath: {string} (必需) 原始视频文件路径，来自ClipMediaTask
     *   - audioDuration: {number} (必需) 音频时长（秒），来自GenerateASSTask
     *   - videoConfig: {Object} (必需) 视频配置对象
     *   - assContent: {string} (必需) ASS字幕内容，来自GenerateASSTask
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioTask
     *   - savePath: {string} (必需) 文件保存路径
     *   - dynamicVideoTitle: {string} (可选) 动态视频标题，用于文件命名
     *
     * @执行后上下文状态 (GenerateVideoTask 完成后 context 新增的字段):
     *   - extendedAudioPath: {string} 重复拼接后的音频文件路径
     *   - progressBarVideoPath: {string} 进度条视频文件路径
     *   - backgroundVideoPath: {string} 背景视频文件路径
     *   - finalVideoPath: {string} 最终生成的视频文件路径
     *   - videoGenerationStats: {Object} 视频生成统计信息
     */
    addGenerateVideoTask() {
        // 创建一个包装的GenerateVideoTask，使用动态标题
        const originalTask = new GenerateVideoTask();
        const originalExecute = originalTask.execute.bind(originalTask);

        originalTask.execute = async (context, progressCallback) => {
            // 使用动态标题更新videoIdentifier（如果存在）
            if (context.dynamicVideoTitle) {
                // 清理标题中的特殊字符，用于文件命名
                const cleanTitle = context.dynamicVideoTitle
                    .replace(/[\/\\:*?"<>|]/g, '_')  // 替换文件系统不允许的字符
                    .substring(0, 100);  // 限制长度

                context.dynamicVideoTitleForFile = cleanTitle;
                logger.info(`${this.logPrefix}[GenerateVideoTask] 使用动态标题: "${cleanTitle}"`);
            }

            return await originalExecute(context, progressCallback);
        };

        this.processingPipeline.addTask(originalTask);
    }



    /**
     * @功能概述: 执行完整的视频生成流程，包括视频画面裁剪、多片段剪辑和自动合并。
     * @param {object} initialContext - 初始上下文，必须包含:
     *                                 - originalVideoPath: string - 原始视频文件路径
     *                                 - cropData: object - 裁剪参数对象，包含cropWidth、cropHeight、cropXOffset、cropYOffset
     *                                 - clipSegments: Array - 片段数组，每个包含startTime和endTime
     *                                 - savePath: string - 文件保存路径
     *                                 - videoIdentifier: string - 视频文件标识符
     *                                 - videoConfig: object - 视频配置对象，包含repeatCount和repeatModes
     *                                 - correctedFullText: string - 校正后的完整文本
     *                                 - englishSrtContent: string - 英文字幕内容
     *                                 - chineseSrtContent: string - 中文字幕内容
     *                                 - (可选) reqId: string - 请求ID，会传递给各任务
     * @param {function} serviceProgressCallback - 服务进度回调函数，用于报告服务执行状态
     * @returns {Promise<object>} Pipeline 执行结果，包含 status, context, tasks。
     *                            成功时，context 中应包含:
     *                              - processedVideoPath: string (来自 ClipMediaTask)
     *                              - processedVideoFileName: string (来自 ClipMediaTask)
     *                              - 其他处理统计信息
     */
    async processVideoGeneration(initialContext, serviceProgressCallback) {
        logger.info(`${this.logPrefix}[processVideoGeneration] 开始执行视频生成流程（视频裁剪和片段剪辑）。`);

        // 步骤 1: 构建执行上下文
        const currentContext = {
            reqId: this.reqId,
            ...initialContext
        };

        // 步骤 2: 参数校验（简化为基本必需字段）
        const requiredFields = [
            'originalVideoPath', 'videoIdentifier'
        ];
        let missingFields = [];
        for (const field of requiredFields) {
            if (currentContext[field] === null || currentContext[field] === undefined) {
                missingFields.push(field);
            }
        }

        // 额外验证clipSegments数组（如果提供）
        if (currentContext.clipSegments && Array.isArray(currentContext.clipSegments)) {
            if (currentContext.clipSegments.length === 0) {
                missingFields.push('clipSegments (empty array)');
            } else {
                currentContext.clipSegments.forEach((segment, index) => {
                    if (typeof segment.startTime !== 'number' || typeof segment.endTime !== 'number') {
                        missingFields.push(`clipSegments[${index}] (invalid startTime or endTime)`);
                    }
                });
            }
        }

        // 额外验证cropData对象（如果提供）
        if (currentContext.cropData && typeof currentContext.cropData === 'object') {
            const cropFields = ['cropWidth', 'cropHeight', 'cropXOffset', 'cropYOffset'];
            cropFields.forEach(field => {
                if (typeof currentContext.cropData[field] !== 'number') {
                    missingFields.push(`cropData.${field} (not a number)`);
                }
            });
        }

        if (missingFields.length > 0) {
            const errorMsg = `processVideoGeneration 调用失败：initialContext 必须包含以下字段: ${missingFields.join(', ')}。`;
            logger.error(`${this.logPrefix}[processVideoGeneration] ${errorMsg}`);

            // 步骤 2.1: 参数校验失败时的回调处理
            if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
                try {
                    logger.debug(`${this.logPrefix}[processVideoGeneration] 参数校验失败，调用服务进度回调。`);
                    serviceProgressCallback({
                        serviceName: 'VideoGenerationPipelineService',
                        methodName: 'processVideoGeneration',
                        status: 'failed_setup',
                        error: { message: errorMsg },
                        timestamp: new Date().toISOString()
                    });
                } catch (cbError) {
                    logger.error(`${this.logPrefix}[processVideoGeneration] 服务进度回调执行出错: ${cbError.message}`);
                }
            }

            return {
                status: 'failed',
                error: new Error(errorMsg),
                context: currentContext,
                tasks: []
            };
        }

        // 步骤 3: 记录任务序列
        const taskNames = this.processingPipeline.tasks.map(task => task.constructor.name).join(' → ');
        logger.info(`${this.logPrefix}[processVideoGeneration][步骤 1] 流水线任务序列确认 (11个任务): ${taskNames}`);

        // 步骤 3.1: 记录接收到的关键参数
        logger.info(`${this.logPrefix}[processVideoGeneration] 参数校验通过，接收到的关键参数:`);
        logger.info(`${this.logPrefix}  - originalVideoPath: ${currentContext.originalVideoPath}`);
        logger.info(`${this.logPrefix}  - 片段数量: ${currentContext.clipSegments.length}`);
        logger.info(`${this.logPrefix}  - 片段详情: ${JSON.stringify(currentContext.clipSegments)}`);
        logger.info(`${this.logPrefix}  - 裁剪尺寸: ${currentContext.cropData.cropWidth}x${currentContext.cropData.cropHeight}`);
        logger.info(`${this.logPrefix}  - 裁剪偏移: (${currentContext.cropData.cropXOffset}, ${currentContext.cropData.cropYOffset})`);
        logger.info(`${this.logPrefix}  - savePath: ${currentContext.savePath}`);
        if (currentContext.videoIdentifier) {
            logger.info(`${this.logPrefix}  - videoIdentifier: ${currentContext.videoIdentifier}`);
        }
        if (currentContext.videoConfig) {
            logger.info(`${this.logPrefix}  - 重复次数: ${currentContext.videoConfig.repeatCount}`);
            logger.info(`${this.logPrefix}  - 重复模式: ${currentContext.videoConfig.repeatModes?.map(m => m.displayText).join(', ') || 'N/A'}`);
        }
        if (currentContext.correctedFullText) {
            logger.info(`${this.logPrefix}  - 校正文本长度: ${currentContext.correctedFullText.length} 字符`);
        }
        if (currentContext.englishSrtContent) {
            logger.info(`${this.logPrefix}  - 英文字幕长度: ${currentContext.englishSrtContent.length} 字符`);
        }
        if (currentContext.chineseSrtContent) {
            logger.info(`${this.logPrefix}  - 中文字幕长度: ${currentContext.chineseSrtContent.length} 字符`);
        }

        // 步骤 3.5: 验证videoConfig配置（使用已合并的配置，不重新加载文件）
        logger.info(`${this.logPrefix}[processVideoGeneration] 验证videoConfig配置...`);

        if (!currentContext.videoConfig) {
            // 如果没有传入videoConfig，则作为降级策略加载默认配置文件
            logger.warn(`${this.logPrefix}[processVideoGeneration] 未找到传入的videoConfig，使用降级策略加载默认配置文件`);
            const videoConfigPath = path.join(__dirname, '../config/video/video-config.json');
            try {
                const configContent = await fs.readFile(videoConfigPath, 'utf8');
                currentContext.videoConfig = JSON.parse(configContent);
                logger.info(`${this.logPrefix}[processVideoGeneration] 默认配置文件加载成功（降级策略）`);
            } catch (error) {
                logger.error(`${this.logPrefix}[processVideoGeneration] 默认配置文件加载失败: ${error.message}`);
                throw new Error(`无法加载默认video-config.json配置文件: ${error.message}`);
            }
        } else {
            // 使用已经合并好的配置（来自generateVideoController.js）
            logger.info(`${this.logPrefix}[processVideoGeneration] 使用已合并的videoConfig配置（来自Controller）`);
            logger.info(`${this.logPrefix}[processVideoGeneration] 配置来源: 前端参数 + 默认配置深度合并`);
        }

        // 记录最终使用的配置摘要
        logger.info(`${this.logPrefix}[processVideoGeneration] 最终配置摘要: ${currentContext.videoConfig.width}x${currentContext.videoConfig.height}, ${currentContext.videoConfig.framerate}fps, 重复${currentContext.videoConfig.repeatCount}次`);

        // 记录关键的前端自定义配置项
        if (currentContext.videoConfig.subtitleConfig) {
            const videoGuide = currentContext.videoConfig.subtitleConfig.videoGuide;
            const advertisement = currentContext.videoConfig.subtitleConfig.advertisement;

            if (videoGuide) {
                logger.info(`${this.logPrefix}[processVideoGeneration] 视频引导配置: enabled=${videoGuide.enabled}, title1="${videoGuide.title1}", title2="${videoGuide.title2}"`);
            }

            if (advertisement && advertisement.titles && advertisement.titles.length > 0) {
                logger.info(`${this.logPrefix}[processVideoGeneration] 广告配置: enabled=${advertisement.enabled}, 标题数量=${advertisement.titles.length}`);
                logger.info(`${this.logPrefix}[processVideoGeneration] 第一个广告标题: line1="${advertisement.titles[0].line1}", line2="${advertisement.titles[0].line2}"`);
            }
        }

        // 步骤 4: 执行流水线
        logger.debug(`${this.logPrefix}[processVideoGeneration] 执行内部流水线，并传递回调函数。`);
        const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);
        logger.info(`${this.logPrefix}[processVideoGeneration] 视频生成流程执行完毕。Status: ${result.status}`);

        // 步骤 5: 记录关键输出
        if (result.context) {
            const { processedVideoPath, processedVideoFileName, originalVideoPath } = result.context;
            logger.debug(`${this.logPrefix}[processVideoGeneration] 最终上下文关键信息: 原始视频=${originalVideoPath}, 处理后视频=${processedVideoPath}, 文件名=${processedVideoFileName}`);
        }

        // 步骤 6: 标准化返回结果
        const standardizedResult = standardizePipelineResult(result, 'generation', currentContext.reqId);
        logger.info(`${this.logPrefix}[processVideoGeneration] 结果已标准化，状态: ${standardizedResult.status}`);

        return standardizedResult;
    }
}

// 导出服务类供其他模块使用
module.exports = VideoGenerationPipelineService;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}VideoGenerationPipelineService 类已导出。`);