/**
 * @文件名: renameProjectController.js
 * @功能概述: 项目文件名重命名控制器
 * @创建时间: 2025-07-27
 * @作者: Augment Agent
 * @描述: 
 *   此控制器负责修改项目的原始视频文件名，不修改扩展名。
 *   修改source目录下的原始视频文件名，同时更新项目元数据。
 *   遵循API创建指导规则，采用单一职责原则。
 */

// 导入必要的模块
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const PathHelper = require('../../utils/pathHelper');

/**
 * @功能概述: 重命名项目文件名
 * @接口路径: PUT /api/video/renameProject
 * @请求参数:
 *   - videoIdentifier (必需): 项目标识符
 *   - newFileName (必需): 新的文件名（不包含扩展名）
 * @响应格式: 
 *   - status: success/error
 *   - message: 响应消息
 *   - data: { videoIdentifier, oldFileName, newFileName, filePath }
 * @错误处理: 
 *   - 项目不存在：返回404错误
 *   - 文件不存在：返回404错误
 *   - 文件名冲突：返回409错误
 *   - 参数错误：返回参数验证错误
 */
const renameProject = async (req, res) => {
    const logPrefix = '[文件：renameProjectController.js][renameProject]';
    
    try {
        logger.info(`${logPrefix} 开始重命名项目文件`);
        
        // === 步骤1: 参数验证 ===
        const { videoIdentifier, newFileName } = req.body;
        
        if (!videoIdentifier) {
            logger.warn(`${logPrefix} 缺少必需参数: videoIdentifier`);
            return res.status(400).json({
                status: 'error',
                message: '缺少必需参数: videoIdentifier',
                errorCode: 'MISSING_PARAMETER',
                details: {
                    parameter: 'videoIdentifier',
                    suggestion: '请提供有效的项目标识符'
                }
            });
        }
        
        if (!newFileName || typeof newFileName !== 'string' || newFileName.trim() === '') {
            logger.warn(`${logPrefix} 缺少或无效的参数: newFileName`);
            return res.status(400).json({
                status: 'error',
                message: '缺少或无效的参数: newFileName',
                errorCode: 'INVALID_PARAMETER',
                details: {
                    parameter: 'newFileName',
                    suggestion: '请提供有效的文件名（不包含扩展名）'
                }
            });
        }
        
        // 清理文件名，移除非法字符
        const cleanFileName = newFileName.trim().replace(/[<>:"/\\|?*]/g, '');
        if (cleanFileName === '') {
            logger.warn(`${logPrefix} 文件名包含非法字符: ${newFileName}`);
            return res.status(400).json({
                status: 'error',
                message: '文件名包含非法字符',
                errorCode: 'INVALID_FILENAME',
                details: {
                    originalName: newFileName,
                    suggestion: '文件名不能包含以下字符: < > : " / \\ | ? *'
                }
            });
        }
        
        logger.info(`${logPrefix} 参数验证通过 - 项目ID: ${videoIdentifier}, 新文件名: ${cleanFileName}`);
        
        // === 步骤2: 检查项目是否存在 ===
        const projectDir = PathHelper.getProjectDir(videoIdentifier);
        const sourceDir = PathHelper.getSourceDir(videoIdentifier);
        
        if (!fs.existsSync(projectDir)) {
            logger.warn(`${logPrefix} 项目目录不存在: ${projectDir}`);
            return res.status(404).json({
                status: 'error',
                message: '项目不存在',
                errorCode: 'PROJECT_NOT_FOUND',
                details: {
                    videoIdentifier,
                    projectDir
                }
            });
        }
        
        if (!fs.existsSync(sourceDir)) {
            logger.warn(`${logPrefix} 源文件目录不存在: ${sourceDir}`);
            return res.status(404).json({
                status: 'error',
                message: '项目源文件目录不存在',
                errorCode: 'SOURCE_DIR_NOT_FOUND',
                details: {
                    videoIdentifier,
                    sourceDir
                }
            });
        }
        
        // === 步骤3: 查找原始视频文件 ===
        const sourceFiles = fs.readdirSync(sourceDir);
        const videoFiles = sourceFiles.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'].includes(ext);
        });
        
        if (videoFiles.length === 0) {
            logger.warn(`${logPrefix} 未找到视频文件: ${sourceDir}`);
            return res.status(404).json({
                status: 'error',
                message: '未找到原始视频文件',
                errorCode: 'VIDEO_FILE_NOT_FOUND',
                details: {
                    videoIdentifier,
                    sourceDir,
                    availableFiles: sourceFiles
                }
            });
        }
        
        if (videoFiles.length > 1) {
            logger.warn(`${logPrefix} 发现多个视频文件: ${videoFiles.join(', ')}`);
            return res.status(409).json({
                status: 'error',
                message: '发现多个视频文件，无法确定要重命名的文件',
                errorCode: 'MULTIPLE_VIDEO_FILES',
                details: {
                    videoIdentifier,
                    videoFiles
                }
            });
        }
        
        const originalFile = videoFiles[0];
        const originalPath = path.join(sourceDir, originalFile);
        const fileExtension = path.extname(originalFile);
        const oldFileName = path.basename(originalFile, fileExtension);
        
        logger.info(`${logPrefix} 找到原始视频文件: ${originalFile}`);
        
        // === 步骤4: 检查新文件名是否与当前文件名相同 ===
        if (cleanFileName === oldFileName) {
            logger.info(`${logPrefix} 新文件名与当前文件名相同，无需修改`);
            return res.status(200).json({
                status: 'success',
                message: '文件名未发生变化',
                data: {
                    videoIdentifier,
                    fileName: cleanFileName + fileExtension,
                    filePath: originalPath,
                    changed: false
                }
            });
        }
        
        // === 步骤5: 检查新文件名是否已存在 ===
        const newFileName_full = cleanFileName + fileExtension;
        const newFilePath = path.join(sourceDir, newFileName_full);
        
        if (fs.existsSync(newFilePath)) {
            logger.warn(`${logPrefix} 新文件名已存在: ${newFileName_full}`);
            return res.status(409).json({
                status: 'error',
                message: '新文件名已存在',
                errorCode: 'FILE_NAME_EXISTS',
                details: {
                    videoIdentifier,
                    newFileName: newFileName_full,
                    existingPath: newFilePath
                }
            });
        }
        
        // === 步骤6: 执行文件重命名 ===
        logger.info(`${logPrefix} 开始重命名文件: ${originalFile} -> ${newFileName_full}`);
        
        fs.renameSync(originalPath, newFilePath);
        
        logger.info(`${logPrefix} 文件重命名成功`);
        
        // === 步骤7: 更新项目元数据（如果存在） ===
        const metadataPath = path.join(projectDir, 'metadata.json');
        if (fs.existsSync(metadataPath)) {
            try {
                const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
                metadata.originalVideoName = newFileName_full;
                metadata.lastModified = new Date().toISOString();
                
                fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
                logger.info(`${logPrefix} 项目元数据已更新`);
            } catch (metadataError) {
                logger.warn(`${logPrefix} 更新元数据失败: ${metadataError.message}`);
                // 不影响主要功能，继续执行
            }
        }
        
        // === 步骤8: 返回成功响应 ===
        const responseData = {
            videoIdentifier,
            oldFileName: oldFileName,
            newFileName: cleanFileName,
            fullFileName: newFileName_full,
            filePath: newFilePath,
            changed: true,
            timestamp: new Date().toISOString()
        };
        
        logger.info(`${logPrefix} 项目文件重命名完成: ${oldFileName} -> ${cleanFileName}`);
        
        res.status(200).json({
            status: 'success',
            message: '项目文件重命名成功',
            data: responseData
        });
        
    } catch (error) {
        logger.error(`${logPrefix} 重命名项目文件失败: ${error.message}`);
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            status: 'error',
            message: '服务器内部错误',
            errorCode: 'INTERNAL_SERVER_ERROR',
            details: {
                error: error.message,
                timestamp: new Date().toISOString()
            }
        });
    }
};

module.exports = {
    renameProject
};
