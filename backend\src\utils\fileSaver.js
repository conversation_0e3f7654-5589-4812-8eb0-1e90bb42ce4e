/**
 * @功能概述: 文件保存工具模块，提供将数据保存到文件的通用方法。
 * @遵循原则: 严格遵循 general_principles.mdc 规范
 *   1. 分层架构: 位于 utils 工具层
 *   2. 模块化设计: 独立文件操作功能
 *   3. 错误隔离: 独立错误处理机制
 *   4. 配置解耦: 动态计算项目路径
 */
const fs = require('fs');
const path = require('path');
const logger = require('./logger'); // 假设 logger.js 与 fileSaver.js 在同一 utils 目录下

// 动态路径计算符合架构规范
// 计算后端项目的根目录路径。
// __dirname 指向当前文件 (fileSaver.js) 所在的目录，即 backend/src/utils。
// '..' 向上移动一级目录，到达 backend/src。
// 再次 '..' 向上移动一级目录，到达 backend 目录，这就是后端项目的根目录。
const backendRoot = path.join(__dirname, '..', '..');


// 计算项目根目录路径。
// __dirname 指向当前文件 (fileSaver.js) 所在的目录，即 backend/src/utils。
// '..' 向上移动一级目录，到达 back end/src。
// 再次 '..' 向上移动一级目录，到达 backend 目录，这就是后端项目的根目录。
// 再次 '..' 向上移动一级目录，到达 express 目录，这就是项目根目录。
const projectRoot = path.join(__dirname, '..', '..', '..');

// 日志前缀标准化
const moduleLogPrefix = '[文件：fileSaver.js][文件保存工具][模块初始化]';
logger.info(`${moduleLogPrefix} 文件保存工具模块已加载并导出方法。`);

// 新增：定义项目根目录
// '..', '..', '..' -> .../express (项目根目录)
const defaultBaseUploadsPath = path.join(backendRoot, 'uploads');




/**
 * @功能概述: 将数据保存到指定文件。
 * @param {string | Buffer} data - 要保存到文件的数据。可以是字符串（将以UTF-8编码写入）或Buffer。
 * @param {string} fileName - 要保存的文件名，例如 'example.txt'。
 * @param {string} [baseDirectory=defaultBaseUploadsPath] - 文件保存的基础目录。如果目录不存在，函数将尝试创建它。
 *                                                         默认为 `defaultBaseUploadsPath` (即 `backend/uploads`)。
 * @param {string} [callerLogPrefix='[FileUtils][saveDataToFile]'] - 调用此函数的模块或组件的日志前缀，用于日志追踪和区分来源。
 * @returns {string | null} - 文件成功保存后的完整绝对路径，如果保存失败则返回 `null`。
 */
function saveDataToFile(data, fileName, baseDirectory = defaultBaseUploadsPath, callerLogPrefix = '[FileUtils][saveDataToFile]') {

    // 构造当前函数的日志前缀，用于区分日志来源
    const logPrefix = `${callerLogPrefix}[saveDataToFile]`;

    // 记录文件保存请求的详细信息，包括文件名和目标基础目录
    logger.info(`${logPrefix} 请求保存文件: ${fileName}, 目标基础目录: ${baseDirectory}`);

    try {
        // 步骤 2: 检查并创建基础目录
        // 检查指定的基础目录是否存在
        if (!fs.existsSync(baseDirectory)) {

            // 如果目录不存在，记录日志并尝试创建
            logger.info(`${logPrefix} 基础目录不存在，尝试创建: ${baseDirectory}`);

            // 使用 fs.mkdirSync 同步创建目录，recursive: true 确保可以创建多级目录
            fs.mkdirSync(baseDirectory, { recursive: true });

            // 记录目录创建成功的日志
            logger.info(`${logPrefix} 基础目录创建成功: ${baseDirectory}`);
        } else {
            // 如果目录已存在，记录调试日志
            logger.debug(`${logPrefix} 基础目录已存在: ${baseDirectory}`);
        }

        // 步骤 3: 构建完整的文件保存路径

        // 使用 path.join 拼接基础目录和文件名，构建完整的文件路径
        const filePath = path.join(baseDirectory, fileName); // 例如: /path/to/uploads/myFile.txt

        // 记录构建出的完整文件路径
        logger.debug(`${logPrefix} 构建的完整文件路径: ${filePath}`);

        // 步骤 4: 写入文件
        // 使用 fs.writeFileSync 同步将数据写入指定文件，编码为 UTF-8
        fs.writeFileSync(filePath, data, 'utf-8');
        // 记录文件成功保存的日志，包括保存路径
        logger.info(`${logPrefix} 文件成功保存到: ${filePath}`);

        // 返回保存后的文件的完整绝对路径
        return filePath;

    } catch (error) {
        // 捕获文件保存过程中可能发生的错误
        // 记录错误信息，包括文件名、目标目录和错误消息
        logger.error(`${logPrefix} 保存文件 '${fileName}' 到 '${baseDirectory}' 失败: ${error.message}`);
        // 记录完整的错误堆栈信息，便于调试
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        // 返回 null 表示文件保存失败
        return null;
    }
}





module.exports = {
    saveDataToFile,
    defaultBaseUploadsPath,
    backendRoot,
    projectRoot
    // 可添加注释：
    // @架构验证: 工具层模块，不依赖 controllers/pipelines/services 层
}; 