# Express 视频处理后端服务

本项目是一个基于 Node.js 和 Express.js 构建的后端应用程序，专为处理各类视频任务而设计，包括但不限于音频提取、基于 Azure AI Speech 的语音转文本，以及其他潜在的字幕处理功能。

该项目作为后端 API 服务，不包含用户交互界面。

## ✨ 核心特性

*   **视频转音频**：借助 FFmpeg 实现高效的音频提取。
*   **语音转写与 LLM 服务集成**：支持 Azure AI Speech SDK, Azure OpenAI (Whisper) 以及 OpenRouter，提供精准的语音转写及后续的文本处理能力。
*   **模块化任务流**：业务逻辑以灵活且可扩展的、基于任务的流水线形式组织。
*   **唯一的请求ID追踪**：通过在 `backend/src/app.js` 中间件使用 `uuid` 为每个进入的HTTP请求生成唯一ID (如 `req.id`)，极大地简化了跨模块和服务的日志追踪与问题诊断。
*   **环境变量配置**：通过 `.env` 文件管理应用程序的配置项及敏感凭证。
*   **结构化日志**：采用 Winston 记录稳健的应用程序日志。

##  本地开发环境准备

在开始之前，请确保您的本地计算机已安装以下软件：

1.  **Git**：用于克隆本代码仓库。
2.  **Node.js**：强烈推荐使用 **v16.20.2** 版本，以确保最佳兼容性。
    *   可使用 [nvm](https://github.com/nvm-sh/nvm) (适用于 macOS/Linux) 或 [nvm-windows](https://github.com/coreybutler/nvm-windows) (适用于 Windows) 管理 Node.js 版本：
        ```bash
        nvm install 16.20.2
        nvm use 16.20.2
        ```
3.  **npm**：**v8.19.4** 版本 (随 Node.js v16.20.2 一同安装)。
4.  **FFmpeg & FFprobe**：视频/音频处理的核心工具。
    *   **Windows 用户**：最简便的安装方式是使用 [Chocolatey](https://chocolatey.org/install)：
        ```bash
        # 以管理员身份运行 PowerShell 或 CMD
        choco install ffmpeg --global
        ```
    *   **macOS 用户**：使用 [Homebrew](https://brew.sh/) 进行安装：
        ```bash
        brew install ffmpeg
        ```
    *   **Linux 用户 (Debian/Ubuntu)**：
        ```bash
        sudo apt update && sudo apt install ffmpeg
        ```
    *   **Linux 用户 (CentOS)**：
        ```bash
        sudo yum install epel-release -y
        sudo yum localinstall --nogpgcheck https://download1.rpmfusion.org/free/el/rpmfusion-free-release-7.noarch.rpm -y
        sudo yum localinstall --nogpgcheck https://download1.rpmfusion.org/nonfree/el/rpmfusion-nonfree-release-7.noarch.rpm -y
        sudo yum update -y
        sudo yum install ffmpeg ffmpeg-devel -y
        ```
    *   **验证安装**：在终端运行 `ffmpeg -version` 和 `ffprobe -version`。确保这些命令在系统的 PATH 环境变量中可被找到。

## 🚀 本地开发快速上手

请遵循以下步骤，在您的本地计算机上运行此后端服务：

1.  **克隆代码仓库**：
    ```bash
    git clone https://github.com/sunshine6666666666/express.git
    cd express
    ```

2.  **进入后端项目目录**：
    所有与后端相关的命令都应在 `backend` 目录下执行。
    ```bash
    cd backend
    ```

3.  **安装依赖项**：
    此步骤会安装包括 `express`, `uuid`, `winston` 等在内的所有生产和开发依赖。
    ```bash
    npm install
    ```

4.  **设置环境变量**：
    本应用程序使用位于 `backend` 目录下的 `.env` 文件来存储配置信息和敏感凭证。
    *   复制环境变量示例文件：
        ```bash
        cp .env.sample .env
        ```
    *   使用文本编辑器打开 `backend/.env` 文件，并根据下文 **"环境变量详解"**部分的说明更新各项配置。
        *   **本地配置关键**：请确保 `FFMPEG_PATH` 和 `FFPROBE_PATH` 指向您本地安装的 FFmpeg 和 FFprobe 可执行文件的正确位置，特别是当它们未被自动添加到系统 PATH 时。在 Windows 上，您可能需要提供完整路径，例如 `C:/ffmpeg/bin/ffmpeg.exe`。在 macOS/Linux 上，如果通过包管理器安装，它们通常已在 PATH 中，因此 `.env.sample` 中的默认值 (`ffmpeg` 和 `ffprobe`) 可能直接可用。您可以使用 `which ffmpeg` (macOS/Linux) 或 `where ffmpeg` (Windows) 命令查找具体路径。

5.  **运行应用程序 (开发模式)**：
    此命令通常会使用 `nodemon`，以便在文件发生变更时自动重启服务。
    ```bash
    npm run dev
    ```
    或者，直接运行应用程序 (通常在 `backend/package.json` 的 `scripts.start` 中定义为 `node src/app.js`)：
    ```bash
    npm start
    ```
    后端服务器此时应已启动，并通常监听 `http://localhost:3000` 端口 (或您在 `.env` 文件中指定的端口)。

## ⚙️ 环境变量详解 (`backend/.env`)

`backend/.env` 文件是配置本应用程序的核心。它由 `backend/src/config/index.js` 负责加载。以下是各变量的详细说明：

```env
#--------------------------------------------------------------------------
# 基础配置
#--------------------------------------------------------------------------

# 应用程序运行端口 (可选, 默认 3000)
# PORT=3000

# Node.js 运行环境 (可选, 'development' 或 'production')
# NODE_ENV=development

# 处理后文件的上传目录
# 相对于 'backend' 目录。请确保此目录存在或应用程序有权创建它。
# 例如: uploads/
UPLOAD_DIR=uploads/

# 日志文件存储目录 (可选, 默认 ./logs/)
# 相对于 'backend' 目录。请确保此目录存在或应用程序有权创建它。
# 例如: logs/
# LOG_DIR=logs/

#--------------------------------------------------------------------------
# FFmpeg 路径配置 (对本地和服务器环境均至关重要)
# 由 convertToAudioTask.js 等任务中的 fluent-ffmpeg 库使用。
#--------------------------------------------------------------------------

# Linux/macOS (如果 FFmpeg 和 FFprobe 已在系统 PATH 中):
FFMPEG_PATH=ffmpeg
FFPROBE_PATH=ffprobe

# Linux/macOS (如果需要指定绝对路径，请替换为 'which ffmpeg' 和 'which ffprobe' 的实际输出):
# FFMPEG_PATH=/usr/bin/ffmpeg
# FFPROBE_PATH=/usr/bin/ffprobe

# Windows 示例 (如果 FFmpeg 未在系统 PATH 中，请填写绝对路径):
# FFMPEG_PATH=C:/ffmpeg/bin/ffmpeg.exe
# FFPROBE_PATH=C:/ffmpeg/bin/ffprobe.exe

#--------------------------------------------------------------------------
# Azure OpenAI 服务配置 (用于 Whisper 模型语音转写)
# (如果使用 Azure OpenAI 服务，请填写以下信息)
#--------------------------------------------------------------------------

# 您的 Azure OpenAI 资源终结点 (Endpoint)
AZURE_OPENAI_ENDPOINT=

# 您的 Azure OpenAI 资源密钥 (Key)
AZURE_OPENAI_KEY=

# 您在 Azure AI Foundry 门户中为 Whisper 模型部署的自定义名称
WHISPER_DEPLOYMENT_NAME=

#--------------------------------------------------------------------------
# OpenRouter 服务配置 (可选的 LLM 服务提供商)
# (如果使用 OpenRouter 服务，请填写以下信息)
#--------------------------------------------------------------------------

# 您的 OpenRouter API 密钥
OPENROUTER_API_KEY=

# OpenRouter Chat Completions API 端点 URL
# (注意：请确认这是当前正确的 OpenRouter API v1 基础 URL，如果不是，请使用官方文档提供的)
OPENROUTER_CHAT_COMPLETIONS_URL="https://openrouter.ai/api/v1/chat/completions"

```

**本地开发配置要点**：
*   根据您选择使用的语音转写服务，配置对应的 Azure OpenAI 或 OpenRouter 
*   **务必校验 `FFMPEG_PATH` 和 `FFPROBE_PATH`**。如果 `ffmpeg` 和 `ffprobe` 命令能在您的终端直接运行，则默认值或直接填写命令名可能适用。否则，请提供准确的绝对路径。
*   `UPLOAD_DIR` 和 `LOG_DIR` 默认分别是 `backend/uploads/` 和 `backend/logs/`。应用程序应能自动创建这些目录（如果尚不存在）。

## 🧪 本地 API 端点测试

当服务器在本地成功运行后，您可以测试其根端点：
*   打开浏览器或使用 Postman/curl 等工具访问 `http://localhost:3000/` (或您在 `.env` 中配置的 `PORT`)。您应能看到 "Hello World" HTML 页面。

对于特定的视频处理 API 端点（例如 `/api/video/upload`），则需要发送相应的请求（例如，包含视频文件的 `POST` 请求）。具体的 API 定义请参考 `backend/src/routes/videoRoutes.js` 和 `backend/src/controllers/videoController.js`。

## ☁️ 服务器部署指南 (通用)

本节概述了将此应用程序部署到 Linux 服务器的通用步骤。推荐使用 PM2 进行进程管理，并使用 Nginx (或其他反向代理服务器) 处理 HTTP 请求和 SSL。

详细的服务器特定部署记录（包括遇到的问题及解决方案），您可以参考 `doc/服务器部署操作记录.md` 和 `doc/服务器信息_0525.md` (这些是项目内的特定文档，可能包含具体服务器的配置细节，供项目维护者参考)。

### 服务器端环境要求：
*   满足所有本地开发环境要求 (Node.js v16.20.2, npm v8.19.4, Git, FFmpeg)。
*   **PM2** (Node.js 进程管理器):
    ```bash
    sudo npm install -g pm2
    ```
*   **Nginx** (或其他反向代理服务器，如 Apache)。

### 通用部署步骤：

1.  **通过 SSH 登录服务器**并进入您希望部署项目的目录 (例如 `/var/www/` 或用户主目录下的某个位置)。
    ```bash
    # 示例:
    # sudo mkdir -p /var/www/your_project_name
    # sudo chown your_user:your_group /var/www/your_project_name
    # cd /var/www/your_project_name
    ```

2.  **克隆代码仓库** (如果尚未克隆)：
    ```bash
    git clone https://github.com/sunshine6666666666/express.git . # 克隆到当前目录
    ```
    如果项目已存在，则拉取最新代码：
    ```bash
    git pull origin main
    ```
    *   **Git 拉取问题提示**：如果遇到 `Empty reply from server` 或 `Connection timed out`，可能需要调整 Git 的 `http.postBuffer` 设置。
        ```bash
        # 示例：为当前用户全局设置
        git config --global http.postBuffer 524288000 
        # 或者为特定仓库设置 (在项目目录下执行)
        # git config http.postBuffer 524288000
        ```

3.  **文件权限**：确保运行 Node.js 应用的用户 (例如 `your_node_user`) 对项目文件有读取权限，对日志和上传目录有写入权限。

4.  **设置 `.env` 文件 (后端)**：
    进入后端目录并配置环境变量：
    ```bash
    cd backend
    if [ ! -f .env ]; then cp .env.sample .env; fi
    nano .env # 或使用您喜欢的编辑器
    ```
    *   **重要**: 使用服务器特定值编辑 `.env`。**必须**正确配置 `FFMPEG_PATH` 和 `FFPROBE_PATH` (例如 `/usr/bin/ffmpeg`)，以及所有 Azure、OpenRouter、Dify 等外部服务凭证。
    *   `UPLOAD_DIR` 和 `LOG_DIR` 是相对于 `backend` 目录的路径 (例如 `uploads/` 和 `logs/`)。确保运行应用的用户对这些目标目录有写权限。

5.  **安装后端依赖项**：
    在 `backend` 目录下执行：
    ```bash
    npm install --production # 仅安装生产依赖
    # 或者 npm install 如果也需要开发依赖 (例如用于构建步骤)
    ```
    如果需要以特定用户运行，请确保该用户执行此命令，或调整文件权限。

6.  **使用 PM2 启动应用**：
    在 `backend` 目录下，您可以通过 `package.json` 中的脚本启动，或直接启动 `app.js`。
    ```bash
    # 示例 1: 使用 npm start (假设 package.json 中定义了 start 脚本为 "node src/app.js")
    pm2 start npm --name "your_app_name" -- start 
    # 示例 2: 直接启动 app.js
    # pm2 start src/app.js --name "your_app_name"

    # 设置 PM2 开机自启 (根据提示执行生成的命令)
    # pm2 startup

    # 保存当前 PM2 进程列表
    # pm2 save
    ```
    *   `your_app_name` 是您为 PM2 进程指定的名称。
    *   管理 PM2 进程：
        ```bash
        pm2 list
        pm2 logs your_app_name
        pm2 restart your_app_name
        pm2 stop your_app_name
        pm2 delete your_app_name
        ```

7.  **配置 Nginx 作为反向代理** (示例)：
    创建一个 Nginx 服务器配置文件 (例如，在 `/etc/nginx/sites-available/your_domain.conf`，然后软链接到 `/etc/nginx/sites-enabled/your_domain.conf`)。

    ```nginx
    server {
        listen 80;
        # listen 443 ssl http2; # 如果配置 SSL
        server_name your_domain.com www.your_domain.com; # 替换为您的域名

        # SSL 配置 (如果启用 HTTPS)
        # ssl_certificate /path/to/your/fullchain.pem;
        # ssl_certificate_key /path/to/your/privkey.pem;
        # include /etc/letsencrypt/options-ssl-nginx.conf; # Let's Encrypt 推荐配置
        # ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # Let's Encrypt 推荐配置

        # 日志路径 (根据需要调整)
        access_log /var/log/nginx/your_app_name.access.log;
        error_log /var/log/nginx/your_app_name.error.log;

        client_max_body_size 1024m; # 允许大文件上传，根据需求调整

        location / { # 代理所有请求到 Node.js 应用
            proxy_pass http://127.0.0.1:3000; # 指向 Node.js 应用监听的端口 (应与 .env 中的 PORT 一致)
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_buffering off; # 对 SSE (Server-Sent Events) 和长轮询很重要
        }

        # 可选：特定路径的额外配置或静态文件服务
        # location /static/ {
        #     alias /path/to/your_project_name/frontend/dist/static/;
        # }
    }
    ```
    *   确保将 `your_domain.com`，`your_app_name`，端口 `3000` (如果您的应用监听不同端口) 以及 SSL 证书路径 (如果使用 HTTPS) 替换为您的实际值。
    *   测试并重新加载 Nginx 配置：
        ```bash
        sudo nginx -t
        sudo systemctl reload nginx
        ```

8.  **DNS 与防火墙**：
    *   确保您的域名 DNS 解析指向服务器的公共 IP 地址。
    *   配置服务器防火墙 (如 `ufw`, `firewalld`) 允许 HTTP (80) 和 HTTPS (443) 的入站流量。

### 应用日志：
*   **PM2 日志**: 通常位于 `~/.pm2/logs/` 目录下，以 `your_app_name-out.log` (标准输出) 和 `your_app_name-error.log` (标准错误) 命名。可以通过 `pm2 logs your_app_name` 查看。
*   **应用 (Winston) 日志**: 存储在 `backend/.env` 中 `LOG_DIR` (默认为 `backend/logs/`) 指定的目录下，文件通常为 `app.log` 和 `error.log`。
*   **Nginx 日志**: 路径在 Nginx 配置文件中定义 (例如 `/var/log/nginx/your_app_name.access.log`)。

##  常见问题解答与故障排除

*   **`EACCES: permission denied` (权限不足错误)**：检查运行 Node.js/PM2 的用户对项目文件、日志目录和上传目录的读写权限。
*   **`Cannot find module 'some_module'` (找不到模块错误)**：确保在 `backend` 目录下已执行 `npm install`。检查 Node.js 版本是否与推荐版本一致。
*   **Nginx `502 Bad Gateway` 或 `connect() failed (111: Connection refused)`**：通常表示 Nginx 无法连接到后端 Node.js 应用。
    *   检查 PM2 是否已成功启动应用 (`pm2 list`)。
    *   检查 Node.js 应用是否在 Nginx `proxy_pass` 指令中指定的端口 (例如 `3000`) 上监听。
    *   查看 PM2 日志 (`pm2 logs your_app_name`) 和应用内部日志 (`backend/logs/app.log`) 获取详细错误。
*   **`ffmpeg/ffprobe: command not found` 或视频处理失败**：
    *   确保 FFmpeg 和 FFprobe 已在服务器上正确安装，并且其路径对运行 Node.js 应用的用户可用。
    *   验证 `backend/.env` 文件中的 `FFMPEG_PATH` 和 `FFPROBE_PATH` 是否设置为服务器上这些可执行文件的正确绝对路径 (例如 `/usr/bin/ffmpeg`)。
*   **Git 拉取缓慢或失败**：尝试调整 `http.postBuffer` (如上文所述)，或检查服务器到 GitHub 的网络连接。

## 贡献代码

欢迎为此项目贡献代码！请遵循标准的 Git 工作流程 (例如，fork、创建特性分支、提交 PR)。

## 许可证

本项目采用 [MIT 许可证](LICENSE)。
