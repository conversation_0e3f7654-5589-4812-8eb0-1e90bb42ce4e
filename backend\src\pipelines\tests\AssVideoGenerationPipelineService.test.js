/**
 * @文件名: AssVideoGenerationPipelineService.test.js
 * @功能概述: ASS字幕和视频生成流水线服务的测试文件 - 专门测试GenerateASSTask和GenerateVideoTask
 * @作者: AI Assistant
 * @创建时间: 2025-01-20
 *
 * @测试目标:
 *   1. 验证GenerateASSTask能正确生成ASS字幕文件
 *   2. 验证GenerateVideoTask能正确生成最终视频
 *   3. 测试video-config.json中各项配置的实际效果
 *   4. 验证广告字幕("关注公众号")的显示效果
 *
 * @测试数据:
 *   - 使用真实的音频和视频文件
 *   - 模拟填空字幕和双语字幕数据
 *   - 从video-config.json加载完整配置
 */

const path = require('path');
const fs = require('fs').promises;
const AssVideoGenerationPipelineService = require('../AssVideoGenerationPipelineService');
const logger = require('../../utils/logger');

// 测试配置
const TEST_CONFIG = {
    videoIdentifier: `test_video_${Date.now()}`, // 随机生成
    audioFilePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3",
    originalVideoPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612.mp4", 
    savePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output",
    videoConfigPath: path.join(__dirname, '../../config/video/video-config.json')
};

// 模拟填空字幕数据
const mockClozedSubtitleJsonArray = [
    {
        "id": 1,
        "start": 0.0,
        "end": 3.0,
        "text": "Hello, this is a _____ test.",
        "answer": "simple",
        "chinese": "你好，这是一个简单的测试。"
    },
    {
        "id": 2,
        "start": 3.5,
        "end": 6.0,
        "text": "We are testing the _____ generation.",
        "answer": "video",
        "chinese": "我们正在测试视频生成。"
    },
    {
        "id": 3,
        "start": 6.5,
        "end": 9.0,
        "text": "This should work _____ well.",
        "answer": "very",
        "chinese": "这应该工作得很好。"
    }
];

// 模拟增强双语字幕数据
const mockEnhancedBilingualSubtitleJsonArray = [
    {
        "id": 1,
        "start": 0.0,
        "end": 3.0,
        "english": "Hello, this is a simple test.",
        "chinese": "你好，这是一个简单的测试。",
        "keywords": ["simple", "test"]
    },
    {
        "id": 2,
        "start": 3.5,
        "end": 6.0,
        "english": "We are testing the video generation.",
        "chinese": "我们正在测试视频生成。",
        "keywords": ["testing", "video", "generation"]
    },
    {
        "id": 3,
        "start": 6.5,
        "end": 9.0,
        "english": "This should work very well.",
        "chinese": "这应该工作得很好。",
        "keywords": ["work", "very", "well"]
    }
];

/**
 * @功能概述: 加载video-config.json配置文件
 * @returns {Promise<Object>} 视频配置对象
 */
async function loadVideoConfig() {
    try {
        const configContent = await fs.readFile(TEST_CONFIG.videoConfigPath, 'utf8');
        const config = JSON.parse(configContent);
        logger.info(`[测试配置] 成功加载video-config.json: ${Object.keys(config).length}个配置项`);
        return config;
    } catch (error) {
        logger.error(`[测试配置] 加载video-config.json失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 验证必需的测试文件是否存在
 * @returns {Promise<boolean>} 文件验证结果
 */
async function validateTestFiles() {
    const filesToCheck = [
        { path: TEST_CONFIG.audioFilePath, name: '音频文件' },
        { path: TEST_CONFIG.originalVideoPath, name: '原始视频文件' },
        { path: TEST_CONFIG.videoConfigPath, name: '视频配置文件' }
    ];

    for (const file of filesToCheck) {
        try {
            await fs.access(file.path);
            logger.info(`[文件验证] ✅ ${file.name}存在: ${file.path}`);
        } catch (error) {
            logger.error(`[文件验证] ❌ ${file.name}不存在: ${file.path}`);
            return false;
        }
    }
    
    return true;
}

/**
 * @功能概述: 验证输出目录是否存在，不存在则创建
 * @returns {Promise<void>}
 */
async function ensureOutputDirectory() {
    try {
        await fs.access(TEST_CONFIG.savePath);
        logger.info(`[目录验证] ✅ 输出目录存在: ${TEST_CONFIG.savePath}`);
    } catch (error) {
        logger.info(`[目录验证] 📁 创建输出目录: ${TEST_CONFIG.savePath}`);
        await fs.mkdir(TEST_CONFIG.savePath, { recursive: true });
    }
}

/**
 * @功能概述: 主测试函数
 */
async function runTest() {
    const startTime = Date.now();
    
    try {
        logger.info('\n=== 开始ASS字幕和视频生成流水线测试 ===');
        
        // 步骤 1: 验证测试文件
        logger.info('\n[步骤 1] 验证测试文件...');
        const filesValid = await validateTestFiles();
        if (!filesValid) {
            throw new Error('测试文件验证失败，请检查文件路径');
        }
        
        // 步骤 2: 确保输出目录存在
        logger.info('\n[步骤 2] 确保输出目录存在...');
        await ensureOutputDirectory();
        
        // 步骤 3: 加载视频配置
        logger.info('\n[步骤 3] 加载视频配置...');
        const videoConfig = await loadVideoConfig();
        
        // 步骤 4: 构建测试上下文
        logger.info('\n[步骤 4] 构建测试上下文...');
        const testContext = {
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            audioFilePath: TEST_CONFIG.audioFilePath,
            clozedSubtitleJsonArray: mockClozedSubtitleJsonArray,
            enhancedBilingualSubtitleJsonArray: mockEnhancedBilingualSubtitleJsonArray,
            savePath: TEST_CONFIG.savePath,
            originalVideoPath: TEST_CONFIG.originalVideoPath,
            videoConfig: videoConfig
        };
        
        logger.info(`[测试上下文] 视频标识符: ${testContext.videoIdentifier}`);
        logger.info(`[测试上下文] 填空字幕数量: ${testContext.clozedSubtitleJsonArray.length}`);
        logger.info(`[测试上下文] 双语字幕数量: ${testContext.enhancedBilingualSubtitleJsonArray.length}`);
        
        // 步骤 5: 创建并执行流水线
        logger.info('\n[步骤 5] 创建并执行ASS字幕和视频生成流水线...');
        const pipeline = new AssVideoGenerationPipelineService();
        
        // 进度回调函数
        const progressCallback = (progress) => {
            logger.info(`[流水线进度] ${JSON.stringify(progress, null, 2)}`);
        };
        
        // 执行流水线
        const result = await pipeline.processVideoGeneration(testContext, progressCallback);
        
        // 步骤 6: 验证结果
        logger.info('\n[步骤 6] 验证测试结果...');
        
        if (result.status === 'completed') {
            logger.info('✅ 流水线执行成功！');
            
            // 验证生成的文件
            const { assFilePath, finalVideoPath } = result.context;
            
            if (assFilePath) {
                try {
                    await fs.access(assFilePath);
                    logger.info(`✅ ASS字幕文件生成成功: ${assFilePath}`);
                } catch (error) {
                    logger.error(`❌ ASS字幕文件不存在: ${assFilePath}`);
                }
            }
            
            if (finalVideoPath) {
                try {
                    await fs.access(finalVideoPath);
                    const stats = await fs.stat(finalVideoPath);
                    logger.info(`✅ 最终视频文件生成成功: ${finalVideoPath}`);
                    logger.info(`📊 视频文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
                } catch (error) {
                    logger.error(`❌ 最终视频文件不存在: ${finalVideoPath}`);
                }
            }
            
            // 显示统计信息
            if (result.context.videoGenerationStats) {
                logger.info(`📈 视频生成统计: ${JSON.stringify(result.context.videoGenerationStats, null, 2)}`);
            }
            
        } else {
            logger.error('❌ 流水线执行失败！');
            logger.error(`错误信息: ${result.error ? result.error.message : '未知错误'}`);
        }
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        logger.info(`\n=== 测试完成 ===`);
        logger.info(`总耗时: ${duration.toFixed(2)} 秒`);
        logger.info(`测试状态: ${result.status === 'completed' ? '✅ 成功' : '❌ 失败'}`);
        
        return result.status === 'completed';
        
    } catch (error) {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        logger.error('\n=== 测试失败 ===');
        logger.error(`错误信息: ${error.message}`);
        logger.error(`总耗时: ${duration.toFixed(2)} 秒`);
        
        return false;
    }
}

// 执行测试
if (require.main === module) {
    runTest()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            logger.error(`测试执行异常: ${error.message}`);
            process.exit(1);
        });
}

module.exports = { runTest };
