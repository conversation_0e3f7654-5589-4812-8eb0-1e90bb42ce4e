# preloadGif()

## 概述

`preloadGif()` 函数用于预加载 GIF 文件，为在 [`<Player>`](/docs/player) 中显示做准备。这是 `@remotion/gif` 包的一部分。

**版本要求**: v3.3.38+

## 语法

```typescript
import { preloadGif } from "@remotion/gif";

const { waitUntilDone, free } = preloadGif(src);
```

## 参数

### src
- **类型**: `string`
- **描述**: 要预加载的 GIF 文件的 URL

## 返回值

返回一个包含两个方法的对象：

- **`waitUntilDone()`**: `() => Promise<void>` - 返回一个 Promise，可以等待预加载完成
- **`free()`**: `() => void` - 取消预加载或释放已预加载 GIF 的内存

## 基础用法

### 1. 基础预加载

```typescript
import { preloadGif } from "@remotion/gif";

const BasicPreload = () => {
  useEffect(() => {
    const { waitUntilDone, free } = preloadGif(
      "https://media.giphy.com/media/xT0GqH01ZyKwd3aT3G/giphy.gif"
    );

    waitUntilDone().then(() => {
      console.log("GIF 预加载完成，现在可以流畅播放！");
    });

    // 组件卸载时清理内存
    return () => {
      free();
    };
  }, []);

  return <div>预加载中...</div>;
};
```

### 2. 本地文件预加载

```typescript
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

const LocalGifPreload = () => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const { waitUntilDone, free } = preloadGif(staticFile("animations/intro.gif"));

    waitUntilDone()
      .then(() => {
        setIsReady(true);
        console.log("本地 GIF 预加载完成");
      })
      .catch((error) => {
        console.error("预加载失败:", error);
      });

    return () => free();
  }, []);

  return (
    <div>
      {isReady ? (
        <p style={{ color: "green" }}>✅ GIF 已准备就绪</p>
      ) : (
        <p>🔄 正在预加载 GIF...</p>
      )}
    </div>
  );
};
```

## 实际应用场景

### 1. 批量预加载管理

```typescript
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

interface PreloadItem {
  src: string;
  name: string;
  status: "pending" | "loading" | "ready" | "error";
}

const BatchGifPreloader = () => {
  const [preloadItems, setPreloadItems] = useState<PreloadItem[]>([
    { src: staticFile("intro.gif"), name: "介绍动画", status: "pending" },
    { src: staticFile("main.gif"), name: "主要内容", status: "pending" },
    { src: staticFile("outro.gif"), name: "结尾动画", status: "pending" }
  ]);

  const [cleanupFunctions, setCleanupFunctions] = useState<(() => void)[]>([]);

  const preloadAll = async () => {
    const newCleanupFunctions: (() => void)[] = [];

    for (let i = 0; i < preloadItems.length; i++) {
      const item = preloadItems[i];
      
      // 更新状态为加载中
      setPreloadItems(prev => prev.map((p, index) => 
        index === i ? { ...p, status: "loading" } : p
      ));

      try {
        const { waitUntilDone, free } = preloadGif(item.src);
        newCleanupFunctions.push(free);

        await waitUntilDone();
        
        // 更新状态为就绪
        setPreloadItems(prev => prev.map((p, index) => 
          index === i ? { ...p, status: "ready" } : p
        ));
      } catch (error) {
        console.error(`预加载 ${item.name} 失败:`, error);
        
        // 更新状态为错误
        setPreloadItems(prev => prev.map((p, index) => 
          index === i ? { ...p, status: "error" } : p
        ));
      }
    }

    setCleanupFunctions(newCleanupFunctions);
  };

  const cleanup = () => {
    cleanupFunctions.forEach(free => free());
    setCleanupFunctions([]);
    setPreloadItems(prev => prev.map(p => ({ ...p, status: "pending" })));
  };

  useEffect(() => {
    return () => cleanup(); // 组件卸载时清理
  }, []);

  const allReady = preloadItems.every(item => item.status === "ready");
  const anyLoading = preloadItems.some(item => item.status === "loading");

  return (
    <div>
      <div style={{ marginBottom: 20 }}>
        <button onClick={preloadAll} disabled={anyLoading}>
          {anyLoading ? "预加载中..." : "开始预加载"}
        </button>
        <button onClick={cleanup} style={{ marginLeft: 10 }}>
          清理内存
        </button>
      </div>

      <div>
        <h3>预加载状态</h3>
        {preloadItems.map((item, index) => (
          <div key={index} style={{ 
            padding: 10, 
            margin: 5, 
            border: "1px solid #ccc",
            borderRadius: 5,
            backgroundColor: item.status === "ready" ? "#e8f5e8" : 
                           item.status === "error" ? "#ffe8e8" : "#f8f8f8"
          }}>
            <strong>{item.name}</strong>
            <span style={{ marginLeft: 10 }}>
              {item.status === "pending" && "⏳ 等待中"}
              {item.status === "loading" && "🔄 加载中"}
              {item.status === "ready" && "✅ 就绪"}
              {item.status === "error" && "❌ 错误"}
            </span>
          </div>
        ))}
      </div>

      {allReady && (
        <div style={{ 
          marginTop: 20, 
          padding: 15, 
          backgroundColor: "#e8f5e8", 
          borderRadius: 5 
        }}>
          <strong>🎉 所有 GIF 预加载完成！现在可以开始播放视频了。</strong>
        </div>
      )}
    </div>
  );
};
```

### 2. 智能预加载策略

```typescript
import { preloadGif } from "@remotion/gif";
import { getGifDurationInSeconds } from "@remotion/gif";
import { staticFile } from "remotion";

const SmartPreloader = () => {
  const [preloadStatus, setPreloadStatus] = useState<{
    [key: string]: {
      duration: number;
      preloaded: boolean;
      priority: "high" | "medium" | "low";
    }
  }>({});

  const smartPreload = async () => {
    const gifConfigs = [
      { src: staticFile("critical-intro.gif"), priority: "high" as const },
      { src: staticFile("main-animation.gif"), priority: "high" as const },
      { src: staticFile("background-effect.gif"), priority: "medium" as const },
      { src: staticFile("optional-outro.gif"), priority: "low" as const }
    ];

    // 首先获取所有 GIF 的时长信息
    const gifInfo = await Promise.all(
      gifConfigs.map(async (config) => {
        try {
          const duration = await getGifDurationInSeconds(config.src);
          return { ...config, duration };
        } catch (error) {
          console.error(`获取 ${config.src} 时长失败:`, error);
          return { ...config, duration: 0 };
        }
      })
    );

    // 按优先级和文件大小排序（时长短的优先）
    const sortedGifs = gifInfo
      .filter(gif => gif.duration > 0)
      .sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.duration - b.duration; // 时长短的优先
      });

    // 逐个预加载
    for (const gif of sortedGifs) {
      if (gif.duration > 5) {
        console.log(`跳过大文件 ${gif.src} (${gif.duration}s)`);
        continue;
      }

      try {
        const { waitUntilDone, free } = preloadGif(gif.src);
        
        setPreloadStatus(prev => ({
          ...prev,
          [gif.src]: {
            duration: gif.duration,
            preloaded: false,
            priority: gif.priority
          }
        }));

        await waitUntilDone();
        
        setPreloadStatus(prev => ({
          ...prev,
          [gif.src]: {
            ...prev[gif.src],
            preloaded: true
          }
        }));

        console.log(`✅ ${gif.src} 预加载完成 (${gif.priority} 优先级)`);
      } catch (error) {
        console.error(`预加载 ${gif.src} 失败:`, error);
      }
    }
  };

  useEffect(() => {
    smartPreload();
  }, []);

  return (
    <div>
      <h3>智能预加载状态</h3>
      {Object.entries(preloadStatus).map(([src, info]) => (
        <div key={src} style={{
          padding: 10,
          margin: 5,
          border: "1px solid #ccc",
          borderRadius: 5,
          backgroundColor: info.preloaded ? "#e8f5e8" : "#fff3cd"
        }}>
          <div><strong>文件:</strong> {src.split('/').pop()}</div>
          <div><strong>时长:</strong> {info.duration.toFixed(2)}s</div>
          <div><strong>优先级:</strong> {info.priority}</div>
          <div><strong>状态:</strong> {info.preloaded ? "✅ 已预加载" : "🔄 预加载中"}</div>
        </div>
      ))}
    </div>
  );
};
```

### 3. 条件性预加载

```typescript
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

const ConditionalPreloader = () => {
  const [userPreferences, setUserPreferences] = useState({
    enableAnimations: true,
    dataUsageMode: "normal" as "low" | "normal" | "high"
  });
  const [preloadedGifs, setPreloadedGifs] = useState<Set<string>>(new Set());

  const conditionalPreload = async () => {
    if (!userPreferences.enableAnimations) {
      console.log("用户禁用了动画，跳过预加载");
      return;
    }

    const gifsByDataUsage = {
      low: [staticFile("essential-only.gif")],
      normal: [
        staticFile("essential-only.gif"),
        staticFile("main-animation.gif")
      ],
      high: [
        staticFile("essential-only.gif"),
        staticFile("main-animation.gif"),
        staticFile("background-effects.gif"),
        staticFile("decorative-elements.gif")
      ]
    };

    const gifsToPreload = gifsByDataUsage[userPreferences.dataUsageMode];
    const cleanupFunctions: (() => void)[] = [];

    for (const gifSrc of gifsToPreload) {
      try {
        const { waitUntilDone, free } = preloadGif(gifSrc);
        cleanupFunctions.push(free);

        await waitUntilDone();
        setPreloadedGifs(prev => new Set([...prev, gifSrc]));
        
        console.log(`✅ 预加载完成: ${gifSrc}`);
      } catch (error) {
        console.error(`预加载失败: ${gifSrc}`, error);
      }
    }

    // 返回清理函数
    return () => {
      cleanupFunctions.forEach(free => free());
      setPreloadedGifs(new Set());
    };
  };

  useEffect(() => {
    const cleanup = conditionalPreload();
    return () => {
      cleanup?.then(cleanupFn => cleanupFn?.());
    };
  }, [userPreferences]);

  return (
    <div>
      <div style={{ marginBottom: 20 }}>
        <h3>用户偏好设置</h3>
        <label>
          <input
            type="checkbox"
            checked={userPreferences.enableAnimations}
            onChange={(e) => setUserPreferences(prev => ({
              ...prev,
              enableAnimations: e.target.checked
            }))}
          />
          启用动画
        </label>
        
        <div style={{ marginTop: 10 }}>
          <label>数据使用模式: </label>
          <select
            value={userPreferences.dataUsageMode}
            onChange={(e) => setUserPreferences(prev => ({
              ...prev,
              dataUsageMode: e.target.value as "low" | "normal" | "high"
            }))}
          >
            <option value="low">低数据使用</option>
            <option value="normal">正常</option>
            <option value="high">高质量</option>
          </select>
        </div>
      </div>

      <div>
        <h3>预加载状态</h3>
        {userPreferences.enableAnimations ? (
          <div>
            <p>数据使用模式: <strong>{userPreferences.dataUsageMode}</strong></p>
            <p>已预加载的 GIF: {preloadedGifs.size} 个</p>
            {Array.from(preloadedGifs).map(gif => (
              <div key={gif} style={{ color: "green" }}>
                ✅ {gif.split('/').pop()}
              </div>
            ))}
          </div>
        ) : (
          <p style={{ color: "gray" }}>动画已禁用，未进行预加载</p>
        )}
      </div>
    </div>
  );
};
```

### 4. 预加载进度监控

```typescript
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

const PreloadProgressMonitor = () => {
  const [progress, setProgress] = useState({
    total: 0,
    completed: 0,
    current: "",
    errors: [] as string[]
  });

  const monitoredPreload = async () => {
    const gifList = [
      "intro-animation.gif",
      "main-content.gif",
      "transition-effect.gif",
      "background-loop.gif",
      "outro-animation.gif"
    ];

    setProgress({
      total: gifList.length,
      completed: 0,
      current: "",
      errors: []
    });

    const cleanupFunctions: (() => void)[] = [];

    for (let i = 0; i < gifList.length; i++) {
      const gifName = gifList[i];
      const gifSrc = staticFile(gifName);

      setProgress(prev => ({
        ...prev,
        current: gifName
      }));

      try {
        const { waitUntilDone, free } = preloadGif(gifSrc);
        cleanupFunctions.push(free);

        await waitUntilDone();
        
        setProgress(prev => ({
          ...prev,
          completed: prev.completed + 1,
          current: prev.completed + 1 === prev.total ? "完成" : prev.current
        }));

      } catch (error) {
        setProgress(prev => ({
          ...prev,
          completed: prev.completed + 1,
          errors: [...prev.errors, `${gifName}: ${error}`]
        }));
      }
    }

    return () => {
      cleanupFunctions.forEach(free => free());
    };
  };

  useEffect(() => {
    const cleanup = monitoredPreload();
    return () => {
      cleanup.then(cleanupFn => cleanupFn?.());
    };
  }, []);

  const progressPercentage = progress.total > 0 ? 
    (progress.completed / progress.total) * 100 : 0;

  return (
    <div>
      <h3>预加载进度</h3>
      
      <div style={{ marginBottom: 20 }}>
        <div style={{
          width: "100%",
          height: 20,
          backgroundColor: "#f0f0f0",
          borderRadius: 10,
          overflow: "hidden"
        }}>
          <div style={{
            width: `${progressPercentage}%`,
            height: "100%",
            backgroundColor: "#4caf50",
            transition: "width 0.3s ease"
          }} />
        </div>
        <p>{progress.completed} / {progress.total} 完成 ({progressPercentage.toFixed(1)}%)</p>
      </div>

      {progress.current && progress.current !== "完成" && (
        <p>当前预加载: <strong>{progress.current}</strong></p>
      )}

      {progress.current === "完成" && (
        <p style={{ color: "green", fontWeight: "bold" }}>
          🎉 所有 GIF 预加载完成！
        </p>
      )}

      {progress.errors.length > 0 && (
        <div style={{ marginTop: 20 }}>
          <h4 style={{ color: "red" }}>预加载错误:</h4>
          {progress.errors.map((error, index) => (
            <p key={index} style={{ color: "red", fontSize: "0.9em" }}>
              ❌ {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};
```

### 5. 内存管理和清理

```typescript
import { preloadGif } from "@remotion/gif";
import { staticFile } from "remotion";

const MemoryManagedPreloader = () => {
  const [memoryUsage, setMemoryUsage] = useState({
    preloadedCount: 0,
    estimatedSize: 0
  });
  
  const [activePreloads, setActivePreloads] = useState<Map<string, () => void>>(new Map());

  const addPreload = async (gifName: string) => {
    const gifSrc = staticFile(gifName);
    
    try {
      const { waitUntilDone, free } = preloadGif(gifSrc);
      
      await waitUntilDone();
      
      // 添加到活跃预加载列表
      setActivePreloads(prev => new Map(prev.set(gifName, free)));
      
      setMemoryUsage(prev => ({
        preloadedCount: prev.preloadedCount + 1,
        estimatedSize: prev.estimatedSize + 2 // 假设每个GIF约2MB
      }));
      
      console.log(`✅ ${gifName} 预加载完成`);
    } catch (error) {
      console.error(`预加载 ${gifName} 失败:`, error);
    }
  };

  const removePreload = (gifName: string) => {
    const freeFunction = activePreloads.get(gifName);
    if (freeFunction) {
      freeFunction();
      
      setActivePreloads(prev => {
        const newMap = new Map(prev);
        newMap.delete(gifName);
        return newMap;
      });
      
      setMemoryUsage(prev => ({
        preloadedCount: Math.max(0, prev.preloadedCount - 1),
        estimatedSize: Math.max(0, prev.estimatedSize - 2)
      }));
      
      console.log(`🗑️ ${gifName} 已从内存中清理`);
    }
  };

  const clearAllPreloads = () => {
    activePreloads.forEach((free, gifName) => {
      free();
      console.log(`🗑️ ${gifName} 已清理`);
    });
    
    setActivePreloads(new Map());
    setMemoryUsage({ preloadedCount: 0, estimatedSize: 0 });
  };

  useEffect(() => {
    return () => {
      // 组件卸载时清理所有预加载
      clearAllPreloads();
    };
  }, []);

  const availableGifs = [
    "intro.gif",
    "main-animation.gif", 
    "background-effect.gif",
    "transition.gif",
    "outro.gif"
  ];

  return (
    <div>
      <div style={{ 
        padding: 15, 
        backgroundColor: "#f8f9fa", 
        borderRadius: 5, 
        marginBottom: 20 
      }}>
        <h3>内存使用情况</h3>
        <p>已预加载: {memoryUsage.preloadedCount} 个 GIF</p>
        <p>估计内存使用: {memoryUsage.estimatedSize} MB</p>
        <button 
          onClick={clearAllPreloads}
          style={{ 
            backgroundColor: "#dc3545", 
            color: "white", 
            border: "none", 
            padding: "8px 16px", 
            borderRadius: 4 
          }}
        >
          清理所有内存
        </button>
      </div>

      <div>
        <h3>GIF 管理</h3>
        {availableGifs.map(gifName => {
          const isPreloaded = activePreloads.has(gifName);
          
          return (
            <div key={gifName} style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: 10,
              margin: 5,
              border: "1px solid #ccc",
              borderRadius: 5,
              backgroundColor: isPreloaded ? "#e8f5e8" : "#ffffff"
            }}>
              <span>
                {isPreloaded ? "✅" : "⏳"} {gifName}
              </span>
              
              <div>
                {!isPreloaded ? (
                  <button 
                    onClick={() => addPreload(gifName)}
                    style={{ 
                      backgroundColor: "#28a745", 
                      color: "white", 
                      border: "none", 
                      padding: "4px 8px", 
                      borderRadius: 3 
                    }}
                  >
                    预加载
                  </button>
                ) : (
                  <button 
                    onClick={() => removePreload(gifName)}
                    style={{ 
                      backgroundColor: "#6c757d", 
                      color: "white", 
                      border: "none", 
                      padding: "4px 8px", 
                      borderRadius: 3 
                    }}
                  >
                    清理
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

## 最佳实践

1. **及时清理**: 总是在组件卸载时调用 `free()` 方法
2. **错误处理**: 使用 try-catch 包装 `waitUntilDone()` 调用
3. **内存管理**: 避免同时预加载过多大文件
4. **优先级策略**: 优先预加载关键和小尺寸的 GIF
5. **用户体验**: 提供预加载进度反馈

## 相关 API

- [`<Gif>`](./Gif.md) - GIF 显示组件
- [`getGifDurationInSeconds()`](./getGifDurationInSeconds.md) - 获取 GIF 时长
- [`staticFile()`](./staticFile.md) - 静态文件引用

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/gif/src/preload-gif.ts)
