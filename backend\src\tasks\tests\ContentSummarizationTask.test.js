/**
 * @功能概述: ContentSummarizationTask 的独立测试脚本。
 *           可以直接通过 `node ContentSummarizationTask.test.js` 执行。
 * @注意事项:
 *   - 测试使用真实的LLM服务，不进行模拟
 *   - 测试文件保存功能和JSON格式验证
 *   - 验证双标题生成功能
 */

const fs = require('fs');
const path = require('path');

const ContentSummarizationTask = require('../ContentSummarizationTask');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const config = require('../../config');

// 测试配置（标准配置）
const TEST_CONFIG = {
    testMode: process.env.TEST_MODE || 'normal',
    timeout: process.env.TEST_MODE === 'quick' ? 30000 : 600000 // 30秒 vs 10分钟
};

// 统一的测试日志前缀
const testLogPrefix = `[文件：ContentSummarizationTask.test.js][ContentSummarizationTask测试]`;

logger.info(`${testLogPrefix} 🧪 开始测试 ContentSummarizationTask`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

// 读取测试用的fullTranscriptText
const fullTranscriptTextPath = path.join(__dirname, '../../../uploads/input/fullTranscriptText');
const testFullTranscriptText = fs.readFileSync(fullTranscriptTextPath, 'utf8').trim();

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 ContentSummarizationTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new ContentSummarizationTask();
        assert(task instanceof ContentSummarizationTask, '任务应为 ContentSummarizationTask 的实例');
        assertEquals(task.name, 'ContentSummarizationTask', '任务名称应为 ContentSummarizationTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
    });

    await runSingleTest('2. 缺少必需字段 - fullTranscriptText', async () => {
        const task = new ContentSummarizationTask();
        const context = { 
            reqId: 'test-missing-field',
            videoIdentifier: 'test-video-001',
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
            // 缺少 fullTranscriptText
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            // 注意：ContentSummarizationTask在参数验证失败时直接抛出错误，不会调用进度回调
            // 这是正常行为，因为任务还没有开始执行就失败了
            logger.info(`${testLogPrefix} 参数验证失败测试通过，任务状态正确设置为FAILED`);
        }
    });

    await runSingleTest('3. 正常执行流程 - 双标题生成测试', async () => {
        const task = new ContentSummarizationTask();
        const context = {
            reqId: 'test-normal-flow',
            videoIdentifier: 'test-video-002',
            fullTranscriptText: testFullTranscriptText,
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
        };
        const progressLogs = [];

        logger.info(`${testLogPrefix} 开始执行正常流程测试，输入文本长度: ${testFullTranscriptText.length} 字符`);

        try {
            const result = await task.execute(context, (data) => {
                logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
                progressLogs.push(data);
            });

            // 验证任务执行结果
            assert(result, '任务执行应返回结果');
            assertEquals(result.summaryStatus, 'success', '总结状态应为成功');
            assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
            assert(task.result, '任务应保存执行结果');

        // 验证双标题格式
        assert(result.transcriptSummary, '应包含transcriptSummary字段');
        assertIncludes(result.transcriptSummary, ' | ', 'summary字段应包含双标题分隔符');
        
        const [englishTitle, newsTitle] = result.transcriptSummary.split(' | ');
        assert(englishTitle && englishTitle.trim().length > 0, '英语教学标题不应为空');
        assert(newsTitle && newsTitle.trim().length > 0, '新闻类标题不应为空');
        
        logger.info(`${testLogPrefix} 双标题验证成功:`);
        logger.info(`${testLogPrefix} - 英语教学标题: ${englishTitle}`);
        logger.info(`${testLogPrefix} - 新闻类标题: ${newsTitle}`);

        // 验证进度回调
        const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
        const hasCompletedProgress = progressLogs.some(p => p.status === TASK_STATUS.COMPLETED);
        assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');
        assert(hasCompletedProgress, '应记录 COMPLETED 状态的进度回调');

            // 验证文件保存
            assert(result.summaryJsonPath, '应包含保存的JSON文件路径');
            assert(fs.existsSync(result.summaryJsonPath), '生成的JSON文件应存在');

            const savedJson = JSON.parse(fs.readFileSync(result.summaryJsonPath, 'utf8'));
            assert(savedJson.summary, '保存的JSON应包含summary字段');
            assertIncludes(savedJson.summary, ' | ', '保存的summary应包含双标题分隔符');

            logger.info(`${testLogPrefix} JSON文件验证成功: ${result.summaryJsonPath}`);

        } catch (error) {
            // 如果是LLM API错误，这是可以接受的（可能是配置或网络问题）
            if (error.message && error.message.includes('LLM API 错误')) {
                logger.warn(`${testLogPrefix} LLM API调用失败，这可能是配置或网络问题: ${error.message}`);
                assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');

                // 验证进度回调至少记录了开始状态
                const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
                assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');

                logger.info(`${testLogPrefix} LLM API错误测试通过，任务正确处理了API失败情况`);
                return; // 测试通过，提前返回
            } else {
                // 其他错误重新抛出
                throw error;
            }
        }
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== ContentSummarizationTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0);
    }
}

// 超时处理
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
