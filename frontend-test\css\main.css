/* 
  @功能概述: 页面的全局样式和布局定义。
  包含基础的WordPress Admin风格化调整。
*/
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    background-color: #f4f6f9; /* WordPress风格背景: 模拟WordPress后台的背景颜色 */
}
#app {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 使应用容器占满整个视口高度 */
}
.el-header {
    background-color: #2c3e50; /* 深色头部: 深色头部背景 */
    color: white;
    line-height: 60px;
    text-align: center;
    border-bottom: 3px solid #1abc9c; /* 强调色: 强调色底部边框 */
}
.el-main {
    padding: 20px; /* 主内容区域内边距 */
}
.navigation-buttons {
    margin-bottom: 20px; /* 导航按钮组底部外边距 */
}
.content-section {
    padding: 20px; /* 内容区域内边距 */
    border: 1px solid #dcdfe6; /* 内容区域边框 */
    background-color: #fff; /* 内容区域背景色 */
    min-height: 200px; /* 确保内容区域可见: 确保内容区有最小高度，便于查看 */
}
.video-editor-section .video-container-16-9 {
    position: relative;
    width: 960px; /* ADDED - Fixed width */
    height: 540px; /* ADDED - Fixed height (16:9 for 960px width) */
    overflow: hidden;
    max-width: 100%; /* Still allow shrinking on smaller viewports */
    background: #000;
    margin: 0 auto 20px auto; /* 水平居中并添加底部外边距 */
    border: 1px solid #ccc;
}
.video-editor-section .video-container-16-9 video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain; /* ADDED - Video self-adaptation */
}
/* .video-editor-section .subtitle-controls { // 已移除，注释保留作为参考
    text-align: center;
    margin-bottom: 10px;
} */
.video-editor-section .subtitle-display-area {
    padding: 15px;
    border: 1px solid #dcdfe6;
    background-color: #f9f9f9;
    min-height: 80px; /* 为两行字幕增加最小高度 */
    max-height: 180px; /* 增加最大高度 */
    overflow-y: auto;
    text-align: center;
    font-size: 1.2em; /* 字号稍大 */
    line-height: 1.6;
    white-space: pre-wrap;
    margin: 0 auto 20px auto; /* 水平居中并添加底部外边距 */
    max-width: 90%; /* 与视频宽度类似 */
}
.video-editor-section .subtitle-display-area.video-overlay {
    position: absolute;
    bottom: 15px; /* Or use percentage like 5% */
    left: 50%;
    transform: translateX(-50%);
    width: 90%; /* Adjust as needed */
    background-color: transparent; /* User request: transparent background */
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7), 0 0 5px rgba(0,0,0,0.5); /* Enhanced text shadow for readability */
    padding: 8px 12px;
    border-radius: 6px;
    z-index: 10;
    pointer-events: none; /* Allow clicks to pass through to video player controls */
    /* Resetting properties from non-overlay style */
    border: none;
    margin: 0;
    min-height: auto; /* Let it collapse if no text */
    /* max-height and overflow-y can be kept if long subtitles are a concern */
    /* text-align, font-size, line-height, white-space are likely fine */
}
.video-editor-section .subtitle-display-area .subtitle-line {
    display: block; /* 每条字幕占一行 */
    min-height: 1.6em; /* 即使为空也确保空间 */
}
 /* 上传状态区域样式 */
#uploadStatus {
    white-space: pre-wrap;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    padding: 10px;
    min-height: 100px;
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

/* 流水线状态区域样式 */
#pipelineStatus {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 20px;
}

/* 状态区块样式 */
.status-section {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.status-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.status-section h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 13px;
    font-weight: bold;
}

/* 状态消息样式 */
.status-message {
    margin: 3px 0;
    padding: 2px 0;
    color: #212529;
}

.status-message.waiting {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

/* 流水线状态区域特殊样式 */
.pipeline-status-section {
    border-left: 4px solid #1abc9c;
    background-color: #f8f9fa;
}

.pipeline-status-section h3 {
    color: #2c3e50;
    margin-top: 0;
}

/* SSE事件流样式 */
.sse-events-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #ffffff;
}

.sse-event-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.sse-event-item:last-child {
    border-bottom: none;
}

.sse-event-item:hover {
    background-color: #f8f9fa;
}

.sse-event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.sse-event-type {
    font-weight: bold;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    background-color: #6c757d;
}

.sse-event-time {
    font-size: 10px;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.sse-event-content {
    font-size: 12px;
    color: #212529;
    margin-bottom: 4px;
}

.sse-event-details {
    font-size: 11px;
    color: #6c757d;
    padding-left: 12px;
    border-left: 2px solid #e9ecef;
}

.sse-detail-item {
    margin: 2px 0;
}

/* 不同事件类型的颜色 */
.sse-pipeline-progress .sse-event-type {
    background-color: #007bff;
}

.sse-controller-status .sse-event-type {
    background-color: #28a745;
}

.sse-heartbeat .sse-event-type {
    background-color: #dc3545;
}

.sse-system-status .sse-event-type {
    background-color: #ffc107;
    color: #212529;
}

.sse-unknown .sse-event-type {
    background-color: #6f42c1;
}

/* Cropper.js 样式覆盖 - 确保在最上层 */
.cropper-container {
    z-index: 9999 !important;
}

.cropper-wrap-box {
    z-index: 9999 !important;
}

.cropper-canvas {
    z-index: 9999 !important;
}

.cropper-drag-box {
    z-index: 10000 !important;
}

.cropper-crop-box {
    z-index: 10001 !important;
}

.cropper-view-box {
    z-index: 10002 !important;
}

.cropper-face {
    z-index: 10003 !important;
}

.cropper-line {
    z-index: 10004 !important;
}

.cropper-point {
    z-index: 10005 !important;
}

/* ============================================================================
   控制台风格样式 - CMD CLI风格的SSE事件展示
   ============================================================================ */

/* 控制台容器 */
.pipeline-console-section {
    border-left: 4px solid #00ff00;
    background-color: #000000;
    color: #00ff00;
    font-family: 'Courier New', 'Consolas', monospace;
    margin-top: 30px;
    margin-bottom: 30px;
    border: 2px solid #00ff00;
    box-shadow: 0 4px 8px rgba(0, 255, 0, 0.3);
}

.pipeline-console-section h3 {
    color: #00ff00;
    margin-top: 0;
    font-family: 'Courier New', 'Consolas', monospace;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 0 10px #00ff00;
    padding: 10px;
    background-color: #001100;
    border-bottom: 1px solid #00ff00;
}

.console-container {
    background-color: #000000;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 15px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Courier New', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* 控制台头部 */
.console-header {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #333333;
}

.console-prompt {
    color: #00ff00;
    font-weight: bold;
}

.console-command {
    color: #ffffff;
    margin-left: 5px;
}

/* 控制台输出区域 */
.console-output {
    min-height: 200px;
}

/* 控制台行样式 */
.console-line {
    margin: 2px 0;
    padding: 1px 0;
    display: flex;
    align-items: flex-start;
    word-wrap: break-word;
}

.console-timestamp {
    color: #888888;
    margin-right: 8px;
    flex-shrink: 0;
    font-size: 11px;
}

.console-level {
    margin-right: 8px;
    flex-shrink: 0;
    font-weight: bold;
    min-width: 60px;
}

.console-message {
    color: #ffffff;
    flex: 1;
    word-break: break-word;
}

/* 不同级别的颜色 */
.console-info .console-level {
    color: #00ff00;
}

.console-success .console-level {
    color: #00ff00;
}

.console-warn .console-level {
    color: #ffff00;
}

.console-error .console-level {
    color: #ff0000;
}

.console-waiting {
    color: #888888;
    font-style: italic;
    justify-content: center;
    padding: 20px 0;
}

/* 控制台光标 */
.console-cursor {
    margin-top: 10px;
    padding-top: 5px;
    border-top: 1px solid #333333;
    display: flex;
    align-items: center;
}

.cursor-blink {
    color: #00ff00;
    animation: blink 1s infinite;
    margin-left: 5px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 滚动条样式 */
.console-container::-webkit-scrollbar {
    width: 8px;
}

.console-container::-webkit-scrollbar-track {
    background: #222222;
}

.console-container::-webkit-scrollbar-thumb {
    background: #555555;
    border-radius: 4px;
}

.console-container::-webkit-scrollbar-thumb:hover {
    background: #777777;
}

/* ============================================================================
   下载链接区域样式
   ============================================================================ */

/* 下载区域容器 */
.download-section {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    border: 1px solid #e1e8ed;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-title {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.download-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.download-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.download-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.download-icon {
    font-size: 24px;
    width: 32px;
    text-align: center;
}

.download-label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 120px;
}

.download-path {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #7f8c8d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.download-button {
    text-decoration: none;
}

.download-button .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .download-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .download-info {
        justify-content: center;
        text-align: center;
    }

    .download-path {
        max-width: 100%;
    }
}

/* ============================================================================
   项目选择功能样式
   ============================================================================ */

/* 加载状态容器 */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px;
    color: #666;
    font-size: 14px;
}

.loading-container .el-icon {
    font-size: 20px;
}

/* 错误状态容器 */
.error-container {
    padding: 20px;
    text-align: center;
}

/* 空状态容器 */
.empty-container {
    padding: 40px;
    text-align: center;
}

/* 项目列表容器 */
.project-list-container {
    padding: 20px 0;
}

/* 项目网格布局 */
.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

/* 项目卡片 */
.project-card {
    transition: all 0.3s ease;
    height: 100%;
}

.project-card.selected {
    transform: translateY(-2px);
}

.project-card-inner {
    height: 100%;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.project-card.selected .project-card-inner {
    border: 2px solid #409eff;
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.25);
}

.project-card-inner:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

/* 确保项目信息区域占据剩余空间 */
.project-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.project-meta-grid {
    margin-top: auto; /* 将元信息推到底部 */
}

/* 项目状态标识 */
.project-status {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
}

/* 项目信息 */
.project-info {
    padding: 20px 20px 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.project-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 44px; /* 确保两行文本的最小高度 */
}

/* 项目元信息网格布局 */
.project-meta-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 16px;
    font-size: 13px;
}

.project-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    line-height: 1.4;
}

.project-meta-item i {
    color: #909399;
    font-size: 14px;
    width: 14px;
    text-align: center;
    flex-shrink: 0;
}

/* 项目操作按钮 */
.project-actions {
    padding: 16px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.primary-action {
    width: 100%;
}

.primary-select-btn {
    width: 100%;
    font-weight: 500;
    padding: 12px 16px;
}

.secondary-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    width: 100%;
}

.secondary-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    white-space: nowrap;
    flex: 1;
    justify-content: center;
    min-width: 0;
}

.secondary-button i {
    font-size: 14px;
}

/* 保持向后兼容的图标按钮样式 */
.icon-button {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.icon-button i {
    font-size: 16px;
}

/* 分页容器 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

/* 确认选择面板 */
.confirmation-panel {
    margin-top: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.confirmation-panel .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-project-info {
    margin-bottom: 20px;
}

.selected-project-info h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 18px;
}

.selected-project-info p {
    margin: 8px 0;
    color: #666;
    font-size: 14px;
}

.confirmation-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .project-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .project-title {
        max-width: 200px;
    }

    .project-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .confirmation-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .project-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .project-info {
        padding: 16px 16px 12px 16px;
    }

    .project-actions {
        padding: 12px 16px 16px 16px;
        flex-direction: column;
        gap: 10px;
    }

    .primary-action {
        width: 100%;
    }

    .secondary-actions {
        width: 100%;
        justify-content: space-between;
        gap: 6px;
    }

    .secondary-button {
        font-size: 12px;
        padding: 6px 8px;
    }

    .project-title {
        font-size: 14px;
        min-height: 38px;
        -webkit-line-clamp: 2;
    }

    .project-meta-grid {
        grid-template-columns: 1fr;
        gap: 8px;
        font-size: 12px;
    }

    .icon-button {
        width: 32px;
        height: 32px;
    }

    .icon-button i {
        font-size: 14px;
    }
}

/* ============================================================================
   编辑状态容器样式 - 退出编辑功能
   ============================================================================ */

/* 编辑状态容器样式 */
.editing-status-container {
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

/* 退出编辑按钮特殊样式 */
.editing-status-container .el-button--danger {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.editing-status-container .el-button--danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.3);
}

.editing-status-container .el-button--danger:active {
    transform: translateY(0);
}

/* 退出编辑按钮加载状态 */
.editing-status-container .el-button--danger.is-loading {
    pointer-events: none;
}

/* ============================================================================ */
/* 生成视频查看对话框样式 */
/* ============================================================================ */

/* 视频列表容器 */
.video-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 8px; /* 为滚动条留出空间 */
}

/* 单个视频项 */
.video-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.video-item:hover {
    background-color: #f5f7fa;
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

/* 复选框样式 */
.video-item .el-checkbox {
    margin-right: 16px;
    margin-top: 4px;
    flex-shrink: 0;
}

/* 视频信息区域 */
.video-info {
    flex: 1;
    min-width: 0; /* 防止flex项目溢出 */
}

.video-info h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    word-break: break-word; /* 长标题换行 */
}

/* 视频详情行 */
.video-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #606266;
    gap: 6px;
}

/* 视频标签信息容器 */
.video-tags-info {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

/* 字幕信息 */
.subtitle-info .el-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* 发布状态信息 */
.status-info .el-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* 对话框自定义样式 */
.el-dialog__header {
    padding: 20px 20px 10px 20px;
    border-bottom: 1px solid #e4e7ed;
}

.el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.el-dialog__footer {
    padding: 10px 20px 20px 20px;
    border-top: 1px solid #e4e7ed;
}

/* 加载状态样式 */
.loading-spinner {
    animation: rotating 2s linear infinite;
    color: #409eff;
    display: inline-block;
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .editing-status-container {
        flex-direction: column;
        gap: 15px;
    }

    .editing-status-container .el-button {
        width: 100%;
        max-width: 200px;
    }

    /* 生成视频对话框响应式 */
    .el-dialog {
        width: 95% !important;
        margin: 0 auto;
    }

    .video-details {
        flex-direction: column;
        gap: 8px;
    }

    .video-item {
        padding: 12px;
    }

    .video-info h4 {
        font-size: 14px;
    }

    .detail-item {
        font-size: 12px;
    }
}

/* ============================================================================
   项目选择对话框样式
   ============================================================================ */

/* 项目选择对话框样式 */
.project-selection-content {
    padding: 20px 0;
}

.selected-project-info h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    word-break: break-all;
}

.project-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.detail-row .value {
    color: #6c757d;
    text-align: right;
    word-break: break-all;
}

.confirmation-message {
    margin-top: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}