---
type: "manual"
description: "globs:"
---
# 项目文件结构与特定目录说明

本文档概述了当前 Express.js 视频处理项目的主要文件和目录结构，并特别说明了几个关键目录的用途，以帮助 AI 理解代码库和开发流程。

## 项目概述

这是一个基于 Express.js 的视频处理和字幕生成系统，采用流水线架构模式，支持视频上传、语音转录、字幕生成、视频合成等功能。

## 核心技术栈

- **后端框架**: Express.js 4.17.1
- **视频处理**: FFmpeg (fluent-ffmpeg 2.1.2)
- **语音识别**: Microsoft Cognitive Services Speech SDK 1.43.1
- **浏览器自动化**: Puppeteer 24.14.0, Playwright 1.54.1
- **图像处理**: Canvas 3.1.0
- **字幕处理**: media-captions 0.0.18, subtitle 4.2.2
- **日志系统**: Winston 3.17.0
- **前端测试**: Vue.js 3 + Element Plus

## 架构分层说明

项目采用清晰的分层架构，各层职责明确：

1. **Controllers** (`controllers/`) - 接口控制层，处理 HTTP 请求和响应
2. **Pipelines** (`pipelines/`) - 流水线编排层，组合和编排多个 tasks，管理复杂业务流程
3. **Services** (`services/`) - 服务支撑层，为 pipelines 和 tasks 提供基础服务和业务逻辑
4. **Tasks** (`tasks/`) - 任务执行层，最小的业务逻辑执行单元，继承自 TaskBase
5. **Utils** (`utils/`) - 工具函数层，提供通用的辅助功能和工具类
6. **Templates** (`templates/`) - 模板层，存放视频生成模板和配置
7. **Frontend-Test** (`frontend-test/`) - 前端测试层，用于测试后端接口的Vue.js应用

## 项目根目录 (`express/`)

*   `/`: 项目的根目录，包含顶级配置文件和目录 (`@C:/Users/<USER>/Desktop/codebase/express`)。
    *   `.augment/`: Augment AI 相关配置和规则。
        *   `rules/`: Augment 规则文件目录，包含项目的所有开发规则文件。
            *   `@.augment/rules/API_CREATION_GUIDELINES.md`: API创建开发指导原则。
            *   `@.augment/rules/code-commenting-standard.md`: 代码注释标准规则。
            *   `@.augment/rules/file_structure.md`: 项目文件结构说明规则（本文件）。
            *   `@.augment/rules/pipeline_architecture.md`: 流水线架构实现规则。
            *   `@.augment/rules/task_development_standard.md`: 任务开发标准规则。
            *   `@.augment/rules/sse_event_standard.md`: SSE 事件数据结构标准。
            *   `@.augment/rules/llm_api_standard.md`: LLM API调用标准。
            *   `@.augment/rules/frontend_development_standard.md`: 前端开发标准。
            *   `@.augment/rules/index.md`: 项目规则索引。
        *   `@.augment/mcp.json`: MCP 工具的配置文件。
    *   `docs/`: 项目文档和记录。
        *   `rules/`: 项目规则文档目录（历史文档）。
        *   `@docs/TASK.md`: 详细的任务分解和待办事项清单。
        *   `@docs/服务器部署操作记录.md`: 服务器部署的操作步骤记录。
        *   `@docs/服务器信息_0602.md`: 服务器配置信息文档。
        *   `@docs/node_express.conf`: Nginx 配置文件示例。
        *   `@docs/res.json`: 示例响应数据文件，用于测试脚本模拟数据。
        *   `@docs/ass 字幕操作规范.md`: ASS字幕格式操作规范。
        *   `@docs/中英字幕呈现方案（分别高亮）.md`: 双语字幕显示方案。
        *   `@docs/interact-mcp部署记录.md`: MCP工具部署记录。
    *   `frontend-test/`: 前端测试环境，用于测试后端接口的Vue.js应用 (`@frontend-test/`)。
        *   `@frontend-test/index.html`: 主页面文件，引入Vue 3和Element Plus CDN。
        *   `@frontend-test/README.md`: 前端测试项目说明文档。
        *   `js/`: JavaScript文件目录。
            *   `@frontend-test/js/app.js`: Vue应用主文件，包含组件和业务逻辑。
        *   `css/`: CSS样式文件目录。
        *   `core/`: 核心功能模块目录。
    *   `backend/`: Express.js 后端应用代码。
    *   `node_modules/`: Node.js 依赖模块目录（根目录级别）。
    *   `@package.json`: 项目根目录的依赖和脚本定义文件。
    *   `@package-lock.json`: 项目根目录的依赖锁定文件。
    *   `@pnpm-lock.yaml`: pnpm 依赖锁定文件。
    *   `@README.md`: 项目说明文档。

## `frontend-test/` 目录详解

*   `frontend-test/`: 前端测试环境，使用Vue 3 + Element Plus技术栈 (`@frontend-test/`)。
    *   `@frontend-test/index.html`: 主页面文件，包含：
        *   Vue 3 CDN引入
        *   Element Plus CDN引入
        *   Element Plus Icons CDN引入
        *   基础HTML结构和Vue应用挂载点
    *   `@frontend-test/README.md`: 前端测试项目说明文档，包含：
        *   技术栈说明
        *   运行方式
        *   WordPress迁移说明
    *   `js/`: JavaScript源码目录。
        *   `@frontend-test/js/app.js`: Vue应用主文件，包含：
            *   Vue 3应用创建和配置
            *   Element Plus组件注册
            *   业务逻辑和组件定义

## `backend/` 目录详解

*   `backend/`: 存放所有 Express.js 后端相关的代码 (`@backend`)。
    *   `docs/`: 后端项目文档目录 (`@backend/docs`)。
        *   `api/`: API接口文档目录。
            *   `@backend/docs/api/视频上传处理接口文档.md`: 视频上传处理接口说明。
            *   `@backend/docs/api/项目管理接口文档.md`: 项目管理接口说明。
        *   `@backend/docs/n8n视频生成工作流设计方案.md`: n8n视频生成工作流设计文档。
        *   `@backend/docs/Web技术驱动的视频美化方案.md`: 视频美化技术方案。
        *   `@backend/docs/editly-deployment-status.md`: Editly部署状态报告。
        *   `@backend/docs/llm-service-enhancement-summary.md`: LLM服务增强总结。
        *   `@backend/docs/文件目录架构重构方案.md`: 架构重构方案文档。
    *   `logs/`: 后端应用日志文件存放目录 (`@backend/logs`)。
        *   `@backend/logs/app.log`: 后端主日志文件。
    *   `node_modules/`: 后端项目的Node.js依赖模块目录。
    *   `@backend/package.json`: Node.js 后端项目的依赖和脚本定义文件。
    *   `@backend/package-lock.json`: 锁定后端项目依赖版本。
    *   `@backend/nodemon.json`: nodemon 配置文件。
    *   `uploads/`: 上传文件和生成文件存储目录 (`@backend/uploads`)。
        *   `input/`: 输入文件目录（上传的原始文件）。
        *   `output/`: 输出文件目录（处理后的文件）。
        *   `temp/`: 临时文件目录。
    *   `src/`: 存放实际的后端源代码 (`@backend/src`)。
        *   `@backend/src/app.js`: Express 应用的入口文件，设置中间件、路由等。
        *   `assets/`: 存放静态资源文件。
            *   `@backend/src/assets/abstract_9_16.png`: 抽象背景图片。
            *   `@backend/src/assets/newspaper.jpeg`: 报纸背景图片。
            *   `@backend/src/assets/newspaper_9_16.png`: 9:16比例报纸背景。
            *   `test_outputs/`: 测试输出文件目录。
        *   `examples/`: 存放示例代码。
            *   `@backend/src/examples/videoCompositionConfigExample.js`: 视频合成配置示例。
        *   `class/`: 存放基类定义。
            *   `@backend/src/class/PipelineBase.js`: 流水线基类，提供任务编排和执行框架。
            *   `@backend/src/class/TaskBase.js`: 任务基类，定义任务执行接口和生命周期。
        *   `config/`: 应用配置：集中管理配置信息。
            *   `@backend/src/config/index.js`: 主配置文件，加载并提供配置。
            *   `@backend/src/config/template-config.js`: 模板配置文件。
            *   `video/`: 视频相关配置目录。
        *   `constants/`: 常量定义：集中管理项目常量。
            *   `@backend/src/constants/progress.js`: 进度监控相关常量和SSE事件标准。
        *   `controllers/`: 控制器层：处理传入请求，遵循"一个接口一个Controller"原则，按功能分类组织。
            *   `video/`: 视频相关功能的控制器。
            *   `download/`: 下载相关功能的控制器。
        *   `middleware/`: Express 中间件：处理请求的预处理和后处理。
            *   `@backend/src/middleware/uploadMiddleware.js`: 配置和使用 multer 处理文件上传。
            *   `@backend/src/middleware/loggingMiddleware.js`: 日志记录中间件。
        *   `prompts/`: 提示词模板文件：存储所有用于 LLM 交互的提示词模板。
            *   `default/`: 默认通用提示词模板目录。
                *   `@backend/src/prompts/default/default.md`: 默认提示词模板。
            *   `translate/`: 文本翻译相关提示词模板目录。
                *   `@backend/src/prompts/translate/default.md`: 默认翻译提示词模板。
            *   `TRANSLATE_SUBTITLE/`: 字幕翻译相关提示词模板目录。
                *   `@backend/src/prompts/TRANSLATE_SUBTITLE/default.md`: 默认字幕翻译提示词模板。
            *   `CORRECT_TRANSCRIPTION/`: 转录修正相关提示词模板目录。
                *   `@backend/src/prompts/CORRECT_TRANSCRIPTION/default.md`: 默认转录修正提示词模板。
            *   `BILINGUAL_SUBTITLE_ENHANCE/`: 双语字幕增强提示词模板目录。
            *   `CONTENT_SUMMARIZATION/`: 内容摘要提示词模板目录。
            *   `SUBTITLE_CLOZE_UTILS/`: 字幕填空工具提示词模板目录。
        *   `routes/`: 路由定义：定义 API 端点，并将请求导向相应的控制器方法。
            *   `@backend/src/routes/videoRoutes.js`: 定义视频相关的 API 路由。
        *   `pipelines/`: 流水线编排层：组合和编排多个任务，管理任务执行流程和进度回调。
            *   `tests/`: 流水线测试文件目录。
            *   `@backend/src/pipelines/videoProcessingPipelineService.js`: 视频处理流水线服务。
            *   `@backend/src/pipelines/videoGenerationPipelineService.js`: 视频生成流水线服务。
            *   `@backend/src/pipelines/AssVideoGenerationPipelineService.js`: ASS字幕视频生成流水线。
            *   `@backend/src/pipelines/TestProcessingPipelineService.js`: 测试处理流水线。
            *   `@backend/src/pipelines/TestSubtitleGenerationPipelineService.js`: 测试字幕生成流水线。
            *   `@backend/src/pipelines/TestCloudflareTranscriptionOptimizationPipelineService.js`: Cloudflare转录优化测试流水线。
        *   `services/`: 服务支撑层：为流水线和任务提供基础服务和业务逻辑。
            *   `@backend/src/services/llmService.js`: 大型语言模型服务接口。
            *   `@backend/src/services/promptTemplates.js`: 提示词模板管理和加载服务。
            *   `@backend/src/services/subtitleEnhancer.js`: 字幕增强服务。
        *   `tasks/`: 任务层：包含具体的、可重用的业务逻辑单元，继承自 `TaskBase`。
            *   `@backend/src/tasks/convertToAudioTask.js`: 视频转音频任务。
            *   `@backend/src/tasks/GetTranscriptionTask.js`: 获取语音转写任务。
            *   `@backend/src/tasks/GetTranscriptionTaskByCloudflare.js`: Cloudflare语音转写任务。
            *   `@backend/src/tasks/TranscriptionCorrectionTask.js`: 转录修正任务。
            *   `@backend/src/tasks/TranslateSubtitleTask.js`: 字幕翻译任务。
            *   `@backend/src/tasks/BilingualSubtitleMergeTask.js`: 双语字幕合并任务。
            *   `@backend/src/tasks/GenerateASSTask.js`: ASS字幕生成任务。
            *   `@backend/src/tasks/GenerateVideoTask.js`: 视频生成任务。
            *   `@backend/src/tasks/VideoCompositionTask.js`: 视频合成任务。
            *   `@backend/src/tasks/VideoClipAndCropTask.js`: 视频剪辑和裁剪任务。
            *   `@backend/src/tasks/ClipMediaTask.js`: 媒体剪辑任务。
            *   `@backend/src/tasks/PuppeteerRecorderTask.js`: Puppeteer录制任务。
            *   `@backend/src/tasks/ContentSummarizationTask.js`: 内容摘要任务。
            *   `@backend/src/tasks/SubtitleClozeTask.js`: 字幕填空任务。
            *   `@backend/src/tasks/SubtitleOptimizationTask.js`: 字幕优化任务。
            *   `@backend/src/tasks/ConvertToAudioForCloudflareTask.js`: Cloudflare音频转换任务。
            *   `@backend/src/tasks/index.js`: 导出任务模块。
            *   `tests/`: 任务测试文件目录。
            *   `docs/`: 任务文档目录。
        *   `templates/`: 模板层：存放视频生成模板和配置。
            *   `modern/`: 现代风格模板目录。
        *   `tests/`: 测试文件目录：包含各种测试脚本和测试数据。
            *   `@backend/src/tests/test-all-projects.js`: 全项目测试脚本。
            *   `@backend/src/tests/test-frontend-integration.js`: 前端集成测试。
            *   `@backend/src/tests/test-new-architecture.js`: 新架构测试。
            *   `@backend/src/tests/test-project-apis.js`: 项目API测试。
            *   `@backend/src/tests/create-test-projects.js`: 创建测试项目脚本。
            *   `temp/`: 临时测试文件目录。
        *   `utils/`: 工具函数和模块：可重用的辅助代码。
            *   `@backend/src/utils/logger.js`: 配置 Winston 日志系统。
            *   `@backend/src/utils/fileSaver.js`: 文件保存工具。
            *   `@backend/src/utils/subtitleProcessor.js`: 字幕处理工具。
            *   `@backend/src/utils/subtitleMerger.js`: 字幕合并工具。
            *   `@backend/src/utils/pipelineResultStandardizer.js`: 流水线结果标准化工具。
            *   `@backend/src/utils/FileInfoExtractor.js`: 文件信息提取器。
            *   `@backend/src/utils/GeneratedFileIdentifier.js`: 生成文件标识器。
            *   `@backend/src/utils/backgroundVideoGenerator.js`: 背景视频生成器。
            *   `@backend/src/utils/progressBarVideoGenerator.js`: 进度条视频生成器。
            *   `@backend/src/utils/compositionConfigConverter.js`: 合成配置转换器。
            *   `@backend/src/utils/fileNameUtils.js`: 文件名工具。
            *   `@backend/src/utils/pathHelper.js`: 路径辅助工具。
            *   `@backend/src/utils/jsonValidator.js`: JSON验证器。
            *   `@backend/src/utils/srtValidator.js`: SRT验证器。
            *   `@backend/src/utils/videoUtils.js`: 视频处理工具。
            *   `video/`: 视频相关工具目录。

## 关键文件作用说明

*   `backend/.env`: **后端**环境变量配置文件 (未列出，但应存在于 `backend/` 目录，用于存放敏感信息)。
*   `@backend/src/app.js`: 后端应用的启动文件，配置中间件、路由和服务器监听。
*   `@backend/src/class/PipelineBase.js`: 流水线基础架构，提供任务编排和执行流程管理。
*   `@backend/src/class/TaskBase.js`: 任务基类，定义了任务执行的接口和生命周期。
*   `@backend/src/config/index.js`: 配置管理模块，负责加载和提供项目配置。
*   `@backend/src/constants/progress.js`: 进度监控常量定义，包含SSE事件标准和任务状态枚举。
*   `@backend/src/pipelines/`: 流水线编排目录，组合和编排多个任务的执行流程。
*   `@backend/src/pipelines/tests/`: 流水线测试目录，包含流水线集成测试。
*   `@backend/src/prompts/`: 提示词模板目录，按照任务类型组织的 `.md` 格式模板文件。
*   `@backend/src/services/promptTemplates.js`: 提示词模板服务，负责动态加载、缓存和管理提示词模板。
*   `@backend/src/tasks/`: 任务执行层目录，包含所有具体的业务逻辑执行单元。
*   `@backend/src/tasks/tests/`: 任务测试目录，包含任务单元测试和功能测试。
*   `@backend/src/templates/`: 模板目录，存放视频生成模板和配置文件。
*   `@backend/src/tests/`: 综合测试目录，包含各种集成测试和功能测试脚本。
*   `@backend/src/utils/`: 工具函数目录，提供各种可重用的辅助功能。
*   `@backend/src/utils/logger.js`: 日志工具，配置 Winston 日志系统。
*   `@backend/src/utils/pipelineResultStandardizer.js`: 流水线结果标准化工具，统一返回格式。
*   `@backend/logs/app.log`: **后端**应用的主要日志文件。
*   `@backend/uploads/`: **后端**用于存储上传的原始文件及处理过程中生成的文件的目录。
*   `@backend/docs/`: **后端**项目文档目录，包含API文档和技术方案。
*   `@frontend-test/index.html`: **前端测试**主页面，用于测试后端接口的Vue.js应用。
*   `@frontend-test/js/app.js`: **前端测试**Vue应用主文件，包含组件和业务逻辑。
*   `@docs/TASK.md`: 任务分解文档，包含项目待办事项和任务进度。
*   `@docs/服务器部署操作记录.md`: 服务器部署过程和配置记录。
*   `@docs/node_express.conf`: Nginx 配置文件示例。
*   `@.augment/rules/`: Augment AI 开发规则目录，包含所有开发标准和规范。

## AI 交互注意事项

*   查找流水线定义和业务流程编排: 查看 `@backend/src/pipelines/`。
*   查找基础和业务服务: 查看 `@backend/src/services/`。
*   查找具体的业务逻辑单元: 查看 `@backend/src/tasks/`。
*   查找流水线和任务的基类定义: 查看 `@backend/src/class/`。
*   查找 API 端点定义: 查看 `@backend/src/routes/`。
*   查找请求处理逻辑 (控制器): 查看 `@backend/src/controllers/`，注意控制器是按功能分类存放在不同的子目录中。
*   查找文件上传配置: 查看 `@backend/src/middleware/uploadMiddleware.js`。
*   查找提示词模板: 查看 `@backend/src/prompts/`，模板按任务类型分目录存储。
*   查找提示词管理: 查看 `@backend/src/services/promptTemplates.js`，负责动态加载提示词模板文件。
*   查找任务测试: 查看 `@backend/src/tasks/tests/`，包含任务和功能单元测试。
*   查找综合测试: 查看 `@backend/src/tests/`，包含各种集成测试和功能测试脚本。
*   查找视频生成模板: 查看 `@backend/src/templates/`，包含视频生成模板和配置。
*   查找工具函数: 查看 `@backend/src/utils/`，包含各种可重用的辅助功能。
*   查找应用配置: 查看 `@backend/src/config/` 和 `backend/.env` 文件 (注意：`.env` 文件通常不提交到版本库)。
*   查找日志配置: 查看 `@backend/src/utils/logger.js`。日志输出位置为 `@backend/logs/app.log`。
*   查找进度监控常量: 查看 `@backend/src/constants/progress.js`，包含SSE事件类型和任务状态定义。
*   查找流水线结果标准化: 查看 `@backend/src/utils/pipelineResultStandardizer.js`，统一返回格式。
*   理解应用启动流程: 查看 `@backend/src/app.js`。
*   文件保存及上传目录: 主要关注 `@backend/uploads/` 目录。相关配置在 `@backend/src/config/index.js`。
*   查找任务详情: 查看 `@docs/TASK.md` 获取任务分解和进度信息。
*   查找项目文档: 查看 `@backend/docs/` 获取API文档和技术方案。
*   前端测试环境: 查看 `@frontend-test/` 目录，用于测试后端接口的Vue.js应用。
*   代码注释: 添加或修改代码时，务必遵循 `@.augment/rules/code-commenting-standard.md`。
*   流水线开发: 创建或修改流水线及任务时，务必遵循 `@.augment/rules/pipeline_architecture.md`。
*   API开发: 创建API时，务必遵循 `@.augment/rules/API_CREATION_GUIDELINES.md`。
*   前端开发: 开发前端功能时，务必遵循 `@.augment/rules/frontend_development_standard.md`。
*   LLM API调用: 遵循 `@.augment/rules/llm_api_standard.md` 标准。
*   任务开发: 遵循 `@.augment/rules/task_development_standard.md` 标准。

## 使用说明 (查找代码)

*   理解后端架构: 查看 `@backend/src` 下的目录结构。
*   查找特定 API 的实现: 根据路由定义 (`@backend/src/routes/`) 定位控制器 (`@backend/src/controllers/{分类名}/`)，再定位流水线 (`@backend/src/pipelines/`) 和具体任务 (`@backend/src/tasks/`)。
*   查找后端配置信息: 查看 `@backend/src/config/` 和 `backend/.env`。
*   查找后端日志文件: `@backend/logs/app.log`。
*   查找后端上传及生成文件: `@backend/uploads/`。
*   查找提示词模板: 按照任务类型在 `@backend/src/prompts/` 下的相应目录中查找。
*   创建或修改提示词: 在 `@backend/src/prompts/` 下相应的任务类型目录中以 `.md` 格式创建文件。
*   查找视频生成模板: 在 `@backend/src/templates/` 目录下查找相应的模板配置。
*   查找工具函数: 在 `@backend/src/utils/` 目录下查找相应的工具类和辅助函数。
*   测试流水线: 在 `@backend/src/pipelines/tests/` 目录下创建或运行流水线集成测试文件。
*   测试任务单元: 在 `@backend/src/tasks/tests/` 目录下创建或运行任务单元测试文件。
*   运行综合测试: 在 `@backend/src/tests/` 目录下运行各种集成测试和功能测试脚本。
*   前端接口测试: 使用 `@frontend-test/` 目录下的Vue.js应用测试后端接口功能。
*   了解当前任务: 查看 `@docs/TASK.md` 了解项目任务和进度状态。
*   查看项目文档: 查看 `@backend/docs/` 了解API文档和技术方案。
*   查看开发规范: 查看 `@.augment/rules/` 了解所有开发标准和规范。

## 技术栈详细说明

### 后端核心依赖
- **Express.js 4.17.1**: Web应用框架
- **fluent-ffmpeg 2.1.2**: FFmpeg视频处理封装
- **microsoft-cognitiveservices-speech-sdk 1.43.1**: 微软语音服务SDK
- **puppeteer 24.14.0**: 无头浏览器自动化
- **playwright 1.54.1**: 跨浏览器自动化
- **canvas 3.1.0**: 2D图形绘制
- **winston 3.17.0**: 日志管理
- **multer 1.4.5**: 文件上传处理
- **axios 1.9.0**: HTTP客户端
- **uuid 11.1.0**: 唯一标识符生成

### 视频和字幕处理
- **media-captions 0.0.18**: 字幕格式处理
- **subtitle 4.2.2**: 字幕文件解析
- **自定义工具**: 字幕合并、处理、验证等工具类

### 开发和测试
- **nodemon 3.1.10**: 开发环境自动重启
- **form-data 4.0.3**: 表单数据处理
- **Vue.js 3 + Element Plus**: 前端测试界面

## 项目特色功能

1. **流水线架构**: 基于PipelineBase和TaskBase的模块化任务编排
2. **视频处理**: 支持视频上传、转录、字幕生成、视频合成
3. **多语言支持**: 中英文字幕生成和双语合并
4. **实时进度**: SSE事件推送处理进度
5. **模板系统**: 可配置的视频生成模板
6. **测试完备**: 单元测试、集成测试、前端测试全覆盖



