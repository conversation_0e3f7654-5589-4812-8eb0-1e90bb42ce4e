/**
 * BilingualSubtitleMergeTask 的独立测试脚本（使用真实硬编码数据）
 * 运行方式：node BilingualSubtitleMergeTask.test.js
 * 环境变量：
 *   - RUN_LLM_TESTS=true  启用真实LLM调用测试（耗时较长）
 */

const fs = require('fs');
const path = require('path');
const BilingualSubtitleMergeTask = require('../BilingualSubtitleMergeTask');
const logger = require('../../utils/logger');
const { TASK_STATUS } = require('../../constants/progress');

// 测试日志前缀
const testLogPrefix = '[文件：BilingualSubtitleMergeTask.test.js][双语字幕合并任务测试][测试执行]';

// 测试配置
const TEST_CONFIG = {
    runLLMTests: process.env.RUN_LLM_TESTS === 'true',
    timeout: process.env.RUN_LLM_TESTS === 'true' ? 300000 : 10000 // 5分钟 vs 10秒
};

logger.info(`${testLogPrefix} 🧪 开始测试 BilingualSubtitleMergeTask`);
logger.info(`${testLogPrefix} 🤖 LLM测试: ${TEST_CONFIG.runLLMTests ? '启用' : '禁用'}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 硬编码的真实测试数据
function getHardcodedTestData() {
    console.log('📁 正在读取硬编码测试数据...');
    
    // 读取 simplifiedSubtitleJsonArray 数据
    const simplifiedSubtitleJsonArray = JSON.parse(fs.readFileSync(
        path.join(__dirname, '../../../uploads/input/test_neetu_garcha_june_8_2025_corrected.json'), 
        'utf8'
    ));
    console.log(`✅ 已读取 simplifiedSubtitleJsonArray，条目数：${simplifiedSubtitleJsonArray.length}`);
    
    // 读取 translatedSubtitleJsonArray 数据
    const translatedSubtitleJsonArray = JSON.parse(fs.readFileSync(
        path.join(__dirname, '../../../uploads/input/test_neetu_garcha_june_8_2025_translated_chinese_subtitle.json'), 
        'utf8'
    ));
    console.log(`✅ 已读取 translatedSubtitleJsonArray，条目数：${translatedSubtitleJsonArray.length}`);
    
    // 读取 clozedSubtitleJsonArray 数据
    const clozedSubtitleJsonArray = JSON.parse(fs.readFileSync(
        path.join(__dirname, '../../../uploads/input/test_neetu_garcha_june_8_2025_clozed_subtitle.json'), 
        'utf8'
    ));
    console.log(`✅ 已读取 clozedSubtitleJsonArray，条目数：${clozedSubtitleJsonArray.length}`);
    
    // 生成 correctedFullText（拼接所有text字段）
    const correctedFullText = simplifiedSubtitleJsonArray.map(item => item.text).join(' ');
    console.log(`✅ 已生成 correctedFullText，长度：${correctedFullText.length} 字符`);
    
    // 检查 clozed 数据中的 words 字段
    console.log('\n🔍 检查挖空字幕中的words字段（前5个条目）：');
    clozedSubtitleJsonArray.slice(0, 5).forEach((item, index) => {
        console.log(`  条目 ${index + 1}: id=${item.id}, words=${JSON.stringify(item.words)}`);
    });
    
    return {
        simplifiedSubtitleJsonArray,
        translatedSubtitleJsonArray,
        clozedSubtitleJsonArray,
        correctedFullText,
        savePath: path.join(__dirname, '../../../uploads/output'),
        videoIdentifier: 'test_neetu_garcha_june_8_2025',
        reqId: 'test_bilingual_merge_' + Date.now()
    };
}

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        console.error(`❌ 断言失败: ${message}`);
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    console.log(`✅ 断言成功: ${message}`);
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

// 详细的进度追踪回调
function createDetailedProgressCallback() {
    const progressLogs = [];
    
    return {
        callback: (data) => {
            const timestamp = new Date().toISOString();
            const logEntry = { timestamp, ...data };
            progressLogs.push(logEntry);
            
            console.log(`\n📊 [${timestamp}] 进度回调:`);
            console.log(`   任务名称: ${data.taskName}`);
            console.log(`   状态: ${data.status}`);
            console.log(`   详情: ${data.detail || 'N/A'}`);
            if (data.technicalDetail) {
                console.log(`   技术详情: ${data.technicalDetail}`);
            }
            if (data.result) {
                console.log(`   结果预览: ${JSON.stringify(data.result).substring(0, 100)}...`);
            }
            if (data.error) {
                console.log(`   错误信息: ${data.error.message}`);
            }
            
            logger.info(`${testLogPrefix}[进度回调] ${JSON.stringify(logEntry)}`);
        },
        getLogs: () => progressLogs
    };
}

// 主要测试函数
async function runDetailedTest() {
    console.log('\n🚀 开始详细测试...\n');
    
    try {
        // 1. 准备测试数据
        console.log('=== 步骤 1: 准备测试数据 ===');
        const testData = getHardcodedTestData();
        
        // 2. 创建任务实例
        console.log('\n=== 步骤 2: 创建任务实例 ===');
        const task = new BilingualSubtitleMergeTask();
        console.log(`✅ 任务实例已创建: ${task.name}`);
        
        // 3. 设置详细进度追踪
        console.log('\n=== 步骤 3: 设置进度追踪 ===');
        const progressTracker = createDetailedProgressCallback();
        
        // 4. 构建完整的context
        console.log('\n=== 步骤 4: 构建context ===');
        const context = {
            reqId: testData.reqId,
            videoIdentifier: testData.videoIdentifier,
            simplifiedSubtitleJsonArray: testData.simplifiedSubtitleJsonArray,
            translatedSubtitleJsonArray: testData.translatedSubtitleJsonArray,
            clozedSubtitleJsonArray: testData.clozedSubtitleJsonArray,
            savePath: testData.savePath,
            correctedFullText: testData.correctedFullText
        };
        
        console.log('📋 Context 摘要:');
        console.log(`   reqId: ${context.reqId}`);
        console.log(`   videoIdentifier: ${context.videoIdentifier}`);
        console.log(`   simplifiedSubtitleJsonArray: ${context.simplifiedSubtitleJsonArray.length} 条目`);
        console.log(`   translatedSubtitleJsonArray: ${context.translatedSubtitleJsonArray.length} 条目`);
        console.log(`   clozedSubtitleJsonArray: ${context.clozedSubtitleJsonArray.length} 条目`);
        console.log(`   savePath: ${context.savePath}`);
        console.log(`   correctedFullText: ${context.correctedFullText.length} 字符`);
        
        // 5. 执行任务
        console.log('\n=== 步骤 5: 执行任务 ===');
        console.log('⚠️  正在执行BilingualSubtitleMergeTask，这可能需要几分钟...');
        
        const startTime = Date.now();
        const result = await task.execute(context, progressTracker.callback);
        const duration = Date.now() - startTime;
        
        console.log(`\n🎉 任务执行完成！耗时: ${(duration / 1000).toFixed(2)}秒`);
        
        // 6. 验证结果
        console.log('\n=== 步骤 6: 验证结果 ===');
        assert(result, '任务应返回结果');
        assert(task.status === TASK_STATUS.COMPLETED, '任务状态应为COMPLETED');
        assert(result.enhancedBilingualSubtitleJsonArray, '结果应包含enhancedBilingualSubtitleJsonArray');
        assert(result.enhancedBilingualSubtitleFilePath, '结果应包含enhancedBilingualSubtitleFilePath');
        
        console.log('\n📊 结果摘要:');
        console.log(`   enhancedBilingualSubtitleJsonArray: ${result.enhancedBilingualSubtitleJsonArray.length} 条目`);
        console.log(`   enhancedBilingualSubtitleFilePath: ${result.enhancedBilingualSubtitleFilePath}`);
        
        // 7. 检查生成的文件
        console.log('\n=== 步骤 7: 检查生成的文件 ===');
        if (fs.existsSync(result.enhancedBilingualSubtitleFilePath)) {
            const fileContent = JSON.parse(fs.readFileSync(result.enhancedBilingualSubtitleFilePath, 'utf8'));
            console.log(`✅ 文件已生成，包含 ${fileContent.length} 个条目`);
            
            // 检查前几个条目的words_explanation
            console.log('\n🔍 检查前5个条目的words_explanation:');
            fileContent.slice(0, 5).forEach((item, index) => {
                console.log(`\n  条目 ${index + 1}:`);
                console.log(`    id: ${item.id}`);
                console.log(`    text_english: ${item.text_english}`);
                console.log(`    text_chinese: ${item.text_chinese}`);
                console.log(`    words_explanation: ${JSON.stringify(item.words_explanation, null, 2)}`);
                
                // 检查words_explanation是否为空
                if (Object.keys(item.words_explanation || {}).length === 0) {
                    console.log(`    ⚠️  words_explanation 为空！`);
                } else {
                    console.log(`    ✅ words_explanation 包含 ${Object.keys(item.words_explanation).length} 个词汇解释`);
                }
            });
        } else {
            console.error(`❌ 文件未生成: ${result.enhancedBilingualSubtitleFilePath}`);
        }
        
        // 8. 输出进度日志摘要
        console.log('\n=== 步骤 8: 进度日志摘要 ===');
        const progressLogs = progressTracker.getLogs();
        console.log(`📈 总共记录了 ${progressLogs.length} 条进度日志`);
        
        progressLogs.forEach((log, index) => {
            console.log(`  ${index + 1}. [${log.timestamp}] ${log.status}: ${log.detail || 'N/A'}`);
        });
        
        console.log('\n🎉 测试完成！');
        return true;

    } catch (error) {
        console.error('\n💥 测试失败:');
        console.error(`   错误消息: ${error.message}`);
        console.error(`   错误堆栈: ${error.stack}`);
        
        logger.error(`${testLogPrefix} 测试失败: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        
        return false;
    }
}

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('💥 未捕获异常:', error.message);
    logger.error(`${testLogPrefix} 未捕获异常: ${error.message}`);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 未处理的Promise拒绝:', reason);
    logger.error(`${testLogPrefix} 未处理的Promise拒绝:`, reason);
    process.exit(1);
});

// 超时处理
setTimeout(() => {
    console.error(`⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    logger.error(`${testLogPrefix} 测试超时，强制退出`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 运行测试
if (TEST_CONFIG.runLLMTests) {
    console.log('🤖 LLM测试已启用，开始详细测试...\n');
    runDetailedTest().then(success => {
        if (success) {
            console.log('\n✅ 所有测试通过！');
            process.exit(0);
        } else {
            console.log('\n❌ 测试失败！');
            process.exit(1);
        }
        });
} else {
    console.log('⚠️  LLM测试未启用，请设置 RUN_LLM_TESTS=true 来运行完整测试');
    console.log('💡 运行命令: $env:RUN_LLM_TESTS = "true"; node BilingualSubtitleMergeTask.test.js');
    process.exit(0);
}
