# &lt;AbsoluteFill&gt;

## 概述

`<AbsoluteFill>` 是一个辅助组件，它是一个绝对定位的 `<div>` 元素，具有预定义的样式，用于创建全屏填充的布局容器。

## 默认样式

```typescript
const style: React.CSSProperties = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
};
```

## 语法

```typescript
import { AbsoluteFill } from "remotion";

<AbsoluteFill>
  <YourContent />
</AbsoluteFill>
```

## 核心特性

### 绝对定位填充
- 完全填充父容器
- 绝对定位，不影响文档流
- 默认 Flexbox 布局（列方向）

### 层叠支持
- 支持多层内容叠加
- 后渲染的层显示在上方
- 适合创建复杂的视觉效果

## 基础示例

### 1. 简单的全屏容器

```typescript
import { AbsoluteFill } from "remotion";

const FullScreenComponent = () => {
  return (
    <AbsoluteFill style={{ backgroundColor: '#000' }}>
      <h1 style={{ color: 'white', textAlign: 'center' }}>
        全屏内容
      </h1>
    </AbsoluteFill>
  );
};
```

### 2. 层叠内容

```typescript
import { AbsoluteFill, OffthreadVideo } from "remotion";

const LayeredContent = () => {
  return (
    <AbsoluteFill>
      {/* 背景视频层 */}
      <AbsoluteFill>
        <OffthreadVideo src="https://example.com/background.mp4" />
      </AbsoluteFill>
      
      {/* 文字覆盖层 */}
      <AbsoluteFill>
        <h1 style={{
          color: 'white',
          fontSize: 48,
          textAlign: 'center',
          textShadow: '2px 2px 4px rgba(0,0,0,0.8)'
        }}>
          这段文字显示在视频上方！
        </h1>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

### 3. 多层复杂布局

```typescript
const ComplexLayeredLayout = () => {
  return (
    <AbsoluteFill>
      {/* 背景层 */}
      <AbsoluteFill style={{
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)'
      }} />
      
      {/* 内容层 */}
      <AbsoluteFill style={{
        padding: 40,
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          padding: 30,
          borderRadius: 12,
          textAlign: 'center'
        }}>
          <h2>主要内容</h2>
          <p>这里是详细描述</p>
        </div>
      </AbsoluteFill>
      
      {/* 装饰层 */}
      <AbsoluteFill style={{
        pointerEvents: 'none',
        background: 'radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%)'
      }} />
    </AbsoluteFill>
  );
};
```

## 实际应用场景

### 1. 视频背景与文字叠加

```typescript
import { AbsoluteFill, Video, useCurrentFrame, interpolate } from "remotion";

const VideoWithTextOverlay = () => {
  const frame = useCurrentFrame();
  
  // 文字淡入动画
  const textOpacity = interpolate(frame, [0, 30], [0, 1], {
    extrapolateRight: 'clamp'
  });
  
  return (
    <AbsoluteFill>
      {/* 背景视频 */}
      <AbsoluteFill>
        <Video src="background-video.mp4" />
      </AbsoluteFill>
      
      {/* 半透明遮罩 */}
      <AbsoluteFill style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)'
      }} />
      
      {/* 文字内容 */}
      <AbsoluteFill style={{
        justifyContent: 'center',
        alignItems: 'center',
        opacity: textOpacity
      }}>
        <h1 style={{
          color: 'white',
          fontSize: 64,
          fontWeight: 'bold',
          textAlign: 'center',
          margin: 0
        }}>
          欢迎观看
        </h1>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

### 2. 分屏布局

```typescript
const SplitScreenLayout = () => {
  return (
    <AbsoluteFill>
      {/* 左侧内容 */}
      <AbsoluteFill style={{
        width: '50%',
        backgroundColor: '#ff6b6b',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h2 style={{ color: 'white' }}>左侧内容</h2>
      </AbsoluteFill>
      
      {/* 右侧内容 */}
      <AbsoluteFill style={{
        left: '50%',
        width: '50%',
        backgroundColor: '#4ecdc4',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h2 style={{ color: 'white' }}>右侧内容</h2>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

### 3. 响应式网格布局

```typescript
const ResponsiveGrid = () => {
  const { width, height } = useVideoConfig();
  const isVertical = height > width;
  
  return (
    <AbsoluteFill style={{
      display: 'grid',
      gridTemplateColumns: isVertical ? '1fr' : '1fr 1fr',
      gridTemplateRows: isVertical ? '1fr 1fr' : '1fr',
      gap: 20,
      padding: 20
    }}>
      <div style={{
        backgroundColor: '#ff6b6b',
        borderRadius: 8,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h3 style={{ color: 'white' }}>区块 1</h3>
      </div>
      
      <div style={{
        backgroundColor: '#4ecdc4',
        borderRadius: 8,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h3 style={{ color: 'white' }}>区块 2</h3>
      </div>
    </AbsoluteFill>
  );
};
```

### 4. 动画层叠效果

```typescript
const AnimatedLayers = () => {
  const frame = useCurrentFrame();
  
  // 背景旋转
  const backgroundRotation = interpolate(frame, [0, 300], [0, 360]);
  
  // 前景缩放
  const foregroundScale = interpolate(
    frame,
    [0, 60, 240, 300],
    [0.8, 1.2, 1.2, 0.8]
  );
  
  return (
    <AbsoluteFill>
      {/* 旋转背景层 */}
      <AbsoluteFill style={{
        transform: `rotate(${backgroundRotation}deg)`,
        background: 'conic-gradient(from 0deg, #ff6b6b, #4ecdc4, #45b7d1, #ff6b6b)'
      }} />
      
      {/* 静态遮罩层 */}
      <AbsoluteFill style={{
        backgroundColor: 'rgba(255, 255, 255, 0.8)'
      }} />
      
      {/* 缩放前景层 */}
      <AbsoluteFill style={{
        transform: `scale(${foregroundScale})`,
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          width: 200,
          height: 200,
          backgroundColor: '#333',
          borderRadius: '50%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <span style={{ color: 'white', fontSize: 24 }}>动画</span>
        </div>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

## 高级用法

### 1. 自定义布局方向

```typescript
const HorizontalLayout = () => {
  return (
    <AbsoluteFill style={{
      flexDirection: 'row', // 覆盖默认的 column
      justifyContent: 'space-around',
      alignItems: 'center',
      padding: 40
    }}>
      <div style={{ backgroundColor: '#ff6b6b', padding: 20 }}>项目 1</div>
      <div style={{ backgroundColor: '#4ecdc4', padding: 20 }}>项目 2</div>
      <div style={{ backgroundColor: '#45b7d1', padding: 20 }}>项目 3</div>
    </AbsoluteFill>
  );
};
```

### 2. 条件层叠

```typescript
const ConditionalLayers = () => {
  const frame = useCurrentFrame();
  const showOverlay = frame > 60;
  
  return (
    <AbsoluteFill>
      {/* 基础内容层 */}
      <AbsoluteFill style={{
        backgroundColor: '#f0f0f0',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h2>基础内容</h2>
      </AbsoluteFill>
      
      {/* 条件覆盖层 */}
      {showOverlay && (
        <AbsoluteFill style={{
          backgroundColor: 'rgba(255, 107, 107, 0.9)',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h2 style={{ color: 'white' }}>覆盖内容</h2>
        </AbsoluteFill>
      )}
    </AbsoluteFill>
  );
};
```

### 3. 嵌套 AbsoluteFill

```typescript
const NestedAbsoluteFills = () => {
  return (
    <AbsoluteFill>
      {/* 外层容器 */}
      <AbsoluteFill style={{
        backgroundColor: '#333',
        padding: 40
      }}>
        {/* 内层容器 */}
        <AbsoluteFill style={{
          backgroundColor: 'white',
          margin: 20,
          borderRadius: 12,
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h3>嵌套内容</h3>
        </AbsoluteFill>
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

## 添加 Ref

```typescript
import { useRef } from "react";

const AbsoluteFillWithRef = () => {
  const fillRef = useRef<HTMLDivElement>(null);
  
  return (
    <AbsoluteFill ref={fillRef}>
      <div>带有 ref 的内容</div>
    </AbsoluteFill>
  );
};
```

## TailwindCSS 兼容性

从 v4.0.249 开始，`<AbsoluteFill>` 会检测冲突的 Tailwind 类并禁用相应的内联样式：

```typescript
// 这将正确工作（行布局）
<AbsoluteFill className="flex flex-row">
  <div>项目 1</div>
  <div>项目 2</div>
</AbsoluteFill>

// Tailwind 类会覆盖默认的 flexDirection: 'column'
```

## 性能优化

### 1. 避免不必要的重渲染

```typescript
import { memo } from "react";

const OptimizedLayer = memo(({ children, style }: {
  children: React.ReactNode;
  style?: React.CSSProperties;
}) => {
  return (
    <AbsoluteFill style={style}>
      {children}
    </AbsoluteFill>
  );
});
```

### 2. 使用 CSS 变量

```typescript
const CSSVariableLayer = () => {
  const frame = useCurrentFrame();
  const opacity = interpolate(frame, [0, 30], [0, 1]);
  
  return (
    <AbsoluteFill 
      style={{
        '--layer-opacity': opacity,
        opacity: 'var(--layer-opacity)'
      } as React.CSSProperties}
    >
      <div>内容</div>
    </AbsoluteFill>
  );
};
```

## 最佳实践

### 1. 语义化层次
```typescript
const SemanticLayers = () => {
  return (
    <AbsoluteFill>
      {/* 背景层 */}
      <AbsoluteFill data-layer="background">
        <BackgroundComponent />
      </AbsoluteFill>
      
      {/* 内容层 */}
      <AbsoluteFill data-layer="content">
        <ContentComponent />
      </AbsoluteFill>
      
      {/* UI 层 */}
      <AbsoluteFill data-layer="ui">
        <UIComponent />
      </AbsoluteFill>
    </AbsoluteFill>
  );
};
```

### 2. 响应式设计
```typescript
const ResponsiveAbsoluteFill = () => {
  const { width, height } = useVideoConfig();
  const isMobile = width < 768;
  
  return (
    <AbsoluteFill style={{
      padding: isMobile ? 20 : 40,
      fontSize: isMobile ? 14 : 16
    }}>
      <div>响应式内容</div>
    </AbsoluteFill>
  );
};
```

### 3. 可访问性考虑
```typescript
const AccessibleLayer = () => {
  return (
    <AbsoluteFill 
      role="main"
      aria-label="主要内容区域"
    >
      <div>可访问的内容</div>
    </AbsoluteFill>
  );
};
```

## 常见用例

1. **全屏背景**: 视频、图片或渐变背景
2. **文字叠加**: 在媒体内容上添加标题和描述
3. **UI 元素**: 按钮、导航、进度条等界面元素
4. **动画层**: 创建复杂的层叠动画效果
5. **布局容器**: 作为其他组件的定位基础

## 相关 API

- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置
- [`interpolate()`](./interpolate.md) - 值插值
- [`<Video>`](./Video.md) - 视频组件
- [`<Audio>`](./Audio.md) - 音频组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/AbsoluteFill.tsx)
