# VideoClipAndCropTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **originalVideoPath** (string): 原始视频文件完整路径
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **savePath** (string): 文件保存路径

### 可选参数
- **cropData** (object): 画面裁剪参数
  - **cropWidth** (number): 裁剪宽度
  - **cropHeight** (number): 裁剪高度
  - **cropXOffset** (number): 裁剪X偏移量
  - **cropYOffset** (number): 裁剪Y偏移量
- **clipSegments** (Array): 时间片段数组
  - **startTime** (number): 开始时间（秒）
  - **endTime** (number): 结束时间（秒）

## 2. 输出上下文参数 (Output Context)

- **processedVideoPath** (string): 处理后视频文件完整路径
- **processedVideoFileName** (string): 处理后视频文件名
- **originalVideoPath** (string): 更新后的原始视频路径（指向处理后视频）
- **originalVideoFileName** (string): 更新后的原始视频文件名
- **videoProcessingStats** (object): 视频处理统计信息
  - **originalDuration** (number): 原始视频时长
  - **processedDuration** (number): 处理后视频时长
  - **compressionRatio** (number): 压缩比例
  - **processingTime** (number): 处理时间（毫秒）
- **cropData** (object): 裁剪参数（原样返回）
- **clipSegments** (Array): 片段信息（原样返回）
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 裁剪参数格式
```json
{
  "cropWidth": 1080,
  "cropHeight": 1920,
  "cropXOffset": 100,
  "cropYOffset": 50
}
```

### 时间片段格式
```json
[
  {
    "startTime": 10.5,
    "endTime": 25.8
  },
  {
    "startTime": 45.2,
    "endTime": 60.0
  }
]
```

### FFmpeg裁剪滤镜格式
```
crop=1080:1920:100:50
```

## 4. 文件操作

### 保存的文件格式
- **.mp4**: 处理后的视频文件

### 文件命名规则
- **无裁剪无片段**: `{videoIdentifier}_processed.mp4`
- **仅裁剪**: `{videoIdentifier}_cropped_{宽度}x{高度}.mp4`
- **仅片段**: `{videoIdentifier}_clipped_{片段数量}segments.mp4`
- **裁剪+片段**: `{videoIdentifier}_clipped_cropped_{宽度}x{高度}_{片段数量}segments.mp4`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 自动创建目录（如果不存在）
- 支持复杂的文件名生成逻辑

## 5. 执行逻辑概述

视频剪辑与裁剪任务负责对原始视频进行预处理，包括时间片段提取和画面裁剪。任务首先验证输入参数，然后根据是否有裁剪参数和时间片段选择不同的处理策略。如果只有裁剪参数，直接对整个视频进行画面裁剪；如果只有时间片段，提取指定时间段的视频；如果两者都有，先提取时间片段再进行裁剪。处理过程使用FFmpeg的高效滤镜链，支持多片段自动合并和复杂的裁剪操作。任务提供详细的处理统计信息，包括时长变化、压缩比例等。处理完成后更新上下文中的视频路径，确保下游任务使用处理后的视频文件。
