/**
 * @功能概述: 对简化字幕JSON数组进行AI挖空处理，生成用于学习的字幕，并保存为JSON和SRT文件。
 *           此任务利用llmService调用大语言模型，根据特定提示词对字幕文本进行挖空处理。
 * @输入依赖: context.videoIdentifier (string, 必需) - 视频标识符，用于文件命名和日志追踪。
 *           context.simplifiedSubtitleJsonArray (Array, 必需) - 来自TranscriptionCorrectionTask的简化字幕JSON数组。
 *           context.savePath (string, 必需) - 文件保存路径。
 *           context.correctedFullText (string, 可选) - 作为LLM理解视频整体内容的上下文参考。
 * @输出结果: context.clozedSubtitleJsonArray (Array) - AI处理后带挖空的字幕JSON数组。
 *           context.clozedSubtitleJsonPath (string) - 保存挖空后字幕JSON文件的路径。
 *           context.clozedEnglishSrtContent (string) - 挖空后的SRT字幕内容。
 *           context.clozedEnglishSrtPath (string) - 保存挖空后SRT文件的路径。
 * @外部依赖: llmService.js (及其依赖的LLM API), fileSaver.js (文件保存工具), jsonValidator.js (JSON校验)。
 * @失败策略: LLM调用失败、参数校验失败、文件保存失败或LLM返回格式不正确时，任务将抛出错误并标记为失败状态。
 * @进度阶段:
 *   1. STARTED + INITIALIZING (0%) - 任务初始化
 *   2. RUNNING + VALIDATING_INPUTS (10%) - 输入参数校验
 *   3. RUNNING + LLM_PROCESSING (20-70%) - 调用LLM进行挖空处理
 *   4. RUNNING + PROCESSING (75%) - LLM返回JSON的解析与校验
 *   5. RUNNING + SAVING_INTERMEDIATE (80%) - 保存挖空后JSON文件
 *   6. RUNNING + SAVING_FILE (90%) - 生成并保存挖空后的SRT文件
 *   7. COMPLETED + FINALIZING (100%) - 任务完成，结果整理
 */

const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const llmService = require('../services/llmService');
const jsonValidator = require('../utils/jsonValidator');
const fileSaver = require('../utils/fileSaver');

// 模块级日志前缀
const taskModuleLogPrefix = '[文件：SubtitleClozeTask.js][字幕挖空任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`); // 日志：确认模块已加载

class SubtitleClozeTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建字幕挖空任务实例。
     * @param {string} [name='SubtitleClozeTask'] - 任务名称。
     */
    constructor(name = 'SubtitleClozeTask') {
        super(name);
        this.instanceLogPrefix = `[文件：SubtitleClozeTask.js][字幕挖空任务][${this.name}]`;
        logger.info(`${this.instanceLogPrefix} ${this.name} 实例已创建。`); // 日志：记录实例创建
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段。
     * @param {object} context - 要验证的上下文对象。
     * @param {Array<string>} requiredFields - 必需字段名称数组。
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录。
     * @throws {Error} 当缺少任何必需字段或字段值为空时抛出错误。
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            // 检查字段是否存在并且对于字符串类型是否非空
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                const errorMsg = `执行失败：上下文缺少必需字段 '${field}' 或字段值为空。`;
                logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`); // 日志：记录参数校验错误
                const error = new Error(errorMsg);
                this.fail(error, TASK_SUBSTATUS.INVALID_INPUT);
                throw error;
            }
        }
        logger.debug(`${execLogPrefix} 所有必需输入参数均已通过验证。`); // 日志：所有必需字段验证通过
    }

    /**
     * @功能概述: 执行字幕挖空任务的核心逻辑。
     * @param {object} context - 上下文对象，期望包含:
     *                           - videoIdentifier: {string} 视频标识符 (必需)。
     *                           - simplifiedSubtitleJsonArray: {Array} 简化字幕JSON数组 (必需)。
     *                           - savePath: {string} 文件保存路径 (必需)。
     *                           - correctedFullText: {string} 视频的完整转录文本，用于LLM理解上下文 (可选)。
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度。
     * @returns {Promise<object>} 包含挖空后字幕JSON数组、文件路径和SRT内容的对象。
     * @throws {Error} 如果参数校验失败、LLM调用失败、文件保存失败或LLM返回格式不正确时，则抛出错误。
     */
    async execute(context, progressCallback) {
        // 步骤 1: 优先从上下文中解构出核心参数
        const { videoIdentifier, simplifiedSubtitleJsonArray, correctedFullText, savePath } = context;

        // 步骤 2: 定义任务执行所必需的字段列表
        const requiredFields = ['videoIdentifier', 'simplifiedSubtitleJsonArray', 'savePath'];

        // 步骤 3: 构建执行日志前缀
        const execLogPrefix = `[文件：SubtitleClozeTask.js][字幕挖空任务][${videoIdentifier || 'unknown_video'}]`;

        // 步骤 4: 验证必需的上下文参数
        this.validateRequiredFields(context, requiredFields, execLogPrefix);

        // 步骤 5: 验证simplifiedSubtitleJsonArray的类型
        if (!Array.isArray(simplifiedSubtitleJsonArray)) {
            const errorMsg = `simplifiedSubtitleJsonArray 必须是一个数组，但收到的是: ${typeof simplifiedSubtitleJsonArray}`;
            logger.error(`${execLogPrefix}[类型验证] ${errorMsg}`);
            this.fail(new Error(errorMsg), TASK_SUBSTATUS.INVALID_INPUT);
            throw new Error(errorMsg);
        }

        this.setProgressCallback(progressCallback);
        this.start();

        logger.info(`${execLogPrefix}[execute] 开始执行字幕挖空任务。`);

        try {
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.VALIDATING_INPUTS, {
                detail: '验证输入参数 (字幕JSON数组, 视频标识符, 保存路径)',
                technicalDetail: 'Checking context for videoIdentifier, simplifiedSubtitleJsonArray, savePath'
            });

            logger.info(`${execLogPrefix}[execute] 输入参数验证通过。`);

            // 记录correctedFullText的状态
            if (correctedFullText && correctedFullText.trim()) {
                logger.debug(`${execLogPrefix}[LLM调用] 使用完整上下文文本，长度：${correctedFullText.length}字符`);
            } else {
                logger.debug(`${execLogPrefix}[LLM调用] 未提供完整上下文文本，将使用默认值`);
            }

            logger.debug(`${execLogPrefix}[调试信息] 原始输入数据长度：${simplifiedSubtitleJsonArray.length}，上下文长度：${correctedFullText ? correctedFullText.length : 0}`);

            // 处理空数组的特殊情况
            if (simplifiedSubtitleJsonArray.length === 0) {
                logger.info(`${execLogPrefix}[特殊处理] 输入字幕数组为空，直接返回空结果`);

                this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_INTERMEDIATE, {
                    detail: '处理空字幕数组，保存空结果',
                    current: 80,
                    total: 100,
                    technicalDetail: 'Handling empty subtitle array'
                });

                // 保存空的JSON文件
                const clozedSubtitleJsonPath = await this.saveClozedSubtitleJson(
                    [],
                    videoIdentifier,
                    savePath,
                    execLogPrefix
                );

                this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_FILE, {
                    detail: '生成空的SRT文件',
                    current: 90,
                    total: 100,
                    technicalDetail: 'Generating empty SRT file'
                });

                // 生成空的SRT内容
                const clozedEnglishSrtContent = '';
                const clozedEnglishSrtPath = await this.saveClozedSRT(
                    clozedEnglishSrtContent,
                    videoIdentifier,
                    savePath,
                    execLogPrefix
                );

                // 返回空结果
                const result = {
                    clozedSubtitleJsonArray: [],
                    clozedSubtitleJsonPath: clozedSubtitleJsonPath,
                    clozedEnglishSrtContent: clozedEnglishSrtContent,
                    clozedEnglishSrtPath: clozedEnglishSrtPath,
                    subtitleClozeTaskStatus: 'success'
                };

                this.result = result;
                this.complete(result);
                logger.info(`${execLogPrefix}[execute] SubtitleClozeTask 成功完成（空数组处理）。`);
                return result;
            }

            // 开始LLM挖空处理
            logger.info(`${execLogPrefix}[LLM处理] 开始对简化字幕JSON数组进行AI挖空处理。`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '准备调用LLM进行字幕挖空',
                current: 20,
                total: 100,
                technicalDetail: 'Preparing parameters for llmService.callLLM for SUBTITLE_CLOZE_UTILS'
            });

            // 调用LLM进行字幕挖空处理
            const clozedSubtitleJsonArray = await this.performLLMJsonCloze(
                simplifiedSubtitleJsonArray,
                correctedFullText,
                videoIdentifier,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[LLM处理] LLM挖空处理完成，返回 ${clozedSubtitleJsonArray.length} 个挖空字幕条目。`);

            // 保存挖空后字幕JSON文件
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_INTERMEDIATE, {
                detail: '保存挖空后字幕JSON文件',
                current: 80,
                total: 100,
                technicalDetail: 'Calling fileSaver.saveDataToFile for clozed subtitle JSON'
            });

            const clozedSubtitleJsonPath = await this.saveClozedSubtitleJson(
                clozedSubtitleJsonArray,
                videoIdentifier,
                savePath,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[文件保存] 挖空后字幕JSON文件已保存到: ${clozedSubtitleJsonPath}`);

            // 生成并保存挖空后的SRT文件
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_FILE, {
                detail: '生成并保存挖空后的SRT文件',
                current: 90,
                total: 100,
                technicalDetail: 'Converting JSON to SRT format and saving file'
            });

            const clozedEnglishSrtContent = this.generateClozeSRT(clozedSubtitleJsonArray, execLogPrefix);
            const clozedEnglishSrtPath = await this.saveClozedSRT(
                clozedEnglishSrtContent,
                videoIdentifier,
                savePath,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[文件保存] 挖空后SRT文件已保存到: ${clozedEnglishSrtPath}`);

            // 准备最终返回结果
            const result = {
                clozedSubtitleJsonArray: clozedSubtitleJsonArray,
                clozedSubtitleJsonPath: clozedSubtitleJsonPath,
                clozedEnglishSrtContent: clozedEnglishSrtContent,
                clozedEnglishSrtPath: clozedEnglishSrtPath,
                subtitleClozeTaskStatus: 'success'
            };

            this.result = result;
            this.complete(result);
            logger.info(`${execLogPrefix}[execute] SubtitleClozeTask 成功完成。`);
            return result;

        } catch (error) {
            const finalError = (error instanceof Error) ? error : new Error(String(error));
            logger.error(`${execLogPrefix}[错误] SubtitleClozeTask 执行过程中发生错误: ${finalError.message}`); // 日志：记录任务执行错误
            if (finalError.stack && !finalError.message.includes(finalError.stack.split('\n')[0])) {
                // 记录不同的调用堆栈信息
                logger.error(`${execLogPrefix} 调用堆栈: ${finalError.stack}`); // 日志：记录错误堆栈
            }

            // 确保 this.fail 被调用时传入 Error 实例和适当的 subStatus (如果可用)
            let subStatus = TASK_SUBSTATUS.UNKNOWN_ERROR;

            if (this.status === TASK_STATUS.FAILED && this.error === finalError && this.currentSubStatus) {
                 subStatus = this.currentSubStatus; // currentSubStatus 来自 TaskBase
            }

            this.fail(finalError, subStatus); // 传递最终的错误对象和子状态

            throw finalError; // 始终抛出 Error 实例，让流水线捕获
        }
    }

    /**
     * @功能概述: Token优化预处理 - 移除原始words字段以减少Token消耗
     * @param {Array} fullSubtitleArray - 完整的5字段字幕数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Object} {optimizedArray: Array, originalArray: Array, tokenSavings: Object}
     */
    prepareOptimizedInputForLLM(fullSubtitleArray, execLogPrefix) {
        try {
            // 保存原始完整数据（如果需要的话，但挖空任务实际上不需要原始words）
            this.originalFullSubtitleArray = JSON.parse(JSON.stringify(fullSubtitleArray));
            
            // 为LLM创建4字段版本（移除words字段）
            const optimizedArray = fullSubtitleArray.map(item => ({
                id: item.id,
                start: item.start,
                end: item.end,
                text: item.text
                // 故意移除words字段 - LLM不需要原始时间戳，会生成新的挖空词列表
            }));
            
            // 计算Token节省效果
            const originalSize = JSON.stringify(fullSubtitleArray).length;
            const optimizedSize = JSON.stringify(optimizedArray).length;
            const savedBytes = originalSize - optimizedSize;
            const savingsPercent = ((savedBytes / originalSize) * 100).toFixed(1);
            
            const tokenSavings = {
                originalSize,
                optimizedSize,
                savedBytes,
                savingsPercent
            };
            
            logger.info(`${execLogPrefix}[Token优化] 数据优化完成`);
            logger.info(`${execLogPrefix}[Token优化] 原始大小: ${originalSize}字符`);
            logger.info(`${execLogPrefix}[Token优化] 优化后大小: ${optimizedSize}字符`);
            logger.info(`${execLogPrefix}[Token优化] 节省: ${savedBytes}字符 (${savingsPercent}%)`);
            
            return {
                optimizedArray,
                originalArray: fullSubtitleArray,
                tokenSavings
            };
            
        } catch (error) {
            logger.error(`${execLogPrefix}[Token优化] 优化预处理失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 调用LLM进行字幕挖空处理（使用标准化API）
     * @param {Array} simplifiedSubtitleJsonArray - 简化字幕JSON数组
     * @param {string} correctedFullText - 完整上下文文本（可选）
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Promise<Array>} 挖空后的字幕JSON数组
     */
    async performLLMJsonCloze(simplifiedSubtitleJsonArray, correctedFullText, videoIdentifier, execLogPrefix) {
        try {
            // 第1步：Token优化预处理
            const optimization = this.prepareOptimizedInputForLLM(
                simplifiedSubtitleJsonArray, 
                execLogPrefix
            );

            // 第2步：标准化LLM调用参数
            const llmOptions = {
                promptParams: {
                    subtitle_json_to_cloze: JSON.stringify(optimization.optimizedArray),
                    overall_video_context: correctedFullText || '无完整上下文文本',
                    systemPromptContent: '你是一位具有15年英语教学经验的资深语言教育专家与语法专家，专精于句子语义分析和关键词识别。严格遵守JSON格式要求，确保输出内容符合JSON格式。'
                },
                templateName: 'default',
                //modelName: 'google/gemini-2.5-flash-preview-05-20',
                //modelName: 'google/gemini-2.5-flash-lite-preview-06-17',
                modelName: 'google/gemini-2.5-flash',
                //modelName: 'google/gemini-2.5-pro',
                temperature: 0.2,
                max_tokens: 30000,
                
                // === 使用 API 增强功能 ===
                apiEnhancements: {
                    structuredOutput: {
                        enabled: true,
                        schema: {
                            name: 'subtitle_cloze_response',
                            strict: true,
                            schema: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        id: { type: 'string' },
                                        start: { type: 'number' },
                                        end: { type: 'number' },
                                        text: { type: 'string' },
                                        words: {
                                            type: 'array',
                                            items: { type: 'string' }
                                        }
                                    },
                                    required: ['id', 'start', 'end', 'text', 'words'],
                                    additionalProperties: false
                                }
                            }
                        }
                    },
                    advancedRetry: {
                        exponentialBackoff: true,
                        jitter: true,
                        maxDelay: 30000
                    }
                },
                
                forceJsonOutput: true,
                validateJsonOutput: true,
                maxJsonValidationRetries: 3,
                retryCount: 3,
                retryDelay: 1000,
                reqId: videoIdentifier
            };

            // 第3步：标准化进度报告
            this.reportLLMProgress('preparing', '准备LLM挖空请求', {
                current: 25,
                total: 100,
                inputSize: optimization.optimizedArray.length,
                tokenSavings: optimization.tokenSavings
            });

            this.reportLLMProgress('sending', '发送LLM挖空请求', {
                current: 40,
                total: 100,
                modelName: llmOptions.modelName
            });

            logger.info(`${execLogPrefix}[LLM调用详情] ========== LLM调用详细信息 ==========`);
            logger.info(`${execLogPrefix}[LLM调用详情] 任务类型: SUBTITLE_CLOZE_UTILS`);
            logger.info(`${execLogPrefix}[LLM调用详情] 模型: ${llmOptions.modelName}`);
            logger.info(`${execLogPrefix}[LLM调用详情] 温度: ${llmOptions.temperature}`);
            logger.info(`${execLogPrefix}[LLM调用详情] 最大令牌: ${llmOptions.max_tokens}`);
            logger.info(`${execLogPrefix}[LLM调用详情] Token优化: 节省${optimization.tokenSavings.savingsPercent}%`);
            logger.info(`${execLogPrefix}[LLM调用详情] 输入数据: ${optimization.optimizedArray.length}个字幕条目`);

            // 第4步：执行LLM调用
            this.reportLLMProgress('waiting', '等待LLM挖空处理', {
                current: 60,
                total: 100
            });

            const llmResult = await llmService.callLLM('SUBTITLE_CLOZE_UTILS', llmOptions);

            this.reportLLMProgress('receiving', '接收LLM挖空响应', {
                current: 80,
                total: 100,
                responseReceived: true
            });

            // 第5步：处理LLM响应
            if (llmResult && llmResult.status === 'success' && llmResult.processedText) {
                logger.info(`${execLogPrefix}[LLM响应] 挖空处理成功完成`);
                logger.info(`${execLogPrefix}[LLM响应] 使用模型: ${llmResult.modelUsed}`);
                if (llmResult.usage) {
                    logger.info(`${execLogPrefix}[LLM响应] Token使用: 输入${llmResult.usage.prompt_tokens}, 输出${llmResult.usage.completion_tokens}`);
                }

                // 解析JSON响应
                let clozedArray;
                try {
                    clozedArray = JSON.parse(llmResult.processedText);
                } catch (parseError) {
                    throw new Error(`LLM响应JSON解析失败: ${parseError.message}`);
                }

                // 验证响应格式
                if (!Array.isArray(clozedArray)) {
                    throw new Error('LLM返回的不是有效的JSON数组');
                }

                if (clozedArray.length !== optimization.optimizedArray.length) {
                    throw new Error(`LLM返回的数组长度不匹配: 期望${optimization.optimizedArray.length}, 实际${clozedArray.length}`);
                }

                // 验证每个条目的结构
                for (let i = 0; i < clozedArray.length; i++) {
                    const item = clozedArray[i];
                    if (!item.id || typeof item.start !== 'number' || typeof item.end !== 'number' || 
                        typeof item.text !== 'string' || !Array.isArray(item.words)) {
                        throw new Error(`条目${i+1}格式不正确: 缺少必需字段或类型错误`);
                    }
                }

                this.reportLLMProgress('parsing', '解析LLM挖空响应', {
                    current: 100,
                    total: 100,
                    parsedItems: clozedArray.length
                });

                logger.info(`${execLogPrefix}[LLM处理] 挖空处理成功，返回${clozedArray.length}个条目`);
                return clozedArray;

            } else {
                const errorMsg = `LLM挖空处理失败。状态: ${llmResult?.status}, 消息: ${llmResult?.message}`;
                logger.error(`${execLogPrefix}[LLM调用] ${errorMsg}`);
                throw new Error(errorMsg);
            }

        } catch (error) {
            logger.error(`${execLogPrefix}[LLM调用] 挖空处理过程中出错: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 标准化LLM进度报告
     * @param {string} stage - 进度阶段
     * @param {string} detail - 详细描述
     * @param {object} extraData - 额外数据
     */
    reportLLMProgress(stage, detail, extraData = {}) {
        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: detail,
            current: extraData.current || 50,
            total: extraData.total || 100,
            technicalDetail: `LLM ${stage} stage`,
            llmStage: stage,
            ...extraData
        });
    }

    /**
     * @功能概述: 将秒数转换为SRT标准时间格式（HH:MM:SS,ms）
     * @参数说明:
     *   - seconds {number} 输入秒数（支持浮点数，如123.456秒）
     * @返回值 {string} SRT兼容的时间字符串格式：HH:MM:SS,ms
     * @错误处理:
     *   - 当输入非数字/负数时返回默认值00:00:00,000
     *   - 记录警告日志帮助调试
     * @说明: 与TranslateSubtitleTask保持完全一致的实现
     */
    formatTime(seconds) {
        // 输入有效性校验（类型检查 + 数值范围）
        if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
            logger.warn(`[WARN] formatTime 输入无效秒数: ${seconds}`);
            return '00:00:00,000'; // 返回安全默认值，保证字幕文件结构完整
        }

        // 时间分解（使用UTC方法避免时区干扰）
        const date = new Date(seconds * 1000); // 转换为毫秒
        const hours = String(date.getUTCHours()).padStart(2, '0');   // 小时补零对齐
        const minutes = String(date.getUTCMinutes()).padStart(2, '0'); // 分钟补零
        const sec = String(date.getUTCSeconds()).padStart(2, '0');    // 秒数补零
        const ms = String(date.getUTCMilliseconds()).padStart(3, '0'); // 毫秒强制3位

        // 格式组装（符合SRT标准格式要求）
        return `${hours}:${minutes}:${sec},${ms}`;
    }

    /**
     * @功能概述: 保存挖空后字幕JSON文件
     * @param {Array} clozedSubtitleJsonArray - 挖空后的字幕JSON数组
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} savePath - 保存路径
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveClozedSubtitleJson(clozedSubtitleJsonArray, videoIdentifier, savePath, execLogPrefix) {
        try {
            // 生成时间戳确保文件名唯一性
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `${videoIdentifier}_clozed_subtitle_${timestamp}.json`;
            const filePath = fileSaver.saveDataToFile(
                JSON.stringify(clozedSubtitleJsonArray, null, 2),
                fileName,
                savePath,
                `${execLogPrefix}[保存挖空JSON]`
            );

            if (!filePath) {
                throw new Error(`保存挖空后字幕JSON文件失败: ${fileName}`);
            }

            logger.debug(`${execLogPrefix}[文件保存] 挖空后字幕JSON文件已保存: ${filePath}`);
            return filePath;

        } catch (error) {
            logger.error(`${execLogPrefix}[文件保存] 保存挖空后字幕JSON文件时出错: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 生成挖空后的SRT字幕内容
     * @param {Array} clozedSubtitleJsonArray - 挖空后的字幕JSON数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {string} SRT格式的字幕内容
     */
    generateClozeSRT(clozedSubtitleJsonArray, execLogPrefix) {
        try {
            // 输入类型验证
            if (!Array.isArray(clozedSubtitleJsonArray)) {
                const errorMsg = '生成挖空SRT失败：输入的不是有效的字幕 JSON 数组。';
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                throw new Error(errorMsg);
            }

            // 处理空数组的特殊情况
            if (clozedSubtitleJsonArray.length === 0) {
                logger.warn(`${execLogPrefix}[WARN] 挖空字幕 JSON 数组为空，将生成空的 SRT 内容。`);
                return '';
            }

            let srtContent = '';
            clozedSubtitleJsonArray.forEach((item) => {
                // 字段格式验证（与TranslateSubtitleTask保持一致）
                if (!item || typeof item.id !== 'string' || typeof item.start !== 'number' ||
                    typeof item.end !== 'number' || typeof item.text !== 'string') {
                    logger.warn(`${execLogPrefix}[WARN][generateClozeSRT] 发现无效条目: ${JSON.stringify(item).substring(0, 100)}... 已跳过`);
                    return; // 跳过无效条目，保持处理流程继续
                }

                // SRT块构建（与TranslateSubtitleTask保持一致的格式）
                srtContent += `${item.id}\r\n`; // ID行
                srtContent += `${this.formatTime(item.start)} --> ${this.formatTime(item.end)}\r\n`; // 时间轴行
                srtContent += `${item.text.trim()}\r\n\r\n`; // 文本行 + 双CRLF分隔符
            });

            // 统一处理末尾空行
            if (srtContent.endsWith('\r\n\r\n')) {
                srtContent = srtContent.slice(0, -2); // 移除最后两个CRLF字符
            }

            // 最终内容类型校验
            if (typeof srtContent !== 'string') {
                const errorMsg = '生成的挖空SRT内容无效';
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                throw new Error(errorMsg);
            }

            logger.debug(`${execLogPrefix}[SRT生成] 成功生成挖空SRT内容，长度: ${srtContent.length}字符`);
            return srtContent;

        } catch (error) {
            logger.error(`${execLogPrefix}[SRT生成] 生成挖空SRT内容时出错: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 保存挖空后的SRT文件
     * @param {string} clozedSrtContent - 挖空后的SRT内容
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} savePath - 保存路径
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveClozedSRT(clozedSrtContent, videoIdentifier, savePath, execLogPrefix) {
        try {
            // 生成时间戳确保文件名唯一性
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `${videoIdentifier}_clozed_en_${timestamp}.srt`;
            const filePath = fileSaver.saveDataToFile(
                clozedSrtContent,
                fileName,
                savePath,
                `${execLogPrefix}[保存挖空SRT]`
            );

            if (!filePath) {
                throw new Error(`保存挖空后SRT文件失败: ${fileName}`);
            }

            logger.debug(`${execLogPrefix}[文件保存] 挖空后SRT文件已保存: ${filePath}`);
            return filePath;

        } catch (error) {
            logger.error(`${execLogPrefix}[文件保存] 保存挖空后SRT文件时出错: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 收集详细上下文信息（基于TaskBase标准）
     * @说明: 遵循TaskBase.js标准，提供字幕挖空任务的详细上下文信息
     * @返回: {object} 包含任务详细信息的上下文对象
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取挖空信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                simplifiedSubtitleJsonReceived: taskResult.clozedSubtitleJsonArray ? true : false,
                subtitleEntriesCount: taskResult.clozedSubtitleJsonArray?.length || 'N/A',
                correctedFullTextReceived: taskResult.correctedFullText ? true : false,
                correctedFullTextLength: taskResult.correctedFullText ? taskResult.correctedFullText.length : 0,
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'simplified_subtitle_json_array',
                inputSource: 'transcription_correction_system'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                clozedSubtitleJsonArray: taskResult.clozedSubtitleJsonArray || 'N/A',
                clozedEntriesCount: taskResult.clozedSubtitleJsonArray ? taskResult.clozedSubtitleJsonArray.length : 0,
                clozedSubtitleJsonPath: taskResult.clozedSubtitleJsonPath || 'N/A',
                clozedEnglishSrtPath: taskResult.clozedEnglishSrtPath || 'N/A',
                clozedEnglishSrtContent: taskResult.clozedEnglishSrtContent ? 'generated' : 'N/A',
                clozedEnglishSrtLength: taskResult.clozedEnglishSrtContent ? taskResult.clozedEnglishSrtContent.length : 0,
                clozeQuality: 'llm_enhanced',
                outputFormat: 'multiple_formats',
                processingSuccess: taskResult.subtitleClozeTaskStatus === 'success'
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'SubtitleCloze',
                llmProvider: 'Google Gemini',
                llmModel: 'google/gemini-2.5-flash-preview-05-20',
                supportedInputFormats: ['simplified_subtitle_json_array'],
                outputFormat: 'clozed_subtitle',
                processingMode: 'llm_cloze',
                timeout: 60000,
                timeoutManagement: 'request_timeout',
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                retryEnabled: true,
                tokenOptimizationEnabled: true,
                apiEnhancementsEnabled: true
            };

            // 字幕挖空特定信息
            const subtitleClozeDetails = {
                processingSteps: [
                    '参数验证',
                    'Token优化预处理',
                    'LLM挖空处理',
                    '挖空结果验证',
                    '挖空JSON保存',
                    'SRT生成',
                    '文件保存'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '文件保存' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中',
                stepProgress: this.status === TASK_STATUS.COMPLETED ? '7/7' :
                            this.status === TASK_STATUS.FAILED ? 'N/A' : 'N/A',
                processingMethod: 'llm_cloze_with_token_optimization',
                qualityLevel: 'high',
                clozeStrategy: 'context_aware_blanking',
                sourceLanguage: 'English',
                targetLanguage: 'English (with blanks)'
            };

            logger.debug(`${logPrefix} 成功收集详细上下文信息，包含基础和挖空特定信息`);

            // 返回完整的上下文信息
            return {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                subtitleClozeDetails,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'SubtitleClozeTask.collectDetailedContext'
            };

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                subtitleClozeError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'SubtitleClozeTask.collectDetailedContext (with error)'
            };
        }
    }
}

module.exports = SubtitleClozeTask;
logger.info(`${taskModuleLogPrefix}SubtitleClozeTask 类已导出。`); // 日志：确认类已导出 