# 文件目录架构重构方案

## 📋 背景和需求

### 当前问题
- 上传视频接口和生成视频接口共用相同的保存目录
- 原始视频、处理文件、生成视频混合存储，管理混乱
- 前端需要获取不同类型文件的清晰链接结构

### 业务需求
1. **上传视频接口**：存储原始视频，产出音频、转录、字幕等处理文件
2. **生成视频接口**：基于原始视频制作短视频，产出最终视频文件
3. **前端需求**：获取分类清晰的文件链接，便于进一步操作

---

## 🏗️ 新架构设计

### 目录结构
```
backend/uploads/
├── projects/                    # 项目主目录
│   └── {videoIdentifier}/       # 每个视频一个项目目录
│       ├── source/              # 原始文件目录
│       │   └── original.mp4     # 原始上传视频
│       ├── processed/           # 处理产出文件目录
│       │   ├── audio.mp3        # 提取的音频文件
│       │   ├── transcription.json # 转录数据文件
│       │   ├── english.srt      # 英文字幕文件
│       │   ├── chinese.srt      # 中文字幕文件（如有）
│       │   └── optimized.json   # 优化数据文件
│       └── generated/           # 生成视频目录
│           ├── video_v1.mp4     # 生成的短视频版本1
│           ├── video_v2.mp4     # 生成的短视频版本2
│           └── subtitles.ass    # ASS字幕文件
└── temp/                        # 临时文件目录
    └── {sessionId}/             # 临时处理文件
```

### 目录说明
- **projects/{videoIdentifier}**：每个视频独立的项目目录
- **source/**：存放原始上传的视频文件
- **processed/**：存放上传流水线处理产出的文件
- **generated/**：存放生成流水线产出的视频文件
- **temp/**：存放临时处理文件，可定期清理

---

## ⚙️ 配置层设计

### 环境变量配置
```env
# 基础上传目录
UPLOAD_DIR=./uploads
```

### config/index.js 修改
```javascript
const config = {
    // 基础上传目录
    baseUploadDir: process.env.UPLOAD_DIR ? 
        path.resolve(path.join(__dirname, '../..', process.env.UPLOAD_DIR)) : 
        path.join(__dirname, '../../uploads'),
    
    // 项目目录结构配置
    projectDirs: {
        projects: 'projects',      # 项目主目录名
        source: 'source',          # 原始文件目录名
        processed: 'processed',    # 处理文件目录名
        generated: 'generated',    # 生成文件目录名
        temp: 'temp'              # 临时文件目录名
    }
};
```

---

## 🔧 工具函数设计

### utils/pathHelper.js（新建）
```javascript
const path = require('path');
const fs = require('fs');
const config = require('../config');

class PathHelper {
    /**
     * 获取项目根目录
     */
    static getProjectDir(videoIdentifier) {
        return path.join(config.baseUploadDir, config.projectDirs.projects, videoIdentifier);
    }
    
    /**
     * 获取原始文件目录
     */
    static getSourceDir(videoIdentifier) {
        return path.join(this.getProjectDir(videoIdentifier), config.projectDirs.source);
    }
    
    /**
     * 获取处理文件目录
     */
    static getProcessedDir(videoIdentifier) {
        return path.join(this.getProjectDir(videoIdentifier), config.projectDirs.processed);
    }
    
    /**
     * 获取生成文件目录
     */
    static getGeneratedDir(videoIdentifier) {
        return path.join(this.getProjectDir(videoIdentifier), config.projectDirs.generated);
    }
    
    /**
     * 创建项目目录结构
     */
    static createProjectDirs(videoIdentifier) {
        const sourceDir = this.getSourceDir(videoIdentifier);
        const processedDir = this.getProcessedDir(videoIdentifier);
        const generatedDir = this.getGeneratedDir(videoIdentifier);
        
        fs.mkdirSync(sourceDir, { recursive: true });
        fs.mkdirSync(processedDir, { recursive: true });
        fs.mkdirSync(generatedDir, { recursive: true });
        
        return { sourceDir, processedDir, generatedDir };
    }
    
    /**
     * 生成文件访问URL
     */
    static generateFileUrl(videoIdentifier, category, filename) {
        return `http://localhost:3000/api/files/projects/${videoIdentifier}/${category}/${filename}`;
    }
}

module.exports = PathHelper;
```

---

## 🎮 控制器层修改

### uploadVideoController.js 修改要点
```javascript
const PathHelper = require('../../utils/pathHelper');

// 在文件上传处理后
const { sourceDir, processedDir } = PathHelper.createProjectDirs(videoIdentifier);

// 移动原始文件到source目录
const finalVideoPath = path.join(sourceDir, 'original.mp4');
fs.renameSync(uploadedVideoFullPath, finalVideoPath);

// 流水线上下文
const context = {
    reqId,
    videoIdentifier,
    originalVideoPath: finalVideoPath,
    savePath: processedDir,  // 处理文件保存到processed目录
    originalVideoName: req.file.originalname,
    uploadedVideoDirPath: sourceDir
};
```

### generateVideoController.js 修改要点
```javascript
const PathHelper = require('../../utils/pathHelper');

// 使用现有项目的generated目录
const generatedDir = PathHelper.getGeneratedDir(videoIdentifier);
fs.mkdirSync(generatedDir, { recursive: true });

const context = {
    reqId,
    videoIdentifier,
    savePath: generatedDir,  // 生成文件保存到generated目录
    // ...其他参数
};
```

---

## 🌐 静态文件服务配置

### app.js 修改
```javascript
// 配置项目文件的静态访问
app.use('/api/files/projects', express.static(path.join(__dirname, '..', 'uploads', 'projects')));

// 保留原有的uploads访问（向后兼容）
app.use('/backend/uploads', express.static(path.join(__dirname, '..', 'uploads')));
```

### URL访问结构
```
# 原始视频
http://localhost:3000/api/files/projects/{videoIdentifier}/source/original.mp4

# 处理文件
http://localhost:3000/api/files/projects/{videoIdentifier}/processed/audio.mp3
http://localhost:3000/api/files/projects/{videoIdentifier}/processed/english.srt
http://localhost:3000/api/files/projects/{videoIdentifier}/processed/transcription.json

# 生成视频
http://localhost:3000/api/files/projects/{videoIdentifier}/generated/video_v1.mp4
```

---

## 📋 返回数据结构设计

### 上传接口返回格式
```json
{
    "status": "completed",
    "videoIdentifier": "video-123",
    "files": {
        "source": {
            "originalVideo": "http://localhost:3000/api/files/projects/video-123/source/original.mp4"
        },
        "processed": {
            "audio": "http://localhost:3000/api/files/projects/video-123/processed/audio.mp3",
            "transcription": "http://localhost:3000/api/files/projects/video-123/processed/transcription.json",
            "englishSrt": "http://localhost:3000/api/files/projects/video-123/processed/english.srt",
            "chineseSrt": "http://localhost:3000/api/files/projects/video-123/processed/chinese.srt",
            "optimized": "http://localhost:3000/api/files/projects/video-123/processed/optimized.json"
        }
    }
}
```

### 生成接口返回格式
```json
{
    "status": "completed",
    "videoIdentifier": "video-123",
    "version": "v1",
    "files": {
        "generated": {
            "video": "http://localhost:3000/api/files/projects/video-123/generated/video_v1.mp4",
            "subtitles": "http://localhost:3000/api/files/projects/video-123/generated/subtitles.ass"
        }
    }
}
```

---

## 🔄 Task层兼容性

### 无需修改现有Task
- 所有Task继续使用 `context.savePath` 参数
- 由控制器层决定具体的保存目录
- 保持Task的通用性和复用性

### 流水线层透传
- VideoProcessingPipelineService 无需修改
- 继续透传 `savePath` 给各个Task
- 保持流水线的通用性

---

## ✅ 方案优势

### 1. 架构清晰
- 按项目组织，每个视频独立目录
- 按文件类型分类，便于管理和查找
- 目录结构一目了然

### 2. 业务逻辑清晰
- source: 原始上传文件
- processed: 上传流水线产出
- generated: 生成流水线产出
- 职责分离明确

### 3. 前端友好
- URL结构清晰规范
- 文件分类明确
- 便于缓存和版本管理

### 4. 扩展性强
- 支持多版本视频生成
- 支持文件版本管理
- 便于添加新的文件类型

### 5. 代码复用性好
- Task层无需修改
- 流水线层无需修改
- 只需修改控制器和配置

---

## 🚀 实施计划

### ✅ 阶段1：基础架构（已完成）
1. ✅ 创建 PathHelper 工具类
2. ✅ 修改配置文件
3. ✅ 更新静态文件服务

### ✅ 阶段2：控制器修改（已完成）
1. ✅ 修改 uploadVideoController
2. ✅ 修改 generateVideoController
3. ⏳ 更新返回数据结构（待前端测试）

### ✅ 阶段3：测试验证（已完成）
1. ✅ 测试目录创建
2. ✅ 测试路径生成
3. ✅ 验证URL生成

### ⏳ 阶段4：文档更新（进行中）
1. ✅ 更新架构文档
2. ⏳ 更新API文档
3. ⏳ 更新前端对接文档

## 🎯 实施完成状态

### ✅ 已完成的修改
- **新建文件**：`backend/src/utils/pathHelper.js`
- **修改配置**：`backend/src/config/index.js`
- **修改控制器**：`uploadVideoController.js`, `generateVideoController.js`
- **修改静态服务**：`backend/src/app.js`
- **修改环境变量**：`backend/.env`

### ✅ 测试验证结果
- 目录结构创建：✅ 成功
- 路径生成：✅ 正确
- URL生成：✅ 标准化
- 配置读取：✅ 正常
- 向后兼容：✅ 保持

---

## 📝 注意事项

### 1. 向后兼容
- 保留原有的 `/backend/uploads` 访问路径
- 现有文件不受影响

### 2. 权限管理
- 确保所有目录都有正确的读写权限
- 考虑文件安全访问控制

### 3. 清理策略
- 定期清理temp目录
- 考虑项目文件的生命周期管理

### 4. 性能考虑
- 大量项目目录的性能影响
- 文件索引和搜索优化
