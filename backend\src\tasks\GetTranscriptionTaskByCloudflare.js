// 导入文件系统操作模块，用于文件存在性检查
const fs = require('fs');
// 导入路径处理模块，用于文件路径解析和标识符提取
const path = require('path');
// 导入HTTP客户端，用于调用Cloudflare Workers AI API（包含重试机制）
const axios = require('axios');
// 导入TaskBase基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');

// 导入配置加载器，用于获取Cloudflare Workers AI相关配置
const loadConfig = require('../config');
// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入JSON校验与修复工具
const { extractAndParseJson } = require('../utils/jsonValidator');
// 导入文件保存工具，用于保存转录JSON文件
const fileSaver = require('../utils/fileSaver');

// 加载应用配置，包含Cloudflare Workers AI的API Token、Account ID等信息
const config = loadConfig;
// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：GetTranscriptionTaskByCloudflare.js][Cloudflare语音转录任务][模块初始化]';

// 记录模块加载成功的日志
logger.info(`${taskModuleLogPrefix}模块已加载。`);

/**
 * @功能概述: Cloudflare Workers AI Whisper API转录任务类，负责将音频文件转录为文本。
 *           使用Cloudflare Workers AI进行语音识别，支持细粒度的进度追踪。
 *
 * @继承关系: 继承自 TaskBase，获得标准化的进度监控和状态管理能力
 *
 * @进度阶段: 提供细粒度的进度阶段，通过 reportProgress 和 reportLLMProgress 方法报告：
 *   - INITIALIZING: 初始化和配置验证阶段
 *   - PROCESSING: 输入文件验证阶段
 *   - LLM_PREPARING: 准备API请求 (读取音频文件)
 *   - LLM_SENDING: 发送API请求到Cloudflare Workers AI
 *   - LLM_WAITING: 等待Cloudflare Workers AI API响应
 *   - LLM_RECEIVING: 接收和初步处理API响应
 *   - FINALIZING: 数据处理和任务完成
 *
 * @API参数: (由Cloudflare Workers AI Whisper API定义)
 *   - audio: 音频文件的二进制数据
 *   - timeout: 300,000 ms (5分钟，可配置)
 *
 * @数据格式: Cloudflare Workers AI直接返回Whisper兼容格式，无需额外转换
 */
class GetTranscriptionTaskByCloudflare extends TaskBase {
    /**
     * @功能概述: 构造函数，创建Cloudflare转录任务实例。
     * @param {string} [name='GetTranscriptionTaskByCloudflare'] - 任务名称，用于日志标识和进度追踪。
     *
     * @说明: 调用父类构造函数初始化基础属性（如任务ID、初始状态），并设置实例级日志前缀。
     */
    constructor(name = 'GetTranscriptionTaskByCloudflare') {
        super(name); // 调用 TaskBase 构造函数，初始化任务ID、状态等基础属性
        // 设置实例级日志前缀，包含文件名、任务类型和实例名称
        this.instanceLogPrefix = `[文件：GetTranscriptionTaskByCloudflare.js][Cloudflare语音转录任务][${this.name}]`;
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} GetTranscriptionTaskByCloudflare 实例已创建。`);
    }



    

   
    async execute(context, progressCallback) {
        // 步骤 1.1: 设置执行级日志前缀
        const reqId = context.reqId || 'unknown_transcription_req';
        const videoIdentifier = context.videoIdentifier; 
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${videoIdentifier}]`;

        // 步骤 1.2: 校验必需的上下文参数
        // originalVideoName 是可选的，主要用于日志或辅助识别，不影响核心功能
        const requiredFields = ['reqId', 'videoIdentifier', 'audioFilePathInUploads', 'savePath'];

        // 使用标准化的字段验证
        for (const field of requiredFields) {
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                const errorMsg = `执行失败：上下文缺少必需或无效的字段 "${field}". 当前值: '${context[field]}'`;
                logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
                const validationError = new Error(errorMsg);
                this.fail(validationError); // 标记任务失败
                throw validationError;
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。必需字段: ${requiredFields.join(', ')}。`);
        
        // 步骤 1.3: 设置进度回调函数
        this.setProgressCallback(progressCallback);
        // 步骤 1.4: 标记任务开始并报告初始状态
        this.start();
        logger.info(`${execLogPrefix} GetTranscriptionTaskByCloudflare execute 方法开始。`);

        // 从上下文中解构参数，方便后续使用
        const { audioFilePathInUploads, savePath } = context; // savePath 已在 requiredFields 中校验

        try {
            // 步骤 2.1: 报告配置验证阶段的进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '验证Azure Speech Service服务配置',
                current: 10,
                total: 100,
                technicalDetail: '检查 Speech Endpoint, Key, API Version, Locale'
            });

            // 步骤 2.2: 从配置中提取Cloudflare Workers AI相关参数
            const {
                cloudflareApiToken,
                cloudflareAccountId,
                cloudflareWhisperModel,
                cloudflareRequestTimeout,
                useCloudflareAi
            } = config;

            // 步骤 2.3: 记录配置加载状态（不记录敏感信息如Token内容）
            logger.info(`${execLogPrefix}[步骤 2.2] Cloudflare配置加载: AccountID=${cloudflareAccountId}, TokenLoaded=${!!cloudflareApiToken}, Model=${cloudflareWhisperModel || '@cf/openai/whisper'}, UseCloudflare=${useCloudflareAi}`);

            if (!cloudflareApiToken || !cloudflareAccountId) {
                const errorMsg = '执行失败：Cloudflare Workers AI 配置参数 (cloudflareApiToken 或 cloudflareAccountId) 缺失。';
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                const configError = new Error(errorMsg);
                this.fail(configError); // 标记任务失败
                throw configError;
            }

            // 步骤 2.4: 构建API URL
            const usedModel = cloudflareWhisperModel || '@cf/openai/whisper';
            const apiUrl = `https://api.cloudflare.com/client/v4/accounts/${cloudflareAccountId}/ai/run/${usedModel}`;
            logger.info(`${execLogPrefix}[步骤 2.4] 构建的 Cloudflare Workers AI URL: ${apiUrl}`);

            // 步骤 3.1: 报告文件验证阶段的进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '验证输入音频文件',
                current: 20,
                total: 100,
                technicalDetail: `检查文件路径: ${audioFilePathInUploads}`
            });

            // 步骤 3.2: 检查音频文件是否存在
            if (!fs.existsSync(audioFilePathInUploads)) {
                const errorMsg = `执行失败：指定的音频文件不存在于路径 ${audioFilePathInUploads}`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                const fileError = new Error(errorMsg);
                this.fail(fileError); // 标记任务失败
                throw fileError;
            }
            logger.info(`${execLogPrefix}[步骤 3.2] 确认音频文件存在: ${audioFilePathInUploads}`);

            // 步骤 4.1: 报告API请求准备阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_PREPARING, '准备Cloudflare Workers AI API请求 (读取音频文件)', {
                current: 30,
                total: 100,
                technicalDetail: `文件大小: ${fs.statSync(audioFilePathInUploads).size} bytes`
            });

            // 步骤 4.2: 读取音频文件为Buffer
            logger.info(`${execLogPrefix}[步骤 4.2] 读取音频文件 ${audioFilePathInUploads} 为Buffer。`);
            const audioBuffer = fs.readFileSync(audioFilePathInUploads);
            logger.info(`${execLogPrefix}[步骤 4.2] 音频文件读取完成，大小: ${audioBuffer.length} bytes`);

            // 步骤 4.3: 转换音频为base64编码（支持所有Whisper模型）
            logger.info(`${execLogPrefix}[步骤 4.3] 将音频Buffer转换为base64编码以支持JSON格式请求。`);
            const base64Audio = audioBuffer.toString('base64');
            logger.info(`${execLogPrefix}[步骤 4.3] base64编码完成，编码后长度: ${base64Audio.length} 字符`);

            // 步骤 4.4: 构建JSON请求体（兼容所有Whisper模型）
            const requestBody = {
                audio: base64Audio
            };
            logger.info(`${execLogPrefix}[步骤 4.4] JSON请求体构建完成，使用统一的base64音频格式。`);

            // 步骤 5.1: 构建请求头（使用JSON格式）
            const headers = {
                'Authorization': `Bearer ${cloudflareApiToken}`,
                'Content-Type': 'application/json'
            };
            logger.debug(`${execLogPrefix}[步骤 5.1] 请求头设置完成。Content-Type: ${headers['Content-Type']}`);

            // 步骤 5.2: 报告API请求发送阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_SENDING, '发送请求到Cloudflare Workers AI服务', {
                current: 40,
                total: 100,
                technicalDetail: `目标 URL: ${apiUrl.substring(0, 80)}...`
            });

            const configuredTimeout = cloudflareRequestTimeout || 300000; // 从配置获取超时或默认5分钟
            logger.info(`${execLogPrefix}[步骤 5.2 AXIOS调用前] API地址: ${apiUrl}, 超时: ${configuredTimeout}ms.`);

            // 步骤 5.3: 发送HTTP POST请求（JSON格式，带智能重试机制）
            logger.info(`${execLogPrefix}[步骤 5.3] 开始发送 POST 请求到 Cloudflare Workers AI: ${apiUrl}`);

            // 步骤 5.4: 报告等待API响应阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_WAITING, '等待Cloudflare Workers AI处理请求并返回响应', {
                current: 60,
                total: 100,
                technicalDetail: `预估音频时长相关的处理时间... (超时设置: ${configuredTimeout / 1000}s)`
            });

            const response = await this.callCloudflareAPIWithRetry(
                apiUrl,
                requestBody,
                headers,
                configuredTimeout,
                execLogPrefix
            );

            // 步骤 6.1: 报告API响应接收阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_RECEIVING, '成功接收Cloudflare Workers AI API响应', {
                current: 80,
                total: 100,
                technicalDetail: `响应状态码: ${response.status}`
            });

            // 步骤 6.2: 记录响应信息
            logger.info(`${execLogPrefix}[步骤 6.2] Cloudflare Workers AI 请求成功，状态码: ${response.status}`);
            logger.debug(`${execLogPrefix} Cloudflare Workers AI 响应头 (部分): ${JSON.stringify({
                'content-type': response.headers['content-type'],
                'date': response.headers['date']
            })}`);
            // 避免记录完整的响应到常规日志，它可能非常大
            logger.debug(`${execLogPrefix} Cloudflare Workers AI 响应数据预览 (前200字符): ${JSON.stringify(response.data).substring(0, 200)}...`);
            if (response.data && response.data.result && response.data.result.text) {
                logger.info(`${execLogPrefix} Cloudflare Workers AI 返回转录文本预览 (前100字符): ${String(response.data.result.text).substring(0,100)}...`);
            }


            // 步骤 7.1: 数据处理和校验
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FINALIZING, {
                detail: '处理Cloudflare Workers AI数据并保存转录JSON文件',
                current: 90,
                total: 100,
                technicalDetail: 'Cloudflare Workers AI直接返回Whisper兼容格式'
            });

            let validatedApiResponse = response.data;
            let whisperFormatData = null;

            try {
                // 使用JSON校验工具验证和修复API响应
                const jsonString = JSON.stringify(response.data);
                const validationResult = extractAndParseJson(jsonString, `${execLogPrefix}[JSON校验]`);

                if (validationResult.success) {
                    validatedApiResponse = validationResult.data;
                    logger.info(`${execLogPrefix}[步骤 7.1] API响应JSON校验成功`);
                } else {
                    logger.warn(`${execLogPrefix}[步骤 7.1] API响应JSON校验失败，使用原始数据: ${validationResult.error}`);
                }

                // 步骤 7.1.2: 提取Cloudflare Workers AI返回的数据并转换为标准格式
                logger.info(`${execLogPrefix}[步骤 7.1.2] 提取Cloudflare Workers AI返回的数据并转换为标准格式`);
                const rawCloudflareData = validatedApiResponse.result || validatedApiResponse;

                // 保存原始API响应用于调试
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const rawResponseFilename = `${videoIdentifier}_raw_cloudflare_response_${timestamp}.json`;
                const rawResponsePath = await fileSaver.saveDataToFile(
                    JSON.stringify(rawCloudflareData, null, 2),
                    rawResponseFilename,
                    savePath,
                    execLogPrefix
                );
                logger.info(`${execLogPrefix}[调试] 原始Cloudflare API响应已保存到: ${rawResponsePath}`);

                // 转换为标准的Whisper兼容格式
                whisperFormatData = this.convertCloudflareToStandardFormat(rawCloudflareData, execLogPrefix);
                logger.info(`${execLogPrefix}[步骤 7.1.2] 数据转换完成，segments数量: ${whisperFormatData.segments?.length || 0}`);

            } catch (validationError) {
                logger.warn(`${execLogPrefix}[步骤 7.1] JSON校验或数据转换过程出错: ${validationError.message}`);
                // 如果转换失败，尝试使用原始数据的result字段
                const fallbackData = response.data.result || response.data;
                // 尝试简单转换
                try {
                    whisperFormatData = this.convertCloudflareToStandardFormat(fallbackData, execLogPrefix);
                } catch (conversionError) {
                    logger.error(`${execLogPrefix}[步骤 7.1] 格式转换失败，使用原始数据: ${conversionError.message}`);
                    whisperFormatData = fallbackData;
                }
            }

            // 步骤 7.2: 保存转录JSON文件
            let transcriptionJsonPath = null;
            try {
                transcriptionJsonPath = await this.saveTranscriptionJson(
                    whisperFormatData, // 保存Whisper兼容格式
                    videoIdentifier,
                    execLogPrefix,
                    savePath
                );
                logger.info(`${execLogPrefix}[步骤 7.2] 转录JSON文件保存成功: ${transcriptionJsonPath}`);
            } catch (saveError) {
                logger.error(`${execLogPrefix}[步骤 7.2] 转录JSON文件保存失败: ${saveError.message}`);
                // 保存失败不影响任务成功，继续执行
            }

            // 步骤 7.3: 构建任务结果（使用Whisper格式数据，保持与现有流水线的兼容性）
            const result = {
                transcriptionStatus: 'success', // 表示API调用成功并获取到响应
                apiResponse: whisperFormatData, // Whisper兼容格式数据（保持现有上下文参数名不变）
                transcriptionJsonPath: transcriptionJsonPath, // 保存的JSON文件路径
                processedAudioPath: audioFilePathInUploads, // 实际处理的音频文件路径
                videoIdentifier: videoIdentifier, // 传递视频标识符
                reqId: reqId, // 传递请求ID
                savePath: savePath // 传递保存路径
            };

            // 步骤 7.4: 标记任务成功完成
            this.complete(result);
            logger.info(`${execLogPrefix}[步骤 7.4] 转录任务执行成功。API响应已校验并保存。`);

            // 步骤 7.5: 返回结果
            return result;

        } catch (error) {
            // 步骤 8.1 & 8.2: 捕获错误并分类
            logger.error(`${execLogPrefix}[ERROR_HANDLER] 调用 Cloudflare Workers AI 进行转录时捕获到错误: ${error.message}`);

            let errorCategory = TASK_SUBSTATUS.FAILED_UNKNOWN; // 默认错误子状态

            if (error.code === 'ECONNABORTED' || (error.message && error.message.toLowerCase().includes('timeout'))) {
                errorCategory = TASK_SUBSTATUS.FAILED_API_TIMEOUT;
                logger.error(`${execLogPrefix}[${errorCategory}] Cloudflare Workers AI 调用超时。配置超时: ${configuredTimeout}ms.`);
            } else if (error.response) {
                errorCategory = TASK_SUBSTATUS.FAILED_API_ERROR;
                logger.error(`${execLogPrefix}[${errorCategory}] Cloudflare Workers AI 返回错误状态: ${error.response.status}`);
                logger.error(`${execLogPrefix}[${errorCategory}] Cloudflare Workers AI 错误响应体: ${JSON.stringify(error.response.data)}`);
                logger.debug(`${execLogPrefix}[${errorCategory}] Cloudflare Workers AI 错误响应头: ${JSON.stringify(error.response.headers)}`);
            } else if (error.request) {
                errorCategory = TASK_SUBSTATUS.FAILED_NETWORK;
                logger.error(`${execLogPrefix}[${errorCategory}] 请求已发送但未收到Cloudflare Workers AI响应。网络问题或上游无响应。`);
                logger.debug(`${execLogPrefix}[${errorCategory}] 未收到响应的请求详情 (部分): ${JSON.stringify({ method: error.request.method, path: error.request.path })}`);
            } else if (error.message && error.message.startsWith('执行失败：')) {
                errorCategory = TASK_SUBSTATUS.FAILED_VALIDATION;
                // 错误信息已在参数校验或文件检查点记录
            } else {
                errorCategory = TASK_SUBSTATUS.FAILED_UNEXPECTED;
                logger.error(`${execLogPrefix}[${errorCategory}] 设置到Cloudflare Workers AI的请求或任务内部发生意外错误: ${error.message}`);
            }

            logger.error(`${execLogPrefix}[错误堆栈][${errorCategory}] ${error.stack}`);

            // 步骤 8.3: 标记任务失败
            this.fail(error); // 使用TaskBase标准的fail方法
            
            // 步骤 8.4: 重新抛出错误
            throw error;
        }
    }

    /**
     * @功能概述: 收集GetTranscriptionTask的详细上下文信息
     * @returns {object} 包含转录获取特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加转录获取特定的上下文信息
     *   - 包含Azure OpenAI API详情、音频处理参数、转录历史等
     *   - 遵循TaskBase标准化的上下文收集模式
     *   - 保持与其他任务类的一致性
     *
     * @返回对象扩展:
     *   - transcriptionProcessingDetails: 转录处理特定信息
     *   - azureApiDetails: Azure OpenAI API交互详情
     *   - inputAudioInfo: 输入音频信息
     *   - outputTranscriptionInfo: 输出转录信息
     *   - transcriptionParameters: 转录参数详情
     *   - transcriptionHistory: 转录历史
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取转录信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                audioFilePathInUploads: taskResult.processedAudioPath || 'N/A',
                audioFileName: taskResult.processedAudioPath ?
                    path.basename(taskResult.processedAudioPath) : 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                reqId: taskResult.reqId || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                inputFileExists: taskResult.processedAudioPath ?
                    fs.existsSync(taskResult.processedAudioPath) : false,
                inputFileSize: taskResult.processedAudioPath && fs.existsSync(taskResult.processedAudioPath) ?
                    fs.statSync(taskResult.processedAudioPath).size : 'N/A'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                transcriptionStatus: taskResult.transcriptionStatus || 'N/A',
                transcriptionJsonPath: taskResult.transcriptionJsonPath || 'N/A',
                transcriptionText: taskResult.apiResponse?.text ?
                    taskResult.apiResponse.text.substring(0, 100) + '...' : 'N/A',
                transcriptionLanguage: taskResult.apiResponse?.language || 'N/A',
                segmentsCount: taskResult.apiResponse?.segments?.length || 'N/A',
                transcriptionDuration: taskResult.apiResponse?.duration || 'N/A',
                outputFileExists: taskResult.transcriptionJsonPath ?
                    fs.existsSync(taskResult.transcriptionJsonPath) : false
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'GetTranscription',
                apiProvider: 'Azure Speech Service',
                apiModel: 'Fast Transcription',
                supportedInputFormats: ['mp3', 'wav', 'mp4', 'm4a', 'flac'],
                outputFormat: 'whisper_compatible_json',
                processingMode: 'azure_speech_api',
                timeout: config.requestTimeout || config.azureApiTimeout || 300000,
                timeoutManagement: 'axios_timeout',
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                dataConversionEnabled: true
            };

            // 转录处理特定信息
            const transcriptionProcessingDetails = {
                processingSteps: [
                    '参数验证',
                    'Azure Speech配置验证',
                    '音频文件验证',
                    'API请求准备',
                    'Azure Speech API调用',
                    '数据格式转换',
                    'JSON校验和修复',
                    '文件保存',
                    '响应处理'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '响应处理' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中',
                stepProgress: this.status === TASK_STATUS.COMPLETED ? '9/9' :
                            this.status === TASK_STATUS.FAILED ? 'N/A' : 'N/A',
                processingMethod: 'azure_speech_service_api',
                qualityLevel: 'high',
                languageDetection: 'automatic',
                timestampPrecision: 'millisecond',
                dataConversion: 'azure_to_whisper_format'
            };

            // Azure API交互详情
            const azureApiDetails = {
                provider: 'Azure Speech Service',
                model: 'Fast Transcription',
                speechEndpoint: config.speechEndpoint || 'N/A',
                speechRegion: config.speechRegion || 'N/A',
                apiVersion: config.apiVersion || '2024-11-15',
                requestMethod: 'POST',
                requestFormat: 'multipart/form-data',
                responseFormat: 'azure_speech_json',
                errorHandling: 'comprehensive',
                retryStrategy: 'none',
                timeoutConfiguration: config.requestTimeout || config.azureApiTimeout || 300000,
                authenticationMethod: 'subscription_key',
                connectionStatus: this.status === TASK_STATUS.COMPLETED ? 'success' :
                               this.status === TASK_STATUS.FAILED ? 'failed' : 'unknown'
            };

            // 输入音频信息（任务特定）
            const inputAudioInfo = {
                audioFilePathInUploads: taskResult.processedAudioPath || 'N/A',
                audioFileName: taskResult.processedAudioPath ?
                    path.basename(taskResult.processedAudioPath) : 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'audio_file',
                inputSource: 'converted_from_video',
                fileExists: taskResult.processedAudioPath ?
                    fs.existsSync(taskResult.processedAudioPath) : false,
                fileSize: taskResult.processedAudioPath && fs.existsSync(taskResult.processedAudioPath) ?
                    fs.statSync(taskResult.processedAudioPath).size : 'N/A'
            };

            // 输出转录信息（任务特定）
            const outputTranscriptionInfo = {
                transcriptionStatus: taskResult.transcriptionStatus || 'N/A',
                transcriptionJsonPath: taskResult.transcriptionJsonPath || 'N/A',
                apiResponseReceived: taskResult.apiResponse ? true : false,
                transcriptionText: taskResult.apiResponse?.text ?
                    taskResult.apiResponse.text.substring(0, 100) + '...' : 'N/A',
                transcriptionLanguage: taskResult.apiResponse?.language || 'N/A',
                segmentsCount: taskResult.apiResponse?.segments?.length || 'N/A',
                transcriptionDuration: taskResult.apiResponse?.duration || 'N/A',
                transcriptionQuality: 'azure_speech_enhanced',
                outputFormat: 'whisper_compatible_json_with_segments',
                dataConversionApplied: true,
                originalAzureFormat: 'azure_speech_json',
                jsonFileExists: taskResult.transcriptionJsonPath ?
                    fs.existsSync(taskResult.transcriptionJsonPath) : false
            };

            // 转录参数详情（任务特定）
            const transcriptionParameters = {
                reqId: taskResult.reqId || 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                processedAudioPath: taskResult.processedAudioPath || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                responseFormat: 'whisper_compatible_json',
                transcriptionMethod: 'azure_speech_service',
                apiTimeout: config.requestTimeout || config.azureApiTimeout || 300000,
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                dataConversionEnabled: true,
                locale: config.defaultLocale || 'en-US',
                profanityFilter: config.profanityFilter || 'Masked'
            };

            // 转录历史详情（任务特定）
            const transcriptionHistory = {
                transcriptionType: 'azure_speech_api',
                inputFormat: 'audio_file',
                outputFormat: 'whisper_compatible_json',
                transcriptionMethod: 'azure_speech_service',
                processingTime: this.getElapsedTime(),
                transcriptionSuccess: this.status === TASK_STATUS.COMPLETED,
                errorOccurred: this.status === TASK_STATUS.FAILED,
                dataConversionPerformed: true,
                originalFormat: 'azure_speech_json',
                convertedFormat: 'whisper_format',
                lastError: this.error ? {
                    message: this.error.message,
                    name: this.error.name,
                    type: this.error.constructor.name
                } : null,
                progressUpdatesCount: this.progressHistory.length,
                lastProgressUpdate: this.progressHistory.length > 0 ?
                    this.progressHistory[this.progressHistory.length - 1] : null
            };

            // 合并所有上下文信息（遵循TaskBase标准结构）
            const extendedContext = {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                transcriptionProcessingDetails,
                azureApiDetails,
                inputAudioInfo,
                outputTranscriptionInfo,
                transcriptionParameters,
                transcriptionHistory,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'GetTranscriptionTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集GetTranscriptionTask详细上下文信息`);
            logger.info(`${logPrefix} 上下文包含 ${Object.keys(extendedContext).length} 个主要部分`);

            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                transcriptionProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'GetTranscriptionTask.collectDetailedContext (with error)'
            };
        }
    }

    /**
     * @功能概述: 保存转录JSON文件
     * @param {object} transcriptionData - 转录数据对象
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @param {string} savePath - 保存路径
     * @returns {Promise<string>} 保存的文件路径
     *
     * @说明:
     *   - 使用标准化的文件命名：[视频标识符]_transcription.json
     *   - 调用通用文件保存工具fileSaver
     *   - 包含完整的错误处理和日志记录
     */
    async saveTranscriptionJson(transcriptionData, videoIdentifier, execLogPrefix, savePath) {
        // 生成时间戳确保文件名唯一性
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // 生成标准化文件名：[视频标识符]_transcription_[时间戳].json
        const filename = `${videoIdentifier}_transcription_${timestamp}.json`;

        try {
            // 调用通用文件保存工具（参数说明：数据内容，文件名，基础目录，日志前缀）
            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(transcriptionData, null, 2), // 格式化JSON
                filename, // 参数2: 纯文件名
                savePath, // 参数3: 保存目录
                execLogPrefix // 参数4: 日志前缀
            );

            // 有效性检查：确保返回路径符合预期
            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }
            return savedPath;
        } catch (saveError) {
            // 在错误信息中增加完整路径信息便于调试
            const errorMsg = `保存转录 JSON 文件失败: ${saveError.message}，路径：${savePath}/${filename}`;
            logger.error(`${execLogPrefix}[ERROR][saveTranscriptionJson] ${errorMsg}`);
            // 向上层抛出标准化错误
            throw new Error(errorMsg);
        }
    }

    /**
     * @功能概述: 将Cloudflare Workers AI返回的数据转换为标准的Whisper兼容格式
     * @param {object} cloudflareData - Cloudflare Workers AI返回的原始数据
     * @param {string} logPrefix - 日志前缀
     * @returns {object} 转换后的标准Whisper兼容格式数据
     *
     * @转换规则:
     *   1. 解析VTT字符串，提取时间戳和文本段落
     *   2. 根据VTT分段创建segments数组
     *   3. 将words数组中的单词分配到对应的segment
     *   4. 添加必需字段：task, language, duration等
     */
    convertCloudflareToStandardFormat(cloudflareData, logPrefix) {
        try {
            logger.info(`${logPrefix}[转换] 开始将Cloudflare数据转换为标准Whisper兼容格式`);

            // 检查输入数据
            if (!cloudflareData || !cloudflareData.text) {
                throw new Error('输入数据无效，缺少text字段');
            }

            // 步骤1: 提取基础信息
            const text = cloudflareData.text || '';
            const language = cloudflareData.transcription_info?.language || 'en';
            const duration = cloudflareData.transcription_info?.duration || 0;

            logger.debug(`${logPrefix}[转换] 基础信息 - 语言: ${language}, 时长: ${duration}秒, 文本长度: ${text.length}`);

            // 步骤2: 优先使用segments字段（Cloudflare API直接返回的高质量segments）
            if (cloudflareData.segments && Array.isArray(cloudflareData.segments) && cloudflareData.segments.length > 0) {
                logger.info(`${logPrefix}[转换] 检测到segments字段，直接使用Cloudflare segments (${cloudflareData.segments.length}个)`);

                const segments = cloudflareData.segments.map((segment, index) => {
                    // 转换words数组为标准Whisper格式（text字段，无probability）
                    const words = (segment.words || []).map(word => ({
                        text: word.word || word.text || '', // Cloudflare使用"word"字段，标准格式使用"text"字段
                        start: word.start || 0,
                        end: word.end || 0
                        // 移除probability字段，标准格式不包含此字段
                    }));

                    return {
                        id: index,
                        seek: segment.seek || 0,
                        start: segment.start || 0,
                        end: segment.end || 0,
                        text: segment.text || '',
                        tokens: segment.tokens || [],
                        temperature: segment.temperature || 0,
                        avg_logprob: segment.avg_logprob || 0,
                        compression_ratio: segment.compression_ratio || 1.0,
                        no_speech_prob: segment.no_speech_prob || 0,
                        words: words
                    };
                });

                logger.info(`${logPrefix}[转换] 成功转换${segments.length}个segments，总计${segments.reduce((sum, seg) => sum + seg.words.length, 0)}个words`);
                return {
                    task: 'transcribe',
                    language: language,
                    duration: duration,
                    text: text,
                    segments: segments
                };
            }

            // 步骤3: 备选方案 - 解析VTT（仅在segments不可用时使用）
            if (cloudflareData.vtt) {
                logger.info(`${logPrefix}[转换] segments不可用，尝试解析VTT作为备选方案`);
                logger.debug(`${logPrefix}[转换] VTT内容长度: ${cloudflareData.vtt.length} 字符`);
                const vttSegments = this.parseVTT(cloudflareData.vtt, logPrefix);

                if (vttSegments && vttSegments.length > 0) {
                    logger.info(`${logPrefix}[转换] VTT解析成功，创建了${vttSegments.length}个segments`);
                    return {
                        task: 'transcribe',
                        language: language,
                        duration: duration,
                        text: text,
                        segments: vttSegments
                    };
                } else {
                    logger.warn(`${logPrefix}[转换] VTT解析失败或无有效segments`);
                }
            }

            // 步骤4: 最后备选方案 - 创建单个segment
            logger.warn(`${logPrefix}[转换] 未找到有效的segments或VTT数据，创建单个segment`);
            const fallbackSegment = {
                id: 0,
                seek: 0,
                start: 0,
                end: duration,
                text: text,
                tokens: [],
                temperature: 0,
                avg_logprob: -0.23,
                compression_ratio: 2,
                no_speech_prob: 0.1,
                words: []
            };

            return {
                task: 'transcribe',
                language: language,
                duration: duration,
                text: text,
                segments: [fallbackSegment]
            };

        } catch (error) {
            logger.error(`${logPrefix}[转换] 数据转换过程中发生错误: ${error.message}`);
            throw new Error(`Cloudflare数据转换失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 解析VTT字符串，提取时间戳和文本段落
     * @param {string} vttString - VTT格式的字符串
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 解析后的VTT分段数组，每个元素包含start, end, text
     */
    parseVTT(vttString, logPrefix) {
        try {
            logger.debug(`${logPrefix}[VTT解析] 开始解析VTT字符串，长度: ${vttString.length}`);
            logger.debug(`${logPrefix}[VTT解析] 原始VTT前200字符: ${vttString.substring(0, 200)}`);

            // 移除WEBVTT头部
            const vttContent = vttString.replace('WEBVTT\n\n', '').replace('WEBVTT\n', '');
            logger.debug(`${logPrefix}[VTT解析] 移除头部后长度: ${vttContent.length}`);
            logger.debug(`${logPrefix}[VTT解析] 移除头部后前200字符: ${vttContent.substring(0, 200)}`);

            // 按空行分割每个字幕块
            const vttBlocks = vttContent.split('\n\n').filter(block => block.trim() !== '');
            logger.debug(`${logPrefix}[VTT解析] 找到${vttBlocks.length}个VTT块`);

            // 输出前3个块的内容用于调试
            for (let i = 0; i < Math.min(3, vttBlocks.length); i++) {
                logger.debug(`${logPrefix}[VTT解析] 块${i}: ${vttBlocks[i]}`);
            }

            // 解析每个字幕块
            const segments = vttBlocks.map(block => {
                // 分割时间戳行和文本行
                const lines = block.split('\n');
                if (lines.length < 2) {
                    logger.warn(`${logPrefix}[VTT解析] 无效的VTT块: ${block}`);
                    return null;
                }

                // 解析时间戳行 (支持标准WebVTT格式: "00:00.000 --> 00:01.900" 或简化格式: "00.000 --> 01.900")
                const timeLine = lines[0];

                // 尝试匹配标准WebVTT格式 (HH:MM:SS.mmm 或 MM:SS.mmm)
                let timeMatch = timeLine.match(/(\d{1,2}):(\d{2})\.(\d{3})\s+-->\s+(\d{1,2}):(\d{2})\.(\d{3})/);
                let start, end;

                if (timeMatch) {
                    // 标准WebVTT格式: MM:SS.mmm --> MM:SS.mmm
                    const startMinutes = parseInt(timeMatch[1]);
                    const startSeconds = parseInt(timeMatch[2]);
                    const startMillis = parseInt(timeMatch[3]);
                    start = startMinutes * 60 + startSeconds + startMillis / 1000;

                    const endMinutes = parseInt(timeMatch[4]);
                    const endSeconds = parseInt(timeMatch[5]);
                    const endMillis = parseInt(timeMatch[6]);
                    end = endMinutes * 60 + endSeconds + endMillis / 1000;

                    logger.debug(`${logPrefix}[VTT解析] 解析标准WebVTT时间戳: ${timeLine} -> start=${start}s, end=${end}s`);
                } else {
                    // 尝试简化格式 (SS.mmm --> SS.mmm)
                    timeMatch = timeLine.match(/(\d+\.\d+)\s+-->\s+(\d+\.\d+)/);
                    if (timeMatch) {
                        start = parseFloat(timeMatch[1]);
                        end = parseFloat(timeMatch[2]);
                        logger.debug(`${logPrefix}[VTT解析] 解析简化时间戳: ${timeLine} -> start=${start}s, end=${end}s`);
                    } else {
                        logger.warn(`${logPrefix}[VTT解析] 无效的时间戳行: ${timeLine}`);
                        return null;
                    }
                }

                // 提取文本 (剩余的所有行)
                const text = lines.slice(1).join(' ');

                return { start, end, text };
            }).filter(segment => segment !== null);

            logger.debug(`${logPrefix}[VTT解析] VTT解析完成，有效分段数: ${segments.length}`);
            return segments;

        } catch (error) {
            logger.error(`${logPrefix}[VTT解析] VTT解析失败: ${error.message}`);
            return []; // 返回空数组，让调用方处理
        }
    }

    /**
     * @功能概述: 从words数组估算音频时长
     * @param {Array} words - 单词数组，每个元素包含start和end时间
     * @returns {number} 估算的音频时长（秒）
     */
    estimateDurationFromWords(words) {
        if (!words || !Array.isArray(words) || words.length === 0) {
            return 30; // 默认30秒
        }

        // 找出最后一个单词的结束时间
        let maxEnd = 0;
        for (const word of words) {
            if (word.end > maxEnd) {
                maxEnd = word.end;
            }
        }

        return maxEnd > 0 ? maxEnd : 30; // 如果计算结果为0，返回默认值30秒
    }

    /**
     * 带智能重试机制的Cloudflare API调用
     * @param {string} apiUrl - API地址
     * @param {Object} requestBody - 请求体
     * @param {Object} headers - 请求头
     * @param {number} timeout - 单次请求超时时间
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<Object>} API响应
     */
    async callCloudflareAPIWithRetry(apiUrl, requestBody, headers, timeout, logPrefix) {
        const maxRetries = 5; // 最大重试次数
        const baseDelay = 2000; // 基础延迟2秒
        const maxDelay = 60000; // 最大延迟60秒

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info(`${logPrefix}[重试机制] 第${attempt}/${maxRetries}次尝试调用Cloudflare API`);

                const response = await axios.post(apiUrl, requestBody, {
                    headers,
                    timeout: timeout
                });

                logger.info(`${logPrefix}[重试机制] ✅ 第${attempt}次尝试成功，状态码: ${response.status}`);
                return response;

            } catch (error) {
                const isTimeout = error.code === 'ECONNABORTED' || error.response?.status === 408;
                const isRetryableError = isTimeout || error.response?.status >= 500;

                logger.warn(`${logPrefix}[重试机制] ❌ 第${attempt}次尝试失败: ${error.message}`);

                if (attempt === maxRetries) {
                    logger.error(`${logPrefix}[重试机制] 🚨 所有${maxRetries}次重试均失败，放弃重试`);
                    throw error;
                }

                if (!isRetryableError) {
                    logger.error(`${logPrefix}[重试机制] 🚨 非可重试错误，立即失败: ${error.response?.status || error.code}`);
                    throw error;
                }

                // 指数退避延迟计算
                const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
                const jitter = Math.random() * 1000; // 添加随机抖动避免雷群效应
                const totalDelay = delay + jitter;

                logger.info(`${logPrefix}[重试机制] ⏳ 等待${(totalDelay/1000).toFixed(1)}秒后进行第${attempt + 1}次重试...`);

                // 报告重试进度
                this.reportLLMProgress(TASK_SUBSTATUS.LLM_WAITING, `API调用失败，${(totalDelay/1000).toFixed(1)}秒后重试 (${attempt}/${maxRetries})`, {
                    current: 60 + (attempt * 5),
                    total: 100,
                    technicalDetail: `错误: ${error.message}, 下次重试延迟: ${(totalDelay/1000).toFixed(1)}s`
                });

                await new Promise(resolve => setTimeout(resolve, totalDelay));
            }
        }
    }
}

// 导出GetTranscriptionTaskByCloudflare类，供其他模块使用
module.exports = GetTranscriptionTaskByCloudflare;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}GetTranscriptionTaskByCloudflare 类已导出。`);