# 视频字幕处理项目：环境与 Git 配置全记录

本文档记录了为视频字幕处理项目搭建 Node.js 开发环境、集成 Azure AI Speech SDK，以及配置 Git 版本控制的完整过程，包括遇到的问题和解决方案。

## 阶段一：环境准备与核心功能开发 (Node.js & Azure AI Speech)

### 步骤 1: 初步概念与环境规划

1.  目标设定：
    *   初步目标：使用 Node.js 直接编写逻辑代码，处理固定目录的视频，通过调用 Azure AI 服务 (类似 Whisper) 获取视频字幕的 JSON 数据，暂不做 UI 界面。
    *   确认可行性：此方案被认为是可行的，可以直接利用 Azure AI Speech 服务的云端 API。

2.  Azure SDK 相关疑问：
    *   疑问：是否必须安装 Azure SDK？
    *   解答：不是绝对必须，可以直接调用 REST API，但推荐使用 Azure SDK 以简化开发。
    *   疑问：是否需要在本地和云服务器都安装 SDK？
    *   解答：是的，SDK 是应用程序的一部分，应用在哪里运行，哪里就需要对应的 SDK 依赖。

3.  项目基本信息（基于 `info_0511.md` 和后续确认）：
    *   云服务器操作系统：CentOS 7.8
    *   项目目录（本地与云服务器）：`express` (云服务器路径: `/www/wwwroot/express`)
    *   云服务器已有 `package.json` 文件。

### 步骤 2: 云服务器环境配置与 SDK 安装

1.  初始 SDK 安装尝试与权限问题 (node_modules)：
    *   操作：在云服务器 `/www/wwwroot/express` 目录下尝试安装 SDK。
    *   命令：`npm install microsoft-cognitiveservices-speech-sdk`
    *   问题：出现 `EACCES: permission denied, access '/www/wwwroot/express/node_modules'` 错误。
    *   原因：当前登录用户 `lighthouse` 对 `node_modules` 目录没有写权限。
    *   临时解决：后续使用了 `sudo` 执行 `npm install` 命令。

2.  云服务器 `npm` 版本升级引发的问题：
    *   背景：SDK 安装后，系统提示 `npm` 有新版本 (6.14.15 → 11.2.0)。
    *   操作：执行了 `sudo npm install -g npm` 来升级全局 `npm`。
    *   问题 1：升级过程中出现警告 `npm WARN notsup Unsupported engine for npm@11.3.0: wanted: {"node":"^20.17.0 || >=22.9.0"} (current: {"node":"14.17.6","npm":"6.14.15"})`。这表明新的 `npm` 11.3.0 与当时 shell 环境的 Node.js 14.17.6 不兼容。
    *   问题 2：`npm` "升级" 到 11.3.0 后，任何 `npm` 命令均失败，报错 `Error: Cannot find module 'node:path'`。
    *   原因：新版 `npm` 与旧版 Node.js 不兼容。
    *   分析：当时 `lighthouse` 用户 shell 环境的 Node.js 是 `v14.17.6`，而宝塔面板为 `express` 项目配置的 Node.js 是 `v16.9.0`。

3.  云服务器 Node.js/npm 版本统一及 `package-lock.json` 权限问题：
    *   调整：后续操作切换到与宝塔面板配置更接近的环境。
    *   环境确认 (在 `/www/wwwroot/express` 目录)：
        *   `node -v` 输出 `v16.20.2`
        *   `npm -v` 输出 `8.19.4`
    *   操作：再次尝试安装 SDK (或确认依赖)。
    *   命令：`npm install microsoft-cognitiveservices-speech-sdk`
    *   问题：出现 `EACCES: permission denied, open '/www/wwwroot/express/package-lock.json'` 错误。
    *   原因：`lighthouse` 用户对 `package-lock.json` 文件没有写权限。
    *   排查：`ls -l /www/wwwroot/express/package-lock.json` 显示文件所有者为 `www www`。
    *   解决：
        *   命令：`sudo chown lighthouse:lighthouse /www/wwwroot/express/package-lock.json`
        *   命令：`sudo chown -R lighthouse:lighthouse /www/wwwroot/express`
    *   结果：再次执行 `npm install microsoft-cognitiveservices-speech-sdk`，提示 `up to date`，问题解决。

4.  云服务器安装 `dotenv`：
    *   操作：为管理环境变量安装 `dotenv`。
    *   命令：`sudo npm install dotenv`
    *   结果：安装成功。

5.  本地安装 `nodemon` (开发依赖):
    *   操作：为方便开发时自动重启服务，安装 `nodemon` 作为开发依赖。
    *   命令：`npm install --save-dev nodemon`
    *   解释：`--save-dev` 参数将 `nodemon` 添加到 `package.json` 的 `devDependencies` 中，表明它只用于开发环境。此处为本地安装，无需 `sudo`。
    *   结果：安装成功，`nodemon` 依赖被添加到 `package.json`。

6.  本地安装视频转码依赖 `fluent-ffmpeg`:
    *   **目的:** 为了在本地开发和测试视频转码功能，需要在本地开发环境中安装 `fluent-ffmpeg` 库。
    *   **安装命令:** 在本地项目根目录 `C:\\Users\\<USER>\\Desktop\\代码库\\express` (或你的实际项目路径) 下执行以下命令。
        ```bash
        npm install fluent-ffmpeg@2.1.0 --save
        ```
    *   **说明:** 同云服务器，安装 `2.1.0` 版本以兼容 Node.js v16。`--save` 参数将其添加到 `package.json` 的 `dependencies` 中。
    *   **验证安装:** 检查本地 `package.json` 的 `dependencies` 中是否包含 `"fluent-ffmpeg": "^2.1.0"`，并且 `backend\\node_modules` 目录下是否存在 `fluent-ffmpeg` 文件夹。

### 步骤 3: FFmpeg 可执行程序安装 (本地与云服务器)

**目的:** `fluent-ffmpeg` Node.js 库需要依赖系统上安装的 FFmpeg 可执行程序来完成视频/音频处理任务。本步骤记录了在本地 Windows 开发环境和云服务器 CentOS 环境中安装 FFmpeg 的方法。

1.  **在本地 Windows 环境安装 FFmpeg:**
    *   **推荐方法:** 使用 Chocolatey 包管理器进行安装，它能自动化下载、安装和配置系统 PATH。
    *   **步骤:**
        1.  **安装 Chocolatey (如果未安装):**
            *   以**管理员身份**打开 PowerShell。
            *   执行以下命令允许运行远程脚本：
                ```powershell
                Set-ExecutionPolicy AllSigned # 如果遇到安全提示，输入 Y 或 A 确认
                ```
                或（临时允许）：
                ```powershell
                Set-ExecutionPolicy Bypass -Scope Process -Force
                ```
            *   执行以下命令安装 Chocolatey：
                ```powershell
                Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
                ```
            *   安装完成后，**关闭并重新打开一个**新的命令行窗口（cmd 或 PowerShell）。
            *   在新的窗口中输入 `choco -v` 验证 Chocolatey 是否安装成功并添加到 PATH。
        2.  **使用 Chocolatey 安装 FFmpeg:**
            *   打开命令提示符或 PowerShell（**推荐以管理员身份运行**以确保系统级安装成功）。
            *   执行安装命令：
                ```bash
                choco install ffmpeg --global
                ```
            *   如果提示运行安装脚本，输入 `Y` 或 `A` 确认。
        3.  **验证 FFmpeg 安装:**
            *   安装完成后，**关闭并重新打开一个**新的命令行窗口。
            *   输入 `ffmpeg -version` 并按回车。
            *   如果看到版本信息，则安装成功并已添加到系统 PATH。

2.  **在云服务器 CentOS 7 环境安装 FFmpeg:**
    *   **推荐方法:** 使用 `yum` 包管理器，需要先添加 EPEL 和 RPM Fusion 仓库。
    *   **步骤:**
        1.  **连接到服务器:** 通过 SSH 客户端连接到您的云服务器。
        2.  **安装 EPEL 仓库:**
            ```bash
            sudo yum install epel-release -y
            ```
        3.  **安装 RPM Fusion 仓库:**
            ```bash
            sudo yum localinstall --nogpgcheck https://download1.rpmfusion.org/free/el/rpmfusion-free-release-7.noarch.rpm -y
            sudo yum localinstall --nogpgcheck https://download1.rpmfusion.org/nonfree/el/rpmfusion-nonfree-release-7.noarch.rpm -y
            ```
        4.  **更新 yum 包列表:**
            ```bash
            sudo yum update -y
            ```
        5.  **安装 FFmpeg:**
            ```bash
            sudo yum install ffmpeg ffmpeg-devel -y
            ```
        6.  **验证安装:** 在 SSH 终端中运行 `ffmpeg -version`。如果显示版本信息，则安装成功并已添加到系统 PATH。
            ```bash
            ffmpeg -version
            ```
        7.  **配置 `.env` 文件以指定服务器上的 FFmpeg 路径:**
            *   背景：在服务器端成功安装 FFmpeg 可执行程序后，Node.js 应用（特别是 `fluent-ffmpeg` 库）需要知道这些可执行文件的确切路径才能调用它们。这些路径通过项目根目录下的 `.env` 文件进行配置。
            *   操作步骤：
                a.  确认服务器上 FFmpeg 和 FFprobe 的安装路径：
                    *   通过 SSH 登录到 CentOS 服务器。
                    *   执行以下命令查找可执行文件的路径：
                        ```bash
                        which ffmpeg
                        which ffprobe
                        ```
                    *   记录这两个命令的输出。通常在 CentOS 上，如果通过 `yum` 安装，路径可能是 `/usr/bin/ffmpeg` 和 `/usr/bin/ffprobe`。
                b.  编辑服务器上的 `.env` 文件：
                    *   导航到项目根目录：`cd /www/wwwroot/express`
                    *   使用文本编辑器打开 `.env` 文件：
                        ```bash
                        sudo nano .env
                        ```
                        (如果文件不存在，此命令会创建它。请确保从本地或模板复制了完整的 `.env` 内容，包括 Azure 配置等。)
                c.  修改或添加 `FFMPEG_PATH` 和 `FFPROBE_PATH`：
                    *   在 `.env` 文件中，找到或添加以下两行，并将其值设置为上一步中 `which` 命令输出的路径：
                        ```env
                        FFMPEG_PATH=/usr/bin/ffmpeg  # 替换为实际的 ffmpeg 路径
                        FFPROBE_PATH=/usr/bin/ffprobe # 替换为实际的 ffprobe 路径
                        ```
                    *   确保移除或注释掉任何指向 Windows 路径（如 `C:\ProgramData\...`）的旧配置。
                d.  保存文件并退出编辑器。
                e.  验证配置：
                    *   重启 Node.js 应用。
                    *   检查应用日志（例如 `doc/logs/app.log`），确认 VideoProcessingService 是否加载了正确的 Linux 路径。日志中应该出现类似以下的信息（路径为您配置的实际路径）：
                        ```
                        DEBUG: [VideoProcessingService][DEBUG] 从配置加载的 FFMPEG_PATH: /usr/bin/ffmpeg
                        DEBUG: [VideoProcessingService][DEBUG] 从配置加载的 FFPROBE_PATH: /usr/bin/ffprobe
                        ```
            *   重要提示：
                *   如果 `which` 命令没有返回路径，说明 FFmpeg 可能未正确安装或其路径未在系统的 PATH 环境变量中。请重新检查本节前面关于在 CentOS 上安装 FFmpeg 的说明进行安装。
                *   `.env` 文件包含敏感配置，不应提交到 Git 仓库。每次部署到新服务器或重置环境时，都需要手动创建或配置此文件。
                *   确保 `.env` 文件中的所有其他配置（如 Azure 服务密钥、数据库连接信息等）也已正确设置为适用于服务器环境的值。

    *   **重要:** 安装 FFmpeg 并正确配置 `.env` 文件后，需要**彻底重启** Node.js 应用服务，以便其加载新的系统环境变量和应用配置。

### 步骤 4: 本地开发环境 Node.js 版本同步

1.  本地初始环境：
    *   `node -v` 输出 `v22.14.0`
    *   `npm -v` 输出 `10.9.2`
    *   SDK 安装：`npm install microsoft-cognitiveservices-speech-sdk` 成功。

2.  版本统一需求：
    *   问题：本地 Node.js/npm 版本 (22.x/10.x) 与云服务器 (16.x/8.x) 不一致。
    *   方案：选择统一 Node.js 版本，使本地与云服务器的 `v16.20.2` 版本一致。

3.  本地 `nvm-windows` 安装与配置：
    *   目标：使用 `nvm` (Node Version Manager) 在本地 Windows 环境中切换 Node.js 版本。
    *   问题 1：直接运行 `nvm install 16.20.2`，报错 `nvm : 无法将"nvm"项识别为 cmdlet...`。
    *   原因：Windows PowerShell 中未安装或未正确配置 `nvm-windows`。
    *   解决：
        1.  提示需要安装 `nvm-windows` from GitHub ([Corey Butler - nvm-windows Releases](https://github.com/coreybutler/nvm-windows/releases)).
        2.  用户确认已安装。
        3.  问题 2：`nvm -version` (带连字符) 仍报错。
        4.  纠正：`nvm-windows` 的版本查看命令是 `nvm version` (无连字符) 或 `nvm v`。
    *   环境确认 (在 `C:\Users\<USER>\Users\halou\Desktop\代码库\express`。
        3.  `node -v` 输出 `v16.20.2`。
        4.  `npm -v` 输出 `8.19.4`。
        5.  `nvm version` (或 `nvm -v`) 输出 `1.2.2`。
    *   结果：本地 Node.js/npm 版本成功与云服务器同步。

4.  本地安装 `dotenv`：
    *   确认：本地和云服务器均已安装 `dotenv`。

5.  本地安装 `winston`：
    *   操作：为实现规范化的日志记录安装 `winston` 库。
    *   命令：`npm install winston --save`
    *   结果：安装成功。 `--save` 参数会将 `winston` 添加到 `package.json` 的 `dependencies` 中。

### 步骤 5: 本地 Git 配置与远程仓库同步

1.  `.gitignore` 文件配置：
    *   疑问 1：是否可以在 `.gitignore` 中写中文注释？
    *   解答：可以，以 `#` 开头的行视为注释，支持 UTF-8 编码。
    *   需求 1：屏蔽本地 `doc/` 目录。
    *   解决：在 `.gitignore` 中添加 `doc/`。
    *   疑问 2：是否需要将 `package.json`, `package-lock.json`, `FileScopeMCP-tree.json` 加入 `.gitignore`？
    *   解答：
        *   `package.json` 和 `package-lock.json`：强烈不建议忽略，它们是项目依赖管理和环境一致性的核心。
        *   `FileScopeMCP-tree.json`：这类特定于 IDE/工具的元数据文件可以忽略。
    *   最终 `.gitignore` (示例)：
        ```gitignore
        # 依赖目录 (Dependencies)
        node_modules/

        # 环境变量文件 (Environment variables file)
        .env

        # 文档目录 (Documentation directory - 本地特定，不提交)
        doc/

        # 特定工具/IDE 生成的文件
        # FileScopeMCP-tree.json # (用户决定是否添加)

        # 可选：日志文件 (Optional: Log files)
        *.log
        # ...其他日志规则...

        # 可选：操作系统生成的文件 (Optional: OS generated files)
        .DS_Store
        Thumbs.db
        ```

2.  Git 初始化与远程仓库连接：
    *   疑问：是否需要先在 GitHub 初始化仓库再执行本地 `git init`？
    *   解答：不需要，本地 `git init` 可以先执行。
    *   操作：在本地 `C:\Users\<USER>\Desktop\代码库\express` 目录下进行 Git 操作。
        1.  (假定已执行 `git init`)
        2.  (假定已配置好 `.gitignore` 并 `git add` 了相关文件，如 `app.js`, `package.json`, `package-lock.json`, `.gitignore`)
        3.  (假定已执行 `git commit -m "Initial commit..."`)
    *   连接远程仓库：
        *   命令：`git remote add origin https://github.com/sunshine6666666666/express.git`
        *   解释：此命令将本地仓库与指定的 GitHub 远程仓库建立连接，并命名为 `origin`。
    *   验证连接：
        *   命令：`git remote -v`
        *   输出：
            ```
            origin  https://github.com/sunshine6666666666/express.git (fetch)
            origin  https://github.com/sunshine6666666666/express.git (push)
            ```
        *   结果：连接配置正常。

3.  处理 Git 推送 (push) 问题：
    *   问题 1：`git push -u origin main` 失败。
    *   错误：`error: src refspec main does not match any`。
    *   原因：本地仓库没有名为 `main` 的分支，或者该分支没有提交。
    *   排查与解决：
        1.  用户确认本地分支名为 `main`。
        2.  通过 `git status` 发现：
            *   当前分支是 `master` (`On branch master`)。
            *   `master` 分支上还没有提交 (`No commits yet`)。
            *   文件已暂存 (`Changes to be committed`)。
        3.  `git branch` 命令无输出 (进一步确认无正式提交创建分支)。
        4.  解决：
            *   先提交：`git commit -m "Initial commit..."` (在 `master` 分支上)
            *   然后推送 `master` 分支：`git push -u origin master` (或先将 `master` 重命名为 `main` 再推送 `main`)。

    *   问题 2：(在解决了分支名和初次提交后，再次尝试 `git push -u origin main`) 推送被拒绝。
    *   错误 (来自VSCode Git日志)：
        ```
        ! [rejected]        main -> main (non-fast-forward)
        error: failed to push some refs to 'https://github.com/sunshine6666666666/express.git'
        hint: Updates were rejected because the tip of your current branch is behind
        ```
    *   原因：非快进式更新。远程仓库 `origin/main` 拥有本地 `main` 分支所没有的提交。
    *   解决尝试 1：`git pull origin main`
    *   问题 3：`git pull origin main` 失败。
    *   错误：`fatal: refusing to merge unrelated histories`。
    *   原因：本地 `main` 和远程 `origin/main` 的提交历史完全不同且没有共同祖先。通常发生在远程仓库创建时自带了初始提交 (如 README)，而本地也独立进行了初始化和提交。
    *   解决尝试 2：`git fetch origin`
    *   确认：执行后，用户确认远程仓库实际上是空的 (或其内容不重要)。
    *   最终解决：
        1.  确认本地分支名 (假设是 `main`，或之前已从 `master` 重命名/正确推送为 `main`)。
        2.  强制推送本地分支覆盖远程分支：
            `git push -u origin main --force`
    *   结果：推送成功。

### 步骤 6: 服务器端代码更新与重启 (示例流程)

1.  **SSH 登录服务器**。

2.  **导航到项目根目录**:
    ```bash
    cd /www/wwwroot/express
    ```

3.  **拉取最新代码** (确保已配置好 Git 仓库和访问权限):
    ```bash
    sudo -u www git pull origin main
    ```

4.  **安装/更新后端依赖** (如果 `backend/package.json` 有变动):
    ```bash
    cd backend
    sudo -u www npm install
    cd ..
    ```

5.  **安装/更新前端依赖并构建** (如果 `frontend/package.json` 或前端代码有变动):
    ```bash
    cd frontend
    sudo -u www npm install
    sudo -u www npm run build
    cd ..
    ```

6.  **重启 Node.js 应用**: **通过宝塔面板 -> 网站 -> Node项目，找到 `express-backend` (或对应的项目名) 并执行重启操作。**

7.  **(可选) 重载 Nginx 配置** (如果修改了 Nginx 配置文件):
    ```bash
    sudo nginx -t && sudo systemctl reload nginx
    ```

8.  **检查应用状态和日志**:
    - 通过宝塔面板查看 Node 项目运行状态和日志。
    - (辅助) `sudo -u www pm2 list` 和 `sudo -u www pm2 logs express-backend`。
    - 查看 `backend/logs/app.log`。

### 步骤 7: 安装视频转码依赖 fluent-ffmpeg (云服务器)

1.  **目的:** 为了在调用 Azure API 前将上传的视频文件转码为音频格式，需要安装 Node.js 的 FFmpeg 封装库 `fluent-ffmpeg`。

2.  **安装命令:** 在项目根目录 `/www/wwwroot/express` 下执行以下命令。
    *   **以 `www` 用户身份安装 (推荐):**
        ```bash
        sudo -u www npm install fluent-ffmpeg@2.1.0 --save
        ```
    *   **使用 `sudo` 安装 (如果遇到权限问题):**
        ```bash
        sudo npm install fluent-ffmpeg@2.1.0 --save
        ```
    *   **说明:** 我们安装的是 `2.1.0` 版本，以确保与当前 Node.js v16 环境的兼容性。`--save` 参数将其添加到 `package.json` 的 `dependencies` 中。

3.  **验证安装:** 检查 `package.json` 的 `dependencies` 中是否包含 `"fluent-ffmpeg": "^2.1.0"`，并且 `backend/node_modules` 目录下是否存在 `fluent-ffmpeg` 文件夹。

# Web 应用部署与故障排除全记录 (Nginx + PM2 + Vue.js + Express.js)

本文档详细记录了将一个包含 Vue.js 前端和 Express.js 后端的 Web 应用部署到 CentOS 服务器（使用宝塔面板和 Nginx）的完整过程，包括遇到的问题、排查步骤和解决方案。

## 阶段一：前端项目搭建与配置 (Vue.js)

### 1.1 Vue CLI 环境准备

*   **背景**：由于本地 Node.js 版本 (v16.20.2) 与 Vite/React 最新版存在兼容性问题，且用户对 Vue.js 更为熟悉，故选择 Vue CLI 搭建前端项目。
*   **全局安装 Vue CLI** (如果尚未安装)：
    ```bash
    npm install -g @vue/cli
    # 或 yarn global add @vue/cli
    ```
*   **验证安装**：
    ```bash
    vue --version
    # 预期输出 @vue/cli 5.x.x 或类似版本
    ```

### 1.2 创建 Vue 前端项目

1.  **导航到前端目录**：
    *   在本地项目根目录 `express/` 下，进入或创建 `frontend/` 目录。
    *   命令 (在 `express/` 目录下执行)：
        ```bash
        cd frontend
        ```
    *   **注意**：如果 `frontend` 目录已存在且包含旧的 Vite 项目，请先删除或备份该目录。

2.  **使用 Vue CLI 创建项目**：
    *   命令 (在 `express/frontend/` 目录下执行，创建名为 `frontend` 的 Vue 项目，实际项目文件将位于 `express/frontend/frontend/`，这与预期不符，后续会调整)：
        ```bash
        vue create frontend
        ```
    *   **修正后的操作**：为避免在 `express/frontend/` 目录下创建嵌套的 `frontend/frontend/` 结构，正确的做法是在 `express/` 目录下直接创建 `frontend` 项目：
        ```bash
        # 切换到 express 根目录
        cd ..
        # 创建名为 frontend 的 Vue 项目
        vue create frontend
        ```
        在提示 "Target directory frontend already exists. Pick an action:" 时，如果 `frontend` 目录已存在且是上次错误创建的，选择 "Overwrite"。如果它是空的或打算覆盖，选择 "Overwrite" 或 "Merge"。

3.  **选择预设 (Preset)**：
    *   在 `vue create` 过程中，选择 "Manually select features"。
    *   勾选以下特性：
        *   Choose Vue version (选择 Vue 3)
        *   Babel
        *   TypeScript
        *   Router
        *   Linter / Formatter
    *   选择 Vue 3.x。
    *   Use class-style component syntax? (No)
    *   Use Babel alongside TypeScript? (Yes)
    *   Use history mode for router? (Yes)
    *   Pick a linter / formatter config: ESLint + Prettier
    *   Pick additional lint features: Lint on save
    *   Where do you prefer placing config for Babel, ESLint, etc.? In dedicated config files
    *   Save this as a preset for future projects? (Yes, 可选)

4.  **项目创建完成**：
    *   等待 Vue CLI 完成项目文件的生成和依赖安装。

### 1.3 本地运行与构建前端应用

1.  **进入前端项目目录**：
    ```bash
    cd frontend
    # 确保当前目录为 express/frontend/
    ```

2.  **启动开发服务器**：
    ```bash
    npm run serve
    ```
    *   应用通常会在 `http://localhost:8080` 启动。

3.  **构建生产版本**：
    ```bash
    npm run build
    ```
    *   构建完成后，会在 `express/frontend/dist/` 目录下生成静态文件，这些文件将用于服务器部署。

## 阶段二：解决 Git 拉取不稳定的问题 (http.postBuffer)

### 问题描述
在服务器上以 `www` 用户身份执行 `git pull origin main` 时，即使配置了包含 Personal Access Token (PAT) 的远程 URL，依然频繁遇到以下错误：
- `fatal: unable to access 'https://[USER]:[TOKEN]@github.com/[USER]/[REPO].git/': Empty reply from server`
- `fatal: unable to access 'https://[USER]:[TOKEN]@github.com/[USER]/[REPO].git/': Failed connect to github.com:443; Connection timed out`

这表明存在网络连接层面的问题，尤其是在数据传输过程中。

### 排查与分析
初步的网络连通性测试 (如 `ping github.com`) 可能成功，但 HTTPS (端口 443) 上的 Git 操作失败，指向了更深层次的连接或数据传输问题。
用户反馈之前曾使用过"带有buffer的拉取方式"成功过，这暗示了问题可能与 Git 的 HTTP 传输缓冲区大小有关。默认的 `http.postBuffer` 可能不足以应对当前网络环境或仓库的大小/复杂度，导致连接在数据传输完成前被服务器或中间网络设备中断。

### 解决方案：调整 Git http.postBuffer

通过增大 Git 的 `http.postBuffer` 配置，可以允许 Git 在单次 HTTP 请求中发送更多数据，减少因网络波动或小缓冲区导致连接重置的风险。

**操作步骤 (在云服务器 `/www/wwwroot/express` 目录下执行)：**

1.  **为 `www` 用户全局设置 `http.postBuffer`：**
    此操作将修改 `www` 用户的全局 Git 配置文件 (`~www/.gitconfig`)，使该设置对 `www` 用户的所有 Git 操作永久生效。

    ```bash
    sudo -u www git config --global http.postBuffer 524288000
    ```
    *   `524288000` 字节约等于 500MB。这个值可以根据实际情况调整。

2.  **执行 `git pull` (验证配置)：**
    设置全局配置后，再次尝试拉取代码。为确保配置生效，也可以在拉取命令中临时指定一次（通常全局配置后无需再次指定，但作为验证步骤可以加上）。

    ```bash
    sudo -u www git -c http.postBuffer=524288000 pull origin main
    ```
    或者，在全局配置已生效的情况下，简单执行：
    ```bash
    sudo -u www git pull origin main
    ```

### 结果
在将 `http.postBuffer` 调整为 `524288000` 后，`sudo -u www git pull origin main` 命令成功完成，代码顺利从远程仓库拉取到服务器。

### 结论
对于因网络不稳定或仓库较大导致的 `Empty reply from server` 或 `Connection timed out` 等 Git 拉取错误，调整 `http.postBuffer` 是一个有效的解决方案。建议在遇到类似问题时，优先尝试增大此配置值。

## 阶段三：Nginx 配置与故障排除

### 3.1 初始 Nginx 配置目标

*   **域名**：`n8n.shuimitao.online` (示例)
*   **根路径 `/`**：应指向前端构建产物 `frontend/dist/`，并处理单页应用 (SPA) 的路由。
*   **API 路径 `/api/`**：应反向代理到在 `http://127.0.0.1:3000` 运行的后端 Express 应用。

### 3.2 Nginx 配置冲突排查

1.  **检查 Nginx 状态和配置**：
    ```bash
    sudo systemctl status nginx
    sudo nginx -t
    ```

2.  **问题：`conflicting server name "n8n.shuimitao.online"`**
    *   `nginx -t` 报错，指示多个配置文件中定义了相同的 `server_name`。
    *   **排查**：使用 `grep` 查找所有包含该域名的 Nginx 配置文件。
        ```bash
        sudo grep -r "n8n.shuimitao.online" /www/server/panel/vhost/nginx/
        ```
    *   **发现**：
        *   `/www/server/panel/vhost/nginx/node_express.conf` (宝塔面板编辑的主配置文件)
        *   `/www/server/panel/vhost/nginx/n8n.shuimitao.online.custom.conf` (一个额外的、冲突的配置文件)
    *   **解决**：禁用或删除冲突的 `.custom.conf` 文件。重命名是较安全的选择：
        ```bash
        sudo mv /www/server/panel/vhost/nginx/n8n.shuimitao.online.custom.conf /www/server/panel/vhost/nginx/n8n.shuimitao.online.custom.conf.disabled
        ```
    *   再次运行 `sudo nginx -t`，冲突应解决。

### 3.3 更新 Nginx 主配置文件

1.  **编辑主配置文件** (例如，通过宝塔面板，或直接编辑 `/www/server/panel/vhost/nginx/node_express.conf`):

    ```nginx
    server {
        listen 80;
        listen [::]:80;
        server_name n8n.shuimitao.online; # 替换为你的域名

        # SSL Configuration (如果配置了HTTPS，宝塔会自动添加)
        # listen 443 ssl http2;
        # listen [::]:443 ssl http2;
        # ssl_certificate /www/server/panel/vhost/cert/n8n.shuimitao.online/fullchain.pem;
        # ssl_certificate_key /www/server/panel/vhost/cert/n8n.shuimitao.online/privkey.pem;
        # ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        # ssl_prefer_server_ciphers on;
        # ssl_session_cache shared:SSL:10m;
        # ssl_session_timeout 10m;
        # error_page 497  https://$host$request_uri;

        # Logging
        access_log /www/wwwlogs/n8n.shuimitao.online.log;
        error_log /www/wwwlogs/n8n.shuimitao.online.error.log;

        # Security headers (可选)
        # add_header X-Frame-Options "SAMEORIGIN";
        # add_header X-Content-Type-Options "nosniff";
        # add_header X-XSS-Protection "1; mode=block";
        # add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Serve frontend static files
        location / {
            root /www/wwwroot/express/frontend/dist; # 前端构建文件路径
            try_files $uri $uri/ /index.html;      # SPA 路由处理
            index index.html index.htm;
        }

        # Proxy API requests to backend
        location /api/ {
            proxy_pass http://127.0.0.1:3000; # 后端服务地址
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Deny access to .htaccess files (if any)
        location ~ /\.ht {
            deny all;
        }
    }
    ```

2.  **验证配置并重启 Nginx**：
    ```bash
    sudo nginx -t
    # 如果显示 "syntax is ok" 和 "test is successful"
    sudo systemctl restart nginx
    ```

3.  **问题：`connect() failed (111: Connection refused) while connecting to upstream`**
    *   Nginx 错误日志 (`/www/wwwlogs/n8n.shuimitao.online.error.log`) 中出现此错误。
    *   **原因**：
        1.  Nginx 配置的 `location /` 仍然错误地代理到后端 (例如 `proxy_pass http://127.0.0.1:3000;`)，而不是服务静态文件。
        2.  或者，后端应用 (`http://127.0.0.1:3000`) 尚未启动或启动失败。
    *   **解决**：仔细检查并更正 Nginx 配置文件中的 `location /` 和 `location /api/` 部分，确保它们如上所示配置正确。然后确保后端应用已通过 PM2 成功启动。

## 阶段四：PM2 后端进程管理与故障排除

### 4.1 启动后端应用 (PM2)

1.  **进入后端项目目录**：
    ```bash
    cd /www/wwwroot/express/backend
    ```

2.  **首次尝试启动 (可能遇到的问题)**：
    *   `sudo -u www pm2 list` 可能显示旧的或失败的进程。
    *   `sudo -u www pm2 logs express-backend` (或你的应用名) 可能显示错误，例如：
        *   `spawn /www/server/nodejs/v16.20.2/bin/node EACCES` (PM2 守护进程权限问题)
        *   `TypeError: Cannot read properties of undefined (reading 'forEach')` (应用内部错误)

3.  **清理 PM2 状态 (如果需要)**：
    ```bash
    sudo -u www pm2 kill      # 停止 PM2 守护进程
    sudo -u www pm2 flush     # 清空日志
    sudo -u www pm2 delete all # 删除所有已注册的应用 (谨慎操作)
    # 或者针对特定应用
    # sudo -u www pm2 delete express-backend
    ```

4.  **以 `www` 用户启动应用**：
    ```bash
    cd /www/wwwroot/express/backend
    sudo -u www pm2 start src/app.js --name express-backend
    ```
    *   `--name express-backend` 指定了 PM2 中的应用名称。

### 4.2 后端启动故障排除

1.  **检查 PM2 状态和日志**：
    ```bash
    sudo -u www pm2 list
    sudo -u www pm2 logs express-backend --lines 100
    ```

2.  **问题：`ReferenceError: logDirectory is not defined` in `backend/src/utils/logger.js`**
    *   PM2 错误日志 (`~/.pm2/logs/express-backend-error.log`) 显示此错误，导致应用不断重启。
    *   **原因**：`logger.js` 文件中，在使用 `logDirectory` 变量之前未正确定义和初始化它。
    *   **本地修复** (`backend/src/utils/logger.js`):
        ```javascript
        const winston = require('winston');
        const path = require('path');
        const fs = require('fs');

        // ---> 新增或确保此行存在 <---
        const logDirectory = path.join(__dirname, '..', '..', 'logs'); // 指向 backend/logs/

        // 确保日志目录存在
        if (!fs.existsSync(logDirectory)) {
          fs.mkdirSync(logDirectory, { recursive: true });
        }

        // ... (logger.js 的其余部分) ...

        const logger = winston.createLogger({
          level: process.env.LOG_LEVEL || 'info',
          format: winston.format.combine(
            winston.format.timestamp({
              format: 'YYYY-MM-DD HH:mm:ss'
            }),
            winston.format.errors({ stack: true }),
            winston.format.splat(),
            winston.format.json()
          ),
          defaultMeta: { service: 'user-service' }, // 可以自定义
          transports: [
            new winston.transports.File({ filename: path.join(logDirectory, 'error.log'), level: 'error' }),
            new winston.transports.File({ filename: path.join(logDirectory, 'app.log') })
          ]
        });

        // 如果不是生产环境，也输出到控制台
        if (process.env.NODE_ENV !== 'production') {
          logger.add(new winston.transports.Console({
            format: winston.format.combine(
              winston.format.colorize(),
              winston.format.simple()
            )
          }));
        }

        module.exports = logger;
        ```
    *   **同步到服务器**：
        1.  在本地提交更改并推送到 Git。
        2.  在服务器上拉取最新代码：
            ```bash
            cd /www/wwwroot/express
            sudo -u www git pull origin main
            ```

3.  **重启 PM2 应用 (应用修复后)**：
    ```bash
    sudo -u www pm2 delete express-backend
    cd /www/wwwroot/express/backend
    sudo -u www pm2 start src/app.js --name express-backend
    ```

4.  **验证后端是否监听端口**：
    *   `sudo -u www pm2 list` 应显示 `express-backend` 状态为 `online`。
    *   PM2 日志 (`pm2 logs express-backend`) 不应再显示 `ReferenceError`，且应有 "Express 应用已启动，监听端口 3000" 或类似日志。
    *   检查网络连接：
        ```bash
        netstat -tulnp | grep 3000
        ```
        *   预期输出应包含类似 `tcp6 0 0 :::3000 :::* LISTEN .../node` 的行，表示 Node.js 应用正在监听端口 3000。

## 阶段五：最终验证

1.  **访问前端**：在浏览器中打开 `http://n8n.shuimitao.online` (或你的域名)。应能看到 Vue.js 应用界面。
2.  **测试 API 调用**：通过前端应用触发 API 请求 (例如，上传文件)，检查 Nginx 是否正确将 `/api/` 请求代理到后端，以及后端是否正常处理并返回响应。
3.  **检查 Nginx 和 PM2 日志**：监控 `/www/wwwlogs/n8n.shuimitao.online.error.log` 和 `pm2 logs express-backend` 以捕获任何潜在错误。

通过以上步骤，Web 应用应能成功部署并运行。遇到问题时，仔细检查相关日志文件和配置文件是关键。

## 阶段六：前端移除后的后端调整与故障排除 (2025-05-21)

本阶段记录了在移除前端 Vue.js 应用，使后端 Express 直接处理根路径并返回 "Hello World" 后，遇到的启动和运行问题及其解决过程。

### 6.1 问题现象

1.  **浏览器访问 `https://n8n.shuimitao.online/` 显示 "Cannot GET /"**：
    *   尽管 Nginx 配置已修改为将根路径 `/` 反向代理到后端 `http://127.0.0.1:3000`，且后端 `app.js` 中已添加 `app.get('/', ...)` 路由处理，但页面仍无法正确显示 "Hello World"。
2.  **PM2 后端进程 (`express-backend`) 错误日志显示 `ReferenceError: logDirectory is not defined`**：
    *   通过 `sudo -u www pm2 logs express-backend` 查看错误日志，发现应用因在 `backend/src/utils/logger.js` 文件中找不到 `logDirectory` 变量而启动失败并不断重启。
3.  **PM2 进程列表出现重复/错误的进程**：
    *   在排查过程中，曾错误地使用 `sudo -u www pm2 start /www/wwwroot/express/backend/` 命令，导致 PM2 启动了一个名为 `backend` 的新进程，而不是重启现有的 `express-backend` 进程，造成混淆。
4.  **服务器文件与 Git 仓库不一致的困惑**：
    *   尽管在服务器上执行了 `sudo -u www git pull origin main` 并提示 `Already up-to-date.`，但检查服务器上的 `backend/src/utils/logger.js` 文件内容，发现其与本地已修复并推送到 Git 仓库的最新版本不符（缺少 `logDirectory` 的正确定义）。

### 6.2 排查与解决步骤

1.  **确认 Nginx 配置**：
    *   再次检查 `/www/server/panel/vhost/nginx/node_express.conf`，确认 `location /` 和 `location /api/` 均正确配置为 `proxy_pass http://127.0.0.1:3000;`。
    *   执行 `sudo nginx -t` 和 `sudo systemctl reload nginx` 确保配置生效。
    *   **结论**：Nginx 配置正确，问题源于后端应用未能正常处理根路径请求。

2.  **检查后端 `app.js` 根路由**：
    *   在本地确认 `backend/src/app.js` 中存在以下代码块，用于处理根路径请求：
        ```javascript
        app.get('/', (req, res) => {
          // ... 日志记录 ...
          res.status(200).send('<h1>Hello World from Express Server!</h1>');
        });
        ```
    *   **结论**：`app.js` 中的根路由处理逻辑本身是存在的。

3.  **分析 PM2 日志并定位 `ReferenceError`**：
    *   错误日志明确指出 `ReferenceError: logDirectory is not defined` 发生在 `backend/src/utils/logger.js` 文件的第8行，该文件被 `app.js` 的第10行引用。
    *   **推测**：服务器上的 `logger.js` 文件不是最新版本。

4.  **处理 PM2 进程混淆**：
    *   使用 `sudo -u www pm2 list` 查看所有进程。
    *   发现存在 `express-backend` (id: 0) 和一个意外的 `backend` (id: 1) 进程。
    *   停止并删除错误的 `backend` 进程：
        ```bash
        sudo -u www pm2 stop 1
        sudo -u www pm2 delete 1
        ```
    *   只保留正确的 `express-backend` 进程 (id: 0) 进行后续操作。

5.  **验证服务器与本地 `logger.js` 文件内容**：
    *   本地 `logger.js` 已确认包含 `const logDirectory = path.join(__dirname, '..', '..', 'logs');`。
    *   服务器上，通过 `cat /www/wwwroot/express/backend/src/utils/logger.js` 查看文件内容，发现确实**缺少** `logDirectory` 的正确定义，与 PM2 错误日志吻合。

6.  **解决服务器文件与 Git 仓库不一致的问题**：
    *   **尝试 `git pull`**：再次执行 `sudo -u www git pull origin main`，仍然提示 `Already up-to-date.`，这表明服务器的 Git 本地仓库认为其工作目录与远程分支一致，但实际文件内容不符。这可能是由于之前的某些操作（如手动修改后未提交，或 `git checkout` 缓存等）导致工作区文件与 Git 索引不一致。
    *   **强制更新工作区文件**：为确保服务器上的 `logger.js` 文件与 Git 仓库中的最新版本完全一致，使用以下命令强制检出该文件：
        ```bash
        cd /www/wwwroot/express
        sudo -u www git checkout HEAD -- backend/src/utils/logger.js
        ```
        这个命令会用 `HEAD`（当前分支的最新提交）中的 `backend/src/utils/logger.js` 文件覆盖工作区中的同名文件。

7.  **重启后端应用**：
    *   执行 `sudo -u www pm2 restart express-backend`。

8.  **检查 PM2 日志 (确认无错误)**：
    *   `sudo -u www pm2 logs express-backend`。
    *   错误日志 (`express-backend-error.log`) 不再出现 `ReferenceError`。
    *   输出日志 (`express-backend-out.log`) 显示应用正常初始化，包括创建日志目录、加载配置、挂载路由等信息。
        ```
        日志目录已存在: /www/wwwroot/express/backend/logs
        info: [文件：app.js][Express应用][初始化] Express 应用开始初始化...
        info: [文件：app.js][Express应用][初始化] 根路径 ("/") GET 请求处理器已配置。
        info: [文件：app.js][Express应用][初始化] Express 应用已成功启动，监听端口 3000。
        ```

9.  **最终验证**：
    *   再次访问 `https://n8n.shuimitao.online/`。
    *   页面成功显示 "Hello World from Express Server!"。

### 6.3 结论

此次故障排除的核心在于发现并解决了服务器上关键代码文件 (`logger.js`) 与 Git 仓库最新版本不一致的问题。通过强制检出 (`git checkout HEAD -- <file>`) 确保了文件同步，随后 PM2 进程得以正常启动，后端应用成功处理了根路径请求。同时，也注意到了正确管理 PM2 进程的重要性，避免因误操作启动多个实例导致混淆。

通过以上步骤，Web 应用应能成功部署并运行。遇到问题时，仔细检查相关日志文件和配置文件是关键。