<!DOCTYPE html>
<html lang="en">
<head>
  <!-- 全局设置和外部资源 -->
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <title>Short Video Design</title>
  
  <!-- Tailwind CSS 核心及插件 -->
  <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  
  <!-- 中文字体加载 -->
  <link href="https://fonts.googleapis.com/css2?family=Ma+Shan+<PERSON>&amp;family=ZCOOL+XiaoWei&amp;display=swap" rel="stylesheet"/>

  <!-- 自定义样式 -->
  <style>
    /* 基础页面样式 */
    body {
      font-family: 'ZCOOL XiaoWei', serif;
      background-image: url('https://images.unsplash.com/photo-1523961131990-5ea7c61b2107?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dGVjaCUyMGJhY2tncm91bmR8ZW58MHx8MHx8fDA%3D&w=1000&q=80');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-color: #0a0a0a;
    }
    
    /* 文字特效类 */
    .text-stroke-gold { /* 金色文字描边效果 */
      -webkit-text-stroke: 1px #FFD700;
      text-stroke: 1px #FFD700;
      color: transparent;
    }
    .text-shadow-gold { /* 金色文字阴影效果 */
      text-shadow: 0 0 8px rgba(255, 215, 0, 0.7);
    }
    .text-shadow-light { /* 浅色文字阴影 */
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    }
  </style>

  <!-- 响应式高度设置 -->
  <style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
</head>

<body class="flex flex-col items-center justify-between min-h-screen text-white">
  <!-- Header Section - 课程标题 -->
  <div class="w-full max-w-md flex flex-col items-center pt-16 text-center">
    <h1 class="text-5xl font-bold text-stroke-gold text-shadow-gold mb-3" style="font-family: 'Ma Shan Zheng', cursive;">坚持30天</h1>
    <h2 class="text-4xl font-semibold text-stroke-gold text-shadow-gold" style="font-family: 'Ma Shan Zheng', cursive;">听懂国外新闻</h2>
  </div>

  <!-- Main Content Card - 填空题区域 -->
  <!-- 背景颜色: 白色 20% 不透明度 -->
  <div class="w-full max-w-md my-8 px-4">
    <div class="bg-white bg-opacity-20 p-6 rounded-xl shadow-2xl backdrop-blur-sm">
      <p class="text-4xl font-bold text-white text-shadow-light leading-relaxed text-center">
        Wildfires and it has been an uphill 
        <span class="font-semibold border-b-2 border-white inline-block w-24 mx-1"></span> <!-- 填空输入框 -->
        with one community losing dozens of 
        <span class="font-semibold border-b-2 border-white inline-block w-24 mx-1"></span>. <!-- 填空输入框 -->
      </p>
    </div>
  </div>

  <!-- Footer Section - 练习阶段标识 -->
  <div class="w-full max-w-md flex flex-col items-center pb-16">
    <h3 class="text-3xl font-medium text-yellow-400 text-shadow-gold">第二遍 单词填空</h3>
  </div>
</body>
</html>