/**
 * @功能概述: 定义单个处理任务的基类 (TaskBase)。所有具体的任务都应该继承此类。
 * @说明: 这是从原 llmPipeline.js 中拆分出来的 Task 基类，集成了标准化的进度监控机制。
 * @架构位置: 任务层基类，为所有具体任务提供统一的执行接口和进度报告能力
 * @进度监控: 支持细粒度的进度追踪，特别针对LLM交互提供专门的监控方法
 */

// 导入日志工具 logger，用于记录程序运行信息，方便调试和追踪。
const logger = require('../utils/logger'); // 路径相对于 backend/src/class/

// 导入进度监控相关常量和工厂函数
const { TASK_STATUS, TASK_SUBSTATUS, createProgressData, createLLMProgressData } = require('../constants/progress');

// 模块级日志前缀，用于标识从本文件输出的日志。
const moduleLogPrefix = `[文件：TaskBase.js][任务基类][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);

/**
 * @功能概述: 定义单个处理任务的基类。所有具体的任务都应该继承此类或实现类似接口。
 * @说明: 提供统一的任务执行接口、进度监控机制和状态管理功能
 * 
 * @property {string} name - 任务的名称，用于标识和日志记录
 * @property {string} taskId - 任务的唯一标识符，格式：{name}-{timestamp}-{randomString}
 * @property {string} status - 任务的当前执行状态，来自 TASK_STATUS 枚举
 * @property {string|null} subStatus - 任务的子状态，来自 TASK_SUBSTATUS 枚举，用于细粒度追踪
 * @property {number|null} startTime - 任务开始执行的时间戳
 * @property {number|null} endTime - 任务结束执行的时间戳
 * @property {any} result - 任务成功执行后的结果对象
 * @property {Error|null} error - 任务执行失败时的错误对象
 * @property {function|null} progressCallback - 进度回调函数，用于向上层报告执行进度
 * @property {Array} progressHistory - 进度历史记录数组，存储所有进度更新
 * 
 * @example
 * // 继承 TaskBase 创建具体任务
 * class MyTask extends TaskBase {
 *   async execute(context, progressCallback) {
 *     this.setProgressCallback(progressCallback);
 *     this.start();
 *     
 *     // 执行具体业务逻辑
 *     this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
 *       detail: '正在处理数据',
 *       current: 50,
 *       total: 100
 *     });
 *     
 *     const result = await this.doSomething();
 *     this.complete(result);
 *     return result;
 *   }
 * }
 */
class TaskBase {
    /**
     * @功能概述: TaskBase 类的构造函数，用于创建一个任务实例。
     * @param {string} name - 任务的名称，应该具有描述性，便于日志识别
     * 
     * @说明: 构造函数会自动生成唯一的任务ID，初始化所有状态属性，并设置初始状态为 PENDING
     * @初始化内容:
     *   - 生成唯一任务ID（包含时间戳和随机字符串）
     *   - 设置初始状态为 PENDING
     *   - 初始化时间戳、结果、错误等属性为 null
     *   - 创建空的进度历史记录数组
     * 
     * @example
     * const task = new MyTask('VideoProcessing');
     * // 任务ID示例: VideoProcessing-1640995200000-abc123def
     */
    constructor(name) {
        // 基础标识信息
        this.name = name; // 任务名称，用于日志和识别
        this.taskId = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; // 生成唯一任务ID
        
        // 状态管理
        this.status = TASK_STATUS.PENDING;  // 主状态，使用标准化常量
        this.subStatus = null; // 子状态，用于细粒度追踪
        
        // 时间追踪
        this.startTime = null; // 任务开始时间戳
        this.endTime = null; // 任务结束时间戳
        
        // 结果和错误
        this.result = null; // 任务执行结果
        this.error = null; // 任务执行错误
        
        // 进度监控
        this.progressCallback = null; // 进度回调函数
        this.progressHistory = []; // 进度历史记录数组
        
        // 记录任务实例创建日志
        logger.info(`${moduleLogPrefix}[TaskBase:${this.name}][constructor] 任务实例已创建，ID: ${this.taskId}`);
    }

    /**
     * @功能概述: 执行任务的核心逻辑。这是一个抽象方法，具体的任务子类需要覆盖 (override) 此方法来实现自己的逻辑。
     * @param {object} context - 包含前序任务输出和全局流程数据的共享上下文对象。
     *                           任务可以从 context 中读取数据，也可以将自己的输出写入 context (通常通过返回一个对象实现)。
     * @param {function} progressCallback - 进度回调函数，用于报告任务进度。
     *                                     回调函数接收一个进度数据对象作为参数。
     * 
     * @returns {Promise<any>} 任务的执行结果。这个结果通常会被合并到流程的共享上下文中。
     * @throws {Error} 如果任务执行失败，应该抛出错误。
     * 
     * @说明: 
     *   - async关键字表示这是一个异步方法，通常用于包含需要等待的操作 (如API调用、文件读写等)。
     *   - 子类在实现时，也应该将其定义为 async 方法。
     *   - 基类实现提供了基本的进度回调调用示例，但会抛出错误提示子类必须实现此方法。
     * 
     * @执行流程:
     *   1. 记录任务开始执行的日志
     *   2. 更新任务状态为 'running'
     *   3. 调用进度回调报告任务开始
     *   4. 抛出错误提示子类必须实现具体逻辑
     * 
     * @example
     * // 子类实现示例
     * async execute(context, progressCallback) {
     *   this.setProgressCallback(progressCallback);
     *   this.start();
     *   
     *   try {
     *     // 具体业务逻辑
     *     const result = await this.processData(context);
     *     this.complete(result);
     *     return result;
     *   } catch (error) {
     *     this.fail(error);
     *     throw error;
     *   }
     * }
     */
    async execute(context, progressCallback) {
        // 为当前任务的 execute 方法生成日志前缀
        const logPrefix = `${moduleLogPrefix}[TaskBase:${this.name}][execute] `;
        
        // 记录日志，表示任务开始执行，并打印出当前上下文对象中的所有键，方便了解任务接收到了哪些数据。
        logger.info(`${logPrefix}开始执行。上下文keys: ${Object.keys(context).join(', ')}`);
        
        // 将任务状态更新为 'running' (正在运行)
        this.status = 'running';

        // 步骤 P.TB.1: 如果 progressCallback 存在，则调用它，报告任务开始。
        if (progressCallback && typeof progressCallback === 'function') {
            try {
                // 日志：记录即将调用进度回调，指明任务状态为 'started'。
                logger.debug(`${logPrefix}调用进度回调 (progressCallback)，任务状态：started。`);
                progressCallback({ taskName: this.name, status: 'started' }); // 发送任务开始状态
            } catch (cbError) {
                // 日志：记录进度回调本身执行时发生的错误，避免影响主流程。
                logger.error(`${logPrefix}进度回调 (progressCallback) 自身执行出错 (状态：started): ${cbError.message}`);
            }
        }
        
        // 警告：基类方法未被子类实现
        logger.warn(`${logPrefix}警告: TaskBase.execute() 方法未在子类中实现。`);
        
        // 步骤 P.TB.2: 如果 progressCallback 存在且基类方法被不当调用（未被子类覆盖），则调用它，报告任务失败。
        // 这是一个示例，实际的成功/失败回调应在子类的实现中，在实际的 try/catch/finally 块中调用。
        if (progressCallback && typeof progressCallback === 'function') {
            try {
                // 日志：记录即将调用进度回调，指明任务状态为 'failed'，原因为方法未实现。
                logger.debug(`${logPrefix}调用进度回调 (progressCallback)，任务状态：failed (未在子类实现)。`);
                progressCallback({ 
                    taskName: this.name, 
                    status: 'failed', 
                    error: new Error('TaskBase.execute() must be implemented by subclasses.') 
                }); // 发送任务失败状态
            } catch (cbError) {
                // 日志：记录进度回调自身执行时发生的错误。
                logger.error(`${logPrefix}进度回调 (progressCallback) 自身执行出错 (状态：failed): ${cbError.message}`);
            }
        }
        
        // 抛出错误，强制子类实现此方法
        throw new Error('TaskBase.execute() must be implemented by subclasses.');
    }

    /**
     * @功能概述: 标准化进度报告方法，用于向上层报告任务执行进度
     * @param {string} status - 主状态（来自TASK_STATUS枚举）
     * @param {string|null} subStatus - 子状态（来自TASK_SUBSTATUS枚举），可选
     * @param {object} options - 进度选项对象
     * @param {number} [options.current=0] - 当前进度值
     * @param {number} [options.total=100] - 总进度值
     * @param {string} [options.detail=''] - 人类可读的详细描述
     * @param {any} [options.result=null] - 任务结果（仅完成时）
     * @param {object} [options.error=null] - 错误信息（仅失败时）
     * @param {...any} options - 其他额外的进度数据
     * 
     * @returns {object} 标准化的进度数据对象
     * 
     * @说明: 
     *   - 使用 createProgressData 工厂函数创建标准化的进度数据
     *   - 自动更新任务的内部状态
     *   - 将进度数据添加到历史记录中
     *   - 安全地调用进度回调函数，捕获回调异常
     * 
     * @执行流程:
     *   1. 使用工厂函数创建标准化进度数据
     *   2. 更新任务内部状态
     *   3. 将进度数据添加到历史记录
     *   4. 安全调用进度回调函数
     *   5. 返回进度数据对象
     * 
     * @example
     * // 报告处理进度
     * this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
     *   detail: '正在处理视频文件',
     *   current: 50,
     *   total: 100,
     *   technicalDetail: 'FFmpeg encoding in progress'
     * });
     */
    reportProgress(status, subStatus = null, options = {}) {
        // 使用标准化工厂函数创建进度数据对象
        const progressData = createProgressData({
            taskName: this.name, // 任务名称
            taskId: this.taskId, // 任务唯一标识
            status, // 主状态
            subStatus, // 子状态
            current: options.current || 0, // 当前进度值，默认0
            total: options.total || 100, // 总进度值，默认100
            detail: options.detail || '', // 详细描述，默认空字符串
            result: options.result || null, // 任务结果，默认null
            error: options.error || null, // 错误信息，默认null
            ...options // 展开其他选项
        });

        // 更新任务内部状态
        this.status = status; // 更新主状态
        this.subStatus = subStatus; // 更新子状态
        
        // 将进度数据添加到历史记录数组中
        this.progressHistory.push(progressData);
        
        // 安全地调用进度回调函数
        if (this.progressCallback && typeof this.progressCallback === 'function') {
            try {
                this.progressCallback(progressData); // 调用回调函数传递进度数据
            } catch (callbackError) {
                // 捕获回调函数执行异常，避免影响主流程
                console.warn(`[${this.name}] 进度回调异常: ${callbackError.message}`);
            }
        }
        
        return progressData; // 返回创建的进度数据对象
    }

    /**
     * @功能概述: LLM交互专用进度报告方法，为LLM相关操作提供细粒度的进度追踪
     * @param {string} phase - LLM处理阶段，可选值：
     *                         'preparing' - 准备请求参数
     *                         'sending' - 发送请求到LLM服务
     *                         'waiting' - 等待LLM响应
     *                         'receiving' - 接收LLM响应
     *                         'parsing' - 解析LLM响应
     *                         'validating' - 验证LLM响应数据
     * @param {string} detail - 详细描述，人类可读的进度说明
     * @param {object} additionalData - 额外数据对象
     * @param {number} [additionalData.current] - 当前进度值
     * @param {number} [additionalData.total] - 总进度值
     * @param {number} [additionalData.estimatedTimeRemaining] - 预估剩余时间（秒）
     * @param {...any} additionalData - 其他额外的进度数据
     * 
     * @returns {object} 标准化的LLM进度数据对象
     * 
     * @说明: 
     *   - 专门为LLM交互设计的进度报告方法
     *   - 自动映射LLM阶段到对应的子状态
     *   - 提供技术详情字段用于调试
     *   - 主状态固定为RUNNING，子状态根据阶段自动设置
     * 
     * @example
     * // LLM请求准备阶段
     * this.reportLLMProgress('preparing', '准备发送到Azure OpenAI', {
     *   current: 10,
     *   total: 100,
     *   requestSize: 1024
     * });
     * 
     * // LLM等待响应阶段
     * this.reportLLMProgress('waiting', '等待LLM处理请求', {
     *   current: 50,
     *   total: 100,
     *   estimatedTimeRemaining: 30
     * });
     */
    reportLLMProgress(phase, detail, additionalData = {}) {
        // 直接使用LLM专用工厂函数创建进度数据
        const progressData = createLLMProgressData(
            this.name,      // 任务名称
            this.taskId,    // 任务唯一标识
            phase,          // LLM处理阶段
            detail,         // 详细描述
            additionalData  // 额外数据
        );

        // 更新任务内部状态（从工厂函数返回的数据中提取）
        this.status = progressData.status; // 更新主状态
        this.subStatus = progressData.subStatus; // 更新子状态
        
        // 将进度数据添加到历史记录数组中
        this.progressHistory.push(progressData);
        
        // 安全地调用进度回调函数
        if (this.progressCallback && typeof this.progressCallback === 'function') {
            try {
                this.progressCallback(progressData); // 调用回调函数传递进度数据
            } catch (callbackError) {
                // 捕获回调函数执行异常，避免影响主流程
                console.warn(`[${this.name}] LLM进度回调异常: ${callbackError.message}`);
            }
        }
        
        return progressData; // 返回创建的进度数据对象
    }

    /**
     * @功能概述: 设置进度回调函数
     * @param {function} callback - 进度回调函数，接收进度数据对象作为参数
     * 
     * @说明: 
     *   - 用于设置任务的进度回调函数
     *   - 回调函数会在每次调用 reportProgress 时被调用
     *   - 建议在任务执行开始前设置回调函数
     * 
     * @example
     * const task = new MyTask('example');
     * task.setProgressCallback((progressData) => {
     *   console.log(`任务 ${progressData.taskName} 进度: ${progressData.progress.percentage}%`);
     * });
     */
    setProgressCallback(callback) {
        this.progressCallback = callback; // 设置进度回调函数
    }

    /**
     * @功能概述: 标记任务开始执行
     * @returns {object} 任务开始的进度数据对象
     * 
     * @说明: 
     *   - 记录任务开始时间戳
     *   - 设置任务状态为STARTED，子状态为INITIALIZING
     *   - 自动生成开始执行的详细描述
     * 
     * @执行流程:
     *   1. 记录当前时间戳为开始时间
     *   2. 调用reportProgress报告开始状态
     *   3. 返回进度数据对象
     * 
     * @example
     * async execute(context, progressCallback) {
     *   this.setProgressCallback(progressCallback);
     *   this.start(); // 标记任务开始
     *   // ... 执行具体逻辑
     * }
     */
    start() {
        this.startTime = Date.now(); // 记录任务开始时间戳
        return this.reportProgress(TASK_STATUS.STARTED, TASK_SUBSTATUS.INITIALIZING, {
            detail: `任务 ${this.name} 开始执行`, // 生成开始执行的描述
            current: 0, // 开始时进度为0
            total: 100 // 总进度100
        });
    }

    /**
     * @功能概述: 标记任务成功完成
     * @param {any} [result=null] - 任务执行结果，可以是任何类型的数据
     * @returns {object} 任务完成的进度数据对象
     * 
     * @说明: 
     *   - 记录任务结束时间戳
     *   - 保存任务执行结果
     *   - 设置任务状态为COMPLETED，进度为100%
     *   - 自动生成完成的详细描述
     * 
     * @执行流程:
     *   1. 记录当前时间戳为结束时间
     *   2. 保存任务执行结果
     *   3. 调用reportProgress报告完成状态
     *   4. 返回进度数据对象
     * 
     * @example
     * const processedData = await this.processVideo(videoPath);
     * this.complete(processedData); // 标记任务完成并保存结果
     */
    complete(result = null) {
        this.endTime = Date.now(); // 记录任务结束时间戳
        this.result = result; // 保存任务执行结果
        return this.reportProgress(TASK_STATUS.COMPLETED, TASK_SUBSTATUS.FINALIZING, {
            detail: `任务 ${this.name} 成功完成`, // 生成完成的描述
            current: 100, // 进度设置为100
            total: 100, // 总进度100
            result: result // 包含执行结果
        });
    }

    /**
     * @功能概述: 标记任务执行失败
     * @param {Error} error - 导致任务失败的错误对象
     * @returns {object} 任务失败的进度数据对象
     * 
     * @说明: 
     *   - 记录任务结束时间戳
     *   - 保存错误对象
     *   - 设置任务状态为FAILED
     *   - 自动生成失败的详细描述，包含错误信息
     *   - 构建标准化的错误对象，包含消息、类型和堆栈信息
     * 
     * @执行流程:
     *   1. 记录当前时间戳为结束时间
     *   2. 保存错误对象
     *   3. 调用reportProgress报告失败状态
     *   4. 返回进度数据对象
     * 
     * @example
     * try {
     *   await this.processData();
     * } catch (error) {
     *   this.fail(error); // 标记任务失败
     *   throw error; // 重新抛出错误
     * }
     */
    fail(error) {
        this.endTime = Date.now(); // 记录任务结束时间戳
        this.error = error; // 保存错误对象
        return this.reportProgress(TASK_STATUS.FAILED, null, {
            detail: `任务 ${this.name} 执行失败: ${error.message}`, // 生成失败描述，包含错误信息
            error: {
                message: error.message, // 错误消息
                type: error.constructor.name, // 错误类型
                stack: error.stack // 错误堆栈信息
            }
        });
    }

    /**
     * @功能概述: 获取当前任务的进度摘要信息
     * @returns {object} 任务进度摘要对象
     * @returns {string} returns.taskName - 任务名称
     * @returns {string} returns.taskId - 任务唯一标识
     * @returns {string} returns.status - 当前主状态
     * @returns {string|null} returns.subStatus - 当前子状态
     * @returns {number|null} returns.startTime - 开始时间戳
     * @returns {number|null} returns.endTime - 结束时间戳
     * @returns {number} returns.duration - 执行时长（毫秒）
     * @returns {object|null} returns.latestProgress - 最新的进度数据
     * 
     * @说明: 
     *   - 提供任务当前状态的快速概览
     *   - 包含时间统计信息
     *   - 包含最新的进度数据
     *   - 自动计算执行时长
     * 
     * @执行时长计算逻辑:
     *   - 如果任务已结束：endTime - startTime
     *   - 如果任务正在执行：当前时间 - startTime
     *   - 如果任务未开始：0
     * 
     * @example
     * const summary = task.getCurrentProgressSummary();
     * console.log(`任务 ${summary.taskName} 已执行 ${summary.duration}ms`);
     * console.log(`当前状态: ${summary.status}/${summary.subStatus}`);
     */
    getCurrentProgressSummary() {
        // 获取最新的进度数据（数组最后一个元素）
        const latest = this.progressHistory[this.progressHistory.length - 1];
        
        return {
            taskName: this.name, // 任务名称
            taskId: this.taskId, // 任务唯一标识
            status: this.status, // 当前主状态
            subStatus: this.subStatus, // 当前子状态
            startTime: this.startTime, // 开始时间戳
            endTime: this.endTime, // 结束时间戳
            // 计算执行时长：已结束则用结束时间，正在执行则用当前时间，未开始则为0
            duration: this.endTime ? 
                this.endTime - this.startTime : // 已结束：结束时间 - 开始时间
                (this.startTime ? Date.now() - this.startTime : 0), // 正在执行：当前时间 - 开始时间，未开始：0
            latestProgress: latest // 最新的进度数据对象
        };
    }

    /**
     * @功能概述: 计算任务从开始到结束（或当前）所花费的时间（毫秒）
     * @returns {number} 耗时（毫秒）。如果任务未开始或开始时间无效，返回0。
     */
    getElapsedTime() {
        if (!this.startTime || typeof this.startTime !== 'number') {
            // 记录警告日志，如果任务未开始或开始时间无效
            // 注意：这里的 moduleLogPrefix 是 TaskBase 的模块前缀，不是实例前缀
            logger.warn(`${moduleLogPrefix}[TaskBase:${this.name}][getElapsedTime] 任务未开始或开始时间无效 (startTime: ${this.startTime})，返回耗时0。`);
            return 0;
        }
        // 如果任务已结束且结束时间有效，则使用结束时间计算耗时
        const endTime = (this.endTime && typeof this.endTime === 'number') ? this.endTime : Date.now();
        return endTime - this.startTime;
    }

    /**
     * @功能概述: 收集任务执行的详细上下文信息，用于前端展示和调试
     * @returns {object} 详细的任务上下文信息对象
     *
     * @说明:
     *   - 收集任务执行过程中的所有关键信息
     *   - 包含输入参数、输出结果、执行统计、进度历史等
     *   - 专门为前端详细展示设计
     *   - 子类可以覆盖此方法添加特定的上下文信息
     *
     * @返回对象结构:
     *   - taskInfo: 任务基本信息
     *   - executionStats: 执行统计信息
     *   - progressHistory: 完整的进度历史
     *   - inputContext: 输入上下文（子类可扩展）
     *   - outputContext: 输出上下文（子类可扩展）
     *   - technicalDetails: 技术细节（子类可扩展）
     *
     * @example
     * // 在任务完成后收集上下文
     * const context = task.collectDetailedContext();
     * console.log('任务详细信息:', context);
     */
    collectDetailedContext() {
        const logPrefix = `${moduleLogPrefix}[TaskBase:${this.name}][collectDetailedContext]`;

        try {
            // 基础任务信息
            const taskInfo = {
                name: this.name,
                taskId: this.taskId,
                status: this.status,
                subStatus: this.subStatus,
                startTime: this.startTime,
                endTime: this.endTime,
                duration: this.getElapsedTime(),
                startTimeFormatted: this.startTime ? new Date(this.startTime).toISOString() : null,
                endTimeFormatted: this.endTime ? new Date(this.endTime).toISOString() : null
            };

            // 执行统计信息
            const executionStats = {
                totalProgressUpdates: this.progressHistory.length,
                isCompleted: this.status === TASK_STATUS.COMPLETED,
                isFailed: this.status === TASK_STATUS.FAILED,
                hasResult: this.result !== null,
                hasError: this.error !== null,
                executionDurationMs: this.getElapsedTime(),
                executionDurationSeconds: Math.round(this.getElapsedTime() / 1000 * 100) / 100
            };

            // 进度历史（最近10条，避免数据过大）
            const recentProgressHistory = this.progressHistory.slice(-10).map(progress => ({
                timestamp: progress.timestamp,
                status: progress.status,
                subStatus: progress.subStatus,
                detail: progress.detail,
                progress: progress.progress,
                technicalDetail: progress.technicalDetail || null
            }));

            // 输入上下文（基类提供基础结构，子类可扩展）
            const inputContext = {
                receivedAt: this.startTime,
                contextKeys: [], // 子类应该填充实际接收到的上下文键
                inputParameters: {} // 子类应该填充实际的输入参数
            };

            // 输出上下文（基类提供基础结构，子类可扩展）
            const outputContext = {
                result: this.result,
                resultType: this.result ? typeof this.result : null,
                resultKeys: this.result && typeof this.result === 'object' ? Object.keys(this.result) : [],
                error: this.error ? {
                    message: this.error.message,
                    type: this.error.constructor.name,
                    stack: this.error.stack
                } : null
            };

            // 技术细节（基类提供基础结构，子类可扩展）
            const technicalDetails = {
                memoryUsage: process.memoryUsage ? process.memoryUsage() : null,
                nodeVersion: process.version,
                platform: process.platform,
                architecture: process.arch,
                taskClassName: this.constructor.name
            };

            const detailedContext = {
                taskInfo,
                executionStats,
                progressHistory: recentProgressHistory,
                inputContext,
                outputContext,
                technicalDetails,
                collectedAt: new Date().toISOString(),
                collectionMethod: 'TaskBase.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集详细上下文信息，包含 ${Object.keys(detailedContext).length} 个主要部分`);
            return detailedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础信息，即使出错也要提供一些有用的信息
            return {
                taskInfo: {
                    name: this.name,
                    taskId: this.taskId,
                    status: this.status,
                    error: 'Failed to collect detailed context'
                },
                collectionError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString()
            };
        }
    }
}

// 导出 TaskBase 类供其他模块使用
module.exports = TaskBase;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}TaskBase 类已导出。`); 