/**
 * @功能概述: GetTranscriptionTaskByCloudflare 的独立测试脚本。
 *           可以直接通过 `node GetTranscriptionTaskByCloudflare.test.js` 执行。
 * @注意事项:
 *   - 测试包含真实的Cloudflare Workers AI API调用
 *   - 需要有效的API Token和Account ID配置
 *   - 需要测试音频文件存在
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const GetTranscriptionTaskByCloudflare = require('../GetTranscriptionTaskByCloudflare');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const config = require('../../config');

// 统一的测试日志前缀
const testLogPrefix = `[文件：GetTranscriptionTaskByCloudflare.test.js][Cloudflare转录任务测试]`;

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

// 生成随机ID的辅助函数
function generateRandomId() {
    return crypto.randomBytes(8).toString('hex');
}

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 GetTranscriptionTaskByCloudflare 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        assert(task instanceof GetTranscriptionTaskByCloudflare, '任务应为 GetTranscriptionTaskByCloudflare 的实例');
        assertEquals(task.name, 'GetTranscriptionTaskByCloudflare', '任务名称应为 GetTranscriptionTaskByCloudflare');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
        assert(task.taskId.includes('GetTranscriptionTaskByCloudflare'), '任务ID应包含任务名称');
    });

    await runSingleTest('2. 缺少必需字段 - reqId', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        const context = {
            videoIdentifier: 'test-video',
            audioFilePathInUploads: 'test.mp3',
            savePath: '/test/path'
        }; // 缺少 reqId
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            // 注意：在当前实现中，任务在验证阶段失败时可能不会调用进度回调
            // 所以我们不检查进度回调中是否有FAILED状态
        }
    });

    await runSingleTest('3. 缺少必需字段 - audioFilePathInUploads', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        const context = { 
            reqId: 'test-req-' + generateRandomId(),
            videoIdentifier: 'test-video',
            savePath: '/test/path'
        }; // 缺少 audioFilePathInUploads
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, 'audioFilePathInUploads', '错误消息应指明缺少audioFilePathInUploads字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('4. 音频文件不存在', async () => {
        // 临时设置配置以通过配置验证
        const originalToken = config.cloudflareApiToken;
        const originalAccountId = config.cloudflareAccountId;
        config.cloudflareApiToken = 'test-token';
        config.cloudflareAccountId = 'test-account';

        try {
            const task = new GetTranscriptionTaskByCloudflare();
            const context = {
                reqId: 'test-req-' + generateRandomId(),
                videoIdentifier: 'test-video-' + generateRandomId(),
                audioFilePathInUploads: '/nonexistent/path/test.mp3',
                savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
            };
            const progressLogs = [];

            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '不存在', '错误消息应指明文件不存在');
        } finally {
            // 恢复配置
            config.cloudflareApiToken = originalToken;
            config.cloudflareAccountId = originalAccountId;
        }
    });

    await runSingleTest('5. 配置验证 - 缺少Cloudflare配置', async () => {
        // 临时备份配置
        const originalToken = config.cloudflareApiToken;
        const originalAccountId = config.cloudflareAccountId;
        
        // 清空配置
        config.cloudflareApiToken = null;
        config.cloudflareAccountId = null;
        
        try {
            const task = new GetTranscriptionTaskByCloudflare();
            const context = {
                reqId: 'test-req-' + generateRandomId(),
                videoIdentifier: 'test-video-' + generateRandomId(),
                audioFilePathInUploads: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3',
                savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
            };
            const progressLogs = [];
            
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, 'Cloudflare Workers AI 配置参数', '错误消息应指明配置缺失');
        } finally {
            // 恢复配置
            config.cloudflareApiToken = originalToken;
            config.cloudflareAccountId = originalAccountId;
        }
    });

    await runSingleTest('6. 进度回调功能', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试基础进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'GetTranscriptionTaskByCloudflare', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');
    });

    await runSingleTest('7. LLM进度报告功能', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        const progressLogs = [];

        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试LLM专用进度报告
        task.reportLLMProgress('preparing', '准备Cloudflare Workers AI请求', {
            current: 30,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录LLM进度回调');
        assertEquals(progressLogs[0].taskName, 'GetTranscriptionTaskByCloudflare', 'LLM进度回调应包含正确的任务名称');
        assert(progressLogs[0].technicalDetail, 'LLM进度回调应包含技术详情');
    });

    await runSingleTest('8. 任务状态管理', async () => {
        const task = new GetTranscriptionTaskByCloudflare();

        // 测试初始状态
        assertEquals(task.status, TASK_STATUS.PENDING, '初始状态应为PENDING');

        // 测试开始状态
        task.start();
        assertEquals(task.status, TASK_STATUS.STARTED, '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');

        // 测试完成状态
        const testResult = { transcriptionStatus: 'success' };
        task.complete(testResult);
        assertEquals(task.status, TASK_STATUS.COMPLETED, '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');

        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('9. collectDetailedContext 方法', async () => {
        const task = new GetTranscriptionTaskByCloudflare();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
    });

    // --- 真实API调用测试 ---
    await runSingleTest('10. 真实Cloudflare API调用测试', async () => {
        // 检查配置是否可用
        logger.info(`${testLogPrefix} 检查配置: Token=${!!config.cloudflareApiToken}, AccountId=${!!config.cloudflareAccountId}`);
        if (!config.cloudflareApiToken || !config.cloudflareAccountId ||
            config.cloudflareApiToken === 'YOUR_API_TOKEN' || config.cloudflareAccountId === 'YOUR_ACCOUNT_ID') {
            logger.warn(`${testLogPrefix} 跳过真实API测试：缺少Cloudflare配置`);
            return;
        }

        // 检查测试音频文件是否存在
        const testAudioPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3';
        if (!fs.existsSync(testAudioPath)) {
            logger.warn(`${testLogPrefix} 跳过真实API测试：测试音频文件不存在 ${testAudioPath}`);
            return;
        }

        const task = new GetTranscriptionTaskByCloudflare();
        const context = {
            reqId: 'test-real-api-' + generateRandomId(),
            videoIdentifier: 'test-video-' + generateRandomId(),
            audioFilePathInUploads: testAudioPath,
            savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
        };
        const progressLogs = [];

        logger.info(`${testLogPrefix} 开始真实API调用测试...`);
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        // 验证结果
        assert(result, '任务执行应返回结果');
        assertEquals(result.transcriptionStatus, 'success', '转录状态应为success');
        assert(result.apiResponse, '应包含API响应');
        assert(result.apiResponse.text, 'API响应应包含转录文本');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        assert(task.result, '任务应保存执行结果');

        // 验证进度回调
        const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
        const hasCompletedProgress = progressLogs.some(p => p.status === TASK_STATUS.COMPLETED);
        assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');
        assert(hasCompletedProgress, '应记录 COMPLETED 状态的进度回调');

        // 验证转录文本
        logger.info(`${testLogPrefix} 转录文本预览: ${result.apiResponse.text.substring(0, 100)}...`);
        assert(result.apiResponse.text.length > 0, '转录文本不应为空');

        // 验证文件保存
        if (result.transcriptionJsonPath) {
            assert(fs.existsSync(result.transcriptionJsonPath), '转录JSON文件应已保存');
            logger.info(`${testLogPrefix} 转录文件已保存: ${result.transcriptionJsonPath}`);
        }

        logger.info(`${testLogPrefix} 真实API调用测试成功完成！`);
    });

    // --- 使用用户提供的上下文进行真实API调用测试 ---
    await runSingleTest('11. 使用用户提供的上下文进行真实API调用测试', async () => {
        // 检查配置是否可用
        logger.info(`${testLogPrefix} 检查配置: Token=${!!config.cloudflareApiToken}, AccountId=${!!config.cloudflareAccountId}`);
        if (!config.cloudflareApiToken || !config.cloudflareAccountId ||
            config.cloudflareApiToken === 'YOUR_API_TOKEN' || config.cloudflareAccountId === 'YOUR_ACCOUNT_ID') {
            logger.warn(`${testLogPrefix} 跳过真实API测试：缺少Cloudflare配置`);
            return;
        }

        // 用户提供的上下文
        const reqId = 'user-test-' + generateRandomId();
        const videoIdentifier = 'user-video-' + generateRandomId();
        const audioFilePathInUploads = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3';
        const savePath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output';

        // 检查测试音频文件是否存在
        if (!fs.existsSync(audioFilePathInUploads)) {
            logger.warn(`${testLogPrefix} 跳过真实API测试：测试音频文件不存在 ${audioFilePathInUploads}`);
            return;
        }

        const task = new GetTranscriptionTaskByCloudflare();
        const context = {
            reqId,
            videoIdentifier,
            audioFilePathInUploads,
            savePath
        };

        logger.info(`${testLogPrefix} 开始使用用户提供的上下文进行真实API调用测试...`);
        logger.info(`${testLogPrefix} 上下文: reqId=${reqId}, videoIdentifier=${videoIdentifier}`);
        logger.info(`${testLogPrefix} 上下文: audioFilePathInUploads=${audioFilePathInUploads}`);
        logger.info(`${testLogPrefix} 上下文: savePath=${savePath}`);

        const progressLogs = [];
        const result = await task.execute(context, (data) => {
            logger.info(`${testLogPrefix}[进度回调]: ${data.status} - ${data.subStatus} - ${data.detail}`);
            progressLogs.push(data);
        });

        // 验证结果
        assert(result, '任务执行应返回结果');
        assertEquals(result.transcriptionStatus, 'success', '转录状态应为success');
        assert(result.apiResponse, '应包含API响应');
        assert(result.apiResponse.text, 'API响应应包含转录文本');

        // 验证转录文本
        logger.info(`${testLogPrefix} 转录文本预览: ${result.apiResponse.text.substring(0, 100)}...`);
        assert(result.apiResponse.text.length > 0, '转录文本不应为空');

        // 验证文件保存
        if (result.transcriptionJsonPath) {
            assert(fs.existsSync(result.transcriptionJsonPath), '转录JSON文件应已保存');
            logger.info(`${testLogPrefix} 转录文件已保存: ${result.transcriptionJsonPath}`);
        }

        logger.info(`${testLogPrefix} 使用用户提供的上下文进行真实API调用测试成功完成！`);
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== GetTranscriptionTaskByCloudflare 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0);
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
