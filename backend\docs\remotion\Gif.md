# &lt;Gif&gt; - @remotion/gif

## 概述

`<Gif>` 组件用于显示与 Remotion 的 [`useCurrentFrame()`](./useCurrentFrame.md) 同步的 GIF 动画。这是 `@remotion/gif` 包的一部分。

**安装**:
```bash
npm i --save-exact @remotion/gif@4.0.331
```

## 语法

```typescript
import { Gif } from "@remotion/gif";
import { useVideoConfig, useRef } from "remotion";

<Gif
  ref={ref}
  src="https://media.giphy.com/media/3o72F7YT6s0EMFI0Za/giphy.gif"
  width={width}
  height={height}
  fit="fill"
  playbackRate={2}
/>
```

## 基础用法

### 1. 基础 GIF 显示

```typescript
import { Gif } from "@remotion/gif";
import { useVideoConfig, useRef } from "remotion";

export const MyComponent: React.FC = () => {
  const { width, height } = useVideoConfig();
  const ref = useRef<HTMLCanvasElement>(null);
  
  return (
    <Gif
      ref={ref}
      src="https://media.giphy.com/media/3o72F7YT6s0EMFI0Za/giphy.gif"
      width={width}
      height={height}
      fit="fill"
    />
  );
};
```

### 2. 本地 GIF 文件

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useVideoConfig } from "remotion";

export const LocalGif = () => {
  const { width, height } = useVideoConfig();
  
  return (
    <Gif
      src={staticFile("animations/loading.gif")}
      width={width}
      height={height}
      fit="contain"
    />
  );
};
```

## 核心属性

### src (必需)
- **类型**: `string`
- **描述**: GIF 的源地址，可以是 URL 或本地图片
- **注意**: 远程 GIF 需要支持 [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)

### width (必需)
- **类型**: `number`
- **描述**: 显示宽度

### height (必需)
- **类型**: `number`
- **描述**: 显示高度

### fit
- **类型**: `'fill' | 'contain' | 'cover'`
- **默认值**: `'fill'`
- **描述**: GIF 的适应方式
  - `'fill'`: 完全填充容器，必要时拉伸
  - `'contain'`: 缩放适应容器，保持宽高比
  - `'cover'`: 完全填充容器并保持宽高比，必要时裁剪

### playbackRate (v4.0.44+)
- **类型**: `number`
- **默认值**: `1`
- **描述**: 控制 GIF 动画播放速度
  - `1`: 正常速度
  - `< 1`: 减慢速度（如 0.5 为半速）
  - `> 1`: 加快速度（如 2.0 为双倍速）

### loopBehavior (v3.3.4+)
- **类型**: `'loop' | 'pause-after-finish' | 'unmount-after-finish'`
- **默认值**: `'loop'`
- **描述**: GIF 的循环行为
  - `'loop'`: 无限循环
  - `'pause-after-finish'`: 播放一次后显示最后一帧
  - `'unmount-after-finish'`: 播放一次后卸载组件

### onLoad
- **类型**: `(data: GifLoadData) => void`
- **描述**: GIF 加载完成后的回调函数

```typescript
interface GifLoadData {
  width: number;      // GIF 文件的像素宽度
  height: number;     // GIF 文件的像素高度
  delays: number[];   // 每帧的时间戳数组
  frames: ImageData[]; // 帧数据数组
}
```

### ref (v3.3.88+)
- **类型**: `React.Ref<HTMLCanvasElement>`
- **描述**: React ref，TypeScript 中需要指定为 `HTMLCanvasElement` 类型

### style
- **类型**: `React.CSSProperties`
- **描述**: 自定义 CSS 样式
- **注意**: 不能通过 style 设置 width 和 height，请使用对应的 props

## 实际应用场景

### 1. 加载动画

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useVideoConfig, useCurrentFrame } from "remotion";

const LoadingAnimation = () => {
  const { width, height } = useVideoConfig();
  const frame = useCurrentFrame();
  const isLoading = frame < 90; // 前3秒显示加载动画

  return (
    <>
      {isLoading && (
        <Gif
          src={staticFile("loading-spinner.gif")}
          width={100}
          height={100}
          fit="contain"
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)"
          }}
        />
      )}
    </>
  );
};
```

### 2. 表情和反应动画

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useCurrentFrame, interpolate } from "remotion";

const EmotionGifs = () => {
  const frame = useCurrentFrame();
  
  // 根据时间选择不同的表情
  const emotionIndex = Math.floor(interpolate(frame, [0, 300], [0, 3], {
    extrapolateRight: "clamp"
  }));
  
  const emotions = [
    "happy.gif",
    "surprised.gif", 
    "thinking.gif",
    "excited.gif"
  ];

  return (
    <Gif
      src={staticFile(`emotions/${emotions[emotionIndex]}`)}
      width={200}
      height={200}
      fit="contain"
      loopBehavior="loop"
    />
  );
};
```

### 3. 背景动画效果

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useVideoConfig } from "remotion";

const AnimatedBackground = () => {
  const { width, height } = useVideoConfig();

  return (
    <div style={{ position: "relative", width, height }}>
      {/* 背景 GIF */}
      <Gif
        src={staticFile("backgrounds/particles.gif")}
        width={width}
        height={height}
        fit="cover"
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          opacity: 0.3
        }}
      />
      
      {/* 前景内容 */}
      <div style={{
        position: "relative",
        zIndex: 1,
        padding: 20
      }}>
        <h1>内容标题</h1>
      </div>
    </div>
  );
};
```

### 4. 交互式 GIF 控制

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useCurrentFrame, interpolate } from "remotion";

const InteractiveGif = () => {
  const frame = useCurrentFrame();
  
  // 动态调整播放速度
  const playbackRate = interpolate(frame, [0, 150, 300], [0.5, 2, 1], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });
  
  // 动态调整大小
  const scale = interpolate(frame, [0, 60, 120], [0.5, 1.2, 1], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <Gif
      src={staticFile("interactive-animation.gif")}
      width={300}
      height={300}
      fit="contain"
      playbackRate={playbackRate}
      style={{
        transform: `scale(${scale})`,
        transition: "transform 0.3s ease"
      }}
    />
  );
};
```

### 5. GIF 数据处理

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useState } from "remotion";

const GifDataProcessor = () => {
  const [gifData, setGifData] = useState<any>(null);

  const handleGifLoad = (data: any) => {
    setGifData(data);
    console.log(`GIF 尺寸: ${data.width}x${data.height}`);
    console.log(`帧数: ${data.frames.length}`);
    console.log(`总时长: ${data.delays[data.delays.length - 1]}ms`);
  };

  return (
    <div>
      <Gif
        src={staticFile("data-gif.gif")}
        width={400}
        height={300}
        fit="contain"
        onLoad={handleGifLoad}
      />
      
      {gifData && (
        <div style={{ marginTop: 20 }}>
          <p>GIF 信息:</p>
          <p>尺寸: {gifData.width} x {gifData.height}</p>
          <p>帧数: {gifData.frames.length}</p>
        </div>
      )}
    </div>
  );
};
```

### 6. 多 GIF 序列

```typescript
import { Gif } from "@remotion/gif";
import { Sequence, staticFile } from "remotion";

const GifSequence = () => {
  return (
    <>
      {/* 第一个 GIF: 0-120帧 */}
      <Sequence from={0} durationInFrames={120}>
        <Gif
          src={staticFile("sequence/part1.gif")}
          width={400}
          height={300}
          fit="contain"
          loopBehavior="pause-after-finish"
        />
      </Sequence>
      
      {/* 第二个 GIF: 120-240帧 */}
      <Sequence from={120} durationInFrames={120}>
        <Gif
          src={staticFile("sequence/part2.gif")}
          width={400}
          height={300}
          fit="contain"
          playbackRate={1.5}
        />
      </Sequence>
      
      {/* 第三个 GIF: 240-360帧 */}
      <Sequence from={240} durationInFrames={120}>
        <Gif
          src={staticFile("sequence/part3.gif")}
          width={400}
          height={300}
          fit="contain"
          loopBehavior="unmount-after-finish"
        />
      </Sequence>
    </>
  );
};
```

### 7. 响应式 GIF 布局

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useVideoConfig } from "remotion";

const ResponsiveGifLayout = () => {
  const { width, height } = useVideoConfig();
  const isVertical = height > width;
  
  const gifSize = isVertical ? 
    { width: width * 0.8, height: width * 0.8 } :
    { width: height * 0.6, height: height * 0.6 };

  return (
    <div style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width,
      height
    }}>
      <Gif
        src={staticFile("responsive-animation.gif")}
        width={gifSize.width}
        height={gifSize.height}
        fit="contain"
        style={{
          borderRadius: 10,
          boxShadow: "0 4px 8px rgba(0,0,0,0.3)"
        }}
      />
    </div>
  );
};
```

## 高级配置

### 1. 错误处理和备用方案

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useState } from "remotion";

const ErrorHandledGif = () => {
  const [hasError, setHasError] = useState(false);

  return (
    <>
      {!hasError ? (
        <Gif
          src="https://example.com/might-fail.gif"
          width={300}
          height={200}
          fit="contain"
          onLoad={() => console.log("GIF 加载成功")}
          // 注意: Gif 组件没有 onError 属性，需要其他方式处理错误
        />
      ) : (
        <div style={{
          width: 300,
          height: 200,
          backgroundColor: "#f0f0f0",
          display: "flex",
          alignItems: "center",
          justifyContent: "center"
        }}>
          <p>GIF 加载失败</p>
        </div>
      )}
    </>
  );
};
```

### 2. 性能优化的 GIF 使用

```typescript
import { Gif } from "@remotion/gif";
import { staticFile, useCurrentFrame } from "remotion";

const OptimizedGif = () => {
  const frame = useCurrentFrame();
  const shouldShowGif = frame >= 30 && frame <= 270; // 只在特定时间显示

  return (
    <>
      {shouldShowGif && (
        <Gif
          src={staticFile("heavy-animation.gif")}
          width={400}
          height={300}
          fit="contain"
          playbackRate={1.2} // 稍微加速以减少播放时间
        />
      )}
    </>
  );
};
```

## 与 AnimatedImage 的区别

| 特性 | `<Gif>` | `<AnimatedImage>` |
|------|---------|-------------------|
| 支持格式 | 仅 GIF | GIF, AVIF, WebP |
| Safari 支持 | ✅ (JavaScript 解码器) | ❌ (依赖浏览器支持) |
| onLoad 回调 | ✅ | ❌ |
| 性能 | 中等 | 更好 |
| 兼容性 | 更好 | 现代浏览器 |

## 最佳实践

1. **文件大小**: 优化 GIF 文件大小以提高加载性能
2. **CORS 配置**: 确保远程 GIF 支持 CORS
3. **适应性**: 使用合适的 `fit` 属性保持视觉效果
4. **性能考虑**: 对于大型 GIF，考虑条件性加载
5. **循环控制**: 根据需求选择合适的 `loopBehavior`

## 相关 API

- [`getGifDurationInSeconds()`](./getGifDurationInSeconds.md) - 获取 GIF 时长
- [`preloadGif()`](./preloadGif.md) - 预加载 GIF
- [`<AnimatedImage>`](./AnimatedImage.md) - 动画图片组件
- [`staticFile()`](./staticFile.md) - 静态文件引用

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/gif/src/Gif.tsx)
