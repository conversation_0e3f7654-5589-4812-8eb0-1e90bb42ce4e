
// 导入 TaskBase 基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入文件系统操作模块，用于目录创建和文件存在性检查
const fs = require('fs');
// 导入路径处理模块，用于文件路径的构建和解析
const path = require('path');
// 导入FFmpeg封装库，用于视频到音频的转换
const ffmpeg = require('fluent-ffmpeg');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');
// 导入配置加载器，用于获取FFmpeg路径等配置信息
const loadConfig = require('../config');
// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入crypto模块，用于哈希处理
const crypto = require('crypto');

// 加载应用配置，包含FFmpeg和FFprobe的可执行文件路径
const config = loadConfig;

// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：ConvertToAudioForCloudflareTask.js][Cloudflare音频转换任务][模块初始化]';

// 模块初始化：配置FFmpeg和FFprobe的可执行文件路径
try {
    if (config.ffmpegPath) {
        ffmpeg.setFfmpegPath(config.ffmpegPath); // 设置FFmpeg可执行文件路径
        logger.info(`${taskModuleLogPrefix} FFmpeg 路径已成功设置为: ${config.ffmpegPath}`);
    }
    if (config.ffprobePath) {
        ffmpeg.setFfprobePath(config.ffprobePath); // 设置FFprobe可执行文件路径
        logger.info(`${taskModuleLogPrefix} FFprobe 路径已成功设置为: ${config.ffprobePath}`);
    }
} catch (e) {
    // 记录FFmpeg路径设置失败的错误，但不阻止模块加载
    logger.error(`${taskModuleLogPrefix} 设置 FFmpeg/FFprobe 路径失败: ${e.message}`);
}

/**
 * @功能概述: Cloudflare专用视频转音频任务类，负责将视频文件转换为极度压缩的MP3音频格式。
 *           专门针对Cloudflare Workers AI的文件大小限制进行优化，确保音频文件小于2MB。
 * 
 * @继承关系: 继承自 TaskBase，获得标准化的进度监控和状态管理能力
 * 
 * @Cloudflare优化特性:
 *   - 极低比特率: 32kbps (vs 原版128kbps)
 *   - 保持语音识别质量: 16kHz采样率
 *   - 单声道输出: 减少50%文件大小
 *   - 目标文件大小: <2MB (适合Cloudflare Workers AI)
 *   - 适用场景: 1小时内视频的语音转录
 * 
 * @进度阶段: 提供7个细粒度的进度阶段：
 *   1. STARTED + INITIALIZING (0%) - 任务开始
 *   2. RUNNING + INITIALIZING (5%) - 目录准备
 *   3. RUNNING + PROCESSING (10%) - 路径构建
 *   4. RUNNING + FFMPEG_STARTING (20%) - FFmpeg准备
 *   5. RUNNING + FFMPEG_PROCESSING (50%) - FFmpeg转换中
 *   6. RUNNING + FFMPEG_COMPLETED (90%) - FFmpeg完成
 *   7. COMPLETED + FINALIZING (100%) - 任务完成
 * 
 * @技术参数 (Cloudflare优化): 
 *   - 音频编码: libmp3lame (MP3格式)
 *   - 比特率: 32kbps (极度压缩，适合语音)
 *   - 采样率: 16000Hz (Whisper推荐)
 *   - 声道: 单声道 (最小文件大小)
 *   - 预期压缩比: 75-80% vs 原版
 */
class ConvertToAudioForCloudflareTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建Cloudflare专用音频转换任务实例
     * @param {string} [name='ConvertToAudioForCloudflareTask'] - 任务名称，用于日志标识和进度追踪
     * 
     * @说明: 调用父类构造函数初始化基础属性，设置实例级日志前缀
     */
    constructor(name = 'ConvertToAudioForCloudflareTask') {
        super(name); // 调用 TaskBase 构造函数，初始化任务ID、状态等基础属性
        // 设置实例级日志前缀，包含文件名、任务类型和实例名称
        this.instanceLogPrefix = `[文件：ConvertToAudioForCloudflareTask.js][Cloudflare音频转换任务][${this.name}]`;
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} ConvertToAudioForCloudflareTask 实例已创建。`);
    }

    
    async execute(context, progressCallback) {
        // 步骤 0: 上下文映射和参数解构
        // 如果上下文包含VideoClipAndCropTask的输出，需要映射到ConvertToAudioForCloudflareTask的输入
        let mappedContext = { ...context };

        // 检查是否有VideoClipAndCropTask的输出需要映射
        if (context.processedVideoPath) {
            // 将VideoClipAndCropTask的输出映射为ConvertToAudioForCloudflareTask的输入
            // 使用处理后的视频作为音频转换的输入
            mappedContext.originalVideoPath = context.processedVideoPath;
            mappedContext.originalVideoName = context.processedVideoFileName || path.basename(context.processedVideoPath);
            mappedContext.videoIdentifier = context.processedVideoIdentifier || context.videoIdentifier;

            // 设置音频文件保存路径（与视频文件同目录）
            const videoDir = path.dirname(context.processedVideoPath);
            if (!mappedContext.savePath) {
                mappedContext.savePath = videoDir;
            }
            if (!mappedContext.uploadedVideoDirPath) {
                mappedContext.uploadedVideoDirPath = videoDir;
            }

            logger.info(`${this.instanceLogPrefix}[execute] 检测到VideoClipAndCropTask输出，已映射上下文参数:`);
            logger.info(`${this.instanceLogPrefix}  - originalVideoPath: ${mappedContext.originalVideoPath}`);
            logger.info(`${this.instanceLogPrefix}  - originalVideoName: ${mappedContext.originalVideoName}`);
            logger.info(`${this.instanceLogPrefix}  - videoIdentifier: ${mappedContext.videoIdentifier}`);
            logger.info(`${this.instanceLogPrefix}  - savePath: ${mappedContext.savePath}`);
            logger.info(`${this.instanceLogPrefix}  - uploadedVideoDirPath: ${mappedContext.uploadedVideoDirPath}`);
        }

        // 如果仍然缺少uploadedVideoDirPath，尝试从其他字段推断
        if (!mappedContext.uploadedVideoDirPath && mappedContext.originalVideoPath) {
            mappedContext.uploadedVideoDirPath = path.dirname(mappedContext.originalVideoPath);
            logger.info(`${this.instanceLogPrefix}[execute] 从originalVideoPath推断uploadedVideoDirPath: ${mappedContext.uploadedVideoDirPath}`);
        }

        // 从映射后的上下文中解构所有必需参数，并构建执行级日志前缀
        const {
            reqId,
            videoIdentifier,
            uploadedVideoDirPath,
            savePath,
            originalVideoName,
            originalVideoPath
        } = mappedContext;
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${videoIdentifier}]`;

        // 设置进度回调函数，用于向上层报告执行进度
        this.setProgressCallback(progressCallback);
        // 使用标准化方法标记任务开始，自动报告STARTED状态
        this.start();
        logger.info(`${execLogPrefix}[execute] Cloudflare音频转换任务开始执行。Input context: ${JSON.stringify(context)}`); // Log entire context for debug

        // 步骤 1: 参数校验与准备
        const requiredFields = ['reqId', 'videoIdentifier', 'uploadedVideoDirPath', 'savePath', 'originalVideoName', 'originalVideoPath'];
        this.validateRequiredFields(mappedContext, requiredFields, execLogPrefix);
        logger.debug(`${execLogPrefix}[execute] 必需参数验证通过。`);

        logger.info(`${execLogPrefix}[步骤 2] 原始视频完整路径: ${originalVideoPath}`); // 记录原始视频文件的完整路径信息。
        if (!fs.existsSync(originalVideoPath)) { // 检查原始视频文件是否存在于文件系统中。
            const errorMsg = `原始视频文件不存在: ${originalVideoPath}`; // 如果文件不存在，构建错误消息。
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`); // 记录错误日志。
            this.fail(new Error(errorMsg)); // 将任务标记为失败，并报告错误。
            throw new Error(errorMsg); // 抛出错误，中断当前执行流程。
        }

        try { // 开始一个try-catch块，用于捕获并处理可能发生的错误。
            // 步骤 3: 工作目录准备 (使用 context.savePath) // 步骤3：准备用于保存输出文件的工作目录（使用上下文中的savePath）。
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, { // 报告任务进度，状态为"运行中"，子状态为"初始化"。
                detail: '准备工作目录和文件路径 (Cloudflare优化)', // 进度详情：正在准备工作目录和文件路径。
                current: 5, // 当前进度百分比：5%。
                total: 100 // 总进度百分比：100%。
            });

            const outputDir = savePath; // 将上下文中的保存路径赋值给输出目录变量。

            logger.debug(`${execLogPrefix}[步骤 3] 检查或创建输出目录: ${outputDir}`); // 记录调试信息，表示正在检查或创建输出目录。
            if (!fs.existsSync(outputDir)) { // 检查输出目录是否存在。
                logger.info(`${execLogPrefix}[步骤 3] 创建输出目录: ${outputDir}`); // 如果目录不存在，记录信息表示正在创建。
                fs.mkdirSync(outputDir, { recursive: true }); // 同步创建输出目录，如果父目录不存在也会一并创建。
                logger.debug(`${execLogPrefix}[步骤 3] 输出目录创建成功。`); // 记录调试信息，表示输出目录创建成功。
            } else { // 如果目录已存在。
                 logger.debug(`${execLogPrefix}[步骤 3] 输出目录已存在。`); // 记录调试信息，表示输出目录已存在。
            }

            // 步骤 4: 构建标准化文件名 (基于 videoIdentifier，添加cloudflare标识和时间戳)
            const generateSafeAudioFileName = (identifier) => { // 定义一个函数，用于生成安全的音频文件名。
                const hash = crypto.createHash('sha1').update(identifier).digest('hex').substring(0, 8); // 使用SHA1哈希算法生成标识符的哈希值，并取前8位。
                const cleanId = identifier.replace(/[^a-zA-Z0-9_.-]/g, '_').substring(0, 64); // 清理标识符，替换非法字符为下划线，并截取前64个字符。
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // 生成时间戳确保文件名唯一性
                return `${cleanId}_${hash}_cloudflare_audio_${timestamp}.mp3`; // 返回由清理后的ID、哈希、cloudflare标识、时间戳和固定后缀组成的音频文件名。
            };
            const audioFileName = generateSafeAudioFileName(videoIdentifier); // 调用函数生成最终的音频文件名。
            const audioFilePathInSavePath = path.join(outputDir, audioFileName); // 将输出目录和音频文件名拼接成完整的音频文件保存路径。

            logger.info(`${execLogPrefix}[步骤 4] Cloudflare优化音频文件名: ${audioFileName}, 完整路径: ${audioFilePathInSavePath}`); // 记录输出音频的文件名和完整路径。

            // 步骤 5: Cloudflare专用FFmpeg转换准备 // 步骤5：准备FFmpeg进行视频到音频的转换，使用Cloudflare优化设置。
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_STARTING, { // 报告任务进度，状态为"运行中"，子状态为"FFmpeg启动中"。
                detail: `准备使用FFmpeg转换 ${originalVideoName} (Cloudflare极度压缩)`, // 进度详情：准备使用FFmpeg转换原始视频。
                current: 20, // 当前进度百分比：20%。
                total: 100, // 总进度百分比：100%。
                technicalDetail: `输出路径: ${audioFilePathInSavePath}, 比特率: 32k, 采样率: 16kHz` // 技术详情：输出文件的路径和压缩参数。
            });
            logger.debug(`${execLogPrefix}[步骤 5] Cloudflare FFmpeg转换准备就绪，输入: ${originalVideoPath}, 输出: ${audioFilePathInSavePath}`); // 记录调试信息，表示FFmpeg转换已准备就绪，并显示输入输出路径。
            logger.info(`${execLogPrefix}[步骤 5] 使用Cloudflare优化设置: 32k比特率, 16kHz采样率, 单声道`); // 记录Cloudflare优化设置信息。

            // 创建一个Promise，用于封装FFmpeg的异步转换过程。
            const conversionPromise = new Promise((resolve, reject) => {
                const ffmpegCommandInstance = ffmpeg(originalVideoPath); // 创建FFmpeg命令实例，指定输入视频路径。
                if (config.ffmpegPath) ffmpegCommandInstance.setFfmpegPath(config.ffmpegPath); // 如果配置中指定了ffmpeg路径，则设置。
                if (config.ffprobePath) ffmpegCommandInstance.setFfprobePath(config.ffprobePath); // 如果配置中指定了ffprobe路径，则设置。

                ffmpegCommandInstance
                    .noVideo() // 设置FFmpeg不处理视频流，只处理音频。
                    .audioCodec('libmp3lame') // 设置音频编码器为libmp3lame，用于生成MP3。
                    .audioBitrate('32k') // 设置音频比特率为32kbps (极度压缩，适合Cloudflare)。
                    .audioFrequency(16000) // 设置音频采样率为16000Hz (Whisper推荐)。
                    .audioChannels(1) // 设置音频通道数为单声道 (最小文件大小)。
                    .outputOptions('-y') // 覆盖输出文件（如果存在）而不询问。
                    .outputOptions('-ac', '1') // 强制单声道输出
                    .outputOptions('-ar', '16000') // 强制16kHz采样率
                    .outputOptions('-b:a', '32k') // 强制32k比特率
                    .outputOptions('-compression_level', '9') // 最高压缩级别

                    .on('start', (commandLine) => { // 监听FFmpeg命令启动事件。
                        logger.info(`${execLogPrefix} Cloudflare FFmpeg命令启动: ${commandLine.substring(0, 200)}...`); // 记录FFmpeg启动的命令，截取前200字符。
                        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_PROCESSING, { // 报告任务进度，状态为"运行中"，子状态为"FFmpeg处理中"。
                            detail: `Cloudflare FFmpeg转换中: ${originalVideoName} (32k压缩)`, // 进度详情：FFmpeg正在转换原始视频。
                            current: 25, // 当前进度百分比：25%。
                            total: 100, // 总进度百分比：100%。
                            technicalDetail: commandLine.substring(0, 100) + '...' // 技术详情：FFmpeg命令的前100字符。
                        });
                    })

                    .on('progress', (progress) => { // 监听FFmpeg转换进度事件。
                         // More robust progress reporting if possible // 如果可能，进行更健壮的进度报告。
                         const percent = progress.percent ? Math.floor(progress.percent) : 50; // 计算FFmpeg转换的百分比，如果不存在则默认为50%。
                         this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_PROCESSING, { // 报告任务进度，状态为"运行中"，子状态为"FFmpeg处理中"。
                            detail: `Cloudflare FFmpeg转换进度: ${percent}% (32k压缩)`, // 进度详情：FFmpeg转换的百分比。
                            current: 20 + Math.floor(70 * (percent / 100)), // 将FFmpeg的0-100%进度映射到任务的20-90%进度。
                            total: 100, // 总进度百分比：100%。
                            technicalDetail: `FFmpeg: ${progress.timemark || 'N/A'}, 目标: <2MB` // 技术详情：FFmpeg的时间标记和目标文件大小。
                         });
                         logger.debug(`${execLogPrefix}[Cloudflare FFmpeg Event][progress] 进度: ${percent}%, timemark: ${progress.timemark}`); // 记录FFmpeg进度事件的调试信息。
                     })

                    .on('error', (err, stdout, stderr) => { // 监听FFmpeg转换错误事件。
                        logger.error(`${execLogPrefix}[ERROR] Cloudflare FFmpeg转换错误: ${err.message}`); // 记录FFmpeg转换错误信息。
                        if (stdout) logger.debug(`${execLogPrefix} Cloudflare FFmpeg stdout: ${stdout}`); // 如果有标准输出，记录FFmpeg的标准输出。
                        if (stderr) logger.error(`${execLogPrefix} Cloudflare FFmpeg stderr: ${stderr}`); // 如果有标准错误，记录FFmpeg的标准错误。

                        const ffmpegError = new Error(`Cloudflare FFmpeg音频转换失败: ${err.message}`); // 创建一个包含FFmpeg错误信息的Error对象。
                        ffmpegError.name = 'CloudflareFFmpegError'; // 设置错误名称。
                        ffmpegError.stdout = stdout; // 将FFmpeg的标准输出附加到错误对象。
                        ffmpegError.stderr = stderr; // 将FFmpeg的标准错误附加到错误对象。
                        logger.error(`${execLogPrefix}[ERROR_DETAILS] Cloudflare FFmpeg Error Name: ${ffmpegError.name}`);
                        logger.error(`${execLogPrefix}[ERROR_DETAILS] Cloudflare FFmpeg Error Message: ${ffmpegError.message}`);
                        if (stdout) logger.error(`${execLogPrefix}[ERROR_DETAILS] Cloudflare FFmpeg STDOUT: ${stdout}`);
                        if (stderr) logger.error(`${execLogPrefix}[ERROR_DETAILS] Cloudflare FFmpeg STDERR: ${stderr}`);

                        this.reportProgress(TASK_STATUS.FAILED, TASK_SUBSTATUS.FFMPEG_FAILED, { // 报告任务进度，状态为"失败"，子状态为"FFmpeg失败"。
                            detail: 'Cloudflare FFmpeg转换过程中发生错误', // 进度详情：FFmpeg转换过程中发生错误。
                            error: { // 错误详情对象。
                                message: ffmpegError.message, // 错误消息。
                                name: ffmpegError.name, // 错误名称。
                                stdout: stdout || 'N/A', // 标准输出，如果没有则为'N/A'。
                                stderr: stderr || 'N/A' // 标准错误，如果没有则为'N/A'。
                            }
                        });
                        reject(ffmpegError); // 拒绝Promise，传递FFmpeg错误。
                    })

                    .on('end', (stdout, stderr) => { // 监听FFmpeg转换完成事件。
                        logger.info(`${execLogPrefix} Cloudflare FFmpeg转换成功: ${audioFilePathInSavePath}`); // 记录FFmpeg转换成功信息和输出文件路径。
                        if (stdout) logger.debug(`${execLogPrefix} Cloudflare FFmpeg stdout (end): ${stdout}`); // 如果有标准输出，记录FFmpeg结束时的标准输出。
                        if (stderr) logger.debug(`${execLogPrefix} Cloudflare FFmpeg stderr (end): ${stderr}`); // 如果有标准错误，记录FFmpeg结束时的标准错误。

                        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FFMPEG_COMPLETED, { // 报告任务进度，状态为"运行中"，子状态为"FFmpeg完成"。
                            detail: 'Cloudflare FFmpeg转换完成 (32k压缩)', // 进度详情：FFmpeg转换完成。
                            current: 90, // 当前进度百分比：90%。
                            total: 100 // 总进度百分比：100%。
                        });

                        // 步骤 6: 构建完整的任务结果 (Cloudflare优化版本) // 步骤6：构建并返回完整的任务结果。

                        // 获取音频文件信息
                        let audioFileSize = 'N/A';
                        let audioFileSizeBytes = 0;
                        let audioFileSizeMB = 0;
                        try {
                            const audioStats = fs.statSync(audioFilePathInSavePath);
                            audioFileSizeBytes = audioStats.size;
                            audioFileSize = audioFileSizeBytes;
                            audioFileSizeMB = (audioFileSizeBytes / (1024 * 1024)).toFixed(2);
                            logger.info(`${execLogPrefix} Cloudflare音频文件大小: ${audioFileSizeMB}MB (${audioFileSizeBytes} bytes)`);

                            // 检查文件大小是否符合Cloudflare限制
                            if (audioFileSizeBytes > 2 * 1024 * 1024) { // 2MB限制
                                logger.warn(`${execLogPrefix} 警告: 音频文件大小 ${audioFileSizeMB}MB 可能超过Cloudflare Workers AI限制 (2MB)`);
                            } else {
                                logger.info(`${execLogPrefix} ✅ 音频文件大小 ${audioFileSizeMB}MB 符合Cloudflare Workers AI限制`);
                            }
                        } catch (statError) {
                            logger.warn(`${execLogPrefix} 无法获取音频文件大小: ${statError.message}`);
                        }

                        const taskResult = { // 定义完整的任务结果对象。
                            // 音频文件基本信息
                            audioFilePathInUploads: audioFilePathInSavePath, // 转换后的音频文件在保存目录下的完整路径。
                            audioFilePath: audioFilePathInSavePath, // 为兼容GenerateASSTask，添加audioFilePath字段
                            audioFileName: audioFileName, // 音频文件名
                            audioFileIdentifier: audioFileName.replace('.mp3', ''), // 音频文件标识符
                            audioFileSize: audioFileSize, // 音频文件大小（字节）
                            audioFileSizeMB: audioFileSizeMB, // 音频文件大小（MB）
                            cloudflareOptimized: true, // 标识为Cloudflare优化版本

                            // 音频技术参数 (Cloudflare优化)
                            finalAudioCodec: 'libmp3lame',
                            finalAudioFormat: 'mp3',
                            finalAudioBitrate: '32k', // Cloudflare优化: 32k vs 128k
                            finalAudioFrequency: 16000,
                            finalAudioChannels: 1,
                            compressionLevel: 9, // 最高压缩级别
                            optimizationTarget: 'cloudflare_workers_ai',

                            // 处理参数
                            videoIdentifier: videoIdentifier, // 透传视频标识符。
                            reqId: reqId, // 请求ID
                            savePath: outputDir, // 保存路径
                            originalVideoName: originalVideoName, // 原始视频名称
                            originalVideoPath: originalVideoPath, // 原始视频路径
                            uploadedVideoDirPath: outputDir, // 上传目录路径

                            // 转换状态
                            conversionSuccess: true,
                            conversionMethod: 'ffmpeg_fluent_cloudflare_optimized',
                            processingTime: new Date().toISOString(),

                            // 任务状态
                            taskStatus: 'completed',
                            taskResult: 'success'
                        };
                        resolve(taskResult); // 解决Promise，传递任务结果。
                    });

                ffmpegCommandInstance.save(audioFilePathInSavePath); // 执行FFmpeg命令，将输出保存到指定路径。

                // Restored manual timeout logic with more robust kill and logging
                const timeoutDuration = config.ffmpegTimeout || 300000;
                logger.info(`${execLogPrefix} Cloudflare FFmpeg转换超时设置为: ${timeoutDuration / 1000}秒 (手动管理)`);

                let ffmpegProcessKilled = false;
                const timeout = setTimeout(() => {
                    logger.warn(`${execLogPrefix}[TIMEOUT] Cloudflare FFmpeg转换已达到 ${timeoutDuration / 1000} 秒的超时限制。`);
                    try {
                        if (ffmpegCommandInstance && ffmpegCommandInstance.ffmpegProc && ffmpegCommandInstance.ffmpegProc.pid) {
                            logger.warn(`${execLogPrefix}[TIMEOUT] 尝试使用 SIGTERM 终止 Cloudflare FFmpeg 进程 (PID: ${ffmpegCommandInstance.ffmpegProc.pid})。`);
                            ffmpegCommandInstance.kill('SIGTERM');
                            ffmpegProcessKilled = true;
                            logger.warn(`${execLogPrefix}[TIMEOUT] 已发送 SIGTERM 信号。`);
                            // Give it a moment to terminate gracefully
                            setTimeout(() => {
                                if (ffmpegCommandInstance.ffmpegProc && ffmpegCommandInstance.ffmpegProc.killed === false) {
                                    logger.warn(`${execLogPrefix}[TIMEOUT] SIGTERM 后 Cloudflare FFmpeg 进程仍在运行，尝试 SIGKILL。`);
                                    ffmpegCommandInstance.kill('SIGKILL');
                                    logger.warn(`${execLogPrefix}[TIMEOUT] 已发送 SIGKILL 信号。`);
                                }
                            }, 2000); // Wait 2 seconds before SIGKILL
                        } else {
                            logger.warn(`${execLogPrefix}[TIMEOUT] 无法获取 Cloudflare FFmpeg 进程 PID，可能已经结束或未正确启动。`);
                        }
                    } catch (killError) {
                        logger.error(`${execLogPrefix}[TIMEOUT][ERROR] 终止 Cloudflare FFmpeg 进程时出错: ${killError.message}. FFmpeg进程可能仍在运行。`, killError);
                    }
                    const timeoutError = new Error(`Cloudflare FFmpeg转换超过 ${timeoutDuration / 1000} 秒未完成`);
                    timeoutError.name = 'CloudflareFFmpegTimeoutError';
                    this.reportProgress(TASK_STATUS.FAILED, TASK_SUBSTATUS.FFMPEG_FAILED, {
                        detail: `Cloudflare FFmpeg转换超时 (${timeoutDuration / 1000}秒)`,
                        error: { message: timeoutError.message, name: timeoutError.name }
                    });
                    if (!ffmpegProcessKilled) {
                         logger.warn(`${execLogPrefix}[TIMEOUT] Cloudflare FFmpeg 进程可能未被成功终止。`);
                    }
                    reject(timeoutError);
                }, timeoutDuration);

                ffmpegCommandInstance
                    .on('end', () => {
                        clearTimeout(timeout);
                        logger.info(`${execLogPrefix}[EVENT_END] Cloudflare FFmpeg 'end' 事件触发。清除超时。`);
                    })
                    .on('error', (err) => {
                        clearTimeout(timeout);
                        logger.error(`${execLogPrefix}[EVENT_ERROR] Cloudflare FFmpeg 'error' 事件触发。清除超时。错误: ${err.message}`);
                        // Note: The main error logging for FFmpeg errors is now within the .on('error') handler itself.
                    });
            });

            const result = await conversionPromise; // 等待FFmpeg转换Promise的完成，获取结果。
            logger.debug(`${execLogPrefix}[execute] Cloudflare FFmpeg转换Promise已解析。结果: ${JSON.stringify(result)}`); // 记录调试信息，表示FFmpeg转换Promise已解析，并显示结果。

            this.complete(result); // 将任务标记为完成，并传递转换结果。
            logger.debug(`${execLogPrefix}[execute] Cloudflare音频转换任务已标记为完成。`); // 记录调试信息，表示任务已标记为完成。
            logger.info(`${execLogPrefix}[execute] ✅ Cloudflare音频转换成功完成，文件大小: ${result.audioFileSizeMB}MB`); // 记录成功完成信息。
            return result; // 返回转换结果。

        } catch (error) {
            this.fail(error); // This will also call reportProgress with FAILED status
            logger.error(`${execLogPrefix}[execute][ERROR] Cloudflare音频转换任务执行失败: ${error.message}`, error); // Log with error object for stack
            throw error;
        }
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     *
     * @throws {Error} 当缺少任何必需字段时抛出错误，错误消息包含缺失的字段名
     *
     * @验证逻辑:
     *   - 遍历所有必需字段
     *   - 检查每个字段是否在上下文中存在且不为falsy值
     *   - 如果发现缺失字段，立即抛出错误
     *   - 所有字段验证通过后，记录验证成功的调试日志
     *
     * @错误处理: 直接抛出错误，由execute方法的catch块统一处理
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        // 遍历所有必需字段进行验证
        for (const field of requiredFields) {
            // 检查字段是否存在且不为falsy值（null、undefined、空字符串等）
            if (!context[field]) {
                // 构建详细的错误消息，包含缺失的字段名
                const errorMsg = `Cloudflare音频转换执行失败：上下文缺少必需字段 ${field}`;
                // 记录错误日志，便于问题定位
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                // 直接抛出错误，让execute方法的catch块统一处理
                throw new Error(errorMsg);
            }
        }
        // 所有字段验证通过，记录成功日志
        logger.debug(`${execLogPrefix} Cloudflare音频转换输入参数验证通过。`);
    }

    /**
     * @功能概述: 收集ConvertToAudioForCloudflareTask的详细上下文信息
     * @returns {object} 包含Cloudflare音频转换特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加Cloudflare音频转换特定的上下文信息
     *   - 包含FFmpeg处理详情、音频技术参数、Cloudflare优化设置等
     *   - 保持与现有逻辑的完全兼容性
     *
     * @返回对象扩展:
     *   - cloudflareOptimization: Cloudflare优化特定信息
     *   - audioProcessingDetails: 音频处理特定信息
     *   - ffmpegDetails: FFmpeg执行详情
     *   - compressionMetrics: 压缩指标
     *   - cloudflareCompatibility: Cloudflare兼容性信息
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取音频转换信息
            const taskResult = this.result || {};

            // Cloudflare优化特定信息
            const cloudflareOptimization = {
                taskType: 'ConvertToAudioForCloudflare',
                optimizationTarget: 'cloudflare_workers_ai',
                fileSizeLimit: '2MB',
                targetBitrate: '32kbps',
                compressionLevel: 'maximum',
                qualityTradeoff: 'size_over_quality',
                speechRecognitionOptimized: true,
                whisperCompatible: true,
                processingMode: 'extreme_compression'
            };

            // 音频处理特定信息
            const audioProcessingDetails = {
                supportedInputFormats: ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'm4v'],
                outputFormat: 'mp3',
                processingSteps: [
                    '参数验证',
                    '工作目录准备',
                    '文件名构建 (cloudflare标识)',
                    'FFmpeg转换准备 (极度压缩)',
                    'FFmpeg转换执行 (32k比特率)',
                    '文件大小验证',
                    '结果构建'
                ],
                qualitySettings: {
                    audioCodec: 'libmp3lame',
                    audioBitrate: '32k', // 极度压缩
                    audioFrequency: 16000,
                    audioChannels: 1,
                    compressionLevel: 9,
                    outputOptions: ['-y', '-ac', '1', '-ar', '16000', '-b:a', '32k', '-compression_level', '9']
                },
                timeout: config.ffmpegTimeout || 300000,
                timeoutManagement: 'manual_with_graceful_termination'
            };

            // FFmpeg执行详情
            const ffmpegDetails = {
                command: 'ffmpeg',
                inputProcessing: 'video_stream_extraction',
                outputProcessing: 'extreme_audio_compression',
                codecUsed: 'libmp3lame',
                progressTracking: 'enabled',
                errorHandling: 'comprehensive',
                timeoutHandling: 'SIGTERM_then_SIGKILL',
                pathConfiguration: {
                    ffmpegPath: config.ffmpegPath || 'system_default',
                    ffprobePath: config.ffprobePath || 'system_default'
                },
                cloudflareSpecific: true
            };

            // 压缩指标
            const compressionMetrics = {
                targetFileSize: '< 2MB',
                actualFileSize: taskResult.audioFileSizeMB || 'N/A',
                actualFileSizeBytes: taskResult.audioFileSize || 'N/A',
                compressionRatio: 'extreme',
                bitrateReduction: '75% (128k → 32k)',
                qualityLevel: 'speech_optimized',
                cloudflareCompliant: taskResult.audioFileSize ? (taskResult.audioFileSize < 2 * 1024 * 1024) : 'unknown'
            };

            // Cloudflare兼容性信息
            const cloudflareCompatibility = {
                workersAIReady: true,
                whisperModelSupport: {
                    'whisper-tiny-en': true,
                    'whisper-large-v3-turbo': true
                },
                fileSizeCompliance: taskResult.audioFileSize ? (taskResult.audioFileSize < 2 * 1024 * 1024) : 'unknown',
                formatSupport: 'mp3_base64_compatible',
                apiEndpoint: '/client/v4/accounts/{account_id}/ai/run/@cf/openai/whisper-*',
                expectedProcessingTime: '< 30 seconds',
                costOptimized: true
            };

            // 合并所有上下文信息
            const extendedContext = {
                ...baseContext,
                cloudflareOptimization,
                audioProcessingDetails,
                ffmpegDetails,
                compressionMetrics,
                cloudflareCompatibility,
                collectionMethod: 'ConvertToAudioForCloudflareTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集ConvertToAudioForCloudflareTask详细上下文信息`);
            logger.info(`${logPrefix} 上下文包含 ${Object.keys(extendedContext).length} 个主要部分`);

            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                cloudflareProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectionMethod: 'ConvertToAudioForCloudflareTask.collectDetailedContext (with error)'
            };
        }
    }
}

// 导出ConvertToAudioForCloudflareTask类，供其他模块使用
module.exports = ConvertToAudioForCloudflareTask;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}ConvertToAudioForCloudflareTask 类已导出。`);
