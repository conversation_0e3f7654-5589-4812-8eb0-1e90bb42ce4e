<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置加载测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
</head>
<body>
    <div id="app">
        <div style="padding: 20px;">
            <h2>配置加载测试</h2>
            
            <!-- 配置加载状态 -->
            <div style="margin-bottom: 20px;">
                <h3>配置加载状态:</h3>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <p><strong>是否正在加载:</strong> {{ configLoadStatus.isLoading }}</p>
                    <p><strong>是否已加载:</strong> {{ configLoadStatus.isLoaded }}</p>
                    <p><strong>配置来源:</strong> {{ configLoadStatus.source }}</p>
                    <p v-if="configLoadStatus.error"><strong>错误信息:</strong> {{ configLoadStatus.error }}</p>
                </div>
            </div>

            <!-- 视频配置 -->
            <div style="margin-bottom: 20px;">
                <h3>视频配置:</h3>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <pre>{{ JSON.stringify(videoConfig, null, 2) }}</pre>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div>
                <button @click="loadConfig" :disabled="configLoadStatus.isLoading">
                    {{ configLoadStatus.isLoading ? '加载中...' : '重新加载配置' }}
                </button>
                <button @click="testAPI">测试API</button>
            </div>

            <!-- API测试结果 -->
            <div v-if="apiTestResult" style="margin-top: 20px;">
                <h3>API测试结果:</h3>
                <div style="padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                // 配置加载状态
                const configLoadStatus = ref({
                    isLoading: false,
                    isLoaded: false,
                    error: null,
                    source: 'frontend-default'
                });

                // 视频配置
                const videoConfig = ref({
                    repeatCount: 3,
                    backgroundStyle: "newspaper",
                    subtitleConfig: {
                        videoGuide: {
                            enabled: true,
                            title1: "坚持30天",
                            title2: "听懂国外新闻"
                        },
                        advertisement: {
                            enabled: true,
                            titles: [
                                {
                                    line1: "🌍关注水蜜桃英语，",
                                    line2: "每天2分钟，听全球要闻！"
                                }
                            ]
                        }
                    }
                });

                // API测试结果
                const apiTestResult = ref(null);

                // 从后端加载配置
                const loadConfig = async () => {
                    console.log('开始加载配置...');
                    configLoadStatus.value.isLoading = true;
                    configLoadStatus.value.error = null;

                    try {
                        const response = await fetch('/api/video/getVideoConfig', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const result = await response.json();
                        console.log('后端响应:', result);

                        if (!result.success || !result.data) {
                            throw new Error(`后端返回格式错误: ${result.error || '未知错误'}`);
                        }

                        // 应用配置
                        const backendConfig = result.data;
                        if (backendConfig.repeatCount) {
                            videoConfig.value.repeatCount = backendConfig.repeatCount;
                        }
                        if (backendConfig.backgroundStyle) {
                            videoConfig.value.backgroundStyle = backendConfig.backgroundStyle;
                        }
                        if (backendConfig.subtitleConfig) {
                            if (backendConfig.subtitleConfig.videoGuide) {
                                Object.assign(videoConfig.value.subtitleConfig.videoGuide, backendConfig.subtitleConfig.videoGuide);
                            }
                            if (backendConfig.subtitleConfig.advertisement) {
                                Object.assign(videoConfig.value.subtitleConfig.advertisement, backendConfig.subtitleConfig.advertisement);
                            }
                        }

                        configLoadStatus.value.isLoaded = true;
                        configLoadStatus.value.source = 'backend-api';
                        console.log('配置加载成功');

                    } catch (error) {
                        console.error('配置加载失败:', error);
                        configLoadStatus.value.error = error.message;
                        configLoadStatus.value.source = 'backend-error';
                    } finally {
                        configLoadStatus.value.isLoading = false;
                    }
                };

                // 测试API
                const testAPI = async () => {
                    try {
                        const response = await fetch('/api/video/getVideoConfig');
                        const result = await response.json();
                        apiTestResult.value = result;
                    } catch (error) {
                        apiTestResult.value = { error: error.message };
                    }
                };

                // 页面加载时自动加载配置
                loadConfig();

                return {
                    configLoadStatus,
                    videoConfig,
                    apiTestResult,
                    loadConfig,
                    testAPI
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
