/**
 * @功能概述: 文字区域组件 - 用于在视频播放完毕后显示文字内容
 * @组件类型: Remotion组件
 * @使用场景: 9:16短视频中，在原始16:9视频播放完毕后的剩余音频时间内显示文字
 * @技术规格: 默认尺寸1080x608px，背景色#3B3B3B，居中显示
 */

import React from 'react';
import { AbsoluteFill } from 'remotion';

/**
 * TextArea组件 - 文字区域显示组件
 * @param {Object} props - 组件属性
 * @param {string} props.backgroundColor - 背景颜色，默认为#3B3B3B
 * @param {string|number} props.width - 宽度，默认为1080px
 * @param {string|number} props.height - 高度，默认为608px
 * @param {string} props.text - 要显示的文字内容
 * @param {Object} props.style - 额外的样式对象
 * @param {Object} props.innerStyle - 内部文字的样式对象
 * @returns {JSX.Element} TextArea组件
 */
const TextArea = ({
    backgroundColor = '#3B3B3B',
    width = 1080,
    height = 608,
    text = '',
    style = {},
    innerStyle = {},
    ...otherProps
}) => {
    // 计算居中位置
    const containerStyle = {
        backgroundColor,
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        ...style
    };

    const textStyle = {
        color: '#FFFFFF',
        fontSize: '24px',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        padding: '20px',
        wordWrap: 'break-word',
        maxWidth: '100%',
        maxHeight: '100%',
        overflow: 'hidden',
        ...innerStyle // 合并用户提供的内部样式
    };

    return (
        <AbsoluteFill>
            <div style={containerStyle} {...otherProps}>
                <div style={textStyle}>
                    {text}
                </div>
            </div>
        </AbsoluteFill>
    );
};

export default TextArea;