/**
 * TestCloudflareTranscriptionOptimizationPipelineService 测试文件
 * 运行方式：node TestCloudflareTranscriptionOptimizationPipelineService.test.js
 * 环境变量：
 *   - RUN_LLM_TESTS=true  启用真实Cloudflare调用测试（耗时较长）
 *   - TEST_MODE=fast      仅运行快速测试（默认）
 */

const TestCloudflareTranscriptionOptimizationPipelineService = require('../TestCloudflareTranscriptionOptimizationPipelineService');
const logger = require('../../utils/logger');

// 测试配置
const TEST_CONFIG = {
    runLLMTests: process.env.RUN_LLM_TESTS === 'true',
    testMode: process.env.TEST_MODE || 'fast',
    timeout: process.env.RUN_LLM_TESTS === 'true' ? 300000 : 10000 // 5分钟 vs 10秒
};

// 测试日志前缀
const testLogPrefix = '[文件：TestCloudflareTranscriptionOptimizationPipelineService.test.js][Cloudflare转录优化流水线测试][测试执行]';

logger.info(`${testLogPrefix} 🧪 开始测试 TestCloudflareTranscriptionOptimizationPipelineService`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} 🤖 Cloudflare测试: ${TEST_CONFIG.runLLMTests ? '启用' : '禁用'}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 测试数据构建器
function createTestContext() {
    return {
        // 基础信息
        videoIdentifier: 'test_0612_cloudflare',
        reqId: 'test-cloudflare-transcription-7-16',

        // 使用指定的测试文件（Cloudflare优化版本）
        originalVideoPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612.mp4",
        originalVideoName: "test_0612.mp4", // ConvertToAudioForCloudflareTask需要的字段
        uploadedVideoDirPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input", // ConvertToAudioForCloudflareTask需要的字段
        savePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output"
    };
}

function buildInvalidContext() {
    return {
        reqId: 'invalid_test_req'
        // 故意缺少必需字段
    };
}

// Mock数据（用于快速测试）
const mockApiResponse = {
    task: 'transcribe',
    language: 'en',
    duration: 30.5,
    text: "Hello world, this is a test transcription from Cloudflare Workers AI.",
    segments: [
        {
            id: 0,
            seek: 0,
            start: 0.0,
            end: 2.5,
            text: "Hello world,",
            tokens: [],
            temperature: 0,
            avg_logprob: -0.23,
            compression_ratio: 1.8,
            no_speech_prob: 0.05,
            words: [
                { text: "Hello", start: 0.0, end: 0.8 },
                { text: "world,", start: 0.8, end: 2.5 }
            ]
        },
        {
            id: 1,
            seek: 0,
            start: 2.5,
            end: 5.0,
            text: "this is a test",
            tokens: [],
            temperature: 0,
            avg_logprob: -0.25,
            compression_ratio: 1.9,
            no_speech_prob: 0.03,
            words: [
                { text: "this", start: 2.5, end: 3.0 },
                { text: "is", start: 3.0, end: 3.2 },
                { text: "a", start: 3.2, end: 3.4 },
                { text: "test", start: 3.4, end: 5.0 }
            ]
        }
    ]
};

// 测试统计
let testStats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
};

// 简单测试框架（基于logger）
function test(name, testFn, skipCondition = false) {
    testStats.total++;
    
    if (skipCondition) {
        logger.info(`${testLogPrefix} ⏭️  SKIP: ${name}`);
        testStats.skipped++;
        return;
    }
    
    logger.debug(`${testLogPrefix} ▶️  START: ${name}`);
    
    return Promise.resolve()
        .then(() => testFn())
        .then(() => {
            logger.info(`${testLogPrefix} ✅ PASS: ${name}`);
            testStats.passed++;
        })
        .catch((error) => {
            logger.error(`${testLogPrefix} ❌ FAIL: ${name}`);
            logger.error(`${testLogPrefix}    错误: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix}    堆栈: ${error.stack.split('\n')[1]?.trim()}`);
            }
            testStats.failed++;
        });
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`期望 ${expected}，实际 ${actual}`);
            }
        },
        toEqual: (expected) => {
            if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                throw new Error(`期望 ${JSON.stringify(expected)}，实际 ${JSON.stringify(actual)}`);
            }
        },
        toHaveProperty: (prop) => {
            if (!(prop in actual)) {
                throw new Error(`期望包含属性 ${prop}`);
            }
        },
        toContain: (item) => {
            if (!actual.includes(item)) {
                throw new Error(`期望包含 ${item}`);
            }
        },
        toThrow: async (fn) => {
            let thrown = false;
            try {
                await fn();
            } catch (e) {
                thrown = true;
            }
            if (!thrown) {
                throw new Error('期望抛出异常但没有');
            }
        }
    };
}

// 主测试执行函数
async function runTests() {
    logger.info(`${testLogPrefix} 🚀 开始执行测试套件`);

    // 测试1: 构造函数测试
    await test('构造函数应该正确初始化', () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService('test-req-123');
        expect(service.reqId).toBe('test-req-123');
        expect(service.processingPipeline).toHaveProperty('tasks');
        expect(service.processingPipeline.tasks.length).toBe(10); // 更新为10个任务：ConvertToAudioForCloudflareTask + GetTranscriptionTaskByCloudflare + SubtitleOptimizationTask + TranscriptionCorrectionTask + ContentSummarizationTask + TranslateSubtitleTask + SubtitleClozeTask + BilingualSubtitleMergeTask + GenerateASSTask + GenerateVideoTask
    });

    // 测试2: 默认reqId测试
    await test('构造函数应该使用默认reqId', () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService();
        expect(service.reqId).toBe('unknown_test_cloudflare_req');
    });

    // 测试3: 输入验证测试
    await test('应该验证必需参数', async () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService('test-validation');
        const invalidContext = buildInvalidContext();

        await expect(() => service.validateInput(invalidContext)).toThrow();
    });

    // 测试4: 有效输入验证测试
    await test('应该通过有效输入验证', () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService('test-validation');
        const validContext = createTestContext();

        // 不应该抛出异常
        service.validateInput(validContext);
    });

    // 测试5: Mock流水线测试（快速测试）
    await test('应该正确处理Mock数据流水线', async () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService('test-mock-pipeline');

        // 创建Mock上下文，包含模拟的apiResponse
        const mockContext = {
            ...createTestContext(),
            // 模拟ConvertToAudioTask的输出
            audioFilePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\test_neetu_garcha_june_8_2025_audio.wav",
            audioFilePathInUploads: "uploads\\output\\test_neetu_garcha_june_8_2025_audio.wav",
            audioDuration: 30.5,
            audioFormat: "wav",
            // 模拟GetTranscriptionTaskByCloudflare的输出
            apiResponse: mockApiResponse,
            transcriptionStatus: "completed",
            transcriptionJsonPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\test_neetu_garcha_june_8_2025_transcription.json"
        };

        // Mock流水线执行方法，直接返回成功结果
        service.processingPipeline.execute = async (context) => {
            return {
                success: true,
                context: {
                    ...context,
                    // 模拟SubtitleOptimizationTask的输出
                    optimizedData: mockApiResponse.segments,
                    simplifiedSubtitleJsonArray: mockApiResponse.segments.map((seg, index) => ({
                        id: index,
                        start: seg.start,
                        end: seg.end,
                        text: seg.text,
                        words: seg.words
                    })),
                    fullTranscriptText: "Hello world, this is a test transcription from Cloudflare Workers AI.",
                    optimizedEnglishSrtContent: "1\n00:00:00,000 --> 00:00:02,500\nHello world,\n\n2\n00:00:02,500 --> 00:00:05,000\nthis is a test\n",
                    optimizationStatus: "completed",
                    // 模拟ContentSummarizationTask的输出
                    transcriptSummary: "这是一个来自Cloudflare Workers AI的测试转录内容，展示了语音识别技术的基本功能。",
                    transcriptTitle: "AI语音转录测试",
                    summaryJsonPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\test_neetu_garcha_june_8_2025_content_summary.json",
                    summaryStatus: "success",
                    dynamicVideoTitle: "AI语音转录测试 | 这是一个来自Cloudflare Workers AI的测试转录内容，展示了语音识别技术的基本功能。",
                    // 模拟TranslateSubtitleTask的输出
                    translatedSubtitleJsonArray: mockApiResponse.segments.map((seg, index) => ({
                        id: index,
                        start: seg.start,
                        end: seg.end,
                        text: seg.text === "Hello world," ? "你好世界，" : "这是一个测试",
                        words: seg.words
                    })),
                    translationStatus: "success",
                    // 模拟SubtitleClozeTask的输出
                    clozedSubtitleJsonArray: mockApiResponse.segments.map((seg, index) => ({
                        id: index,
                        start: seg.start,
                        end: seg.end,
                        text: seg.text.replace("world", "____"),
                        words: ["world"]
                    })),
                    clozeStatus: "success",
                    // 模拟BilingualSubtitleMergeTask的输出
                    enhancedBilingualSubtitleJsonArray: mockApiResponse.segments.map((seg, index) => ({
                        id: index,
                        start: seg.start,
                        end: seg.end,
                        text_english: seg.text,
                        text_chinese: seg.text === "Hello world," ? "你好世界，" : "这是一个测试",
                        words_explanation: { "world": "世界" }
                    })),
                    bilingualSubtitleMergeTaskStatus: "success",
                    // 模拟GenerateASSTask的输出
                    assFilePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\test_neetu_garcha_june_8_2025_extended_ass.ass",
                    assContent: "[Script Info]\nTitle: Test ASS\n[Events]\nDialogue: 0,0:00:00.00,0:00:02.50,Default,,0,0,0,,Hello world,",
                    // 模拟GenerateVideoTask的输出
                    finalVideoPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\test_neetu_garcha_june_8_2025_final_video.mp4",
                    videoGenerationStats: { totalDuration: 30.5, videoResolution: "1080x1920" },
                    // 模拟videoConfig配置
                    videoConfig: {
                        width: 1080,
                        height: 1920,
                        framerate: 30,
                        repeatCount: 3,
                        codec: "libx264",
                        preset: "slow",
                        crf: 20
                    }
                }
            };
        };

        const result = await service.processCloudflareTranscriptionOptimization(mockContext);

        expect(result).toHaveProperty('status');
        expect(result).toHaveProperty('context');
        expect(result.context).toHaveProperty('optimizedData');
        expect(result.context).toHaveProperty('simplifiedSubtitleJsonArray');
        expect(result.context).toHaveProperty('fullTranscriptText');
        expect(result.context).toHaveProperty('transcriptSummary');
        expect(result.context).toHaveProperty('transcriptTitle');
        expect(result.context).toHaveProperty('summaryStatus');
        expect(result.context).toHaveProperty('dynamicVideoTitle');
        expect(result.context).toHaveProperty('translatedSubtitleJsonArray');
        expect(result.context).toHaveProperty('clozedSubtitleJsonArray');
        expect(result.context).toHaveProperty('enhancedBilingualSubtitleJsonArray');
        expect(result.context).toHaveProperty('assFilePath');
        expect(result.context).toHaveProperty('finalVideoPath');

    }, TEST_CONFIG.testMode === 'mock-only');

    // 测试6: 真实Cloudflare流水线测试（需要真实文件和API调用）
    await test('应该执行完整的Cloudflare转录优化流水线', async () => {
        const service = new TestCloudflareTranscriptionOptimizationPipelineService('test-real-pipeline');
        const context = createTestContext();

        logger.info(`${testLogPrefix} 🔄 开始真实流水线测试，预计耗时2-5分钟...`);

        const result = await service.processCloudflareTranscriptionOptimization(context, (progress) => {
            logger.info(`${testLogPrefix} 📊 流水线进度: ${progress.taskName} - ${progress.status}`);
        });

        // 验证结果结构
        expect(result).toHaveProperty('status');
        expect(result).toHaveProperty('context');

        if (result.status === 'completed') {
            // 验证ConvertToAudioTask输出
            expect(result.context).toHaveProperty('audioFilePath');
            // 注意：audioDuration字段在ConvertToAudioTask中不存在，移除此验证

            // 验证GetTranscriptionTaskByCloudflare输出
            expect(result.context).toHaveProperty('apiResponse');
            expect(result.context).toHaveProperty('transcriptionStatus');

            // 验证SubtitleOptimizationTask输出
            expect(result.context).toHaveProperty('optimizedData');
            expect(result.context).toHaveProperty('simplifiedSubtitleJsonArray');
            expect(result.context).toHaveProperty('fullTranscriptText');
            expect(result.context).toHaveProperty('optimizedEnglishSrtContent');

            // 验证ContentSummarizationTask输出
            expect(result.context).toHaveProperty('transcriptSummary');
            expect(result.context).toHaveProperty('transcriptTitle');
            expect(result.context).toHaveProperty('summaryJsonPath');
            expect(result.context).toHaveProperty('summaryStatus');
            expect(result.context).toHaveProperty('dynamicVideoTitle');

            // 验证TranslateSubtitleTask输出
            expect(result.context).toHaveProperty('translatedSubtitleJsonArray');
            expect(result.context).toHaveProperty('translateSubtitleTaskStatus');

            // 验证SubtitleClozeTask输出
            expect(result.context).toHaveProperty('clozedSubtitleJsonArray');
            expect(result.context).toHaveProperty('subtitleClozeTaskStatus');

            // 验证BilingualSubtitleMergeTask输出
            expect(result.context).toHaveProperty('enhancedBilingualSubtitleJsonArray');
            expect(result.context).toHaveProperty('bilingualSubtitleMergeTaskStatus');

            // 验证GenerateASSTask输出
            expect(result.context).toHaveProperty('assFilePath');
            expect(result.context).toHaveProperty('assContent');

            // 验证GenerateVideoTask输出
            expect(result.context).toHaveProperty('finalVideoPath');
            expect(result.context).toHaveProperty('videoGenerationStats');

            logger.info(`${testLogPrefix} ✅ 真实流水线测试成功完成`);
            logger.info(`${testLogPrefix} 📄 生成的字幕segments数量: ${result.context.optimizedData?.length || 0}`);
            logger.info(`${testLogPrefix} 📁 音频文件路径: ${result.context.audioFilePath}`);
            logger.info(`${testLogPrefix} 📁 转录JSON路径: ${result.context.transcriptionJsonPath}`);
            logger.info(`${testLogPrefix} 📝 完整转录文本长度: ${result.context.fullTranscriptText?.length || 0} 字符`);
            logger.info(`${testLogPrefix} 📋 生成的标题: "${result.context.transcriptTitle}"`);
            logger.info(`${testLogPrefix} 📄 生成的摘要: "${result.context.transcriptSummary?.substring(0, 50)}..."`);
            logger.info(`${testLogPrefix} 🎬 动态视频标题: "${result.context.dynamicVideoTitle?.substring(0, 100)}..."`);
            logger.info(`${testLogPrefix} 📁 总结JSON路径: ${result.context.summaryJsonPath}`);
            logger.info(`${testLogPrefix} 🌐 翻译字幕数量: ${result.context.translatedSubtitleJsonArray?.length || 0}`);
            logger.info(`${testLogPrefix} 📝 挖空字幕数量: ${result.context.clozedSubtitleJsonArray?.length || 0}`);
            logger.info(`${testLogPrefix} 🔗 双语字幕数量: ${result.context.enhancedBilingualSubtitleJsonArray?.length || 0}`);
            logger.info(`${testLogPrefix} 📄 ASS文件路径: ${result.context.assFilePath}`);
            logger.info(`${testLogPrefix} 🎥 最终视频路径: ${result.context.finalVideoPath}`);
        } else {
            logger.error(`${testLogPrefix} ❌ 真实流水线测试失败: ${result.error}`);
            throw new Error(`流水线执行失败: ${result.error}`);
        }

    }, !TEST_CONFIG.runLLMTests); // 只有在启用LLM测试时才运行

    // 输出测试统计
    logger.info(`${testLogPrefix} 📊 测试完成统计:`);
    logger.info(`${testLogPrefix}    总计: ${testStats.total}`);
    logger.info(`${testLogPrefix}    通过: ${testStats.passed} ✅`);
    logger.info(`${testLogPrefix}    失败: ${testStats.failed} ❌`);
    logger.info(`${testLogPrefix}    跳过: ${testStats.skipped} ⏭️`);

    if (testStats.failed > 0) {
        logger.error(`${testLogPrefix} 🚨 有 ${testStats.failed} 个测试失败！`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试通过！`);
        process.exit(0);
    }
}

// 执行测试
if (require.main === module) {
    runTests().catch((error) => {
        logger.error(`${testLogPrefix} 💥 测试执行异常: ${error.message}`);
        logger.error(`${testLogPrefix} 💥 错误堆栈: ${error.stack}`);
        process.exit(1);
    });
}
