<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
</head>
<body>
    <div id="debug-info">
        <h2>调试信息</h2>
        <div id="console-output"></div>
    </div>

    <script>
        // 捕获所有控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const outputDiv = document.getElementById('console-output');
        
        function addToOutput(type, message) {
            const div = document.createElement('div');
            div.style.margin = '5px 0';
            div.style.padding = '5px';
            div.style.borderLeft = `3px solid ${type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'blue'}`;
            div.innerHTML = `<strong>[${type.toUpperCase()}]</strong> ${message}`;
            outputDiv.appendChild(div);
        }
        
        console.log = function(...args) {
            addToOutput('log', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToOutput('error', args.join(' '));
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToOutput('warn', args.join(' '));
            originalWarn.apply(console, args);
        };
        
        // 捕获未处理的错误
        window.addEventListener('error', function(event) {
            addToOutput('error', `未捕获错误: ${event.error.message} 在 ${event.filename}:${event.lineno}`);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addToOutput('error', `未处理的Promise拒绝: ${event.reason}`);
        });
        
        console.log('调试页面已加载');
        
        // 测试API调用
        async function testAPI() {
            try {
                console.log('开始测试API...');
                const response = await fetch('/api/video/getVideoConfig');
                console.log('API响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('API响应数据:', JSON.stringify(result, null, 2));
                
            } catch (error) {
                console.error('API测试失败:', error.message);
            }
        }
        
        // 自动测试API
        testAPI();
        
        // 尝试加载主应用的JS文件
        console.log('尝试加载主应用JS文件...');
        const script = document.createElement('script');
        script.src = '/js/app.js';
        script.onload = function() {
            console.log('主应用JS文件加载成功');
        };
        script.onerror = function() {
            console.error('主应用JS文件加载失败');
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
