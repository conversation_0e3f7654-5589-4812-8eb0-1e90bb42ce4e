# BBC新闻动画渲染API文档

## 📋 概述

BBC新闻动画渲染API专为Express+Puppeteer设计，用于生成BBC风格的新闻开场动画视频。

### 🎯 核心功能
- **GET接口**: 通过URL参数渲染BBC新闻开场动画
- **逐帧录制**: 30fps高质量视频帧序列生成
- **FFmpeg集成**: 自动合成MP4视频文件

### 🌐 访问地址
```
前端渲染页面: http://localhost:9999/api/bbc-render
```

---

## 🔧 API接口规范

### GET /api/bbc-render (前端渲染页面)

**请求方式**: GET
**URL格式**: `/api/bbc-render?参数名=参数值&...`

**基础示例**:
```
http://localhost:9999/api/bbc-render?chineseTitle=BBC英语精听&englishTitle=Breaking%20News&countdown=3&duration=4
```

**中文参数示例**:
```
http://localhost:9999/api/bbc-render?chineseTitle=%E4%BB%8A%E6%97%A5%E6%96%B0%E9%97%BB&englishTitle=Today%20News&subtitle=%E7%AA%81%E5%8F%91%E6%96%B0%E9%97%BB
```

**参数说明**:
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `chineseTitle` | string | `'BBC英语精听'` | 中文主标题 (需URL编码) |
| `englishTitle` | string | `'Breaking News'` | 英文标题 |
| `subtitle` | string | `'突发新闻'` | 中文副标题 (需URL编码) |
| `countdown` | number | `3` | 倒计时起始数字(1-10) |
| `duration` | number | `4` | 动画总时长(秒) |
| `isVideoMode` | boolean | `true` | 视频模式开关 |
| `backgroundImage` | string | `''` | 背景图片URL |
| `bbcLogoText` | string | `'BBC NEWS'` | BBC标志文字 |

### 🈳 中文参数处理方案

#### 方案1：URL编码 (推荐)
```javascript
// JavaScript中编码中文参数
const chineseTitle = '今日新闻';
const encodedTitle = encodeURIComponent(chineseTitle);
const url = `http://localhost:9999/api/bbc-render?chineseTitle=${encodedTitle}`;

// 结果: chineseTitle=%E4%BB%8A%E6%97%A5%E6%96%B0%E9%97%BB
```

#### 方案2：Express服务器端编码
```javascript
// Express服务器中处理中文参数
const params = {
  chineseTitle: '今日新闻',
  englishTitle: 'Today News',
  subtitle: '突发新闻'
};

// 构建URL
const queryString = new URLSearchParams();
Object.entries(params).forEach(([key, value]) => {
  queryString.append(key, value);
});

const url = `http://localhost:9999/api/bbc-render?${queryString}`;
```

#### 方案3：Base64编码 (备选)
```javascript
// 对于特殊字符，可使用Base64编码
const chineseTitle = '今日新闻';
const base64Title = btoa(unescape(encodeURIComponent(chineseTitle)));
const url = `http://localhost:9999/api/bbc-render?chineseTitle=${base64Title}&encoding=base64`;
```



---

## 🤖 Express + Puppeteer + FFmpeg 完整实现

### 1. 安装依赖

```bash
npm install express puppeteer fluent-ffmpeg
# 确保系统已安装 FFmpeg
```

### 2. 核心集成概念

Express服务器通过Puppeteer访问前端渲染页面，使用GET方式传递参数：

```javascript
const express = require('express');
const puppeteer = require('puppeteer');

// 构建GET请求URL
const params = new URLSearchParams({
  chineseTitle: '新闻标题',
  englishTitle: 'News Title',
  duration: '4',
  isVideoMode: 'true'
});

const renderUrl = `http://localhost:9999/api/bbc-render?${params}`;
await page.goto(renderUrl);
```

### 3. 前端页面控制

前端页面 (`http://localhost:9999/api/bbc-render`) 支持以下控制接口：

```javascript
// 设置动画参数
window.setBBCParams({
  chineseTitle: '自定义标题',
  englishTitle: 'Custom Title',
  duration: 5
});

// 设置动画时间点 (用于逐帧录制)
window.setVideoTime(1.5); // 设置到1.5秒位置

// 获取当前状态
const status = window.getBBCStatus();
```

---

## 🎬 使用示例

### 1. 直接访问渲染页面 (GET方式)

```bash
# 基础示例
curl "http://localhost:9999/api/bbc-render?chineseTitle=BBC英语精听&englishTitle=Breaking%20News&countdown=3&duration=4"

# 中文参数示例 (URL编码)
curl "http://localhost:9999/api/bbc-render?chineseTitle=%E4%BB%8A%E6%97%A5%E6%96%B0%E9%97%BB&englishTitle=Today%20News&subtitle=%E7%AA%81%E5%8F%91%E6%96%B0%E9%97%BB"
```

### 2. Express服务器集成示例

```javascript
// Express服务器端代码
const { chineseTitle, englishTitle, subtitle, countdown, duration } = requestData;

// 构建GET请求URL
const queryParams = new URLSearchParams({
  chineseTitle,
  englishTitle,
  subtitle,
  countdown: countdown.toString(),
  duration: duration.toString(),
  isVideoMode: 'true'
});

const renderUrl = `http://localhost:9999/api/bbc-render?${queryParams}`;

// 使用Puppeteer访问
await page.goto(renderUrl);
// ... 后续录制逻辑
```

### 3. JavaScript中处理中文参数

```javascript
// 方法1：使用URLSearchParams (推荐)
const params = {
  chineseTitle: '今日新闻',
  englishTitle: 'Today News',
  subtitle: '突发新闻',
  countdown: 3,
  duration: 4
};

const queryString = new URLSearchParams(params).toString();
const url = `http://localhost:9999/api/bbc-render?${queryString}`;

// 方法2：手动编码
const chineseTitle = encodeURIComponent('今日新闻');
const url2 = `http://localhost:9999/api/bbc-render?chineseTitle=${chineseTitle}`;

// 方法3：批量处理
const videos = [
  { chineseTitle: '新闻1', englishTitle: 'News 1' },
  { chineseTitle: '新闻2', englishTitle: 'News 2' }
];

for (const video of videos) {
  const queryString = new URLSearchParams(video).toString();
  const renderUrl = `http://localhost:9999/api/bbc-render?${queryString}`;

  // 使用Puppeteer访问渲染页面
  await page.goto(renderUrl);
  // ... 录制逻辑
}
```

### 4. 测试中文参数编码

```javascript
// 测试中文编码是否正确
const testParams = {
  chineseTitle: '测试中文标题',
  englishTitle: 'Test English Title',
  subtitle: '测试副标题'
};

console.log('原始参数:', testParams);

const encoded = new URLSearchParams(testParams).toString();
console.log('编码后:', encoded);

const decoded = new URLSearchParams(encoded);
console.log('解码后:', Object.fromEntries(decoded));
```

---

## ⚠️ 重要注意事项

### 1. 系统要求
- **FFmpeg**: 必须安装并在PATH中
- **内存**: 建议8GB以上
- **磁盘**: 每分钟视频约需100MB临时空间

### 2. 性能优化
- 使用 `headless: true` 提高性能
- 调整 `fps` 平衡质量和速度
- 并发限制：建议同时最多2个任务

### 3. 错误处理
- 自动清理临时文件
- 浏览器异常自动关闭
- 详细错误日志记录

### 4. 文件管理
- 定期清理输出目录
- 监控磁盘空间使用
- 设置文件大小限制

---

## � 故障排除

**1. FFmpeg未找到**
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# 下载并添加到PATH
```

**2. 内存不足**
- 减少并发任务数量
- 降低视频分辨率
- 增加系统内存

**3. 视频质量问题**
- 调整 `-crf` 参数 (18-28)
- 检查帧率设置
- 确认16:9比例

**4. 生成速度慢**
- 使用 `headless: true`
- 减少 `waitForTimeout` 时间
- 优化动画复杂度

---

## 📊 性能参考

| 配置 | 4秒视频 | 生成时间 | 文件大小 |
|------|---------|----------|----------|
| 1920x1080@30fps | 120帧 | ~45秒 | ~2.5MB |
| 1280x720@30fps | 120帧 | ~30秒 | ~1.8MB |
| 1920x1080@60fps | 240帧 | ~80秒 | ~4.2MB |

**测试环境**: Intel i7, 16GB RAM, SSD

---

## 🔄 最新更新说明

### v1.1.0 更新内容

#### 1. 修复强制视频模式问题
- **问题**: 之前版本强制设置 `isVideoMode: true`，导致所有动画暂停
- **修复**: 现在使用传入的实际参数值，支持正常播放和视频模式
- **影响**: URL参数中的 `isVideoMode=false` 现在能正确播放动画

#### 2. URL参数解析改进
- **中文参数处理**: 自动解码URL编码的中文字符
- **安全解码**: 解码失败时返回原值，避免程序崩溃
- **调试支持**: 添加参数解析状态显示

#### 3. 使用建议更新

**非视频模式 (推荐用于预览)**:
```
http://localhost:9999/api/bbc-render?chineseTitle=测试标题&isVideoMode=false
```
- 动画自动播放
- 适合预览和测试效果

**视频模式 (用于Puppeteer录制)**:
```
http://localhost:9999/api/bbc-render?chineseTitle=测试标题&isVideoMode=true
```
- 动画暂停等待外部控制
- 适合逐帧录制

---

## 🎥 Express程序员视频模式使用指南

### 核心概念理解

**视频模式 (`isVideoMode=true`)** 的工作原理：
1. 动画创建后立即暂停，不自动播放
2. 暴露 `window.setVideoTime(time)` 全局函数
3. 通过外部时间控制实现逐帧录制

### 🔑 关键决策：isVideoMode设置

**重要**：Puppeteer录制时的`isVideoMode`设置取决于录制方案：

| 录制方案 | isVideoMode设置 | 原因 |
|----------|----------------|------|
| **逐帧截图** | `true` | 需要暂停动画，手动控制时间 |
| **原生录制** | `false` | 让动画自动播放，录制整个过程 |

### 推荐录制方案

#### 方案1：逐帧截图 (推荐) - isVideoMode=true
```javascript
const express = require('express');
const puppeteer = require('puppeteer');

async function renderVideo(params) {
  const { chineseTitle, englishTitle, duration = 4, fps = 30 } = params;

  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });

  // 构建视频模式URL - 关键：isVideoMode=true
  const params = new URLSearchParams({
    chineseTitle,
    englishTitle,
    duration: duration.toString(),
    isVideoMode: 'true'  // ✅ 必须设置为true，暂停动画等待控制
  });

  await page.goto(`http://localhost:9999/api/bbc-render?${params}`);

  // 等待页面准备就绪
  await page.waitForFunction(() => window.setVideoTime);

  // 逐帧录制
  const totalFrames = Math.ceil(duration * fps);
  const frames = [];

  for (let frame = 0; frame < totalFrames; frame++) {
    const time = frame / fps;

    // 设置动画时间点 - 这就是为什么需要isVideoMode=true
    await page.evaluate((t) => {
      window.setVideoTime(t);
    }, time);

    // 等待渲染稳定
    await page.waitForTimeout(50);

    // 截图
    const screenshot = await page.screenshot({
      type: 'png',
      fullPage: false
    });

    frames.push(screenshot);
  }

  await browser.close();

  // 使用FFmpeg合成视频
  // ... FFmpeg处理逻辑
});
```

**为什么逐帧截图需要isVideoMode=true？**
- 动画暂停，不会自动播放
- 暴露`window.setVideoTime()`函数
- 可以精确控制动画到任意时间点
- 确保每帧截图的时间准确性

#### 方案2：Puppeteer原生录制 (实验性) - isVideoMode=false
```javascript
// 需要安装FFmpeg
async function renderVideoNative(params) {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();

  // 构建URL - 关键：isVideoMode=false
  const urlParams = new URLSearchParams({
    chineseTitle: params.chineseTitle,
    isVideoMode: 'false'  // ✅ 设置为false，让动画自动播放
  });

  await page.goto(`http://localhost:9999/api/bbc-render?${urlParams}`);

  // 开始录制
  const recorder = await page.screencast({
    path: 'output.webm',
    fps: 30
  });

  // 等待动画完成
  await page.waitForTimeout(params.duration * 1000 + 1000);

  // 停止录制
  await recorder.stop();
  await browser.close();
});
```

**为什么原生录制需要isVideoMode=false？**
- 动画自动播放，无需手动控制
- Puppeteer录制整个播放过程
- 简单但精度较低
- 依赖Puppeteer的录制能力

### 📊 录制方案对比

| 特性 | 逐帧截图 (isVideoMode=true) | 原生录制 (isVideoMode=false) |
|------|---------------------------|----------------------------|
| **精度** | ⭐⭐⭐⭐⭐ 完美 | ⭐⭐⭐ 一般 |
| **控制性** | ⭐⭐⭐⭐⭐ 完全控制 | ⭐⭐ 有限 |
| **文件大小** | ⭐⭐⭐ 中等 | ⭐⭐⭐⭐ 较小 |
| **开发复杂度** | ⭐⭐ 复杂 | ⭐⭐⭐⭐⭐ 简单 |
| **稳定性** | ⭐⭐⭐⭐⭐ 很稳定 | ⭐⭐⭐ 实验性 |
| **推荐度** | ✅ **强烈推荐** | ⚠️ 备选方案 |

### 🎯 最佳实践建议

**生产环境推荐**：
```javascript
// ✅ 推荐：逐帧截图方案
const params = new URLSearchParams({
  chineseTitle: '新闻标题',
  englishTitle: 'News Title',
  duration: '4',
  isVideoMode: 'true'  // 关键设置
});
```

**快速原型推荐**：
```javascript
// ⚠️ 备选：原生录制方案
const params = new URLSearchParams({
  chineseTitle: '新闻标题',
  englishTitle: 'News Title',
  duration: '4',
  isVideoMode: 'false'  // 关键设置
});
```

### 时间控制最佳实践

#### 1. 精确时间控制
```javascript
// 确保时间控制精度
const setAnimationTime = async (page, time) => {
  await page.evaluate((t) => {
    if (window.setVideoTime) {
      window.setVideoTime(t);
    }
  }, time);

  // 等待GSAP渲染
  await page.waitForTimeout(33); // ~30fps
};
```

#### 2. 状态检查
```javascript
// 检查页面是否准备就绪
const waitForVideoReady = async (page) => {
  await page.waitForFunction(() => {
    return window.setVideoTime &&
           document.body.getAttribute('data-video-ready') === 'true';
  }, { timeout: 10000 });
};
```

#### 3. 错误处理
```javascript
try {
  await page.evaluate((time) => {
    if (!window.setVideoTime) {
      throw new Error('视频模式未初始化');
    }
    window.setVideoTime(time);
  }, currentTime);
} catch (error) {
  console.error('时间设置失败:', error);
  // 重试或错误处理
}
```

### 性能优化建议

#### 1. 浏览器配置（重要：解决文字渲染问题）
```javascript
const browser = await puppeteer.launch({
  headless: true,
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    // 🔧 解决倒计时数字不显示问题的关键参数
    '--font-render-hinting=none',           // 修复字体渲染问题
    '--disable-font-subpixel-positioning',  // 禁用字体子像素定位
    '--disable-gpu-sandbox',                // 禁用GPU沙盒
    '--disable-software-rasterizer',        // 禁用软件光栅化
    '--disable-background-timer-throttling', // 禁用后台定时器限制
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding'
  ]
});
```

#### 2. 并发控制
```javascript
// 限制同时录制的任务数量
const semaphore = new Semaphore(2); // 最多2个并发任务

async function renderVideoWithSemaphore(params) {
  await semaphore.acquire();
  try {
    // 录制逻辑
  } finally {
    semaphore.release();
  }
}
```

#### 3. 内存管理
```javascript
// 定期清理页面
if (frameCount % 100 === 0) {
  await page.evaluate(() => {
    if (window.gc) window.gc();
  });
}
```

### 故障排除

#### 常见问题1：动画不暂停
```javascript
// 检查视频模式是否正确设置
const isVideoMode = await page.evaluate(() => {
  return window.getBBCStatus?.()?.params?.isVideoMode;
});
console.log('视频模式状态:', isVideoMode);
```

#### 常见问题2：时间控制无效
```javascript
// 验证时间控制函数是否存在
const hasTimeControl = await page.evaluate(() => {
  return typeof window.setVideoTime === 'function';
});
if (!hasTimeControl) {
  throw new Error('时间控制函数未找到');
}
```

#### 常见问题3：倒计时数字不显示 ⭐ 重要
```javascript
// 问题：红色圆形背景显示，但数字不可见
// 原因：视频模式下GSAP初始状态与内联样式冲突

// ✅ 解决方案1：检查参数传递
const params = new URLSearchParams({
  chineseTitle: '新闻标题',
  countdown: '3',        // 确保传递倒计时参数
  isVideoMode: 'true'
});

// ✅ 解决方案2：等待字体加载
await page.evaluateOnNewDocument(() => {
  document.fonts.ready.then(() => {
    console.log('字体加载完成');
  });
});

// ✅ 解决方案3：验证元素状态
const countdownVisible = await page.evaluate(() => {
  const element = document.querySelector('[class*="countdown"]');
  const style = window.getComputedStyle(element);
  return {
    opacity: style.opacity,
    transform: style.transform,
    color: style.color,
    textContent: element.textContent
  };
});
console.log('倒计时元素状态:', countdownVisible);
```

#### 常见问题4：截图质量问题
```javascript
// 优化截图设置
await page.screenshot({
  type: 'png',
  fullPage: false,
  clip: { x: 0, y: 0, width: 1920, height: 1080 },
  omitBackground: false
});
```

### 完整示例代码

参考 `docs/api/express-integration.md` 中的完整Express集成示例，包含：
- 完整的错误处理
- FFmpeg视频合成
- 批量处理支持
- 性能监控
