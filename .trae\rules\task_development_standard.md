---
type: "manual"
description: "globs:"
---
# 任务开发标准

本规则定义了项目中任务（Task）类的开发标准，特别是基于流水线架构的任务实现、标准化进度监控机制、与提示词模板的交互，以及强制性的单元测试要求。

## 任务模型概述

任务是流水线架构的基本执行单元，负责完成特定的业务逻辑。所有任务都应该继承自 `TaskBase` 类，并实现特定的接口和约定。

## 核心架构组件

### 1. 标准化进度监控基础设施

- **进度常量文件**: `@backend/src/constants/progress.js` - 集中管理所有进度相关的枚举、状态和数据结构
- **任务基类**: `@backend/src/class/TaskBase.js` - 提供标准化的进度监控、状态管理和详细上下文收集能力
- **流水线基类**: `@backend/src/class/PipelineBase.js` - 整合任务进度并向上层传递

### 2. TaskBase核心能力（基于最新实现）

#### 2.1 进度监控系统
- `reportProgress(status, subStatus, options)` - 标准化进度报告
- `reportLLMProgress(phase, detail, additionalData)` - LLM专用进度报告
- `setProgressCallback(callback)` - 设置进度回调函数
- 自动进度历史记录和状态管理

#### 2.2 任务生命周期管理
- `start()` - 标记任务开始，自动设置STARTED状态
- `complete(result)` - 标记任务完成，自动设置COMPLETED状态
- `fail(error)` - 标记任务失败，自动设置FAILED状态
- `getElapsedTime()` - 计算任务执行时长

#### 2.3 详细上下文收集系统
- `collectDetailedContext()` - 收集任务执行的详细上下文信息
- `getCurrentProgressSummary()` - 获取当前任务的进度摘要
- 自动收集执行统计、进度历史、技术细节等信息

### 3. 数据流向

```
TaskBase.reportProgress() → PipelineBase → Controller → Frontend (SSE)
TaskBase.collectDetailedContext() → checkTaskStatus API → Frontend Display
```

### 4. 进度监控层级

- **任务层**: 使用 `reportProgress()` 和 `reportLLMProgress()` 方法报告细粒度进度
- **流水线层**: 自动整合任务进度，生成任务快照和整体进度
- **控制器层**: 将进度数据转换为SSE事件发送给前端，通过checkTaskStatus API提供详细上下文

## 任务创建标准

### 基础结构要求

1. 文件位置：所有任务类都应放在 `backend/src/tasks/` 目录下
2. 命名约定：任务类名应使用 PascalCase 格式，并以 "Task" 结尾（例如：`ConvertToAudioTask`）
3. 继承关系：必须继承 `TaskBase` 类
4. 模块导出：每个任务文件应导出单个任务类
5. 专属测试文件：每个新创建的任务都必须配备一个专属的测试文件。此测试文件应位于 `backend/src/tasks/tests/` 目录下，并以 `[TaskName].test.js` 的格式命名。
6. 提示词模板使用：如果任务需要与 LLM 交互，必须使用 `promptTemplates.js` 服务获取提示词，而不是在任务代码中硬编码提示词

### 标准导入结构

每个任务文件必须包含以下标准导入，并根据需要导入其他文件：

```javascript
const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 根据需要导入其他依赖
const config = require('../config');
```

### **模块级日志前缀标准**

模块级日志前缀是任务文件中的一个强制性标准，它在模块初始化时定义，并用于该模块内所有日志输出的前缀。**这对于快速定位问题至关重要。** 当系统出现异常或需要追踪特定任务的行为时，通过日志前缀可以迅速过滤和识别出特定任务模块的日志，极大地提高了调试和问题排查的效率。

请确保每个任务文件都定义并使用一个唯一的、描述性的 `taskModuleLogPrefix`。

```javascript
const taskModuleLogPrefix = '[文件：[TaskName].js][任务中文名][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`);
```

## 标准化任务类结构模板

### 完整实现模板

以下是一个标准化任务类的完整实现模板，它包含了任务开发所需的所有核心组件和最佳实践。请根据您的具体任务需求，在此模板的基础上进行扩展和填充。

#### 模板结构说明

1.  标准导入 (Standard Imports)
    *   每个任务文件必须包含 `TaskBase`、`logger` 以及进度常量 `TASK_STATUS`, `TASK_SUBSTATUS` 的导入。
    *   根据任务需要，可以导入 `config` 或其他自定义模块。

2.  模块级日志前缀 (Module-level Log Prefix)
    *   强制要求定义 `taskModuleLogPrefix`，用于模块初始化时的日志输出，便于快速定位问题。
    *   格式应遵循 `[文件：[TaskName].js][任务中文名][模块初始化]`。

3.  任务类定义 (Task Class Definition)
    *   任务类必须继承自 `TaskBase`，以获得任务管理、进度报告等基础能力。
    *   类名应遵循 PascalCase 格式，并以 "Task" 结尾。

4.  构造函数 (Constructor)
    *   调用 `super(name)` 初始化父类。
    *   定义 `this.instanceLogPrefix`，用于该任务实例的所有日志输出，包含任务名称，进一步细化日志追踪。

5.  `execute` 方法 (核心执行逻辑)
    *   这是任务的核心入口点，所有业务逻辑都在此方法中实现。
    *   JSDoc 注释与上下文定义: 必须包含详细的 JSDoc 注释，说明功能概述、参数（特别是 `context` 对象的预期结构和必需字段）、返回值、可能抛出的错误以及详细的执行流程。
    *   参数校验: 在方法开始处，必须对 `context` 对象中的所有必需参数进行严格校验。建议定义并使用通用的校验函数（如 `validateRequiredFields`）以确保参数的完整性和正确性。
    *   日志前缀: 在 `execute` 方法内部，应定义 `execLogPrefix`，结合 `reqId` 和 `fileIdentifier`（如果适用），提供最细粒度的日志追踪。
    *   分步实现、注释与日志统一: 业务逻辑应按照执行流程的步骤，逐步实现任务的具体功能。每个主要步骤都应有清晰的注释说明其目的，并配合 `execLogPrefix` 输出相应的日志，确保日志与代码逻辑高度一致，便于追踪和问题定位。
    *   必要、及时的进度报告:
        *   通过 `this.setProgressCallback(progressCallback)` 设置回调函数。
        *   使用 `this.start()` 标记任务开始，自动报告 `STARTED` 状态。
        *   在任务执行过程中，应适时（即在关键业务节点或耗时操作前后）调用 `this.reportProgress()` 或 `this.reportLLMProgress()` 报告细粒度进度。
        *   任务成功完成时，调用 `this.complete()`。
        *   任务失败时，调用 `this.fail(error)`。
    *   错误处理: 必须在 `execute` 方法内部使用 `try-catch` 块捕获并处理所有预期和非预期异常，确保任务状态能正确更新为 `FAILED`，并记录详细错误信息。
    *   清晰的返回结果: `execute` 方法的返回值应是一个结构清晰、易于理解的对象。


#### 示例

```javascript


const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');

// TODO 根据需要导入其他依赖
const config = require('../config');


// 模块级日志前缀 - 标准格式
const taskModuleLogPrefix = '[文件：[TaskName].js][任务中文名][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`);

class MyTask extends TaskBase {


    constructor(name = 'MyTask') {
        super(name);
        this.instanceLogPrefix = `[文件：[TaskName].js][任务中文名][${this.name}]`;
        logger.info(`${this.instanceLogPrefix} 实例已创建。`);
    }

    /**
     * @功能概述: [详细的执行方法描述]
     * @param {object} context - 上下文对象，期望包含:
     *                           - 必需字段1: 类型 - 用途说明
     *                           - 必需字段2: 类型 - 用途说明
     * @param {function} progressCallback - 进度回调函数
     * @returns {Promise<object>} [返回值说明]
     * @throws {Error} [错误条件]
     * @执行流程:
     *   1. 参数校验与准备
     *   2. 主要业务逻辑处理
     *   3. 结果整理与输出
     */

    async execute(context, progressCallback) {

        // 01. 从上下文中解构必要参数
        // TODO: 根据实际业务需求，在此处解构任务所需的输入参数，例如 inputData, configOption, reqId 等。
        const { inputData, configOption, reqId } = context;

        // 任务执行日志前缀，包含请求ID和可选的业务标识符
        // TODO: 根据实际业务需求，可以在此处添加更多标识符，例如 fileId, businessKey 等，以增强日志可追溯性。
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}]`; // 示例：[FileID:xxx]

        // 2. 设置进度回调函数，用于向上层报告执行进度
        this.setProgressCallback(progressCallback);
        // 3. 使用标准化方法标记任务开始，自动报告STARTED状态
        this.start();

        //4. 提前声明并初始化所有任务处理结果相关的变量。

        let contextData_1=null;
        let contextData_2=null;

        try {
            // 5. 参数校验与准备
            // TODO: 根据当前任务的实际需求，定义并验证必需的上下文参数。
            // 示例：如果任务需要一个 'inputData' 字段和一个 'configOption' 字段。
            const requiredFields = ['inputData', 'configOption', 'reqId']; // 请根据实际业务需求调整，确保reqId也被验证
            this.validateRequiredFields(context, requiredFields, execLogPrefix);

            logger.info(`${execLogPrefix}[执行阶段] 任务开始执行主要业务逻辑。`);

            // 4. 主要业务逻辑处理
            // TODO: 在此处实现任务的核心业务逻辑。
            // 确保在关键业务节点或耗时操作前后，适时调用 this.reportProgress() 或 this.reportLLMProgress()


            // 假设业务逻辑完成后会产生一个结果对象
            const taskResult = {
                taskStatus: 'SUCCESS', // 任务最终状态,taskStatus根据具体任务名而改变。

                // TODO: 填充实际的业务结果数据，这些数据将作为任务的最终输出。
                contextData_1: contextData_1,
                contextData_2: contextData_2

            };

            // 5. 任务成功完成时，调用 this.complete()，并传入最终结果
            this.complete(taskResult);
            logger.info(`${execLogPrefix} 任务执行成功，返回结果。`);

            // 6. 返回结构清晰、易于理解的结果对象
            return taskResult;

        } catch (error) {
            // 7. 错误处理: 必须在 `execute` 方法内部使用 `try-catch` 块捕获并处理所有预期和非预期异常。
            // 使用标准化方法处理错误，自动设置失败状态、记录错误信息、调用进度回调。
            logger.error(`${execLogPrefix}[ERROR] 任务执行失败: ${error.message}`, error);
            this.fail(error);
            // 重新抛出错误，确保上层调用者（如流水线）能够感知到任务失败，并进行相应处理。
            throw error;
        }
    }





    /**
     * @功能概述: 确保上下文对象中包含所有必需的字段，否则抛出错误。
     * @param {object} context - 要验证的上下文对象。
     * @param {Array<string>} requiredFields - 必需字段名称数组。
     * @param {string} logPrefix - 日志前缀，用于错误日志记录。
     *
     * @throws {Error} 当缺少任何必需字段时抛出 `Error`，错误消息会明确指出缺失的字段名，并包含'执行失败'前缀。
     *
     * @验证逻辑:
     *   - 遍历 `requiredFields` 数组中的每个字段名。
     *   - 检查 `context` 对象中对应字段是否存在且不为 `undefined` 或 `null`。
     *   - 如果发现任何必需字段缺失，立即记录错误日志并抛出 `Error`。
     *   - 所有字段验证通过后，记录一条调试日志表示验证成功。
     *
     * @错误处理: 错误直接抛出，由调用方的 `catch` 块（例如 `execute` 方法）进行统一处理。
     */
    validateRequiredFields(context, requiredFields, logPrefix) {
        // 遍历所有必需字段进行验证
        for (const field of requiredFields) {
            // 检查字段是否存在且不为 undefined 或者 null
            if (context[field] === undefined || context[field] === null) {
                // 构建详细的错误消息，包含缺失的字段名
                const errorMsg = `执行失败：上下文缺少必需字段 "${field}"`;
                // 记录错误日志，使用传递进来的 logPrefix
                logger.error(`${logPrefix}[ERROR] ${errorMsg}`);
                // 直接抛出错误，让execute方法的catch块统一处理
                throw new Error(errorMsg);
            }
        }
        // 所有字段验证通过，记录成功日志，使用传递进来的 logPrefix
        logger.debug(`${logPrefix} 输入参数验证通过。`);
    }



    /**
     * @功能概述: 收集任务的详细上下文信息，用于前端展示和调试
     * @returns {object} 包含任务特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加任务特定的上下文信息
     *   - 遵循TaskBase标准化的上下文收集模式
     *   - 保持与其他任务类的一致性
     *
     * @返回对象扩展:
     *   - inputContext: 扩展的输入上下文信息
     *   - outputContext: 扩展的输出上下文信息
     *   - technicalDetails: 扩展的技术细节信息
     *   - taskSpecificDetails: 任务特定的详细信息
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                // TODO: 添加任务特定的输入信息
                inputData: taskResult.inputData || 'N/A',
                configOption: taskResult.configOption || 'N/A'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                // TODO: 添加任务特定的输出信息
                taskStatus: taskResult.taskStatus || 'N/A',
                processedData: taskResult.processedData ? 'available' : 'N/A'
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                // TODO: 添加任务特定的技术信息
                taskType: 'MyTask',
                processingMode: 'standard',
                supportedFormats: ['format1', 'format2']
            };

            // 任务特定的详细信息
            const taskSpecificDetails = {
                // TODO: 添加任务特有的详细信息
                processingSteps: [
                    '参数验证',
                    '数据处理',
                    '结果输出'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '结果输出' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中'
            };

            // 合并所有上下文信息（遵循TaskBase标准结构）
            const extendedContext = {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                taskSpecificDetails,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'MyTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集MyTask详细上下文信息`);
            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                taskProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'MyTask.collectDetailedContext (with error)'
            };
        }
    }

    /**
     * TODO 更多其他函数...
     */

}

module.exports = MyTask;
logger.info(`${taskModuleLogPrefix}MyTask 类已导出。`);
```

## 标准化进度监控机制

### 1. 基础进度报告

使用 `reportProgress()` 方法报告任务进度：

```javascript
this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
    detail: '正在处理数据', // 人类可读的详细描述
    current: 50, // 当前进度值
    total: 100, // 总进度值
    technicalDetail: '具体技术操作描述', // 技术详情，用于调试
    result: null, // 任务结果（仅完成时）
    error: null // 错误信息（仅失败时）
});
```

**进度报告自动功能**：
- 自动更新任务内部状态（`this.status`, `this.subStatus`）
- 自动添加到进度历史记录（`this.progressHistory`）
- 安全调用进度回调函数，捕获回调异常
- 使用标准化工厂函数创建进度数据对象

### 2. LLM专用进度报告

对于LLM相关任务，使用 `reportLLMProgress()` 方法：

```javascript
// LLM处理阶段：'preparing', 'sending', 'waiting', 'receiving', 'parsing', 'validating'
this.reportLLMProgress('waiting', '等待LLM处理请求', {
    current: 50,
    total: 100,
    estimatedTimeRemaining: 30, // 预估剩余时间（秒）
    requestSize: 1024, // 请求大小
    modelName: 'gpt-4' // 使用的模型名称
});
```

**LLM进度报告特性**：
- 自动映射LLM阶段到对应的子状态
- 主状态固定为RUNNING，子状态根据阶段自动设置
- 提供技术详情字段用于调试
- 专门为LLM交互设计的进度追踪

### 3. 标准化任务生命周期方法

- `this.start()` - 标记任务开始，自动报告STARTED状态，记录开始时间
- `this.complete(result)` - 标记任务完成，自动报告COMPLETED状态，记录结束时间和结果
- `this.fail(error)` - 标记任务失败，自动报告FAILED状态，记录结束时间和错误信息

**生命周期方法特性**：
- 自动时间戳记录（`startTime`, `endTime`）
- 自动状态转换和进度报告
- 标准化的错误对象构建（包含消息、类型、堆栈信息）

### 4. 详细上下文收集系统

TaskBase提供了强大的上下文收集能力，用于前端展示和调试：

#### 4.1 基础上下文收集

```javascript
// 在任务完成后收集详细上下文
const detailedContext = this.collectDetailedContext();
```

**基础上下文结构**（由TaskBase自动提供）：
```javascript
{
    taskInfo: {
        name: 'TaskName',
        taskId: 'TaskName-timestamp-randomId',
        status: 'completed',
        subStatus: 'finalizing',
        startTime: 1640995200000,
        endTime: 1640995260000,
        duration: 60000,
        startTimeFormatted: '2021-12-31T12:00:00.000Z',
        endTimeFormatted: '2021-12-31T12:01:00.000Z'
    },
    executionStats: {
        totalProgressUpdates: 15,
        isCompleted: true,
        isFailed: false,
        hasResult: true,
        hasError: false,
        executionDurationMs: 60000,
        executionDurationSeconds: 60.0
    },
    progressHistory: [...], // 最近10条进度记录
    inputContext: {...}, // 输入上下文（子类可扩展）
    outputContext: {...}, // 输出上下文（子类可扩展）
    technicalDetails: {...} // 技术细节（子类可扩展）
}
```

#### 4.2 子类扩展上下文收集

子类应该覆盖`collectDetailedContext()`方法来添加任务特定信息：

```javascript
collectDetailedContext() {
    // 获取基础上下文
    const baseContext = super.collectDetailedContext();

    // 扩展输入上下文
    const inputContext = {
        ...baseContext.inputContext,
        audioFilePath: this.result?.processedAudioPath || 'N/A',
        videoIdentifier: this.result?.videoIdentifier || 'N/A'
    };

    // 扩展输出上下文
    const outputContext = {
        ...baseContext.outputContext,
        transcriptionStatus: this.result?.transcriptionStatus || 'N/A',
        transcriptionText: this.result?.apiResponse?.text || 'N/A'
    };

    // 添加任务特定信息
    const taskSpecificDetails = {
        apiProvider: 'Azure OpenAI',
        processingSteps: ['验证', '调用API', '保存结果']
    };

    return {
        ...baseContext,
        inputContext,
        outputContext,
        taskSpecificDetails,
        collectionMethod: 'MyTask.collectDetailedContext'
    };
}
```

### 5. 进度阶段规划

每个任务应该规划清晰的进度阶段，例如：

```javascript
// 示例：视频转音频任务的进度阶段
// 0% - STARTED + INITIALIZING - 任务开始
// 5% - RUNNING + INITIALIZING - 目录准备
// 10% - RUNNING + PROCESSING - 路径构建
// 20% - RUNNING + FFMPEG_STARTING - FFmpeg准备
// 50% - RUNNING + FFMPEG_PROCESSING - FFmpeg转换中
// 90% - RUNNING + FFMPEG_COMPLETED - FFmpeg完成
// 100% - COMPLETED + FINALIZING - 任务完成
```

## 错误处理标准

### 1. 统一错误处理模式

```javascript
try {
    // 业务逻辑
    const result = await this.performOperation();
    return result;
} catch (error) {
    // 使用标准化错误处理
    this.fail(error);
    throw error; // 重新抛出，让流水线知道失败
}
```

### 2. 错误分类和日志记录

```javascript
// 分析错误类型，提供更精确的错误分类
let errorCategory = 'UNKNOWN_ERROR';

if (error.response) { 
    // API错误：收到了服务器的错误响应
    errorCategory = 'API_ERROR_RESPONSE';
    logger.error(`${execLogPrefix}[${errorCategory}] API错误响应状态码: ${error.response.status}`);
} else if (error.request) { 
    // API错误：请求已发出但未收到响应
    errorCategory = 'API_NO_RESPONSE';
    logger.error(`${execLogPrefix}[${errorCategory}] 请求已发出但未收到API响应`);
} else { 
    // 其他类型的错误
    errorCategory = 'REQUEST_SETUP_ERROR';
    logger.error(`${execLogPrefix}[${errorCategory}] 设置请求时发生错误: ${error.message}`);
}
```

### 3. 降级策略实现

```javascript
try {
    // 尝试主要处理方式
    const result = await this.primaryProcessing(data);
    return result;
} catch (primaryError) {
    logger.warn(`${execLogPrefix}[WARN] 主要处理失败，尝试降级策略: ${primaryError.message}`);
    
    try {
        // 降级处理方式
        const fallbackResult = await this.fallbackProcessing(data);
        logger.info(`${execLogPrefix} 降级策略成功`);
        return fallbackResult;
    } catch (fallbackError) {
        logger.error(`${execLogPrefix}[ERROR] 降级策略也失败: ${fallbackError.message}`);
        throw primaryError; // 抛出原始错误
    }
}
```

## 日志记录标准

### 1. 分层日志前缀体系

1. **模块级**：`[文件：TaskName.js][任务中文名][模块初始化]`
2. **实例级**：`[文件：TaskName.js][任务中文名][TaskName]`  
3. **执行级**：`[文件：TaskName.js][任务中文名][TaskName][ReqID:xxx][FileID:xxx]`



### 2. 关键数据记录

```javascript
// 配置验证日志
logger.info(`${execLogPrefix}[步骤 1] 配置验证: Endpoint=${endpoint}, KeyLoaded=${!!key}`);

// API响应预览
logger.debug(`${execLogPrefix}[步骤 2] API响应预览: ${JSON.stringify(response.data).substring(0, 200)}...`);

// 处理结果统计
logger.info(`${execLogPrefix}[步骤 3] 处理完成。输出长度: ${result.length} 字符`);
```

## LLM交互任务特殊要求

**对于需要与 LLM 服务交互的任务，必须严格遵循统一的 LLM API 调用标准。** 详细的调用规范、配置标准和最佳实践请参考 [llm_api_standard.mdc](mdc:llm_api_standard.mdc)。

If a Task calls an LLM service and the LLM's response is enforced to be in JSON format, the `extractAndParseJson()` function from the `@backend\src\utils\jsonValidator.js` module must be called to validate and repair the JSON generated by the LLM, ensuring the correctness and robustness of the data format.

## 文件保存策略

Any file saving operation within a Task must call the `saveDataToFile()` function from the `@backend\src\utils\fileSaver.js` module. Please strictly adhere to the parameter requirements of this function to ensure consistency and reliability in file saving operations.

## 任务索引文件更新

创建新任务后，必须更新 `@backend/src/tasks/index.js`：

```javascript
const ConvertToAudioTask = require('./convertToAudioTask');
const GetTranscriptionTask = require('./GetTranscriptionTask');
const TranscriptionCorrectionTask = require('./TranscriptionCorrectionTask');
const TranslateSubtitleTask = require('./TranslateSubtitleTask');
const NewTask = require('./NewTask'); // 添加新任务

module.exports = {
    ConvertToAudioTask,
    GetTranscriptionTask,
    TranscriptionCorrectionTask,
    TranslateSubtitleTask,
    NewTask // 导出新任务
};
```

## 单元测试要求

每个新创建或重要修改的任务都**必须**配备一个专属的、可独立执行的单元测试文件。此测试文件旨在验证任务的核心功能、参数处理、进度报告和错误处理机制。

### 1. 测试文件结构与位置

1.  位置：所有任务的测试文件应位于 `backend/src/tasks/tests/` 目录下。
2.  命名：测试文件名应严格遵循 `[TaskName].test.js` 的格式，例如 `MyTask.test.js`。
3.  独立性：每个测试文件应该是自包含的，可以直接通过 `node backend/src/tasks/tests/[TaskName].test.js` 命令执行，**严格禁止**依赖外部测试运行器（如Jest）。

**⚠️ 重要提醒：测试方法限制**
- **严格禁止使用Jest**：本项目的任务测试文件不允许使用Jest或任何其他外部测试框架
- **唯一允许的测试方法**：只能使用 `node + 测试文件` 的方式进行测试
- **执行命令格式**：`node backend/src/tasks/tests/[TaskName].test.js`
- **违规后果**：使用Jest或其他测试框架将被视为不符合项目标准

### 2. 核心测试理念

1.  覆盖关键路径：测试应覆盖任务的正常执行流程、所有预期的错误处理路径以及边界条件。
2.  数据隔离：测试应使用独立的、可控的输入数据。对于需要文件输入的情况，可以准备小型的、专用的测试数据文件，或在测试脚本中动态生成模拟数据。避免依赖外部大型文件或不稳定的数据源。
3.  结果明确：每个测试用例执行后，应通过日志清晰地指示成功或失败，并提供足够的上下文信息。
4.  进度验证：虽然精确的进度百分比可能难以断言，但应验证关键的进度回调（如 `started`, `completed`, `failed`, 以及特定子状态）是否按预期被调用。
5.  错误捕获：测试必须验证任务在遇到无效输入或内部错误时，是否能正确抛出异常并报告失败状态。
6.  禁止模拟LLM API响应：任务测试文件 **不允许** 通过 Mocking 或其他方式模拟 `llmService.callLLM` 或任何直接/间接调用 LLM 的行为。测试应该调用真实的 LLM 服务（尽管可以通过配置限制模型或参数以控制成本和时间）。

### 3. 测试配置与超时标准

考虑到任务大多数需要与LLM交互或生成视频等耗时操作，测试文件必须配置合理的超时时间：

#### 3.1 标准测试配置模板

```javascript
// 测试配置
const TEST_CONFIG = {
    testMode: process.env.TEST_MODE || 'normal',
    timeout: process.env.TEST_MODE === 'quick' ? 30000 : 600000 // 30秒 vs 10分钟
};
```

#### 3.2 超时时间标准

1. **默认模式 (normal)**: **10分钟 (600000ms)**
   - 适用于包含LLM交互、视频生成、音频处理等耗时操作的任务
   - 基于实际测试：5秒视频生成需要200秒以上，LLM调用可能需要30-60秒
   - 为复杂任务提供充足的执行时间

2. **快速模式 (quick)**: **30秒 (30000ms)**
   - 仅用于快速验证任务启动和基础功能
   - 通过 `TEST_MODE=quick` 环境变量启用
   - 适用于开发阶段的快速验证

#### 3.3 使用方式

```bash
# 默认模式（10分钟超时）- 推荐用于完整测试
node src/tasks/tests/TaskName.test.js

# 快速模式（30秒超时）- 仅用于快速验证
TEST_MODE=quick node src/tasks/tests/TaskName.test.js
```

#### 3.4 超时处理实现

```javascript
// 超时处理
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);
```

### 5. 基础测试场景（推荐覆盖）

1.  **实例化测试**：
    *   验证任务能否被正确实例化。
    *   验证构造函数是否正确设置了任务名称和初始状态。
    *   验证任务ID格式是否正确（包含任务名称、时间戳、随机字符串）。

2.  **参数校验测试**：
    *   必需字段缺失：测试当 `context` 缺少一个或多个必需字段时，任务是否按预期抛出错误并设置失败状态。
    *   无效参数值：测试当传入的参数值无效（例如，类型错误、格式错误、超出范围等），任务是否能正确处理并报错。
    *   验证错误处理时是否正确调用了`this.fail(error)`方法。

3.  **正常执行流程测试**：
    *   使用有效的、典型的输入 `context` 测试任务的完整执行流程。
    *   验证任务是否成功完成 (`status: 'completed'`)。
    *   验证任务结果是否按预期生成（`this.result`）。
    *   验证关键的进度回调是否被触发。
    *   如果任务生成文件，验证文件是否已创建，内容是否符合预期（可抽样检查）。

4.  **进度监控系统测试**：
    *   **基础进度报告**：验证`reportProgress()`方法是否正确更新任务状态和进度历史。
    *   **LLM进度报告**：验证`reportLLMProgress()`方法是否正确处理LLM特定的进度阶段。
    *   **生命周期方法**：验证`start()`, `complete()`, `fail()`方法是否正确管理任务状态和时间戳。
    *   **进度回调验证**：验证进度回调函数是否被正确调用，传递的数据结构是否符合预期。

5.  **详细上下文收集测试**：
    *   验证`collectDetailedContext()`方法是否返回完整的上下文对象。
    *   验证基础上下文结构（taskInfo, executionStats, progressHistory等）。
    *   验证任务特定的上下文信息是否正确添加。
    *   验证上下文收集的错误处理机制。

6.  **任务状态管理测试**：
    *   验证任务状态转换的正确性（PENDING → STARTED → RUNNING → COMPLETED/FAILED）。
    *   验证时间戳记录（startTime, endTime）。
    *   验证执行时长计算（getElapsedTime()）。

7.  **错误处理测试**：
    *   模拟任务执行过程中可能发生的内部错误。
    *   验证任务是否能捕获这些错误，将状态设置为 `failed`，并报告正确的错误信息。
    *   验证错误是否按预期向上层抛出。
    *   验证错误对象的结构（消息、类型、堆栈信息）。

8.  **边界条件测试**：
    *   测试空输入、极大/极小值、特殊字符等边界情况。

9.  **特定逻辑/功能点测试**：
    *   针对任务中的特定重要算法、数据转换或条件分支，设计专门的测试用例。
    *   如果任务涉及文件保存，验证`saveDataToFile()`的调用和结果。
    *   如果任务涉及JSON处理，验证`extractAndParseJson()`的调用和结果。

### 6. 测试脚本模板示例 (`[TaskName].test.js`)

```javascript
/**
 * @功能概述: [TaskName] 的独立测试脚本。
 *           可以直接通过 `node [TaskName].test.js` 执行。
 * @注意事项:
 *   - 根据实际任务调整 mockContext 和断言。
 *   - 对于涉及文件I/O的任务，确保测试数据路径正确，并在测试后进行清理（如果需要）。
 */

const fs = 'fs'; // 使用字符串避免工具报错
const path = 'path'; // 使用字符串避免工具报错
// 实际项目中应使用:
// const fs = require('fs');
// const path = require('path');

const MyTask = require('../MyTask'); // 替换为实际的任务类路径
const logger = require('../../utils/logger'); // 替换为实际的logger路径
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress'); // 替换为实际的progress常量路径
const config = require('../../config'); // 替换为实际的config路径

// 测试配置（标准配置）
const TEST_CONFIG = {
    testMode: process.env.TEST_MODE || 'normal',
    timeout: process.env.TEST_MODE === 'quick' ? 30000 : 600000 // 30秒 vs 10分钟
};

// 统一的测试日志前缀
const testLogPrefix = `[文件：MyTask.test.js][MyTask测试]`;

logger.info(`${testLogPrefix} 🧪 开始测试 MyTask`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}


async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 MyTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new MyTask();
        assert(task instanceof MyTask, '任务应为 MyTask 的实例');
        assertEquals(task.name, 'MyTask', '任务名称应为 MyTask'); // 假设默认构造函数如此设置
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
    });

    await runSingleTest('2. 缺少必需字段 - someRequiredField', async () => {
        const task = new MyTask();
        const context = { reqId: 'test-missing-field' }; // 缺少 someRequiredField
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出'); // 如果没有错误，则测试失败
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            const hasFailedProgress = progressLogs.some(p => p.status === TASK_STATUS.FAILED);
            assert(hasFailedProgress, '应记录 FAILED 状态的进度回调');
        }
    });

    await runSingleTest('3. 正常执行流程', async () => {
        const task = new MyTask();
        const context = {
            reqId: 'test-normal-flow',
            someRequiredField: 'validValue',
            // ... 其他任务所需的有效上下文参数
            // 例如，如果任务处理文件，可以准备一个临时的测试文件
            // inputFilePath: path.join(__dirname, 'test-data', 'sample.txt'),
        };
        const progressLogs = [];

        // (可选) 准备测试数据，例如创建临时文件
        // fs.writeFileSync(context.inputFilePath, 'Test content');

        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        assert(result, '任务执行应返回结果');
        // 根据任务具体输出断言 result 的内容
        // assertEquals(result.someOutputField, 'expectedOutputValue', '输出字段 someOutputField 值应正确');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        assert(task.result, '任务应保存执行结果');

        const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
        const hasCompletedProgress = progressLogs.some(p => p.status === TASK_STATUS.COMPLETED);
        assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');
        assert(hasCompletedProgress, '应记录 COMPLETED 状态的进度回调');

        // (可选) 清理测试数据
        // fs.unlinkSync(context.inputFilePath);
    });

    await runSingleTest('4. 进度回调功能', async () => {
        const task = new MyTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试基础进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'MyTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');
    });

    await runSingleTest('5. LLM进度报告功能', async () => {
        const task = new MyTask();
        const progressLogs = [];

        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试LLM专用进度报告
        task.reportLLMProgress('preparing', '准备LLM请求', {
            current: 30,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录LLM进度回调');
        assertEquals(progressLogs[0].taskName, 'MyTask', 'LLM进度回调应包含正确的任务名称');
        assert(progressLogs[0].technicalDetail, 'LLM进度回调应包含技术详情');
    });

    await runSingleTest('6. 任务状态管理', async () => {
        const task = new MyTask();

        // 测试初始状态
        assertEquals(task.status, TASK_STATUS.PENDING, '初始状态应为PENDING');

        // 测试开始状态
        task.start();
        assertEquals(task.status, TASK_STATUS.STARTED, '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');

        // 测试完成状态
        const testResult = { test: 'result' };
        task.complete(testResult);
        assertEquals(task.status, TASK_STATUS.COMPLETED, '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');

        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('7. 错误处理和失败状态', async () => {
        const task = new MyTask();
        const testError = new Error('测试错误');

        // 测试失败状态
        task.fail(testError);
        assertEquals(task.status, TASK_STATUS.FAILED, '失败后状态应为FAILED');
        assertEquals(task.error, testError, '应保存错误对象');
        assert(task.endTime, '失败时应记录结束时间');
    });

    await runSingleTest('8. collectDetailedContext 方法', async () => {
        const task = new MyTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
        assertEquals(context.collectionMethod, 'MyTask.collectDetailedContext',
                    '收集方法应正确标识');
    });

    // --- 更多测试用例 ---
    // await runSingleTest('4. 特定错误场景 - XXX', async () => { ... });
    // await runSingleTest('5. 边界条件 - YYY', async () => { ... });


    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== MyTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1); // 以错误码退出，方便CI/CD集成
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0); // 成功退出
    }
}

// 超时处理
setTimeout(() => {
    logger.error(`${testLogPrefix} ⏰ 测试超时 (${TEST_CONFIG.timeout / 1000}秒)，强制退出`);
    console.log(`❌ 测试超时: ${TEST_CONFIG.timeout / 1000}秒`);
    process.exit(1);
}, TEST_CONFIG.timeout);

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
```

### 5. 最佳实践总结
1. 标准化优先：严格使用 `reportProgress()` 和 `reportLLMProgress()` 方法
2. 错误隔离：使用 `this.fail(error)` 统一处理错误
3. 日志完整：提供分层日志前缀和文件标识符
4. 进度透明：为每个关键步骤提供进度报告
5. 测试全面：覆盖正常流程和异常情况
6. 文档完整：使用详细的JSDoc注释
7. 模块化设计：将复杂逻辑拆分为独立方法

这些标准确保了任务开发的一致性、可靠性和可维护性，为项目的持续发展提供了坚实的基础。

## 版本更新记录

### v2.0 (2025-06-06)
**基于TaskBase.js最新实现的重大更新**

#### 新增功能
1. **详细上下文收集系统**
   - 新增`collectDetailedContext()`方法标准
   - 标准化上下文结构定义
   - 子类扩展上下文的最佳实践

2. **增强的进度监控机制**
   - 完善`reportProgress()`和`reportLLMProgress()`方法说明
   - 新增自动状态管理和进度历史记录功能
   - 标准化生命周期方法（start, complete, fail）

3. **完善的测试标准**
   - 新增进度监控系统测试要求
   - 新增详细上下文收集测试要求
   - 新增任务状态管理测试要求
   - 更新测试模板，包含8个核心测试用例

#### 改进内容
1. **任务类模板优化**
   - 添加`collectDetailedContext()`方法实现模板
   - 完善错误处理机制
   - 标准化日志前缀体系

2. **文档结构优化**
   - 重新组织核心架构组件说明
   - 完善TaskBase核心能力描述
   - 更新数据流向图

#### 技术规范
- 基于TaskBase.js (提交时间: 2025/6/6 16:04:45)
- 兼容现有任务实现
- 向后兼容v1.0标准

### v1.0 (2024-06-01)
- 初始版本
- 基础任务开发标准
- 基本进度监控机制
- 单元测试要求






