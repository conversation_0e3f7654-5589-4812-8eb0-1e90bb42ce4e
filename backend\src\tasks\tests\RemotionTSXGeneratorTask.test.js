/**
 * RemotionTSXGeneratorTask 专属测试文件
 * 按照任务开发标准创建的自包含测试文件
 * 可通过 node backend/src/tasks/tests/RemotionTSXGeneratorTask.test.js 直接执行
 */

const RemotionTSXGeneratorTask = require('../RemotionTSXGeneratorTask');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 加载真实的视频配置文件
const videoConfigPath = path.resolve(__dirname, '../../config/video/video-config.json');
const realVideoConfig = JSON.parse(fs.readFileSync(videoConfigPath, 'utf8'));

// 真实的媒体文件路径
const realVideoPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612.mp4';
const realAudioPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3';

// 获取音频时长的函数
function getAudioDuration(audioPath) {
    return new Promise((resolve, reject) => {
        const ffprobe = spawn('ffprobe', [
            '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            audioPath
        ]);
        
        let output = '';
        ffprobe.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        ffprobe.on('close', (code) => {
            if (code === 0) {
                const duration = parseFloat(output.trim());
                resolve(duration);
            } else {
                // 如果ffprobe失败，使用默认值
                logger.warn(`${testLogPrefix} 无法获取音频时长，使用默认值 30 秒`);
                resolve(30);
            }
        });
        
        ffprobe.on('error', (error) => {
            logger.warn(`${testLogPrefix} ffprobe执行失败: ${error.message}，使用默认值 30 秒`);
            resolve(30);
        });
    });
}

// 测试日志前缀
const testLogPrefix = '[文件：RemotionTSXGeneratorTask.test.js][RemotionTSX生成任务测试]';

// 断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 RemotionTSXGeneratorTask 测试 ==========`);
    
    // 获取真实的音频时长
    logger.info(`${testLogPrefix} 正在获取音频文件时长: ${realAudioPath}`);
    const realAudioDuration = await getAudioDuration(realAudioPath);
    logger.info(`${testLogPrefix} 音频时长: ${realAudioDuration} 秒`);
    
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new RemotionTSXGeneratorTask();
        assert(task instanceof RemotionTSXGeneratorTask, '任务应为 RemotionTSXGeneratorTask 的实例');
        assertEquals(task.name, 'RemotionTSXGeneratorTask', '任务名称应为 RemotionTSXGeneratorTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
    });

    await runSingleTest('2. 缺少必需字段 - videoConfig', async () => {
        const task = new RemotionTSXGeneratorTask();
        const context = { 
            reqId: 'test-missing-videoconfig',
            // 缺少 videoConfig，但提供其他必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            const hasFailedProgress = progressLogs.some(p => p.status === TASK_STATUS.FAILED);
            assert(hasFailedProgress, '应记录 FAILED 状态的进度回调');
        }
    });

    await runSingleTest('3. 微模板Schema验证 - 新标准格式', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        // 使用真实的视频配置文件，并添加remotionTemplate配置
        const context = {
            reqId: 'test-schema-validation',
            videoConfig: realVideoConfig,
            remotionTemplate: {
                layers: [
                    {
                        type: 'background',
                        template: 'newspaper'
                    },
                    {
                        type: 'video',
                        template: 'default-muted'
                    }
                ]
            },
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assert(result.compositionId, '结果应包含组合ID');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
        const hasCompletedProgress = progressLogs.some(p => p.status === TASK_STATUS.COMPLETED);
        assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');
        assert(hasCompletedProgress, '应记录 COMPLETED 状态的进度回调');
    });

    await runSingleTest('4. 向后兼容性测试 - 新旧格式混合', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        const context = {
            reqId: 'test-backward-compatibility',
            videoConfig: realVideoConfig,
            // 不提供remotionTemplate，测试向后兼容性
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assert(result.compositionId, '结果应包含组合ID');
        assert(result.totalAudioDuration, '结果应包含总音频时长');
        assert(result.copiedAudioPath, '结果应包含复制的音频路径');
        assert(result.copiedVideoPath, '结果应包含复制的视频路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
    });

    await runSingleTest('5. 错误处理 - 不存在的微模板文件', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        const context = {
            reqId: 'test-missing-template',
            videoConfig: realVideoConfig,
            remotionTemplate: {
                layers: [
                    {
                        type: 'background',
                        template: 'nonexistent-template'
                    }
                ]
            },
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            // 检查错误消息是否包含文件不存在的相关信息
            const errorMessage = error.message.toLowerCase();
            const hasFileNotFoundError = errorMessage.includes('enoent') || 
                                       errorMessage.includes('no such file') || 
                                       errorMessage.includes('cannot find') ||
                                       errorMessage.includes('not found');
            assert(hasFileNotFoundError, `错误消息应指明文件不存在，实际错误: ${error.message}`);
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('6. VideoGuide功能测试', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        // 直接使用真实配置文件中的videoGuide配置
        const context = {
            reqId: 'test-videoguide',
            videoConfig: realVideoConfig, // 使用真实配置，不覆盖videoGuide
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        // 检查生成的TSX文件是否包含videoGuide内容（使用真实配置中的值）
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');
        assertIncludes(tsxContent, '坚持30天', 'TSX文件应包含title1文本');
        assertIncludes(tsxContent, '听懂国外新闻', 'TSX文件应包含title2文本');
        assertIncludes(tsxContent, '"fontSize":"80px"', 'TSX文件应包含innerStyle中的fontSize样式');
        assertIncludes(tsxContent, '"color":"rgb(255, 255, 255)"', 'TSX文件应包含转换后的color样式');
        assertIncludes(tsxContent, '"fontWeight":"bold"', 'TSX文件应包含转换后的fontWeight样式');
        assertIncludes(tsxContent, '"textShadow":', 'TSX文件应包含转换后的textShadow样式');
        assertIncludes(tsxContent, '"top":"300px"', 'TSX文件应包含title1的top位置');
        assertIncludes(tsxContent, '"top":"440px"', 'TSX文件应包含title2的top位置');
        assertIncludes(tsxContent, '"padding":"5px 20px 20px"', 'TSX文件应包含title2的padding样式');
        
        logger.info(`${testLogPrefix} VideoGuide功能测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
    });

    await runSingleTest('7. RepeatModeGuide功能测试', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        // 创建包含repeatModes配置的测试上下文
        const testVideoConfig = JSON.parse(JSON.stringify(realVideoConfig)); // 深拷贝
        testVideoConfig.repeatModes = [
            {
                name: "blindListen",
                displayText: "第一遍 盲听",
                repeatCount: 1
            },
            {
                name: "clozedSubtitle",
                displayText: "第二遍 单词填空",
                repeatCount: 1
            }
        ];
        
        const context = {
            reqId: 'test-repeatmodeguide',
            videoConfig: testVideoConfig,
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        // 检查生成的TSX文件是否包含repeatModeGuide内容
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');
        assertIncludes(tsxContent, '第一遍 盲听', 'TSX文件应包含第一个引导文字');
        assertIncludes(tsxContent, '第二遍 单词填空', 'TSX文件应包含第二个引导文字');
        // 由于只配置了两个repeatModes，不检查第三个引导文字
        // repeatModeGuide在TSX中实际渲染为TextArea组件，所以不需要检查类型标识
        assertIncludes(tsxContent, 'interpolate', 'TSX文件应包含动画interpolate函数');
        assertIncludes(tsxContent, 'useCurrentFrame', 'TSX文件应包含useCurrentFrame钩子');
        assertIncludes(tsxContent, '"transform":', 'TSX文件应包含transform样式用于动画');
        assertIncludes(tsxContent, '"opacity":', 'TSX文件应包含opacity样式用于淡入淡出');
        assertIncludes(tsxContent, 'drop-shadow', 'TSX文件应包含drop-shadow滤镜效果');
        
        logger.info(`${testLogPrefix} RepeatModeGuide功能测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
    });

    await runSingleTest('8. Advertisement玻璃效果测试', async () => {
        const task = new RemotionTSXGeneratorTask();

        // 直接使用真实配置文件，不做任何修改
        const context = {
            reqId: 'test-advertisement-glass',
            videoConfig: realVideoConfig, // 直接使用真实配置，测试真实的兼容性
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };

        const progressLogs = [];

        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        // 检查生成的TSX文件是否包含advertisement内容
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');

        // 检查是否包含真实配置文件中的广告文本
        const realAdvertisementTitles = realVideoConfig.subtitleConfig.advertisement.titles;
        const hasFirstLineText = realAdvertisementTitles.some(title =>
            tsxContent.includes(title.line1)
        );
        const hasSecondLineText = realAdvertisementTitles.some(title =>
            tsxContent.includes(title.line2)
        );
        assert(hasFirstLineText, 'TSX文件应包含真实配置文件中的第一行广告文本内容');
        assert(hasSecondLineText, 'TSX文件应包含真实配置文件中的第二行广告文本内容');

        // 检查标准玻璃效果动画变量
        assertIncludes(tsxContent, 'advertisementOpacity1', 'TSX文件应包含广告透明度动画变量');
        assertIncludes(tsxContent, 'advertisementScale1', 'TSX文件应包含广告缩放动画变量');
        assertIncludes(tsxContent, 'advertisementRotation1', 'TSX文件应包含玻璃旋转动画变量');
        assertIncludes(tsxContent, 'advertisementTranslateY1', 'TSX文件应包含垂直位移动画变量');
        assertIncludes(tsxContent, 'advertisementBreathing1', 'TSX文件应包含玻璃呼吸动画变量');
        assertIncludes(tsxContent, 'advertisementGlow1', 'TSX文件应包含玻璃反光闪烁动画变量');
        assertIncludes(tsxContent, 'interpolate', 'TSX文件应包含动画interpolate函数');
        assertIncludes(tsxContent, 'useCurrentFrame', 'TSX文件应包含useCurrentFrame钩子');

        // 检查时间配置（6秒开始）
        assertIncludes(tsxContent, 'from={180}', 'TSX文件应包含正确的开始帧数（6秒*30fps=180帧）');

        // 检查标准玻璃效果样式
        assertIncludes(tsxContent, 'left: \'840px\'', 'TSX文件应包含标准水平位置840px');
        assertIncludes(tsxContent, 'top: \'670px\'', 'TSX文件应包含标准垂直位置670px');
        assertIncludes(tsxContent, 'width: \'38%\'', 'TSX文件应包含标准宽度38%');
        assertIncludes(tsxContent, 'linear-gradient(135deg, rgba(30, 144, 255, 0.9), rgba(0, 100, 200, 0.8))', 'TSX文件应包含深蓝渐变背景');
        assertIncludes(tsxContent, 'borderRadius: \'30px\'', 'TSX文件应包含圆角边框');
        assertIncludes(tsxContent, 'padding: \'10px 20px\'', 'TSX文件应包含标准内边距');

        // 检查多层阴影和发光效果
        assertIncludes(tsxContent, '0 8px 32px rgba(30, 144, 255, 0.4)', 'TSX文件应包含蓝色外阴影');
        assertIncludes(tsxContent, '0 4px 16px rgba(0, 0, 0, 0.3)', 'TSX文件应包含黑色阴影');
        assertIncludes(tsxContent, 'inset 0 1px 0 rgba(255, 255, 255', 'TSX文件应包含内发光效果');
        assertIncludes(tsxContent, '0 0 20px rgba(255, 255, 255', 'TSX文件应包含外发光效果');

        // 检查标准文字样式（白色文字+深色阴影）
        assertIncludes(tsxContent, 'fontSize: \'32px\'', 'TSX文件应包含标准字体大小32px');
        assertIncludes(tsxContent, 'color: \'#FFFFFF\'', 'TSX文件应包含白色文字');
        assertIncludes(tsxContent, 'fontFamily: \'"sans-serif"\'', 'TSX文件应包含sans-serif字体');
        assertIncludes(tsxContent, 'textShadow: \'2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 4px rgba(0, 0, 0, 0.6)\'', 'TSX文件应包含深色文字阴影');
        assertIncludes(tsxContent, 'letterSpacing: \'1px\'', 'TSX文件应包含字母间距');

        // 检查复合动画变换
        assertIncludes(tsxContent, 'translateY(${advertisementTranslateY1}px)', 'TSX文件应包含垂直位移变换');
        assertIncludes(tsxContent, 'scale(${advertisementScale1 * advertisementBreathing1})', 'TSX文件应包含复合缩放变换');
        assertIncludes(tsxContent, 'rotate(${advertisementRotation1}deg)', 'TSX文件应包含旋转变换');
        assertIncludes(tsxContent, '${0.2 + advertisementGlow1}', 'TSX文件应包含动态发光强度');

        logger.info(`${testLogPrefix} Advertisement玻璃效果测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
    });

    await runSingleTest('9. ClozedText挖空字幕测试', async () => {
        const task = new RemotionTSXGeneratorTask();

        // 硬编码模拟clozedSubtitleJsonArray，格式严格按照test_neetu_garcha_june_8_2025_clozed_subtitle.json
        const mockClozedSubtitleJsonArray = [
            {
                "end": 4.32,
                "id": "1",
                "start": 0.16,
                "text": "On this Sunday night, Trump's () on () in Los Angeles.",
                "words": [
                    "crackdown",
                    "protests"
                ]
            },
            {
                "end": 6.88,
                "id": "2",
                "start": 5.12,
                "text": "Good evening, and thank you for () us.",
                "words": [
                    "joining"
                ]
            },
            {
                "end": 13.04,
                "id": "3",
                "start": 6.96,
                "text": "U.S. President Donald Trump is once again () from protocol, this time by () the National",
                "words": [
                    "breaking",
                    "deploying"
                ]
            }
        ];

        // 创建包含clozedSubtitleJsonArray的测试上下文
        const testVideoConfig = JSON.parse(JSON.stringify(realVideoConfig)); // 深拷贝
        // 确保有repeatModes配置，包含clozedSubtitle
        testVideoConfig.subtitleConfig = testVideoConfig.subtitleConfig || {};
        testVideoConfig.subtitleConfig.repeatModes = [
            {
                name: "blindListen",
                displayText: "第一遍 盲听",
                repeatCount: 1
            },
            {
                name: "clozedSubtitle",
                displayText: "第二遍 单词填空",
                repeatCount: 1
            },
            {
                name: "bilingualSubtitle",
                displayText: "第三遍 双语字幕",
                repeatCount: 1
            }
        ];

        const context = {
            reqId: 'test-clozedtext',
            videoConfig: testVideoConfig,
            clozedSubtitleJsonArray: mockClozedSubtitleJsonArray, // 添加挖空字幕数据
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };

        const progressLogs = [];

        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });

        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');

        // 检查生成的TSX文件是否包含ClozedText内容
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');

        // 检查是否包含挖空字幕文本内容
        assertIncludes(tsxContent, "On this Sunday night, Trump's () on () in Los Angeles.", 'TSX文件应包含第一条挖空字幕文本');
        assertIncludes(tsxContent, "Good evening, and thank you for () us.", 'TSX文件应包含第二条挖空字幕文本');
        assertIncludes(tsxContent, "U.S. President Donald Trump is once again () from protocol, this time by () the National", 'TSX文件应包含第三条挖空字幕文本');

        // 检查ClozedText样式（现代化设计）
        assertIncludes(tsxContent, 'fontSize: \'48px\'', 'TSX文件应包含48px字体大小');
        assertIncludes(tsxContent, 'fontFamily: \'sans-serif\'', 'TSX文件应包含sans-serif字体');
        assertIncludes(tsxContent, 'fontWeight: \'bold\'', 'TSX文件应包含粗体字重');
        assertIncludes(tsxContent, 'color: \'#FFFFFF\'', 'TSX文件应包含白色文字');
        assertIncludes(tsxContent, 'textShadow: \'2px 2px 4px rgba(0, 0, 0, 0.8)\'', 'TSX文件应包含深色文字阴影');
        assertIncludes(tsxContent, 'letterSpacing: \'1.5px\'', 'TSX文件应包含1.5px字母间距');
        assertIncludes(tsxContent, 'textAlign: \'center\'', 'TSX文件应包含居中对齐');

        // 检查ClozedText位置布局（新标准样式）
        assertIncludes(tsxContent, 'top: \'960px\'', 'TSX文件应包含灰色区域中心位置');
        assertIncludes(tsxContent, 'left: \'50%\'', 'TSX文件应包含水平居中定位');
        assertIncludes(tsxContent, 'transform: \'translate(-50%, -50%)\'', 'TSX文件应包含双向居中变换');
        assertIncludes(tsxContent, 'width: \'calc(100% - 40px)\'', 'TSX文件应包含响应式宽度');
        assertIncludes(tsxContent, 'maxWidth: \'800px\'', 'TSX文件应包含最大宽度限制');

        // 检查新增的样式属性
        assertIncludes(tsxContent, 'display: \'flex\'', 'TSX文件应包含flex布局');
        assertIncludes(tsxContent, 'alignItems: \'center\'', 'TSX文件应包含垂直居中对齐');
        assertIncludes(tsxContent, 'justifyContent: \'center\'', 'TSX文件应包含水平居中对齐');
        assertIncludes(tsxContent, 'padding: \'20px\'', 'TSX文件应包含20px内边距');
        assertIncludes(tsxContent, 'lineHeight: \'1.3\'', 'TSX文件应包含1.3行高');
        assertIncludes(tsxContent, 'wordWrap: \'break-word\'', 'TSX文件应包含自动换行');
        assertIncludes(tsxContent, 'overflowWrap: \'break-word\'', 'TSX文件应包含溢出换行');

        // 检查时间偏移计算（clozedSubtitle在第二个位置，应该有时间偏移）
        // 由于clozedSubtitle在repeatModes的第1个位置（索引1），应该有时间偏移
        // 第一条字幕原始开始时间0.16秒，加上时间偏移后应该大于原始时间
        const sequenceMatches = tsxContent.match(/from=\{(\d+)\}/g);
        if (sequenceMatches && sequenceMatches.length > 0) {
            // 检查是否有大于原始时间的帧数（说明有时间偏移）
            const frameNumbers = sequenceMatches.map(match => parseInt(match.match(/\d+/)[0]));
            const hasTimeOffset = frameNumbers.some(frame => frame > 100); // 大于100帧说明有偏移
            assert(hasTimeOffset, 'ClozedText序列应该有时间偏移（因为在repeatModes第二个位置）');
        }

        logger.info(`${testLogPrefix} ClozedText挖空字幕测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
        logger.info(`${testLogPrefix} 成功渲染${mockClozedSubtitleJsonArray.length}条挖空字幕`);
    });

    await runSingleTest('10. BilingualText双语字幕测试', async () => {
        const task = new RemotionTSXGeneratorTask();

        // 硬编码模拟enhancedBilingualSubtitleJsonArray，格式参考test_enhanced_bilingual_subtitle.json
        const mockEnhancedBilingualSubtitleJsonArray = [
            {
                "id": "1",
                "start": 0,
                "end": 2.9,
                "text_english": "In Montreal, there are still many questions over the deadly",
                "text_chinese": "在蒙特利尔，关于那场致命火灾仍有许多疑问，",
                "words_explanation": {
                    "questions": "疑问",
                    "deadly": "致命的"
                }
            },
            {
                "id": "2",
                "start": 2.9,
                "end": 6.36,
                "text_english": "fire that tore through a century-old building Friday morning.",
                "text_chinese": "大火于周五早上吞噬了一座百年老建筑。",
                "words_explanation": {
                    "fire": "火灾",
                    "tore": "吞噬",
                    "century-old": "百年",
                    "building": "建筑"
                }
            }
        ];

        const context = {
            reqId: 'test-bilingual-text',
            videoConfig: realVideoConfig,
            enhancedBilingualSubtitleJsonArray: mockEnhancedBilingualSubtitleJsonArray,
            originalVideoPath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612.mp4',
            audioFilePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3',
            audioDuration: 22.212
        };

        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[BilingualText测试进度]: ${data.detail}`);
        });

        // 基础验证
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');

        // 读取生成的TSX文件内容
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');

        // 验证双语字幕内容（包含关键词翻译）
        assertIncludes(tsxContent, 'In Montreal, there are still many', 'TSX文件应包含第一条英文字幕基础文本');
        assertIncludes(tsxContent, '在蒙特利尔，关于那场致命火灾仍有许多疑问，', 'TSX文件应包含第一条中文字幕文本');
        assertIncludes(tsxContent, 'Friday morning.', 'TSX文件应包含第二条英文字幕基础文本');
        assertIncludes(tsxContent, '大火于周五早上吞噬了一座百年老建筑。', 'TSX文件应包含第二条中文字幕文本');

        // 验证英文字幕样式
        assertIncludes(tsxContent, 'fontSize: \'36px\'', 'TSX文件应包含36px英文字体大小');
        assertIncludes(tsxContent, 'top: \'800px\'', 'TSX文件应包含英文字幕位置');
        assertIncludes(tsxContent, 'color: \'#FFFFFF\'', 'TSX文件应包含白色英文字体');

        // 验证中文字幕样式
        assertIncludes(tsxContent, 'fontSize: \'32px\'', 'TSX文件应包含32px中文字体大小');
        assertIncludes(tsxContent, 'top: \'900px\'', 'TSX文件应包含中文字幕位置');
        assertIncludes(tsxContent, 'color: \'#FFFF99\'', 'TSX文件应包含淡黄色中文字体');

        // 验证布局样式
        assertIncludes(tsxContent, 'transform: \'translate(-50%, -50%)\'', 'TSX文件应包含双向居中变换');
        assertIncludes(tsxContent, 'maxWidth: \'800px\'', 'TSX文件应包含最大宽度限制');
        assertIncludes(tsxContent, 'display: \'flex\'', 'TSX文件应包含flex布局');
        assertIncludes(tsxContent, 'alignItems: \'center\'', 'TSX文件应包含垂直居中对齐');
        assertIncludes(tsxContent, 'justifyContent: \'center\'', 'TSX文件应包含水平居中对齐');

        // 验证动效
        assertIncludes(tsxContent, 'interpolate', 'TSX文件应包含动画interpolate函数');
        assertIncludes(tsxContent, 'opacity:', 'TSX文件应包含透明度动画');

        // 验证关键词翻译功能
        assertIncludes(tsxContent, 'dangerouslySetInnerHTML', 'TSX文件应包含HTML内容渲染');
        assertIncludes(tsxContent, 'color: #00FFFF', 'TSX文件应包含青色高亮关键词');
        assertIncludes(tsxContent, 'color: #FFFF99', 'TSX文件应包含淡黄色翻译注释');
        assertIncludes(tsxContent, '[疑问]', 'TSX文件应包含关键词翻译注释');
        assertIncludes(tsxContent, '[致命的]', 'TSX文件应包含关键词翻译注释');

        // 验证时间偏移（bilingualSubtitle在repeatModes第三个位置）
        const sequenceMatches = tsxContent.match(/from=\{\d+\}/g);
        if (sequenceMatches && sequenceMatches.length > 0) {
            const frameNumbers = sequenceMatches.map(match => parseInt(match.match(/\d+/)[0]));
            const hasTimeOffset = frameNumbers.some(frame => frame > 1300); // 大于1300帧说明有偏移（第三个位置）
            assert(hasTimeOffset, 'BilingualText序列应该有时间偏移（因为在repeatModes第三个位置）');
        }

        logger.info(`${testLogPrefix} BilingualText双语字幕测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
        logger.info(`${testLogPrefix} 成功渲染${mockEnhancedBilingualSubtitleJsonArray.length * 2}个双语字幕序列（英文+中文）`);
    });

    // --- 测试结果汇总 ---
    logger.info(`${testLogPrefix} ========== 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed} 个测试`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed} 个测试`);
    
    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} 存在失败的测试用例，请检查上述错误信息`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试用例均通过！`);
        process.exit(0);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTests().catch(error => {
        logger.error(`${testLogPrefix} 测试执行过程中发生未捕获错误: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        process.exit(1);
    });
}

module.exports = { runTests };
