# getVideoConfig API 接口文档

## 📋 接口概述

**接口名称**: 获取视频配置参数  
**接口路径**: `GET /api/video/getVideoConfig`  
**功能描述**: 为前端提供动态视频配置，解决前后端配置不一致问题  
**创建时间**: 2025-08-10  
**版本**: v1.0  

## 🎯 业务背景

### 问题分析
- 前端app.js中的defaultConfig使用硬编码值
- 前后端配置不一致导致参数设置不生效
- 缺乏统一的配置管理机制

### 解决方案
- 创建动态配置获取API
- 前端启动时自动获取后端配置
- 建立前后端配置的统一管理机制

## 🔧 技术规格

### 请求规格
```http
GET /api/video/getVideoConfig
Host: localhost:3000
Content-Type: application/json
```

**请求参数**: 无需参数

### 响应规格

#### 成功响应 (200 OK)
```json
{
  "success": true,
  "data": {
    "repeatCount": 3,
    "backgroundStyle": "newspaper",
    "repeatModes": [
      {
        "name": "blindListen",
        "displayText": "第一遍 盲听"
      },
      {
        "name": "clozedSubtitle", 
        "displayText": "第二遍 单词填空"
      },
      {
        "name": "bilingualSubtitle",
        "displayText": "第三遍 中英翻译"
      }
    ],
    "subtitleConfig": {
      "videoGuide": {
        "enabled": true,
        "title1": "三遍通关",
        "title2": "听懂国外新闻"
      },
      "advertisement": {
        "enabled": true,
        "titles": [
          {
            "line1": "🎯关注水蜜桃英语",
            "line2": "每天2分钟，听力打卡！"
          }
        ]
      }
    }
  },
  "metadata": {
    "source": "backend/src/config/video/video-config.json",
    "extractedAt": "2025-08-10T03:11:16.581Z",
    "validation": {
      "hasRepeatCount": true,
      "hasBackgroundStyle": true,
      "hasRepeatModes": true,
      "hasVideoGuide": true,
      "hasAdvertisement": true,
      "repeatCountMatchesModesLength": true
    },
    "reqId": "c17878c0-8e81-407..."
  }
}
```

#### 错误响应 (500 Internal Server Error)
```json
{
  "success": false,
  "error": "配置文件读取失败",
  "message": "ENOENT: no such file or directory...",
  "reqId": "request-id-here"
}
```

## 📊 数据字段说明

### data 对象
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `repeatCount` | number | ✅ | 重复播放次数，默认3 |
| `backgroundStyle` | string | ✅ | 背景风格，默认"newspaper" |
| `repeatModes` | array | ✅ | 重复模式配置数组 |
| `subtitleConfig` | object | ✅ | 字幕配置对象 |

### repeatModes 数组元素
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `name` | string | ✅ | 模式名称标识符 |
| `displayText` | string | ✅ | 前端显示文本 |

### subtitleConfig.videoGuide 对象
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `enabled` | boolean | ✅ | 是否启用视频引导 |
| `title1` | string | ✅ | 引导标题第一行 |
| `title2` | string | ✅ | 引导标题第二行 |

### subtitleConfig.advertisement 对象
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `enabled` | boolean | ✅ | 是否启用广告 |
| `titles` | array | ✅ | 广告标题数组 |

### advertisement.titles 数组元素
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `line1` | string | ✅ | 广告第一行文本 |
| `line2` | string | ✅ | 广告第二行文本 |

### metadata 对象
| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `source` | string | ✅ | 配置文件来源路径 |
| `extractedAt` | string | ✅ | 配置提取时间(ISO格式) |
| `validation` | object | ✅ | 配置验证结果 |
| `reqId` | string | ✅ | 请求追踪ID |

## 🔄 配置转换逻辑

### 1. 数据来源
- 读取 `backend/src/config/video/video-config.json` 文件
- 提取前端需要的配置项
- 转换为前端兼容格式

### 2. 关键转换
- **repeatModes位置调整**: 从 `subtitleConfig.repeatModes` 提取到根级别
- **默认值填充**: 为缺失配置提供合理默认值
- **格式标准化**: 确保数组和对象结构正确

### 3. 验证机制
- 检查必需字段完整性
- 验证数据类型正确性
- 确保 `repeatCount` 与 `repeatModes.length` 一致

## 🚀 使用示例

### JavaScript/前端调用
```javascript
// 获取视频配置
async function loadVideoConfig() {
    try {
        const response = await fetch('/api/video/getVideoConfig');
        const result = await response.json();
        
        if (result.success) {
            // 使用配置更新前端
            videoConfig.value = result.data;
            console.log('配置加载成功:', result.data);
        } else {
            console.error('配置加载失败:', result.error);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 页面加载时调用
window.addEventListener('load', loadVideoConfig);
```

### cURL 命令
```bash
curl -X GET http://localhost:3000/api/video/getVideoConfig
```

### PowerShell 命令
```powershell
Invoke-RestMethod -Uri 'http://localhost:3000/api/video/getVideoConfig' -Method Get
```

## 🔍 调试信息

### 日志追踪
接口执行过程中会产生详细的日志，包括：
- 配置文件读取状态
- 数据提取和转换过程
- 验证结果和错误信息
- 请求处理时间统计

### 日志前缀格式
```
[文件：getVideoConfigController.js][获取视频配置][getVideoConfig][ReqID:xxx]
```

## ⚠️ 注意事项

### 1. 配置文件依赖
- 确保 `backend/src/config/video/video-config.json` 文件存在
- 配置文件格式必须为有效JSON
- 建议定期备份配置文件

### 2. 性能考虑
- 配置文件每次请求都会重新读取
- 适合配置变更频率不高的场景
- 如需高频访问，建议添加缓存机制

### 3. 错误处理
- 配置文件不存在时返回500错误
- JSON解析失败时返回详细错误信息
- 建议前端实现降级策略

## 🔗 相关文件

### 控制器文件
- `backend/src/controllers/video/getVideoConfigController.js` - 主控制器
- `backend/src/routes/videoRoutes.js` - 路由配置

### 配置文件
- `backend/src/config/video/video-config.json` - 视频配置源文件

### 前端集成
- `frontend-test/js/app.js` - 前端配置使用

## 📝 更新日志

### v1.0 (2025-08-10)
- ✅ 创建基础API接口
- ✅ 实现配置文件读取和转换
- ✅ 添加完整的验证机制
- ✅ 提供详细的错误处理
- ✅ 完成接口文档编写

---

**维护者**: Augment Agent  
**最后更新**: 2025-08-10  
**接口状态**: ✅ 已测试通过
