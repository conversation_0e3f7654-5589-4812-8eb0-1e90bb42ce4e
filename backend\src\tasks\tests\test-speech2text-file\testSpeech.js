// 导入必需的模块
require('dotenv').config(); // 加载环境变量
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// 从环境变量获取配置
const speechKey = process.env.SPEECH_KEY;
const speechEndpoint = process.env.SPEECH_ENDPOINT;
const apiVersion = process.env.API_VERSION || '2024-11-15';
const defaultLocale = process.env.DEFAULT_LOCALE || 'en-US';
const profanityFilter = process.env.PROFANITY_FILTER || 'Masked';
const requestTimeout = parseInt(process.env.REQUEST_TIMEOUT) || 300000;

// 硬编码的音频文件路径
const audioFilePath = "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\videoFile-1749985366429-521903550_d16a3c6c_audio.mp3";

/**
 * @功能概述: 使用Azure Fast Transcription API进行语音识别
 * @param {string} audioPath - 音频文件路径
 * @returns {Promise<object>} 转录结果
 */
async function transcribeAudio(audioPath) {
    console.log('🎤 开始语音识别测试...');
    console.log(`📁 音频文件路径: ${audioPath}`);

    try {
        // 1. 验证配置
        if (!speechKey || !speechEndpoint) {
            throw new Error('❌ 缺少必需的环境变量: SPEECH_KEY 或 SPEECH_ENDPOINT');
        }

        console.log(`🔑 Speech Key: ${speechKey.substring(0, 10)}...`);
        console.log(`🌐 Speech Endpoint: ${speechEndpoint}`);

        // 2. 检查音频文件是否存在
        if (!fs.existsSync(audioPath)) {
            throw new Error(`❌ 音频文件不存在: ${audioPath}`);
        }

        const fileStats = fs.statSync(audioPath);
        console.log(`📊 文件大小: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

        // 3. 构建API URL
        const apiUrl = `${speechEndpoint}/speechtotext/transcriptions:transcribe?api-version=${apiVersion}`;
        console.log(`🔗 API URL: ${apiUrl}`);

        // 4. 创建FormData
        const formData = new FormData();
        const audioStream = fs.createReadStream(audioPath);
        formData.append('audio', audioStream);

        // 5. 构建definition配置
        const definition = {
            locales: [defaultLocale],
            profanityFilterMode: profanityFilter,
            channels: [0, 1] // 处理立体声
        };

        formData.append('definition', JSON.stringify(definition));

        console.log(`⚙️  转录配置:`, definition);

        // 6. 设置请求头
        const headers = {
            'Accept': 'application/json',
            'Ocp-Apim-Subscription-Key': speechKey,
            ...formData.getHeaders()
        };

        console.log('📤 发送转录请求...');

        // 7. 发送请求
        const response = await axios.post(apiUrl, formData, {
            headers: headers,
            timeout: requestTimeout
        });

        console.log(`✅ 请求成功! 状态码: ${response.status}`);

        // 8. 处理响应
        const result = response.data;

        console.log('\n🎯 转录结果:');
        console.log('=' .repeat(80));

        // 显示基本信息
        console.log(`⏱️  音频时长: ${(result.durationMilliseconds / 1000).toFixed(2)} 秒`);

        // 显示完整转录文本
        if (result.combinedPhrases && result.combinedPhrases.length > 0) {
            console.log('\n📝 完整转录文本:');
            result.combinedPhrases.forEach((phrase, index) => {
                console.log(`\n[频道 ${phrase.channel || 0}]:`);
                console.log(phrase.text);
            });
        }

        // 显示详细片段信息
        if (result.phrases && result.phrases.length > 0) {
            console.log('\n📋 详细片段信息:');
            console.log('-'.repeat(80));

            result.phrases.slice(0, 5).forEach((phrase, index) => {
                const startTime = (phrase.offsetMilliseconds / 1000).toFixed(2);
                const endTime = ((phrase.offsetMilliseconds + phrase.durationMilliseconds) / 1000).toFixed(2);
                const confidence = (phrase.confidence * 100).toFixed(1);

                console.log(`\n[${index + 1}] ${startTime}s - ${endTime}s (置信度: ${confidence}%)`);
                console.log(`    语言: ${phrase.locale}`);
                if (phrase.speaker !== undefined) {
                    console.log(`    说话人: ${phrase.speaker}`);
                }
                if (phrase.channel !== undefined) {
                    console.log(`    频道: ${phrase.channel}`);
                }
                console.log(`    文本: "${phrase.text}"`);
            });

            if (result.phrases.length > 5) {
                console.log(`\n... 还有 ${result.phrases.length - 5} 个片段`);
            }
        }

        console.log('\n' + '='.repeat(80));
        console.log(`🎉 转录完成! 共识别出 ${result.phrases?.length || 0} 个语音片段`);

        return result;

    } catch (error) {
        console.error('\n❌ 转录失败:');

        if (error.response) {
            // API返回错误
            console.error(`   状态码: ${error.response.status}`);
            console.error(`   错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
        } else if (error.request) {
            // 网络错误
            console.error('   网络错误: 无法连接到Azure服务');
            console.error(`   请求详情: ${error.message}`);
        } else {
            // 其他错误
            console.error(`   错误: ${error.message}`);
        }

        throw error;
    }
}

/**
 * @功能概述: 主函数 - 执行语音识别测试
 */
async function main() {
    console.log('🚀 Azure Fast Transcription API 测试开始');
    console.log('=' .repeat(80));

    try {
        const result = await transcribeAudio(audioFilePath);

        // 可选：保存结果到文件
        const outputPath = path.join(__dirname, 'transcription_result.json');
        fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');
        console.log(`💾 转录结果已保存到: ${outputPath}`);

    } catch (error) {
        console.error('\n💥 测试失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main();
}