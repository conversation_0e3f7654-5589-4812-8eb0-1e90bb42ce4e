# measureSpring()

## 概述

`measureSpring()` 函数基于 [`spring()`](./spring.md) 配置和帧率，返回弹簧动画稳定所需的时间长度（以帧为单位）。

**版本要求**: v2.0.8+

## 语法

```typescript
import { measureSpring, SpringConfig } from "remotion";

const duration = measureSpring({
  fps: 30,
  config: { damping: 200 }
});
```

## 参数

### fps (必需)
- **类型**: `number`
- **描述**: 弹簧动画基于的帧率

### threshold (可选)
- **类型**: `number`
- **默认值**: `0.005`
- **描述**: 定义动画何时被认为完成的阈值

### config (可选)
- **类型**: `Partial<SpringConfig>`
- **描述**: 传递给 [`spring()`](./spring.md) 的弹簧配置

### from (可选)
- **类型**: `number`
- **默认值**: `0`
- **描述**: 动画的初始值

### to (可选)
- **类型**: `number`
- **默认值**: `1`
- **描述**: 动画的结束值

## 返回值

- **类型**: `number`
- **描述**: 弹簧动画稳定所需的帧数

## 基础用法

### 1. 基础测量

```typescript
import { measureSpring } from "remotion";

const duration = measureSpring({
  fps: 30,
  config: {
    damping: 200
  }
}); // => 23 帧
```

### 2. 自定义阈值

```typescript
import { measureSpring } from "remotion";

// 更严格的阈值（动画时间更长）
const preciseDuration = measureSpring({
  fps: 30,
  config: { damping: 200 },
  threshold: 0.001 // 0.1% 阈值
});

// 更宽松的阈值（动画时间更短）
const quickDuration = measureSpring({
  fps: 30,
  config: { damping: 200 },
  threshold: 0.01 // 1% 阈值
});
```

### 3. 不同的起始和结束值

```typescript
import { measureSpring } from "remotion";

const duration = measureSpring({
  fps: 30,
  config: { damping: 200 },
  from: 100,
  to: 500,
  threshold: 0.005
});
```

## 实际应用场景

### 1. 动态序列长度计算

```typescript
import React from 'react';
import { measureSpring, spring, useCurrentFrame, Sequence } from "remotion";

interface DynamicSequenceProps {
  springConfig: {
    damping: number;
    stiffness: number;
    mass: number;
  };
  fps: number;
}

const DynamicSequence: React.FC<DynamicSequenceProps> = ({ 
  springConfig, 
  fps 
}) => {
  // 计算弹簧动画的持续时间
  const springDuration = measureSpring({
    fps,
    config: springConfig,
    threshold: 0.005
  });

  const frame = useCurrentFrame();

  // 使用计算出的持续时间创建弹簧动画
  const animatedValue = spring({
    frame,
    fps,
    config: springConfig
  });

  return (
    <div>
      <Sequence from={0} durationInFrames={springDuration}>
        <div style={{
          transform: `translateX(${animatedValue * 200}px)`,
          width: 100,
          height: 100,
          backgroundColor: "#3498db",
          borderRadius: 10
        }}>
          弹簧动画元素
        </div>
      </Sequence>
      
      <div style={{
        position: "absolute",
        top: 120,
        left: 0,
        fontSize: 16,
        color: "#333"
      }}>
        动画持续时间: {springDuration} 帧
      </div>
    </div>
  );
};

export default DynamicSequence;
```

### 2. 多阶段动画规划

```typescript
import React from 'react';
import { measureSpring, spring, useCurrentFrame, Sequence, interpolate } from "remotion";

interface MultiStageAnimationProps {
  stages: Array<{
    name: string;
    springConfig: {
      damping: number;
      stiffness: number;
      mass: number;
    };
    from: number;
    to: number;
  }>;
  fps: number;
}

const MultiStageAnimation: React.FC<MultiStageAnimationProps> = ({ 
  stages, 
  fps 
}) => {
  const frame = useCurrentFrame();

  // 计算每个阶段的持续时间
  const stageDurations = stages.map(stage => 
    measureSpring({
      fps,
      config: stage.springConfig,
      from: stage.from,
      to: stage.to,
      threshold: 0.005
    })
  );

  // 计算累积时间偏移
  const stageOffsets = stageDurations.reduce((acc, duration, index) => {
    if (index === 0) return [0];
    return [...acc, acc[acc.length - 1] + stageDurations[index - 1]];
  }, [] as number[]);

  const totalDuration = stageDurations.reduce((sum, duration) => sum + duration, 0);

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundColor: "#f0f0f0"
    }}>
      {stages.map((stage, index) => {
        const stageFrame = Math.max(0, frame - stageOffsets[index]);
        
        const animatedValue = spring({
          frame: stageFrame,
          fps,
          config: stage.springConfig,
          from: stage.from,
          to: stage.to
        });

        return (
          <Sequence
            key={index}
            from={stageOffsets[index]}
            durationInFrames={stageDurations[index]}
          >
            <div style={{
              position: "absolute",
              left: 50,
              top: 50 + index * 120,
              width: 80,
              height: 80,
              backgroundColor: `hsl(${index * 60}, 70%, 50%)`,
              borderRadius: 10,
              transform: `translateX(${animatedValue}px)`,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontWeight: "bold"
            }}>
              {stage.name}
            </div>
          </Sequence>
        );
      })}
      
      {/* 时间轴显示 */}
      <div style={{
        position: "absolute",
        bottom: 50,
        left: 50,
        right: 50,
        height: 40,
        backgroundColor: "#333",
        borderRadius: 5,
        display: "flex",
        alignItems: "center",
        color: "white",
        padding: "0 20px"
      }}>
        <div style={{
          width: `${(frame / totalDuration) * 100}%`,
          height: "100%",
          backgroundColor: "#3498db",
          borderRadius: 5,
          position: "absolute",
          left: 0,
          top: 0
        }} />
        <span style={{ position: "relative", zIndex: 1 }}>
          进度: {frame} / {totalDuration} 帧
        </span>
      </div>
    </div>
  );
};

// 使用示例
export const AnimationPipeline = () => {
  const animationStages = [
    {
      name: "入场",
      springConfig: { damping: 200, stiffness: 100, mass: 1 },
      from: 0,
      to: 200
    },
    {
      name: "弹跳",
      springConfig: { damping: 50, stiffness: 300, mass: 1 },
      from: 200,
      to: 300
    },
    {
      name: "稳定",
      springConfig: { damping: 300, stiffness: 200, mass: 1 },
      from: 300,
      to: 250
    }
  ];

  return <MultiStageAnimation stages={animationStages} fps={30} />;
};
```

### 3. 自适应动画时长

```typescript
import React from 'react';
import { measureSpring, spring, useCurrentFrame, useVideoConfig } from "remotion";

interface AdaptiveAnimationProps {
  targetDistance: number;
  animationIntensity: 'gentle' | 'normal' | 'energetic';
}

const AdaptiveAnimation: React.FC<AdaptiveAnimationProps> = ({ 
  targetDistance, 
  animationIntensity 
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 根据动画强度选择弹簧配置
  const getSpringConfig = () => {
    switch (animationIntensity) {
      case 'gentle':
        return { damping: 300, stiffness: 100, mass: 1 };
      case 'normal':
        return { damping: 200, stiffness: 200, mass: 1 };
      case 'energetic':
        return { damping: 100, stiffness: 400, mass: 1 };
      default:
        return { damping: 200, stiffness: 200, mass: 1 };
    }
  };

  const springConfig = getSpringConfig();

  // 测量动画持续时间
  const animationDuration = measureSpring({
    fps,
    config: springConfig,
    from: 0,
    to: targetDistance,
    threshold: 0.005
  });

  // 创建弹簧动画
  const animatedPosition = spring({
    frame,
    fps,
    config: springConfig,
    from: 0,
    to: targetDistance
  });

  // 计算完成百分比
  const completionPercentage = Math.min((frame / animationDuration) * 100, 100);

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundColor: "#f8f9fa",
      padding: 40
    }}>
      {/* 动画元素 */}
      <div style={{
        position: "absolute",
        left: 50,
        top: 200,
        width: 60,
        height: 60,
        backgroundColor: "#e74c3c",
        borderRadius: "50%",
        transform: `translateX(${animatedPosition}px)`,
        boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
      }} />

      {/* 目标位置指示器 */}
      <div style={{
        position: "absolute",
        left: 50 + targetDistance,
        top: 190,
        width: 2,
        height: 80,
        backgroundColor: "#27ae60",
        opacity: 0.7
      }} />

      {/* 信息面板 */}
      <div style={{
        position: "absolute",
        top: 50,
        left: 50,
        backgroundColor: "white",
        padding: 20,
        borderRadius: 10,
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        fontSize: 14,
        lineHeight: 1.5
      }}>
        <div><strong>动画强度:</strong> {animationIntensity}</div>
        <div><strong>目标距离:</strong> {targetDistance}px</div>
        <div><strong>预计持续时间:</strong> {animationDuration} 帧</div>
        <div><strong>当前进度:</strong> {completionPercentage.toFixed(1)}%</div>
        <div><strong>弹簧配置:</strong></div>
        <div style={{ marginLeft: 10, fontSize: 12, color: "#666" }}>
          阻尼: {springConfig.damping}<br/>
          刚度: {springConfig.stiffness}<br/>
          质量: {springConfig.mass}
        </div>
      </div>

      {/* 进度条 */}
      <div style={{
        position: "absolute",
        bottom: 50,
        left: 50,
        right: 50,
        height: 20,
        backgroundColor: "#ecf0f1",
        borderRadius: 10,
        overflow: "hidden"
      }}>
        <div style={{
          width: `${completionPercentage}%`,
          height: "100%",
          backgroundColor: "#3498db",
          borderRadius: 10,
          transition: "width 0.1s ease"
        }} />
      </div>
    </div>
  );
};

export default AdaptiveAnimation;
```

### 4. 弹簧配置比较工具

```typescript
import React from 'react';
import { measureSpring, spring, useCurrentFrame, useVideoConfig } from "remotion";

interface SpringComparison {
  name: string;
  config: {
    damping: number;
    stiffness: number;
    mass: number;
  };
  color: string;
}

const SpringComparisonTool: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  const springConfigs: SpringComparison[] = [
    {
      name: "过阻尼",
      config: { damping: 500, stiffness: 100, mass: 1 },
      color: "#e74c3c"
    },
    {
      name: "临界阻尼",
      config: { damping: 200, stiffness: 200, mass: 1 },
      color: "#f39c12"
    },
    {
      name: "欠阻尼",
      config: { damping: 50, stiffness: 200, mass: 1 },
      color: "#27ae60"
    },
    {
      name: "高频振荡",
      config: { damping: 30, stiffness: 500, mass: 1 },
      color: "#3498db"
    }
  ];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#f8f9fa",
      padding: 40
    }}>
      <h2 style={{
        textAlign: "center",
        marginBottom: 40,
        color: "#2c3e50"
      }}>
        弹簧配置比较
      </h2>

      {springConfigs.map((config, index) => {
        const duration = measureSpring({
          fps,
          config: config.config,
          threshold: 0.005
        });

        const animatedValue = spring({
          frame,
          fps,
          config: config.config,
          from: 0,
          to: 300
        });

        return (
          <div key={index} style={{
            marginBottom: 80,
            position: "relative"
          }}>
            {/* 配置信息 */}
            <div style={{
              display: "flex",
              alignItems: "center",
              marginBottom: 20,
              fontSize: 16
            }}>
              <div style={{
                width: 20,
                height: 20,
                backgroundColor: config.color,
                borderRadius: "50%",
                marginRight: 15
              }} />
              <div style={{ flex: 1 }}>
                <strong>{config.name}</strong>
                <div style={{ fontSize: 12, color: "#666", marginTop: 5 }}>
                  阻尼: {config.config.damping}, 
                  刚度: {config.config.stiffness}, 
                  质量: {config.config.mass}
                </div>
              </div>
              <div style={{
                fontSize: 14,
                color: "#666",
                textAlign: "right"
              }}>
                持续时间: {duration} 帧<br/>
                ({(duration / fps).toFixed(2)}秒)
              </div>
            </div>

            {/* 动画轨道 */}
            <div style={{
              position: "relative",
              height: 40,
              backgroundColor: "#ecf0f1",
              borderRadius: 20,
              overflow: "hidden"
            }}>
              {/* 轨道标记 */}
              <div style={{
                position: "absolute",
                right: 20,
                top: 0,
                bottom: 0,
                width: 2,
                backgroundColor: "#bdc3c7"
              }} />

              {/* 动画球 */}
              <div style={{
                position: "absolute",
                left: 20,
                top: "50%",
                transform: `translate(${animatedValue}px, -50%)`,
                width: 30,
                height: 30,
                backgroundColor: config.color,
                borderRadius: "50%",
                boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
                transition: frame === 0 ? "none" : "transform 0.1s ease"
              }} />
            </div>
          </div>
        );
      })}

      {/* 时间指示器 */}
      <div style={{
        position: "absolute",
        bottom: 20,
        left: 40,
        right: 40,
        textAlign: "center",
        fontSize: 18,
        color: "#2c3e50"
      }}>
        当前帧: {frame} | 时间: {(frame / fps).toFixed(2)}秒
      </div>
    </div>
  );
};

export default SpringComparisonTool;
```

### 5. 性能优化的动画预计算

```typescript
import React, { useMemo } from 'react';
import { measureSpring, spring, useCurrentFrame, useVideoConfig } from "remotion";

interface OptimizedAnimationProps {
  elements: Array<{
    id: string;
    startFrame: number;
    springConfig: {
      damping: number;
      stiffness: number;
      mass: number;
    };
    from: number;
    to: number;
  }>;
}

const OptimizedAnimation: React.FC<OptimizedAnimationProps> = ({ elements }) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 预计算所有动画的持续时间
  const animationData = useMemo(() => {
    return elements.map(element => {
      const duration = measureSpring({
        fps,
        config: element.springConfig,
        from: element.from,
        to: element.to,
        threshold: 0.005
      });

      return {
        ...element,
        duration,
        endFrame: element.startFrame + duration
      };
    });
  }, [elements, fps]);

  // 计算总动画时长
  const totalDuration = useMemo(() => {
    return Math.max(...animationData.map(data => data.endFrame));
  }, [animationData]);

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundColor: "#2c3e50"
    }}>
      {animationData.map((data, index) => {
        const elementFrame = Math.max(0, frame - data.startFrame);
        const isActive = frame >= data.startFrame && frame <= data.endFrame;

        const animatedValue = isActive ? spring({
          frame: elementFrame,
          fps,
          config: data.springConfig,
          from: data.from,
          to: data.to
        }) : (frame > data.endFrame ? data.to : data.from);

        return (
          <div
            key={data.id}
            style={{
              position: "absolute",
              left: 50,
              top: 50 + index * 60,
              width: 40,
              height: 40,
              backgroundColor: isActive ? "#e74c3c" : "#95a5a6",
              borderRadius: "50%",
              transform: `translateX(${animatedValue}px)`,
              opacity: isActive ? 1 : 0.5,
              transition: "background-color 0.2s ease, opacity 0.2s ease"
            }}
          />
        );
      })}

      {/* 性能信息 */}
      <div style={{
        position: "absolute",
        top: 20,
        right: 20,
        backgroundColor: "rgba(255,255,255,0.9)",
        padding: 15,
        borderRadius: 8,
        fontSize: 12,
        color: "#2c3e50"
      }}>
        <div><strong>性能统计:</strong></div>
        <div>元素数量: {elements.length}</div>
        <div>总持续时间: {totalDuration} 帧</div>
        <div>当前活跃: {animationData.filter(data => 
          frame >= data.startFrame && frame <= data.endFrame
        ).length}</div>
      </div>
    </div>
  );
};

export default OptimizedAnimation;
```

## 重要概念

### 阈值理解
- `0.005` (0.5%): 默认阈值，平衡精度和性能
- `0.001` (0.1%): 高精度，动画时间更长
- `0.01` (1%): 低精度，动画时间更短

### 理论说明
弹簧动画理论上永远不会完全停止，总是有微小的能量导致细微的运动。阈值定义了何时认为动画"完成"。

## 最佳实践

1. **性能优化**: 预计算动画持续时间，避免重复计算
2. **阈值选择**: 根据视觉需求选择合适的阈值
3. **配置缓存**: 对于相同的弹簧配置，缓存测量结果
4. **动画规划**: 使用测量结果规划复杂的动画序列
5. **调试工具**: 创建可视化工具比较不同配置

## 常见用例

- 动态序列长度计算
- 多阶段动画规划
- 自适应动画时长
- 弹簧配置比较
- 性能优化预计算

## 相关 API

- [`spring()`](./spring.md) - 弹簧动画函数
- [`interpolate()`](./interpolate.md) - 数值插值
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`Sequence`](./Sequence.md) - 时间序列组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/spring/measure-spring.ts)
