import React from 'react';
import { AbsoluteFill, OffthreadVideo, staticFile } from 'remotion';

interface CenteredVideoProps {
  src: string;
  isMuted?: boolean;
}

export const CenteredVideo: React.FC<CenteredVideoProps> = ({ src, isMuted = false }) => {
  return (
    <AbsoluteFill
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <OffthreadVideo
        src={staticFile(src)}
        muted={isMuted}
        style={{
          width: '100%',
          height: 'auto',
        }}
      />
    </AbsoluteFill>
  );
};