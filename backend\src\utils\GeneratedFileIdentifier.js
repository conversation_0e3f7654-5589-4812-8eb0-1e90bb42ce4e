/**
 * @功能概述: Generated目录文件识别器 - 基于Task源码的精确文件类型识别
 * @作者: AI Assistant
 * @创建时间: 2025-07-26
 * 
 * @技术基础: 基于BilingualSubtitleMergeTask和GenerateVideoTask的实际命名规律
 * @支持类型:
 *   - extended_video: GenerateVideoTask生成的扩展视频文件
 *   - bilingual_subtitle: BilingualSubtitleMergeTask生成的双语字幕文件
 * 
 * @命名规律:
 *   - 视频: ${videoIdentifier}_extended_video_${timestamp}.mp4
 *   - 字幕: ${videoIdentifier}_enhanced_bilingual_subtitle_${timestamp}.json
 *   - 时间戳: YYYY-MM-DDTHH-mm-ss-sssZ (ISO格式，冒号替换为横线)
 */

const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class GeneratedFileIdentifier {
    /**
     * @功能概述: 识别generated目录中的文件类型
     * @参数说明:
     *   - filename: 文件名
     * @返回值: {type, isGenerated, videoIdentifier, timestamp}
     */
    static identifyFile(filename) {
        // 基于Task代码的精确模式匹配
        const patterns = {
            // BilingualSubtitleMergeTask生成的双语字幕
            bilingual_subtitle: /_enhanced_bilingual_subtitle_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.json$/,
            
            // GenerateVideoTask生成的扩展视频
            extended_video: /_extended_video_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.mp4$/,
            
            // GenerateVideoTask生成的中间文件（可选识别）
            extended_audio: /_extended_audio\.mp3$/,
            progress_bar: /_progress_bar_.*\.mp4$/,
            background: /_background_.*\.mp4$/
        };

        for (const [type, pattern] of Object.entries(patterns)) {
            if (pattern.test(filename)) {
                return {
                    type,
                    isGenerated: true,
                    videoIdentifier: this.extractVideoIdentifier(filename, type),
                    timestamp: this.extractTimestamp(filename)
                };
            }
        }

        return { type: 'unknown', isGenerated: false };
    }

    /**
     * @功能概述: 提取视频标识符
     * @参数说明:
     *   - filename: 文件名
     *   - type: 文件类型
     * @返回值: string - 视频标识符
     */
    static extractVideoIdentifier(filename, type) {
        const patterns = {
            bilingual_subtitle: /^(.+)_enhanced_bilingual_subtitle_/,
            extended_video: /^(.+)_extended_video_/
        };

        const pattern = patterns[type];
        if (pattern) {
            const match = filename.match(pattern);
            return match ? match[1] : null;
        }
        return null;
    }

    /**
     * @功能概述: 提取时间戳
     * @参数说明:
     *   - filename: 文件名
     * @返回值: string - ISO时间戳
     */
    static extractTimestamp(filename) {
        const match = filename.match(/(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)/);
        return match ? match[1] : null;
    }

    /**
     * @功能概述: 扫描generated目录并分类文件
     * @参数说明:
     *   - generatedDirPath: generated目录路径
     * @返回值: {videos, subtitles, others, pairs}
     */
    static scanGeneratedDirectory(generatedDirPath) {
        const functionName = 'scanGeneratedDirectory';
        
        if (!fs.existsSync(generatedDirPath)) {
            logger.warn(`[${functionName}] Generated目录不存在: ${generatedDirPath}`);
            return { videos: [], subtitles: [], others: [], pairs: [] };
        }

        const files = fs.readdirSync(generatedDirPath);
        const result = {
            videos: [],
            subtitles: [],
            others: [],
            pairs: []
        };

        logger.info(`[${functionName}] 开始扫描目录: ${generatedDirPath}, 发现 ${files.length} 个文件`);

        // 分类文件
        files.forEach(filename => {
            const identification = this.identifyFile(filename);
            const filePath = path.join(generatedDirPath, filename);

            switch (identification.type) {
                case 'extended_video':
                    result.videos.push({
                        filename,
                        path: filePath,
                        videoIdentifier: identification.videoIdentifier,
                        timestamp: identification.timestamp,
                        type: 'extended_video'
                    });
                    break;
                
                case 'bilingual_subtitle':
                    result.subtitles.push({
                        filename,
                        path: filePath,
                        videoIdentifier: identification.videoIdentifier,
                        timestamp: identification.timestamp,
                        type: 'bilingual_subtitle'
                    });
                    break;
                
                default:
                    if (identification.isGenerated) {
                        result.others.push({
                            filename,
                            path: filePath,
                            type: identification.type
                        });
                    }
            }
        });

        // 配对视频和字幕
        result.pairs = this.pairVideoWithSubtitle(result.videos, result.subtitles);

        logger.info(`[${functionName}] 扫描完成: 视频${result.videos.length}个, 字幕${result.subtitles.length}个, 配对${result.pairs.length}个`);
        return result;
    }

    /**
     * @功能概述: 配对视频和字幕文件
     * @参数说明:
     *   - videos: 视频文件数组
     *   - subtitles: 字幕文件数组
     * @返回值: 配对结果数组
     */
    static pairVideoWithSubtitle(videos, subtitles) {
        return videos.map(video => {
            // 基于videoIdentifier精确匹配
            const matchingSubtitle = subtitles.find(subtitle => 
                subtitle.videoIdentifier === video.videoIdentifier
            );

            return {
                videoIdentifier: video.videoIdentifier,
                video: video,
                subtitle: matchingSubtitle || null,
                isPaired: !!matchingSubtitle
            };
        });
    }

    /**
     * @功能概述: 获取最新的视频-字幕对
     * @参数说明:
     *   - generatedDirPath: generated目录路径
     *   - limit: 返回数量限制
     * @返回值: 按时间排序的配对结果
     */
    static getLatestPairs(generatedDirPath, limit = 10) {
        const scanResult = this.scanGeneratedDirectory(generatedDirPath);
        
        return scanResult.pairs
            .sort((a, b) => {
                // 按视频时间戳降序排列
                const timeA = a.video.timestamp;
                const timeB = b.video.timestamp;
                return timeB.localeCompare(timeA);
            })
            .slice(0, limit);
    }
}

module.exports = GeneratedFileIdentifier;
