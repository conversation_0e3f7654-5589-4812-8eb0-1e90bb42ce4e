# 项目文件结构与特定目录说明

本文档概述了当前 Express.js 项目的主要文件和目录结构，并特别说明了几个关键目录的用途，以帮助 AI 理解代码库和开发流程。

## 项目根目录 (`express/`)

*   **`/`**: 项目的根目录，包含顶级配置文件和目录。
    *   `.cursor/`: Cursor AI 相关配置和规则。
        *   `rules/`: Cursor 规则文件目录，包含如 `code-commenting-standard.mdc` 等规则文件。
    *   `doc/`: 项目文档和记录。
        *   `logs/`: 应用日志文件存放目录 (根据代码注释标准的规范)，例如 `app.log`。
        *   `rules/`: 项目规则文档目录，例如 `code-commenting-standard.md`。
        *   `Node.js & Azure AI Speech 安装.md`: 环境搭建和安装记录。
        *   `plan.md`: 项目方案、架构和流程文档。
    *   `backend/`: Express.js 后端应用代码。
    *   `frontend/`: 测试前端静态文件 (由 Express 后端提供服务)。
    *   `.env`: 环境变量配置文件，存放敏感信息和配置。
    *   `.gitignore`: Git 版本控制忽略文件列表。
    *   `package.json`: Node.js 项目依赖和脚本定义文件。
    *   `package-lock.json`: 锁定项目依赖的具体版本。

## `backend/` 目录详解

*   **`backend/`**: 存放所有 Express.js 后端相关的代码。
    *   `src/`: 存放实际的后端源代码。
        *   `controllers/`: **控制器层**：处理传入请求，协调服务层并准备响应。例如 `videoController.js` 负责处理视频上传请求。
        *   `services/`: **服务层**：包含核心业务逻辑，如与 Azure、Dify 的交互。例如 `azureSpeechService.js` 处理 Azure API 调用，`difyService.js` 处理 Dify API 调用。
        *   `routes/`: **路由定义**：定义 API 端点 (如 `/upload`, `/status`)，并将请求导向相应的控制器方法。例如 `videoRoutes.js` 定义视频相关的 API 路由。
        *   `middleware/`: **Express 中间件**：处理请求的预处理和后处理。例如 `uploadMiddleware.js` 配置和使用 multer 处理文件上传。
        *   `utils/`: **工具函数和模块**：可重用的辅助代码。例如 `logger.js` 配置 Winston 日志。
        *   `config/`: **应用配置**：集中管理配置信息，通常从 `.env` 文件加载。例如 `index.js` 加载并提供配置。
        *   `app.js`: Express 应用的**入口文件**，设置中间件、路由等。

## `frontend/` 目录详解

*   **`frontend/`**: 存放用于测试后端接口的简单前端静态文件。
    *   `public/`: **可公开访问的静态资源目录**，Express 后端可以直接提供服务。
        *   `index.html`: 包含文件上传表单和结果展示的测试页面。
        *   `js/`: 前端 JavaScript 文件，例如 `upload.js` 处理上传逻辑。
        *   `css/`: 前端 CSS 文件，例如 `style.css`。

## 关键文件作用说明

*   `.env`: 存放 Azure AI 服务密钥、Dify API 端点等敏感配置信息。
*   `.gitignore`: 配置 Git 忽略文件，确保敏感信息和生成的目录不被提交。
*   `package.json`: 管理项目依赖和定义运行脚本。
*   `package-lock.json`: 锁定项目依赖版本，保证环境一致性。
*   `backend/src/app.js`: 后端应用的启动文件。
*   `frontend/public/index.html`: 测试页面的入口 HTML 文件。

## AI 交互注意事项

*   **查找后端核心逻辑**: 与外部服务 (Azure, Dify) 交互或复杂业务处理 -> 查看 `backend/src/services/`。
*   **查找 API 端点定义**: 查看 `backend/src/routes/`。
*   **查找请求处理逻辑**: 查看 `backend/src/controllers/`。
*   **查找文件上传配置**: 查看 `backend/src/middleware/uploadMiddleware.js`。
*   **查找应用配置**: 查看 `backend/src/config/` 和 `.env` 文件。
*   **查找日志配置**: 查看 `backend/src/utils/logger.js`。
*   **理解应用启动流程**: 查看 `backend/src/app.js`。
*   **理解测试前端**: 查看 `frontend/public/` 下的 `index.html`, `js/`, `css/` 文件。
*   **代码注释**: 添加或修改代码时，务必遵循 `@code-commenting-standard.mdc`。

## 使用说明 (查找代码)

*   **理解后端架构**: 查看 `backend/src/` 下的目录结构。
*   **查找特定 API 的实现**: 根据路由定义 (`backend/src/routes/`) 定位控制器 (`backend/src/controllers/`)，再定位服务 (`backend/src/services/`)。
*   **查找配置信息**: 查看 `backend/src/config/` 和 `.env`。
*   **理解前端交互**: 查看 `frontend/public/js/` 下的脚本文件。

*   进行主题开发或 API 开发时，主要关注 `wp-content/themes/shuimitao.online/` 目录。
*   涉及用户数据处理和模型时，查看 `wp-content/themes/shuimitao.online/api/v1/models/`。
*   理解 API 设计意图时，参考 `wp-content/themes/shuimitao.online/api/v1/apidoc/`。
*   修改核心业务逻辑或添加钩子时，检查 `wp-content/themes/shuimitao.online/functions.php`。 