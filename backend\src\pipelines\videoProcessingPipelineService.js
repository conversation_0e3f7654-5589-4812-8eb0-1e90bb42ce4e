/**
 * @功能概述: 视频处理流程服务，负责构建和执行视频处理相关的 Pipeline。
 *           此服务编排多个任务，如视频转音频、音频转录和字幕优化。
 *           集成标准化进度监控机制，支持细粒度的流水线和任务进度追踪。
 *           实现流水线级别的容错降级策略，确保即使转录API失败也能返回结果。
 * 
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 * 
 * @容错策略: 实现智能降级机制
 *   - 转录失败时自动跳过字幕优化任务
 *   - 生成基础字幕文件和默认结果
 *   - 保持接口返回格式的一致性
 *   - 向前端明确标识降级状态
 * 
 * @进度监控: 使用标准化的进度监控机制，支持实时SSE事件推送
 * @数据流向: TaskBase → PipelineBase → VideoProcessingPipelineService → Controller → Frontend
 */

const PipelineBase = require('../class/PipelineBase');
const { ConvertToAudioForCloudflareTask, GetTranscriptionTask, SubtitleOptimizationTask } = require('../tasks');
const logger = require('../utils/logger');
const config = require('../config');
const { standardizePipelineResult } = require('../utils/pipelineResultStandardizer');
const fileSaver = require('../utils/fileSaver');

// 导入标准化进度监控常量和工厂函数
const { 
    PIPELINE_STATUS, 
    TASK_STATUS,
    createPipelineProgressData,
    validateProgressData 
} = require('../constants/progress');

// 模块级日志前缀，用于标识从本文件输出的日志
const moduleLogPrefix = `[文件：videoProcessingPipelineService.js][视频流程服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录，任务通过 services/ 目录调用外部API`);
logger.info(`${moduleLogPrefix}[容错策略] 已集成流水线级别降级策略，支持转录API失败时的自动降级`);

/**
 * @功能概述: 视频处理流水线服务类，负责编排和执行视频处理相关的任务序列
 * @继承关系: 使用 PipelineBase 作为底层流水线执行引擎
 * @任务序列: ConvertToAudioForCloudflareTask → GetTranscriptionTask → SubtitleOptimizationTask (可跳过)
 * @进度监控: 集成标准化进度监控，支持流水线级别和任务级别的双重进度追踪
 * @容错处理: 支持转录任务失败时的智能降级，确保用户始终能获得结果
 */
class VideoProcessingPipelineService {
    /**
     * @功能概述: 构造函数。初始化处理流水线并添加所需任务。
     *           使用标准化的任务添加方式和进度监控机制。
     *           配置容错降级策略。
     * 
     * @param {string} [reqId='unknown_req'] - 请求ID，用于日志追踪和流水线标识
     * 
     * @说明: 
     *   - 创建 PipelineBase 实例作为底层执行引擎
     *   - 按照业务流程顺序添加所有必需任务
     *   - 每个任务都有详细的上下文依赖和输出说明
     *   - 支持标准化的进度监控和错误处理
     *   - 配置容错降级机制，确保服务可用性
     */
    constructor(reqId = 'unknown_req') {
        // 基础标识信息
        this.reqId = reqId;
        this.logPrefix = `[文件：videoProcessingPipelineService.js][视频流程服务][ReqID:${this.reqId}]`;

        // 创建底层流水线实例，使用标准化命名
        this.processingPipeline = new PipelineBase(`VideoProcessing-${this.reqId}`);

        // 容错配置
        this.fallbackConfig = {
            enableFallback: config.pipeline?.enableFallback !== false, // 默认启用降级
            generateBasicSubtitles: true, // 生成基础字幕文件
            skipOptimizationOnTranscriptionFailure: true, // 转录失败时跳过优化
            fallbackMessage: "转录服务暂时不可用，已生成基础时间轴字幕"
        };

        // 按照业务流程顺序添加所有任务
        this.addAllTasks();
        
        // 记录流水线创建完成，包含完整任务序列和容错配置
        logger.info(`${this.logPrefix}VideoProcessing Pipeline 已创建，包含完整任务序列: ConvertToAudioForCloudflareTask → GetTranscriptionTask → SubtitleOptimizationTask`);
        logger.info(`${this.logPrefix}容错配置: ${JSON.stringify(this.fallbackConfig)}`);

        // 新增调试标记
        this.debugMode = config.pipeline?.debugMode || false;
        logger.info(`${this.logPrefix} 调试模式: ${this.debugMode ? '启用' : '禁用'}`);
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     * @说明: 将任务添加逻辑集中管理，便于维护和调试
     */
    addAllTasks() {
        this.addConvertToAudioTask();
        this.addGetTranscriptionTask();
        this.addSubtitleOptimizationTask();
    }

    /**
     * @功能概述: 添加Cloudflare专用视频转音频任务 (ConvertToAudioForCloudflareTask) 到流水线。
     *           此任务负责将上传的视频文件转换为极度压缩的音频格式，专门针对Cloudflare Workers AI优化。
     *
     * @上下文输入 (context 预期的字段):
     *   - reqId: {string} (必需) 请求追踪ID
     *   - videoIdentifier: {string} (必需) 视频标识符
     *   - uploadedVideoDirPath: {string} (必需) 上传视频文件所在目录路径
     *   - originalVideoName: {string} (必需) 原始视频文件名
     *   - savePath: {string} (必需) 保存路径前缀
     *   - config.UPLOAD_DIR: {string} (来自配置模块) 上传目录配置
     *
     * @执行后上下文状态 (ConvertToAudioForCloudflareTask 完成后 context 对象的完整内容):
     *   // === 来自初始输入 ===
     *   - reqId: {string} 请求追踪ID
     *   - videoIdentifier: {string} 视频标识符
     *   - uploadedVideoDirPath: {string} 上传视频文件目录路径
     *   - originalVideoName: {string} 原始视频文件名
     *   - savePath: {string} 保存路径前缀
     *   
     *   // === 来自 ConvertToAudioForCloudflareTask (第1个任务新增) ===
     *   - audioFilePathInUploads: {string} 转换后的音频文件路径（相对于uploads目录）
     *   - audioFileName: {string} 转换后的音频文件名
     *   - conversionStatus: {string} 转换状态（'success' 或 'failed'）
     *   (任务内部会更新自身的 this.status 和 this.result)
     */
    addConvertToAudioTask() {
        this.processingPipeline.addTask(new ConvertToAudioForCloudflareTask());
    }

    addGetTranscriptionTask() {
        this.processingPipeline.addTask(new GetTranscriptionTask());
    }

    addSubtitleOptimizationTask() {
        this.processingPipeline.addTask(new SubtitleOptimizationTask());
    }

    /**
     * @功能概述: 执行完整的视频处理流程，包括音频转换、语音转录和字幕优化。
     *           实现智能容错机制，当转录任务失败时自动降级处理。
     * @param {object} initialContext - 初始上下文，必须包含:
     *                                 - reqId: string - 请求ID
     *                                 - videoIdentifier: string - 视频标识符
     *                                 - uploadedVideoDirPath: string - 上传视频目录路径
     *                                 - originalVideoName: string - 原始视频文件名
     *                                 - savePath: string - 保存路径前缀
     *                                 - (可选) reqId: string - 请求ID，会传递给各任务
     * @param {function} serviceProgressCallback - 服务进度回调函数，用于报告服务执行状态
     * @returns {Promise<object>} 标准化的Pipeline执行结果，包含 status, context, tasks。
     *                            成功时，context 中应包含:
     *                              - audioFilePathInUploads: string (来自 ConvertToAudioForCloudflareTask)
     *                              - rawTranscriptionText: string (来自 GetTranscriptionTask 或降级结果)
     *                              - optimizedSrtContent: string (来自 SubtitleOptimizationTask 或降级结果)
     */
    async processUploadedVideo(initialContext, serviceProgressCallback) {
        // 新增调试钩子
        if (this.debugMode) {
            this.enableDebugHooks();
        }

        logger.info(`${this.logPrefix}[processUploadedVideo] 开始执行视频处理流程 (音频转换、转录和字幕优化)。`); 
        
        // 步骤 1: 构建执行上下文
        // 确保 reqId 优先使用 initialContext 中的值，如果不存在则使用 this.reqId
        const currentContext = {
            reqId: initialContext.reqId || this.reqId, 
            ...initialContext 
        };
        
        // 步骤 2: 参数校验
        // 定义必需的字段列表
        const requiredFields = ['reqId', 'videoIdentifier', 'uploadedVideoDirPath', 'savePath', 'originalVideoName'];
        let missingFields = [];
        for (const field of requiredFields) {
            if (!currentContext[field]) {
                missingFields.push(field);
            }
        }

        if (missingFields.length > 0) {
            const errorMsg = `processUploadedVideo 调用失败：initialContext 必须包含以下字段: ${missingFields.join(', ')}。`;
            logger.error(`${this.logPrefix}[processUploadedVideo] ${errorMsg}`);
            
            // 步骤 2.1: 参数校验失败时的回调处理
            if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
                try {
                    logger.debug(`${this.logPrefix}[processUploadedVideo] 参数校验失败，调用服务进度回调。`);
                    
                    // 使用标准化工厂函数创建服务级别的错误进度数据
                    const serviceErrorData = createPipelineProgressData({
                        pipelineName: `VideoProcessing-${this.reqId}`,
                        pipelineId: this.processingPipeline.pipelineId,
                        pipelineStatus: PIPELINE_STATUS.FAILED,
                        tasks: [], // 没有任务执行
                        failedTaskName: null, // 不是任务失败，是服务级别失败
                        failedTaskErrorDetails: { 
                            message: errorMsg, 
                            name: 'ValidationError',
                            stackPreview: 'Service level validation failure'
                        },
                        // 服务级别的额外信息
                        serviceName: 'VideoProcessingPipelineService',
                        methodName: 'processUploadedVideo',
                        serviceStatus: 'failed_setup'
                    });
                    
                    // 验证进度数据的完整性
                    if (validateProgressData(serviceErrorData)) {
                        serviceProgressCallback(serviceErrorData);
                    } else {
                        logger.warn(`${this.logPrefix}[processUploadedVideo] 生成的服务错误进度数据验证失败`);
                    }
                } catch (cbError) {
                    logger.error(`${this.logPrefix}[processUploadedVideo] 服务进度回调执行出错: ${cbError.message}`);
                }
            }
            
            return { 
                status: 'failed', 
                error: new Error(errorMsg), 
                context: currentContext, 
                tasks: [] 
            };
        }
        
        // 步骤 3: 记录任务序列
        logger.info(`${this.logPrefix}[processUploadedVideo][步骤 1] 流水线任务序列确认: ConvertToAudioForCloudflareTask → GetTranscriptionTask → SubtitleOptimizationTask`);
        logger.info(`${this.logPrefix}[processUploadedVideo][步骤 1.1] 接收到的上下文关键信息: reqId=${currentContext.reqId}, videoIdentifier=${currentContext.videoIdentifier}, uploadedVideoDirPath=${currentContext.uploadedVideoDirPath}, savePath=${currentContext.savePath}, originalVideoName=${currentContext.originalVideoName}`);

        // 步骤 4: 执行流水线（使用自定义执行逻辑以支持容错）
        logger.debug(`${this.logPrefix}[processUploadedVideo] 执行内部流水线，并传递回调函数。`);
        const result = await this.executeWithFallback(currentContext, serviceProgressCallback);
        logger.info(`${this.logPrefix}[processUploadedVideo] 视频处理流程执行完毕。Status: ${result.status}`);
        
        // 新增调试数据转储
        if (this.debugMode) {
            this.dumpDebugData(result);
        }

        // 步骤 5: 记录关键输出
        if (result.context) {
            const { audioFilePathInUploads, transcriptionStatus, optimizationStatus, optimizedSrtContent, pipelineError, fallbackMode } = result.context;

            // 检查是否有流水线级别的错误信息
            if (pipelineError) {
                logger.error(`${this.logPrefix}[processUploadedVideo] 流水线执行中捕获到错误。`);
                logger.error(`${this.logPrefix}[processUploadedVideo] 失败任务: ${pipelineError.failedTaskName}`);
                logger.error(`${this.logPrefix}[processUploadedVideo] 错误类型: ${pipelineError.name}`);
                logger.error(`${this.logPrefix}[processUploadedVideo] 错误消息: ${pipelineError.message}`);
                if (pipelineError.stack) {
                    logger.error(`${this.logPrefix}[processUploadedVideo] 错误栈预览: ${pipelineError.stack.substring(0, 200)}...`);
                }
            } else {
                // 记录成功时的关键输出信息
                logger.info(`${this.logPrefix}[processUploadedVideo] 流水线执行成功，最终上下文关键信息:`);
                logger.info(`${this.logPrefix}  reqId=${result.context.reqId}`);
                logger.info(`${this.logPrefix}  videoIdentifier=${result.context.videoIdentifier}`);
                logger.info(`${this.logPrefix}  audioFilePathInUploads=${audioFilePathInUploads}`);
                logger.info(`${this.logPrefix}  transcriptionStatus=${transcriptionStatus}`);
                logger.info(`${this.logPrefix}  optimizationStatus=${optimizationStatus}`);
                logger.info(`${this.logPrefix}  optimizedSrtContent长度=${optimizedSrtContent ? optimizedSrtContent.length : '未知'}`);
                
                // 记录降级模式信息
                if (fallbackMode) {
                    logger.info(`${this.logPrefix}  降级模式: ${fallbackMode.enabled ? '启用' : '禁用'}`);
                    logger.info(`${this.logPrefix}  降级原因: ${fallbackMode.reason || 'N/A'}`);
                    logger.info(`${this.logPrefix}  跳过的任务: ${fallbackMode.skippedTasks ? fallbackMode.skippedTasks.join(', ') : 'N/A'}`);
                }
                 // 可以在这里添加更多需要记录的关键字段
            }

        }

        // 步骤 6: 标准化返回结果
        const standardizedResult = standardizePipelineResult(result, 'upload', currentContext.reqId);
        logger.info(`${this.logPrefix}[processUploadedVideo] 结果已标准化，状态: ${standardizedResult.status}`);

        return standardizedResult;
    }

    /**
     * @功能概述: 带容错机制的流水线执行方法
     * @param {object} context - 执行上下文
     * @param {function} progressCallback - 进度回调函数
     * @returns {Promise<object>} 执行结果
     */
    async executeWithFallback(context, progressCallback) {
        logger.info(`${this.logPrefix}[executeWithFallback] 开始执行带容错机制的流水线`);
        
        try {
            // 执行正常流水线
            const result = await this.processingPipeline.execute(context, progressCallback);
            
            // 检查GetTranscriptionTask是否失败
            const transcriptionTask = this.processingPipeline.tasks.find(task => task.name === 'GetTranscriptionTask');
            
            if (transcriptionTask && transcriptionTask.status === TASK_STATUS.FAILED) {
                logger.warn(`${this.logPrefix}[executeWithFallback] 检测到GetTranscriptionTask失败，启动降级处理`);
                return await this.handleTranscriptionFailureFallback(result, context, progressCallback);
            }
            
            return result;
            
        } catch (error) {
            logger.error(`${this.logPrefix}[executeWithFallback] 流水线执行异常: ${error.message}`);
            
            // 检查是否是转录任务导致的失败
            const transcriptionTask = this.processingPipeline.tasks.find(task => task.name === 'GetTranscriptionTask');
            
            if (transcriptionTask && transcriptionTask.status === TASK_STATUS.FAILED && this.fallbackConfig.enableFallback) {
                logger.info(`${this.logPrefix}[executeWithFallback] 转录任务失败，尝试降级处理`);
                return await this.handleTranscriptionFailureFallback(null, context, progressCallback);
            }
            
            // 如果不是转录失败或未启用降级，重新抛出错误
            throw error;
        }
    }

    /**
     * @功能概述: 处理转录任务失败的降级逻辑
     * @param {object|null} partialResult - 部分执行结果（可能为null）
     * @param {object} context - 原始上下文
     * @param {function} progressCallback - 进度回调函数
     * @returns {Promise<object>} 降级处理结果
     */
    async handleTranscriptionFailureFallback(partialResult, context, progressCallback) {
        logger.info(`${this.logPrefix}[handleTranscriptionFailureFallback] 开始处理转录失败降级`);
        
        try {
            // 发送降级模式进度通知
            if (progressCallback && typeof progressCallback === 'function') {
                const fallbackProgressData = createPipelineProgressData({
                    pipelineName: `VideoProcessing-${this.reqId}`,
                    pipelineId: this.processingPipeline.pipelineId,
                    pipelineStatus: PIPELINE_STATUS.RUNNING,
                    currentTaskName: 'FallbackProcessing',
                    currentTaskStatus: TASK_STATUS.RUNNING,
                    currentTaskDetail: '转录服务不可用，正在生成降级结果',
                    fallbackMode: {
                        enabled: true,
                        reason: 'transcription_api_failure',
                        action: 'generating_basic_subtitles'
                    }
                });
                
                progressCallback(fallbackProgressData);
            }
            
            // 生成降级结果
            const fallbackResult = await this.generateFallbackResult(context, partialResult);
            
            // 发送降级完成进度通知
            if (progressCallback && typeof progressCallback === 'function') {
                const fallbackCompleteData = createPipelineProgressData({
                    pipelineName: `VideoProcessing-${this.reqId}`,
                    pipelineId: this.processingPipeline.pipelineId,
                    pipelineStatus: PIPELINE_STATUS.COMPLETED,
                    currentTaskName: 'FallbackProcessing',
                    currentTaskStatus: TASK_STATUS.COMPLETED,
                    currentTaskDetail: '降级处理完成，已生成基础字幕文件',
                    finalMessage: this.fallbackConfig.fallbackMessage,
                    fallbackMode: {
                        enabled: true,
                        reason: 'transcription_api_failure',
                        action: 'completed_basic_subtitles'
                    }
                });
                
                progressCallback(fallbackCompleteData);
            }
            
            logger.info(`${this.logPrefix}[handleTranscriptionFailureFallback] 降级处理完成`);
            return fallbackResult;
            
        } catch (fallbackError) {
            logger.error(`${this.logPrefix}[handleTranscriptionFailureFallback] 降级处理失败: ${fallbackError.message}`);
            
            // 降级处理也失败时，返回最基础的错误结果
            return {
                status: 'failed',
                error: fallbackError,
                context: {
                    ...context,
                    fallbackMode: {
                        enabled: true,
                        failed: true,
                        reason: 'transcription_api_failure',
                        fallbackError: fallbackError.message
                    }
                },
                tasks: this.processingPipeline.tasks || []
            };
        }
    }

    /**
     * @功能概述: 生成降级结果
     * @param {object} context - 原始上下文
     * @param {object|null} partialResult - 部分执行结果
     * @returns {Promise<object>} 降级结果
     */
    async generateFallbackResult(context, partialResult) {
        logger.info(`${this.logPrefix}[generateFallbackResult] 开始生成降级结果`);
        
        // 获取音频转换任务的结果（应该是成功的）
        const audioTask = this.processingPipeline.tasks.find(task => task.name === 'ConvertToAudioForCloudflareTask');
        const audioResult = audioTask?.result || {};
        
        // 生成基础的转录数据结构（模拟正常的转录结果格式）
        const fallbackTranscriptionData = this.generateBasicTranscriptionData(context, audioResult);
        
        // 生成基础字幕文件
        const fallbackSubtitleFiles = await this.generateBasicSubtitleFiles(context, fallbackTranscriptionData);
        
        // 构建完整的降级结果
        const fallbackContext = {
            ...context,
            // 保留音频转换的结果
            ...audioResult,
            
            // 添加降级的转录结果
            transcriptionStatus: 'fallback',
            apiResponse: fallbackTranscriptionData,
            transcriptionJsonPath: fallbackSubtitleFiles.transcriptionJsonPath,
            
            // 添加降级的字幕优化结果
            optimizationStatus: 'skipped_due_to_fallback',
            simplifiedSubtitleJsonArray: fallbackSubtitleFiles.simplifiedSubtitleJsonArray,
            simplifiedSubtitleJsonPath: fallbackSubtitleFiles.simplifiedSubtitleJsonPath,
            optimizedEnglishSrtContent: fallbackSubtitleFiles.optimizedEnglishSrtContent,
            
            // 降级模式标识
            fallbackMode: {
                enabled: true,
                reason: 'transcription_api_failure',
                message: this.fallbackConfig.fallbackMessage,
                skippedTasks: ['SubtitleOptimizationTask'],
                generatedFiles: [
                    fallbackSubtitleFiles.transcriptionJsonPath,
                    fallbackSubtitleFiles.simplifiedSubtitleJsonPath,
                    fallbackSubtitleFiles.englishSrtPath // 新增：英文SRT文件
                ]
            }
        };
        
        logger.info(`${this.logPrefix}[generateFallbackResult] 降级结果生成完成`);
        
        return {
            status: 'completed',
            context: fallbackContext,
            tasks: this.processingPipeline.tasks || []
        };
    }

    /**
     * @功能概述: 生成基础的转录数据结构
     * @param {object} context - 上下文
     * @param {object} audioResult - 音频转换结果
     * @returns {object} 基础转录数据
     */
    generateBasicTranscriptionData(context, audioResult) {
        logger.info(`${this.logPrefix}[generateBasicTranscriptionData] 生成基础转录数据结构`);
        
        // 估算视频时长（基于音频文件信息或默认值）
        const estimatedDuration = audioResult.estimatedDuration || 60; // 默认60秒
        
        // 生成基础的segments（每10秒一个segment）
        const segments = [];
        const segmentDuration = 10; // 每个segment 10秒
        const segmentCount = Math.ceil(estimatedDuration / segmentDuration);
        
        for (let i = 0; i < segmentCount; i++) {
            const start = i * segmentDuration;
            const end = Math.min((i + 1) * segmentDuration, estimatedDuration);
            
            segments.push({
                id: i,
                seek: 0,
                start: start,
                end: end,
                text: `[${this.formatTime(start)} - ${this.formatTime(end)}] 转录服务暂时不可用`,
                tokens: [],
                temperature: 0,
                avg_logprob: -0.5,
                compression_ratio: 1.0,
                no_speech_prob: 0.1,
                words: [
                    {
                        text: "[转录服务暂时不可用]",
                        start: start,
                        end: end
                    }
                ]
            });
        }
        
        return {
            task: 'transcribe',
            language: 'en',
            duration: estimatedDuration,
            text: segments.map(s => s.text).join(' '),
            segments: segments
        };
    }

    /**
     * @功能概述: 生成基础字幕文件
     * @param {object} context - 上下文
     * @param {object} transcriptionData - 转录数据
     * @returns {Promise<object>} 生成的文件路径信息
     */
    async generateBasicSubtitleFiles(context, transcriptionData) {
        logger.info(`${this.logPrefix}[generateBasicSubtitleFiles] 开始生成基础字幕文件`);
        
        const { videoIdentifier, savePath } = context;
        const execLogPrefix = `${this.logPrefix}[generateBasicSubtitleFiles]`;
        
        try {
            // 1. 保存转录JSON文件
            const transcriptionFilename = `${videoIdentifier}_transcription.json`;
            const transcriptionJsonPath = await fileSaver.saveDataToFile(
                JSON.stringify(transcriptionData, null, 2),
                transcriptionFilename,
                savePath,
                execLogPrefix
            );
            
            // 2. 生成简化字幕JSON
            const simplifiedSubtitleJsonArray = transcriptionData.segments.map((segment, index) => ({
                id: String(index + 1),
                start: segment.start,
                end: segment.end,
                text: segment.text,
                words: segment.words || []
            }));
            
            // 3. 保存简化字幕JSON文件
            const simplifiedFilename = `${videoIdentifier}_simplified_subtitle.json`;
            const simplifiedSubtitleJsonPath = await fileSaver.saveDataToFile(
                JSON.stringify(simplifiedSubtitleJsonArray, null, 2),
                simplifiedFilename,
                savePath,
                execLogPrefix
            );
            
            // 4. 生成SRT内容
            let srtContent = '';
            simplifiedSubtitleJsonArray.forEach((segment, index) => {
                const startTimeFormatted = this.formatTimeForSRT(segment.start);
                const endTimeFormatted = this.formatTimeForSRT(segment.end);
                
                srtContent += `${segment.id}\r\n`;
                srtContent += `${startTimeFormatted} --> ${endTimeFormatted}\r\n`;
                srtContent += `${segment.text}\r\n\r\n`;
            });
            
            // 移除末尾多余的空行
            if (srtContent.endsWith('\r\n\r\n')) {
                srtContent = srtContent.slice(0, -2);
            }

            // 5. 保存英文SRT字幕文件
            const englishSrtFilename = `${videoIdentifier}_english.srt`;
            const englishSrtPath = await fileSaver.saveDataToFile(
                srtContent,
                englishSrtFilename,
                savePath,
                execLogPrefix
            );
            logger.info(`${execLogPrefix} 英文SRT字幕文件保存成功: ${englishSrtPath}`);

            logger.info(`${this.logPrefix}[generateBasicSubtitleFiles] 基础字幕文件生成完成`);

            return {
                transcriptionJsonPath,
                simplifiedSubtitleJsonArray,
                simplifiedSubtitleJsonPath,
                optimizedEnglishSrtContent: srtContent,
                englishSrtPath: englishSrtPath // 新增：英文SRT文件路径
            };
            
        } catch (error) {
            logger.error(`${this.logPrefix}[generateBasicSubtitleFiles] 生成基础字幕文件失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * @功能概述: 格式化时间为可读格式 (MM:SS)
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * @功能概述: 格式化时间为SRT格式 (HH:MM:SS,ms)
     * @param {number} seconds - 秒数
     * @returns {string} SRT格式的时间字符串
     */
    formatTimeForSRT(seconds) {
        if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
            return '00:00:00,000';
        }
        const date = new Date(seconds * 1000);
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const sec = String(date.getUTCSeconds()).padStart(2, '0');
        const ms = String(date.getUTCMilliseconds()).padStart(3, '0');
        return `${hours}:${minutes}:${sec},${ms}`;
    }

    /** 新增调试方法 */
    enableDebugHooks() {
        // 添加任务执行前/后的调试日志
        this.processingPipeline.on('taskStart', (task) => {
            logger.debug(`${this.logPrefix}[DEBUG] 任务启动: ${task.name}`);
        });

        this.processingPipeline.on('taskComplete', (task, result) => {
            logger.debug(`${this.logPrefix}[DEBUG] 任务完成: ${task.name}`, {
                status: task.status,
                duration: task.duration
            });
        });
    }

    dumpDebugData(result) {
        const debugInfo = {
            timestamp: new Date().toISOString(),
            pipelineId: this.processingPipeline.pipelineId,
            contextSnapshot: {
                // 选择关键字段进行记录
                fileIdentifier: result.context.fileIdentifier,
                audioPath: result.context.audioFilePathInUploads,
                transcriptionStatus: result.context.transcriptionStatus,
                optimizationStatus: result.context.optimizationStatus,
                fallbackMode: result.context.fallbackMode
            },
            performanceMetrics: this.processingPipeline.getPerformanceMetrics()
        };

        logger.debug(`${this.logPrefix}[DEBUG] 调试数据转储`, debugInfo);
    }
}

// 导出服务类供其他模块使用
module.exports = VideoProcessingPipelineService;

// 记录模块导出完成的日志
logger.info(`${moduleLogPrefix}VideoProcessingPipelineService 类已导出。`); 
logger.info(`${moduleLogPrefix}[容错策略] 流水线级别降级策略已就绪，支持转录API失败时的智能降级处理`); 