/**
 * @文件名: listProjectsController.js
 * @功能概述: 获取已上传项目列表的控制器
 * @创建时间: 2025-07-16
 * @作者: Augment Agent
 * @描述: 
 *   此控制器负责扫描projects目录，获取所有有效的视频项目列表。
 *   支持分页、排序和状态过滤功能。
 *   遵循API创建指导规则，采用单一职责原则。
 */

// 导入必要的模块
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const PathHelper = require('../../utils/pathHelper');

// 导入生成文件识别器
const GeneratedFileIdentifier = require('../../utils/GeneratedFileIdentifier');

/**
 * @功能概述: 获取已上传项目列表
 * @接口路径: GET /api/video/listProjects
 * @请求参数:
 *   - page (可选): 页码，默认1
 *   - pageSize (可选): 每页数量，默认10，最大50
 *   - sortBy (可选): 排序字段，默认uploadTime
 *   - order (可选): 排序方向，默认desc
 * @响应格式: 
 *   - status: success/error
 *   - message: 响应消息
 *   - data: { projects: [], pagination: {} }
 * @错误处理: 
 *   - 目录不存在：返回空列表
 *   - 权限问题：返回错误信息
 *   - 参数错误：返回参数验证错误
 */
const listProjects = async (req, res) => {
    const logPrefix = '[文件：listProjectsController.js][listProjects]';
    
    try {
        logger.info(`${logPrefix} 开始获取项目列表`);
        
        // === 步骤1: 参数验证和默认值设置 ===
        const page = Math.max(1, parseInt(req.query.page) || 1);
        const pageSize = Math.min(50, Math.max(1, parseInt(req.query.pageSize) || 10));
        const sortBy = req.query.sortBy || 'uploadTime';
        const order = req.query.order === 'asc' ? 'asc' : 'desc';
        
        logger.info(`${logPrefix} 请求参数: page=${page}, pageSize=${pageSize}, sortBy=${sortBy}, order=${order}`);
        
        // === 步骤2: 检查projects目录是否存在 ===
        const projectsPath = PathHelper.getProjectsDir();
        logger.info(`${logPrefix} 扫描项目目录: ${projectsPath}`);
        
        if (!fs.existsSync(projectsPath)) {
            logger.warn(`${logPrefix} projects目录不存在: ${projectsPath}`);
            return res.json({
                status: 'success',
                message: '项目列表获取成功',
                data: {
                    projects: [],
                    pagination: {
                        total: 0,
                        page: page,
                        pageSize: pageSize,
                        totalPages: 0
                    }
                }
            });
        }
        
        // === 步骤3: 扫描项目目录 ===
        const projectDirs = fs.readdirSync(projectsPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);
        
        logger.info(`${logPrefix} 发现项目目录数量: ${projectDirs.length}`);
        
        // === 步骤4: 验证和收集项目信息 ===
        const validProjects = [];
        
        for (const projectId of projectDirs) {
            try {
                const projectInfo = await buildProjectInfo(projectId);
                if (projectInfo) {
                    validProjects.push(projectInfo);
                    logger.debug(`${logPrefix} 有效项目: ${projectId}`);
                } else {
                    logger.debug(`${logPrefix} 无效项目: ${projectId}`);
                }
            } catch (error) {
                logger.warn(`${logPrefix} 处理项目 ${projectId} 时出错: ${error.message}`);
            }
        }
        
        logger.info(`${logPrefix} 有效项目数量: ${validProjects.length}`);
        
        // === 步骤5: 排序 ===
        validProjects.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];
            
            // 时间字段特殊处理
            if (sortBy === 'uploadTime') {
                aValue = new Date(aValue).getTime();
                bValue = new Date(bValue).getTime();
            }
            
            if (order === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        // === 步骤6: 分页 ===
        const total = validProjects.length;
        const totalPages = Math.ceil(total / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedProjects = validProjects.slice(startIndex, endIndex);
        
        logger.info(`${logPrefix} 分页结果: 总数=${total}, 当前页=${page}, 每页=${pageSize}, 总页数=${totalPages}`);
        
        // === 步骤7: 返回结果 ===
        const response = {
            status: 'success',
            message: '项目列表获取成功',
            data: {
                projects: paginatedProjects,
                pagination: {
                    total: total,
                    page: page,
                    pageSize: pageSize,
                    totalPages: totalPages
                }
            }
        };
        
        logger.info(`${logPrefix} 项目列表获取成功，返回 ${paginatedProjects.length} 个项目`);
        res.json(response);
        
    } catch (error) {
        logger.error(`${logPrefix} 获取项目列表失败: ${error.message}`);
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            status: 'error',
            message: '获取项目列表失败',
            errorCode: 'LIST_PROJECTS_FAILED',
            details: {
                error: error.message,
                suggestion: '请检查服务器状态或联系管理员'
            }
        });
    }
};

/**
 * @功能概述: 构建单个项目的信息对象
 * @参数: projectId - 项目标识符
 * @返回: 项目信息对象或null（如果项目无效）
 * @说明: 检查项目有效性并收集基础信息
 */
const buildProjectInfo = async (projectId) => {
    const logPrefix = '[文件：listProjectsController.js][buildProjectInfo]';
    
    try {
        // 检查项目是否存在
        if (!PathHelper.projectExists(projectId)) {
            return null;
        }
        
        // 获取项目目录路径
        const projectDir = PathHelper.getProjectDir(projectId);
        const sourceDir = PathHelper.getSourceDir(projectId);
        const processedDir = PathHelper.getProcessedDir(projectId);
        const generatedDir = PathHelper.getGeneratedDir(projectId);
        
        // 查找原始视频文件
        const originalFile = findOriginalVideoFile(sourceDir);
        const hasOriginalVideo = !!originalFile;

        // 即使没有视频文件也显示项目（用于删除等管理操作）
        if (!originalFile) {
            logger.debug(`${logPrefix} 项目 ${projectId} 缺少原始视频文件，但仍显示在列表中`);
        }
        
        // 获取项目统计信息
        const projectDirStats = fs.statSync(projectDir);

        // 判断项目状态
        const projectStatus = determineProjectStatus(processedDir, generatedDir);

        // 检查是否有处理数据
        const hasProcessedData = checkHasProcessedData(processedDir);

        // 统计生成的视频数量
        const generatedVideoCount = countGeneratedVideos(generatedDir);

        // 统计已发布的视频数量
        const publishedVideoCount = countPublishedVideos(generatedDir);

        // 构建项目信息
        let projectInfo = {
            videoIdentifier: projectId,
            originalVideoName: hasOriginalVideo ? (originalFile.originalName || originalFile.name) : '无视频文件',
            uploadTime: projectDirStats.birthtime.toISOString(),
            hasProcessedData: hasProcessedData,
            projectStatus: projectStatus,
            lastModified: projectDirStats.mtime.toISOString(),
            hasOriginalVideo: hasOriginalVideo === true, // 确保返回布尔值
            generatedVideoCount: generatedVideoCount, // 🆕 添加生成视频数量
            publishedVideoCount: publishedVideoCount // 🆕 添加已发布视频数量
        };

        // 如果有视频文件，添加视频相关信息
        if (hasOriginalVideo) {
            const originalFilePath = path.join(sourceDir, originalFile.name);
            const fileStats = fs.statSync(originalFilePath);

            // 生成文件URLs
            const files = {
                source: { [originalFile.name]: originalFile.name }
            };
            const fileUrls = PathHelper.generateFileUrls(projectId, files);

            projectInfo.videoPlaybackUrl = fileUrls.source[originalFile.name];
            projectInfo.fileSize = formatFileSize(fileStats.size);
            projectInfo.uploadTime = fileStats.birthtime.toISOString(); // 使用文件时间而不是目录时间
        } else {
            projectInfo.videoPlaybackUrl = null;
            projectInfo.fileSize = '0 B';
        }
        
        return projectInfo;
        
    } catch (error) {
        logger.error(`${logPrefix} 构建项目信息失败 ${projectId}: ${error.message}`);
        return null;
    }
};

/**
 * @功能概述: 查找原始视频文件
 * @参数: sourceDir - source目录路径
 * @返回: 文件信息对象或null
 * @说明: 支持新的文件名保留功能，任何视频文件都被认为是有效的原始文件
 */
const findOriginalVideoFile = (sourceDir) => {
    try {
        if (!fs.existsSync(sourceDir)) {
            return null;
        }

        const files = fs.readdirSync(sourceDir);
        const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'];

        for (const file of files) {
            const ext = path.extname(file).toLowerCase();
            if (videoExtensions.includes(ext)) {
                return {
                    name: file,
                    originalName: file // 所有视频文件都被认为是原始文件名
                };
            }
        }

        return null;
    } catch (error) {
        return null;
    }
};

/**
 * @功能概述: 判断项目状态
 * @参数: processedDir, generatedDir - 处理和生成目录路径
 * @返回: 项目状态字符串
 */
const determineProjectStatus = (processedDir, generatedDir) => {
    try {
        const hasProcessedFiles = fs.existsSync(processedDir) && fs.readdirSync(processedDir).length > 0;
        const hasGeneratedFiles = fs.existsSync(generatedDir) && fs.readdirSync(generatedDir).length > 0;
        
        if (hasGeneratedFiles) {
            return 'completed';
        } else if (hasProcessedFiles) {
            return 'processed';
        } else {
            return 'uploaded';
        }
    } catch (error) {
        return 'unknown';
    }
};

/**
 * @功能概述: 检查是否有处理数据
 * @参数: processedDir - 处理目录路径
 * @返回: boolean
 */
const checkHasProcessedData = (processedDir) => {
    try {
        if (!fs.existsSync(processedDir)) {
            return false;
        }
        
        const files = fs.readdirSync(processedDir);
        const requiredFiles = ['.mp3', '.srt', '.json']; // 音频、字幕、转录文件
        
        return requiredFiles.some(ext => 
            files.some(file => file.toLowerCase().endsWith(ext))
        );
    } catch (error) {
        return false;
    }
};

/**
 * @功能概述: 格式化文件大小
 * @参数: bytes - 字节数
 * @返回: 格式化的文件大小字符串
 */
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * @功能概述: 统计生成的视频数量
 * @参数: generatedDir - generated目录路径
 * @返回: 生成视频的数量
 * @说明: 使用GeneratedFileIdentifier扫描generated目录，统计extended_video类型的文件数量
 */
const countGeneratedVideos = (generatedDir) => {
    try {
        if (!fs.existsSync(generatedDir)) {
            return 0;
        }

        // 使用GeneratedFileIdentifier扫描目录
        const scanResult = GeneratedFileIdentifier.scanGeneratedDirectory(generatedDir);

        // 返回视频文件数量
        return scanResult.videos.length;

    } catch (error) {
        logger.warn(`[countGeneratedVideos] 统计生成视频数量失败: ${error.message}`);
        return 0;
    }
};

/**
 * @功能概述: 统计已发布的视频数量
 * @参数: generatedDir - generated目录路径
 * @返回: 已发布视频的数量
 * @说明: 读取generated目录下的tags.json文件，统计status为"published"的视频数量
 */
const countPublishedVideos = (generatedDir) => {
    try {
        if (!fs.existsSync(generatedDir)) {
            return 0;
        }

        // 构建tags.json文件路径
        const tagsFilePath = path.join(generatedDir, 'tags.json');

        if (!fs.existsSync(tagsFilePath)) {
            return 0;
        }

        // 读取并解析tags.json文件
        const content = fs.readFileSync(tagsFilePath, 'utf8');
        const tagsData = JSON.parse(content);

        // 统计status为"published"的视频数量
        let publishedCount = 0;
        for (const videoName in tagsData) {
            if (tagsData[videoName] && tagsData[videoName].status === 'published') {
                publishedCount++;
            }
        }

        return publishedCount;

    } catch (error) {
        logger.warn(`[countPublishedVideos] 统计已发布视频数量失败: ${error.message}`);
        return 0;
    }
};

// 导出控制器函数
module.exports = {
    listProjects
};
