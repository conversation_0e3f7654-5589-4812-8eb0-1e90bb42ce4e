/**
 * @fileoverview VideoCompositionTask配置使用示例
 * @description 演示如何使用shared/types/composition.js的配置传递给VideoCompositionTask
 * @version 1.0.0
 * <AUTHOR>
 * @created 2025-01-07
 */

const VideoCompositionTask = require('../tasks/VideoCompositionTask');
const { convertToVideoCompositionConfig, createHardcodedTestConfig } = require('../utils/compositionConfigConverter');
const logger = require('../utils/logger');
const path = require('path');

// 示例日志前缀
const exampleLogPrefix = '[文件：videoCompositionConfigExample.js][配置使用示例]';

/**
 * 示例1: 使用shared/types/composition.js的DEFAULT_COMPOSITION_CONFIG
 */
function example1_DefaultConfig() {
    logger.info(`${exampleLogPrefix} ========== 示例1: 默认配置使用 ==========`);
    
    // 模拟从前端传来的配置（对应shared/types/composition.js的DEFAULT_COMPOSITION_CONFIG）
    const frontendCompositionConfig = {
        repeatCount: 3,
        segments: [
            {
                contentType: 'video',
                backgroundType: 'none'
            },
            {
                contentType: 'english',
                backgroundType: 'dark'
            },
            {
                contentType: 'chinese',
                backgroundType: 'blue'
            }
        ],
        assStyleParams: {
            fontSize: 32,
            fontName: 'Arial',
            primaryColor: '#FFFFFF',
            secondaryColor: '#000000',
            outlineColor: '#000000',
            backColor: '#80000000',
            bold: false,
            highlightWords: {
                enabled: false,
                color: '#FF0000',
                words: []
            }
        },
        outputParams: {
            aspectRatio: '9:16',
            quality: 'high',
            format: 'mp4',
            maxDuration: 300
        }
    };

    // 转换为VideoCompositionTask期望的格式
    const videoCompositionConfig = convertToVideoCompositionConfig(frontendCompositionConfig);
    
    logger.info(`${exampleLogPrefix} 前端配置: ${JSON.stringify(frontendCompositionConfig, null, 2)}`);
    logger.info(`${exampleLogPrefix} 转换后配置: ${JSON.stringify(videoCompositionConfig, null, 2)}`);
    
    return videoCompositionConfig;
}

/**
 * 示例2: 硬编码传递给VideoCompositionTask
 */
async function example2_HardcodedUsage() {
    logger.info(`${exampleLogPrefix} ========== 示例2: 硬编码使用 ==========`);
    
    try {
        // 创建VideoCompositionTask实例
        const task = new VideoCompositionTask();
        
        // 硬编码的测试上下文
        const testDataDir = "C:\\Users\\<USER>\\Desktop\\代码库\\express\\backend\\uploads\\test-data";
        const baseFileName = "videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z";
        
        const context = {
            // 基础信息
            videoIdentifier: 'config_example_test',
            reqId: 'config-example-001',
            
            // 文件路径
            processedVideoPath: path.join(testDataDir, `${baseFileName}.mp4`),
            processedAudioPath: path.join(testDataDir, `videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-_744348a8_audio.mp3`),
            assSubtitlePath: path.join(testDataDir, `test_123_subtitle.ass`),
            savePath: testDataDir,
            
            // 使用转换后的配置
            compositionConfig: createHardcodedTestConfig('high_quality') // 高质量配置
        };
        
        logger.info(`${exampleLogPrefix} 硬编码上下文: ${JSON.stringify(context, null, 2)}`);
        
        // 验证配置解析
        const parsedConfig = task.parseCompositionConfig(context.compositionConfig, exampleLogPrefix);
        logger.info(`${exampleLogPrefix} 解析后的配置: ${JSON.stringify(parsedConfig, null, 2)}`);
        
        return { context, parsedConfig };
        
    } catch (error) {
        logger.error(`${exampleLogPrefix} 示例2执行失败: ${error.message}`);
        throw error;
    }
}

/**
 * 示例3: 不同场景的配置对比
 */
function example3_ScenarioComparison() {
    logger.info(`${exampleLogPrefix} ========== 示例3: 不同场景配置对比 ==========`);
    
    const scenarios = ['default', 'fast', 'high_quality', 'custom'];
    const configs = {};
    
    scenarios.forEach(scenario => {
        configs[scenario] = createHardcodedTestConfig(scenario);
        logger.info(`${exampleLogPrefix} ${scenario}场景配置:`);
        logger.info(`${exampleLogPrefix}   - 音频重复: ${configs[scenario].audioRepeatCount}次`);
        logger.info(`${exampleLogPrefix}   - 输出尺寸: ${configs[scenario].outputWidth}x${configs[scenario].outputHeight}`);
        logger.info(`${exampleLogPrefix}   - 视频质量: CRF=${configs[scenario].crf}, 预设=${configs[scenario].preset}`);
        logger.info(`${exampleLogPrefix}   - 音频比特率: ${configs[scenario].audioBitrate}`);
        logger.info(`${exampleLogPrefix}   - 背景颜色: ${configs[scenario].backgroundColor}`);
        logger.info(`${exampleLogPrefix}   - 帧率: ${configs[scenario].frameRate}fps`);
    });
    
    return configs;
}

/**
 * 示例4: 自定义配置创建
 */
function example4_CustomConfig() {
    logger.info(`${exampleLogPrefix} ========== 示例4: 自定义配置创建 ==========`);
    
    // 完全自定义的前端配置
    const customFrontendConfig = {
        repeatCount: 2,
        segments: [
            {
                contentType: 'video',
                backgroundType: 'purple'
            }
        ],
        outputParams: {
            aspectRatio: '1:1',  // 正方形视频
            quality: 'medium',
            format: 'mp4',
            maxDuration: 180
        }
    };
    
    // 自定义选项
    const customOptions = {
        fastMode: false,
        customFrameRate: 24  // 电影级帧率
    };
    
    const customConfig = convertToVideoCompositionConfig(customFrontendConfig, customOptions);
    
    logger.info(`${exampleLogPrefix} 自定义前端配置: ${JSON.stringify(customFrontendConfig, null, 2)}`);
    logger.info(`${exampleLogPrefix} 自定义选项: ${JSON.stringify(customOptions, null, 2)}`);
    logger.info(`${exampleLogPrefix} 最终配置: ${JSON.stringify(customConfig, null, 2)}`);
    
    return customConfig;
}

/**
 * 示例5: 实际使用场景 - 创建完整的任务上下文
 */
function example5_CompleteTaskContext() {
    logger.info(`${exampleLogPrefix} ========== 示例5: 完整任务上下文 ==========`);
    
    const testDataDir = "C:\\Users\\<USER>\\Desktop\\代码库\\express\\backend\\uploads\\test-data";
    const baseFileName = "videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-625Z";
    
    // 模拟从前端API接收到的完整配置
    const apiRequest = {
        videoIdentifier: 'user_video_123',
        composition: {
            repeatCount: 3,
            segments: [
                { contentType: 'video', backgroundType: 'none' },
                { contentType: 'english', backgroundType: 'dark' },
                { contentType: 'chinese', backgroundType: 'blue' }
            ],
            assStyleParams: {
                fontSize: 36,
                fontName: 'Arial',
                primaryColor: '#FFFFFF',
                highlightWords: {
                    enabled: true,
                    color: '#FFD700',
                    words: ['important', 'key', 'highlight']
                }
            },
            outputParams: {
                aspectRatio: '9:16',
                quality: 'high',
                format: 'mp4',
                maxDuration: 300
            }
        }
    };
    
    // 转换配置
    const compositionConfig = convertToVideoCompositionConfig(apiRequest.composition);
    
    // 创建完整的任务上下文
    const completeContext = {
        // 基础信息
        videoIdentifier: apiRequest.videoIdentifier,
        reqId: `req-${Date.now()}`,
        
        // 文件路径
        processedVideoPath: path.join(testDataDir, `${baseFileName}.mp4`),
        processedAudioPath: path.join(testDataDir, `videoFile-1749090956973-678162462_processed_2025-06-06_09-40-52-_744348a8_audio.mp3`),
        assSubtitlePath: path.join(testDataDir, `test_123_subtitle.ass`),
        savePath: testDataDir,
        
        // 转换后的合成配置
        compositionConfig: compositionConfig,
        
        // ASS样式参数（传递给AssSubtitleGenerationTask）
        assStyleParams: apiRequest.composition.assStyleParams,
        
        // 其他可能需要的参数
        metadata: {
            userAgent: 'VideoComposition/1.0',
            timestamp: new Date().toISOString(),
            source: 'api_request'
        }
    };
    
    logger.info(`${exampleLogPrefix} API请求: ${JSON.stringify(apiRequest, null, 2)}`);
    logger.info(`${exampleLogPrefix} 完整任务上下文: ${JSON.stringify(completeContext, null, 2)}`);
    
    return completeContext;
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
    logger.info(`${exampleLogPrefix} ========== 开始运行所有配置示例 ==========`);
    
    try {
        // 示例1: 默认配置
        const defaultConfig = example1_DefaultConfig();
        
        // 示例2: 硬编码使用
        const { context, parsedConfig } = await example2_HardcodedUsage();
        
        // 示例3: 场景对比
        const scenarioConfigs = example3_ScenarioComparison();
        
        // 示例4: 自定义配置
        const customConfig = example4_CustomConfig();
        
        // 示例5: 完整上下文
        const completeContext = example5_CompleteTaskContext();
        
        logger.info(`${exampleLogPrefix} ========== 所有示例运行完成 ==========`);
        
        return {
            defaultConfig,
            hardcodedExample: { context, parsedConfig },
            scenarioConfigs,
            customConfig,
            completeContext
        };
        
    } catch (error) {
        logger.error(`${exampleLogPrefix} 示例运行失败: ${error.message}`);
        throw error;
    }
}

// 如果直接运行此文件，则执行所有示例
if (require.main === module) {
    runAllExamples().then(results => {
        logger.info(`${exampleLogPrefix} ✅ 所有配置示例运行成功`);
        process.exit(0);
    }).catch(error => {
        logger.error(`${exampleLogPrefix} ❌ 配置示例运行失败: ${error.message}`);
        process.exit(1);
    });
}

module.exports = {
    example1_DefaultConfig,
    example2_HardcodedUsage,
    example3_ScenarioComparison,
    example4_CustomConfig,
    example5_CompleteTaskContext,
    runAllExamples
};
