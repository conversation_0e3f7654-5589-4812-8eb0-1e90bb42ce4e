/**
 * @文件名: GenerateVideoTask.test.js
 * @功能概述: GenerateVideoTask任务类的独立测试脚本
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 *
 * @功能描述:
 *   测试GenerateVideoTask任务类的完整功能，包括音频重复拼接、
 *   Canvas进度条生成、背景视频生成、ASS字幕烧录和9:16视频合成。
 *   使用真实的上下文数据进行端到端测试。
 *
 * @测试范围:
 *   - 任务参数验证
 *   - 音频重复拼接处理
 *   - Canvas进度条视频生成
 *   - Canvas背景视频生成
 *   - ASS字幕文件写入和验证
 *   - 9:16视频合成和输出
 *
 * @执行方式: node backend/src/tasks/tests/GenerateVideoTask.test.js
 */

const GenerateVideoTask = require('../GenerateVideoTask');
const logger = require('../../utils/logger');

// 测试配置 - 参考processing.test.js的上下文
const TEST_CONFIG = {
    videoIdentifier: 'test_0613',
    savePath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/output',
    audioFilePath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/input/test_0612_audio.mp3',
    // 硬编码原视频路径
    originalVideoPath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/input/test_0612.mp4',
    // ASS文件路径
    assFilePath: 'C:/Users/<USER>/Desktop/codebase/express/backend/uploads/input/test_0613_extended_ass.ass'
};

/**
 * @功能概述: 读取ASS文件内容
 * @返回值: {Promise<string>} ASS文件内容
 */
async function readAssContent() {
    const fs = require('fs').promises;
    try {
        const assContent = await fs.readFile(TEST_CONFIG.assFilePath, 'utf8');
        log(`✅ ASS文件读取成功，内容长度: ${assContent.length} 字符`, 'INFO', 'readAssContent');
        return assContent;
    } catch (error) {
        log(`❌ ASS文件读取失败: ${error.message}`, 'ERROR', 'readAssContent');
        throw error;
    }
}

// 模拟从GenerateASSTask获得的上下文数据
async function createMockContext() {
    const assContent = await readAssContent();

    return {
        // 基础信息
        videoIdentifier: TEST_CONFIG.videoIdentifier,
        savePath: TEST_CONFIG.savePath,
        audioFilePath: TEST_CONFIG.audioFilePath,
        originalVideoPath: TEST_CONFIG.originalVideoPath,

        // 从GenerateASSTask获得的数据
        audioDuration: 22.212, // 音频时长（秒）
        assContent: assContent

        // 注意: videoConfig现在从video-config.json文件读取，不再从上下文获取
    };
}

/**
 * @功能概述: 标准化日志输出函数
 * @参数说明: 
 *   - message: {string} 日志消息内容
 *   - level: {string} 日志级别 (INFO, ERROR, WARN, DEBUG)
 *   - functionName: {string} [可选] 调用函数名
 */
function log(message, level = 'INFO', functionName = 'general') {
    const timestamp = new Date().toISOString();
    const logPrefix = `[文件：GenerateVideoTask.test.js][视频生成任务测试][${functionName}]`;
    console.log(`${timestamp} [${level}] ${logPrefix} ${message}`);
}



/**
 * @功能概述: 主测试函数，执行GenerateVideoTask的完整测试流程
 * @执行流程:
 *   0. 创建测试视频文件
 *   1. 创建GenerateVideoTask实例
 *   2. 准备测试上下文数据
 *   3. 执行视频生成任务
 *   4. 验证输出结果
 * @错误处理:
 *   - 任何步骤失败都会终止测试流程
 *   - 记录详细错误信息并以错误码退出
 */
async function main() {
    const functionName = 'main';

    try {
        log('========== 开始GenerateVideoTask测试 ==========', 'INFO', functionName);

        // 步骤 1: 创建GenerateVideoTask实例
        log('步骤1: 创建GenerateVideoTask实例', 'INFO', functionName);
        const generateVideoTask = new GenerateVideoTask();
        log(`任务实例创建成功，任务名: ${generateVideoTask.taskName}`, 'INFO', functionName);

        // 步骤 2: 准备测试上下文数据
        log('步骤2: 准备测试上下文数据', 'INFO', functionName);
        const testContext = await createMockContext();
        log(`测试上下文准备完成，包含字段: ${Object.keys(testContext).join(', ')}`, 'INFO', functionName);
        log(`视频标识符: ${testContext.videoIdentifier}`, 'INFO', functionName);
        log(`音频时长: ${testContext.audioDuration} 秒`, 'INFO', functionName);
        log(`注意: videoConfig将从video-config.json文件读取`, 'INFO', functionName);

        // 步骤 3: 设置进度回调函数
        log('步骤3: 设置进度回调函数', 'INFO', functionName);
        const progressCallback = (progress) => {
            try {
                log(`进度回调: ${progress.status}/${progress.subStatus} - ${progress.progress.detail} (${progress.progress.current}/${progress.progress.total})`, 'INFO', functionName);
            } catch (error) {
                log(`进度回调处理异常: ${error.message}`, 'ERROR', functionName);
            }
        };

        // 步骤 4: 执行GenerateVideoTask任务
        log('步骤4: 执行GenerateVideoTask任务', 'INFO', functionName);
        const startTime = Date.now();
        
        const taskResult = await generateVideoTask.execute(testContext, progressCallback);
        
        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000;

        // 步骤 5: 验证任务结果
        log('步骤5: 验证任务结果', 'INFO', functionName);
        log(`任务执行完成，耗时: ${executionTime.toFixed(2)} 秒`, 'INFO', functionName);
        log(`任务状态: ${taskResult.taskStatus}`, 'INFO', functionName);
        log(`任务结果: ${taskResult.taskResult}`, 'INFO', functionName);

        // 步骤 6: 验证输出文件
        log('步骤6: 验证输出文件', 'INFO', functionName);
        const fs = require('fs').promises;
        
        // 验证最终视频文件
        if (taskResult.finalVideoPath) {
            try {
                await fs.access(taskResult.finalVideoPath);
                const stats = await fs.stat(taskResult.finalVideoPath);
                const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
                log(`✅ 最终视频文件存在: ${taskResult.finalVideoPath}`, 'INFO', functionName);
                log(`✅ 最终视频文件大小: ${fileSizeMB}MB`, 'INFO', functionName);
            } catch (error) {
                log(`❌ 最终视频文件不存在: ${taskResult.finalVideoPath}`, 'ERROR', functionName);
            }
        }

        // 验证扩展音频文件
        if (taskResult.extendedAudioPath) {
            try {
                await fs.access(taskResult.extendedAudioPath);
                const stats = await fs.stat(taskResult.extendedAudioPath);
                const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
                log(`✅ 扩展音频文件存在: ${taskResult.extendedAudioPath}`, 'INFO', functionName);
                log(`✅ 扩展音频文件大小: ${fileSizeMB}MB`, 'INFO', functionName);
            } catch (error) {
                log(`❌ 扩展音频文件不存在: ${taskResult.extendedAudioPath}`, 'ERROR', functionName);
            }
        }

        // 注意：ASS字幕现在直接使用assContent，不再生成独立文件

        log('========== GenerateVideoTask测试完成 ==========', 'INFO', functionName);
        log(`✅ 测试成功完成，总耗时: ${executionTime.toFixed(2)} 秒`, 'INFO', functionName);
        log(`✅ 最终视频文件: ${taskResult.finalVideoPath}`, 'INFO', functionName);
        log(`✅ 视频生成统计: ${JSON.stringify(taskResult.videoGenerationStats)}`, 'INFO', functionName);

        process.exit(0); // 成功退出

    } catch (error) {
        log(`❌ GenerateVideoTask测试失败: ${error.message}`, 'ERROR', functionName);
        console.error('错误详情:', error);
        process.exit(1); // 错误退出
    }
}

// 立即执行主函数
log('GenerateVideoTask测试脚本启动', 'INFO', '脚本启动');
main().catch(error => {
    console.error('脚本执行失败:', error);
    log(`脚本执行失败: ${error.message}`, 'ERROR', '脚本启动');
    process.exit(1);
});
