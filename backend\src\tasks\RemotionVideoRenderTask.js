/**
 * @功能概述: Remotion视频渲染任务，使用Remotion API创建复杂视频布局和动画效果
 * @输入依赖: context需包含processedAudioPath、durationInFrames、subtitleData、videoConfig字段
 * @输出结果: 向context添加finalVideoPath、renderStats字段
 * @外部依赖: Remotion渲染引擎、Node.js环境
 * @失败策略: 渲染失败时抛出详细错误信息
 */

const TaskBase = require('../class/TaskBase');
const { bundle } = require('@remotion/bundler');
const { renderMedia, selectComposition } = require('@remotion/renderer');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');
const VideoConfigManager = require('../config/converters/VideoConfigManager');

class RemotionVideoRenderTask extends TaskBase {
    constructor() {
        super('RemotionVideoRenderTask', 'Remotion视频渲染任务');
        this.description = '使用Remotion引擎渲染复杂视频布局和动画效果';
    }

    /**
     * @功能概述: 执行Remotion视频渲染任务
     * @param {Object} context - 任务上下文
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Object} 渲染结果
     * @执行流程:
     *   1. 验证输入参数和依赖
     *   2. 准备Remotion配置
     *   3. 创建Remotion组件
     *   4. 执行视频渲染
     *   5. 返回渲染结果
     */
    async execute(context, progressCallback) {
        const reqId = context.reqId || 'unknown';
        const logPrefix = `[文件：RemotionVideoRenderTask.js][Remotion视频渲染任务][execute][ReqID:${reqId}] `;
        
        logger.info(`${logPrefix}[步骤 1] 开始Remotion视频渲染任务`);
        this.status = 'running';
        
        // 报告任务开始
        if (progressCallback) progressCallback({
            taskName: this.name,
            status: 'started',
            detail: '开始Remotion视频渲染'
        });

        try {
            // 步骤 1: 验证输入参数
            const { 
                processedAudioPath, 
                durationInFrames, 
                subtitleData = {}, 
                videoConfig,
                savePath 
            } = context;
            
            if (!processedAudioPath || !durationInFrames || !videoConfig || !savePath) {
                throw new Error('缺少必需的参数: processedAudioPath, durationInFrames, videoConfig, savePath');
            }
            
            logger.debug(`${logPrefix}[步骤 1] 输入验证通过 - 音频: ${processedAudioPath}, 帧数: ${durationInFrames}`);

            // 步骤 2: 准备Remotion配置
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'preparing_config',
                detail: '准备Remotion渲染配置'
            });
            
            const remotionConfig = await this._prepareRemotionConfig(videoConfig, context, logPrefix);
            logger.info(`${logPrefix}[步骤 2] Remotion配置准备完成`);

            // 步骤 3: 创建Remotion组件和打包
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'bundling',
                detail: '打包Remotion组件'
            });
            
            const bundleLocation = await this._createAndBundleComponent(remotionConfig, context, logPrefix);
            logger.info(`${logPrefix}[步骤 3] 组件打包完成: ${bundleLocation}`);

            // 步骤 4: 执行视频渲染
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'rendering',
                detail: 'Remotion视频渲染进行中'
            });
            
            const renderResult = await this._renderVideo(bundleLocation, remotionConfig, context, progressCallback, logPrefix);
            logger.info(`${logPrefix}[步骤 4] 视频渲染完成: ${renderResult.outputPath}`);

            // 步骤 5: 构建返回结果
            this.status = 'completed';
            const result = {
                finalVideoPath: renderResult.outputPath,
                renderStats: {
                    durationInFrames,
                    renderTime: renderResult.renderTime,
                    outputSize: renderResult.outputSize,
                    composition: remotionConfig.composition
                },
                remotionConfig
            };
            this.result = result;
            
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'completed',
                result: this.result,
                detail: 'Remotion视频渲染任务完成'
            });
            
            logger.info(`${logPrefix}[SUCCESS] Remotion视频渲染任务完成`);
            return result;
            
        } catch (error) {
            logger.error(`${logPrefix}[ERROR] Remotion视频渲染失败: ${error.message}`);
            this.status = 'failed';
            this.error = error;
            
            if (progressCallback) progressCallback({
                taskName: this.name,
                status: 'failed_remotion_render',
                error: { message: error.message, name: error.name },
                detail: `Remotion视频渲染失败: ${error.message}`
            });
            
            throw error;
        }
    }

    /**
     * @功能概述: 准备Remotion配置
     * @param {Object} videoConfig - 视频配置
     * @param {Object} context - 任务上下文
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<Object>} Remotion配置对象
     */
    async _prepareRemotionConfig(videoConfig, context, logPrefix) {
        logger.debug(`${logPrefix}[步骤 2.1] 开始准备Remotion配置`);
        
        try {
            // 转换为Remotion配置格式
            const remotionConfig = VideoConfigManager.getRemotionConfig(videoConfig);
            logger.debug(`${logPrefix}[步骤 2.1] 配置转换为Remotion格式完成`);
            
            // 更新动态参数
            remotionConfig.composition.durationInFrames = context.durationInFrames;
            remotionConfig.composition.defaultProps = {
                audioPath: context.processedAudioPath,
                subtitleData: context.subtitleData || {},
                clozedSubtitleJsonArray: context.clozedSubtitleJsonArray || [],
                enhancedBilingualSubtitleJsonArray: context.enhancedBilingualSubtitleJsonArray || [],
                originalVideoPath: context.originalVideoPath,
                videoIdentifier: context.videoIdentifier || 'unknown'
            };
            
            logger.debug(`${logPrefix}[步骤 2.1] Remotion配置准备完成`);
            return remotionConfig;
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 2.1][ERROR] Remotion配置准备失败: ${error.message}`);
            throw new Error(`Remotion配置准备失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 创建并打包Remotion组件
     * @param {Object} remotionConfig - Remotion配置
     * @param {Object} context - 任务上下文
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<string>} 打包文件路径
     */
    async _createAndBundleComponent(remotionConfig, context, logPrefix) {
        logger.debug(`${logPrefix}[步骤 3.1] 开始创建和打包Remotion组件`);
        
        try {
            // 创建临时组件文件
            const componentPath = await this._createVideoComponent(remotionConfig, context, logPrefix);
            
            // 打包组件
            const publicDir = path.join(__dirname, '../remotion/public');
            const bundleLocation = await bundle(componentPath, () => undefined, {
                // 打包选项
                publicDir: publicDir,
                webpackOverride: (config) => {
                    // 自定义webpack配置
                    return {
                        ...config,
                        resolve: {
                            ...config.resolve,
                            alias: {
                                ...config.resolve.alias,
                                // 添加路径别名
                            }
                        }
                    };
                }
            });
            
            logger.debug(`${logPrefix}[步骤 3.1] 组件打包完成: ${bundleLocation}`);
            return bundleLocation;
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 3.1][ERROR] 组件打包失败: ${error.message}`);
            throw new Error(`组件打包失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 创建Remotion视频组件
     * @param {Object} remotionConfig - Remotion配置
     * @param {Object} context - 任务上下文
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<string>} 组件文件路径
     */
    async _createVideoComponent(remotionConfig, context, logPrefix) {
        logger.debug(`${logPrefix}[步骤 3.2] 开始创建视频组件`);

        try {
            // 步骤 3.2.1: 准备音频文件到public目录
            const publicDir = path.join(__dirname, '../remotion/public');
            await fs.mkdir(publicDir, { recursive: true });

            const audioFileName = path.basename(context.processedAudioPath);
            const publicAudioPath = path.join(publicDir, audioFileName);

            // 复制音频文件到public目录
            await fs.copyFile(context.processedAudioPath, publicAudioPath);
            logger.debug(`${logPrefix}[步骤 3.2.1] 音频文件已复制到: ${publicAudioPath}`);

            // 步骤 3.2.2: 创建组件目录和文件
            const componentDir = path.join(__dirname, '../remotion/temp');
            await fs.mkdir(componentDir, { recursive: true });

            const componentPath = path.join(componentDir, `VideoComponent_${context.reqId || Date.now()}.jsx`);

            // 生成React组件代码，传递相对音频路径
            const componentCode = this._generateComponentCode(remotionConfig, context, audioFileName);

            // 写入组件文件
            await fs.writeFile(componentPath, componentCode, 'utf8');

            logger.debug(`${logPrefix}[步骤 3.2] 视频组件创建完成: ${componentPath}`);
            return componentPath;
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 3.2][ERROR] 视频组件创建失败: ${error.message}`);
            throw new Error(`视频组件创建失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 生成Remotion组件代码
     * @param {Object} remotionConfig - Remotion配置
     * @param {Object} context - 任务上下文
     * @param {string} audioFileName - 音频文件名（相对于public目录）
     * @returns {string} 组件代码
     */
    _generateComponentCode(remotionConfig, context, audioFileName) {
        const { composition } = remotionConfig;
        const { clozedSubtitleJsonArray = [], enhancedBilingualSubtitleJsonArray = [] } = context;

        // 将字幕数据转换为字符串，以便在组件中使用
        const clozedSubtitlesStr = JSON.stringify(clozedSubtitleJsonArray);
        const bilingualSubtitlesStr = JSON.stringify(enhancedBilingualSubtitleJsonArray);

        return `
import React from 'react';
import {
    Composition,
    registerRoot,
    useCurrentFrame,
    useVideoConfig,
    interpolate,
    AbsoluteFill,
    Audio,
    Sequence,
    staticFile
} from 'remotion';

// 字幕数据
const clozedSubtitles = ${clozedSubtitlesStr};
const bilingualSubtitles = ${bilingualSubtitlesStr};

// 主视频组件
const VideoComposition = () => {
    const frame = useCurrentFrame();
    const { fps, durationInFrames, width, height } = useVideoConfig();
    const currentTime = frame / fps;

    // 背景组件 - 模拟Canvas生成的报纸背景
    const Background = () => (
        <AbsoluteFill style={{
            backgroundColor: '#1a1a2e',
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="newspaper" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"%3E%3Crect width="20" height="20" fill="%23f0f0f0"/%3E%3Cline x1="0" y1="5" x2="20" y2="5" stroke="%23ddd" stroke-width="0.5"/%3E%3Cline x1="0" y1="10" x2="20" y2="10" stroke="%23ddd" stroke-width="0.5"/%3E%3Cline x1="0" y1="15" x2="20" y2="15" stroke="%23ddd" stroke-width="0.5"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100" height="100" fill="url(%23newspaper)"/%3E%3C/svg%3E")',
            backgroundSize: '100px 100px',
            backgroundPosition: 'center'
        }}>
            {/* 80%黑色遮罩 */}
            <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(0,0,0,0.8)'
            }} />
        </AbsoluteFill>
    );

    // 原视频区域组件 - 模拟原视频在上半部分显示
    const OriginalVideoArea = () => {
        const maxOriginalVideoDuration = currentTime <= 3 ? 1 : 0; // 简化：前3秒显示原视频区域

        if (maxOriginalVideoDuration === 0) return null;

        return (
            <div style={{
                position: 'absolute',
                top: '10%',
                left: '10%',
                width: '80%',
                height: '40%',
                backgroundColor: 'rgba(50,50,50,0.9)',
                border: '2px solid #666',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#fff',
                fontSize: '24px',
                fontFamily: 'Arial'
            }}>
                原视频播放区域
            </div>
        );
    };

    // 文本区域组件 - 模拟字幕显示区域
    const TextArea = () => {
        const showTextArea = currentTime > 3; // 3秒后显示文本区域

        if (!showTextArea) return null;

        return (
            <div style={{
                position: 'absolute',
                top: '55%',
                left: '5%',
                width: '90%',
                height: '35%',
                backgroundColor: 'rgba(128,128,128,0.9)',
                borderRadius: '8px',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center'
            }}>
                {/* 这里会被字幕组件覆盖 */}
            </div>
        );
    };

    // 进度条组件 - 模拟Canvas生成的进度条效果
    const ProgressBar = () => {
        const progress = interpolate(frame, [0, durationInFrames], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp'
        });

        const progressBarHeight = Math.round(width * 0.05); // 5%的宽度作为进度条高度
        const progressBarY = Math.round(height * 0.75); // 75%位置

        return (
            <div style={{
                position: 'absolute',
                left: '0px',
                top: progressBarY + 'px',
                width: width + 'px',
                height: progressBarHeight + 'px',
                backgroundColor: 'rgba(0,0,0,0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start'
            }}>
                <div style={{
                    width: progress * width + 'px',
                    height: '100%',
                    backgroundColor: '#FFFF00',
                    borderRadius: '0px'
                }} />
            </div>
        );
    };

    // 字幕组件 - 模拟ASS字幕烧录效果
    const Subtitles = () => {
        // 查找当前时间对应的字幕
        const currentSubtitle = bilingualSubtitles.find(sub =>
            currentTime >= sub.start && currentTime <= sub.end
        );

        if (!currentSubtitle) return null;

        const showInTextArea = currentTime > 3; // 3秒后在文本区域显示

        return (
            <div style={{
                position: 'absolute',
                top: showInTextArea ? '60%' : '70%',
                left: '5%',
                right: '5%',
                textAlign: 'center',
                zIndex: 10
            }}>
                {/* 英文字幕 */}
                <div style={{
                    color: '#FFFFFF',
                    fontSize: showInTextArea ? '28px' : '36px',
                    fontFamily: 'Arial',
                    fontWeight: 'bold',
                    marginBottom: '8px',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                    backgroundColor: showInTextArea ? 'transparent' : 'rgba(0,0,0,0.7)',
                    padding: showInTextArea ? '0' : '8px 16px',
                    borderRadius: showInTextArea ? '0' : '4px'
                }}>
                    {currentSubtitle.english}
                </div>
                {/* 中文字幕 */}
                <div style={{
                    color: '#FFFF00',
                    fontSize: showInTextArea ? '24px' : '32px',
                    fontFamily: 'Arial',
                    fontWeight: 'normal',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                    backgroundColor: showInTextArea ? 'transparent' : 'rgba(0,0,0,0.7)',
                    padding: showInTextArea ? '0' : '6px 12px',
                    borderRadius: showInTextArea ? '0' : '4px'
                }}>
                    {currentSubtitle.chinese}
                </div>
                {/* 关键词高亮 */}
                {currentSubtitle.keywords && currentSubtitle.keywords.length > 0 && (
                    <div style={{
                        color: '#00FFFF',
                        fontSize: showInTextArea ? '20px' : '24px',
                        fontFamily: 'Arial',
                        fontWeight: 'normal',
                        marginTop: '8px',
                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)'
                    }}>
                        关键词: {currentSubtitle.keywords.join(', ')}
                    </div>
                )}
            </div>
        );
    };

    return (
        <AbsoluteFill>
            <Background />
            <OriginalVideoArea />
            <TextArea />
            <Audio src={staticFile("` + audioFileName + `")} />
            <Subtitles />
            <ProgressBar />
        </AbsoluteFill>
    );
};

// 注册组件
registerRoot(() => (
    <Composition
        id="${composition.id}"
        component={VideoComposition}
        durationInFrames={${composition.durationInFrames}}
        fps={${composition.fps}}
        width={${composition.width}}
        height={${composition.height}}
        defaultProps={{}}
    />
));
`;
    }

    /**
     * @功能概述: 执行视频渲染
     * @param {string} bundleLocation - 打包文件路径
     * @param {Object} remotionConfig - Remotion配置
     * @param {Object} context - 任务上下文
     * @param {Function} progressCallback - 进度回调函数
     * @param {string} logPrefix - 日志前缀
     * @returns {Promise<Object>} 渲染结果
     */
    async _renderVideo(bundleLocation, remotionConfig, context, progressCallback, logPrefix) {
        logger.debug(`${logPrefix}[步骤 4.1] 开始视频渲染`);
        
        try {
            const startTime = Date.now();
            const outputPath = path.join(context.savePath, `${context.videoIdentifier || 'video'}_remotion.mp4`);
            
            // 确保输出目录存在
            await fs.mkdir(path.dirname(outputPath), { recursive: true });
            
            // 准备输入属性
            const inputProps = {
                ...remotionConfig.composition.defaultProps
            };

            // 获取组合信息
            const compositions = await selectComposition({
                serveUrl: bundleLocation,
                id: remotionConfig.composition.id,
                inputProps: inputProps
            });

            // 渲染视频
            await renderMedia({
                composition: compositions,
                serveUrl: bundleLocation,
                codec: 'h264',
                outputLocation: outputPath,
                inputProps: inputProps,
                onProgress: ({ renderedFrames, encodedFrames, encodedDoneIn }) => {
                    const progress = Math.round((renderedFrames / remotionConfig.composition.durationInFrames) * 100);
                    logger.debug(`${logPrefix}[步骤 4.1] 渲染进度: ${progress}% (${renderedFrames}/${remotionConfig.composition.durationInFrames})`);
                    
                    if (progressCallback) {
                        progressCallback({
                            taskName: this.name,
                            status: 'rendering_progress',
                            detail: `渲染进度: ${progress}%`,
                            progress: {
                                current: renderedFrames,
                                total: remotionConfig.composition.durationInFrames,
                                percentage: progress
                            }
                        });
                    }
                }
            });
            
            const renderTime = Date.now() - startTime;
            
            // 获取输出文件大小
            const stats = await fs.stat(outputPath);
            const outputSize = stats.size;
            
            logger.info(`${logPrefix}[步骤 4.1][SUCCESS] 视频渲染完成 - 耗时: ${renderTime}ms, 大小: ${outputSize} bytes`);
            
            return {
                outputPath,
                renderTime,
                outputSize
            };
            
        } catch (error) {
            logger.error(`${logPrefix}[步骤 4.1][ERROR] 视频渲染失败: ${error.message}`);
            throw new Error(`视频渲染失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 清理任务资源
     */
    async cleanup() {
        const logPrefix = `[RemotionVideoRenderTask][cleanup]`;
        
        try {
            logger.debug(`${logPrefix} 开始清理任务资源`);
            
            // 清理临时组件文件
            const tempDir = path.join(__dirname, '../temp/remotion-components');
            try {
                const files = await fs.readdir(tempDir);
                for (const file of files) {
                    if (file.includes(this.id || 'VideoComponent')) {
                        await fs.unlink(path.join(tempDir, file));
                        logger.debug(`${logPrefix} 删除临时文件: ${file}`);
                    }
                }
            } catch (error) {
                // 忽略清理错误
            }
            
            logger.debug(`${logPrefix} 资源清理完成`);
            
        } catch (error) {
            logger.error(`${logPrefix} 资源清理失败: ${error.message}`);
            // 清理失败不应该影响主流程，只记录错误
        }
    }
}

module.exports = RemotionVideoRenderTask;
