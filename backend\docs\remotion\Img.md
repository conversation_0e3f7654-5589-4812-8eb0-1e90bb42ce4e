# &lt;Img&gt;

## 概述

`<Img>` 标签可以像常规的 `<img>` HTML 标签一样使用。使用 `<Img>` 时，Remotion 会确保图像在渲染帧之前已加载完成，这样可以避免图像未立即加载时出现的闪烁现象。

## 语法

```typescript
import { Img } from "remotion";

<Img src="图像路径" />
```

## 核心属性

### src (必需)
- **类型**: `string`
- **描述**: 图像的源路径，可以是本地文件或远程 URL

### onError (可选)
- **类型**: `(event: React.SyntheticEvent<HTMLImageElement, Event>) => void`
- **描述**: 图像加载失败时的错误处理函数

### maxRetries (可选)
- **类型**: `number`
- **默认值**: `2`
- **版本要求**: v3.3.82+
- **描述**: 图像加载失败时的重试次数

### pauseWhenLoading (可选)
- **类型**: `boolean`
- **版本要求**: v4.0.111+
- **描述**: 如果设置为 `true`，在图像加载时暂停播放器

### delayRenderTimeoutInMilliseconds (可选)
- **类型**: `number`
- **版本要求**: v4.0.140+
- **描述**: 自定义 [`delayRender()`](./delayRender.md) 调用的超时时间

### delayRenderRetries (可选)
- **类型**: `number`
- **版本要求**: v4.0.140+
- **描述**: 自定义 [`delayRender()`](./delayRender.md) 调用的重试次数

## 基础用法

### 1. 本地图像

```typescript
import { AbsoluteFill, Img, staticFile } from "remotion";

export const MyComp: React.FC = () => {
  return (
    <AbsoluteFill>
      <Img src={staticFile("hi.png")} />
    </AbsoluteFill>
  );
};
```

### 2. 远程图像

```typescript
import { AbsoluteFill, Img } from "remotion";

export const MyComp: React.FC = () => {
  return (
    <AbsoluteFill>
      <Img src="https://picsum.photos/200/300" />
    </AbsoluteFill>
  );
};
```

### 3. 错误处理

```typescript
import { AbsoluteFill, Img, staticFile } from "remotion";

export const MyComp: React.FC = () => {
  return (
    <AbsoluteFill>
      <Img
        src={staticFile("hi.png")}
        onError={(event) => {
          console.error("图像加载失败:", event);
          // 处理图像加载错误
        }}
      />
    </AbsoluteFill>
  );
};
```

## 实际应用场景

### 1. 响应式图像显示

```typescript
import React from 'react';
import { Img, staticFile, useVideoConfig } from "remotion";

interface ResponsiveImageProps {
  imageName: string;
  alt?: string;
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({ 
  imageName, 
  alt = "图像" 
}) => {
  const { width, height } = useVideoConfig();
  
  // 根据视频尺寸选择合适的图像
  const getImageSrc = () => {
    const aspectRatio = width / height;
    
    if (aspectRatio > 1.5) {
      // 横向格式
      return staticFile(`images/landscape/${imageName}`);
    } else if (aspectRatio < 0.8) {
      // 竖向格式
      return staticFile(`images/portrait/${imageName}`);
    } else {
      // 正方形格式
      return staticFile(`images/square/${imageName}`);
    }
  };

  return (
    <Img
      src={getImageSrc()}
      style={{
        width: "100%",
        height: "100%",
        objectFit: "cover"
      }}
      alt={alt}
      onError={(event) => {
        console.warn(`图像加载失败: ${imageName}`);
      }}
    />
  );
};

export default ResponsiveImage;
```

### 2. 图像预加载和缓存

```typescript
import React, { useState } from 'react';
import { Img, staticFile, delayRender, continueRender } from "remotion";

interface PreloadedImageProps {
  images: string[];
  currentIndex: number;
}

const PreloadedImageGallery: React.FC<PreloadedImageProps> = ({ 
  images, 
  currentIndex 
}) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [renderHandle] = useState(() => delayRender());

  React.useEffect(() => {
    // 预加载所有图像
    const preloadImages = async () => {
      const promises = images.map(imageName => {
        return new Promise<string>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(imageName);
          img.onerror = () => reject(imageName);
          img.src = staticFile(`gallery/${imageName}`);
        });
      });

      try {
        const loaded = await Promise.all(promises);
        setLoadedImages(new Set(loaded));
        continueRender(renderHandle);
      } catch (error) {
        console.error("图像预加载失败:", error);
        continueRender(renderHandle);
      }
    };

    preloadImages();
  }, [images, renderHandle]);

  const currentImage = images[currentIndex];

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative",
      backgroundColor: "#f0f0f0"
    }}>
      {loadedImages.has(currentImage) ? (
        <Img
          src={staticFile(`gallery/${currentImage}`)}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "contain"
          }}
          alt={`图像 ${currentIndex + 1}`}
        />
      ) : (
        <div style={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontSize: 24,
          color: "#666"
        }}>
          加载中...
        </div>
      )}
      
      {/* 图像计数器 */}
      <div style={{
        position: "absolute",
        bottom: 20,
        right: 20,
        backgroundColor: "rgba(0,0,0,0.7)",
        color: "white",
        padding: "8px 16px",
        borderRadius: 20,
        fontSize: 16
      }}>
        {currentIndex + 1} / {images.length}
      </div>
    </div>
  );
};
```

### 3. 动态图像切换

```typescript
import React from 'react';
import { Img, staticFile, useCurrentFrame, interpolate } from "remotion";

interface ImageTransitionProps {
  images: string[];
  transitionDuration: number;
}

const ImageTransition: React.FC<ImageTransitionProps> = ({ 
  images, 
  transitionDuration 
}) => {
  const frame = useCurrentFrame();
  
  // 计算当前应该显示的图像索引
  const currentImageIndex = Math.floor(frame / transitionDuration) % images.length;
  const nextImageIndex = (currentImageIndex + 1) % images.length;
  
  // 计算过渡进度
  const transitionProgress = interpolate(
    frame % transitionDuration,
    [0, transitionDuration * 0.8, transitionDuration],
    [0, 0, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      position: "relative"
    }}>
      {/* 当前图像 */}
      <Img
        src={staticFile(`slideshow/${images[currentImageIndex]}`)}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          opacity: 1 - transitionProgress,
          position: "absolute",
          top: 0,
          left: 0
        }}
        alt={`幻灯片 ${currentImageIndex + 1}`}
      />
      
      {/* 下一张图像 */}
      <Img
        src={staticFile(`slideshow/${images[nextImageIndex]}`)}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          opacity: transitionProgress,
          position: "absolute",
          top: 0,
          left: 0
        }}
        alt={`幻灯片 ${nextImageIndex + 1}`}
      />
    </div>
  );
};
```

### 4. 图像错误处理和回退

```typescript
import React, { useState } from 'react';
import { Img, staticFile } from "remotion";

interface RobustImageProps {
  primarySrc: string;
  fallbackSrc?: string;
  placeholder?: React.ReactNode;
  alt?: string;
}

const RobustImage: React.FC<RobustImageProps> = ({ 
  primarySrc, 
  fallbackSrc, 
  placeholder,
  alt = "图像"
}) => {
  const [currentSrc, setCurrentSrc] = useState(primarySrc);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      console.warn(`主图像加载失败，切换到备用图像: ${primarySrc}`);
      setCurrentSrc(fallbackSrc);
      setHasError(false);
    } else {
      console.error(`所有图像源都加载失败: ${primarySrc}`);
      setHasError(true);
    }
    setIsLoading(false);
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  if (hasError) {
    return (
      <div style={{
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
        border: "2px dashed #ccc",
        color: "#666",
        fontSize: 18
      }}>
        {placeholder || (
          <div style={{ textAlign: "center" }}>
            <div style={{ fontSize: 48, marginBottom: 10 }}>📷</div>
            <div>图像加载失败</div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div style={{ width: "100%", height: "100%", position: "relative" }}>
      <Img
        src={currentSrc}
        onError={handleError}
        onLoad={handleLoad}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          opacity: isLoading ? 0 : 1,
          transition: "opacity 0.3s ease"
        }}
        alt={alt}
        maxRetries={3}
      />
      
      {isLoading && (
        <div style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f0f0f0",
          color: "#666",
          fontSize: 16
        }}>
          加载中...
        </div>
      )}
    </div>
  );
};

// 使用示例
export const ImageExample = () => {
  return (
    <RobustImage
      primarySrc={staticFile("hero-image.jpg")}
      fallbackSrc={staticFile("default-image.jpg")}
      alt="英雄图像"
    />
  );
};
```

### 5. 图像网格布局

```typescript
import React from 'react';
import { Img, staticFile, useVideoConfig } from "remotion";

interface ImageGridProps {
  images: string[];
  columns: number;
  gap?: number;
}

const ImageGrid: React.FC<ImageGridProps> = ({ 
  images, 
  columns, 
  gap = 10 
}) => {
  const { width, height } = useVideoConfig();
  
  const rows = Math.ceil(images.length / columns);
  const imageWidth = (width - gap * (columns + 1)) / columns;
  const imageHeight = (height - gap * (rows + 1)) / rows;

  return (
    <div style={{
      width: "100%",
      height: "100%",
      display: "grid",
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gridTemplateRows: `repeat(${rows}, 1fr)`,
      gap: gap,
      padding: gap,
      backgroundColor: "#000"
    }}>
      {images.map((imageName, index) => (
        <div
          key={index}
          style={{
            width: "100%",
            height: "100%",
            overflow: "hidden",
            borderRadius: 8
          }}
        >
          <Img
            src={staticFile(`grid/${imageName}`)}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover"
            }}
            alt={`网格图像 ${index + 1}`}
            onError={() => {
              console.warn(`网格图像加载失败: ${imageName}`);
            }}
          />
        </div>
      ))}
    </div>
  );
};

// 使用示例
export const PhotoGrid = () => {
  const images = [
    "photo1.jpg", "photo2.jpg", "photo3.jpg",
    "photo4.jpg", "photo5.jpg", "photo6.jpg"
  ];

  return (
    <ImageGrid 
      images={images} 
      columns={3} 
      gap={15} 
    />
  );
};
```

## 错误行为

### 版本差异
- **v4.0.0+**: 如果图像加载失败且没有剩余重试次数，将调用 [`cancelRender`](./cancelRender.md) 抛出错误，除非使用 `onError()` 处理错误
- **v3.3.82+**: 如果图像加载失败，将重试最多 2 次
- **早期版本**: 图像加载失败会在控制台显示错误消息并最终超时

### 重试机制
- 使用指数退避策略
- 第一次和第二次尝试之间延迟 1000ms
- 然后是 2000ms、4000ms，以此类推

## 限制和注意事项

1. **最大分辨率**: Chrome 可显示的最大分辨率为 `2^29` 像素（539 兆像素）
2. **GIF 支持**: 不要使用 `<Img>` 标签显示 GIF，请使用 [`@remotion/gif`](./Gif.md) 代替
3. **错误处理**: 如果发生错误，必须卸载组件或替换 `src`，否则渲染会超时

## 其他属性

`<Img>` 组件继承常规 `<img>` 标签的所有属性，例如 `style`、`className`、`alt` 等。

## 最佳实践

1. **错误处理**: 始终提供 `onError` 处理函数
2. **预加载**: 对于关键图像，考虑预加载策略
3. **回退方案**: 为重要图像提供备用源
4. **性能优化**: 使用适当的图像格式和尺寸
5. **可访问性**: 提供有意义的 `alt` 属性

## 常见用例

- 背景图像显示
- 产品图片展示
- 用户头像显示
- 图像幻灯片
- 网格布局图片
- 响应式图像

## 相关 API

- [`staticFile()`](./staticFile.md) - 静态文件访问
- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`continueRender()`](./continueRender.md) - 继续渲染
- [`<Video>`](./Video.md) - 视频组件
- [`<Gif>`](./Gif.md) - GIF 组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/Img.tsx)
