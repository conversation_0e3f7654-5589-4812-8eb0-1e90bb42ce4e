/**
 * @fileoverview 字幕生成流水线服务测试
 * @description 完整的字幕生成流水线测试，从视频输入到字幕产出的全过程
 * @version 1.0.0
 * <AUTHOR> Assistant
 * @created 2025-01-13
 */

const TestSubtitleGenerationPipelineService = require('../TestSubtitleGenerationPipelineService');
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
    runLLMTests: process.env.RUN_LLM_TESTS === 'true',
    testMode: process.env.TEST_MODE || 'fast',
    timeout: process.env.RUN_LLM_TESTS === 'true' ? 600000 : 30000 // 10分钟 vs 30秒
};

// 测试日志前缀
const testLogPrefix = '[文件：TestSubtitleGenerationPipelineService.test.js][字幕生成流水线测试][测试执行]';

logger.info(`${testLogPrefix} 🧪 开始测试 TestSubtitleGenerationPipelineService`);
logger.info(`${testLogPrefix} 📊 测试模式: ${TEST_CONFIG.testMode}`);
logger.info(`${testLogPrefix} 🤖 LLM测试: ${TEST_CONFIG.runLLMTests ? '启用' : '禁用'}`);
logger.info(`${testLogPrefix} ⏱️ 超时设置: ${TEST_CONFIG.timeout / 1000}秒`);

/**
 * 创建测试上下文（硬编码）
 * @returns {Object} 测试上下文
 */
function createTestContext() {
    return {
        // 基础信息
        videoIdentifier: 'test_neetu_garcha_june_8_2025',
        reqId: 'test-subtitle-generation-001',
        
        // 硬编码的文件路径
        originalVideoPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\Neetu Garcha June 8 2025.mp4",
        originalVideoName: "Neetu Garcha June 8 2025.mp4", // ConvertToAudioTask需要的字段
        uploadedVideoDirPath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input", // ConvertToAudioTask需要的字段
        savePath: "C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output"
    };
}

/**
 * 验证文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否存在
 */
function validateFileExists(filePath) {
    try {
        return fs.existsSync(filePath);
    } catch (error) {
        logger.error(`${testLogPrefix} 文件检查失败: ${error.message}`);
        return false;
    }
}

/**
 * 创建输出目录
 * @param {string} dirPath - 目录路径
 */
function ensureOutputDirectory(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            logger.info(`${testLogPrefix} 创建输出目录: ${dirPath}`);
        }
    } catch (error) {
        logger.error(`${testLogPrefix} 创建输出目录失败: ${error.message}`);
    }
}

/**
 * 基础验证测试
 */
async function runBasicValidationTests() {
    logger.info(`${testLogPrefix} ========== 基础验证测试 ==========`);
    
    let testsPassed = 0;
    let testsFailed = 0;
    
    try {
        // 测试1: 流水线创建测试
        logger.info(`${testLogPrefix} --- 测试1: 流水线创建测试 ---`);
        
        const service = new TestSubtitleGenerationPipelineService('test-subtitle-generation');
        
        if (service instanceof TestSubtitleGenerationPipelineService) {
            logger.info(`${testLogPrefix} ✅ 流水线服务创建成功`);
            testsPassed++;
        } else {
            logger.error(`${testLogPrefix} ❌ 流水线服务创建失败`);
            testsFailed++;
        }
        
        // 测试2: 任务数量验证
        logger.info(`${testLogPrefix} --- 测试2: 任务数量验证 ---`);
        
        if (service.processingPipeline.tasks.length === 7) {
            logger.info(`${testLogPrefix} ✅ 流水线包含7个任务`);
            testsPassed++;
        } else {
            logger.error(`${testLogPrefix} ❌ 流水线任务数量错误: ${service.processingPipeline.tasks.length}`);
            testsFailed++;
        }
        
        // 测试3: 任务序列验证
        logger.info(`${testLogPrefix} --- 测试3: 任务序列验证 ---`);
        
        const expectedTasks = [
            'ConvertToAudioTask',
            'GetTranscriptionTask',
            'SubtitleOptimizationTask',
            'TranscriptionCorrectionTask',
            'TranslateSubtitleTask',
            'SubtitleClozeTask',
            'BilingualSubtitleMergeTask'
        ];

        let taskSequenceCorrect = true;
        for (let i = 0; i < expectedTasks.length; i++) {
            const expectedTask = expectedTasks[i];
            const actualTask = service.processingPipeline.tasks[i]?.name;
            
            if (actualTask !== expectedTask) {
                logger.error(`${testLogPrefix} ❌ 任务${i + 1}不匹配: 期望${expectedTask}, 实际${actualTask}`);
                taskSequenceCorrect = false;
            }
        }

        if (taskSequenceCorrect) {
            logger.info(`${testLogPrefix} ✅ 任务序列正确`);
            testsPassed++;
        } else {
            testsFailed++;
        }
        
        // 测试4: 输入文件验证
        logger.info(`${testLogPrefix} --- 测试4: 输入文件验证 ---`);
        
        const testContext = createTestContext();
        
        if (validateFileExists(testContext.originalVideoPath)) {
            logger.info(`${testLogPrefix} ✅ 输入视频文件存在: ${testContext.originalVideoPath}`);
            testsPassed++;
        } else {
            logger.error(`${testLogPrefix} ❌ 输入视频文件不存在: ${testContext.originalVideoPath}`);
            testsFailed++;
        }
        
        // 测试5: 输出目录准备
        logger.info(`${testLogPrefix} --- 测试5: 输出目录准备 ---`);
        
        ensureOutputDirectory(testContext.savePath);
        
        if (validateFileExists(testContext.savePath)) {
            logger.info(`${testLogPrefix} ✅ 输出目录已准备: ${testContext.savePath}`);
            testsPassed++;
        } else {
            logger.error(`${testLogPrefix} ❌ 输出目录创建失败: ${testContext.savePath}`);
            testsFailed++;
        }
        
        logger.info(`${testLogPrefix} 基础验证完成: 通过 ${testsPassed}, 失败 ${testsFailed}`);
        return { passed: testsPassed, failed: testsFailed };
        
    } catch (error) {
        logger.error(`${testLogPrefix} 基础验证测试异常: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        return { passed: testsPassed, failed: testsFailed + 1 };
    }
}

/**
 * 完整流水线执行测试
 */
async function runFullPipelineTest() {
    logger.info(`${testLogPrefix} ========== 完整流水线执行测试 ==========`);
    
    try {
        const startTime = Date.now();
        
        // 创建流水线服务
        const service = new TestSubtitleGenerationPipelineService('full-pipeline-test');
        
        // 创建测试上下文
        const testContext = createTestContext();
        
        logger.info(`${testLogPrefix} 开始执行完整流水线...`);
        logger.info(`${testLogPrefix} 输入视频: ${testContext.originalVideoPath}`);
        logger.info(`${testLogPrefix} 输出目录: ${testContext.savePath}`);
        logger.info(`${testLogPrefix} 视频标识: ${testContext.videoIdentifier}`);
        
        // 进度回调函数
        const progressCallback = (progress) => {
            logger.info(`${testLogPrefix} 流水线进度: [${progress.taskName}] ${progress.status} - ${progress.detail || 'N/A'}`);
        };
        
        // 执行流水线
        const result = await service.processSubtitles(testContext, progressCallback);
        
        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000;
        
        logger.info(`${testLogPrefix} 流水线执行完成，耗时: ${executionTime.toFixed(2)}秒`);
        logger.info(`${testLogPrefix} 执行状态: ${result.status}`);
        
        // 验证执行结果
        if (result.status === 'completed') {
            logger.info(`${testLogPrefix} ✅ 流水线执行成功`);
            
            // 检查输出文件
            if (result.context) {
                const outputs = [
                    { name: '音频文件', path: result.context.audioFilePath },
                    { name: '英文字幕JSON', path: result.context.correctedSubtitleJsonPath },
                    { name: '英文SRT文件', path: result.context.englishSrtPath },
                    { name: '中文字幕JSON', path: result.context.translatedSubtitleJsonPath },
                    { name: '中文SRT文件', path: result.context.chineseSrtPath },
                    { name: '挖空字幕JSON', path: result.context.clozedSubtitleJsonPath },
                    { name: '增强双语字幕JSON', path: result.context.enhancedBilingualSubtitleJsonPath }
                ];
                
                let outputsValid = 0;
                for (const output of outputs) {
                    if (output.path && typeof output.path === 'string') {
                        logger.info(`${testLogPrefix} ✅ ${output.name}: ${output.path}`);
                        outputsValid++;
                    } else {
                        logger.warn(`${testLogPrefix} ⚠️ ${output.name}: 路径无效或缺失`);
                    }
                }
                
                logger.info(`${testLogPrefix} 输出文件验证: ${outputsValid}/${outputs.length} 个文件路径有效`);
                
                // 验证关键数据
                const dataChecks = [
                    { name: '音频时长', value: result.context.audioDuration, type: 'number' },
                    { name: '转录文本', value: result.context.transcriptionText, type: 'string' },
                    { name: '英文字幕数组', value: result.context.correctedSubtitleJsonArray, type: 'array' },
                    { name: '中文字幕数组', value: result.context.translatedSubtitleJsonArray, type: 'array' },
                    { name: '挖空字幕数组', value: result.context.clozedSubtitleJsonArray, type: 'array' },
                    { name: '增强双语字幕数组', value: result.context.enhancedBilingualSubtitleJsonArray, type: 'array' }
                ];
                
                let dataValid = 0;
                for (const check of dataChecks) {
                    const isValid = check.type === 'array' ? Array.isArray(check.value) : typeof check.value === check.type;
                    if (isValid) {
                        const length = check.type === 'array' ? check.value.length : check.value.toString().length;
                        logger.info(`${testLogPrefix} ✅ ${check.name}: ${check.type === 'array' ? `${length}条` : `${length}字符`}`);
                        dataValid++;
                    } else {
                        logger.warn(`${testLogPrefix} ⚠️ ${check.name}: 数据无效`);
                    }
                }
                
                logger.info(`${testLogPrefix} 数据验证: ${dataValid}/${dataChecks.length} 项数据有效`);
                
                // 显示统计信息
                if (result.context.originalSegmentsCount && result.context.optimizedSegmentsCount) {
                    logger.info(`${testLogPrefix} 字幕优化: ${result.context.originalSegmentsCount} → ${result.context.optimizedSegmentsCount} segments`);
                }
                
                // 显示音频信息
                if (result.context.audioDuration) {
                    logger.info(`${testLogPrefix} 音频时长: ${result.context.audioDuration.toFixed(2)}秒`);
                }
                
                return { success: true, result, executionTime, outputValidation: outputsValid };
            }
        } else {
            logger.error(`${testLogPrefix} ❌ 流水线执行失败`);
            if (result.error) {
                logger.error(`${testLogPrefix} 错误信息: ${result.error}`);
            }
            return { success: false, result, executionTime };
        }
        
    } catch (error) {
        logger.error(`${testLogPrefix} 完整流水线测试异常: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        return { success: false, error: error.message };
    }
}

/**
 * 主测试函数
 */
async function runAllTests() {
    logger.info(`${testLogPrefix} =======================================`);
    logger.info(`${testLogPrefix} 开始执行字幕生成流水线完整测试`);
    logger.info(`${testLogPrefix} =======================================`);
    
    const testStartTime = Date.now();
    
    try {
        // 阶段1: 基础验证测试
        const basicTestResults = await runBasicValidationTests();
        
        if (basicTestResults.failed > 0) {
            logger.error(`${testLogPrefix} 基础验证测试失败，跳过完整流水线测试`);
            return;
        }
        
        // 阶段2: 完整流水线测试
        const pipelineTestResult = await runFullPipelineTest();
        
        const totalTestTime = (Date.now() - testStartTime) / 1000;
        
        // 生成最终报告
        logger.info(`${testLogPrefix} =======================================`);
        logger.info(`${testLogPrefix} 测试报告`);
        logger.info(`${testLogPrefix} =======================================`);
        logger.info(`${testLogPrefix} 基础验证: 通过 ${basicTestResults.passed}, 失败 ${basicTestResults.failed}`);
        logger.info(`${testLogPrefix} 流水线执行: ${pipelineTestResult.success ? '成功' : '失败'}`);
        if (pipelineTestResult.executionTime) {
            logger.info(`${testLogPrefix} 流水线耗时: ${pipelineTestResult.executionTime.toFixed(2)}秒`);
        }
        if (pipelineTestResult.outputValidation) {
            logger.info(`${testLogPrefix} 输出文件: ${pipelineTestResult.outputValidation} 个文件生成成功`);
        }
        logger.info(`${testLogPrefix} 总测试时间: ${totalTestTime.toFixed(2)}秒`);
        logger.info(`${testLogPrefix} =======================================`);
        
        if (basicTestResults.failed === 0 && pipelineTestResult.success) {
            logger.info(`${testLogPrefix} 🎉 所有测试通过！字幕生成流水线工作正常。`);
        } else {
            logger.error(`${testLogPrefix} ❌ 部分测试失败，请检查上述错误信息。`);
        }
        
    } catch (error) {
        logger.error(`${testLogPrefix} 测试执行异常: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
    }
}

// 导出测试函数
module.exports = {
    runAllTests,
    runBasicValidationTests,
    runFullPipelineTest,
    createTestContext
};

// 如果直接运行此文件，则执行所有测试
if (require.main === module) {
    runAllTests().catch(error => {
        logger.error(`${testLogPrefix} 未捕获的测试错误: ${error.message}`);
        process.exit(1);
    });
} 