# spring()

## 概述

`spring()` 是 Remotion 中基于物理的动画原语，提供自然的弹簧动画效果。它模拟真实世界中弹簧的物理特性，创造出更加自然和流畅的动画。

## 语法

```typescript
import { spring, useCurrentFrame, useVideoConfig } from "remotion";

const value = spring({
  frame,
  fps,
  config: {
    stiffness: 100,
  },
});
```

## 参数

### 必需参数

#### frame
- **类型**: `number`
- **描述**: 当前时间值，通常传入 `useCurrentFrame()` 的返回值
- **说明**: 弹簧动画从第 0 帧开始，如需延迟可传入 `frame - 20` 等

#### fps
- **类型**: `number`
- **描述**: 每秒帧数，应传入 `useVideoConfig().fps`

### 可选参数

#### from
- **类型**: `number`
- **默认值**: `0`
- **描述**: 动画的初始值

#### to
- **类型**: `number`
- **默认值**: `1`
- **描述**: 动画的结束值（可能会有超调）

#### reverse
- **类型**: `boolean`
- **默认值**: `false`
- **描述**: 是否反向播放动画

#### config
物理属性配置对象：

##### mass
- **类型**: `number`
- **默认值**: `1`
- **描述**: 弹簧的质量，减少质量会使动画更快

##### damping
- **类型**: `number`
- **默认值**: `10`
- **描述**: 阻尼系数，控制动画减速的强度

##### stiffness
- **类型**: `number`
- **默认值**: `100`
- **描述**: 弹簧刚度系数，影响动画的弹性

##### overshootClamping
- **类型**: `boolean`
- **默认值**: `false`
- **描述**: 是否限制超调，设为 true 时不会超过目标值

#### durationInFrames
- **类型**: `number`
- **描述**: 拉伸动画曲线到指定的帧数长度

#### durationRestThreshold
- **类型**: `number`
- **描述**: 动画接近结束的阈值，仅在指定 `durationInFrames` 时有效

#### delay
- **类型**: `number`
- **描述**: 延迟动画的帧数

## 基础示例

### 1. 简单弹簧动画

```typescript
import { spring, useCurrentFrame, useVideoConfig } from "remotion";

const SimpleSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const value = spring({
    frame,
    fps,
    config: {
      stiffness: 100,
    },
  });
  
  return (
    <div style={{ 
      transform: `scale(${value})`,
      width: 100,
      height: 100,
      backgroundColor: 'red'
    }}>
      弹簧缩放
    </div>
  );
};
```

### 2. 自定义起始和结束值

```typescript
const CustomRangeSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const position = spring({
    frame,
    fps,
    from: 0,
    to: 300,
    config: {
      stiffness: 80,
      damping: 12,
    },
  });
  
  return (
    <div style={{ 
      transform: `translateX(${position}px)`,
      width: 50,
      height: 50,
      backgroundColor: 'blue'
    }}>
      移动元素
    </div>
  );
};
```

### 3. 延迟弹簧动画

```typescript
const DelayedSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const value = spring({
    frame,
    fps,
    delay: 30, // 延迟 30 帧开始
    config: {
      stiffness: 150,
    },
  });
  
  return (
    <div style={{ opacity: value }}>
      延迟出现的内容
    </div>
  );
};
```

## 高级配置示例

### 1. 高弹性动画

```typescript
const BouncySpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const scale = spring({
    frame,
    fps,
    config: {
      mass: 0.5,      // 较轻的质量
      stiffness: 200, // 高刚度
      damping: 8,     // 低阻尼
    },
  });
  
  return (
    <div style={{ transform: `scale(${scale})` }}>
      高弹性动画
    </div>
  );
};
```

### 2. 平滑动画（无超调）

```typescript
const SmoothSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const position = spring({
    frame,
    fps,
    config: {
      mass: 1,
      stiffness: 100,
      damping: 20,              // 高阻尼
      overshootClamping: true,  // 防止超调
    },
  });
  
  return (
    <div style={{ transform: `translateY(${position * 200}px)` }}>
      平滑移动
    </div>
  );
};
```

### 3. 固定持续时间的弹簧

```typescript
const FixedDurationSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const value = spring({
    frame,
    fps,
    durationInFrames: 60,        // 固定 60 帧持续时间
    durationRestThreshold: 0.01, // 99% 接近目标值
    config: {
      stiffness: 100,
    },
  });
  
  return (
    <div style={{ transform: `rotate(${value * 360}deg)` }}>
      固定时长旋转
    </div>
  );
};
```

## 实际应用场景

### 1. 入场动画

```typescript
const EntranceAnimation = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const scale = spring({
    frame,
    fps,
    from: 0,
    to: 1,
    config: {
      stiffness: 120,
      damping: 14,
    },
  });
  
  const opacity = spring({
    frame,
    fps,
    config: {
      stiffness: 100,
      damping: 10,
    },
  });
  
  return (
    <div style={{ 
      transform: `scale(${scale})`,
      opacity,
      padding: 20,
      backgroundColor: '#f0f0f0',
      borderRadius: 8
    }}>
      弹性入场的卡片
    </div>
  );
};
```

### 2. 序列动画

```typescript
const SequenceSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const items = [0, 1, 2, 3];
  
  return (
    <div style={{ display: 'flex', gap: 10 }}>
      {items.map((index) => {
        const itemSpring = spring({
          frame,
          fps,
          delay: index * 10, // 每个元素延迟 10 帧
          config: {
            stiffness: 150,
            damping: 12,
          },
        });
        
        return (
          <div
            key={index}
            style={{
              transform: `translateY(${(1 - itemSpring) * 50}px)`,
              opacity: itemSpring,
              width: 40,
              height: 40,
              backgroundColor: `hsl(${index * 60}, 70%, 50%)`,
            }}
          />
        );
      })}
    </div>
  );
};
```

### 3. 反向弹簧动画

```typescript
const ReverseSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const value = spring({
    frame,
    fps,
    reverse: true, // 反向播放
    config: {
      stiffness: 100,
    },
  });
  
  return (
    <div style={{ 
      transform: `scale(${value})`,
      opacity: value
    }}>
      反向弹簧动画
    </div>
  );
};
```

## 操作顺序

弹簧动画的参数按以下顺序应用：

1. **拉伸持续时间**: 如果指定了 `durationInFrames`，首先拉伸动画
2. **反向播放**: 如果 `reverse: true`，然后反向动画
3. **延迟**: 如果指定了 `delay`，最后应用延迟

## 弹簧编辑器

访问 [springs.remotion.dev](https://springs.remotion.dev) 可以实时调整弹簧参数并查看效果。

## 最佳实践

### 1. 性能优化

```typescript
import { useMemo } from "react";

const OptimizedSpring = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 缓存弹簧计算
  const springValue = useMemo(() => spring({
    frame,
    fps,
    config: { stiffness: 100 }
  }), [frame, fps]);
  
  return <div style={{ transform: `scale(${springValue})` }} />;
};
```

### 2. 组合多个弹簧

```typescript
const MultiSpringAnimation = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const x = spring({ frame, fps, to: 200, config: { stiffness: 80 } });
  const y = spring({ frame, fps, to: 100, delay: 15, config: { stiffness: 120 } });
  const rotation = spring({ frame, fps, to: 360, delay: 30 });
  
  return (
    <div style={{
      transform: `translate(${x}px, ${y}px) rotate(${rotation}deg)`
    }}>
      复合弹簧动画
    </div>
  );
};
```

## 相关 API

- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置
- [`interpolate()`](./interpolate.md) - 值插值
- [`measureSpring()`](./measureSpring.md) - 测量弹簧持续时间

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/spring/index.ts)
