# interpolate()

## 概述

`interpolate()` 是 Remotion 中最重要的函数之一，允许您使用简洁的语法将一个值范围映射到另一个值范围。这是创建动画的核心工具。

## 语法

```typescript
import { interpolate } from "remotion";

const result = interpolate(
  inputValue,
  inputRange,
  outputRange,
  options?
);
```

## 参数

1. **inputValue** (`number`): 输入值
2. **inputRange** (`number[]`): 输入值的范围数组
3. **outputRange** (`number[]`): 输出值的范围数组
4. **options** (`InterpolateOptions`): 可选配置对象

## 返回值

- **类型**: `number`
- **描述**: 映射后的输出值

## 基础示例

### 1. 淡入效果

```typescript
import { interpolate, useCurrentFrame } from "remotion";

const FadeInComponent = () => {
  const frame = useCurrentFrame(); // 假设当前帧是 10
  
  // 从第 0 帧到第 20 帧，透明度从 0 变为 1
  const opacity = interpolate(frame, [0, 20], [0, 1]); // 结果: 0.5
  
  return (
    <div style={{ opacity }}>
      淡入内容
    </div>
  );
};
```

### 2. 淡入淡出效果

```typescript
import { interpolate, useCurrentFrame, useVideoConfig } from "remotion";

const FadeInOutComponent = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  
  const opacity = interpolate(
    frame,
    [0, 20, durationInFrames - 20, durationInFrames],
    [0, 1, 1, 0]
  );
  
  return (
    <div style={{ opacity }}>
      淡入淡出内容
    </div>
  );
};
```

### 3. 弹簧动画插值

```typescript
import { useCurrentFrame, interpolate, spring, useVideoConfig } from 'remotion';

const SpringAnimationComponent = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 创建弹簧动画驱动器
  const driver = spring({
    frame,
    fps
  });
  
  // 将弹簧值从 0-1 映射到 0-200 像素
  const marginLeft = interpolate(driver, [0, 1], [0, 200]);
  
  return (
    <div style={{ marginLeft }}>
      弹簧动画元素
    </div>
  );
};
```

## 配置选项

### extrapolateLeft

控制输入值在输入范围左侧时的行为：

- `'extend'` (默认): 继续插值，即使超出输出范围
- `'clamp'`: 返回范围内最接近的值
- `'wrap'`: 循环值变化
- `'identity'`: 返回输入值本身

### extrapolateRight

控制输入值在输入范围右侧时的行为，选项同 `extrapolateLeft`。

```typescript
// 示例：不同的外推选项
interpolate(1.5, [0, 1], [0, 2], { extrapolateRight: "extend" }); // 3
interpolate(1.5, [0, 1], [0, 2], { extrapolateRight: "clamp" }); // 2
interpolate(1.5, [0, 1], [0, 2], { extrapolateRight: "identity" }); // 1.5
interpolate(1.5, [0, 1], [0, 2], { extrapolateRight: "wrap" }); // 1
```

### easing

自定义缓动函数，默认为线性插值：

```typescript
import { interpolate, Easing } from "remotion";

const easedValue = interpolate(frame, [0, 100], [0, 1], {
  easing: Easing.bezier(0.8, 0.22, 0.96, 0.65),
  extrapolateLeft: "clamp",
  extrapolateRight: "clamp",
});
```

## 实际应用示例

### 1. 缩放动画

```typescript
const ScaleAnimation = () => {
  const frame = useCurrentFrame();
  
  const scale = interpolate(frame, [0, 20], [0, 1], {
    extrapolateRight: "clamp", // 防止超过 20 帧后继续放大
  });
  
  return (
    <div style={{ transform: `scale(${scale})` }}>
      缩放动画
    </div>
  );
};
```

### 2. 位置动画

```typescript
const PositionAnimation = () => {
  const frame = useCurrentFrame();
  
  const x = interpolate(frame, [0, 60], [0, 300]);
  const y = interpolate(frame, [0, 60], [0, 200]);
  
  return (
    <div style={{ 
      transform: `translate(${x}px, ${y}px)`,
      position: 'absolute'
    }}>
      移动的元素
    </div>
  );
};
```

### 3. 颜色渐变（配合 interpolateColors）

```typescript
import { interpolateColors } from "remotion";

const ColorAnimation = () => {
  const frame = useCurrentFrame();
  
  const backgroundColor = interpolateColors(
    frame,
    [0, 30, 60],
    ['#ff0000', '#00ff00', '#0000ff']
  );
  
  return (
    <div style={{ backgroundColor, width: 100, height: 100 }}>
      颜色渐变
    </div>
  );
};
```

### 4. 多段动画

```typescript
const MultiStageAnimation = () => {
  const frame = useCurrentFrame();
  
  // 多个关键帧的复杂动画
  const rotation = interpolate(
    frame,
    [0, 30, 60, 90, 120],
    [0, 90, 180, 270, 360],
    { extrapolateRight: "clamp" }
  );
  
  const opacity = interpolate(
    frame,
    [0, 15, 105, 120],
    [0, 1, 1, 0]
  );
  
  return (
    <div style={{ 
      transform: `rotate(${rotation}deg)`,
      opacity,
      transition: 'all 0.1s ease'
    }}>
      多段动画
    </div>
  );
};
```

## 类型定义

```typescript
import { ExtrapolateType, InterpolateOptions } from "remotion";

const extrapolate: ExtrapolateType = "clamp";
const options: InterpolateOptions = { 
  extrapolateLeft: extrapolate,
  extrapolateRight: "extend",
  easing: (x) => x * x // 二次缓动
};
```

## 最佳实践

### 1. 使用 clamp 防止超出范围

```typescript
const safeScale = interpolate(frame, [0, 30], [0, 1], {
  extrapolateLeft: "clamp",
  extrapolateRight: "clamp",
});
```

### 2. 组合多个插值

```typescript
const ComplexAnimation = () => {
  const frame = useCurrentFrame();
  
  const scale = interpolate(frame, [0, 30], [0.5, 1.2]);
  const rotation = interpolate(frame, [0, 60], [0, 360]);
  const opacity = interpolate(frame, [0, 15], [0, 1]);
  
  return (
    <div style={{
      transform: `scale(${scale}) rotate(${rotation}deg)`,
      opacity
    }}>
      复合动画
    </div>
  );
};
```

### 3. 性能优化

```typescript
import { useMemo } from "react";

const OptimizedComponent = () => {
  const frame = useCurrentFrame();
  
  // 缓存复杂的插值计算
  const animatedValues = useMemo(() => ({
    x: interpolate(frame, [0, 60], [0, 300]),
    y: interpolate(frame, [0, 60], [0, 200]),
    scale: interpolate(frame, [0, 30], [0, 1])
  }), [frame]);
  
  return (
    <div style={{
      transform: `translate(${animatedValues.x}px, ${animatedValues.y}px) scale(${animatedValues.scale})`
    }}>
      优化的动画
    </div>
  );
};
```

## 相关 API

- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`spring()`](./spring.md) - 物理动画
- [`interpolateColors()`](./interpolateColors.md) - 颜色插值
- [`Easing`](./Easing.md) - 缓动函数

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/interpolate.ts)
