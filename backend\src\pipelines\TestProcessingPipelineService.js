/**
 * @文件名: TestProcessingPipelineService.js
 * @功能概述: 测试专用的完整视频处理流水线服务
 * @作者: AI Assistant
 * @创建时间: 2025-06-12
 * @最后修改: 2025-06-12
 *
 * @功能描述:
 *   完整的视频处理流水线，包含从视频转音频、语音转录到9:16视频生成的全流程。
 *   此服务编排了8个核心任务，实现了完整的视频处理、字幕生成和视频合成工作流。
 *
 * ========== 流水线任务序列和数据流转 ==========
 *
 * @任务流程:
 *   1. ConvertToAudioTask - 视频转音频，提取音频文件
 *   2. GetTranscriptionTask - 语音转录，获取ASR原始响应
 *   3. TranscriptionCorrectionTask - 转录校正，生成英文字幕
 *   4. TranslateSubtitleTask - 字幕翻译，生成中文字幕
 *   5. SubtitleClozeTask - 字幕挖空，生成填空练习字幕
 *   6. BilingualSubtitleMergeTask - 双语字幕合并增强，生成词汇解释
 *   7. GenerateASSTask - ASS字幕生成，生成完整的ASS格式字幕文件
 *   8. GenerateVideoTask - 9:16视频生成，生成最终的视频文件
 *
 * ========== 流水线上下文输入输出 ==========
 *
 * @流水线输入 (initialContext):
 *   - videoIdentifier: {string} 视频唯一标识符
 *   - originalVideoPath: {string} 原始视频文件路径
 *   - savePath: {string} 文件保存路径（可选）
 *   - reqId: {string} 请求ID（可选）
 *
 * @流水线输出 (finalContext):
 *   - audioFilePath: {string} 提取的音频文件路径
 *   - apiResponse: {object} ASR原始响应数据
 *   - transcriptionStatus: {string} 转录状态
 *   - simplifiedSubtitleJsonArray: {Array} 英文字幕JSON数组
 *   - simplifiedSubtitleJsonPath: {string} 英文字幕JSON文件路径
 *   - englishSrtPath: {string} 英文SRT文件路径
 *   - translatedSubtitleJsonArray: {Array} 中文字幕JSON数组
 *   - translatedSubtitleJsonPath: {string} 中文字幕JSON文件路径
 *   - chineseSrtPath: {string} 中文SRT文件路径
 *   - clozedSubtitleJsonArray: {Array} 挖空字幕JSON数组
 *   - clozedSubtitleJsonPath: {string} 挖空字幕JSON文件路径
 *   - clozedSubtitleSrtPath: {string} 挖空字幕SRT文件路径
 *   - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕JSON数组
 *   - enhancedBilingualSubtitleJsonPath: {string} 增强双语字幕JSON文件路径
 *   - assFilePath: {string} ASS字幕文件路径
 *   - assContent: {string} 完整的ASS字幕内容
 *   - assContentLength: {number} ASS内容长度（字符数）
 *   - videoConfig: {object} 视频配置对象
 *   - audioDuration: {number} 音频时长（秒）
 *   - finalVideoPath: {string} 最终生成的9:16视频文件路径
 *   - videoGenerationStats: {object} 视频生成统计信息
 *
 * @架构验证: 新架构下的分层验证
 *   - 流水线层: 位于 backend/src/pipelines/ 目录，负责任务编排和流程管理
 *   - 服务层: 位于 backend/src/services/ 目录，提供基础和业务服务
 *   - 任务层: 位于 backend/src/tasks/ 目录，通过服务层调用外部API
 *   - 控制器层: 位于 backend/src/controllers/ 目录，通过流水线层调用处理流程
 */

const PipelineBase = require('../class/PipelineBase');
const ConvertToAudioTask = require('../tasks/convertToAudioTask');
const GetTranscriptionTask = require('../tasks/GetTranscriptionTask');
const { TranscriptionCorrectionTask, TranslateSubtitleTask } = require('../tasks'); // Assuming tasks are exported from tasks/index.js
const SubtitleClozeTask = require('../tasks/SubtitleClozeTask');
const BilingualSubtitleMergeTask = require('../tasks/BilingualSubtitleMergeTask');
const GenerateASSTask = require('../tasks/GenerateASSTask');
const GenerateVideoTask = require('../tasks/GenerateVideoTask');
const logger = require('../utils/logger');
const config = require('../config'); // For UPLOAD_DIR

// 模块级日志前缀 - 统一格式
const moduleLogPrefix = `[文件：TestProcessingPipelineService.js][完整视频处理流水线服务][模块初始化]`;
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 流水线服务正确位于 pipelines/ 目录。`);
logger.info(`${moduleLogPrefix}[功能验证] 包含8个任务的完整视频处理、字幕生成和视频合成流水线。`);

/**
 * @类名: TestProcessingPipelineService
 * @继承: 无
 * @功能: 完整的视频处理流水线服务类
 */
class TestProcessingPipelineService {
    /**
     * @功能概述: 构造函数 - 初始化完整的视频处理流水线
     * @参数说明:
     *   - reqId: {string} [可选] 请求ID，用于日志追踪，默认为'unknown_test_req'
     * @执行流程:
     *   1. 初始化日志前缀和请求ID
     *   2. 创建PipelineBase实例
     *   3. 按顺序添加所有8个任务到流水线
     *   4. 记录任务序列信息
     */
    constructor(reqId = 'unknown_test_req') {
        this.reqId = reqId;
        this.logPrefix = `[文件：TestProcessingPipelineService.js][完整视频处理流水线服务][ReqID:${this.reqId}]`;

        // 创建流水线实例
        this.processingPipeline = new PipelineBase(`TestVideoProcessingPipeline-${this.reqId}`);

        // 按执行顺序统一添加所有任务
        this.addAllTasks();

        const taskNames = this.processingPipeline.tasks.map(task => task.name).join(' → ');
        logger.info(`${this.logPrefix}[TestVideoProcessingPipeline] Pipeline 已创建，包含任务序列: ${taskNames}`);
    }

    /**
     * @功能概述: 统一添加所有任务到流水线，按执行顺序排列
     * @执行顺序:
     *   1. ConvertToAudioTask - 视频转音频任务
     *   2. GetTranscriptionTask - 语音转录任务
     *   3. TranscriptionCorrectionTask - 转录校正任务
     *   4. TranslateSubtitleTask - 字幕翻译任务
     *   5. SubtitleClozeTask - 字幕挖空任务
     *   6. BilingualSubtitleMergeTask - 双语字幕合并增强任务
     *   7. GenerateASSTask - ASS字幕生成任务
     *   8. GenerateVideoTask - 9:16视频生成任务
     * @数据流转:
     *   每个任务的输出会自动传递给下一个任务作为输入，
     *   形成完整的视频处理、字幕生成和视频合成数据流水线。
     */
    addAllTasks() {
        this.addConvertToAudioTask();
        this.addGetTranscriptionTask();
        this.addTranscriptionCorrectionTask();
        this.addTranslateSubtitleTask();
        this.addSubtitleClozeTask();
        this.addBilingualSubtitleMergeTask();
        this.addGenerateASSTask();
        this.addGenerateVideoTask();
    }

    /**
     * @功能概述: 添加视频转音频任务 (ConvertToAudioTask) 到流水线。
     *           此任务负责从原始视频文件中提取音频，生成音频文件供后续转录使用。
     *
     * @上下文输入 (context 预期的字段 for ConvertToAudioTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - originalVideoPath: {string} (必需) 原始视频文件路径
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 输出的字段 from ConvertToAudioTask):
     *   - audioFilePath: {string} 提取的音频文件路径
     *   - audioDuration: {number} 音频时长（秒）
     *   - audioFormat: {string} 音频格式
     */
    addConvertToAudioTask() {
        this.processingPipeline.addTask(new ConvertToAudioTask());
    }

    /**
     * @功能概述: 添加语音转录任务 (GetTranscriptionTask) 到流水线。
     *           此任务负责调用语音转录服务，将音频文件转换为文本转录结果。
     *
     * @上下文输入 (context 预期的字段 for GetTranscriptionTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioTask
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 输出的字段 from GetTranscriptionTask):
     *   - apiResponse: {object} ASR原始响应数据
     *   - transcriptionStatus: {string} 转录状态
     *   - transcriptionText: {string} 转录文本
     */
    addGetTranscriptionTask() {
        this.processingPipeline.addTask(new GetTranscriptionTask());
    }

    /**
     * @功能概述: 添加转录校正任务 (TranscriptionCorrectionTask) 到流水线。
     *           此任务负责对语音转写任务的输出进行校正处理，
     *           包括调用LLM服务优化转录文本，并最终生成SRT格式的英文字幕。
     *
     * @上下文输入 (context 预期的字段 for TranscriptionCorrectionTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - apiResponse: {object} (必需) 语音转写服务返回的原始JSON响应数据，来自GetTranscriptionTask
     *   - transcriptionStatus: {string} (必需) 转录状态，来自GetTranscriptionTask
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 输出的字段 from TranscriptionCorrectionTask):
     *   - simplifiedSubtitleJsonArray: {Array} 英文字幕JSON数组
     *   - simplifiedSubtitleJsonPath: {string} 英文字幕JSON文件路径
     *   - englishSrtPath: {string} 英文SRT文件路径
     *   - correctedFullText: {string} 校正后的完整文本
     */
    addTranscriptionCorrectionTask() {
        this.processingPipeline.addTask(new TranscriptionCorrectionTask());
    }


    /**
     * @功能概述: 添加字幕翻译任务 (TranslateSubtitleTask) 到流水线。
     *           此任务负责将英文字幕翻译为中文字幕，生成双语字幕对照。
     *
     * @上下文输入 (context 预期的字段 for TranslateSubtitleTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     *
     * @上下文输出 (context 输出的字段 from TranslateSubtitleTask):
     *   - translatedSubtitleJsonArray: {Array} 翻译后的中文字幕JSON数组
     *   - translatedSubtitleJsonPath: {string} 中文字幕JSON文件路径
     *   - chineseSrtPath: {string} 中文SRT文件路径
     */
    addTranslateSubtitleTask() {
        this.processingPipeline.addTask(new TranslateSubtitleTask());
    }

    /**
     * @功能概述: 添加字幕挖空任务 (SubtitleClozeTask) 到流水线。
     *           此任务负责对英文字幕进行智能挖空处理，生成填空练习格式的字幕。
     *
     * @上下文输入 (context 预期的字段 for SubtitleClozeTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     */
    addSubtitleClozeTask() {
        this.processingPipeline.addTask(new SubtitleClozeTask());
    }

    /**
     * @功能概述: 添加双语字幕合并增强任务 (BilingualSubtitleMergeTask) 到流水线。
     *           此任务负责将英文、中文和挖空字幕合并，并通过LLM生成词汇解释。
     *
     * @上下文输入 (context 预期的字段 for BilingualSubtitleMergeTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - simplifiedSubtitleJsonArray: {Array} (必需) 来自TranscriptionCorrectionTask的英文字幕JSON数组
     *   - translatedSubtitleJsonArray: {Array} (必需) 来自TranslateSubtitleTask的中文字幕JSON数组
     *   - clozedSubtitleJsonArray: {Array} (必需) 来自SubtitleClozeTask的挖空字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *   - correctedFullText: {string} (可选) 完整上下文文本
     */
    addBilingualSubtitleMergeTask() {
        this.processingPipeline.addTask(new BilingualSubtitleMergeTask());
    }

    /**
     * @功能概述: 添加ASS字幕生成任务 (GenerateASSTask) 到流水线。
     *           此任务负责基于前序任务的输出生成完整的ASS格式字幕文件，
     *           包含视频标题、挖空字幕、双语字幕、单元引导字幕等所有组件。
     *
     * @上下文输入 (context 预期的字段 for GenerateASSTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - audioFilePath: {string} (必需) 音频文件路径，来自ConvertToAudioTask
     *   - clozedSubtitleJsonArray: {Array} (必需) 来自SubtitleClozeTask的挖空字幕JSON数组
     *   - enhancedBilingualSubtitleJsonArray: {Array} (必需) 来自BilingualSubtitleMergeTask的增强双语字幕JSON数组
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 输出的字段 from GenerateASSTask):
     *   - assFilePath: {string} 生成的ASS字幕文件路径
     *   - assContent: {string} 完整的ASS字幕内容
     *   - assContentLength: {number} ASS内容长度（字符数）
     *   - videoConfig: {object} 视频配置对象
     *   - audioDuration: {number} 音频时长（秒）
     *   - processedSubtitles: {object} 处理统计信息
     */
    addGenerateASSTask() {
        this.processingPipeline.addTask(new GenerateASSTask());
    }

    /**
     * @功能概述: 添加9:16视频生成任务 (GenerateVideoTask) 到流水线。
     *           此任务负责基于前序任务的输出生成完整的9:16比例视频文件，
     *           包含音频重复拼接、Canvas进度条生成、背景视频生成、ASS字幕烧录等功能。
     *
     * @上下文输入 (context 预期的字段 for GenerateVideoTask):
     *   - videoIdentifier: {string} (必需) 视频唯一标识符
     *   - originalVideoPath: {string} (必需) 原始视频文件路径
     *   - audioDuration: {number} (必需) 音频时长（秒），来自GenerateASSTask
     *   - videoConfig: {object} (必需) 视频配置对象，来自GenerateASSTask
     *   - assContent: {string} (必需) ASS字幕内容，来自GenerateASSTask
     *   - audioFilePath: {string} (必需) 音频文件路径
     *   - savePath: {string} (必需) 文件保存路径
     *
     * @上下文输出 (context 输出的字段 from GenerateVideoTask):
     *   - finalVideoPath: {string} 最终生成的9:16视频文件路径
     *   - videoGenerationStats: {object} 视频生成统计信息
     *   注意：临时文件（背景视频、进度条视频、扩展音频）在视频生成完成后自动删除
     */
    addGenerateVideoTask() {
        this.processingPipeline.addTask(new GenerateVideoTask());
    }

    /**
     * @功能概述: 执行完整的视频处理流水线
     * @方法名: processSubtitles
     * @参数说明:
     *   - initialContext: {object} 初始上下文，必须包含以下字段:
     *     - reqId: {string} 请求ID，用于日志追踪
     *     - videoIdentifier: {string} 视频唯一标识符
     *     - originalVideoPath: {string} 原始视频文件路径
     *     - savePath: {string} [可选] 文件保存路径，如果不提供则使用默认路径
     *   - serviceProgressCallback: {function} [可选] 服务进度回调函数，用于报告服务执行状态
     *
     * @返回值: {Promise<object>} Pipeline执行结果，包含以下字段:
     *   - status: {string} 执行状态 ('completed' | 'failed')
     *   - context: {object} 最终上下文，成功时包含:
     *     - audioFilePath: {string} 提取的音频文件路径
     *     - audioDuration: {number} 音频时长（秒）
     *     - apiResponse: {object} ASR原始响应数据
     *     - transcriptionStatus: {string} 转录状态
     *     - simplifiedSubtitleJsonArray: {Array} 英文字幕JSON数组
     *     - simplifiedSubtitleJsonPath: {string} 英文字幕JSON文件路径
     *     - englishSrtPath: {string} 英文SRT文件路径
     *     - translatedSubtitleJsonArray: {Array} 中文字幕JSON数组
     *     - translatedSubtitleJsonPath: {string} 中文字幕JSON文件路径
     *     - chineseSrtPath: {string} 中文SRT文件路径
     *     - clozedSubtitleJsonArray: {Array} 挖空字幕JSON数组
     *     - clozedSubtitleJsonPath: {string} 挖空字幕JSON文件路径
     *     - clozedSubtitleSrtPath: {string} 挖空字幕SRT文件路径
     *     - enhancedBilingualSubtitleJsonArray: {Array} 增强双语字幕JSON数组
     *     - enhancedBilingualSubtitleJsonPath: {string} 增强双语字幕JSON文件路径
     *     - assFilePath: {string} ASS字幕文件路径
     *     - assContent: {string} 完整的ASS字幕内容
     *     - assContentLength: {number} ASS内容长度（字符数）
     *     - videoConfig: {object} 视频配置对象
     *     - finalVideoPath: {string} 最终生成的9:16视频文件路径
     *     - videoGenerationStats: {object} 视频生成统计信息
     *   - tasks: {Array} 任务执行详情
     *   - error: {Error} [可选] 失败时的错误信息
     *
     * @执行流程:
     *   1. 构建执行上下文并验证必需参数
     *   2. 记录任务序列信息
     *   3. 执行8个任务的完整流水线
     *   4. 记录关键输出文件路径
     *   5. 返回执行结果
     *
     * @异常处理:
     *   - 参数校验失败时返回failed状态
     *   - 任务执行失败时记录错误并返回失败结果
     *   - 支持服务进度回调的错误处理
     */


    async processSubtitles(initialContext, serviceProgressCallback) {
        const methodLogPrefix = `${this.logPrefix}[processSubtitles]`;
        logger.info(`${methodLogPrefix} 开始执行完整视频处理流水线。`);

        // 步骤 1: 构建执行上下文
        // initialContext 包含了从测试传入的参数，包括硬编码的 savePath (如果存在)。
        // 通过展开 initialContext，savePath (如果存在于 initialContext 中) 会被复制到 currentContext。
        // 如果 initialContext 中没有 savePath，则 currentContext.savePath 将为 undefined，
        // 这将导致任务最终调用 fileSaver 时，fileSaver 使用其内部的默认路径。
        const currentContext = {
            ...initialContext,
            reqId: initialContext.reqId || this.reqId, // 确保reqId存在
            // config 对象可以保留，因为它可能用于任务内部的其他配置项，与 savePath 分开处理。
            config: {
                UPLOAD_DIR: config.UPLOAD_DIR
            }
            // 不再显式设置 currentContext.savePath 来覆盖或提供默认值，
            // 而是依赖 initialContext 中是否已定义 savePath。
        };

        // 步骤 2: 参数校验（更新为新的必需字段）
        const requiredFields = ['videoIdentifier', 'originalVideoPath'];
        for (const field of requiredFields) {
            if (!currentContext[field]) {
                const errorMsg = `processSubtitles 调用失败：initialContext 必须包含 '${field}'。`;
                logger.error(`${methodLogPrefix} ${errorMsg}`);

                if (serviceProgressCallback && typeof serviceProgressCallback === 'function') {
                    try {
                        serviceProgressCallback({
                            serviceName: 'TestProcessingPipelineService',
                            methodName: 'processSubtitles',
                            status: 'failed_setup',
                            error: { message: errorMsg },
                            timestamp: new Date().toISOString()
                        });
                    } catch (cbError) {
                        logger.error(`${methodLogPrefix} 服务进度回调执行出错: ${cbError.message}`);
                    }
                }
                return {
                    status: 'failed',
                    error: new Error(errorMsg),
                    context: currentContext,
                    tasks: []
                };
            }
        }
        logger.info(`${methodLogPrefix} 参数校验通过。Initial context: ${JSON.stringify(Object.keys(currentContext))}`);



        // 步骤 3: 记录任务序列
        const taskNames = this.processingPipeline.tasks.map(task => task.name).join(' → ');
        logger.info(`${methodLogPrefix}[步骤 1] 流水线任务序列确认: ${taskNames}`);
        
        // 步骤 4: 执行流水线
        logger.debug(`${methodLogPrefix} 执行内部流水线...`);
        const result = await this.processingPipeline.execute(currentContext, serviceProgressCallback);
        logger.info(`${methodLogPrefix} 完整视频处理流水线执行完毕。Status: ${result.status}`);
        
        // 步骤 5: 记录关键输出
        if (result.status === 'completed' && result.context) {
            const {
                audioFilePath,
                audioDuration,
                apiResponse,
                transcriptionStatus,
                simplifiedSubtitleJsonPath,
                englishSrtPath,
                translatedSubtitleJsonPath,
                chineseSrtPath,
                clozedSubtitleJsonPath,
                clozedSubtitleSrtPath,
                enhancedBilingualSubtitleJsonPath,
                assFilePath,
                finalVideoPath,
                videoGenerationStats
            } = result.context;
            logger.info(`${methodLogPrefix} 完整视频处理流水线成功完成。关键产物路径:
            - Audio File: ${audioFilePath}
            - Audio Duration: ${audioDuration}s
            - Transcription Status: ${transcriptionStatus}
            - Simplified JSON: ${simplifiedSubtitleJsonPath}
            - English SRT: ${englishSrtPath}
            - Translated JSON: ${translatedSubtitleJsonPath}
            - Chinese SRT: ${chineseSrtPath}
            - Clozed JSON: ${clozedSubtitleJsonPath}
            - Clozed SRT: ${clozedSubtitleSrtPath}
            - Enhanced Bilingual JSON: ${enhancedBilingualSubtitleJsonPath}
            - ASS Subtitle File: ${assFilePath}
            - Final Video File: ${finalVideoPath}
            - Video Generation Stats: ${JSON.stringify(videoGenerationStats)}`);
        } else if (result.status === 'failed') {
            logger.error(`${methodLogPrefix} 完整视频处理流水线执行失败。Error: ${result.error?.message}`);
        }
        
        return result;
    }
}

module.exports = TestProcessingPipelineService;
logger.info(`${moduleLogPrefix}TestProcessingPipelineService 类已导出。`);