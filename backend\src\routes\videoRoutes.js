/**
 * @功能概述: 定义视频相关的 API 路由。
 * @架构设计: 重构后使用专用控制器，遵循单一职责原则
 * @重构时间: 2025-06-12
 * @重构说明: 将原来的单一videoController拆分为3个专用控制器，提高可维护性
 */

// Node.js 基础知识：模块系统
// 在 Node.js 中，每个文件都是一个模块。通过 require 函数可以导入其他模块导出的功能（变量、函数、对象等）。
// 这种模块化的设计有助于组织代码、避免全局命名冲突以及提高代码的可重用性。
// CommonJS 是 Node.js 默认使用的模块规范，require 函数就是 CommonJS 规范的一部分。

const express = require('express'); // require('express') 的作用：
// 1. 查找并加载名为 'express' 的已安装模块。Express 是一个流行的 Node.js Web 应用框架。
// 2. require 函数执行 Express 模块的主文件，并返回该模块通过 module.exports 导出的内容。
// 3. 在这里，它返回 Express 框架的应用创建函数或对象。

const router = express.Router(); // express.Router() 的作用：
// 1. 这是 Express 框架提供的一个核心功能，用于创建一个新的路由对象（Router 实例）。
// 2. Router 实例是一个迷你型的 Express 应用，它可以定义自己的中间件和路由（如 GET, POST 等）。
// 3. 使用 Router 的好处是可以将特定路径下的路由逻辑分组到单独的文件中，使代码更模块化、更易于管理。
// 4. 这个 router 对象可以像完整的 Express 应用 (app) 一样使用 app.get(), app.post(), app.use() 等方法。
// 5. 最后，这个 Router 实例可以作为一个中间件被挂载到主 Express 应用 (app) 的某个特定路径下（例如， app.use('/api/video', videoRoutes);），这样只有访问该路径下的请求才会进入这个 Router 中定义的路由处理。

// === 导入专用控制器（分类架构） ===
// 重构说明：将原来的单一videoController拆分为多个专用控制器，并按功能类目分组
const uploadVideoController = require('../controllers/video/uploadVideoController'); // 专门处理视频上传
const generateVideoController = require('../controllers/video/generateVideoController'); // 专门处理视频生成
const taskStatusController = require('../controllers/video/taskStatusController'); // 专门处理状态查询
const listProjectsController = require('../controllers/video/listProjectsController'); // 专门处理项目列表
const projectDetailsController = require('../controllers/video/projectDetailsController'); // 专门处理项目详情
const deleteProjectController = require('../controllers/video/deleteProjectController'); // 专门处理项目删除
const listGeneratedFilesController = require('../controllers/video/listGeneratedFilesController'); // 专门处理generated文件查询
const videoTagsController = require('../controllers/video/videoTagsController'); // 专门处理视频标注
const renameProjectController = require('../controllers/video/renameProjectController'); // 专门处理项目重命名
const getVideoConfigController = require('../controllers/video/getVideoConfigController'); // 专门处理视频配置获取


const uploadMiddleware = require('../middleware/uploadMiddleware'); // require('../middleware/uploadMiddleware') 的作用：
// 1. 类似地，使用相对路径 '../middleware/uploadMiddleware' 导入位于上一级目录 (..) 下的 middleware 目录中的 uploadMiddleware.js 文件。
// 2. 返回 uploadMiddleware.js 文件通过 module.exports 导出的内容（通常是一个 Express 中间件函数）。
// 3. 将返回的中间件函数赋值给 uploadMiddleware 变量，以便在路由定义中作为处理函数使用。

const { videoGenerationLoggingMiddleware } = require('../middleware/loggingMiddleware'); // 导入视频生成专用日志中间件
const { generateVideo, videoGenerationProgress } = require('../controllers/video/generateVideoController');
const { checkTaskStatus } = require('../controllers/video/taskStatusController');


/**
 * @功能概述: 处理视频文件上传并启动处理流程。
 * @route POST /api/video/uploadVideo
 * @controller uploadVideoController - 专用上传控制器
 * @param {object} req - Express 请求对象。
 * @param {object} res - Express 响应对象。
 * @命名一致性: 接口路径 /uploadVideo 与控制器 uploadVideoController 完全对应
 * @重构说明: 使用专用的uploadVideoController替代原来的videoController.uploadVideo
 */
router.post('/uploadVideo',
    uploadMiddleware, // 添加文件上传中间件
    uploadVideoController.uploadVideo // 使用专用上传控制器处理函数
);

/**
 * @功能概述: 处理视频生成请求，根据时间片段和裁剪参数生成新视频。
 * @route POST /api/video/generateVideo
 * @controller generateVideoController - 专用生成控制器
 * @middleware videoGenerationLoggingMiddleware - 视频生成专用日志中间件
 * @param {object} req - Express 请求对象，包含生成参数。
 * @param {object} res - Express 响应对象。
 * @命名一致性: 接口路径 /generateVideo 与控制器 generateVideoController 完全对应
 * @重构说明: 使用专用的generateVideoController替代原来的videoController.generateVideo
 */
router.post('/generateVideo',
    videoGenerationLoggingMiddleware, // 添加视频生成专用日志中间件
    generateVideoController.generateVideo // 使用专用生成控制器处理函数
);

/**
 * @功能概述: 检查任务状态，用于SSE连接断开后的状态查询。
 * @route GET /api/video/taskStatus/:reqId
 * @controller taskStatusController - 专用状态查询控制器
 * @param {object} req - Express 请求对象，包含reqId参数。
 * @param {object} res - Express 响应对象。
 * @命名一致性: 接口路径 /taskStatus 与控制器 taskStatusController 完全对应
 * @重构说明: 使用专用的taskStatusController替代原来的videoController.checkTaskStatus
 */
router.get('/taskStatus/:reqId', checkTaskStatus);

/**
 * @功能概述: 获取已上传项目列表，支持分页和排序。
 * @route GET /api/video/listProjects
 * @controller listProjectsController - 专用项目列表控制器
 * @param {object} req - Express 请求对象，包含查询参数。
 * @param {object} res - Express 响应对象。
 * @查询参数:
 *   - page (可选): 页码，默认1
 *   - pageSize (可选): 每页数量，默认10，最大50
 *   - sortBy (可选): 排序字段，默认uploadTime
 *   - order (可选): 排序方向，默认desc
 * @命名一致性: 接口路径 /listProjects 与控制器 listProjectsController 完全对应
 * @新增说明: 支持"选择已有视频"功能的项目列表获取
 */
router.get('/listProjects', listProjectsController.listProjects);

/**
 * @功能概述: 获取项目详细信息，包括完整的editorData格式数据。
 * @route GET /api/video/projectDetails
 * @controller projectDetailsController - 专用项目详情控制器
 * @param {object} req - Express 请求对象，包含查询参数。
 * @param {object} res - Express 响应对象。
 * @查询参数:
 *   - videoIdentifier (必需): 项目标识符
 * @命名一致性: 接口路径 /projectDetails 与控制器 projectDetailsController 完全对应
 * @新增说明: 支持"选择已有视频"功能的项目详情获取，返回可直接用于编辑器的数据格式
 */
router.get('/projectDetails', projectDetailsController.projectDetails);

/**
 * @功能概述: 删除指定的视频项目及其所有相关文件。
 * @route DELETE /api/video/deleteProject
 * @controller deleteProjectController - 专用项目删除控制器
 * @param {object} req - Express 请求对象，包含请求体参数。
 * @param {object} res - Express 响应对象。
 * @请求体参数:
 *   - videoIdentifier (必需): 要删除的项目标识符
 * @命名一致性: 接口路径 /deleteProject 与控制器 deleteProjectController 完全对应
 * @安全性: 包含路径遍历攻击防护和项目存在性验证
 * @功能说明: 彻底删除项目目录及其所有内容，包括source、processed、generated等子目录
 */
router.delete('/deleteProject', deleteProjectController.deleteProject);

/**
 * @功能概述: 查询指定项目的generated目录文件
 * @route GET /api/video/listGeneratedFiles/:projectId
 * @controller listGeneratedFilesController - 专用generated文件查询控制器
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 * @路径参数:
 *   - projectId (必需): 项目唯一标识符
 * @查询参数:
 *   - limit (可选): 返回数量限制，默认20
 *   - sortBy (可选): 排序方式，默认time_desc
 * @命名一致性: 接口路径 /listGeneratedFiles 与控制器 listGeneratedFilesController 完全对应
 * @功能说明: 扫描项目generated目录，识别extended_video和enhanced_bilingual_subtitle文件，建立配对关系
 * @安全性: 包含projectId格式验证和路径遍历攻击防护
 */
router.get('/listGeneratedFiles/:projectId', listGeneratedFilesController.listGeneratedFiles);

/**
 * @功能概述: 更新视频标注信息
 * @route PUT /api/video/updateVideoTags/:projectId
 * @controller videoTagsController - 专用视频标注控制器
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 * @路径参数:
 *   - projectId (必需): 项目唯一标识符
 * @请求体参数:
 *   - videos (必需): 视频标注数据对象，格式如 {"video_v1.mp4": {"tags": ["已发布"], "status": "published"}}
 * @功能说明: 在项目的generated目录中创建或更新tags.json文件，存储视频标注信息
 * @安全性: 包含projectId格式验证和路径遍历攻击防护
 */
router.put('/updateVideoTags/:projectId', videoTagsController.updateVideoTags);

/**
 * @功能概述: 获取视频标注信息
 * @route GET /api/video/getVideoTags/:projectId
 * @controller videoTagsController - 专用视频标注控制器
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 * @路径参数:
 *   - projectId (必需): 项目唯一标识符
 * @功能说明: 读取项目generated目录中的tags.json文件，返回视频标注信息
 * @安全性: 包含projectId格式验证和路径遍历攻击防护
 */
router.get('/getVideoTags/:projectId', videoTagsController.getVideoTags);

/**
 * @功能概述: 重命名项目文件名
 * @route PUT /api/video/renameProject
 * @controller renameProjectController - 专用项目重命名控制器
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 * @请求体参数:
 *   - videoIdentifier (必需): 项目唯一标识符
 *   - newFileName (必需): 新的文件名（不包含扩展名）
 * @功能说明: 修改项目source目录下的原始视频文件名，不修改扩展名，同时更新项目元数据
 * @安全性: 包含文件名合法性验证和文件冲突检查
 * @命名一致性: 接口路径 /renameProject 与控制器 renameProjectController 完全对应
 */
router.put('/renameProject', renameProjectController.renameProject);

/**
 * @功能概述: 获取视频配置参数，为前端提供动态配置
 * @route GET /api/video/getVideoConfig
 * @controller getVideoConfigController - 专用视频配置获取控制器
 * @param {object} req - Express 请求对象
 * @param {object} res - Express 响应对象
 * @请求参数: 无需参数
 * @响应格式: JSON，包含前端需要的视频配置
 * @功能说明: 读取后端video-config.json配置文件，提取并转换为前端兼容的配置格式
 * @配置项:
 *   - repeatCount: 重复次数
 *   - backgroundStyle: 背景风格
 *   - repeatModes: 重复模式数组（从subtitleConfig中提取到根级别）
 *   - subtitleConfig.videoGuide: 视频引导配置
 *   - subtitleConfig.advertisement: 广告配置
 * @命名一致性: 接口路径 /getVideoConfig 与控制器 getVideoConfigController 完全对应
 * @架构意义: 解决前后端配置不一致问题，建立统一的配置管理机制
 */
router.get('/getVideoConfig', getVideoConfigController);

// 注释：getAllTaskStatus 功能暂未实现，如需要可以在 taskStatusController.js 中添加

module.exports = router;