# useCurrentFrame()

## 概述

`useCurrentFrame()` 是 Remotion 的核心 Hook，用于获取当前视频的帧数。帧数从 0 开始索引，第一帧是 `0`，最后一帧是组合持续时间减 1。

## 语法

```typescript
import { useCurrentFrame } from "remotion";

const frame = useCurrentFrame();
```

## 返回值

- **类型**: `number`
- **描述**: 当前帧数（0 索引）

## 重要特性

### 1. 相对帧数计算

如果组件被 `<Sequence>` 包装，`useCurrentFrame()` 将返回相对于序列开始时间的帧数。

### 2. 时间轴位置

假设时间轴标记位于第 25 帧：

```typescript
import { Sequence, useCurrentFrame } from "remotion";

const Title = () => {
  const frame = useCurrentFrame(); // 返回 25
  return <div>{frame}</div>;
};

const Subtitle = () => {
  const frame = useCurrentFrame(); // 返回 15 (25 - 10)
  return <div>{frame}</div>;
};

const MyVideo = () => {
  const frame = useCurrentFrame(); // 返回 25
  return (
    <div>
      <Title />
      <Sequence from={10}>
        <Subtitle />
      </Sequence>
    </div>
  );
};
```

## 使用场景

### 1. 基础动画

```typescript
import { useCurrentFrame, interpolate } from "remotion";

const AnimatedComponent = () => {
  const frame = useCurrentFrame();
  
  // 创建淡入效果
  const opacity = interpolate(frame, [0, 30], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });
  
  return (
    <div style={{ opacity }}>
      淡入动画
    </div>
  );
};
```

### 2. 条件渲染

```typescript
import { useCurrentFrame } from "remotion";

const ConditionalComponent = () => {
  const frame = useCurrentFrame();
  
  // 在第 60 帧后显示内容
  if (frame < 60) {
    return null;
  }
  
  return <div>60帧后显示的内容</div>;
};
```

### 3. 复杂动画序列

```typescript
import { useCurrentFrame, interpolate } from "remotion";

const ComplexAnimation = () => {
  const frame = useCurrentFrame();
  
  // 多阶段动画
  const scale = interpolate(
    frame,
    [0, 30, 60, 90],
    [0, 1.2, 1, 1.1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  const rotation = interpolate(
    frame,
    [0, 90],
    [0, 360],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  return (
    <div
      style={{
        transform: `scale(${scale}) rotate(${rotation}deg)`,
        transition: 'transform 0.1s ease',
      }}
    >
      复杂动画元素
    </div>
  );
};
```

## 获取绝对帧数

在序列内部获取时间轴的绝对帧数：

```typescript
const Subtitle: React.FC<{ absoluteFrame: number }> = ({ absoluteFrame }) => {
  console.log(useCurrentFrame()); // 15 (相对帧数)
  console.log(absoluteFrame); // 25 (绝对帧数)
  return null;
};

const MyVideo = () => {
  const frame = useCurrentFrame(); // 25
  return (
    <Sequence from={10}>
      <Subtitle absoluteFrame={frame} />
    </Sequence>
  );
};
```

## 最佳实践

### 1. 性能优化

```typescript
import { useCurrentFrame, useMemo } from "remotion";

const OptimizedComponent = () => {
  const frame = useCurrentFrame();
  
  // 使用 useMemo 缓存复杂计算
  const expensiveValue = useMemo(() => {
    return someExpensiveCalculation(frame);
  }, [frame]);
  
  return <div>{expensiveValue}</div>;
};
```

### 2. 帧数验证

```typescript
import { useCurrentFrame, useVideoConfig } from "remotion";

const SafeComponent = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  
  // 确保帧数在有效范围内
  const safeFrame = Math.max(0, Math.min(frame, durationInFrames - 1));
  
  return <div>安全帧数: {safeFrame}</div>;
};
```

## 注意事项

1. **帧数从 0 开始**: 第一帧是 0，不是 1
2. **序列相对性**: 在 `<Sequence>` 内部，帧数是相对的
3. **性能考虑**: 每帧都会重新渲染，避免在每帧进行昂贵的计算
4. **类型安全**: 返回值始终是 `number` 类型

## 相关 API

- [`useVideoConfig()`](./useVideoConfig.md) - 获取视频配置
- [`interpolate()`](./interpolate.md) - 值插值
- [`<Sequence>`](./Sequence.md) - 时间序列
- [`spring()`](./spring.md) - 物理动画

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/use-current-frame.ts)
