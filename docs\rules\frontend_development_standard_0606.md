# 前端开发标准

本规则为开发者制定了细粒度的前端开发标准，包括技术栈选择、模块化架构、开发流程和代码质量要求。

## 技术栈标准

### 必须使用的技术栈
- **Vue 3 (CDN版本)** - 主要前端框架，必须使用Composition API
- **Element Plus (CDN版本)** - UI组件库，所有UI组件必须使用Element Plus
- **原生JavaScript** - 业务逻辑实现，禁止使用其他JS框架
- **Cropper.js** - 图像裁剪功能，版本必须为v1.5.13

### CDN依赖引入标准
开发时必须按以下顺序引入CDN依赖：

```html
<!-- 1. CSS文件必须在head中引入 -->
<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css" rel="stylesheet">

<!-- 2. JavaScript文件必须在body结束前引入 -->
<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
<script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
```

## 模块化架构标准

### 文件组织规范
开发时必须按以下目录结构组织代码：

```
@frontend-test/
├── @index.html              # HTML模板和应用启动
├── @test.html               # 功能测试页面（可选）
├── @css/main.css           # 样式文件
└── @js/
    ├── @utils.js           # 工具函数库
    ├── @video-editor.js    # 视频编辑器模块
    └── @app.js             # Vue应用主入口
```

### 模块职责分工标准

#### 第一步：确定功能归属
开发新功能时，必须首先确定功能属于哪个模块：

| 功能类型 | 目标文件 | 判断标准 |
|---------|---------|---------|
| 数据处理、格式化、API调用 | @utils.js | 无状态的纯函数，可复用的工具函数 |
| 视频编辑、播放控制、状态管理 | @video-editor.js | 与视频编辑器直接相关的业务逻辑 |
| 应用级别功能、文件上传、导航 | @app.js | Vue应用的整体协调和生命周期管理 |
| HTML结构、样式、模板 | @index.html | 静态结构和应用启动逻辑 |

#### 第二步：模块开发规范

##### @utils.js 开发规范
- **职责**：提供无状态的工具函数
- **禁止**：包含任何Vue响应式状态
- **必须**：每个函数都是纯函数，相同输入产生相同输出
- **导出格式**：
```javascript
window.Utils = {
    functionName1,
    functionName2
};
```

##### @video-editor.js 开发规范
- **职责**：管理视频编辑器的状态和业务逻辑
- **必须**：使用工厂函数模式创建功能模块
- **禁止**：直接操作DOM元素
- **导出格式**：
```javascript
window.VideoEditor = {
    createVideoEditorState,
    createVideoEditorComputed,
    createVideoPlaybackFunctions
};
```

##### @app.js 开发规范
- **职责**：Vue应用的初始化和整体协调
- **必须**：整合各个模块的功能
- **必须**：处理应用级别的状态管理
- **导出格式**：
```javascript
window.AppCore = {
    startApp
};
```

## 开发流程标准

### 第一步：功能需求分析
接到开发任务时，必须按以下步骤分析：

1. **功能性质判断**：
   - 是否涉及数据处理？→ @utils.js
   - 是否涉及视频编辑？→ @video-editor.js
   - 是否涉及应用级别功能？→ @app.js
   - 是否涉及HTML结构？→ @index.html

2. **依赖关系分析**：
   - 需要调用哪些现有函数？
   - 需要访问哪些状态变量？
   - 需要与哪些模块交互？

### 第二步：函数开发标准

#### 函数命名规范
必须按以下规范命名函数：

```javascript
// 事件处理函数
const handle[EventName] = () => {};

// 状态切换函数
const toggle[StateName] = () => {};

// 初始化函数
const init[ComponentName] = () => {};

// 销毁函数
const destroy[ComponentName] = () => {};

// 创建函数
const create[ObjectName] = () => {};

// 工具函数（动词开头）
const format[DataType] = () => {};
const validate[DataType] = () => {};
const parse[DataType] = () => {};
```

#### 函数结构标准
每个函数必须按以下结构编写：

```javascript
/**
 * @功能概述: [一句话描述函数功能]
 * @参数说明: {type} paramName - 参数描述
 * @返回值: {type} 返回值描述
 * @调用关系: 由[调用者]调用，调用[被调用者]
 * @状态影响: 影响[状态名称]状态
 */
const functionName = (param) => {
    const logPrefix = '[文件：filename.js][functionName]';
    console.log(`${logPrefix} 开始执行`);

    // 1. 参数验证（必须）
    if (!param) {
        console.error(`${logPrefix} 参数验证失败`);
        throw new Error('参数不能为空');
    }

    try {
        // 2. 主要逻辑
        const result = processLogic(param);

        // 3. 成功日志
        console.log(`${logPrefix} 执行成功`);
        return result;

    } catch (error) {
        // 4. 错误处理（必须）
        console.error(`${logPrefix} 执行失败:`, error);
        throw error;
    }
};
```

### 第三步：模块导出标准

#### 导出接口规范
每个模块文件末尾必须按以下格式导出：

```javascript
// 导出模块到全局对象
window.[ModuleName] = {
    // 按字母顺序排列
    functionA,
    functionB,
    functionC
};

// 模块加载确认日志
console.log('[文件：filename.js] [模块名称]模块已加载');
```

#### 模块加载顺序
在@index.html中必须按以下顺序加载模块：

```html
<!-- 1. 工具函数库（无依赖） -->
<script src="./js/utils.js"></script>

<!-- 2. 视频编辑器模块（依赖utils） -->
<script src="./js/video-editor.js"></script>

<!-- 3. 应用主入口（依赖前两个模块） -->
<script src="./js/app.js"></script>
```

### 第四步：Vue应用集成标准

#### 在@app.js中集成新功能
开发的功能需要在Vue应用中使用时，必须按以下步骤集成：

```javascript
// 在app.js的setup函数中
setup() {
    // 1. 获取各模块功能
    const utilsFunctions = window.Utils;
    const videoEditorFunctions = window.VideoEditor;

    // 2. 创建状态（如果需要）
    const state = window.VideoEditor.createVideoEditorState();

    // 3. 创建业务函数
    const handleNewFeature = () => {
        try {
            // 调用新开发的函数
            const result = window.Utils.newFunction();
            console.log('功能执行成功:', result);
        } catch (error) {
            console.error('功能执行失败:', error);
            Vue.ElMessage.error(`操作失败: ${error.message}`);
        }
    };

    // 4. 返回给模板使用
    return {
        handleNewFeature
    };
}
```

### 第五步：测试验证标准

#### 功能测试要求
功能开发完成后，必须进行以下测试：

1. **模块加载测试**：
```javascript
// 在浏览器控制台检查
console.log(window.Utils);      // 应该显示工具函数对象
console.log(window.VideoEditor); // 应该显示视频编辑器对象
console.log(window.AppCore);     // 应该显示应用核心对象
```

2. **函数调用测试**：
```javascript
// 测试新开发的函数
try {
    const result = window.Utils.newFunction('test');
    console.log('✅ 函数测试成功:', result);
} catch (error) {
    console.error('❌ 函数测试失败:', error);
}
```

3. **集成测试**：
   - 在@test.html中添加测试按钮
   - 验证函数在Vue应用中正常工作
   - 检查错误处理是否正确

## API调用标准

### 通用SSE API调用规范

#### 第一步：选择合适的API调用方式
根据API类型选择对应的调用函数：

| API类型 | 调用函数 | 使用场景 |
|---------|---------|---------|
| 视频生成 | `callVideoGenerateAPI(params, state)` | 视频处理流水线 |
| 视频上传 | `callVideoUploadAPI(formData, state)` | 文件上传处理 |
| 通用SSE | `callSSEAPI(url, options, state, apiName)` | 自定义SSE接口 |
| 状态查询 | `callStatusAPI(reqId)` | 任务状态查询 |

#### 第二步：标准调用模式
所有SSE API调用必须按以下模式进行：

```javascript
/**
 * 标准SSE API调用模式
 */
const handleAPICall = async (params, state) => {
    const logPrefix = '[文件：filename.js][handleAPICall]';

    try {
        // 1. 设置加载状态
        state.isGenerating.value = true;

        // 2. 调用API（自动处理SSE事件）
        const result = await window.Utils.callVideoGenerateAPI(params, state);
        console.log(`${logPrefix} API调用完成:`, result);

        // 3. 启动状态轮询（作为SSE备选方案）
        const reqId = params.videoIdentifier || result.reqId;
        window.Utils.globalStatusPollingManager.startPolling(
            reqId,
            state,
            5000,  // 5秒轮询间隔
            'APIName'
        );

        // 4. 显示成功消息
        ElMessage.success('请求已提交，正在处理...');

    } catch (error) {
        console.error(`${logPrefix} API调用失败:`, error);
        state.isGenerating.value = false;
        ElMessage.error(error.message);
    }
};
```

#### 第三步：自动状态管理
使用全局状态轮询管理器处理任务状态：

```javascript
// 启动轮询（在API调用后）
window.Utils.globalStatusPollingManager.startPolling(reqId, state, interval, apiName);

// 停止特定轮询
window.Utils.globalStatusPollingManager.stopPolling(reqId);

// 停止所有轮询（页面卸载时）
window.Utils.globalStatusPollingManager.stopAllPolling();

// 查看活跃轮询
const activePollings = window.Utils.globalStatusPollingManager.getActivePollings();
```

#### 第四步：SSE事件处理
SSE事件会自动调用`handleSSEMessage`函数，无需手动处理：

```javascript
// 在app.js中定义handleSSEMessage（已存在）
const handleSSEMessage = (data, state) => {
    // 自动处理所有SSE事件
    // 包括：task_start, task_progress, task_complete, pipeline_complete等
};

// 导出到全局（已存在）
window.AppCore = {
    handleSSEMessage,
    // 其他函数...
};
```

### 新手快速上手指南

#### 步骤1：准备参数
```javascript
// 视频生成API参数示例
const generateParams = {
    videoIdentifier: 'unique-id',
    // 其他必需参数...
};

// 视频上传API参数示例
const formData = new FormData();
formData.append('video', fileInput.files[0]);
```

#### 步骤2：调用API
```javascript
// 在Vue组件的事件处理函数中
const handleGenerate = async () => {
    try {
        // 调用API，传入state对象启用自动SSE处理
        const result = await window.Utils.callVideoGenerateAPI(generateParams, state);

        // 启动轮询备选方案
        window.Utils.globalStatusPollingManager.startPolling(
            generateParams.videoIdentifier,
            state,
            5000,
            'VideoGenerate'
        );

    } catch (error) {
        ElMessage.error(error.message);
    }
};
```

#### 步骤3：处理完成事件
```javascript
// 任务完成时会自动：
// 1. 停止轮询
// 2. 设置 state.isGenerating.value = false
// 3. 调用 handleSSEMessage 处理完成事件
// 4. 显示完成消息

// 无需手动处理，系统自动管理
```

### 常见错误及解决方案

#### 错误1：忘记传递state参数
```javascript
// ❌ 错误：没有传递state参数
const result = await window.Utils.callVideoGenerateAPI(params);

// ✅ 正确：传递state参数启用自动SSE处理
const result = await window.Utils.callVideoGenerateAPI(params, state);
```

#### 错误2：忘记启动轮询备选方案
```javascript
// ❌ 错误：只依赖SSE，没有轮询备选
const result = await window.Utils.callVideoGenerateAPI(params, state);

// ✅ 正确：启动轮询作为备选方案
const result = await window.Utils.callVideoGenerateAPI(params, state);
window.Utils.globalStatusPollingManager.startPolling(reqId, state, 5000, 'APIName');
```

#### 错误3：忘记清理资源
```javascript
// ✅ 正确：在页面卸载时清理所有轮询
onUnmounted(() => {
    window.Utils.globalStatusPollingManager.stopAllPolling();
});
```

## 代码质量标准

### 必须遵循的编码规范

#### 1. 错误处理规范
每个函数必须包含完整的错误处理：

```javascript
const functionName = (param) => {
    const logPrefix = '[文件：filename.js][functionName]';

    try {
        // 参数验证
        if (!param) {
            throw new Error('参数不能为空');
        }

        // 主要逻辑
        const result = processData(param);
        return result;

    } catch (error) {
        console.error(`${logPrefix} 执行失败:`, error);

        // 根据错误类型提供用户友好的提示
        if (error.name === 'TypeError') {
            throw new Error('数据类型错误，请检查输入参数');
        } else if (error.message.includes('网络')) {
            throw new Error('网络连接失败，请检查网络设置');
        } else {
            throw new Error(`操作失败: ${error.message}`);
        }
    }
};
```

#### 2. 日志记录规范
所有函数必须使用统一的日志格式：

```javascript
const logPrefix = '[文件：filename.js][functionName]';

// 开始执行日志
console.log(`${logPrefix} 开始执行，参数:`, param);

// 关键步骤日志
console.log(`${logPrefix} 正在处理数据...`);

// 成功日志
console.log(`${logPrefix} 执行成功，结果:`, result);

// 错误日志
console.error(`${logPrefix} 执行失败:`, error);
```

#### 3. 参数验证规范
所有函数必须对输入参数进行严格验证：

```javascript
const functionName = (param1, param2) => {
    const logPrefix = '[文件：filename.js][functionName]';

    // 必须：参数类型验证
    if (typeof param1 !== 'string') {
        throw new Error('param1必须是字符串类型');
    }

    // 必须：参数值验证
    if (!param1 || param1.trim() === '') {
        throw new Error('param1不能为空');
    }

    // 必须：参数范围验证（如适用）
    if (typeof param2 === 'number' && (param2 < 0 || param2 > 100)) {
        throw new Error('param2必须在0-100范围内');
    }

    console.log(`${logPrefix} 参数验证通过`);
};
```

## 常见错误及避免方法

### 错误1：模块选择错误
**错误示例**：将API调用函数放在@video-editor.js中
**正确做法**：API调用函数应该放在@utils.js中
**判断标准**：API调用是无状态的工具函数

### 错误2：忘记更新导出接口
**错误示例**：开发了新函数但忘记添加到window导出对象
**正确做法**：每次添加函数后立即更新window导出
**检查方法**：在浏览器控制台验证函数是否可访问

### 错误3：缺少错误处理
**错误示例**：函数没有try-catch包装
**正确做法**：所有函数必须包含完整的错误处理
**标准模板**：使用上述提供的函数结构标准

### 错误4：日志格式不统一
**错误示例**：使用console.log('错误')
**正确做法**：使用统一的日志前缀格式
**标准格式**：`[文件：filename.js][functionName]`







## 检查清单

### 开发前检查
- [ ] 已确定功能属于哪个模块（@utils.js / @video-editor.js / @app.js）
- [ ] 已了解相关模块的现有函数和结构
- [ ] 已确定函数命名符合规范

### 开发中检查
- [ ] 函数包含完整的JSDoc注释
- [ ] 函数包含参数验证逻辑
- [ ] 函数包含错误处理机制
- [ ] 使用统一的日志前缀格式
- [ ] 函数职责单一，逻辑清晰

### 开发后检查
- [ ] 已更新模块的window导出对象
- [ ] 在浏览器控制台验证函数可访问
- [ ] 在@app.js中正确集成（如需要）
- [ ] 使用@test.html验证功能正常
- [ ] 错误处理和用户提示正常工作

## 快速参考

### 模块选择速查表
```
数据处理、格式化、API调用 → @utils.js
视频编辑、播放控制、状态管理 → @video-editor.js
应用级别功能、文件上传、导航 → @app.js
HTML结构、样式、模板 → @index.html
```

### API调用速查表
```javascript
// 视频生成API
await window.Utils.callVideoGenerateAPI(params, state);

// 视频上传API
await window.Utils.callVideoUploadAPI(formData, state);

// 通用SSE API
await window.Utils.callSSEAPI(url, options, state, apiName);

// 状态查询API
await window.Utils.callStatusAPI(reqId);

// 启动状态轮询
window.Utils.globalStatusPollingManager.startPolling(reqId, state, 5000, 'APIName');

// 停止所有轮询
window.Utils.globalStatusPollingManager.stopAllPolling();
```

### 函数命名速查表
```
handle[EventName]    - 事件处理函数
toggle[StateName]    - 状态切换函数
init[ComponentName]  - 初始化函数
destroy[ComponentName] - 销毁函数
create[ObjectName]   - 创建函数
format[DataType]     - 格式化函数
validate[DataType]   - 验证函数
parse[DataType]      - 解析函数
```

### 日志前缀格式
```
const logPrefix = '[文件：filename.js][functionName]';
```

### 导出格式模板
```javascript
window.[ModuleName] = {
    functionA,
    functionB,
    functionC
};
console.log('[文件：filename.js] [模块名称]模块已加载');
```
