# BilingualSubtitleMergeTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名和日志追踪
- **simplifiedSubtitleJsonArray** (Array): 来自TranscriptionCorrectionTask的英文字幕JSON数组
  - 结构: `[{id, start, end, text, words}, ...]`
- **translatedSubtitleJsonArray** (Array): 来自TranslateSubtitleTask的中文字幕JSON数组
  - 结构: `[{id, start, end, text, words}, ...]`
- **clozedSubtitleJsonArray** (Array): 来自SubtitleClozeTask的挖空字幕JSON数组
  - 结构: `[{id, start, end, text, words}, ...]`
- **savePath** (string): 文件保存路径

### 可选参数
- **correctedFullText** (string): 完整上下文文本，用于LLM生成词汇解释
- **reqId** (string): 请求ID，用于日志追踪

## 2. 输出上下文参数 (Output Context)

- **enhancedBilingualSubtitleJsonArray** (Array): 增强后的双语字幕JSON数组，包含词汇解释
  - 结构: `[{id, start, end, text_english, text_chinese, words_explanation}, ...]`
- **enhancedBilingualSubtitleJsonPath** (string): 保存增强双语字幕JSON文件的完整路径
- **bilingualSubtitleMergeTaskStatus** (string): 任务执行状态，成功时为'success'
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### 合并前的输入格式（来自三个上游Task）
```json
// simplifiedSubtitleJsonArray (来自TranscriptionCorrectionTask)
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "Good afternoon, there is another wildfire evacuation alert tonight.",
    "words": [
      {"text": "Good", "start": 0.16, "end": 0.5},
      {"text": "afternoon", "start": 0.5, "end": 1.0}
    ]
  }
]

// translatedSubtitleJsonArray (来自TranslateSubtitleTask)
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "下午好，今晚又发布了野火疏散警报。",
    "words": [
      {"text": "下午好", "start": 0.16, "end": 0.5, "original": "Good"},
      {"text": "今晚", "start": 0.5, "end": 1.0, "original": "afternoon"}
    ]
  }
]

// clozedSubtitleJsonArray (来自SubtitleClozeTask)
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text": "Good afternoon, there is () () () () tonight.",
    "words": ["another wildfire evacuation alert"]
  }
]
```

### 合并后的中间格式（传递给LLM）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text_english": "Good afternoon, there is another wildfire evacuation alert tonight.",
    "text_chinese": "下午好，今晚又发布了野火疏散警报。",
    "text_clozed": "Good afternoon, there is () () () () tonight.",
    "words": ["another wildfire evacuation alert"]
  }
]
```

### LLM提示词参数格式
```json
{
  "merged_subtitle_json": "[{\"id\":\"1\",\"start\":0.16,\"end\":4.32,\"text_english\":\"Good afternoon, there is another wildfire evacuation alert tonight.\",\"text_chinese\":\"下午好，今晚又发布了野火疏散警报。\",\"text_clozed\":\"Good afternoon, there is () () () () tonight.\",\"words\":[\"another wildfire evacuation alert\"]}]",
  "overall_video_context": "完整视频转录文本，用于理解整体语境..."
}
```

### LLM输出格式（最终输出）
```json
[
  {
    "id": "1",
    "start": 0.16,
    "end": 4.32,
    "text_english": "Good afternoon, there is another wildfire evacuation alert tonight.",
    "text_chinese": "下午好，今晚又发布了野火疏散警报。",
    "words_explanation": {
      "another wildfire evacuation alert": "疏散警报"
    }
  }
]
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 增强双语字幕JSON文件

### 文件命名规则
- **模式**: `{videoIdentifier}_enhanced_bilingual_subtitle.json`
- **示例**: `video123_enhanced_bilingual_subtitle.json`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保中文字符正确保存

## 5. 执行逻辑概述

双语字幕合并与增强任务负责将来自TranscriptionCorrectionTask、TranslateSubtitleTask和SubtitleClozeTask的三种字幕数据源合并为统一的增强双语字幕格式。任务首先验证必需参数（videoIdentifier、simplifiedSubtitleJsonArray、translatedSubtitleJsonArray、clozedSubtitleJsonArray、savePath），然后执行数据验证和预处理，确保三种字幕数据的完整性和一致性。核心合并阶段将三种字幕按索引对齐，构建包含7个字段的中间格式（id、start、end、text_english、text_chinese、text_clozed、words）。接下来通过LLM服务调用BILINGUAL_SUBTITLE_ENHANCE提示词，使用merged_subtitle_json和overall_video_context参数生成词汇解释，输出只包含6个字段的最终格式（id、start、end、text_english、text_chinese、words_explanation）。最后进行智能校验和修复，确保LLM输出的质量，并将增强后的双语字幕保存为JSON文件。整个过程提供详细的进度报告和错误处理，为语言学习提供高质量的双语学习材料。
