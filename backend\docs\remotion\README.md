# Remotion API 文档索引

本目录包含 Remotion 核心 API 的详细文档，所有内容基于官方文档 https://www.remotion.dev/docs/api 确保100%准确。

## 核心 API 文档

### 时间和帧控制
- [useCurrentFrame](./useCurrentFrame.md) - 获取当前帧数
- [useVideoConfig](./useVideoConfig.md) - 获取视频配置信息

### 动画和插值
- [interpolate](./interpolate.md) - 值范围映射
- [spring](./spring.md) - 物理动画
- [interpolateColors](./interpolateColors.md) - 颜色插值
- [Easing](./Easing.md) - 缓动函数

### 组合和项目结构
- [Composition](./Composition.md) - 定义视频组合
- [Still](./Still.md) - 定义静态图像
- [registerRoot](./registerRoot.md) - 初始化项目
- [Folder](./Folder.md) - 组织组合

### 布局和定位
- [AbsoluteFill](./AbsoluteFill.md) - 绝对定位填充
- [Sequence](./Sequence.md) - 时间偏移
- [Series](./Series.md) - 顺序显示
- [Freeze](./Freeze.md) - 冻结内容
- [Loop](./Loop.md) - 循环播放

### 媒体元素
- [Video](./Video.md) - 视频元素
- [Audio](./Audio.md) - 音频元素
- [Img](./Img.md) - 图像元素
- [OffthreadVideo](./OffthreadVideo.md) - 离线视频
- [AnimatedImage](./AnimatedImage.md) - 动画图像
- [IFrame](./IFrame.md) - 内嵌框架

### 渲染控制
- [delayRender](./delayRender.md) - 延迟渲染
- [continueRender](./continueRender.md) - 继续渲染
- [cancelRender](./cancelRender.md) - 取消渲染

### 数据和环境
- [getInputProps](./getInputProps.md) - 获取输入属性
- [getRemotionEnvironment](./getRemotionEnvironment.md) - 获取环境信息
- [staticFile](./staticFile.md) - 静态文件访问
- [VERSION](./VERSION.md) - 版本信息

## 文档说明

- 所有文档基于 Remotion 4.0.331 版本
- 每个文档包含：API 作用、参数说明、返回值、使用示例
- 示例代码均为实际可运行的代码
- 文档内容100%基于官方文档验证

## 更新日期

文档创建时间：2025年8月3日
基于官方文档版本：最新版本（2025年8月1日更新）
