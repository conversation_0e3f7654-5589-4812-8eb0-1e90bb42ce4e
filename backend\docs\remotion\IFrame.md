# &lt;IFrame&gt;

## 概述

`<IFrame>` 组件可以像常规的 `<iframe>` HTML 标签一样使用，用于在 Remotion 视频中嵌入外部网页内容。

Remotion 自动将 `<iframe>` 包装在 [`delayRender()`](./delayRender.md) 调用中，确保在渲染帧之前 iframe 已完全加载。

**重要提示**: 理想情况下，嵌入的网站不应有动画，因为 Remotion 只支持使用 [`useCurrentFrame()`](./useCurrentFrame.md) 的动画。

## 语法

```typescript
import { IFrame } from "remotion";

<IFrame src="网页URL" />
```

## 核心属性

### src (必需)
- **类型**: `string`
- **描述**: 要加载的网页 URL

### delayRenderTimeoutInMilliseconds (可选)
- **类型**: `number`
- **版本要求**: v4.0.140+
- **描述**: 自定义 [`delayRender()`](./delayRender.md) 调用的超时时间

### delayRenderRetries (可选)
- **类型**: `number`
- **版本要求**: v4.0.140+
- **描述**: 自定义 [`delayRender()`](./delayRender.md) 调用的重试次数

## 基础用法

### 1. 基础网页嵌入

```typescript
import { IFrame } from "remotion";

export const WebsiteEmbed: React.FC = () => {
  return (
    <IFrame src="https://remotion.dev" />
  );
};
```

### 2. 自定义超时和重试

```typescript
import { IFrame } from "remotion";

export const CustomIFrame: React.FC = () => {
  return (
    <IFrame 
      src="https://example.com"
      delayRenderTimeoutInMilliseconds={10000}
      delayRenderRetries={3}
    />
  );
};
```

## 实际应用场景

### 1. 网站截图和演示

```typescript
import React from 'react';
import { IFrame, useCurrentFrame, interpolate, useVideoConfig } from "remotion";

interface WebsiteShowcaseProps {
  websites: Array<{
    name: string;
    url: string;
    description: string;
  }>;
}

const WebsiteShowcase: React.FC<WebsiteShowcaseProps> = ({ websites }) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 计算当前显示的网站索引
  const websiteIndex = Math.floor(
    (frame / durationInFrames) * websites.length
  ) % websites.length;

  const currentWebsite = websites[websiteIndex];

  // 淡入淡出效果
  const opacity = interpolate(
    frame % (durationInFrames / websites.length),
    [0, 15, (durationInFrames / websites.length) - 15, durationInFrames / websites.length],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#f0f2f5",
      padding: 40,
      display: "flex",
      flexDirection: "column"
    }}>
      {/* 标题栏 */}
      <div style={{
        backgroundColor: "white",
        borderRadius: "15px 15px 0 0",
        padding: 20,
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        display: "flex",
        alignItems: "center",
        gap: 15
      }}>
        <div style={{
          display: "flex",
          gap: 8
        }}>
          <div style={{
            width: 12,
            height: 12,
            borderRadius: "50%",
            backgroundColor: "#ff5f57"
          }} />
          <div style={{
            width: 12,
            height: 12,
            borderRadius: "50%",
            backgroundColor: "#ffbd2e"
          }} />
          <div style={{
            width: 12,
            height: 12,
            borderRadius: "50%",
            backgroundColor: "#28ca42"
          }} />
        </div>
        
        <div style={{
          flex: 1,
          backgroundColor: "#f5f5f5",
          borderRadius: 8,
          padding: "8px 15px",
          fontSize: 14,
          color: "#666"
        }}>
          {currentWebsite.url}
        </div>
      </div>

      {/* 网站内容 */}
      <div style={{
        flex: 1,
        backgroundColor: "white",
        borderRadius: "0 0 15px 15px",
        overflow: "hidden",
        boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        opacity
      }}>
        <IFrame 
          src={currentWebsite.url}
          delayRenderTimeoutInMilliseconds={15000}
          delayRenderRetries={2}
          style={{
            width: "100%",
            height: "100%",
            border: "none"
          }}
        />
      </div>

      {/* 信息栏 */}
      <div style={{
        marginTop: 20,
        backgroundColor: "white",
        borderRadius: 15,
        padding: 20,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
      }}>
        <h3 style={{
          margin: 0,
          marginBottom: 8,
          fontSize: 20,
          fontWeight: "bold",
          color: "#2c3e50"
        }}>
          {currentWebsite.name}
        </h3>
        <p style={{
          margin: 0,
          fontSize: 14,
          color: "#666",
          lineHeight: 1.5
        }}>
          {currentWebsite.description}
        </p>
      </div>
    </div>
  );
};

export default WebsiteShowcase;
```

### 2. 产品演示和教程

```typescript
import React from 'react';
import { IFrame, useCurrentFrame, interpolate, Sequence } from "remotion";

interface ProductDemoProps {
  demoSteps: Array<{
    title: string;
    url: string;
    duration: number;
    description: string;
  }>;
}

const ProductDemo: React.FC<ProductDemoProps> = ({ demoSteps }) => {
  const frame = useCurrentFrame();

  let currentFrame = 0;

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#1a1a1a",
      position: "relative"
    }}>
      {demoSteps.map((step, index) => {
        const sequenceStart = currentFrame;
        currentFrame += step.duration;

        return (
          <Sequence
            key={index}
            from={sequenceStart}
            durationInFrames={step.duration}
          >
            <DemoStep 
              step={step}
              stepNumber={index + 1}
              totalSteps={demoSteps.length}
            />
          </Sequence>
        );
      })}
    </div>
  );
};

const DemoStep: React.FC<{
  step: any;
  stepNumber: number;
  totalSteps: number;
}> = ({ step, stepNumber, totalSteps }) => {
  const frame = useCurrentFrame();

  // 入场动画
  const slideIn = interpolate(
    frame,
    [0, 20],
    [-100, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 缩放动画
  const scale = interpolate(
    frame,
    [0, 15, 30],
    [0.9, 1.02, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      display: "flex",
      padding: 40,
      gap: 30
    }}>
      {/* 左侧信息面板 */}
      <div style={{
        width: 350,
        display: "flex",
        flexDirection: "column",
        gap: 20,
        transform: `translateX(${slideIn}px)`
      }}>
        {/* 步骤指示器 */}
        <div style={{
          backgroundColor: "rgba(255,255,255,0.1)",
          borderRadius: 15,
          padding: 20,
          color: "white"
        }}>
          <div style={{
            fontSize: 14,
            opacity: 0.7,
            marginBottom: 5
          }}>
            步骤 {stepNumber} / {totalSteps}
          </div>
          <h2 style={{
            margin: 0,
            fontSize: 24,
            fontWeight: "bold"
          }}>
            {step.title}
          </h2>
        </div>

        {/* 描述 */}
        <div style={{
          backgroundColor: "rgba(255,255,255,0.05)",
          borderRadius: 15,
          padding: 20,
          color: "white",
          fontSize: 16,
          lineHeight: 1.6
        }}>
          {step.description}
        </div>

        {/* 进度条 */}
        <div style={{
          backgroundColor: "rgba(255,255,255,0.1)",
          borderRadius: 10,
          height: 8,
          overflow: "hidden"
        }}>
          <div style={{
            width: `${(frame / step.duration) * 100}%`,
            height: "100%",
            backgroundColor: "#3498db",
            borderRadius: 10,
            transition: "width 0.1s ease"
          }} />
        </div>
      </div>

      {/* 右侧网页演示 */}
      <div style={{
        flex: 1,
        backgroundColor: "white",
        borderRadius: 20,
        overflow: "hidden",
        boxShadow: "0 8px 32px rgba(0,0,0,0.3)",
        transform: `scale(${scale})`
      }}>
        <IFrame 
          src={step.url}
          delayRenderTimeoutInMilliseconds={12000}
          delayRenderRetries={2}
          style={{
            width: "100%",
            height: "100%",
            border: "none"
          }}
        />
      </div>
    </div>
  );
};

export default ProductDemo;
```

### 3. 社交媒体内容展示

```typescript
import React from 'react';
import { IFrame, useCurrentFrame, interpolate, useVideoConfig } from "remotion";

interface SocialMediaFeedProps {
  posts: Array<{
    platform: 'twitter' | 'instagram' | 'linkedin';
    embedUrl: string;
    author: string;
    content: string;
  }>;
}

const SocialMediaFeed: React.FC<SocialMediaFeedProps> = ({ posts }) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  const postDuration = durationInFrames / posts.length;
  const currentPostIndex = Math.floor(frame / postDuration);
  const currentPost = posts[currentPostIndex];

  // 平台颜色
  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'twitter': return '#1da1f2';
      case 'instagram': return '#e4405f';
      case 'linkedin': return '#0077b5';
      default: return '#333';
    }
  };

  // 平台图标
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter': return '🐦';
      case 'instagram': return '📷';
      case 'linkedin': return '💼';
      default: return '📱';
    }
  };

  // 卡片动画
  const cardScale = interpolate(
    frame % postDuration,
    [0, 20, postDuration - 20, postDuration],
    [0.9, 1, 1, 0.9],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const cardOpacity = interpolate(
    frame % postDuration,
    [0, 15, postDuration - 15, postDuration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#f8f9fa",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: 40
    }}>
      <div style={{
        width: 600,
        backgroundColor: "white",
        borderRadius: 20,
        boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
        overflow: "hidden",
        transform: `scale(${cardScale})`,
        opacity: cardOpacity
      }}>
        {/* 平台标题栏 */}
        <div style={{
          backgroundColor: getPlatformColor(currentPost.platform),
          color: "white",
          padding: 20,
          display: "flex",
          alignItems: "center",
          gap: 15
        }}>
          <span style={{ fontSize: 24 }}>
            {getPlatformIcon(currentPost.platform)}
          </span>
          <div>
            <h3 style={{
              margin: 0,
              fontSize: 18,
              fontWeight: "bold"
            }}>
              {currentPost.platform.charAt(0).toUpperCase() + currentPost.platform.slice(1)}
            </h3>
            <div style={{
              fontSize: 14,
              opacity: 0.9
            }}>
              @{currentPost.author}
            </div>
          </div>
        </div>

        {/* 内容预览 */}
        <div style={{
          padding: 20,
          borderBottom: "1px solid #eee",
          fontSize: 16,
          lineHeight: 1.5,
          color: "#333"
        }}>
          {currentPost.content}
        </div>

        {/* 嵌入内容 */}
        <div style={{
          height: 400,
          backgroundColor: "#f8f9fa"
        }}>
          <IFrame 
            src={currentPost.embedUrl}
            delayRenderTimeoutInMilliseconds={10000}
            delayRenderRetries={2}
            style={{
              width: "100%",
              height: "100%",
              border: "none"
            }}
          />
        </div>

        {/* 底部指示器 */}
        <div style={{
          padding: 15,
          backgroundColor: "#f8f9fa",
          display: "flex",
          justifyContent: "center",
          gap: 8
        }}>
          {posts.map((_, index) => (
            <div
              key={index}
              style={{
                width: 8,
                height: 8,
                borderRadius: "50%",
                backgroundColor: index === currentPostIndex ? 
                  getPlatformColor(currentPost.platform) : "#ddd",
                transition: "background-color 0.3s ease"
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SocialMediaFeed;
```

### 4. 网页性能监控展示

```typescript
import React from 'react';
import { IFrame, useCurrentFrame, interpolate } from "remotion";

interface PerformanceMonitorProps {
  websiteUrl: string;
  metrics: {
    loadTime: number;
    pageSize: number;
    requests: number;
    score: number;
  };
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  websiteUrl, 
  metrics 
}) => {
  const frame = useCurrentFrame();

  // 加载进度动画
  const loadProgress = interpolate(
    frame,
    [0, 60],
    [0, 100],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 指标动画
  const metricsOpacity = interpolate(
    frame,
    [60, 80],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#1e1e1e",
      display: "flex",
      gap: 30,
      padding: 30
    }}>
      {/* 左侧网页预览 */}
      <div style={{
        flex: 2,
        backgroundColor: "white",
        borderRadius: 15,
        overflow: "hidden",
        boxShadow: "0 8px 32px rgba(0,0,0,0.3)"
      }}>
        {/* 浏览器标题栏 */}
        <div style={{
          backgroundColor: "#f0f0f0",
          padding: 15,
          borderBottom: "1px solid #ddd",
          display: "flex",
          alignItems: "center",
          gap: 10
        }}>
          <div style={{
            display: "flex",
            gap: 6
          }}>
            <div style={{
              width: 10,
              height: 10,
              borderRadius: "50%",
              backgroundColor: "#ff5f57"
            }} />
            <div style={{
              width: 10,
              height: 10,
              borderRadius: "50%",
              backgroundColor: "#ffbd2e"
            }} />
            <div style={{
              width: 10,
              height: 10,
              borderRadius: "50%",
              backgroundColor: "#28ca42"
            }} />
          </div>
          <div style={{
            flex: 1,
            backgroundColor: "white",
            borderRadius: 6,
            padding: "6px 12px",
            fontSize: 12,
            color: "#666"
          }}>
            {websiteUrl}
          </div>
        </div>

        {/* 加载进度条 */}
        {loadProgress < 100 && (
          <div style={{
            height: 3,
            backgroundColor: "#e0e0e0"
          }}>
            <div style={{
              width: `${loadProgress}%`,
              height: "100%",
              backgroundColor: "#4285f4",
              transition: "width 0.1s ease"
            }} />
          </div>
        )}

        {/* 网页内容 */}
        <div style={{
          height: "calc(100% - 60px)"
        }}>
          {loadProgress >= 100 ? (
            <IFrame 
              src={websiteUrl}
              delayRenderTimeoutInMilliseconds={15000}
              delayRenderRetries={3}
              style={{
                width: "100%",
                height: "100%",
                border: "none"
              }}
            />
          ) : (
            <div style={{
              width: "100%",
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#f8f9fa",
              color: "#666",
              fontSize: 16
            }}>
              加载中... {Math.round(loadProgress)}%
            </div>
          )}
        </div>
      </div>

      {/* 右侧性能指标 */}
      <div style={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        gap: 20,
        opacity: metricsOpacity
      }}>
        <h2 style={{
          color: "white",
          margin: 0,
          fontSize: 24,
          fontWeight: "bold"
        }}>
          性能指标
        </h2>

        {/* 性能评分 */}
        <div style={{
          backgroundColor: "rgba(255,255,255,0.1)",
          borderRadius: 15,
          padding: 20,
          textAlign: "center"
        }}>
          <div style={{
            fontSize: 48,
            fontWeight: "bold",
            color: metrics.score >= 90 ? "#4caf50" : 
                   metrics.score >= 70 ? "#ff9800" : "#f44336",
            marginBottom: 10
          }}>
            {metrics.score}
          </div>
          <div style={{
            color: "white",
            fontSize: 16
          }}>
            性能评分
          </div>
        </div>

        {/* 详细指标 */}
        <div style={{
          display: "flex",
          flexDirection: "column",
          gap: 15
        }}>
          <MetricCard 
            label="加载时间"
            value={`${metrics.loadTime}s`}
            color="#2196f3"
          />
          <MetricCard 
            label="页面大小"
            value={`${metrics.pageSize}KB`}
            color="#9c27b0"
          />
          <MetricCard 
            label="请求数量"
            value={`${metrics.requests}`}
            color="#ff5722"
          />
        </div>

        {/* 建议 */}
        <div style={{
          backgroundColor: "rgba(255,255,255,0.05)",
          borderRadius: 15,
          padding: 20,
          color: "white",
          fontSize: 14,
          lineHeight: 1.5
        }}>
          <h4 style={{ margin: "0 0 10px 0" }}>优化建议:</h4>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>压缩图片资源</li>
            <li>启用 Gzip 压缩</li>
            <li>减少 HTTP 请求</li>
            <li>使用 CDN 加速</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

const MetricCard: React.FC<{
  label: string;
  value: string;
  color: string;
}> = ({ label, value, color }) => (
  <div style={{
    backgroundColor: "rgba(255,255,255,0.1)",
    borderRadius: 10,
    padding: 15,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  }}>
    <span style={{
      color: "white",
      fontSize: 14
    }}>
      {label}
    </span>
    <span style={{
      color: color,
      fontSize: 18,
      fontWeight: "bold"
    }}>
      {value}
    </span>
  </div>
);

export default PerformanceMonitor;
```

### 5. 多设备响应式预览

```typescript
import React from 'react';
import { IFrame, useCurrentFrame, interpolate, Sequence } from "remotion";

interface ResponsivePreviewProps {
  websiteUrl: string;
  devices: Array<{
    name: string;
    width: number;
    height: number;
    userAgent: string;
  }>;
}

const ResponsivePreview: React.FC<ResponsivePreviewProps> = ({ 
  websiteUrl, 
  devices 
}) => {
  const frame = useCurrentFrame();

  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: "#2c3e50",
      padding: 40,
      display: "flex",
      flexDirection: "column",
      alignItems: "center"
    }}>
      <h1 style={{
        color: "white",
        marginBottom: 40,
        fontSize: 32,
        fontWeight: "bold"
      }}>
        响应式设计预览
      </h1>

      <div style={{
        display: "flex",
        gap: 30,
        alignItems: "flex-end",
        justifyContent: "center",
        flex: 1
      }}>
        {devices.map((device, index) => {
          // 错开显示时间
          const startFrame = index * 20;
          const opacity = interpolate(
            frame,
            [startFrame, startFrame + 15],
            [0, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          const scale = interpolate(
            frame,
            [startFrame, startFrame + 10, startFrame + 20],
            [0.8, 1.05, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          return (
            <div
              key={index}
              style={{
                opacity,
                transform: `scale(${scale})`,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: 15
              }}
            >
              {/* 设备标签 */}
              <div style={{
                color: "white",
                fontSize: 14,
                fontWeight: "bold",
                textAlign: "center"
              }}>
                {device.name}
              </div>

              {/* 设备框架 */}
              <div style={{
                backgroundColor: "#34495e",
                borderRadius: device.name.includes("手机") ? 25 : 15,
                padding: device.name.includes("手机") ? 15 : 20,
                boxShadow: "0 8px 32px rgba(0,0,0,0.3)"
              }}>
                {/* 屏幕 */}
                <div style={{
                  width: device.width * 0.3, // 缩放显示
                  height: device.height * 0.3,
                  backgroundColor: "white",
                  borderRadius: device.name.includes("手机") ? 15 : 8,
                  overflow: "hidden",
                  position: "relative"
                }}>
                  {/* 状态栏 (仅手机) */}
                  {device.name.includes("手机") && (
                    <div style={{
                      height: 8,
                      backgroundColor: "#000",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "0 6px",
                      fontSize: 6,
                      color: "white"
                    }}>
                      <span>9:41</span>
                      <span>100%</span>
                    </div>
                  )}

                  {/* 网页内容 */}
                  <div style={{
                    width: "100%",
                    height: device.name.includes("手机") ? "calc(100% - 8px)" : "100%"
                  }}>
                    <IFrame 
                      src={`${websiteUrl}?viewport=${device.width}x${device.height}`}
                      delayRenderTimeoutInMilliseconds={12000}
                      delayRenderRetries={2}
                      style={{
                        width: "100%",
                        height: "100%",
                        border: "none",
                        transform: `scale(${1 / 3.33})`, // 适应缩放
                        transformOrigin: "top left"
                      }}
                    />
                  </div>
                </div>

                {/* 设备规格 */}
                <div style={{
                  marginTop: 10,
                  textAlign: "center",
                  fontSize: 10,
                  color: "#bdc3c7"
                }}>
                  {device.width} × {device.height}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 底部说明 */}
      <div style={{
        marginTop: 30,
        color: "#bdc3c7",
        fontSize: 14,
        textAlign: "center",
        maxWidth: 600,
        lineHeight: 1.5
      }}>
        展示网站在不同设备上的响应式表现，确保在各种屏幕尺寸下都能提供良好的用户体验。
      </div>
    </div>
  );
};

// 使用示例
export const ResponsiveDemo = () => {
  const devices = [
    {
      name: "桌面电脑",
      width: 1920,
      height: 1080,
      userAgent: "desktop"
    },
    {
      name: "平板电脑",
      width: 768,
      height: 1024,
      userAgent: "tablet"
    },
    {
      name: "智能手机",
      width: 375,
      height: 667,
      userAgent: "mobile"
    }
  ];

  return (
    <ResponsivePreview 
      websiteUrl="https://example.com"
      devices={devices}
    />
  );
};

export default ResponsivePreview;
```

## 重要注意事项

### 动画限制
- 嵌入的网站不应包含动画
- 只支持使用 [`useCurrentFrame()`](./useCurrentFrame.md) 的 Remotion 动画
- 避免使用包含 CSS 动画或 JavaScript 动画的网站

### 加载处理
- 组件自动使用 [`delayRender()`](./delayRender.md) 等待加载完成
- 可以自定义超时时间和重试次数
- 建议为复杂网站设置更长的超时时间

### 性能考虑
- IFrame 渲染可能较慢
- 避免同时显示多个复杂的 IFrame
- 考虑使用静态截图替代动态 IFrame

## 最佳实践

1. **超时设置**: 根据网站复杂度设置合适的超时时间
2. **重试机制**: 为不稳定的网站设置重试次数
3. **错误处理**: 提供加载失败时的降级方案
4. **性能优化**: 避免过多并发 IFrame 加载
5. **内容验证**: 确保嵌入内容适合视频展示

## 常见用例

- 网站截图和演示
- 产品功能展示
- 社交媒体内容
- 性能监控展示
- 响应式设计预览

## 相关 API

- [`delayRender()`](./delayRender.md) - 延迟渲染
- [`<Img>`](./Img.md) - 图像组件
- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/IFrame.tsx)
