/**
 * @功能概述: 视频合成任务，实现9:16短视频的完整合成功能。
 *           包括音频重复拼接、16:9视频居中放置在9:16画布、多段视频内容合成、ASS字幕渲染等核心功能。
 *           此任务继承自TaskBase，利用标准化的进度监控和错误处理机制。
 * @输入依赖: context.videoIdentifier (string, 必需) - 视频标识符，用于文件命名和日志追踪。
 *           context.processedVideoPath (string, 必需) - 来自VideoClipAndCropTask的处理后视频路径。
 *           context.processedAudioPath (string, 必需) - 来自ConvertToAudioTask的音频文件路径。
 *           context.assSubtitlePath (string, 必需) - 来自AssSubtitleGenerationTask的ASS字幕文件路径。
 *           context.savePath (string, 必需) - 文件保存路径。
 *           context.compositionConfig (Object, 可选) - 合成配置参数。
 * @输出结果: context.finalVideoPath (string) - 最终合成的9:16视频文件路径。
 *           context.videoCompositionTaskStatus (string) - 任务执行状态。
 *           context.compositionMetadata (Object) - 合成过程的元数据信息。
 * @数据格式: 输入：16:9视频 + 音频 + ASS字幕
 *           输出：9:16高质量MP4视频，包含重复音频和字幕渲染
 * @处理逻辑: 1. 验证输入文件和参数
 *           2. 音频重复拼接（2-5遍）
 *           3. 创建9:16画布并居中放置16:9视频
 *           4. 渲染ASS字幕到视频
 *           5. 合成最终视频文件
 * @FFmpeg功能: 音频拼接、视频画布调整、字幕渲染、视频编码
 * @质量控制: 输出1080x1920分辨率，H.264编码，AAC音频
 */

const TaskBase = require('../class/TaskBase');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const config = require('../config');

// 模块级日志前缀 - 标准格式
const taskModuleLogPrefix = '[文件：VideoCompositionTask.js][视频合成任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`);

/**
 * @class VideoCompositionTask
 * @extends TaskBase
 * @description 视频合成任务类，负责将处理后的视频、音频和字幕合成为最终的9:16短视频
 */
class VideoCompositionTask extends TaskBase {
    /**
     * @constructor
     * @description 创建视频合成任务实例
     */
    constructor() {
        super('VideoCompositionTask');
        this.instanceLogPrefix = `[文件：VideoCompositionTask.js][视频合成任务][${this.name}]`;
        this.timeout = 600000; // 10分钟超时（视频合成可能需要较长时间）
        this.currentFFmpegProcess = null; // 当前运行的FFmpeg进程

        // 支持的视频格式
        this.supportedFormats = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];

        logger.info(`${this.instanceLogPrefix} 实例已创建。`);
    }

    /**
     * @功能概述: 执行视频合成任务的核心逻辑。
     * @param {object} context - 上下文对象，期望包含:
     *                           - videoIdentifier: {string} 视频标识符 (必需)。
     *                           - processedVideoPath: {string} 处理后视频路径 (必需)。
     *                           - processedAudioPath: {string} 音频文件路径 (必需)。
     *                           - assSubtitlePath: {string} ASS字幕文件路径 (必需)。
     *                           - savePath: {string} 文件保存路径 (必需)。
     *                           - compositionConfig: {Object} 合成配置 (可选)。
     * @param {function} progressCallback - 进度回调函数，用于报告任务执行进度。
     * @returns {Promise<object>} 包含最终视频路径和合成元数据的对象。
     * @throws {Error} 如果参数校验失败、文件处理失败或FFmpeg执行失败时，则抛出错误。
     */
    async execute(context, progressCallback) {
        // 步骤 1: 从上下文中解构出核心参数
        const { videoIdentifier, processedVideoPath, processedAudioPath, assSubtitlePath, savePath, compositionConfig } = context;

        // 步骤 2: 定义任务执行所必需的字段列表
        const requiredFields = ['videoIdentifier', 'processedVideoPath', 'processedAudioPath', 'assSubtitlePath', 'savePath'];

        // 步骤 3: 构建执行日志前缀
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${context.reqId || 'unknown'}][VideoID:${videoIdentifier}]`;

        // 步骤 4: 设置进度回调并开始任务
        this.setProgressCallback(progressCallback);
        this.start();

        // 步骤 5: 验证必需的上下文参数
        this.validateRequiredFields(context, requiredFields, execLogPrefix);

        let finalVideoPath = null;
        let compositionMetadata = null;
        let extendedAudioPath = null;

        try {
            // 记录上下文参数验证通过的日志
            logger.debug(`${execLogPrefix} 上下文参数验证通过，开始视频合成流程`);

            // 阶段1: 任务初始化
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: `开始视频合成任务 [${videoIdentifier}]`,
                current: 5,
                total: 100
            });
            logger.info(`${execLogPrefix}[阶段1/5] 任务初始化完成`);

            // 阶段2: 解析合成配置
            logger.info(`${execLogPrefix}[阶段2/5] 开始解析合成配置参数`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `解析合成配置参数`,
                current: 15,
                total: 100
            });

            const config = this.parseCompositionConfig(compositionConfig, execLogPrefix);
            logger.info(`${execLogPrefix}[阶段2/5] 合成配置解析完成`);

            // 阶段3: 验证输入文件
            logger.info(`${execLogPrefix}[阶段3/5] 开始验证输入文件`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `验证输入文件存在性和格式`,
                current: 25,
                total: 100
            });

            await this.validateInputFiles(processedVideoPath, processedAudioPath, assSubtitlePath, execLogPrefix);
            logger.info(`${execLogPrefix}[阶段3/5] 输入文件验证完成`);

            // 阶段4: 音频重复拼接
            logger.info(`${execLogPrefix}[阶段4/5] 开始音频重复拼接`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `音频重复拼接 (${config.audioRepeatCount}遍)`,
                current: 40,
                total: 100
            });

            extendedAudioPath = await this.extendAudio(
                processedAudioPath,
                config.audioRepeatCount,
                videoIdentifier,
                savePath,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[阶段4/5] 音频拼接完成: ${extendedAudioPath}`);

            // 阶段5: 视频合成
            logger.info(`${execLogPrefix}[阶段5/5] 开始最终视频合成`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: `合成9:16视频 (画布调整+字幕渲染)`,
                current: 60,
                total: 100
            });

            const compositionResult = await this.composeVideo(
                processedVideoPath,
                extendedAudioPath,
                assSubtitlePath,
                config,
                videoIdentifier,
                savePath,
                execLogPrefix
            );

            finalVideoPath = compositionResult.finalVideoPath;
            compositionMetadata = compositionResult.metadata;

            logger.info(`${execLogPrefix}[阶段5/5] 视频合成完成: ${finalVideoPath}`);

            // 准备最终返回结果
            const result = {
                finalVideoPath: finalVideoPath,
                videoCompositionTaskStatus: 'success',
                compositionMetadata: compositionMetadata,
                extendedAudioPath: extendedAudioPath,
                compositionConfig: config
            };

            // 标记任务完成
            this.reportProgress(TASK_STATUS.COMPLETED, null, {
                detail: `视频合成任务完成`,
                current: 100,
                total: 100
            });

            this.result = result;
            this.complete(result);
            logger.info(`${execLogPrefix}[execute] VideoCompositionTask 成功完成，总耗时：${this.getElapsedTime()}ms`);
            return result;

        } catch (error) {
            // 捕获任务执行过程中发生的任何错误
            logger.error(`${execLogPrefix} 任务执行失败，错误信息：${error.message}，堆栈：${error.stack}`);
            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: 验证上下文对象中是否包含所有必需的字段
     * @param {object} context - 要验证的上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} execLogPrefix - 执行日志前缀，用于错误日志记录
     * @throws {Error} 当缺少任何必需字段时抛出错误
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (context[field] === undefined || context[field] === null) {
                const errorMsg = `执行失败：上下文缺少必需字段 "${field}"`;
                logger.error(`${execLogPrefix}[字段校验] ${errorMsg}`);
                this.fail(new Error(errorMsg));
                throw new Error(errorMsg);
            }
        }
        logger.debug(`${execLogPrefix}[字段校验] 所有必需字段 (${requiredFields.join(', ')}) 验证通过。`);
    }

    /**
     * @功能概述: 解析和验证合成配置参数
     * @param {Object} compositionConfig - 合成配置对象
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Object} 解析后的合成配置参数
     */
    parseCompositionConfig(compositionConfig, execLogPrefix) {
        // 默认合成配置参数
        const defaultConfig = {
            audioRepeatCount: 3,           // 音频重复次数
            outputWidth: 1080,             // 输出视频宽度
            outputHeight: 1920,            // 输出视频高度 (9:16)
            videoCodec: 'libx264',         // 视频编码器
            audioCodec: 'aac',             // 音频编码器
            crf: 23,                       // 视频质量 (18-28, 越小质量越高)
            preset: 'medium',              // 编码速度预设
            audioBitrate: '128k',          // 音频比特率
            frameRate: 30,                 // 帧率
            backgroundColor: '#000000'     // 背景颜色
        };

        // 如果有配置，则合并配置
        let config = { ...defaultConfig };
        
        if (compositionConfig) {
            config = { ...config, ...compositionConfig };
        }

        // 验证关键参数
        if (config.audioRepeatCount < 2 || config.audioRepeatCount > 5) {
            logger.warn(`${execLogPrefix}[配置参数] audioRepeatCount超出范围(2-5)，使用默认值3`);
            config.audioRepeatCount = 3;
        }

        if (config.outputWidth <= 0 || config.outputHeight <= 0) {
            logger.warn(`${execLogPrefix}[配置参数] 输出尺寸无效，使用默认值1080x1920`);
            config.outputWidth = 1080;
            config.outputHeight = 1920;
        }

        logger.debug(`${execLogPrefix}[配置参数] 合成配置解析完成：${JSON.stringify(config)}`);
        return config;
    }

    /**
     * @功能概述: 验证输入文件的存在性和格式
     * @param {string} videoPath - 视频文件路径
     * @param {string} audioPath - 音频文件路径
     * @param {string} subtitlePath - 字幕文件路径
     * @param {string} execLogPrefix - 日志前缀
     * @throws {Error} 如果文件不存在或格式不支持
     */
    async validateInputFiles(videoPath, audioPath, subtitlePath, execLogPrefix) {
        const filesToCheck = [
            { path: videoPath, type: '视频文件' },
            { path: audioPath, type: '音频文件' },
            { path: subtitlePath, type: '字幕文件' }
        ];

        for (const file of filesToCheck) {
            try {
                await fs.access(file.path);
                logger.debug(`${execLogPrefix}[文件验证] ${file.type}存在: ${file.path}`);
            } catch (error) {
                const errorMsg = `${file.type}不存在: ${file.path}`;
                logger.error(`${execLogPrefix}[文件验证] ${errorMsg}`);
                throw new Error(errorMsg);
            }
        }

        // 验证视频文件格式
        const videoExt = path.extname(videoPath).toLowerCase();
        if (!this.supportedFormats.includes(videoExt)) {
            throw new Error(`不支持的视频格式: ${videoExt}`);
        }

        // 验证字幕文件格式
        const subtitleExt = path.extname(subtitlePath).toLowerCase();
        if (subtitleExt !== '.ass') {
            throw new Error(`不支持的字幕格式: ${subtitleExt}，仅支持ASS格式`);
        }

        logger.info(`${execLogPrefix}[文件验证] 所有输入文件验证通过`);
    }

    /**
     * @功能概述: 音频重复拼接功能
     * @param {string} audioPath - 原始音频文件路径
     * @param {number} repeatCount - 重复次数
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} savePath - 保存路径
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<string>} 拼接后的音频文件路径
     */
    async extendAudio(audioPath, repeatCount, videoIdentifier, savePath, execLogPrefix) {
        const outputFileName = `${videoIdentifier}_extended_audio.mp3`;
        const outputPath = path.join(savePath, outputFileName);

        logger.info(`${execLogPrefix}[音频拼接] 开始拼接音频，重复${repeatCount}次`);

        // 构建FFmpeg命令：使用stream_loop重复音频
        const ffmpegArgs = [
            '-stream_loop', (repeatCount - 1).toString(), // 重复次数-1，因为原始播放1次
            '-i', audioPath,
            '-c:a', 'libmp3lame',
            '-b:a', '128k',
            '-y',
            outputPath
        ];

        await this.executeFFmpegCommand(ffmpegArgs, '音频拼接', execLogPrefix);

        // 验证输出文件
        try {
            await fs.access(outputPath);
            logger.info(`${execLogPrefix}[音频拼接] 音频拼接完成: ${outputPath}`);
            return outputPath;
        } catch (error) {
            throw new Error(`音频拼接失败，输出文件不存在: ${outputPath}`);
        }
    }

    /**
     * @功能概述: 视频合成功能 - 创建9:16画布，居中放置16:9视频，渲染字幕
     * @param {string} videoPath - 视频文件路径
     * @param {string} audioPath - 音频文件路径
     * @param {string} subtitlePath - 字幕文件路径
     * @param {Object} config - 合成配置
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} savePath - 保存路径
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<Object>} 合成结果对象
     */
    async composeVideo(videoPath, audioPath, subtitlePath, config, videoIdentifier, savePath, execLogPrefix) {
        const outputFileName = `${videoIdentifier}_final_video.mp4`;
        const outputPath = path.join(savePath, outputFileName);

        logger.info(`${execLogPrefix}[视频合成] 开始合成最终视频`);

        // 获取输入视频的元数据
        const videoMetadata = await this.getVideoMetadata(videoPath, execLogPrefix);

        // 计算视频在9:16画布中的位置（居中）
        const inputWidth = videoMetadata.width;
        const inputHeight = videoMetadata.height;
        const outputWidth = config.outputWidth;
        const outputHeight = config.outputHeight;

        // 计算缩放比例，保持宽高比
        const scaleRatio = Math.min(outputWidth / inputWidth, outputHeight / inputHeight);
        const scaledWidth = Math.round(inputWidth * scaleRatio);
        const scaledHeight = Math.round(inputHeight * scaleRatio);

        // 计算居中位置
        const xOffset = Math.round((outputWidth - scaledWidth) / 2);
        const yOffset = Math.round((outputHeight - scaledHeight) / 2);

        logger.info(`${execLogPrefix}[视频合成] 视频缩放: ${inputWidth}x${inputHeight} -> ${scaledWidth}x${scaledHeight}`);
        logger.info(`${execLogPrefix}[视频合成] 画布位置: (${xOffset}, ${yOffset})`);

        // 构建复杂的FFmpeg滤镜链（暂时不使用ASS字幕，避免路径问题）
        const filterComplex = [
            // 1. 创建黑色背景画布
            `color=${config.backgroundColor}:size=${outputWidth}x${outputHeight}:rate=${config.frameRate}[bg]`,
            // 2. 缩放输入视频
            `[0:v]scale=${scaledWidth}:${scaledHeight}[scaled]`,
            // 3. 将缩放后的视频叠加到背景上
            `[bg][scaled]overlay=${xOffset}:${yOffset}[final]`
        ].join(';');

        // 构建完整的FFmpeg命令
        const ffmpegArgs = [
            '-i', videoPath,                    // 输入视频
            '-i', audioPath,                    // 输入音频
            '-filter_complex', filterComplex,   // 复杂滤镜
            '-map', '[final]',                  // 映射最终视频
            '-map', '1:a',                      // 映射音频
            '-c:v', config.videoCodec,          // 视频编码器
            '-crf', config.crf.toString(),      // 视频质量
            '-preset', config.preset,           // 编码预设
            '-c:a', config.audioCodec,          // 音频编码器
            '-b:a', config.audioBitrate,        // 音频比特率
            '-r', config.frameRate.toString(),  // 帧率
            '-pix_fmt', 'yuv420p',              // 像素格式
            '-movflags', '+faststart',          // 优化流媒体播放
            '-y',                               // 覆盖输出文件
            outputPath                          // 输出路径
        ];

        await this.executeFFmpegCommand(ffmpegArgs, '视频合成', execLogPrefix);

        // 验证输出文件并获取元数据
        try {
            await fs.access(outputPath);
            const outputMetadata = await this.getVideoMetadata(outputPath, execLogPrefix);

            const metadata = {
                inputVideo: {
                    path: videoPath,
                    width: inputWidth,
                    height: inputHeight,
                    duration: videoMetadata.duration
                },
                outputVideo: {
                    path: outputPath,
                    width: outputWidth,
                    height: outputHeight,
                    duration: outputMetadata.duration
                },
                composition: {
                    scaledWidth,
                    scaledHeight,
                    xOffset,
                    yOffset,
                    scaleRatio,
                    audioRepeatCount: config.audioRepeatCount
                },
                processing: {
                    videoCodec: config.videoCodec,
                    audioCodec: config.audioCodec,
                    crf: config.crf,
                    preset: config.preset
                }
            };

            logger.info(`${execLogPrefix}[视频合成] 视频合成完成: ${outputPath}`);
            return {
                finalVideoPath: outputPath,
                metadata: metadata
            };
        } catch (error) {
            throw new Error(`视频合成失败，输出文件不存在: ${outputPath}`);
        }
    }

    /**
     * @功能概述: 执行FFmpeg命令并监控进度
     * @param {Array} ffmpegArgs - FFmpeg命令参数数组
     * @param {string} operation - 操作名称（用于日志）
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<void>} 执行完成的Promise
     */
    async executeFFmpegCommand(ffmpegArgs, operation, execLogPrefix) {
        return new Promise((resolve, reject) => {
            logger.info(`${execLogPrefix}[${operation}] 启动FFmpeg进程`);
            logger.debug(`${execLogPrefix}[${operation}] FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`);

            // 启动FFmpeg进程
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
            let isProcessCompleted = false;
            let timeoutHandle = null;

            // 存储进程引用以便外部清理
            this.currentFFmpegProcess = ffmpegProcess;

            let ffmpegOutput = '';
            let ffmpegError = '';

            // 清理函数
            const cleanup = () => {
                if (timeoutHandle) {
                    clearTimeout(timeoutHandle);
                    timeoutHandle = null;
                }
                this.currentFFmpegProcess = null;
            };

            // 强制终止进程的函数
            const forceKillProcess = () => {
                if (!ffmpegProcess.killed && !isProcessCompleted) {
                    logger.warn(`${execLogPrefix}[${operation}] 强制终止FFmpeg进程 PID: ${ffmpegProcess.pid}`);
                    try {
                        // Windows系统使用taskkill强制终止
                        if (process.platform === 'win32') {
                            require('child_process').exec(`taskkill /F /PID ${ffmpegProcess.pid}`, (error) => {
                                if (error) {
                                    logger.error(`${execLogPrefix}[${operation}] taskkill失败: ${error.message}`);
                                } else {
                                    logger.info(`${execLogPrefix}[${operation}] FFmpeg进程已强制终止`);
                                }
                            });
                        } else {
                            // Unix系统使用SIGKILL
                            ffmpegProcess.kill('SIGKILL');
                        }
                    } catch (error) {
                        logger.error(`${execLogPrefix}[${operation}] 强制终止进程失败: ${error.message}`);
                    }
                }
            };

            // 监听stdout输出
            ffmpegProcess.stdout.on('data', (data) => {
                ffmpegOutput += data.toString();
            });

            // 监听stderr输出（FFmpeg的主要输出）
            ffmpegProcess.stderr.on('data', (data) => {
                const output = data.toString();
                ffmpegError += output;

                // 解析进度信息
                if (output.includes('time=')) {
                    const timeMatch = output.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
                    if (timeMatch) {
                        const hours = parseInt(timeMatch[1]);
                        const minutes = parseInt(timeMatch[2]);
                        const seconds = parseFloat(timeMatch[3]);
                        const currentTime = hours * 3600 + minutes * 60 + seconds;

                        logger.debug(`${execLogPrefix}[${operation}] FFmpeg进度: ${currentTime.toFixed(2)}秒`);
                    }
                }
            });

            // 监听进程退出
            ffmpegProcess.on('close', (code) => {
                isProcessCompleted = true;
                cleanup();

                if (code === 0) {
                    logger.info(`${execLogPrefix}[${operation}] FFmpeg执行成功`);
                    resolve();
                } else {
                    const errorMsg = `FFmpeg执行失败，退出码: ${code}`;
                    logger.error(`${execLogPrefix}[${operation}] ${errorMsg}`);
                    logger.error(`${execLogPrefix}[${operation}] FFmpeg错误输出: ${ffmpegError.slice(-500)}`);
                    reject(new Error(errorMsg));
                }
            });

            // 监听进程错误
            ffmpegProcess.on('error', (error) => {
                isProcessCompleted = true;
                cleanup();
                const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                logger.error(`${execLogPrefix}[${operation}] ${errorMsg}`);
                reject(new Error(errorMsg));
            });

            // 设置超时处理
            timeoutHandle = setTimeout(() => {
                if (!isProcessCompleted) {
                    logger.warn(`${execLogPrefix}[${operation}] FFmpeg执行超时(${this.timeout}ms)，开始终止进程`);

                    // 首先尝试优雅终止
                    if (!ffmpegProcess.killed) {
                        ffmpegProcess.kill('SIGTERM');
                        logger.info(`${execLogPrefix}[${operation}] 发送SIGTERM信号`);
                    }

                    // 5秒后强制终止
                    setTimeout(() => {
                        if (!isProcessCompleted) {
                            forceKillProcess();
                        }
                    }, 5000);

                    cleanup();
                    reject(new Error(`FFmpeg执行超时(${this.timeout}ms)`));
                }
            }, this.timeout);
        });
    }

    /**
     * @功能概述: 强制清理当前运行的FFmpeg进程
     * @param {string} reason - 清理原因（用于日志）
     */
    forceCleanupFFmpegProcess(reason = '外部请求') {
        if (this.currentFFmpegProcess && !this.currentFFmpegProcess.killed) {
            const pid = this.currentFFmpegProcess.pid;
            logger.warn(`${this.instanceLogPrefix}[进程清理] 因${reason}强制清理FFmpeg进程 PID: ${pid}`);

            try {
                if (process.platform === 'win32') {
                    // Windows系统使用taskkill
                    require('child_process').exec(`taskkill /F /PID ${pid}`, (error) => {
                        if (error) {
                            logger.error(`${this.instanceLogPrefix}[进程清理] taskkill失败: ${error.message}`);
                        } else {
                            logger.info(`${this.instanceLogPrefix}[进程清理] FFmpeg进程已强制终止`);
                        }
                    });
                } else {
                    // Unix系统使用SIGKILL
                    this.currentFFmpegProcess.kill('SIGKILL');
                    logger.info(`${this.instanceLogPrefix}[进程清理] FFmpeg进程已强制终止`);
                }
            } catch (error) {
                logger.error(`${this.instanceLogPrefix}[进程清理] 强制终止进程失败: ${error.message}`);
            }

            this.currentFFmpegProcess = null;
        } else {
            logger.debug(`${this.instanceLogPrefix}[进程清理] 没有需要清理的FFmpeg进程`);
        }
    }

    /**
     * @功能概述: 获取当前FFmpeg进程状态
     * @returns {Object} 进程状态信息
     */
    getFFmpegProcessStatus() {
        if (this.currentFFmpegProcess) {
            return {
                hasProcess: true,
                pid: this.currentFFmpegProcess.pid,
                killed: this.currentFFmpegProcess.killed
            };
        } else {
            return {
                hasProcess: false,
                pid: null,
                killed: null
            };
        }
    }

    /**
     * @功能概述: 获取视频文件的元数据信息
     * @param {string} videoPath - 视频文件路径
     * @param {string} execLogPrefix - 日志前缀
     * @returns {Promise<Object>} 视频元数据对象
     */
    async getVideoMetadata(videoPath, execLogPrefix) {
        return new Promise((resolve, reject) => {
            logger.info(`${execLogPrefix}[元数据获取] 获取视频元数据: ${videoPath}`);

            // 使用ffprobe获取视频信息
            const ffprobeArgs = [
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                videoPath
            ];

            const ffprobeProcess = spawn('ffprobe', ffprobeArgs);

            let output = '';
            let error = '';

            ffprobeProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            ffprobeProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            ffprobeProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const metadata = JSON.parse(output);
                        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');

                        if (!videoStream) {
                            reject(new Error('未找到视频流'));
                            return;
                        }

                        const result = {
                            width: videoStream.width,
                            height: videoStream.height,
                            duration: parseFloat(metadata.format.duration),
                            bitrate: parseInt(metadata.format.bit_rate),
                            codec: videoStream.codec_name,
                            frameRate: eval(videoStream.r_frame_rate) // 计算帧率
                        };

                        logger.info(`${execLogPrefix}[元数据获取] 视频信息: ${result.width}x${result.height}, ${result.duration.toFixed(2)}秒`);
                        resolve(result);
                    } catch (parseError) {
                        reject(new Error(`解析视频元数据失败: ${parseError.message}`));
                    }
                } else {
                    reject(new Error(`ffprobe执行失败，退出码: ${code}, 错误: ${error}`));
                }
            });

            ffprobeProcess.on('error', (error) => {
                reject(new Error(`ffprobe进程启动失败: ${error.message}`));
            });
        });
    }
}

module.exports = VideoCompositionTask;
logger.info(`${taskModuleLogPrefix}VideoCompositionTask 类已导出。`);
