// 导入文件系统操作模块，用于文件存在性检查
const fs = require('fs');
// 导入路径处理模块，用于文件路径解析和标识符提取
const path = require('path');
// 导入HTTP客户端，用于调用Azure OpenAI API
const axios = require('axios');
// 导入表单数据处理模块，用于构建multipart/form-data请求
const FormData = require('form-data');
// 导入TaskBase基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');


// 导入配置加载器，用于获取Azure Speech Service相关配置
const loadConfig = require('../config');
// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入JSON校验与修复工具
const { extractAndParseJson } = require('../utils/jsonValidator');
// 导入文件保存工具，用于保存转录JSON文件
const fileSaver = require('../utils/fileSaver');

// 加载应用配置，包含Azure Speech Service的端点、密钥等信息
const config = loadConfig;
// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：GetTranscriptionTask.js][Azure语音转录任务][模块初始化]';

// 记录模块加载成功的日志
logger.info(`${taskModuleLogPrefix}模块已加载。`);

/**
 * @功能概述: Azure Speech Service Fast Transcription API转录任务类，负责将音频文件转录为文本。
 *           使用Azure Speech Service进行语音识别，支持细粒度的进度追踪。
 * 
 * @继承关系: 继承自 TaskBase，获得标准化的进度监控和状态管理能力
 * 
 * @进度阶段: 提供细粒度的进度阶段，通过 reportProgress 和 reportLLMProgress 方法报告：
 *   - INITIALIZING: 初始化和配置验证阶段
 *   - PROCESSING: 输入文件验证阶段
 *   - LLM_PREPARING: 准备API请求 (构建FormData)
 *   - LLM_SENDING: 发送API请求到Azure Speech Service
 *   - LLM_WAITING: 等待Azure Speech Service API响应
 *   - LLM_RECEIVING: 接收和初步处理API响应
 *   - FINALIZING: 数据格式转换和任务完成
 * 
 * @API参数: (由Azure Speech Service Fast Transcription API定义)
 *   - definition: 包含locales、profanityFilterMode、channels等配置
 *   - timeout: 300,000 ms (5分钟，可配置)
 * 
 * @数据转换: 将Azure Speech Service返回的格式转换为兼容的Whisper格式：
 *   - durationMilliseconds -> duration (毫秒转秒)
 *   - phrases -> segments (包含时间戳和置信度转换)
 *   - words字段格式转换 (offsetMilliseconds + durationMilliseconds -> start/end)
 */
class GetTranscriptionTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建Azure转录任务实例。
     * @param {string} [name='GetTranscriptionTask'] - 任务名称，用于日志标识和进度追踪。
     * 
     * @说明: 调用父类构造函数初始化基础属性（如任务ID、初始状态），并设置实例级日志前缀。
     */
    constructor(name = 'GetTranscriptionTask') {
        super(name); // 调用 TaskBase 构造函数，初始化任务ID、状态等基础属性
        // 设置实例级日志前缀，包含文件名、任务类型和实例名称
        this.instanceLogPrefix = `[文件：GetTranscriptionTask.js][Azure语音转录任务][${this.name}]`;
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} GetTranscriptionTask 实例已创建。`);
    }



    

   
    async execute(context, progressCallback) {
        // 步骤 1.1: 设置执行级日志前缀
        const reqId = context.reqId || 'unknown_transcription_req';
        const videoIdentifier = context.videoIdentifier; 
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${videoIdentifier}]`;

        // 步骤 1.2: 校验必需的上下文参数
        // originalVideoName 是可选的，主要用于日志或辅助识别，不影响核心功能
        const requiredFields = ['reqId', 'videoIdentifier', 'audioFilePathInUploads', 'savePath'];

        // 使用标准化的字段验证
        for (const field of requiredFields) {
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                const errorMsg = `执行失败：上下文缺少必需或无效的字段 "${field}". 当前值: '${context[field]}'`;
                logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
                const validationError = new Error(errorMsg);
                this.fail(validationError); // 标记任务失败
                throw validationError;
            }
        }
        logger.debug(`${execLogPrefix} 输入参数验证通过。必需字段: ${requiredFields.join(', ')}。`);
        
        // 步骤 1.3: 设置进度回调函数
        this.setProgressCallback(progressCallback);
        // 步骤 1.4: 标记任务开始并报告初始状态
        this.start(); 
        logger.info(`${execLogPrefix} GetTranscriptionTask execute 方法开始。`);

        // 从上下文中解构参数，方便后续使用
        const { audioFilePathInUploads, originalVideoName, savePath } = context; // savePath 已在 requiredFields 中校验

        try {
            // 步骤 2.1: 报告配置验证阶段的进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '验证Azure Speech Service服务配置',
                current: 10,
                total: 100,
                technicalDetail: '检查 Speech Endpoint, Key, API Version, Locale'
            });

            // 步骤 2.2: 从配置中提取Azure Speech Service相关参数
            const { speechEndpoint, speechKey, apiVersion, defaultLocale, profanityFilter, requestTimeout } = config;
            // 步骤 2.3: 记录配置加载状态（不记录敏感信息如密钥内容）
            logger.info(`${execLogPrefix}[步骤 2.2] Azure Speech配置加载: Endpoint=${speechEndpoint}, KeyLoaded=${!!speechKey}, ApiVersion=${apiVersion || '2024-11-15'}, Locale=${defaultLocale || 'en-US'}`);
            
            if (!speechEndpoint || !speechKey) {
                const errorMsg = '执行失败：Azure Speech Service 配置参数 (speechEndpoint 或 speechKey) 缺失。';
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                const configError = new Error(errorMsg);
                this.fail(configError); // 标记任务失败
                throw configError;
            }

            // 步骤 2.4: 构建API URL
            const usedApiVersion = apiVersion || '2024-11-15';
            const apiUrl = `${speechEndpoint}/speechtotext/transcriptions:transcribe?api-version=${usedApiVersion}`;
            logger.info(`${execLogPrefix}[步骤 2.4] 构建的 Azure Speech API URL: ${apiUrl}`);

            // 步骤 3.1: 报告文件验证阶段的进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '验证输入音频文件',
                current: 20,
                total: 100,
                technicalDetail: `检查文件路径: ${audioFilePathInUploads}`
            });

            // 步骤 3.2: 检查音频文件是否存在
            if (!fs.existsSync(audioFilePathInUploads)) {
                const errorMsg = `执行失败：指定的音频文件不存在于路径 ${audioFilePathInUploads}`;
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                const fileError = new Error(errorMsg);
                this.fail(fileError); // 标记任务失败
                throw fileError;
            }
            logger.info(`${execLogPrefix}[步骤 3.2] 确认音频文件存在: ${audioFilePathInUploads}`);

            // 步骤 4.1: 报告API请求准备阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_PREPARING, '准备Azure Speech Service API请求 (构建FormData)', {
                current: 30,
                total: 100,
                technicalDetail: `文件大小: ${fs.statSync(audioFilePathInUploads).size} bytes`
            });

            // 步骤 4.2: 创建音频文件的读取流并监听错误
            logger.info(`${execLogPrefix}[步骤 4.2] 为音频文件 ${audioFilePathInUploads} 创建读取流。`);
            const fileStream = fs.createReadStream(audioFilePathInUploads);
            fileStream.on('error', (streamError) => {
                // 这个错误是异步的，如果发生在API调用之后可能不会被主try-catch捕获，
                // 但通常如果文件有问题，流创建时或读取早期就会失败。
                logger.error(`${execLogPrefix}[ASYNC_STREAM_ERROR] 音频文件读取流发生错误: ${streamError.message}`);
                // 考虑是否需要在这里通过某种机制中断任务，如果任务还在进行中
                // 但通常 axios 的 FormData 处理会在流出错时导致请求失败，被主 try-catch 捕获
            });

            // 步骤 4.3: 创建FormData对象和definition配置
            const formData = new FormData();
            formData.append('audio', fileStream); // Azure Speech Service使用'audio'字段
            
            // 构建definition配置（参考testSpeech.js）
            const definition = {
                locales: [defaultLocale || 'en-US'],
                profanityFilterMode: profanityFilter || 'Masked',
                channels: [0, 1] // 处理立体声
            };
            formData.append('definition', JSON.stringify(definition));
            logger.info(`${execLogPrefix}[步骤 4.3] FormData 构建完成。参数: audio, definition=${JSON.stringify(definition)}.`);

            // 步骤 5.1: 构建请求头
            const headers = {
                'Accept': 'application/json',
                'Ocp-Apim-Subscription-Key': speechKey, // Azure Speech Service使用Ocp-Apim-Subscription-Key
                ...formData.getHeaders() // FormData 会自动生成 'Content-Type': 'multipart/form-data; boundary=...'
            };
            logger.debug(`${execLogPrefix}[步骤 5.1] 请求头设置完成。Content-Type: ${headers['Content-Type']}`);

            // 步骤 5.2: 报告API请求发送阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_SENDING, '发送请求到Azure Speech Service服务', {
                current: 40,
                total: 100,
                technicalDetail: `目标 URL: ${apiUrl.substring(0, 80)}...`
            });
            
            const configuredTimeout = requestTimeout || config.requestTimeout || 300000; // 从配置获取超时或默认5分钟
            logger.info(`${execLogPrefix}[步骤 5.2 AXIOS调用前] API地址: ${apiUrl}, 超时: ${configuredTimeout}ms.`);
            
            // 步骤 5.3: 发送HTTP POST请求
            logger.info(`${execLogPrefix}[步骤 5.3] 开始发送 POST 请求到 Azure Speech API: ${apiUrl}`);
            
            // 步骤 5.4: 报告等待API响应阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_WAITING, '等待Azure Speech Service处理请求并返回响应', {
                current: 60,
                total: 100,
                technicalDetail: `预估音频时长相关的处理时间... (超时设置: ${configuredTimeout / 1000}s)`
            });

            const response = await axios.post(apiUrl, formData, { 
                headers,
                timeout: configuredTimeout
            });

            // 步骤 6.1: 报告API响应接收阶段
            this.reportLLMProgress(TASK_SUBSTATUS.LLM_RECEIVING, '成功接收Azure Speech Service API响应', {
                current: 80,
                total: 100,
                technicalDetail: `响应状态码: ${response.status}`
            });

            // 步骤 6.2: 记录响应信息
            logger.info(`${execLogPrefix}[步骤 6.2] Azure Speech API 请求成功，状态码: ${response.status}`);
            logger.debug(`${execLogPrefix} Azure Speech API 响应头 (部分): ${JSON.stringify({ 
                'content-type': response.headers['content-type'], 
                'date': response.headers['date']
            })}`);
            // 避免记录完整的响应到常规日志，它可能非常大
            logger.debug(`${execLogPrefix} Azure Speech API 响应数据预览 (前200字符): ${JSON.stringify(response.data).substring(0, 200)}...`);
            if (response.data && response.data.combinedPhrases && response.data.combinedPhrases.length > 0) {
                logger.info(`${execLogPrefix} Azure Speech API 返回转录文本预览 (前100字符): ${String(response.data.combinedPhrases[0].text).substring(0,100)}...`);
            }


            // 步骤 7.1: 数据格式转换和校验
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FINALIZING, {
                detail: '转换Azure Speech数据格式并保存转录JSON文件',
                current: 90,
                total: 100,
                technicalDetail: '将Azure Speech Service格式转换为兼容的Whisper格式'
            });

            let validatedApiResponse = response.data;
            let convertedWhisperFormatData = null;
            
            try {
                // 使用JSON校验工具验证和修复API响应
                const jsonString = JSON.stringify(response.data);
                const validationResult = extractAndParseJson(jsonString, `${execLogPrefix}[JSON校验]`);

                if (validationResult.success) {
                    validatedApiResponse = validationResult.data;
                    logger.info(`${execLogPrefix}[步骤 7.1] API响应JSON校验成功`);
                } else {
                    logger.warn(`${execLogPrefix}[步骤 7.1] API响应JSON校验失败，使用原始数据: ${validationResult.error}`);
                }

                // 步骤 7.1.2: 转换数据格式（从Azure Speech格式转换为Whisper兼容格式）
                logger.info(`${execLogPrefix}[步骤 7.1.2] 开始转换Azure Speech数据格式为Whisper兼容格式`);
                convertedWhisperFormatData = this.convertAzureSpeechToWhisperFormat(validatedApiResponse, execLogPrefix);
                logger.info(`${execLogPrefix}[步骤 7.1.2] 数据格式转换完成，segments数量: ${convertedWhisperFormatData.segments?.length || 0}`);
                
            } catch (validationError) {
                logger.warn(`${execLogPrefix}[步骤 7.1] JSON校验或数据转换过程出错: ${validationError.message}`);
                // 如果转换失败，仍尝试使用原始数据
                convertedWhisperFormatData = validatedApiResponse;
            }

            // 步骤 7.2: 保存转录JSON文件（保存转换后的格式）
            let transcriptionJsonPath = null;
            try {
                transcriptionJsonPath = await this.saveTranscriptionJson(
                    convertedWhisperFormatData, // 保存转换后的Whisper兼容格式
                    videoIdentifier,
                    execLogPrefix,
                    savePath
                );
                logger.info(`${execLogPrefix}[步骤 7.2] 转录JSON文件保存成功: ${transcriptionJsonPath}`);
            } catch (saveError) {
                logger.error(`${execLogPrefix}[步骤 7.2] 转录JSON文件保存失败: ${saveError.message}`);
                // 保存失败不影响任务成功，继续执行
            }

            // 步骤 7.3: 构建任务结果（使用转换后的数据，保持与现有流水线的兼容性）
            const result = {
                transcriptionStatus: 'success', // 表示API调用成功并获取到响应
                apiResponse: convertedWhisperFormatData, // 转换后的Whisper兼容格式数据（保持现有上下文参数名不变）
                transcriptionJsonPath: transcriptionJsonPath, // 保存的JSON文件路径
                processedAudioPath: audioFilePathInUploads, // 实际处理的音频文件路径
                videoIdentifier: videoIdentifier, // 传递视频标识符
                reqId: reqId, // 传递请求ID
                savePath: savePath // 传递保存路径
            };

            // 步骤 7.4: 标记任务成功完成
            this.complete(result);
            logger.info(`${execLogPrefix}[步骤 7.4] 转录任务执行成功。API响应已校验并保存。`);

            // 步骤 7.5: 返回结果
            return result;

        } catch (error) {
            // 步骤 8.1 & 8.2: 捕获错误并分类
            logger.error(`${execLogPrefix}[ERROR_HANDLER] 调用 Azure Speech Service API 进行转录时捕获到错误: ${error.message}`);
            
            let errorCategory = TASK_SUBSTATUS.FAILED_UNKNOWN; // 默认错误子状态
            
            if (error.code === 'ECONNABORTED' || (error.message && error.message.toLowerCase().includes('timeout'))) {
                errorCategory = TASK_SUBSTATUS.FAILED_API_TIMEOUT;
                logger.error(`${execLogPrefix}[${errorCategory}] Azure Speech Service API 调用超时。配置超时: ${configuredTimeout}ms.`);
            } else if (error.response) { 
                errorCategory = TASK_SUBSTATUS.FAILED_API_ERROR;
                logger.error(`${execLogPrefix}[${errorCategory}] Azure Speech Service API 返回错误状态: ${error.response.status}`);
                logger.error(`${execLogPrefix}[${errorCategory}] Azure Speech Service API 错误响应体: ${JSON.stringify(error.response.data)}`);
                logger.debug(`${execLogPrefix}[${errorCategory}] Azure Speech Service API 错误响应头: ${JSON.stringify(error.response.headers)}`);
            } else if (error.request) { 
                errorCategory = TASK_SUBSTATUS.FAILED_NETWORK; // 或更具体的 FAILED_API_NO_RESPONSE
                logger.error(`${execLogPrefix}[${errorCategory}] 请求已发送但未收到Azure Speech Service API响应。网络问题或上游无响应。`);
                logger.debug(`${execLogPrefix}[${errorCategory}] 未收到响应的请求详情 (部分): ${JSON.stringify({ method: error.request.method, path: error.request.path })}`);
            } else if (error.message && error.message.startsWith('执行失败：')) {
                errorCategory = TASK_SUBSTATUS.FAILED_VALIDATION; // 参数校验或文件校验失败
                // 错误信息已在 validateRequiredFields 或文件检查点记录
            } else { 
                errorCategory = TASK_SUBSTATUS.FAILED_UNEXPECTED;
                logger.error(`${execLogPrefix}[${errorCategory}] 设置到Azure Speech Service API的请求或任务内部发生意外错误: ${error.message}`);
            }

            logger.error(`${execLogPrefix}[错误堆栈][${errorCategory}] ${error.stack}`);

            // 步骤 8.3: 标记任务失败
            this.fail(error); // 使用TaskBase标准的fail方法
            
            // 步骤 8.4: 重新抛出错误
            throw error;
        }
    }

    /**
     * @功能概述: 收集GetTranscriptionTask的详细上下文信息
     * @returns {object} 包含转录获取特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加转录获取特定的上下文信息
     *   - 包含Azure OpenAI API详情、音频处理参数、转录历史等
     *   - 遵循TaskBase标准化的上下文收集模式
     *   - 保持与其他任务类的一致性
     *
     * @返回对象扩展:
     *   - transcriptionProcessingDetails: 转录处理特定信息
     *   - azureApiDetails: Azure OpenAI API交互详情
     *   - inputAudioInfo: 输入音频信息
     *   - outputTranscriptionInfo: 输出转录信息
     *   - transcriptionParameters: 转录参数详情
     *   - transcriptionHistory: 转录历史
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取转录信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                audioFilePathInUploads: taskResult.processedAudioPath || 'N/A',
                audioFileName: taskResult.processedAudioPath ?
                    path.basename(taskResult.processedAudioPath) : 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                reqId: taskResult.reqId || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                inputFileExists: taskResult.processedAudioPath ?
                    fs.existsSync(taskResult.processedAudioPath) : false,
                inputFileSize: taskResult.processedAudioPath && fs.existsSync(taskResult.processedAudioPath) ?
                    fs.statSync(taskResult.processedAudioPath).size : 'N/A'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                transcriptionStatus: taskResult.transcriptionStatus || 'N/A',
                transcriptionJsonPath: taskResult.transcriptionJsonPath || 'N/A',
                transcriptionText: taskResult.apiResponse?.text ?
                    taskResult.apiResponse.text.substring(0, 100) + '...' : 'N/A',
                transcriptionLanguage: taskResult.apiResponse?.language || 'N/A',
                segmentsCount: taskResult.apiResponse?.segments?.length || 'N/A',
                transcriptionDuration: taskResult.apiResponse?.duration || 'N/A',
                outputFileExists: taskResult.transcriptionJsonPath ?
                    fs.existsSync(taskResult.transcriptionJsonPath) : false
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'GetTranscription',
                apiProvider: 'Azure Speech Service',
                apiModel: 'Fast Transcription',
                supportedInputFormats: ['mp3', 'wav', 'mp4', 'm4a', 'flac'],
                outputFormat: 'whisper_compatible_json',
                processingMode: 'azure_speech_api',
                timeout: config.requestTimeout || config.azureApiTimeout || 300000,
                timeoutManagement: 'axios_timeout',
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                dataConversionEnabled: true
            };

            // 转录处理特定信息
            const transcriptionProcessingDetails = {
                processingSteps: [
                    '参数验证',
                    'Azure Speech配置验证',
                    '音频文件验证',
                    'API请求准备',
                    'Azure Speech API调用',
                    '数据格式转换',
                    'JSON校验和修复',
                    '文件保存',
                    '响应处理'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '响应处理' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中',
                stepProgress: this.status === TASK_STATUS.COMPLETED ? '9/9' :
                            this.status === TASK_STATUS.FAILED ? 'N/A' : 'N/A',
                processingMethod: 'azure_speech_service_api',
                qualityLevel: 'high',
                languageDetection: 'automatic',
                timestampPrecision: 'millisecond',
                dataConversion: 'azure_to_whisper_format'
            };

            // Azure API交互详情
            const azureApiDetails = {
                provider: 'Azure Speech Service',
                model: 'Fast Transcription',
                speechEndpoint: config.speechEndpoint || 'N/A',
                speechRegion: config.speechRegion || 'N/A',
                apiVersion: config.apiVersion || '2024-11-15',
                requestMethod: 'POST',
                requestFormat: 'multipart/form-data',
                responseFormat: 'azure_speech_json',
                errorHandling: 'comprehensive',
                retryStrategy: 'none',
                timeoutConfiguration: config.requestTimeout || config.azureApiTimeout || 300000,
                authenticationMethod: 'subscription_key',
                connectionStatus: this.status === TASK_STATUS.COMPLETED ? 'success' :
                               this.status === TASK_STATUS.FAILED ? 'failed' : 'unknown'
            };

            // 输入音频信息（任务特定）
            const inputAudioInfo = {
                audioFilePathInUploads: taskResult.processedAudioPath || 'N/A',
                audioFileName: taskResult.processedAudioPath ?
                    path.basename(taskResult.processedAudioPath) : 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'audio_file',
                inputSource: 'converted_from_video',
                fileExists: taskResult.processedAudioPath ?
                    fs.existsSync(taskResult.processedAudioPath) : false,
                fileSize: taskResult.processedAudioPath && fs.existsSync(taskResult.processedAudioPath) ?
                    fs.statSync(taskResult.processedAudioPath).size : 'N/A'
            };

            // 输出转录信息（任务特定）
            const outputTranscriptionInfo = {
                transcriptionStatus: taskResult.transcriptionStatus || 'N/A',
                transcriptionJsonPath: taskResult.transcriptionJsonPath || 'N/A',
                apiResponseReceived: taskResult.apiResponse ? true : false,
                transcriptionText: taskResult.apiResponse?.text ?
                    taskResult.apiResponse.text.substring(0, 100) + '...' : 'N/A',
                transcriptionLanguage: taskResult.apiResponse?.language || 'N/A',
                segmentsCount: taskResult.apiResponse?.segments?.length || 'N/A',
                transcriptionDuration: taskResult.apiResponse?.duration || 'N/A',
                transcriptionQuality: 'azure_speech_enhanced',
                outputFormat: 'whisper_compatible_json_with_segments',
                dataConversionApplied: true,
                originalAzureFormat: 'azure_speech_json',
                jsonFileExists: taskResult.transcriptionJsonPath ?
                    fs.existsSync(taskResult.transcriptionJsonPath) : false
            };

            // 转录参数详情（任务特定）
            const transcriptionParameters = {
                reqId: taskResult.reqId || 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                processedAudioPath: taskResult.processedAudioPath || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                responseFormat: 'whisper_compatible_json',
                transcriptionMethod: 'azure_speech_service',
                apiTimeout: config.requestTimeout || config.azureApiTimeout || 300000,
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                dataConversionEnabled: true,
                locale: config.defaultLocale || 'en-US',
                profanityFilter: config.profanityFilter || 'Masked'
            };

            // 转录历史详情（任务特定）
            const transcriptionHistory = {
                transcriptionType: 'azure_speech_api',
                inputFormat: 'audio_file',
                outputFormat: 'whisper_compatible_json',
                transcriptionMethod: 'azure_speech_service',
                processingTime: this.getElapsedTime(),
                transcriptionSuccess: this.status === TASK_STATUS.COMPLETED,
                errorOccurred: this.status === TASK_STATUS.FAILED,
                dataConversionPerformed: true,
                originalFormat: 'azure_speech_json',
                convertedFormat: 'whisper_format',
                lastError: this.error ? {
                    message: this.error.message,
                    name: this.error.name,
                    type: this.error.constructor.name
                } : null,
                progressUpdatesCount: this.progressHistory.length,
                lastProgressUpdate: this.progressHistory.length > 0 ?
                    this.progressHistory[this.progressHistory.length - 1] : null
            };

            // 合并所有上下文信息（遵循TaskBase标准结构）
            const extendedContext = {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                transcriptionProcessingDetails,
                azureApiDetails,
                inputAudioInfo,
                outputTranscriptionInfo,
                transcriptionParameters,
                transcriptionHistory,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'GetTranscriptionTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集GetTranscriptionTask详细上下文信息`);
            logger.info(`${logPrefix} 上下文包含 ${Object.keys(extendedContext).length} 个主要部分`);

            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                transcriptionProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'GetTranscriptionTask.collectDetailedContext (with error)'
            };
        }
    }

    /**
     * @功能概述: 保存转录JSON文件
     * @param {object} transcriptionData - 转录数据对象
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @param {string} savePath - 保存路径
     * @returns {Promise<string>} 保存的文件路径
     *
     * @说明:
     *   - 参照TranscriptionCorrectionTask的保存方法
     *   - 使用标准化的文件命名：[视频标识符]_transcription.json
     *   - 调用通用文件保存工具fileSaver
     *   - 包含完整的错误处理和日志记录
     */
    async saveTranscriptionJson(transcriptionData, videoIdentifier, execLogPrefix, savePath) {
        // 生成标准化文件名：[视频标识符]_transcription.json
        const filename = `${videoIdentifier}_transcription.json`;

        try {
            // 调用通用文件保存工具（参数说明：数据内容，文件名，基础目录，日志前缀）
            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(transcriptionData, null, 2), // 格式化JSON
                filename, // 参数2: 纯文件名
                savePath, // 参数3: 保存目录
                execLogPrefix // 参数4: 日志前缀
            );

            // 有效性检查：确保返回路径符合预期
            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }
            return savedPath;
        } catch (saveError) {
            // 在错误信息中增加完整路径信息便于调试
            const errorMsg = `保存转录 JSON 文件失败: ${saveError.message}，路径：${savePath}/${filename}`;
            logger.error(`${execLogPrefix}[ERROR][saveTranscriptionJson] ${errorMsg}`);
            // 向上层抛出标准化错误
            throw new Error(errorMsg);
        }
    }

    /**
     * @功能概述: 将Azure Speech Service的响应格式转换为Whisper兼容格式
     * @param {object} azureSpeechData - Azure Speech Service的原始响应数据
     * @param {string} logPrefix - 日志前缀
     * @returns {object} 转换后的Whisper兼容格式数据
     * 
     * @转换规则:
     *   1. durationMilliseconds -> duration (毫秒转秒)
     *   2. combinedPhrases[0].text -> text
     *   3. phrases -> segments 的逐个转换:
     *      - offsetMilliseconds -> start (毫秒转秒)
     *      - offsetMilliseconds + durationMilliseconds -> end (毫秒转秒)
     *      - text -> text
     *      - confidence -> 模拟 avg_logprob, compression_ratio, no_speech_prob
     *      - words 字段转换 (offsetMilliseconds和durationMilliseconds转换为start和end)
     */
    convertAzureSpeechToWhisperFormat(azureSpeechData, logPrefix) {
        try {
            logger.info(`${logPrefix}[转换] 开始将Azure Speech格式转换为Whisper兼容格式`);
            
            // 步骤 1: 转换基本信息
            const convertedData = {
                task: 'transcribe', // 固定值，保持与原格式一致
                language: azureSpeechData.locale ? azureSpeechData.locale.split('-')[0] : 'en', // 从locale中提取语言代码
                duration: azureSpeechData.durationMilliseconds ? azureSpeechData.durationMilliseconds / 1000 : 0, // 毫秒转秒
                text: azureSpeechData.combinedPhrases && azureSpeechData.combinedPhrases.length > 0 
                      ? azureSpeechData.combinedPhrases[0].text 
                      : '', // 提取combinedPhrases的第一个text
                segments: [] // 将由phrases转换而来
            };
            
            logger.debug(`${logPrefix}[转换] 基本信息转换完成: duration=${convertedData.duration}s, language=${convertedData.language}`);
            
            // 步骤 2: 转换segments（从phrases转换）
            if (azureSpeechData.phrases && Array.isArray(azureSpeechData.phrases)) {
                logger.info(`${logPrefix}[转换] 开始转换 ${azureSpeechData.phrases.length} 个phrases为segments`);
                
                azureSpeechData.phrases.forEach((phrase, index) => {
                    // 计算时间戳（毫秒转秒）
                    const startTime = phrase.offsetMilliseconds ? phrase.offsetMilliseconds / 1000 : 0;
                    const endTime = phrase.offsetMilliseconds && phrase.durationMilliseconds 
                                  ? (phrase.offsetMilliseconds + phrase.durationMilliseconds) / 1000 
                                  : startTime;
                    
                    // 模拟Whisper的统计参数（基于confidence值）
                    const confidence = phrase.confidence || 0.8; // 默认confidence
                    const avgLogprob = this.simulateAvgLogprob(confidence);
                    const compressionRatio = this.simulateCompressionRatio(phrase.text);
                    const noSpeechProb = this.simulateNoSpeechProb(confidence);
                    
                    // 转换words字段
                    const convertedWords = this.convertWordsField(phrase.words, logPrefix);
                    
                    // 构建segment对象
                    const segment = {
                        id: index, // 从0开始递增
                        seek: 0, // 固定值
                        start: startTime,
                        end: endTime,
                        text: phrase.text || '',
                        tokens: [], // 固定为空数组
                        temperature: 0, // 固定值
                        avg_logprob: avgLogprob,
                        compression_ratio: compressionRatio,
                        no_speech_prob: noSpeechProb,
                        words: convertedWords // 转换后的words字段
                    };
                    
                    convertedData.segments.push(segment);
                });
                
                logger.info(`${logPrefix}[转换] segments转换完成，共 ${convertedData.segments.length} 个segments`);
            } else {
                logger.warn(`${logPrefix}[转换] 未找到phrases数据或格式不正确`);
            }
            
            logger.info(`${logPrefix}[转换] Azure Speech数据格式转换完成`);
            return convertedData;
            
        } catch (error) {
            logger.error(`${logPrefix}[转换] 数据格式转换失败: ${error.message}`);
            throw new Error(`Azure Speech数据格式转换失败: ${error.message}`);
        }
    }
    
    /**
     * @功能概述: 转换words字段格式
     * @param {Array} azureWords - Azure Speech的words数组
     * @param {string} logPrefix - 日志前缀
     * @returns {Array} 转换后的words数组
     */
    convertWordsField(azureWords, logPrefix) {
        if (!azureWords || !Array.isArray(azureWords)) {
            return [];
        }
        
        return azureWords.map(word => {
            const startTime = word.offsetMilliseconds ? word.offsetMilliseconds / 1000 : 0;
            const endTime = word.offsetMilliseconds && word.durationMilliseconds 
                          ? (word.offsetMilliseconds + word.durationMilliseconds) / 1000 
                          : startTime;
            
            return {
                text: word.text || '',
                start: startTime,
                end: endTime
            };
        });
    }
    
    /**
     * @功能概述: 基于confidence模拟avg_logprob值
     * @param {number} confidence - Azure Speech的confidence值 (0-1)
     * @returns {number} 模拟的avg_logprob值
     */
    simulateAvgLogprob(confidence) {
        // avg_logprob通常是负值，confidence越高，avg_logprob越接近0
        // 参考default.md中的说明：avg_logprob是token概率的对数平均值
        return -((1 - confidence) * 2); // 将0-1的confidence转换为-2到0的avg_logprob
    }
    
    /**
     * @功能概述: 基于文本长度模拟compression_ratio值
     * @param {string} text - 文本内容
     * @returns {number} 模拟的compression_ratio值
     */
    simulateCompressionRatio(text) {
        if (!text) return 1.0;
        // compression_ratio通常在1.0-3.0之间，文本越长比率可能越高
        const textLength = text.length;
        return Math.min(1.0 + (textLength / 100), 3.0);
    }
    
    /**
     * @功能概述: 基于confidence模拟no_speech_prob值
     * @param {number} confidence - Azure Speech的confidence值 (0-1)
     * @returns {number} 模拟的no_speech_prob值
     */
    simulateNoSpeechProb(confidence) {
        // no_speech_prob是检测到非语音的概率，confidence越高，no_speech_prob应该越低
        return Math.max(0, (1 - confidence) * 0.5); // 将confidence转换为0-0.5的no_speech_prob
    }
}

// 导出GetTranscriptionTask类，供其他模块使用
module.exports = GetTranscriptionTask;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}GetTranscriptionTask 类已导出。`); 