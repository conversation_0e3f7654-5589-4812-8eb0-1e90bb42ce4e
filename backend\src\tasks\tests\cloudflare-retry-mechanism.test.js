/**
 * Cloudflare API 智能重试机制测试
 * 验证新增的重试功能是否能有效处理API超时和网络错误
 */

const GetTranscriptionTaskByCloudflare = require('../GetTranscriptionTaskByCloudflare');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    reqId: 'retry-mechanism-test',
    videoIdentifier: 'test_retry_mechanism',
    audioFilePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3',
    savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output'
};

async function testRetryMechanism() {
    console.log('🔄 开始测试 Cloudflare API 智能重试机制');
    console.log('📊 测试音频文件:', TEST_CONFIG.audioFilePath);
    
    // 检查音频文件是否存在
    if (!fs.existsSync(TEST_CONFIG.audioFilePath)) {
        console.error('❌ 测试音频文件不存在:', TEST_CONFIG.audioFilePath);
        return;
    }
    
    const audioStats = fs.statSync(TEST_CONFIG.audioFilePath);
    console.log('📁 音频文件大小:', (audioStats.size / 1024 / 1024).toFixed(2), 'MB');
    
    try {
        // 创建任务实例
        const task = new GetTranscriptionTaskByCloudflare();
        
        // 构建上下文
        const context = {
            reqId: TEST_CONFIG.reqId,
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            audioFilePathInUploads: TEST_CONFIG.audioFilePath,
            savePath: TEST_CONFIG.savePath
        };
        
        console.log('🚀 开始测试带重试机制的 Cloudflare Workers AI API...');
        console.log('📋 重试机制特性:');
        console.log('  - 最大重试次数: 5次');
        console.log('  - 指数退避延迟: 2s, 4s, 8s, 16s, 32s');
        console.log('  - 随机抖动: 0-1秒');
        console.log('  - 可重试错误: 超时(408), 服务器错误(5xx)');
        console.log('  - 不可重试错误: 客户端错误(4xx)');
        
        const startTime = Date.now();
        
        // 执行转录任务（带重试机制）
        const result = await task.execute(context);
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log('✅ 重试机制测试成功！');
        console.log('⏱️  总耗时:', duration, '秒');
        console.log('📊 转录状态:', result.transcriptionStatus);
        
        // 读取生成的转录文件
        const transcriptionPath = path.join(TEST_CONFIG.savePath, `${TEST_CONFIG.videoIdentifier}_transcription.json`);
        if (fs.existsSync(transcriptionPath)) {
            const transcriptionData = JSON.parse(fs.readFileSync(transcriptionPath, 'utf8'));
            
            console.log('\n📋 转录结果分析:');
            console.log('- 语言:', transcriptionData.language);
            console.log('- 时长:', transcriptionData.duration, '秒');
            console.log('- 文本长度:', transcriptionData.text.length, '字符');
            console.log('- segments 数量:', transcriptionData.segments.length);
            
            // 分析segments详情
            let totalWords = 0;
            transcriptionData.segments.forEach((segment, index) => {
                const wordsCount = segment.words ? segment.words.length : 0;
                totalWords += wordsCount;
                if (index < 3) { // 只显示前3个segments
                    console.log(`  - Segment ${index}: ${segment.start.toFixed(2)}s-${segment.end.toFixed(2)}s, ${wordsCount} words`);
                }
            });
            
            console.log('- 总计 words:', totalWords);
            
            // 验证重试机制效果
            console.log('\n🎯 重试机制验证结果:');
            if (transcriptionData.segments.length > 1) {
                console.log('✅ segments 数据完整:', transcriptionData.segments.length, '个');
            }
            
            if (totalWords > 0) {
                console.log('✅ words 数据完整:', totalWords, '个');
            }
            
            console.log('✅ 重试机制成功处理了API调用');
            
        } else {
            console.error('❌ 转录文件未生成:', transcriptionPath);
        }
        
        console.log('\n🎉 重试机制测试完成！');
        
    } catch (error) {
        console.error('❌ 重试机制测试失败:', error.message);
        
        // 分析失败原因
        if (error.message.includes('所有') && error.message.includes('次重试均失败')) {
            console.log('📊 失败分析: 重试机制已正确执行，但所有重试均失败');
            console.log('🔍 可能原因:');
            console.log('  - Cloudflare API 服务暂时不可用');
            console.log('  - 网络连接问题');
            console.log('  - API 配额限制');
            console.log('  - 音频文件格式或大小问题');
        } else if (error.message.includes('非可重试错误')) {
            console.log('📊 失败分析: 遇到非可重试错误，重试机制正确跳过');
            console.log('🔍 可能原因:');
            console.log('  - API 密钥无效 (401)');
            console.log('  - 请求格式错误 (400)');
            console.log('  - 权限不足 (403)');
        } else {
            console.log('📊 失败分析: 其他错误');
        }
        
        console.log('📋 错误详情:', error);
    }
}

// 运行重试机制测试
if (require.main === module) {
    testRetryMechanism().catch(console.error);
}

module.exports = { testRetryMechanism };
