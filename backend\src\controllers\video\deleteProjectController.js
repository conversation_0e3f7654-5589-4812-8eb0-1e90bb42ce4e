/**
 * @文件名: deleteProjectController.js
 * @功能概述: 删除视频项目控制器
 * @创建时间: 2025-07-17
 * @作者: Augment Agent
 * @描述: 
 *   此控制器负责处理视频项目的删除操作，包括删除项目文件夹及其所有内容。
 *   遵循API创建指导规则，提供安全的项目删除功能。
 */

const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const PathHelper = require('../../utils/pathHelper');

// 模块日志前缀
const moduleLogPrefix = '[文件：deleteProjectController.js]';

/**
 * @功能概述: 删除视频项目
 * @接口路径: DELETE /api/video/deleteProject
 * @请求参数:
 *   - videoIdentifier (必需): 项目标识符，通过请求体传递
 * @响应格式: 
 *   - status: success/error
 *   - message: 响应消息
 *   - data: { deletedProject: videoIdentifier }
 * @错误处理: 
 *   - 项目不存在：返回404错误
 *   - 权限问题：返回403错误
 *   - 删除失败：返回500错误
 * @安全性: 
 *   - 验证项目标识符格式
 *   - 确保只删除项目目录内的文件
 *   - 记录删除操作日志
 */
const deleteProject = async (req, res) => {
    const logPrefix = `${moduleLogPrefix}[deleteProject]`;
    
    try {
        logger.info(`${logPrefix} 开始处理项目删除请求`);
        
        // === 步骤1: 参数验证 ===
        const { videoIdentifier } = req.body;
        
        if (!videoIdentifier) {
            logger.warn(`${logPrefix} 缺少必需参数: videoIdentifier`);
            return res.status(400).json({
                status: 'error',
                message: '缺少必需参数：videoIdentifier',
                errorCode: 'MISSING_PARAMETER',
                details: {
                    requiredParameter: 'videoIdentifier',
                    suggestion: '请在请求体中提供videoIdentifier参数'
                }
            });
        }
        
        // 验证videoIdentifier格式（基本安全检查）
        if (typeof videoIdentifier !== 'string' || videoIdentifier.length === 0) {
            logger.warn(`${logPrefix} 无效的videoIdentifier格式: ${videoIdentifier}`);
            return res.status(400).json({
                status: 'error',
                message: '无效的项目标识符格式',
                errorCode: 'INVALID_PARAMETER',
                details: {
                    parameter: 'videoIdentifier',
                    received: videoIdentifier,
                    suggestion: '项目标识符必须是非空字符串'
                }
            });
        }
        
        // 安全检查：防止路径遍历攻击
        if (videoIdentifier.includes('..') || videoIdentifier.includes('/') || videoIdentifier.includes('\\')) {
            logger.warn(`${logPrefix} 检测到潜在的路径遍历攻击: ${videoIdentifier}`);
            return res.status(400).json({
                status: 'error',
                message: '无效的项目标识符：包含非法字符',
                errorCode: 'INVALID_PARAMETER',
                details: {
                    parameter: 'videoIdentifier',
                    reason: '项目标识符不能包含路径分隔符或相对路径字符',
                    suggestion: '请使用有效的项目标识符'
                }
            });
        }
        
        logger.info(`${logPrefix} 请求删除项目: ${videoIdentifier}`);
        
        // === 步骤2: 检查项目是否存在 ===
        if (!PathHelper.projectExists(videoIdentifier)) {
            logger.warn(`${logPrefix} 项目不存在: ${videoIdentifier}`);
            return res.status(404).json({
                status: 'error',
                message: '项目不存在',
                errorCode: 'PROJECT_NOT_FOUND',
                details: {
                    videoIdentifier: videoIdentifier,
                    suggestion: '请检查项目标识符是否正确'
                }
            });
        }
        
        // === 步骤3: 获取项目路径信息 ===
        const projectDir = PathHelper.getProjectDir(videoIdentifier);
        const sourceDir = PathHelper.getSourceDir(videoIdentifier);
        const processedDir = PathHelper.getProcessedDir(videoIdentifier);
        const generatedDir = PathHelper.getGeneratedDir(videoIdentifier);
        
        logger.info(`${logPrefix} 项目目录路径: ${projectDir}`);
        
        // === 步骤4: 收集删除前的项目信息（用于日志记录） ===
        const projectInfo = await collectProjectInfoBeforeDeletion(videoIdentifier, {
            projectDir,
            sourceDir,
            processedDir,
            generatedDir
        });
        
        logger.info(`${logPrefix} 删除前项目信息: ${JSON.stringify(projectInfo)}`);
        
        // === 步骤5: 执行删除操作 ===
        logger.info(`${logPrefix} 开始删除项目目录: ${projectDir}`);
        
        await deleteDirectoryRecursively(projectDir);
        
        logger.info(`${logPrefix} 项目目录删除成功: ${projectDir}`);
        
        // === 步骤6: 验证删除结果 ===
        if (fs.existsSync(projectDir)) {
            logger.error(`${logPrefix} 删除验证失败：项目目录仍然存在: ${projectDir}`);
            return res.status(500).json({
                status: 'error',
                message: '项目删除失败：目录仍然存在',
                errorCode: 'DELETE_VERIFICATION_FAILED',
                details: {
                    videoIdentifier: videoIdentifier,
                    projectDir: projectDir,
                    suggestion: '请检查文件权限或联系管理员'
                }
            });
        }
        
        // === 步骤7: 记录删除操作 ===
        logger.info(`${logPrefix} 项目删除操作完成`);
        logger.info(`${logPrefix} 删除的项目信息: ${JSON.stringify({
            videoIdentifier: videoIdentifier,
            projectDir: projectDir,
            deletedFiles: projectInfo.totalFiles,
            deletedSize: projectInfo.totalSize,
            deletionTime: new Date().toISOString()
        })}`);
        
        // === 步骤8: 返回成功响应 ===
        const response = {
            status: 'success',
            message: '项目删除成功',
            data: {
                deletedProject: videoIdentifier,
                deletionInfo: {
                    projectDir: projectDir,
                    deletedFiles: projectInfo.totalFiles,
                    deletedSize: projectInfo.totalSize,
                    deletionTime: new Date().toISOString()
                }
            }
        };
        
        logger.info(`${logPrefix} 项目删除成功，返回响应`);
        res.json(response);
        
    } catch (error) {
        logger.error(`${logPrefix} 删除项目失败: ${error.message}`);
        logger.error(`${logPrefix} 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            status: 'error',
            message: '删除项目失败',
            errorCode: 'DELETE_PROJECT_FAILED',
            details: {
                error: error.message,
                suggestion: '请检查服务器状态或联系管理员'
            }
        });
    }
};

/**
 * @功能概述: 收集删除前的项目信息
 * @参数: videoIdentifier - 项目标识符, paths - 路径对象
 * @返回: 项目信息对象
 * @说明: 用于记录删除操作的详细信息
 */
const collectProjectInfoBeforeDeletion = async (videoIdentifier, paths) => {
    const logPrefix = `${moduleLogPrefix}[collectProjectInfoBeforeDeletion]`;
    
    try {
        let totalFiles = 0;
        let totalSize = 0;
        const directories = [];
        
        // 统计各目录的文件信息
        for (const [dirName, dirPath] of Object.entries(paths)) {
            if (dirName === 'projectDir') continue; // 跳过项目根目录
            
            if (fs.existsSync(dirPath)) {
                const dirInfo = await getDirectoryInfo(dirPath);
                directories.push({
                    name: dirName,
                    path: dirPath,
                    files: dirInfo.files,
                    size: dirInfo.size
                });
                totalFiles += dirInfo.files;
                totalSize += dirInfo.size;
            }
        }
        
        return {
            videoIdentifier: videoIdentifier,
            totalFiles: totalFiles,
            totalSize: formatFileSize(totalSize),
            directories: directories,
            collectionTime: new Date().toISOString()
        };
        
    } catch (error) {
        logger.warn(`${logPrefix} 收集项目信息失败: ${error.message}`);
        return {
            videoIdentifier: videoIdentifier,
            totalFiles: 0,
            totalSize: '未知',
            directories: [],
            collectionTime: new Date().toISOString(),
            error: error.message
        };
    }
};

/**
 * @功能概述: 获取目录信息
 * @参数: dirPath - 目录路径
 * @返回: { files: number, size: number }
 */
const getDirectoryInfo = async (dirPath) => {
    let files = 0;
    let size = 0;
    
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
            const subDirInfo = await getDirectoryInfo(itemPath);
            files += subDirInfo.files;
            size += subDirInfo.size;
        } else {
            files++;
            const stats = fs.statSync(itemPath);
            size += stats.size;
        }
    }
    
    return { files, size };
};

/**
 * @功能概述: 递归删除目录
 * @参数: dirPath - 要删除的目录路径
 * @说明: 安全地删除目录及其所有内容
 */
const deleteDirectoryRecursively = async (dirPath) => {
    const logPrefix = `${moduleLogPrefix}[deleteDirectoryRecursively]`;
    
    if (!fs.existsSync(dirPath)) {
        logger.debug(`${logPrefix} 目录不存在，跳过删除: ${dirPath}`);
        return;
    }
    
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
            await deleteDirectoryRecursively(itemPath);
        } else {
            fs.unlinkSync(itemPath);
            logger.debug(`${logPrefix} 删除文件: ${itemPath}`);
        }
    }
    
    fs.rmdirSync(dirPath);
    logger.debug(`${logPrefix} 删除目录: ${dirPath}`);
};

/**
 * @功能概述: 格式化文件大小
 * @参数: bytes - 字节数
 * @返回: 格式化的文件大小字符串
 */
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 导出控制器函数
module.exports = {
    deleteProject
};
