# &lt;Freeze&gt;

## 概述

`<Freeze>` 组件用于将其所有子组件冻结到指定的帧。当组件被 `<Freeze>` 包裹时，调用 [`useCurrentFrame()`](./useCurrentFrame.md) 钩子将始终返回指定的帧号，不受任何 [`<Sequence>`](./Sequence.md) 的影响。

**版本要求**: v2.2.0+

## 语法

```typescript
import { Freeze } from "remotion";

<Freeze frame={30}>
  <YourComponent />
</Freeze>
```

## 核心属性

### frame (必需)
- **类型**: `number`
- **描述**: 子组件应该冻结到的帧号

### active (v4.0.127+)
- **类型**: `boolean | ((frame: number) => boolean)`
- **描述**: 控制冻结组件是否激活
- **默认值**: `true`

## 基础用法

### 1. 基础冻结

```typescript
import { Freeze } from "remotion";

const BlueSquare = () => (
  <div style={{
    width: 100,
    height: 100,
    backgroundColor: "blue"
  }} />
);

const MyVideo = () => {
  return (
    <Freeze frame={30}>
      <BlueSquare />
    </Freeze>
  );
};
```

### 2. 冻结动画组件

```typescript
import { Freeze, useCurrentFrame, interpolate } from "remotion";

const AnimatedCircle = () => {
  const frame = useCurrentFrame();
  
  const x = interpolate(frame, [0, 100], [0, 500], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      width: 50,
      height: 50,
      borderRadius: "50%",
      backgroundColor: "red",
      transform: `translateX(${x}px)`
    }} />
  );
};

const FrozenAnimation = () => {
  return (
    <div>
      {/* 正常动画 */}
      <AnimatedCircle />
      
      {/* 冻结在第50帧的动画 */}
      <div style={{ marginTop: 100 }}>
        <Freeze frame={50}>
          <AnimatedCircle />
        </Freeze>
      </div>
    </div>
  );
};
```

## 实际应用场景

### 1. 创建静态背景

```typescript
import { Freeze, Video, staticFile } from "remotion";

const StaticBackground = () => {
  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      {/* 冻结视频的第一帧作为静态背景 */}
      <Freeze frame={0}>
        <Video 
          src={staticFile("background-video.mp4")}
          style={{ 
            width: "100%", 
            height: "100%", 
            objectFit: "cover",
            position: "absolute",
            zIndex: -1
          }}
        />
      </Freeze>
      
      {/* 前景内容 */}
      <div style={{
        position: "relative",
        zIndex: 1,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        color: "white",
        fontSize: 48
      }}>
        静态背景上的内容
      </div>
    </div>
  );
};
```

### 2. 条件性冻结

```typescript
import { Freeze, useCurrentFrame } from "remotion";

const ConditionalFreeze = () => {
  const frame = useCurrentFrame();
  
  return (
    <div>
      <h2>当前帧: {frame}</h2>
      
      {/* 从第30帧开始冻结 */}
      <Freeze frame={30} active={(f) => f >= 30}>
        <AnimatedComponent />
      </Freeze>
      
      {/* 在第60帧之前冻结 */}
      <Freeze frame={20} active={(f) => f < 60}>
        <AnotherAnimatedComponent />
      </Freeze>
    </div>
  );
};

const AnimatedComponent = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      width: 100,
      height: 100,
      backgroundColor: "blue",
      transform: `rotate(${frame * 3}deg)`
    }}>
      旋转方块 (帧: {frame})
    </div>
  );
};

const AnotherAnimatedComponent = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      width: 100,
      height: 100,
      backgroundColor: "green",
      opacity: frame / 100
    }}>
      淡入方块 (帧: {frame})
    </div>
  );
};
```

### 3. 创建缩略图

```typescript
import { Freeze, Sequence } from "remotion";

const ThumbnailGenerator = () => {
  const scenes = [
    { start: 0, duration: 60, thumbnailFrame: 30 },
    { start: 60, duration: 90, thumbnailFrame: 105 },
    { start: 150, duration: 120, thumbnailFrame: 210 }
  ];

  return (
    <div style={{ display: "flex", gap: 20 }}>
      {scenes.map((scene, index) => (
        <div key={index} style={{
          width: 200,
          height: 150,
          border: "2px solid #ccc",
          borderRadius: 8,
          overflow: "hidden"
        }}>
          <Freeze frame={scene.thumbnailFrame}>
            <Sequence from={scene.start} durationInFrames={scene.duration}>
              <SceneComponent sceneIndex={index} />
            </Sequence>
          </Freeze>
          
          <div style={{
            padding: 10,
            backgroundColor: "#f0f0f0",
            fontSize: 12,
            textAlign: "center"
          }}>
            场景 {index + 1} (帧 {scene.thumbnailFrame})
          </div>
        </div>
      ))}
    </div>
  );
};

const SceneComponent = ({ sceneIndex }: { sceneIndex: number }) => {
  const frame = useCurrentFrame();
  const colors = ["#e74c3c", "#2ecc71", "#3498db"];
  
  return (
    <div style={{
      width: "100%",
      height: "100%",
      backgroundColor: colors[sceneIndex],
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      color: "white",
      fontSize: 24,
      transform: `scale(${1 + Math.sin(frame * 0.1) * 0.1})`
    }}>
      场景 {sceneIndex + 1}
    </div>
  );
};
```

### 4. 暂停效果

```typescript
import { Freeze, useCurrentFrame, interpolate } from "remotion";

const PauseEffect = () => {
  const frame = useCurrentFrame();
  
  // 在特定帧范围内暂停动画
  const pauseRanges = [
    { start: 50, end: 80 },   // 第50-80帧暂停
    { start: 150, end: 200 }  // 第150-200帧暂停
  ];

  const isPaused = pauseRanges.some(range => 
    frame >= range.start && frame <= range.end
  );

  const pauseFrame = isPaused ? 
    pauseRanges.find(range => frame >= range.start && frame <= range.end)?.start || frame :
    frame;

  return (
    <div>
      <div style={{ marginBottom: 20, fontSize: 18 }}>
        当前帧: {frame} {isPaused ? "(已暂停)" : ""}
      </div>
      
      <Freeze frame={pauseFrame} active={isPaused}>
        <MovingBall />
      </Freeze>
      
      {!isPaused && <MovingBall />}
    </div>
  );
};

const MovingBall = () => {
  const frame = useCurrentFrame();
  
  const x = interpolate(frame, [0, 300], [0, 800], {
    extrapolateLeft: "clamp",
    extrapolateRight: "clamp"
  });

  return (
    <div style={{
      width: 50,
      height: 50,
      borderRadius: "50%",
      backgroundColor: "#e74c3c",
      transform: `translateX(${x}px)`,
      transition: "transform 0.1s ease"
    }} />
  );
};
```

### 5. 多层冻结效果

```typescript
import { Freeze, Sequence, useCurrentFrame } from "remotion";

const MultiLayerFreeze = () => {
  return (
    <div style={{ position: "relative", width: "100%", height: "100%" }}>
      {/* 背景层 - 冻结在第0帧 */}
      <Freeze frame={0}>
        <BackgroundLayer />
      </Freeze>
      
      {/* 中间层 - 冻结在第30帧 */}
      <Freeze frame={30}>
        <MiddleLayer />
      </Freeze>
      
      {/* 前景层 - 正常动画 */}
      <ForegroundLayer />
      
      {/* 覆盖层 - 条件性冻结 */}
      <Freeze frame={60} active={(f) => f > 100}>
        <OverlayLayer />
      </Freeze>
    </div>
  );
};

const BackgroundLayer = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: `hsl(${frame * 2}, 50%, 80%)`,
      zIndex: 1
    }}>
      背景 (帧: {frame})
    </div>
  );
};

const MiddleLayer = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      position: "absolute",
      top: "25%",
      left: "25%",
      width: "50%",
      height: "50%",
      backgroundColor: `hsl(${frame * 3}, 70%, 60%)`,
      borderRadius: "50%",
      zIndex: 2,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white"
    }}>
      中间层 (帧: {frame})
    </div>
  );
};

const ForegroundLayer = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      position: "absolute",
      top: "10%",
      left: "10%",
      width: 100,
      height: 100,
      backgroundColor: "#e74c3c",
      zIndex: 3,
      transform: `rotate(${frame * 5}deg)`,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: 12
    }}>
      前景 {frame}
    </div>
  );
};

const OverlayLayer = () => {
  const frame = useCurrentFrame();
  return (
    <div style={{
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 4,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: 24
    }}>
      覆盖层 (帧: {frame})
    </div>
  );
};
```

### 6. 时间轴控制

```typescript
import { Freeze, useCurrentFrame, useVideoConfig } from "remotion";

const TimelineControl = () => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // 定义时间轴控制点
  const controlPoints = [
    { frame: 30, label: "开始" },
    { frame: 90, label: "中间" },
    { frame: 150, label: "高潮" },
    { frame: 210, label: "结束" }
  ];

  return (
    <div>
      {/* 时间轴显示 */}
      <div style={{
        position: "absolute",
        top: 20,
        left: 20,
        right: 20,
        height: 60,
        backgroundColor: "rgba(0,0,0,0.8)",
        borderRadius: 5,
        display: "flex",
        alignItems: "center",
        padding: "0 20px",
        zIndex: 10
      }}>
        <div style={{ color: "white", marginRight: 20 }}>
          {Math.floor(frame / fps)}s / {Math.floor(durationInFrames / fps)}s
        </div>
        
        <div style={{ flex: 1, position: "relative", height: 4, backgroundColor: "#333" }}>
          <div style={{
            position: "absolute",
            left: 0,
            top: 0,
            height: "100%",
            width: `${(frame / durationInFrames) * 100}%`,
            backgroundColor: "#e74c3c"
          }} />
          
          {controlPoints.map((point, index) => (
            <div key={index} style={{
              position: "absolute",
              left: `${(point.frame / durationInFrames) * 100}%`,
              top: -8,
              width: 4,
              height: 20,
              backgroundColor: "#f39c12",
              borderRadius: 2
            }}>
              <div style={{
                position: "absolute",
                top: -25,
                left: -20,
                width: 44,
                textAlign: "center",
                color: "white",
                fontSize: 10
              }}>
                {point.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 根据当前帧显示不同的冻结内容 */}
      <div style={{ marginTop: 100 }}>
        {controlPoints.map((point, index) => (
          <Freeze 
            key={index}
            frame={point.frame} 
            active={Math.abs(frame - point.frame) < 10}
          >
            <div style={{
              position: "absolute",
              top: 200 + index * 80,
              left: 50,
              width: 200,
              height: 60,
              backgroundColor: "#3498db",
              borderRadius: 5,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              opacity: Math.abs(frame - point.frame) < 10 ? 1 : 0.3
            }}>
              {point.label} - 帧 {point.frame}
            </div>
          </Freeze>
        ))}
      </div>
    </div>
  );
};
```

## 重要特性

### 1. 影响 useCurrentFrame()
- 被 `<Freeze>` 包裹的组件中，`useCurrentFrame()` 返回冻结的帧号
- 不受外部 `<Sequence>` 组件影响

### 2. 媒体元素行为
- `<Video>` 和 `<OffthreadVideo>` 元素会暂停
- `<Audio>` 元素会静音渲染

### 3. 嵌套支持
- 可以嵌套多个 `<Freeze>` 组件
- 内层的 `<Freeze>` 会覆盖外层的设置

## 最佳实践

1. **性能考虑**: 冻结复杂动画可以提高渲染性能
2. **缩略图生成**: 用于创建视频场景的静态预览
3. **暂停效果**: 创建戏剧性的暂停时刻
4. **条件冻结**: 使用 `active` 属性实现动态控制
5. **静态背景**: 将动态内容转换为静态背景

## 常见用例

- 创建视频缩略图
- 实现暂停/播放效果
- 生成静态背景
- 时间轴控制点
- 性能优化（冻结复杂动画）

## 相关 API

- [`useCurrentFrame()`](./useCurrentFrame.md) - 获取当前帧
- [`<Sequence>`](./Sequence.md) - 时间序列组件
- [`<Video>`](./Video.md) - 视频组件
- [`<Audio>`](./Audio.md) - 音频组件

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/freeze.tsx)
