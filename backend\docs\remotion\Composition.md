# &lt;Composition&gt;

## 概述

`<Composition>` 是 Remotion 中用于注册视频组合的核心组件，使其可渲染并在 Remotion 开发界面的侧边栏中显示。组合代表您要创建的视频，作为多个片段（例如多个 `<Sequence>`）的集合，这些片段将依次播放以形成您的视频。

## 语法

```typescript
import { Composition } from "remotion";

<Composition
  id="unique-id"
  component={YourComponent}
  durationInFrames={300}
  width={1920}
  height={1080}
  fps={30}
  defaultProps={{}}
/>
```

## 必需属性

### id
- **类型**: `string`
- **描述**: 组合的唯一标识符，显示在侧边栏中
- **限制**: 只能包含字母、数字和 `-`
- **用途**: 渲染时需要指定此 ID

### fps
- **类型**: `number`
- **描述**: 组合的帧率（每秒帧数）
- **常用值**: 24, 25, 30, 60

### durationInFrames
- **类型**: `number`
- **描述**: 组合的总帧数长度
- **计算**: 秒数 × fps = 帧数

### width
- **类型**: `number`
- **描述**: 组合的宽度（像素）

### height
- **类型**: `number`
- **描述**: 组合的高度（像素）

### component 或 lazyComponent
- **component**: 直接传入组件
- **lazyComponent**: 传入返回动态导入的函数
- **注意**: 必须传入其中一个，不能同时传入或都不传入

## 可选属性

### defaultProps
- **类型**: `object`
- **描述**: 组件的默认属性
- **限制**: 必须是纯 JSON 可序列化的值
- **支持**: `Date`、`Map`、`Set` 和 `staticFile()`
- **用途**: 可通过属性编辑器和输入属性覆盖

### calculateMetadata
- **类型**: `function`
- **描述**: 动态计算组合元数据的函数
- **用途**: 运行时修改组合属性

### schema
- **类型**: `ZodSchema`
- **描述**: Zod 模式验证默认属性
- **用途**: 启用可视化编辑功能

## 基础示例

### 1. 使用 component 属性

```typescript
import { Composition } from "remotion";
import { MyComponent } from "./MyComponent";

export const Root: React.FC = () => {
  return (
    <>
      <Composition
        id="my-video"
        component={MyComponent}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={300} // 10秒视频 (300帧 ÷ 30fps)
        defaultProps={{
          title: "我的视频",
          backgroundColor: "#000000"
        }}
      />
    </>
  );
};
```

### 2. 使用 lazyComponent 属性

```typescript
export const Root: React.FC = () => {
  return (
    <>
      <Composition
        id="lazy-video"
        lazyComponent={() => import("./LazyComponent")}
        width={1080}
        height={1920} // 9:16 竖屏格式
        fps={30}
        durationInFrames={180} // 6秒视频
      />
    </>
  );
};
```

### 3. 多个组合

```typescript
import { Composition } from "remotion";
import { IntroComponent } from "./IntroComponent";
import { MainComponent } from "./MainComponent";
import { OutroComponent } from "./OutroComponent";

export const Root: React.FC = () => {
  return (
    <>
      {/* 16:9 横屏格式 */}
      <Composition
        id="intro-16x9"
        component={IntroComponent}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={90}
        defaultProps={{ format: "horizontal" }}
      />
      
      {/* 9:16 竖屏格式 */}
      <Composition
        id="intro-9x16"
        component={IntroComponent}
        width={1080}
        height={1920}
        fps={30}
        durationInFrames={90}
        defaultProps={{ format: "vertical" }}
      />
      
      {/* 主要内容 */}
      <Composition
        id="main-content"
        component={MainComponent}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={600}
      />
    </>
  );
};
```

## 使用文件夹组织

```typescript
import { Composition, Folder } from "remotion";

export const Root: React.FC = () => {
  return (
    <>
      <Folder name="社交媒体格式">
        <Composition
          id="instagram-story"
          component={StoryComponent}
          width={1080}
          height={1920}
          fps={30}
          durationInFrames={150}
        />
        
        <Composition
          id="instagram-post"
          component={PostComponent}
          width={1080}
          height={1080}
          fps={30}
          durationInFrames={90}
        />
      </Folder>
      
      <Folder name="YouTube格式">
        <Composition
          id="youtube-video"
          component={YouTubeComponent}
          width={1920}
          height={1080}
          fps={30}
          durationInFrames={1800}
        />
        
        <Composition
          id="youtube-short"
          component={ShortComponent}
          width={1080}
          height={1920}
          fps={30}
          durationInFrames={180}
        />
      </Folder>
    </>
  );
};
```

## 高级用法

### 1. 带有 Schema 的组合

```typescript
import { z } from "zod";

const mySchema = z.object({
  title: z.string(),
  color: z.string(),
  duration: z.number().min(1).max(10)
});

export const Root: React.FC = () => {
  return (
    <Composition
      id="schema-video"
      component={MyComponent}
      width={1920}
      height={1080}
      fps={30}
      durationInFrames={300}
      schema={mySchema}
      defaultProps={{
        title: "标题",
        color: "#ff0000",
        duration: 5
      }}
    />
  );
};
```

### 2. 动态元数据计算

```typescript
import { calculateMetadata } from "remotion";

export const Root: React.FC = () => {
  return (
    <Composition
      id="dynamic-video"
      component={DynamicComponent}
      width={1920}
      height={1080}
      fps={30}
      durationInFrames={300}
      calculateMetadata={({ props }) => {
        return {
          durationInFrames: props.duration * 30, // 根据属性动态计算帧数
          props: {
            ...props,
            calculatedValue: props.baseValue * 2
          }
        };
      }}
      defaultProps={{
        duration: 10,
        baseValue: 50
      }}
    />
  );
};
```

### 3. 响应式组合

```typescript
const createResponsiveComposition = (
  id: string,
  width: number,
  height: number,
  component: React.ComponentType
) => (
  <Composition
    key={id}
    id={id}
    component={component}
    width={width}
    height={height}
    fps={30}
    durationInFrames={300}
    defaultProps={{
      aspectRatio: width / height,
      isVertical: height > width
    }}
  />
);

export const Root: React.FC = () => {
  const formats = [
    { id: "horizontal", width: 1920, height: 1080 },
    { id: "vertical", width: 1080, height: 1920 },
    { id: "square", width: 1080, height: 1080 }
  ];

  return (
    <>
      {formats.map(format => 
        createResponsiveComposition(
          format.id,
          format.width,
          format.height,
          ResponsiveComponent
        )
      )}
    </>
  );
};
```

## 最佳实践

### 1. 命名约定

```typescript
// 好的命名
<Composition id="intro-16x9" ... />
<Composition id="main-content-vertical" ... />
<Composition id="outro-square" ... />

// 避免的命名
<Composition id="comp1" ... />
<Composition id="test" ... />
<Composition id="video with spaces" ... /> // 包含空格
```

### 2. 属性类型安全

```typescript
interface VideoProps {
  title: string;
  subtitle?: string;
  backgroundColor: string;
}

const MyComponent: React.FC<VideoProps> = ({ title, subtitle, backgroundColor }) => {
  // 组件实现
  return <div style={{ backgroundColor }}>{title}</div>;
};

export const Root: React.FC = () => {
  return (
    <Composition
      id="typed-video"
      component={MyComponent}
      width={1920}
      height={1080}
      fps={30}
      durationInFrames={300}
      defaultProps={{
        title: "标题",
        backgroundColor: "#000000"
        // TypeScript 会检查类型
      }}
    />
  );
};
```

### 3. 性能优化

```typescript
// 使用 lazyComponent 减少启动时间
export const Root: React.FC = () => {
  return (
    <>
      <Composition
        id="heavy-component"
        lazyComponent={() => import("./HeavyComponent")}
        width={1920}
        height={1080}
        fps={30}
        durationInFrames={300}
      />
    </>
  );
};
```

## 常见用例

### 1. 多格式视频生成

```typescript
const VideoFormats = {
  YOUTUBE: { width: 1920, height: 1080, fps: 30 },
  INSTAGRAM_STORY: { width: 1080, height: 1920, fps: 30 },
  INSTAGRAM_POST: { width: 1080, height: 1080, fps: 30 },
  TIKTOK: { width: 1080, height: 1920, fps: 30 }
};

export const Root: React.FC = () => {
  return (
    <>
      {Object.entries(VideoFormats).map(([name, config]) => (
        <Composition
          key={name}
          id={name.toLowerCase()}
          component={AdaptiveComponent}
          {...config}
          durationInFrames={300}
          defaultProps={{ format: name }}
        />
      ))}
    </>
  );
};
```

### 2. 模板系统

```typescript
const createTemplate = (templateName: string, props: any) => (
  <Composition
    id={`template-${templateName}`}
    component={TemplateComponent}
    width={1920}
    height={1080}
    fps={30}
    durationInFrames={300}
    defaultProps={{ template: templateName, ...props }}
  />
);

export const Root: React.FC = () => {
  return (
    <>
      {createTemplate("modern", { theme: "dark", accent: "#ff6b6b" })}
      {createTemplate("classic", { theme: "light", accent: "#4ecdc4" })}
      {createTemplate("minimal", { theme: "white", accent: "#45b7d1" })}
    </>
  );
};
```

## 注意事项

1. **ID 唯一性**: 每个组合的 ID 必须唯一
2. **属性序列化**: defaultProps 必须是 JSON 可序列化的
3. **性能考虑**: 避免在 defaultProps 中传递大型对象
4. **类型安全**: 使用 TypeScript 确保属性类型正确
5. **懒加载**: 对于大型组件使用 lazyComponent

## 相关 API

- [`registerRoot()`](./registerRoot.md) - 注册根组件
- [`<Folder>`](./Folder.md) - 组织组合
- [`<Sequence>`](./Sequence.md) - 时间序列
- [`<Still>`](./Still.md) - 静态图像
- [`calculateMetadata()`](./calculateMetadata.md) - 动态元数据

## 源码链接

[GitHub 源码](https://github.com/remotion-dev/remotion/blob/main/packages/core/src/Composition.tsx)
