/**
 * @功能概述: 视频配置管理器，提供配置加载、验证、版本检测等功能
 * @创建时间: 2025-01-03
 * @版本: 2.0
 * @依赖: VideoConfigConverter, fs, path
 */

const fs = require('fs').promises;
const path = require('path');
const VideoConfigConverter = require('./VideoConfigConverter');
const logger = require('../../utils/logger');

class VideoConfigManager {
    /**
     * @功能概述: 加载视频配置文件，自动检测版本并转换为通用格式
     * @param {string} configPath - 配置文件路径
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 通用配置对象
     * @执行流程:
     *   1. 读取配置文件内容
     *   2. 解析JSON格式
     *   3. 检测配置版本
     *   4. 根据版本进行相应转换
     *   5. 验证配置有效性
     */
    static async loadConfig(configPath, options = {}) {
        const logPrefix = '[VideoConfigManager][loadConfig]';
        
        try {
            logger.info(`${logPrefix} 开始加载配置文件: ${configPath}`);
            
            // 步骤 1: 读取配置文件
            const configContent = await fs.readFile(configPath, 'utf8');
            const parsedConfig = JSON.parse(configContent);
            
            // 步骤 2: 检测配置版本
            const version = this.detectConfigVersion(parsedConfig);
            logger.info(`${logPrefix} 检测到配置版本: ${version}`);
            
            let universalConfig;
            
            // 步骤 3: 根据版本进行转换
            if (version === "1.0" || version === "legacy") {
                // 老配置，需要转换
                logger.info(`${logPrefix} 转换老配置为新格式`);
                universalConfig = VideoConfigConverter.convertLegacyToUniversal(parsedConfig);
            } else if (version === "2.0") {
                // 新配置，直接使用
                logger.info(`${logPrefix} 使用新配置格式`);
                universalConfig = parsedConfig;
            } else {
                throw new Error(`不支持的配置版本: ${version}`);
            }
            
            // 步骤 4: 验证配置有效性
            this.validateConfig(universalConfig);
            
            // 步骤 5: 应用选项
            if (options.renderEngine) {
                universalConfig.renderEngine = options.renderEngine;
            }
            
            logger.info(`${logPrefix} 配置加载完成`);
            return universalConfig;
            
        } catch (error) {
            logger.error(`${logPrefix} 配置加载失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 检测配置文件版本
     * @param {Object} config - 配置对象
     * @returns {string} 版本号
     */
    static detectConfigVersion(config) {
        if (!config || typeof config !== 'object') {
            throw new Error('无效的配置对象');
        }
        
        // 检查是否有明确的版本标识
        if (config.version) {
            return config.version;
        }
        
        // 检查是否有新版本特有的字段
        if (config.configType === "universal-video-config") {
            return "2.0";
        }
        
        // 检查是否有老版本特有的字段
        if (config.width && config.height && config.framerate && !config.output) {
            return "1.0";
        }
        
        // 默认为老版本
        return "legacy";
    }
    
    /**
     * @功能概述: 验证配置对象的有效性
     * @param {Object} config - 配置对象
     * @throws {Error} 配置无效时抛出错误
     */
    static validateConfig(config) {
        const logPrefix = '[VideoConfigManager][validateConfig]';
        
        try {
            logger.debug(`${logPrefix} 开始验证配置`);
            
            // 基础结构验证
            if (!config || typeof config !== 'object') {
                throw new Error('配置必须是一个对象');
            }
            
            // 版本验证
            if (!config.version) {
                throw new Error('配置缺少版本信息');
            }
            
            // 输出配置验证
            if (!config.output) {
                throw new Error('配置缺少output部分');
            }
            
            const { output } = config;
            if (!output.width || !output.height || !output.fps) {
                throw new Error('输出配置缺少必需的width、height或fps');
            }
            
            if (output.width <= 0 || output.height <= 0 || output.fps <= 0) {
                throw new Error('输出配置的width、height、fps必须大于0');
            }
            
            // 内容配置验证
            if (!config.content) {
                throw new Error('配置缺少content部分');
            }
            
            const { content } = config;
            if (!content.audio || !content.video || !content.background) {
                throw new Error('内容配置缺少audio、video或background部分');
            }
            
            if (!content.audio.repeat || content.audio.repeat <= 0) {
                throw new Error('音频重复次数必须大于0');
            }
            
            // 字幕配置验证
            if (config.subtitles && config.subtitles.enabled) {
                if (!config.subtitles.styles) {
                    throw new Error('启用字幕时必须提供样式配置');
                }
                
                if (!config.subtitles.timing || !config.subtitles.timing.modes) {
                    throw new Error('启用字幕时必须提供时间轴配置');
                }
            }
            
            logger.debug(`${logPrefix} 配置验证通过`);
            
        } catch (error) {
            logger.error(`${logPrefix} 配置验证失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 获取Remotion特定配置
     * @param {Object} universalConfig - 通用配置对象
     * @returns {Object} Remotion配置对象
     */
    static getRemotionConfig(universalConfig) {
        const logPrefix = '[VideoConfigManager][getRemotionConfig]';
        
        try {
            logger.debug(`${logPrefix} 转换为Remotion配置`);
            return VideoConfigConverter.convertUniversalToRemotion(universalConfig);
        } catch (error) {
            logger.error(`${logPrefix} Remotion配置转换失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 获取FFmpeg特定配置
     * @param {Object} universalConfig - 通用配置对象
     * @returns {Object} FFmpeg配置对象
     */
    static getFFmpegConfig(universalConfig) {
        const logPrefix = '[VideoConfigManager][getFFmpegConfig]';
        
        try {
            logger.debug(`${logPrefix} 转换为FFmpeg配置`);
            return VideoConfigConverter.convertUniversalToFFmpeg(universalConfig);
        } catch (error) {
            logger.error(`${logPrefix} FFmpeg配置转换失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 保存配置文件
     * @param {string} configPath - 配置文件路径
     * @param {Object} config - 配置对象
     * @param {Object} options - 保存选项
     */
    static async saveConfig(configPath, config, options = {}) {
        const logPrefix = '[VideoConfigManager][saveConfig]';
        
        try {
            logger.info(`${logPrefix} 保存配置文件: ${configPath}`);
            
            // 验证配置
            this.validateConfig(config);
            
            // 确保目录存在
            const configDir = path.dirname(configPath);
            await fs.mkdir(configDir, { recursive: true });
            
            // 格式化JSON
            const jsonContent = JSON.stringify(config, null, options.indent || 2);
            
            // 写入文件
            await fs.writeFile(configPath, jsonContent, 'utf8');
            
            logger.info(`${logPrefix} 配置文件保存成功`);
            
        } catch (error) {
            logger.error(`${logPrefix} 配置文件保存失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 迁移老配置文件到新格式
     * @param {string} oldConfigPath - 老配置文件路径
     * @param {string} newConfigPath - 新配置文件路径
     * @param {Object} options - 迁移选项
     */
    static async migrateConfig(oldConfigPath, newConfigPath, options = {}) {
        const logPrefix = '[VideoConfigManager][migrateConfig]';
        
        try {
            logger.info(`${logPrefix} 开始配置迁移: ${oldConfigPath} -> ${newConfigPath}`);
            
            // 加载老配置
            const universalConfig = await this.loadConfig(oldConfigPath);
            
            // 添加迁移信息
            universalConfig.metadata = universalConfig.metadata || {};
            universalConfig.metadata.migratedFrom = oldConfigPath;
            universalConfig.metadata.migratedAt = new Date().toISOString();
            
            // 保存新配置
            await this.saveConfig(newConfigPath, universalConfig, options);
            
            logger.info(`${logPrefix} 配置迁移完成`);
            
        } catch (error) {
            logger.error(`${logPrefix} 配置迁移失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 获取默认配置
     * @param {string} videoType - 视频类型
     * @param {string} renderEngine - 渲染引擎
     * @returns {Object} 默认配置对象
     */
    static getDefaultConfig(videoType = "educational_subtitle", renderEngine = "remotion") {
        const logPrefix = '[VideoConfigManager][getDefaultConfig]';
        
        try {
            logger.debug(`${logPrefix} 获取默认配置: ${videoType}, ${renderEngine}`);
            
            const defaultConfigPath = path.join(__dirname, '../video/universal-video-config.json');
            return this.loadConfig(defaultConfigPath, { renderEngine });
            
        } catch (error) {
            logger.error(`${logPrefix} 获取默认配置失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 合并配置对象
     * @param {Object} baseConfig - 基础配置
     * @param {Object} overrideConfig - 覆盖配置
     * @returns {Object} 合并后的配置
     */
    static mergeConfigs(baseConfig, overrideConfig) {
        const logPrefix = '[VideoConfigManager][mergeConfigs]';
        
        try {
            logger.debug(`${logPrefix} 合并配置对象`);
            
            // 深度合并配置对象
            const mergedConfig = this._deepMerge(baseConfig, overrideConfig);
            
            // 验证合并后的配置
            this.validateConfig(mergedConfig);
            
            return mergedConfig;
            
        } catch (error) {
            logger.error(`${logPrefix} 配置合并失败: ${error.message}`);
            throw error;
        }
    }
    
    /**
     * @功能概述: 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     */
    static _deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    result[key] = this._deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }
}

module.exports = VideoConfigManager;
