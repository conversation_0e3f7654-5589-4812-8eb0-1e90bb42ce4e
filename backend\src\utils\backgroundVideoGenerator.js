/**
 * @文件名: backgroundVideoGenerator.js
 * @功能概述: 使用Node.js Canvas生成带报纸背景+80%黑色遮罩的静音视频
 * @技术栈: Node.js + node-canvas + FFmpeg
 * @创建时间: 2025-06-08
 * @作者: AI Assistant
 * @位置: backend/src/utils/ (工具函数层)
 * @用途: 为视频生成流水线提供背景视频生成功能
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 检查是否安装了node-canvas
let Canvas;
try {
    Canvas = require('canvas');
} catch (error) {
    console.error('❌ node-canvas未安装，请运行: npm install canvas');
    process.exit(1);
}

const { createCanvas, loadImage } = Canvas;

/**
 * @功能概述: 使用Canvas生成带报纸背景+80%黑色遮罩的静音视频
 * @参数说明:
 *   - duration: 视频时长（秒）
 *   - width: 视频宽度（像素）
 *   - height: 视频高度（像素）
 *   - backgroundImagePath: 报纸背景图片路径
 *   - framerate: 帧率
 *   - outputPath: 输出视频路径
 * @返回值: Promise<string> - 生成的视频文件路径
 * @技术实现:
 *   1. 加载报纸背景图片
 *   2. 使用Canvas逐帧绘制：报纸背景 + 80%黑色遮罩
 *   3. 保存每一帧为PNG图片
 *   4. 使用FFmpeg将图片序列合成静音视频
 *   5. 清理临时文件
 * @性能优化:
 *   - 背景图片只加载一次，重复使用
 *   - 生成速度快（秒级完成）
 *   - 内存占用低（逐帧处理）
 */
async function generateBackgroundVideoWithCanvas({
    duration = 44.64,
    width = 1080,
    height = 1920,
    backgroundImagePath,
    framerate = 30,
    outputPath
}) {
    const functionName = 'generateBackgroundVideoWithCanvas';
    
    console.log(`[${functionName}] 开始使用Canvas生成背景视频...`);
    console.log(`[${functionName}] 参数: ${width}x${height}, ${duration}秒, ${framerate}fps`);
    console.log(`[${functionName}] 背景图片: ${backgroundImagePath}`);
    
    // 计算总帧数
    const totalFrames = Math.ceil(duration * framerate);
    console.log(`[${functionName}] 总帧数: ${totalFrames}`);
    
    // 创建临时目录存储帧图片
    const tempDir = path.join(path.dirname(outputPath), 'temp_bg_frames');
    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }
    
    try {
        // 加载报纸背景图片
        console.log(`[${functionName}] 加载背景图片...`);
        const backgroundImage = await loadImage(backgroundImagePath);
        console.log(`[${functionName}] 背景图片加载成功: ${backgroundImage.width}x${backgroundImage.height}`);

        // 创建Canvas
        const canvas = createCanvas(width, height);
        const ctx = canvas.getContext('2d');

        // 优化：只生成一帧，因为所有帧都相同（静态背景）
        console.log(`[${functionName}] 生成静态背景帧...`);

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 绘制报纸背景图片，缩放到视频尺寸
        ctx.drawImage(backgroundImage, 0, 0, width, height);

        // 绘制80%透明度的黑色遮罩
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, 0, width, height);

        // 保存单帧图片
        const frameFileName = 'background_frame.png';
        const frameFilePath = path.join(tempDir, frameFileName);

        const buffer = canvas.toBuffer('image/png');
        fs.writeFileSync(frameFilePath, buffer);

        console.log(`[${functionName}] 背景帧生成完成，开始合成视频...`);
        
        console.log(`[${functionName}] 帧图片生成完成，开始合成视频...`);
        
        // 使用FFmpeg合成静音视频（循环单帧图片）
        const ffmpegArgs = [
            '-y', // 覆盖输出文件
            '-loop', '1', // 循环输入图片
            '-i', frameFilePath, // 单帧背景图片
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-pix_fmt', 'yuv420p',
            '-t', duration.toString(), // 视频时长
            '-r', framerate.toString(), // 输出帧率
            outputPath
        ];
        
        console.log(`[${functionName}] FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`);
        
        return new Promise((resolve, reject) => {
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
            
            let errorOutput = '';
            
            ffmpegProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            ffmpegProcess.on('close', (code) => {
                // 清理临时文件（延迟清理，避免Windows文件锁定问题）
                setTimeout(() => {
                    try {
                        // 递归删除临时目录及其内容
                        if (fs.existsSync(tempDir)) {
                            const files = fs.readdirSync(tempDir);
                            files.forEach(file => {
                                const filePath = path.join(tempDir, file);
                                try {
                                    fs.unlinkSync(filePath);
                                } catch (fileError) {
                                    console.warn(`[${functionName}] 删除文件失败: ${filePath}, ${fileError.message}`);
                                }
                            });
                            fs.rmdirSync(tempDir);
                            console.log(`[${functionName}] 临时文件清理完成`);
                        }
                    } catch (error) {
                        console.warn(`[${functionName}] 临时文件清理失败: ${error.message}`);
                        // 清理失败不影响主要功能，继续执行
                    }
                }, 1000); // 延迟1秒清理，确保FFmpeg释放文件锁
                
                if (code === 0) {
                    console.log(`[${functionName}] 背景视频生成成功: ${outputPath}`);
                    
                    // 检查文件大小
                    try {
                        const stats = fs.statSync(outputPath);
                        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
                        console.log(`[${functionName}] 视频文件大小: ${fileSizeMB}MB`);
                    } catch (error) {
                        console.warn(`[${functionName}] 无法获取文件大小: ${error.message}`);
                    }
                    
                    resolve(outputPath);
                } else {
                    reject(new Error(`FFmpeg执行失败，退出码: ${code}, 错误: ${errorOutput}`));
                }
            });
            
            ffmpegProcess.on('error', (error) => {
                reject(new Error(`FFmpeg进程启动失败: ${error.message}`));
            });
        });
        
    } catch (error) {
        // 清理临时文件（异常情况下也使用延迟清理）
        setTimeout(() => {
            try {
                if (fs.existsSync(tempDir)) {
                    const files = fs.readdirSync(tempDir);
                    files.forEach(file => {
                        const filePath = path.join(tempDir, file);
                        try {
                            fs.unlinkSync(filePath);
                        } catch (fileError) {
                            // 忽略单个文件删除失败
                        }
                    });
                    fs.rmdirSync(tempDir);
                }
            } catch (cleanupError) {
                console.warn(`[${functionName}] 临时文件清理失败: ${cleanupError.message}`);
            }
        }, 1000);
        
        throw new Error(`背景视频生成失败: ${error.message}`);
    }
}

// 导出函数
module.exports = {
    generateBackgroundVideoWithCanvas
};

/**
 * @使用示例:
 * 
 * const { generateBackgroundVideoWithCanvas } = require('./backgroundVideoGenerator');
 * 
 * // 生成背景视频
 * const result = await generateBackgroundVideoWithCanvas({
 *     duration: 44.64,
 *     width: 1080,
 *     height: 1920,
 *     backgroundImagePath: '/path/to/newspaper_9_16.png',
 *     framerate: 30,
 *     outputPath: '/path/to/background_video.mp4'
 * });
 * 
 * console.log('背景视频生成成功:', result);
 */
