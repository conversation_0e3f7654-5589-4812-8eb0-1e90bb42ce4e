/**
 * @fileoverview VideoCompositionTask 单元测试
 * @description 测试视频合成任务的各种场景和边界情况
 */

const VideoCompositionTask = require('../VideoCompositionTask');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// 统一的测试日志前缀
const testLogPrefix = `[文件：VideoCompositionTask.test.js][VideoCompositionTask测试]`;

// 简易断言函数
function assert(condition, message) {
    if (!condition) {
        console.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    console.log(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        console.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    console.log(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        console.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    console.log(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

async function runTests() {
    console.log(`${testLogPrefix} ========== 开始执行 VideoCompositionTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        console.log(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            console.log(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            console.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            console.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                console.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        console.log(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new VideoCompositionTask();
        assert(task instanceof VideoCompositionTask, '任务应为 VideoCompositionTask 的实例');
        assertEquals(task.name, 'VideoCompositionTask', '任务名称应为 VideoCompositionTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
        assert(task.timeout === 600000, '超时时间应为600000ms (10分钟)');
        assert(Array.isArray(task.supportedFormats), '应有支持的格式列表');
    });

    await runSingleTest('2. 缺少必需字段 - videoIdentifier', async () => {
        const task = new VideoCompositionTask();
        const context = { 
            reqId: 'test-missing-field',
            processedVideoPath: '/test/video.mp4',
            processedAudioPath: '/test/audio.mp3',
            assSubtitlePath: '/test/subtitle.ass',
            savePath: '/test/output'
        }; // 缺少 videoIdentifier
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            const hasFailedProgress = progressLogs.some(p => p.status === TASK_STATUS.FAILED);
            assert(hasFailedProgress, '应记录 FAILED 状态的进度回调');
        }
    });

    await runSingleTest('3. parseCompositionConfig 方法', async () => {
        const task = new VideoCompositionTask();
        
        // 测试默认配置
        const defaultConfig = task.parseCompositionConfig(null, '[TEST]');
        assertEquals(defaultConfig.audioRepeatCount, 3, '默认音频重复次数应为3');
        assertEquals(defaultConfig.outputWidth, 1080, '默认输出宽度应为1080');
        assertEquals(defaultConfig.outputHeight, 1920, '默认输出高度应为1920');
        assertEquals(defaultConfig.videoCodec, 'libx264', '默认视频编码器应为libx264');
        
        // 测试自定义配置
        const customConfig = task.parseCompositionConfig({
            audioRepeatCount: 4,
            outputWidth: 720,
            outputHeight: 1280,
            crf: 20
        }, '[TEST]');
        assertEquals(customConfig.audioRepeatCount, 4, '自定义音频重复次数应为4');
        assertEquals(customConfig.outputWidth, 720, '自定义输出宽度应为720');
        assertEquals(customConfig.crf, 20, '自定义CRF应为20');
        
        // 测试参数验证
        const invalidConfig = task.parseCompositionConfig({
            audioRepeatCount: 10, // 超出范围
            outputWidth: -100     // 无效值
        }, '[TEST]');
        assertEquals(invalidConfig.audioRepeatCount, 3, '超出范围的重复次数应使用默认值');
        assertEquals(invalidConfig.outputWidth, 1080, '无效的宽度应使用默认值');
    });

    await runSingleTest('4. validateInputFiles 方法', async () => {
        const task = new VideoCompositionTask();
        
        // 创建临时测试文件
        const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'video-test-'));
        const videoPath = path.join(tempDir, 'test.mp4');
        const audioPath = path.join(tempDir, 'test.mp3');
        const subtitlePath = path.join(tempDir, 'test.ass');
        
        // 创建空文件
        await fs.writeFile(videoPath, 'fake video content');
        await fs.writeFile(audioPath, 'fake audio content');
        await fs.writeFile(subtitlePath, 'fake subtitle content');
        
        // 测试正常情况
        try {
            await task.validateInputFiles(videoPath, audioPath, subtitlePath, '[TEST]');
            console.log(`${testLogPrefix} 文件验证通过`);
        } catch (error) {
            throw new Error(`文件验证应该通过: ${error.message}`);
        }
        
        // 测试文件不存在的情况
        try {
            await task.validateInputFiles('/nonexistent/video.mp4', audioPath, subtitlePath, '[TEST]');
            throw new Error('应该抛出文件不存在错误');
        } catch (error) {
            assertIncludes(error.message, '不存在', '应该提示文件不存在');
        }
        
        // 测试不支持的格式
        const invalidVideoPath = path.join(tempDir, 'test.txt');
        await fs.writeFile(invalidVideoPath, 'fake content');
        try {
            await task.validateInputFiles(invalidVideoPath, audioPath, subtitlePath, '[TEST]');
            throw new Error('应该抛出格式不支持错误');
        } catch (error) {
            assertIncludes(error.message, '不支持的视频格式', '应该提示格式不支持');
        }
        
        // 清理临时文件
        await fs.rmdir(tempDir, { recursive: true });
    });

    await runSingleTest('5. 进度回调功能', async () => {
        const task = new VideoCompositionTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试基础进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'VideoCompositionTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');
    });

    await runSingleTest('6. 任务状态管理', async () => {
        const task = new VideoCompositionTask();

        // 测试初始状态
        assertEquals(task.status, TASK_STATUS.PENDING, '初始状态应为PENDING');

        // 测试开始状态
        task.start();
        assertEquals(task.status, TASK_STATUS.STARTED, '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');

        // 测试完成状态
        const testResult = { finalVideoPath: '/test/output.mp4' };
        task.complete(testResult);
        assertEquals(task.status, TASK_STATUS.COMPLETED, '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');

        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('7. 错误处理和失败状态', async () => {
        const task = new VideoCompositionTask();
        const testError = new Error('测试错误');

        // 测试失败状态
        task.fail(testError);
        assertEquals(task.status, TASK_STATUS.FAILED, '失败后状态应为FAILED');
        assertEquals(task.error, testError, '应保存错误对象');
        assert(task.endTime, '失败时应记录结束时间');
    });

    await runSingleTest('8. collectDetailedContext 方法', async () => {
        const task = new VideoCompositionTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
    });

    await runSingleTest('9. FFmpeg进程管理功能', async () => {
        const task = new VideoCompositionTask();

        // 测试初始进程状态
        let processStatus = task.getFFmpegProcessStatus();
        assert(!processStatus.hasProcess, '初始状态不应该有FFmpeg进程');
        assertEquals(processStatus.pid, null, '初始PID应为null');
        assertEquals(processStatus.killed, null, '初始killed状态应为null');

        // 测试强制清理（无进程时应安全处理）
        task.forceCleanupFFmpegProcess('测试清理');
        processStatus = task.getFFmpegProcessStatus();
        assert(!processStatus.hasProcess, '清理后仍不应该有进程');

        // 验证currentFFmpegProcess属性存在
        assert(task.hasOwnProperty('currentFFmpegProcess'), '任务应有currentFFmpegProcess属性');
        assertEquals(task.currentFFmpegProcess, null, 'currentFFmpegProcess初始值应为null');
    });

    await runSingleTest('10. 超时配置验证', async () => {
        const task = new VideoCompositionTask();

        // 验证超时设置
        assertEquals(task.timeout, 600000, '超时时间应为600000ms (10分钟)');

        // 验证超时时间是合理的（大于0且不超过1小时）
        assert(task.timeout > 0, '超时时间应大于0');
        assert(task.timeout <= 3600000, '超时时间不应超过1小时');
    });

    await runSingleTest('11. 9:16短视频配置解析', async () => {
        const task = new VideoCompositionTask();
        const { createHardcodedTestConfig } = require('../../utils/compositionConfigConverter');

        // 测试平衡模式配置
        const balancedConfig = createHardcodedTestConfig('balanced');
        const parsedConfig = task.parseCompositionConfig(balancedConfig, '[TEST]');

        // 验证基础配置
        assertEquals(parsedConfig.audioRepeatCount, 3, '音频重复次数应为3');
        assertEquals(parsedConfig.outputWidth, 1080, '输出宽度应为1080');
        assertEquals(parsedConfig.outputHeight, 1920, '输出高度应为1920');
        assertEquals(parsedConfig.frameRate, 30, '帧率应为30');

        // 验证视频布局配置
        assert(parsedConfig.videoLayout, '应包含视频布局配置');
        assert(parsedConfig.videoLayout.firstSegment, '应包含第一段配置');
        assert(parsedConfig.videoLayout.laterSegments, '应包含后续段配置');

        assertEquals(parsedConfig.videoLayout.firstSegment.showVideo, true, '第一段应显示视频');
        assertEquals(parsedConfig.videoLayout.firstSegment.showSubtitles, false, '第一段不应显示字幕');
        assertEquals(parsedConfig.videoLayout.laterSegments.showVideo, false, '后续段不应显示视频');
        assertEquals(parsedConfig.videoLayout.laterSegments.showSubtitles, true, '后续段应显示字幕');

        // 验证字幕样式配置
        assert(parsedConfig.subtitleStyle, '应包含字幕样式配置');
        assertEquals(parsedConfig.subtitleStyle.fontSize, 36, '字体大小应为36');
        assertEquals(parsedConfig.subtitleStyle.primaryColor, '#FFFFFF', '主要颜色应为白色');
        assertEquals(parsedConfig.subtitleStyle.secondaryColor, '#FFD700', '次要颜色应为金色');
    });

    await runSingleTest('12. 不同场景配置对比', async () => {
        const task = new VideoCompositionTask();
        const { createHardcodedTestConfig } = require('../../utils/compositionConfigConverter');

        const scenarios = ['fast', 'balanced', 'high_quality', 'custom'];
        const configs = {};

        scenarios.forEach(scenario => {
            configs[scenario] = createHardcodedTestConfig(scenario);
            const parsed = task.parseCompositionConfig(configs[scenario], '[TEST]');

            // 验证音频重复次数在合理范围内
            assert(parsed.audioRepeatCount >= 2 && parsed.audioRepeatCount <= 5,
                   `${scenario}场景音频重复次数应在2-5之间`);

            // 验证输出尺寸为9:16比例
            const aspectRatio = parsed.outputWidth / parsed.outputHeight;
            assert(Math.abs(aspectRatio - 9/16) < 0.01,
                   `${scenario}场景应为9:16比例`);
        });

        // 验证快速模式的特殊设置
        const fastParsed = task.parseCompositionConfig(configs.fast, '[TEST]');
        assertEquals(fastParsed.audioRepeatCount, 2, '快速模式音频重复2次');
        assertEquals(fastParsed.outputWidth, 720, '快速模式宽度720');
        assertEquals(fastParsed.outputHeight, 1280, '快速模式高度1280');

        // 验证高质量模式的特殊设置
        const hqParsed = task.parseCompositionConfig(configs.high_quality, '[TEST]');
        assertEquals(hqParsed.audioRepeatCount, 4, '高质量模式音频重复4次');
        assertEquals(hqParsed.subtitleStyle.fontSize, 40, '高质量模式字体大小40');
    });

    // --- 测试总结 ---
    console.log(`${testLogPrefix} ========== VideoCompositionTask 测试执行完毕 ==========`);
    console.log(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    console.log(`${testLogPrefix} 通过: ${testsPassed}`);
    console.log(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        console.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1);
    } else {
        console.log(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0);
    }
}

// 立即执行测试
runTests().catch(error => {
    console.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
