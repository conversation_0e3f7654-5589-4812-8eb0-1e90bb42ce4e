/**
 * @功能概述: 配置和处理视频文件上传的 Express 中间件。
 *           使用 multer 库进行文件上传。
 */

// 导入必要的库和模块
const multer = require('multer'); // 导入 multer 模块。Multer 是一个 Node.js 的中间件，用于处理 multipart/form-data 类型的表单数据，主要用于文件上传。
const path = require('path'); // 导入 Node.js 内置的 path 模块。它提供了用于处理文件路径和目录路径的实用工具。
const fs = require('fs'); // 导入 Node.js 内置的 fs (File System) 模块。它提供了用于与文件系统进行交互的方法，例如读取、写入、删除文件或检查目录是否存在。
const logger = require('../utils/logger'); // 导入项目自定义的日志工具模块，用于记录日志信息。

// 导入配置模块。修正：config模块现在直接导出配置对象，不再需要调用函数。
const config = require('../config'); // 直接导入已初始化的配置对象

// Configure multer storage options
// 配置 multer 的存储选项。

// Multer configuration for file storage.
// 配置 multer 用于文件存储。

// multer.diskStorage 用于配置磁盘存储引擎。它接受一个对象作为参数，该对象可以包含以下两个主要函数：
// 1. destination:
//    - 作用: 用于确定上传文件应该存储在哪个文件夹。
//    - 调用机制: Multer 在接收到文件时会调用此函数。
//    - 函数参数: (req, file, cb)
//      - req: 当前的 HTTP 请求对象。
//      - file: 一个包含文件信息的对象 (如 originalname, mimetype 等)。
//      - cb: 一个回调函数，用于指定存储目录。调用方式为 cb(null, '目标目录路径')。如果发生错误，第一个参数传递错误对象。
// 2. filename:
//    - 作用: 用于确定保存在目标文件夹中的文件名。
//    - 调用机制: Multer 在确定目标文件夹后，会调用此函数来获取文件名。
//    - 函数参数: (req, file, cb)
//      - req: 当前的 HTTP 请求对象。
//      - file: 一个包含文件信息的对象。
//      - cb: 一个回调函数，用于指定文件名。调用方式为 cb(null, '自定义文件名')。如果发生错误，第一个参数传递错误对象。
// 如果不提供 filename 函数，Multer 会自动生成一个不带文件扩展名的随机文件名。


const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        
        const logPrefix = `[文件：uploadMiddleware.js][上传中间件][storage.destination][ReqID:${req.id || 'unknown'}] `;
        // Set the destination directory for uploaded files.
        // 设置上传文件的目标目录。
        // 从已经获取到的配置对象中读取上传目录路径。
        const uploadDir = config.uploadDir;

        // 添加日志，打印实际获取到的上传目录路径
        logger.info(`${logPrefix}正在使用的上传目录路径: ${uploadDir}`);

        // --- 确保文件上传的目标目录存在 ---
        /**
         * @功能概述: 检查文件上传的目标目录是否存在，如果不存在则创建。
         * @分步说明:
         *   1.  使用 fs.existsSync() 同步检查 uploadDir 路径是否存在。
         *   2.  如果路径不存在 (!fs.existsSync(uploadDir))，则进入此代码块。
         *   3.  使用 fs.mkdirSync(uploadDir, { recursive: true }) 同步创建目录。
         *       { recursive: true } 选项确保如果父目录不存在，也会一并创建，避免错误。
         *   4.  创建成功后，记录一条信息日志，明确说明目录已创建。
         */
        // 确保文件上传的目标目录存在。
        if (!fs.existsSync(uploadDir)) {
            // 在检查通过后再次确认 uploadDir 不是 undefined，虽然理论上调用 configLoader() 后应该不会是 undefined 了
            if (uploadDir === undefined) {
                 logger.error(`${logPrefix}错误：uploadDir 仍然为 undefined，无法创建目录。请检查 config/index.js`);
                 // 如果无法创建目录，应该通过回调函数报告错误给 multer
                 return cb(new Error('文件上传目录未配置或无效。'));
            }
            logger.info(`${logPrefix}[步骤 1] 文件上传目录不存在: ${uploadDir}，正在创建...`); // 记录目录不存在，准备创建
            fs.mkdirSync(uploadDir, { recursive: true }); // 同步地创建目录。{ recursive: true } 选项确保如果父目录不存在，也会一并创建。
            logger.info(`${logPrefix}[步骤 2] 上传目录创建成功: ${uploadDir}`); // 记录目录创建成功的消息。
        } else {
             // 如果目录已存在，也可以选择记录一条调试日志，但在常规流程中 info 级别可能不需要
             // logger.debug(`${logPrefix} 上传目录已存在: ${uploadDir}`);
        }

        // Pass the destination directory to multer's callback.
        // 将目标目录传递给 multer 的回调函数。
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => { // filename 函数用于确定上传文件在目录中的文件名。
        // req, file, cb 的含义同上。
        // Set the filename to avoid naming conflicts
        // 设置文件名以避免命名冲突
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9); // 生成一个基于当前时间和随机数的唯一字符串，用于文件名，防止同名文件覆盖。
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname)); // 调用回调函数，生成最终的文件名。
        // file.fieldname: 文件上传时对应的表单字段名 (例如 'videoFile')。
        // uniqueSuffix: 生成的唯一字符串。
        // path.extname(file.originalname): 获取原始文件的扩展名（例如 '.mp4', '.wav'）。
        // 将这些部分拼接起来形成最终的文件名。
    }
});

// Create a multer upload instance
// 创建一个 multer 上传实例。
// TODO: 可以根据需要配置允许的文件类型 (fileFilter) 和大小限制 (limits)
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 2 * 1024 * 1024 * 1024 // 2GB，增加文件大小限制以支持更大的视频文件
    }
}); // 创建一个 multer 实例，并将上面定义的 storage 存储引擎配置给它。

// Export the middleware function to handle single file upload with field name 'videoFile'
// 导出中间件函数，用于处理单个文件上传。'videoFile' 是期望接收文件的表单字段的 name 属性值。
// upload.single('videoFile') 返回一个 Multer 中间件函数，当它被用作 Express 路由的处理函数时，
// 会查找请求中名为 'videoFile' 的文件，并将其保存到配置的 uploads 目录中。
// 保存成功后，文件信息会添加到请求对象 req.file 中，然后控制权会传递给下一个处理函数（在本例中是 videoController.uploadVideo）。
// module.exports = upload.single('videoFile');




// 详细解释:

// 1. `module.exports`:
//    - 在 Node.js 中，每个 `.js` 文件都被视为一个独立的"模块"(Module)。模块化的好处是代码组织清晰、易于维护，并且可以避免不同文件中的变量名冲突。
//    - `module` 是一个在每个 Node.js 模块中都可用的特殊对象，它代表了当前模块自身。
//    - `exports` 是 `module` 对象的一个属性 (`module.exports`)。它的作用是指定这个模块对外暴露（或称"导出"）什么内容。
//    - 当其他文件通过 `require('./your-module-file.js')` 来导入这个模块时，`require` 函数的返回值就是 `module.exports` 所指向的值。
//    - 简单来说，`module.exports = ...` 这行代码的意思是："当其他文件导入我这个模块时，它们得到的就是等号右边的这个东西。"

// 2. `upload`:
//    - 在这个文件的上文代码中 (你没有选中这部分，但我根据上下文推断)，`upload` 变量是通过 `const upload = multer({ storage: storage });` 这样一行代码创建的。
//    - `multer` 是一个非常流行的 Node.js 中间件，专门用于处理 `multipart/form-data` 类型的表单数据，这种表单数据最常用于文件上传。
//    - 所以，`upload` 在这里是一个配置好的 `multer` 实例。它已经知道了文件应该存储在哪里 (通过 `storage` 配置)，以及文件名如何生成等。

// 3. `.single('videoFile')`:
//    - 这是 `multer` 实例 (`upload`) 提供的一个方法。
//    - `.single()`: 这个方法告诉 `multer`："我期望处理一个**单个文件**的上传。" 如果表单尝试上传多个同名字段的文件，或者没有文件，`multer` 会有相应的处理（可能报错或不处理）。
//    - `('videoFile')`: 括号里的 `'videoFile'` 是一个字符串参数。它指定了在 HTML 表单中，那个 `<input type="file">` 标签的 `name` 属性值。
//      例如，前端的 HTML 表单可能长这样：
//      ```html
//      <form action="/api/video/upload" method="post" enctype="multipart/form-data">
//          <input type="file" name="videoFile"> <!-- 注意这里的 name="videoFile" -->
//          <button type="submit">Upload</button>
//      </form>
//      ```
//      `multer` 会在请求中寻找名为 `videoFile` 的字段，并处理它所附带的文件。

// 4. 整体含义: `upload.single('videoFile')`
//    - 这部分代码执行后，会返回一个**中间件函数**。
//    - **中间件 (Middleware)** 在 Express.js (Node.js 的一个常用 Web 框架) 中是一个非常核心的概念。它是一些函数，可以在请求到达最终的路由处理函数之前，或者在响应发送给客户端之前，对请求 (req) 和响应 (res) 对象进行处理，或者执行一些代码，或者结束请求-响应循环。
//    - 这个由 `upload.single('videoFile')` 返回的中间件函数，当被用在 Express 路由中时 (就像 `videoRoutes.js` 文件中那样)，它会自动：
//        a. 检查 HTTP 请求是否是 `multipart/form-data` 类型。
//        b. 查找请求中名为 `videoFile` 的文件。
//        c. 如果找到了文件，它会根据之前 `storage` 的配置，将文件保存到服务器的指定目录 (例如 `uploads/`)，并生成一个唯一的文件名。
//        d. 处理完成后，它会将上传的文件信息附加到 Express 的 `req` 对象上，通常是 `req.file`。
//        e. 然后，它会调用 `next()` 函数 (Express 中间件的约定)，将控制权传递给路由中的下一个中间件或最终的路由处理函数 (例如 `videoController.uploadVideo`)。

// 总结:
// `module.exports = upload.single('videoFile');`
// 这行代码的整体作用是：
// 将一个由 `multer` 生成并配置好的、专门用于处理名为 `videoFile` 的单个文件上传的中间件函数，作为当前模块 (`uploadMiddleware.js`) 的导出内容。
// 这样，其他模块 (比如 `videoRoutes.js`) 就可以通过 `require('../middleware/uploadMiddleware')` 来获取这个中间件函数，并将其用于 Express 路由中，以便在处理特定请求路径 (如 `/upload`) 时，先由这个中间件来处理文件上传的逻辑。

module.exports = upload.single('videoFile'); // 将这个配置好的文件上传中间件函数作为模块的导出值。