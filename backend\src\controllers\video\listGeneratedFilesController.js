/**
 * @功能概述: 查询Generated文件控制器 - 专门处理generated目录文件查询请求
 * @职责范围: 
 *   - 扫描指定项目的generated目录
 *   - 识别extended_video和enhanced_bilingual_subtitle文件
 *   - 建立视频-字幕文件配对关系
 *   - 提取文件详细信息（大小、时长、条目数等）
 *   - 返回结构化数据供前端展示
 * 
 * @API接口: GET /api/video/listGeneratedFiles/:projectId
 * @请求格式: 路径参数projectId，查询参数limit、sortBy
 * @响应格式: JSON
 * 
 * @架构设计: 单一职责原则 - 只处理generated文件查询相关逻辑
 * @创建时间: 2025-07-26
 */

// === 导入依赖模块 ===
const logger = require('../../utils/logger');
const path = require('path');
const fs = require('fs');

// 导入业务工具
const GeneratedFileIdentifier = require('../../utils/GeneratedFileIdentifier');
const FileInfoExtractor = require('../../utils/FileInfoExtractor');

// 模块级日志前缀
const moduleLogPrefix = '[文件：listGeneratedFilesController.js][Generated文件查询控制器][模块初始化]';
logger.info(`${moduleLogPrefix}模块已加载。`);
logger.info(`${moduleLogPrefix}[架构验证] 专用Generated文件查询控制器，遵循单一职责原则`);

/**
 * @功能概述: 查询指定项目的generated目录文件
 * @参数说明:
 *   - req: Express请求对象
 *     - params.projectId: 项目ID
 *     - query.limit: 返回数量限制（可选，默认20）
 *     - query.sortBy: 排序方式（可选，默认时间降序）
 *   - res: Express响应对象
 * 
 * @处理流程:
 *   1. 参数验证和路径构建
 *   2. 扫描generated目录
 *   3. 提取文件详细信息
 *   4. 数据排序和分页
 *   5. 返回结构化响应
 * 
 * @响应格式:
 *   - success: {status, message, data: {projectId, generatedPath, totalVideos, totalSubtitles, pairs, unpairedVideos, unpairedSubtitles}}
 *   - error: {status, message, error}
 */
const listGeneratedFiles = async (req, res) => {
    const logPrefix = '[文件：listGeneratedFilesController.js][listGeneratedFiles]';
    const { projectId } = req.params;
    const { limit = 20, sortBy = 'time_desc' } = req.query;

    try {
        logger.info(`${logPrefix}[ProjectID:${projectId}] 开始查询generated文件`);

        // === 步骤1: 参数验证 ===
        if (!projectId) {
            logger.warn(`${logPrefix} 参数验证失败: projectId不能为空`);
            return res.status(400).json({
                status: 'error',
                message: '项目ID不能为空',
                error: 'Missing projectId parameter'
            });
        }

        // 验证projectId格式，防止路径遍历攻击
        if (!/^[a-zA-Z0-9_-]+$/.test(projectId)) {
            logger.warn(`${logPrefix}[ProjectID:${projectId}] 参数验证失败: projectId格式不正确`);
            return res.status(400).json({
                status: 'error',
                message: '项目ID格式不正确',
                error: 'Invalid projectId format'
            });
        }

        // === 步骤2: 构建项目路径 ===
        const projectsDir = path.join(__dirname, '../../../uploads/projects');
        const projectDir = path.join(projectsDir, projectId);
        const generatedDir = path.join(projectDir, 'generated');

        logger.debug(`${logPrefix}[ProjectID:${projectId}] 项目路径: ${projectDir}`);
        logger.debug(`${logPrefix}[ProjectID:${projectId}] Generated路径: ${generatedDir}`);

        // 验证项目目录是否存在
        if (!fs.existsSync(projectDir)) {
            logger.warn(`${logPrefix}[ProjectID:${projectId}] 项目目录不存在: ${projectDir}`);
            return res.status(404).json({
                status: 'error',
                message: '项目不存在',
                error: 'Project directory not found'
            });
        }

        // === 步骤3: 扫描generated目录 ===
        logger.info(`${logPrefix}[ProjectID:${projectId}] 开始扫描generated目录...`);
        const scanResult = GeneratedFileIdentifier.scanGeneratedDirectory(generatedDir);

        logger.info(`${logPrefix}[ProjectID:${projectId}] 扫描结果: 视频${scanResult.videos.length}个, 字幕${scanResult.subtitles.length}个, 配对${scanResult.pairs.length}个`);

        // === 步骤4: 提取文件详细信息 ===
        logger.info(`${logPrefix}[ProjectID:${projectId}] 开始提取文件详细信息...`);
        const enhancedPairs = await FileInfoExtractor.extractPairsInfo(scanResult.pairs);

        // 处理未配对的文件
        const unpairedVideos = scanResult.videos.filter(video => 
            !scanResult.pairs.some(pair => pair.video.filename === video.filename)
        );
        const unpairedSubtitles = scanResult.subtitles.filter(subtitle => 
            !scanResult.pairs.some(pair => pair.subtitle && pair.subtitle.filename === subtitle.filename)
        );

        // 提取未配对文件的详细信息
        const enhancedUnpairedVideos = await Promise.all(
            unpairedVideos.map(video => FileInfoExtractor.extractVideoInfo(video))
        );
        const enhancedUnpairedSubtitles = await Promise.all(
            unpairedSubtitles.map(subtitle => FileInfoExtractor.extractSubtitleInfo(subtitle))
        );

        // === 步骤5: 数据排序 ===
        let sortedPairs = [...enhancedPairs];
        switch (sortBy) {
            case 'time_desc':
                sortedPairs.sort((a, b) => b.video.timestamp.localeCompare(a.video.timestamp));
                break;
            case 'time_asc':
                sortedPairs.sort((a, b) => a.video.timestamp.localeCompare(b.video.timestamp));
                break;
            case 'size_desc':
                sortedPairs.sort((a, b) => (b.video._rawSize || 0) - (a.video._rawSize || 0));
                break;
            case 'size_asc':
                sortedPairs.sort((a, b) => (a.video._rawSize || 0) - (b.video._rawSize || 0));
                break;
            default:
                // 默认按时间降序
                sortedPairs.sort((a, b) => b.video.timestamp.localeCompare(a.video.timestamp));
        }

        // === 步骤6: 分页处理 ===
        const limitNum = parseInt(limit);
        const paginatedPairs = limitNum > 0 ? sortedPairs.slice(0, limitNum) : sortedPairs;

        // === 步骤7: 构建响应数据 ===
        const responseData = {
            projectId,
            generatedPath: generatedDir,
            totalVideos: scanResult.videos.length,
            totalSubtitles: scanResult.subtitles.length,
            totalPairs: enhancedPairs.length,
            pairs: paginatedPairs,
            unpairedVideos: enhancedUnpairedVideos,
            unpairedSubtitles: enhancedUnpairedSubtitles,
            // 元数据
            metadata: {
                limit: limitNum,
                sortBy,
                hasMore: enhancedPairs.length > limitNum,
                scannedAt: new Date().toISOString()
            }
        };

        res.json({
            status: 'success',
            message: '查询成功',
            data: responseData
        });

        logger.info(`${logPrefix}[ProjectID:${projectId}] 查询完成，返回${paginatedPairs.length}个配对结果`);

    } catch (error) {
        logger.error(`${logPrefix}[ProjectID:${projectId}] 查询失败: ${error.message}`);
        logger.error(`${logPrefix}[ProjectID:${projectId}] 错误堆栈: ${error.stack}`);
        
        res.status(500).json({
            status: 'error',
            message: '查询失败',
            error: error.message
        });
    }
};

// 导出控制器方法
module.exports = {
    listGeneratedFiles
};
