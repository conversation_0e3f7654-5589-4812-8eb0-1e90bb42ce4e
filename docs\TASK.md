# 视频生成项目 - VideoClipAndCropTask 开发任务清单

## 任务概述
创建 VideoClipAndCropTask，实现视频片段裁剪和画面裁剪功能，支持完整的进度监控和错误处理。

## 核心开发任务

### **阶段1: Task文件创建和基础结构**

- [ ] **T.1 创建 VideoClipAndCropTask 基础文件**
  - [ ] T.1.1 创建任务文件基础结构
    - 实现位置：`backend/src/tasks/VideoClipAndCropTask.js`
    - 功能要求：按照 `.cursor/rules/task_development_standard.mdc` 标准创建
    - 继承要求：继承 `TaskBase` 类
    - 日志要求：实现模块级日志前缀 `[文件：VideoClipAndCropTask.js][视频裁剪任务][模块初始化]`
    - 验证标准：文件创建后无语法错误，可正常导入

  - [ ] T.1.2 实现构造函数和基础属性
    - 实现位置：VideoClipAndCropTask 类构造函数
    - 功能要求：调用 `super('VideoClipAndCrop')`，设置实例日志前缀
    - 属性设置：设置合适的超时时间（建议300秒）
    - 日志要求：记录实例创建和超时设置
    - 验证标准：实例化成功，属性设置正确

### **阶段2: 参数验证和文件查找逻辑**

- [ ] **T.2 实现参数验证逻辑**
  - [ ] T.2.1 定义必需参数列表
    - 实现位置：execute 方法开始部分
    - 参数列表：`['videoIdentifier', 'clipStartTime', 'clipEndTime', 'cropWidth', 'cropHeight', 'cropXOffset', 'cropYOffset', 'reqId']`
    - 功能要求：使用 `validateRequiredFields` 方法验证
    - 日志要求：记录参数验证过程和结果
    - 验证标准：缺少参数时正确抛出错误

  - [ ] T.2.2 实现参数类型和范围验证
    - 实现位置：紧接 T.2.1 之后
    - 验证内容：时间参数为数字且 `clipStartTime < clipEndTime`，裁剪参数为正数
    - 功能要求：详细的参数合法性检查
    - 日志要求：记录每个参数的验证结果
    - 验证标准：无效参数时正确抛出错误并记录详细信息

- [ ] **T.3 实现视频文件查找逻辑**
  - [ ] T.3.1 根据 videoIdentifier 查找原始视频文件
    - 实现位置：参数验证后
    - 查找策略：在 `backend/uploads` 目录中查找匹配的视频文件
    - 支持格式：`.mp4`, `.avi`, `.mov`, `.mkv`, `.webm`
    - 日志要求：记录查找过程和找到的文件路径
    - 验证标准：文件存在时返回正确路径，不存在时抛出明确错误

  - [ ] T.3.2 验证原始视频文件可访问性
    - 实现位置：紧接 T.3.1 之后
    - 功能要求：使用 `fs.access()` 验证文件可读性
    - 错误处理：文件不存在或无权限时抛出详细错误
    - 日志要求：记录文件验证结果
    - 验证标准：文件可访问时继续执行，否则抛出错误

### **阶段3: FFmpeg集成和视频处理**

- [ ] **T.4 实现FFmpeg视频处理逻辑**
  - [ ] T.4.1 构建输出文件路径和命名
    - 实现位置：文件验证后
    - 命名规则：`${videoIdentifier}_processed_${timestamp}.mp4`
    - 路径策略：输出到同一 uploads 目录
    - 日志要求：记录输出文件路径构建过程
    - 验证标准：生成的路径格式正确且目录可写

  - [ ] T.4.2 构建FFmpeg命令参数
    - 实现位置：紧接 T.4.1 之后
    - 命令结构：`ffmpeg -i input.mp4 -ss ${clipStartTime} -t ${duration} -vf "crop=${cropWidth}:${cropHeight}:${cropXOffset}:${cropYOffset}" -c:v libx264 -crf 23 output.mp4`
    - 参数计算：`duration = clipEndTime - clipStartTime`
    - 日志要求：记录完整的FFmpeg命令
    - 验证标准：命令参数格式正确，包含所有必需参数

  - [ ] T.4.3 执行FFmpeg处理并监控进度
    - 实现位置：紧接 T.4.2 之后
    - 执行方式：使用 `child_process.spawn` 执行FFmpeg
    - 进度监控：解析FFmpeg输出获取处理进度
    - 进度报告：使用 `this.reportProgress()` 报告处理进度
    - 日志要求：记录FFmpeg执行状态和进度信息
    - 验证标准：FFmpeg成功执行，进度正确报告

### **阶段4: 输出验证和上下文构建**

- [ ] **T.5 实现输出文件验证**
  - [ ] T.5.1 验证输出视频文件生成
    - 实现位置：FFmpeg执行完成后
    - 验证内容：文件存在性、文件大小、基本格式
    - 功能要求：使用 `fs.stat()` 获取文件信息
    - 日志要求：记录输出文件验证结果
    - 验证标准：文件存在且大小合理时通过验证

  - [ ] T.5.2 获取输出视频技术参数
    - 实现位置：紧接 T.5.1 之后
    - 获取信息：视频宽高、时长、帧率、编码格式、文件大小
    - 实现方式：使用FFprobe获取视频元数据
    - 日志要求：记录获取的技术参数
    - 验证标准：成功获取所有必需的技术参数

- [ ] **T.6 构建完整的输出上下文**
  - [ ] T.6.1 构建核心文件信息
    - 实现位置：技术参数获取后
    - 包含字段：`processedVideoPath`, `processedVideoFileName`, `processedVideoIdentifier`, `originalVideoPath`, `originalVideoIdentifier`
    - 功能要求：生成新的视频标识符
    - 日志要求：记录核心文件信息构建
    - 验证标准：所有核心字段正确填充

  - [ ] T.6.2 构建技术参数信息
    - 实现位置：紧接 T.6.1 之后
    - 包含字段：`finalVideoWidth`, `finalVideoHeight`, `finalVideoDuration`, `finalVideoFrameRate`, `finalVideoCodec`, `finalVideoFormat`, `finalVideoBitrate`, `finalVideoFileSize`, `hasAudio`, `audioCodec`, `audioSampleRate`, `audioChannels`
    - 功能要求：从FFprobe结果中提取并格式化
    - 日志要求：记录技术参数信息构建
    - 验证标准：技术参数准确反映输出视频特性

  - [ ] T.6.3 构建处理历史信息
    - 实现位置：紧接 T.6.2 之后
    - 包含字段：`processingHistory.clipOperation`, `processingHistory.cropOperation`, `processedAt`, `processingDuration`
    - 功能要求：记录完整的处理参数和时间信息
    - 日志要求：记录处理历史信息构建
    - 验证标准：处理历史完整记录所有操作参数

  - [ ] T.6.4 构建后续处理支持信息
    - 实现位置：紧接 T.6.3 之后
    - 包含字段：`concatenationReady`, `copyConfig`, `aiProcessingReady`
    - 功能要求：为后续视频处理提供必要的元数据
    - 日志要求：记录后续处理支持信息构建
    - 验证标准：支持信息正确配置，便于后续处理

### **阶段5: 错误处理和进度监控**

- [ ] **T.7 实现完整的错误处理机制**
  - [ ] T.7.1 实现参数验证错误处理
    - 实现位置：参数验证部分的catch块
    - 错误类型：参数缺失、类型错误、范围错误
    - 处理方式：使用 `this.fail(error)` 标记失败
    - 日志要求：详细记录错误类型和参数信息
    - 验证标准：错误正确分类，状态正确更新

  - [ ] T.7.2 实现文件操作错误处理
    - 实现位置：文件查找和验证部分的catch块
    - 错误类型：文件不存在、权限不足、路径错误
    - 处理方式：提供清晰的错误信息和建议
    - 日志要求：记录文件操作错误详情
    - 验证标准：文件错误时提供有用的错误信息

  - [ ] T.7.3 实现FFmpeg执行错误处理
    - 实现位置：FFmpeg执行部分的catch块
    - 错误类型：FFmpeg命令错误、编码失败、磁盘空间不足
    - 处理方式：解析FFmpeg错误输出，提供具体错误信息
    - 日志要求：记录FFmpeg错误输出和分析结果
    - 验证标准：FFmpeg错误时提供可操作的错误信息

- [ ] **T.8 实现详细的进度监控**
  - [ ] T.8.1 实现任务生命周期进度报告
    - 实现位置：execute方法关键节点
    - 进度节点：任务开始(0%)、参数验证(10%)、文件查找(20%)、FFmpeg开始(30%)、FFmpeg处理中(30-90%)、输出验证(95%)、任务完成(100%)
    - 功能要求：使用 `this.reportProgress()` 报告每个节点
    - 日志要求：记录每个进度节点的详细信息
    - 验证标准：进度报告准确反映任务执行状态

  - [ ] T.8.2 实现FFmpeg处理进度监控
    - 实现位置：FFmpeg执行过程中
    - 监控方式：解析FFmpeg stderr输出中的进度信息
    - 进度计算：基于处理时间和总时长计算百分比
    - 功能要求：实时更新处理进度
    - 日志要求：记录FFmpeg进度解析过程
    - 验证标准：进度更新及时准确，用户可感知处理状态

### **阶段6: 流水线集成和测试**

- [ ] **T.9 更新任务导出和流水线集成**
  - [ ] T.9.1 更新tasks/index.js导出新任务
    - 实现位置：`backend/src/tasks/index.js`
    - 功能要求：导入并导出 `VideoClipAndCropTask`
    - 验证标准：任务可正常导入，无语法错误

  - [ ] T.9.2 在流水线中集成新任务
    - 实现位置：`backend/src/pipelines/videoGenerationPipelineService.js`
    - 功能要求：在 `addAllTasks()` 方法中添加 `VideoClipAndCropTask`
    - 集成方式：`this.processingPipeline.addTask(new VideoClipAndCropTask())`
    - 日志要求：记录任务添加到流水线
    - 验证标准：流水线可正常创建，包含新任务

- [ ] **T.10 端到端功能验证**
  - [ ] T.10.1 前端到后端完整流程测试
    - 测试位置：通过前端页面触发完整流程
    - 测试内容：参数传递、任务执行、进度更新、结果返回
    - 验证要求：生成的视频效果与前端预览一致
    - 日志验证：控制台显示清晰的处理过程和结果
    - 验证标准：用户可通过页面和控制台了解完整的生成过程

  - [ ] T.10.2 生成视频文件验证
    - 验证位置：`backend/uploads` 目录
    - 验证内容：文件存在、时长正确、画面裁剪正确、质量可接受
    - 对比标准：与前端用户设置的参数效果完全一致
    - 验证标准：手动检查生成视频，确认裁剪效果准确

## 验收标准总结

### 功能验收标准
1. **视频效果一致性**：生成的视频与前端用户传递的参数看到的效果完全一致
2. **文件生成正确**：在 `backend/uploads` 目录中生成正确命名的视频文件
3. **参数传递完整**：所有前端参数正确传递到任务并正确处理

### 用户体验验收标准
1. **前端响应清晰**：用户通过页面信息了解视频生成过程
2. **控制台信息完整**：用户通过控制台了解详细的生成过程和结果
3. **进度反馈及时**：用户可实时了解处理进度和当前状态

### 技术验收标准
1. **代码规范符合**：完全符合 `.cursor/rules/task_development_standard.mdc` 标准
2. **日志记录完整**：所有关键步骤都有详细的日志记录
3. **错误处理健壮**：各种异常情况都有适当的错误处理和用户提示
4. **进度监控准确**：进度报告准确反映任务执行状态
