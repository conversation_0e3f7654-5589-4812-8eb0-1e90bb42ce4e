# ContentSummarizationTask 技术文档

## 1. 输入上下文参数 (Input Context)

### 必需参数
- **reqId** (string): 请求ID，用于日志追踪
- **videoIdentifier** (string): 视频唯一标识符，用于文件命名
- **fullTranscriptText** (string): 完整转录文本，来自TranscriptionCorrectionTask
- **savePath** (string): 文件保存路径

### 可选参数
- **summaryLength** (number): 摘要长度（中文字），默认100字
- **titleLength** (number): 标题长度（中文字），默认10字
- **modelName** (string): LLM模型名称，默认'google/gemini-2.5-flash-lite-preview-06-17'
- **temperature** (number): 创造性控制参数，默认0.3
- **maxTokens** (number): 输出令牌限制，默认2000

## 2. 输出上下文参数 (Output Context)

- **summaryStatus** (string): 总结状态，成功时为'success'
- **transcriptSummary** (string): 内容摘要（约100个中文字）
- **transcriptTitle** (string): 内容标题（约10个中文字）
- **summaryJsonPath** (string): 保存的JSON文件完整路径
- **summaryMetadata** (object): 总结元数据
  - **originalTextLength** (number): 原始文本长度
  - **summaryLength** (number): 摘要长度
  - **titleLength** (number): 标题长度
  - **modelUsed** (string): 使用的LLM模型
  - **processingTime** (string): 处理时间戳
- **videoIdentifier** (string): 视频标识符（原样返回）
- **reqId** (string): 请求ID（原样返回）
- **savePath** (string): 保存路径（原样返回）

## 3. 重要数据格式

### LLM提示词参数格式
```json
{
  "input_text": "完整的视频转录文本内容...",
  "summary_length": 100,
  "title_length": 10
}
```

### LLM响应格式
```json
{
  "title": "视频内容标题",
  "summary": "视频内容摘要，约100个中文字的详细描述...",
  "keyTopics": ["关键话题1", "关键话题2", "关键话题3"]
}
```

### 保存的JSON文件格式
```json
{
  "videoIdentifier": "video123",
  "title": "视频内容标题",
  "summary": "视频内容摘要...",
  "keyTopics": ["关键话题1", "关键话题2"],
  "metadata": {
    "titleLength": 10,
    "summaryLength": 98,
    "modelUsed": "google/gemini-2.5-flash-lite-preview-06-17",
    "processingTime": "2025-07-17T10:30:00.000Z"
  }
}
```

## 4. 文件操作

### 保存的文件格式
- **.json**: 内容总结结果JSON文件

### 文件命名规则
- **模式**: `{videoIdentifier}_content_summary.json`
- **示例**: `video123_content_summary.json`

### 文件保存路径规则
- 使用context.savePath作为基础目录
- 通过fileSaver.saveDataToFile统一保存
- 使用UTF-8编码确保中文字符正确保存

## 5. 执行逻辑概述

内容总结任务负责对完整转录文本进行AI智能分析，生成内容摘要和标题。任务首先验证输入参数的完整性，特别是fullTranscriptText的有效性。然后调用LLM服务，使用CONTENT_SUMMARIZATION提示模板进行内容分析，生成约100个中文字的摘要和10个中文字的标题。LLM还会提取关键话题列表，帮助用户快速了解视频内容要点。最后将总结结果保存为JSON文件，包含完整的元数据信息。整个过程提供详细的进度报告，确保用户能够实时了解处理状态。
