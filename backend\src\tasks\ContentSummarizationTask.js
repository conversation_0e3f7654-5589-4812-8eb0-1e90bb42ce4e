/**
 * @文件名: ContentSummarizationTask.js
 * @功能概述: 内容总结任务类，负责对完整转录文本进行AI总结和标题生成
 * @作者: AI Assistant
 * @创建时间: 2025-07-17
 * @最后修改: 2025-07-17
 *
 * @功能描述:
 *   使用LLM对完整转录文本进行智能分析，生成内容摘要和标题。
 *   摘要长度控制在100个中文字，标题长度控制在10个中文字。
 *   输出JSON格式并保存为文件。
 *
 * @输入要求:
 *   - fullTranscriptText: {string} 完整转录文本，来自SubtitleOptimizationTask
 *   - videoIdentifier: {string} 视频唯一标识符
 *   - savePath: {string} 文件保存路径
 *   - reqId: {string} 请求追踪ID
 *
 * @输出结果:
 *   - transcriptSummary: {string} 内容摘要（100个中文字）
 *   - transcriptTitle: {string} 内容标题（10个中文字）
 *   - summaryJsonPath: {string} 保存的JSON文件路径
 *   - summaryStatus: {string} 总结状态
 */

// 导入TaskBase基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');
// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入文件保存工具，用于保存总结结果文件
const fileSaver = require('../utils/fileSaver');
// 导入LLM服务，用于调用AI进行内容总结
const llmService = require('../services/llmService');

// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：ContentSummarizationTask.js][内容总结任务][模块初始化]';

// 记录模块加载成功的日志
logger.info(`${taskModuleLogPrefix}模块已加载。`);

/**
 * @功能概述: 内容总结任务类，负责对完整转录文本进行AI总结和标题生成
 * 
 * @继承关系: 继承自 TaskBase，获得标准化的进度监控和状态管理能力
 * 
 * @进度阶段: 提供细粒度的进度阶段：
 *   - INITIALIZING: 初始化和输入验证阶段
 *   - PROCESSING: LLM内容总结处理阶段
 *   - FINALIZING: 保存总结结果文件
 * 
 * @LLM处理步骤:
 *   - 第一步: 准备LLM调用参数
 *   - 第二步: 调用LLM进行内容总结和标题生成
 *   - 第三步: 解析和验证LLM响应
 *   - 第四步: 保存结果为JSON文件
 */
class ContentSummarizationTask extends TaskBase {
    /**
     * @功能概述: 构造函数，创建内容总结任务实例
     * @param {string} [name='ContentSummarizationTask'] - 任务名称，用于日志标识和进度追踪
     * @param {object} [options={}] - 总结选项配置
     */
    constructor(name = 'ContentSummarizationTask', options = {}) {
        super(name);
        // 设置实例级日志前缀
        this.instanceLogPrefix = `[文件：ContentSummarizationTask.js][内容总结任务][${this.name}]`;
        
        // 总结配置参数
        this.options = {
            summaryLength: options.summaryLength || 80,      // 摘要长度（中文字）
            titleLength: options.titleLength || 10,          // 标题长度（中文字）
            modelName: options.modelName || 'google/gemini-2.5-flash', // 使用与TranscriptionCorrectionTask一致的模型
            temperature: options.temperature || 0.3,         // 创造性控制
            maxTokens: options.maxTokens || 3000,           // 输出限制
            ...options
        };
        
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} ContentSummarizationTask 实例已创建。总结配置: ${JSON.stringify(this.options)}`);
    }

    /**
     * @功能概述: 主执行方法，执行内容总结流程
     * @param {object} context - 执行上下文
     * @param {string} context.reqId - 请求ID
     * @param {string} context.videoIdentifier - 视频标识符
     * @param {string} context.fullTranscriptText - 完整转录文本，来自SubtitleOptimizationTask
     * @param {string} context.savePath - 保存路径
     * @param {function} progressCallback - 进度回调函数
     * @returns {Promise<object>} 总结结果
     */
    async execute(context, progressCallback) {
        // 步骤 1.1: 设置执行级日志前缀
        const reqId = context.reqId || 'unknown_summarization_req';
        const videoIdentifier = context.videoIdentifier; 
        const execLogPrefix = `${this.instanceLogPrefix}[ReqID:${reqId}][FileID:${videoIdentifier}]`;

        // 步骤 1.2: 校验必需的上下文参数
        const requiredFields = ['reqId', 'videoIdentifier', 'fullTranscriptText', 'savePath'];

        for (const field of requiredFields) {
            if (!context[field] || (typeof context[field] === 'string' && context[field].trim() === '')) {
                const errorMsg = `执行失败：上下文缺少必需或无效的字段 "${field}". 当前值: '${context[field]}'`;
                logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
                const validationError = new Error(errorMsg);
                this.fail(validationError);
                throw validationError;
            }
        }

        // 特别验证fullTranscriptText
        if (typeof context.fullTranscriptText !== 'string' || context.fullTranscriptText.trim().length === 0) {
            const errorMsg = `执行失败：fullTranscriptText为空或格式不正确`;
            logger.error(`${execLogPrefix}[VALIDATION_ERROR] ${errorMsg}`);
            const validationError = new Error(errorMsg);
            this.fail(validationError);
            throw validationError;
        }

        logger.debug(`${execLogPrefix} 输入参数验证通过。转录文本长度: ${context.fullTranscriptText.length} 字符`);
        
        // 步骤 1.3: 设置进度回调函数和开始任务
        this.setProgressCallback(progressCallback);
        this.start();

        try {
            // 步骤 2: 初始化阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: '开始内容总结任务',
                current: 10,
                total: 100,
                technicalDetail: 'ContentSummarizationTask initialization started'
            });

            logger.info(`${execLogPrefix}[步骤 2] 内容总结任务开始执行。`);
            logger.info(`${execLogPrefix}[输入信息] 转录文本长度: ${context.fullTranscriptText.length} 字符`);

            // 步骤 3: LLM内容总结处理
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
                detail: '调用LLM进行内容总结',
                current: 30,
                total: 100,
                technicalDetail: 'Calling LLM for content summarization'
            });

            logger.info(`${execLogPrefix}[步骤 3] 开始LLM内容总结处理。`);
            const summaryResult = await this.performLLMSummarization(
                context.fullTranscriptText,
                videoIdentifier,
                execLogPrefix
            );

            logger.info(`${execLogPrefix}[步骤 3] LLM内容总结处理完成。`);

            // 步骤 4: 保存总结结果
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_INTERMEDIATE, {
                detail: '保存总结结果JSON文件',
                current: 80,
                total: 100,
                technicalDetail: 'Saving summarization result to JSON file'
            });

            logger.info(`${execLogPrefix}[步骤 4] 开始保存总结结果文件。`);
            const summaryJsonPath = await this.saveSummaryResult(
                summaryResult,
                videoIdentifier,
                execLogPrefix,
                context.savePath
            );
            logger.info(`${execLogPrefix}[步骤 4] 总结结果文件保存成功: ${summaryJsonPath}`);

            // 步骤 5: 完成阶段
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.FINALIZING, {
                detail: '内容总结任务完成',
                current: 100,
                total: 100,
                technicalDetail: 'ContentSummarizationTask completed successfully'
            });

            // 步骤 5.1: 构建任务结果
            const result = {
                summaryStatus: 'success',
                transcriptSummary: summaryResult.summary,
                transcriptTitle: summaryResult.title,
                summaryJsonPath: summaryJsonPath,
                summaryMetadata: {
                    originalTextLength: context.fullTranscriptText.length,
                    summaryLength: summaryResult.summary.length,
                    titleLength: summaryResult.title.length,
                    modelUsed: this.options.modelName,
                    processingTime: new Date().toISOString()
                },
                videoIdentifier: videoIdentifier,
                reqId: reqId,
                savePath: context.savePath
            };

            // 步骤 5.2: 标记任务成功完成
            this.complete(result);
            logger.info(`${execLogPrefix}[步骤 5.2] 内容总结任务执行成功。`);

            return result;

        } catch (error) {
            logger.error(`${execLogPrefix}[ERROR_HANDLER] 内容总结过程中发生错误: ${error.message}`);
            logger.error(`${execLogPrefix}[错误堆栈] ${error.stack}`);

            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: 执行LLM内容总结处理
     * @param {string} fullTranscriptText - 完整转录文本
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Promise<object>} 总结结果 {title, summary}
     */
    async performLLMSummarization(fullTranscriptText, videoIdentifier, execLogPrefix) {
        logger.info(`${execLogPrefix}[performLLMSummarization] 开始LLM内容总结处理。`);

        try {
            // 第1步：准备LLM调用参数
            this.reportLLMProgress('preparing', '准备LLM总结请求', {
                current: 20,
                total: 100,
                inputLength: fullTranscriptText.length
            });

            // 构建LLM调用选项
            const llmOptions = {
                promptParams: {
                    input_text: fullTranscriptText,
                    summary_length: this.options.summaryLength,
                    title_length: this.options.titleLength,
                    systemPromptContent: "你是一个专业的内容分析师，擅长对视频转录内容进行总结和标题生成。"
                },
                reqId: execLogPrefix,
                templateName: 'default',
                modelName: this.options.modelName,
                temperature: this.options.temperature,
                max_tokens: this.options.maxTokens,
                forceJsonOutput: true,
                validateJsonOutput: true,
                maxJsonValidationRetries: 2,
                retryCount: 3,
                retryDelay: 1000,

                // 使用OpenRouter标准的JSON Schema
                apiEnhancements: {
                    structuredOutput: {
                        enabled: true,
                        response_format: {
                            type: "json_schema",
                            json_schema: {
                                name: "content_summarization_response",
                                strict: true,
                                schema: {
                                    type: "object",
                                    properties: {
                                        title: {
                                            type: "string",
                                            description: "视频内容标题，10个中文字以内，新闻导向但避免负面内容"
                                        },
                                        summary: {
                                            type: "string",
                                            description: "视频内容摘要，100个中文字以内"
                                        },
                                        keyTopics: {
                                            type: "array",
                                            items: { type: "string" },
                                            description: "关键话题列表"
                                        }
                                    },
                                    required: ["title", "summary", "keyTopics"],
                                    additionalProperties: false
                                }
                            }
                        }
                    }
                }
            };

            logger.info(`${execLogPrefix}[LLM调用详情] 模型: ${llmOptions.modelName}`);
            logger.info(`${execLogPrefix}[LLM调用详情] 温度: ${llmOptions.temperature}`);
            logger.info(`${execLogPrefix}[LLM调用详情] 最大令牌: ${llmOptions.max_tokens}`);
            logger.info(`${execLogPrefix}[LLM调用详情] 输入文本长度: ${fullTranscriptText.length} 字符`);

            // 第2步：执行LLM调用
            this.reportLLMProgress('waiting', '等待LLM总结处理', {
                current: 60,
                total: 100
            });

            const llmResult = await llmService.callLLM('CONTENT_SUMMARIZATION', llmOptions);

            this.reportLLMProgress('receiving', '接收LLM总结响应', {
                current: 80,
                total: 100,
                responseReceived: true
            });

            // 第3步：处理LLM响应
            if (llmResult && llmResult.status === 'success' && llmResult.processedText) {
                logger.info(`${execLogPrefix}[LLM响应] 内容总结处理成功完成`);
                logger.info(`${execLogPrefix}[LLM响应] 使用模型: ${llmResult.modelUsed}`);
                if (llmResult.usage) {
                    logger.info(`${execLogPrefix}[LLM响应] Token使用: 输入${llmResult.usage.prompt_tokens}, 输出${llmResult.usage.completion_tokens}`);
                }

                // 解析JSON响应
                let summaryData;
                try {
                    summaryData = JSON.parse(llmResult.processedText);
                } catch (parseError) {
                    logger.error(`${execLogPrefix}[LLM响应] JSON解析失败: ${parseError.message}`);
                    logger.debug(`${execLogPrefix}[LLM响应] 原始响应: ${llmResult.processedText}`);
                    throw new Error(`LLM响应JSON解析失败: ${parseError.message}`);
                }

                // 验证响应结构
                if (!summaryData.title || !summaryData.summary) {
                    throw new Error('LLM响应缺少必需的title或summary字段');
                }

                // 验证长度限制
                if (summaryData.title.length > this.options.titleLength * 2) {
                    logger.warn(`${execLogPrefix}[LLM响应] 标题长度超限，进行截断: ${summaryData.title.length} -> ${this.options.titleLength * 2}`);
                    summaryData.title = summaryData.title.substring(0, this.options.titleLength * 2);
                }

                if (summaryData.summary.length > this.options.summaryLength * 2) {
                    logger.warn(`${execLogPrefix}[LLM响应] 摘要长度超限，进行截断: ${summaryData.summary.length} -> ${this.options.summaryLength * 2}`);
                    summaryData.summary = summaryData.summary.substring(0, this.options.summaryLength * 2);
                }

                this.reportLLMProgress('parsing', '解析LLM总结响应', {
                    current: 100,
                    total: 100
                });

                logger.info(`${execLogPrefix}[LLM处理完成] 标题: "${summaryData.title}" (${summaryData.title.length}字)`);
                logger.info(`${execLogPrefix}[LLM处理完成] 摘要: "${summaryData.summary.substring(0, 50)}..." (${summaryData.summary.length}字)`);

                return {
                    title: summaryData.title,
                    summary: summaryData.summary,
                    keyTopics: summaryData.keyTopics || []
                };

            } else {
                const errorMsg = `LLM调用失败: ${llmResult ? llmResult.message : '未知错误'}`;
                logger.error(`${execLogPrefix}[LLM响应] ${errorMsg}`);
                throw new Error(errorMsg);
            }

        } catch (error) {
            logger.error(`${execLogPrefix}[performLLMSummarization] LLM总结处理失败: ${error.message}`);
            logger.error(`${execLogPrefix}[performLLMSummarization] 错误堆栈: ${error.stack}`);

            // 使用TaskBase标准化错误处理
            this.fail(error);
            throw error;
        }
    }

    /**
     * @功能概述: 保存总结结果为JSON文件
     * @param {object} summaryResult - 总结结果对象
     * @param {string} videoIdentifier - 视频标识符
     * @param {string} execLogPrefix - 执行日志前缀
     * @param {string} savePath - 保存路径
     * @returns {Promise<string>} 保存的文件路径
     */
    async saveSummaryResult(summaryResult, videoIdentifier, execLogPrefix, savePath) {
        // 生成时间戳确保文件名唯一性
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${videoIdentifier}_content_summary_${timestamp}.json`;

        try {
            logger.debug(`${execLogPrefix}[saveSummaryResult] 开始保存文件: ${filename}`);

            // 构建完整的保存数据
            const saveData = {
                videoIdentifier: videoIdentifier,
                title: summaryResult.title,
                summary: summaryResult.summary,
                keyTopics: summaryResult.keyTopics,
                metadata: {
                    titleLength: summaryResult.title.length,
                    summaryLength: summaryResult.summary.length,
                    keyTopicsCount: summaryResult.keyTopics.length,
                    modelUsed: this.options.modelName,
                    generatedAt: new Date().toISOString(),
                    taskName: this.name
                }
            };

            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(saveData, null, 2),
                filename,
                savePath,
                execLogPrefix
            );

            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }

            logger.info(`${execLogPrefix}[saveSummaryResult] 文件保存成功: ${savedPath}`);
            return savedPath;

        } catch (saveError) {
            const errorMsg = `保存总结结果JSON文件失败: ${saveError.message}，路径：${savePath}/${filename}`;
            logger.error(`${execLogPrefix}[saveSummaryResult][ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }
    }

    /**
     * @功能概述: 报告LLM处理进度
     * @param {string} stage - 处理阶段
     * @param {string} message - 进度消息
     * @param {object} details - 详细信息
     */
    reportLLMProgress(stage, message, details = {}) {
        const progressData = {
            detail: message,
            stage: stage,
            ...details
        };

        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, progressData);

        const logPrefix = `${this.instanceLogPrefix}[LLM进度]`;
        logger.debug(`${logPrefix}[${stage}] ${message} - ${JSON.stringify(details)}`);
    }

    /**
     * @功能概述: 收集详细上下文信息
     * @returns {object} 包含内容总结特定信息的详细上下文
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            const baseContext = super.collectDetailedContext();
            const taskResult = this.getResult();

            const summarizationDetails = {
                taskType: 'ContentSummarization',
                modelUsed: this.options.modelName,
                summaryLength: taskResult.summaryMetadata?.summaryLength || 'N/A',
                titleLength: taskResult.summaryMetadata?.titleLength || 'N/A',
                originalTextLength: taskResult.summaryMetadata?.originalTextLength || 'N/A',
                summarizationConfig: this.options,
                collectedAt: new Date().toISOString(),
                collectionMethod: 'ContentSummarizationTask.collectDetailedContext'
            };

            return {
                ...baseContext,
                summarizationDetails
            };

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);
            return {
                ...super.collectDetailedContext(),
                summarizationError: {
                    message: error.message,
                    stack: error.stack
                }
            };
        }
    }
}

// 导出ContentSummarizationTask类
module.exports = ContentSummarizationTask;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}ContentSummarizationTask 类已导出。`);
